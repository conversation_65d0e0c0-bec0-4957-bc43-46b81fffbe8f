UpcomingEventModel = {}
UpcomingEventModel.__index = UpcomingEventModel

function UpcomingEventModel:Init()
  EventDispatcher.AddListener(EEventType.LevelUp, self, self.OnLevelUp)
end

function UpcomingEventModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function UpcomingEventModel:OnLevelUp()
  if self.m_config ~= nil and self.m_config.eLv ~= nil and GM.LevelModel:GetCurrentLevel() > self.m_config.eLv then
    self.m_config = nil
    EventDispatcher.DispatchEvent(EEventType.UpcomingEventStateChanged)
  end
end

function UpcomingEventModel:LoadServerConfig()
  local md5 = GM.ConfigModel:GetServerConfigMD5(ServerConfigKey.UpcomingEvent)
  if self.m_md5 == md5 then
    return
  end
  self.m_md5 = md5
  local config = GM.ConfigModel:GetServerConfig(ServerConfigKey.UpcomingEvent)
  if config == nil then
    self.m_config = nil
    EventDispatcher.DispatchEvent(EEventType.UpcomingEventStateChanged)
    return
  end
  table.sort(config, function(a, b)
    return a.id < b.id
  end)
  self.m_config = config[1]
  EventDispatcher.DispatchEvent(EEventType.UpcomingEventStateChanged)
end

function UpcomingEventModel:UpdatePerSecond()
  if self:HasUpcomingEvent() and GM.GameModel:GetServerTime() >= self:GetLastTime() then
    self.m_config = nil
    EventDispatcher.DispatchEvent(EEventType.UpcomingEventStateChanged)
  end
end

function UpcomingEventModel:HasUpcomingEvent()
  return self.m_config ~= nil
end

function UpcomingEventModel:GetId()
  return self.m_config and self.m_config.id
end

function UpcomingEventModel:GetIconLink()
  return self.m_config and self.m_config.iconLink
end

function UpcomingEventModel:GetPictureLink()
  return self.m_config and self.m_config.picLink
end

function UpcomingEventModel:GetLastTime()
  return self.m_config and self.m_config.rTime
end

function UpcomingEventModel:GetTitleText()
  return self.m_config and GM.GameTextModel:GetText(self.m_config.titleKey)
end

function UpcomingEventModel:GetDescText()
  return self.m_config and GM.GameTextModel:GetText(self.m_config.descKey)
end

function UpcomingEventModel:GetButtonText()
  return self.m_config and GM.GameTextModel:GetText(self.m_config.buttonKey)
end
