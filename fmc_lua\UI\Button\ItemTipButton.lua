ItemTipButton = {}
ItemTipButton.__index = ItemTipButton

function ItemTipButton:Awake()
  self.bRewardHelpBtn = true
end

function ItemTipButton:UpdateItemType(itemType, iapMode, detailWinRefer, callback)
  self.m_itemType = itemType
  self.m_iapMode = iapMode
  self.m_detailWinRefer = detailWinRefer
  self.m_callback = callback
  Log.Assert(not StringUtil.IsNilOrEmpty(detailWinRefer), "ItemDetailWindow 需要指定refer，见 EItemDetailWindowRefer")
end

function ItemTipButton:SetEnabled(enabled)
  self.m_btn.enabled = enabled
end

function ItemTipButton:OnClicked()
  Log.Assert(self.m_itemType ~= nil, "ItemTipButton:OnClicked")
  ItemDetailWindow.Open(self.m_itemType, ItemDetailWindowMode.Normal, self.m_detailWinRefer)
  if self.m_callback then
    self.m_callback()
  end
end
