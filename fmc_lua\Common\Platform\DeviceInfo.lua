DeviceInfo = {}
local lastRecordCpuTime = -1

function DeviceInfo.GetCpuTime()
  lastRecordCpuTime = math.max(lastRecordCpuTime, math.floor(CS.UnityEngine.Time.realtimeSinceStartup))
  return lastRecordCpuTime
end

function DeviceInfo.GetOsName()
  return CSPlatform:GetOSName()
end

function DeviceInfo.IsSystemIOS()
  return CSPlatform:IsSystemIOS()
end

function DeviceInfo.IsUnityEditor()
  return Application.isEditor
end

function DeviceInfo.GetOsVersion()
  return CSPlatform:GetOSVersion()
end

function DeviceInfo.GetDeviceModel()
  return CSPlatform:GetDeviceModel()
end

function DeviceInfo.GetCountry()
  local func = function()
    return CSPlatform:GetCountry()
  end
  local status, result = SafeCall(func)
  if status == true then
    return result
  else
    return ""
  end
end

function DeviceInfo.GetLocale()
  return CSPlatform:GetLocale()
end

function DeviceInfo.GetCurrentLanguage()
  return CSPlatform:GetCurrentLanguage()
end

function DeviceInfo.GetMCC()
  return CSPlatform:GetMCC()
end

function DeviceInfo.GetMNC()
  return CSPlatform:GetMNC()
end

function DeviceInfo.GetMacAddress()
  return CSPlatform:GetMacAddress()
end

function DeviceInfo.GetAdvertisingIdentifier()
  return CSPlatform:GetAdvertisingIdentifier()
end

function DeviceInfo.GetOpenUDID()
  return ""
end

function DeviceInfo.GetVendorID()
  return CSPlatform:GetVendorID()
end

function DeviceInfo.GetDeviceID()
  if DeviceInfo.IsSystemIOS() then
    return CSPlatform:GetVendorIDFromKeychain()
  else
    return CSPlatform:GetAndroidDeviceID()
  end
end

function DeviceInfo.GetOAID()
  return ""
end

function DeviceInfo.GetIMEI()
  return ""
end

function DeviceInfo.GetAndroidPushToken()
  return CSPlatform:GetFcmToken()
end
