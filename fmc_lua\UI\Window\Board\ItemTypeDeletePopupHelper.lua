ItemTypeDeletePopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true
  },
  canIgnorePopup = false
}, BasePopupHelper)
ItemTypeDeletePopupHelper.__index = ItemTypeDeletePopupHelper

function ItemTypeDeletePopupHelper.Create()
  local helper = setmetatable({}, ItemTypeDeletePopupHelper)
  helper:Init()
  return helper
end

function ItemTypeDeletePopupHelper:CheckPopup()
  local canPop, rewardCount, mapItems = GM.ItemTypeDeleteModel:NeedPopupWindow()
  if canPop then
    GM.ItemTypeDeleteModel:OnWindowPoped(rewardCount)
    local winName = 0 < rewardCount and UIPrefabConfigName.ItemTypeDeleteRewardsWindow or UIPrefabConfigName.ItemTypeDeleteNoticeWindow
    return winName, {rewardCount, mapItems}
  end
end
