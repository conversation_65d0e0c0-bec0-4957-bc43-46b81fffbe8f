DiscoveriesChain = {}
DiscoveriesChain.__index = DiscoveriesChain

function DiscoveriesChain:Init(id, title, index)
  if not self.m_list then
    self.m_list = {}
    self.m_transform = self.gameObject.transform
  end
  self.m_chain = id
  self.m_title = GM.GameTextModel:GetText("chain_" .. title .. "_name")
  local items = GM.ItemDataModel:GetDiscoveriesItemsByOrder(id)
  local count = #items
  for i = 1, count do
    if not self.m_list[i] then
      local go = GameObject.Instantiate(self.m_item, self.m_content)
      self.m_list[i] = go:GetLuaTable()
    end
    self.m_list[i].gameObject:SetActive(true)
    self.m_list[i]:Init(self.m_chain, index, items[i], i % 4 ~= 0 and i ~= count)
  end
  for i = count + 1, #self.m_list do
    self.m_list[i].gameObject:SetActive(false)
  end
  self.m_transform.sizeDelta = Vector2(self.m_transform.sizeDelta.x, 75 + math.ceil(count / 4) * 194)
  self:_UpdateTitle()
  EventDispatcher.AddListener(EEventType.DiscoveriesUpdate, self, self._UpdateTitle, true)
end

function DiscoveriesChain:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function DiscoveriesChain:_UpdateTitle()
  local items = GM.ItemDataModel:GetDiscoveriesItemsByOrder(self.m_chain)
  local count = #items
  local num = 0
  for i = 1, count do
    if GM.ItemDataModel:GetUnlockState(items[i]) == EItemUnlockState.Rewarded then
      num = num + 1
    end
  end
  self.m_name.text = self.m_title .. " (" .. num .. "/" .. count .. ")"
end

function DiscoveriesChain:GetPosY()
  return -self.m_transform.localPosition.y
end

function DiscoveriesChain:GetHeight()
  return self.m_transform.rect.height
end

function DiscoveriesChain:GetChain()
  return self.m_chain
end

function DiscoveriesChain:GetCellTb(index)
  return self.m_list[index]
end
