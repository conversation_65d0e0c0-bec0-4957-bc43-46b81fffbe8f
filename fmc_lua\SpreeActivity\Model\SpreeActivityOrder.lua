SpreeActivityOrder = setmetatable({}, SlotOrder)
SpreeActivityOrder.__index = SpreeActivityOrder

function SpreeActivityOrder.Create(slot, id, requirements, type, rewards, createTime)
  local order = setmetatable({}, SpreeActivityOrder)
  order:Init(slot, id, requirements, type, rewards, createTime)
  return order
end

function SpreeActivityOrder:Init(slot, id, requirements, type, rewards, createTime)
  SlotOrder.Init(self, slot, id, requirements, createTime, SpreeActivityOrderModel.SlotCount)
  self.m_type = type
  self.m_rewards = rewards
end

function SpreeActivityOrder:GetType()
  return self.m_type
end

function SpreeActivityOrder:GetMaxPossibleRewardNumber()
  return 2
end

function SpreeActivityOrder:GetRewards()
  return self.m_rewards
end
