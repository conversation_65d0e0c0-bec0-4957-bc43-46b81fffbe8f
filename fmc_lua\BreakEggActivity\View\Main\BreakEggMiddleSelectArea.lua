BreakEggMiddleSelectArea = setmetatable({}, BreakEggBaseArea)
BreakEggMiddleSelectArea.__index = BreakEggMiddleSelectArea

function BreakEggMiddleSelectArea:Init()
  BreakEggBaseArea.Init(self)
  self.m_selectionCells = {
    self.m_selectionCell1,
    self.m_selectionCell2,
    self.m_selectionCell3,
    self.m_selectionCell4
  }
  for index, cell in ipairs(self.m_selectionCells) do
    cell:Init(index)
  end
  self:_UpdateTray()
  self:_UpdateTrayAlpha()
end

function BreakEggMiddleSelectArea:UpdateContent()
  local active = self.m_activityModel:GetBreakEggState() ~= BreakEggState.NoStart
  self.gameObject:SetActive(active)
  if active then
    self.m_tipText.text = GM.GameTextModel:GetText("break_egg_main_ready")
    for _, cell in ipairs(self.m_selectionCells) do
      cell:UpdateContent()
    end
    self:_UpdateTray()
    self:_UpdateTrayAlpha()
  end
end

function BreakEggMiddleSelectArea:OnReviveSuccess()
  local index = self.m_activityModel:GetBreakEggIndex()
  self.m_selectionCells[index]:OnReviveSuccess()
  self:_SwitchTipText("break_egg_main_ready")
end

function BreakEggMiddleSelectArea:DisplayFirstStep()
  self:_UpdateTray()
  self:FadeIn()
  UIUtil.SetAlpha(self.m_tipText, 1)
  self.m_tipText.text = GM.GameTextModel:GetText("break_egg_main_ready")
  for _, cell in ipairs(self.m_selectionCells) do
    cell:DisplayFirstStep()
  end
end

function BreakEggMiddleSelectArea:DisplayStart()
  self:FadeOut()
  self.m_tipText:DOFade(0, 0.5)
  self:_UpdateTray()
  for i = 1, 4 do
    self["m_tray" .. i]:DOFade(1, 0.5)
  end
  DOVirtual.DelayedCall(0.5, function()
    for _, cell in ipairs(self.m_selectionCells) do
      cell:DisplayStart()
    end
  end)
end

function BreakEggMiddleSelectArea:CollectReward(selectedIndex)
  for _, cell in ipairs(self.m_selectionCells) do
    cell:CollectReward(selectedIndex)
  end
  if self:_IsChangeTray() then
    for i = 1, 4 do
      self["m_tray" .. i]:DOFade(0, 0.47)
    end
    DOVirtual.DelayedCall(0.5, function()
      self:_UpdateTray()
    end)
  end
end

function BreakEggMiddleSelectArea:DisplayNewStep()
  self:_SwitchTipText("break_egg_main_ready")
  local delay = 0
  if self:_IsChangeTray() then
    for i = 1, 4 do
      self["m_tray" .. i]:DOFade(1, 0.5)
    end
    delay = 0.5
    GM.UIManager:ShowPromptWithKey("break_egg_new_stage")
  end
  DOVirtual.DelayedCall(delay, function()
    for _, cell in ipairs(self.m_selectionCells) do
      cell:DisplayNewStep()
    end
  end)
end

function BreakEggMiddleSelectArea:_IsChangeTray()
  local curStep = self.m_activityModel:GetCurrentStep()
  local maxStep = self.m_activityModel:GetStepTotal()
  if curStep > maxStep then
    return false
  end
  return self:_GetIdx(curStep) ~= self:_GetIdx(curStep - 1)
end

function BreakEggMiddleSelectArea:_GetIdx(step)
  if 0 < step then
    return (step - 1) // 10 + 1
  end
  return 1
end

function BreakEggMiddleSelectArea:_UpdateTray()
  local curStep = self.m_activityModel:GetCurrentStep()
  local idx = (curStep - 1) // 10 + 1
  local sp = self["m_traySprite" .. idx] or self.m_traySprite1
  for i = 1, 4 do
    self["m_tray" .. i].sprite = sp
  end
end

function BreakEggMiddleSelectArea:_UpdateTrayAlpha()
  for i = 1, 4 do
    UIUtil.SetAlpha(self["m_tray" .. i], 1)
  end
end

function BreakEggMiddleSelectArea:DisplayResults(results, selectedIndex)
  local breakSuccess = self.m_activityModel:GetBreakEggState() ~= BreakEggState.WaitRevive
  if breakSuccess then
    self:_SwitchTipText("break_egg_main_success")
  else
    self:_SwitchTipText("break_egg_main_fail")
  end
  local sequence = DOTween.Sequence()
  for index, cell in ipairs(self.m_selectionCells) do
    if index == selectedIndex then
      sequence:InsertCallback(0, function()
        cell:DisplayResult(results[index], false)
      end)
    elseif breakSuccess then
      sequence:InsertCallback(1.5, function()
        cell:DisplayResult(results[index], true)
      end)
    end
  end
end

function BreakEggMiddleSelectArea:_SwitchTipText(textKey)
  local sequence = DOTween.Sequence()
  sequence:Append(self.m_tipText:DOFade(0, 0.3))
  sequence:AppendCallback(function()
    self.m_tipText.text = GM.GameTextModel:GetText(textKey)
  end)
  sequence:Append(self.m_tipText:DOFade(1, 0.3))
end

function BreakEggMiddleSelectArea:CloneRewardObject(selectedIndex)
  return self.m_selectionCells[selectedIndex]:CloneRewardObject()
end
