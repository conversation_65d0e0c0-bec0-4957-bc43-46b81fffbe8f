return {
  {
    ChapterId = "Seafood",
    Id = 1,
    Cost = 219,
    Rewards = {
      {Currency = "exp", Amount = 168},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "thirdFloorOld",
        State = 100
      },
      {Slot = "thirdFloor", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 2,
    StartConditions = {1},
    Cost = 236,
    Rewards = {
      {Currency = "exp", Amount = 168},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "thirdFence", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 3,
    StartConditions = {2},
    Cost = 253,
    Rewards = {
      {Currency = "exp", Amount = 168},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "thirdTableR",
        State = 9
      }
    }
  },
  {
    ChapterId = "Seafood",
    Id = 4,
    StartConditions = {3},
    Cost = 219,
    Rewards = {
      {Currency = "exp", Amount = 168},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "thirdChairR",
        State = 9
      }
    }
  },
  {
    ChapterId = "Seafood",
    Id = 5,
    StartConditions = {4},
    Cost = 236,
    Rewards = {
      {Currency = "exp", Amount = 168},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "thirdTableM",
        State = 9
      }
    }
  },
  {
    ChapterId = "Seafood",
    Id = 6,
    StartConditions = {5},
    Cost = 253,
    Rewards = {
      {Currency = "exp", Amount = 170},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "thirdChairL",
        State = 9
      }
    }
  },
  {
    ChapterId = "Seafood",
    Id = 7,
    StartConditions = {6},
    Cost = 317,
    Rewards = {
      {Currency = "exp", Amount = 170},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "thirdTableL",
        State = 9
      }
    }
  },
  {
    ChapterId = "Seafood",
    Id = 8,
    StartConditions = {7},
    Cost = 183,
    Rewards = {
      {Currency = "exp", Amount = 170},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "thirdRug", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 9,
    StartConditions = {8},
    Cost = 295,
    Rewards = {
      {Currency = "exp", Amount = 170},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "thirdPlant", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 10,
    StartConditions = {9},
    Cost = 317,
    Rewards = {
      {Currency = "exp", Amount = 170},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "djFloorTrash",
        State = 100
      },
      {Slot = "djFloorOld", State = 100},
      {Slot = "djFloor", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 11,
    StartConditions = {10},
    Cost = 317,
    Rewards = {
      {Currency = "exp", Amount = 173},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "djFence", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 12,
    StartConditions = {11},
    Cost = 183,
    Rewards = {
      {Currency = "exp", Amount = 173},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "djRug", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 13,
    StartConditions = {12},
    Cost = 317,
    Rewards = {
      {Currency = "exp", Amount = 173},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "djStereo", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 14,
    StartConditions = {13},
    Cost = 277,
    Rewards = {
      {Currency = "exp", Amount = 173},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "djPlant", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 15,
    StartConditions = {14},
    Cost = 373,
    Rewards = {
      {Currency = "exp", Amount = 173},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secFloorOld",
        State = 100
      },
      {Slot = "secFloor", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 16,
    StartConditions = {15},
    Cost = 349,
    Rewards = {
      {Currency = "exp", Amount = 175},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secTable", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 17,
    StartConditions = {16},
    Cost = 373,
    Rewards = {
      {Currency = "exp", Amount = 175},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secFence", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 18,
    StartConditions = {17},
    Cost = 277,
    Rewards = {
      {Currency = "exp", Amount = 175},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secPlant", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 19,
    StartConditions = {18},
    Cost = 301,
    Rewards = {
      {Currency = "exp", Amount = 175},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secWall", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 20,
    StartConditions = {19},
    Cost = 253,
    Rewards = {
      {Currency = "exp", Amount = 175},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secWin", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 21,
    StartConditions = {20},
    Cost = 294,
    Rewards = {
      {Currency = "exp", Amount = 175},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secDec", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 22,
    StartConditions = {21},
    Cost = 331,
    Rewards = {
      {Currency = "exp", Amount = 178},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secParasol", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 23,
    StartConditions = {22},
    Cost = 555,
    Rewards = {
      {Currency = "exp", Amount = 178},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secOpenwin", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 24,
    StartConditions = {23},
    Cost = 257,
    Rewards = {
      {Currency = "exp", Amount = 178},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secCover", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 25,
    StartConditions = {24},
    Cost = 444,
    Rewards = {
      {Currency = "exp", Amount = 178},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "firstFloorOld",
        State = 100
      },
      {Slot = "firstFloor", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 26,
    StartConditions = {25},
    Cost = 406,
    Rewards = {
      {Currency = "exp", Amount = 178},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstWall", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 27,
    StartConditions = {26},
    Cost = 369,
    Rewards = {
      {Currency = "exp", Amount = 180},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstDoor", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 28,
    StartConditions = {27},
    Cost = 257,
    Rewards = {
      {Currency = "exp", Amount = 180},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstLed", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 29,
    StartConditions = {28},
    Cost = 248,
    Rewards = {
      {Currency = "exp", Amount = 180},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstLight", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 30,
    StartConditions = {29},
    Cost = 663,
    Rewards = {
      {Currency = "exp", Amount = 180},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstFence", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 31,
    StartConditions = {30},
    Cost = 294,
    Rewards = {
      {Currency = "exp", Amount = 180},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstRug", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 32,
    StartConditions = {31},
    Cost = 248,
    Rewards = {
      {Currency = "exp", Amount = 183},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstPlant", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 33,
    StartConditions = {32},
    Cost = 617,
    Rewards = {
      {Currency = "exp", Amount = 183},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstWallM", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 34,
    StartConditions = {33},
    Cost = 248,
    Rewards = {
      {Currency = "exp", Amount = 183},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstCover", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 35,
    StartConditions = {34},
    Cost = 524,
    Rewards = {
      {Currency = "exp", Amount = 183},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstWin", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 36,
    StartConditions = {35},
    Cost = 386,
    Rewards = {
      {Currency = "exp", Amount = 183},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "frontPlant", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 37,
    StartConditions = {36},
    Cost = 489,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "groundTrash",
        State = 9
      },
      {Slot = "groundOld", State = 100},
      {Slot = "ground", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 38,
    StartConditions = {37},
    Cost = 461,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "tableL", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 39,
    StartConditions = {38},
    Cost = 378,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "umbrellaL", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 40,
    StartConditions = {39},
    Cost = 405,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "lampL", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 41,
    StartConditions = {40},
    Cost = 461,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "tableR", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 42,
    StartConditions = {41},
    Cost = 378,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "umbrellaR", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 43,
    StartConditions = {42},
    Cost = 405,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "lampR", State = 9}
    }
  },
  {
    ChapterId = "Seafood",
    Id = 44,
    StartConditions = {43},
    Cost = 405,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "podium", State = 9}
    }
  }
}
