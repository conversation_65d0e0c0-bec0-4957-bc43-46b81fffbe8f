PassActivityNoticeBubble = {}
PassActivityNoticeBubble.__index = PassActivityNoticeBubble

function PassActivityNoticeBubble:Awake()
  self.m_bAwaked = true
end

function PassActivityNoticeBubble:Init(activityType, orderArea, task)
  self.m_activityType = activityType
  self.m_orderArea = orderArea
  self.m_task = task
  PassActivityViewHelper.SetTaskTargetImage(self.m_iconImage, task, false, true)
  self:_SetProgress()
  self:_Show()
end

function PassActivityNoticeBubble:OnDestroy()
  self:_RemoveTween()
  Scheduler.UnscheduleTarget(self)
end

function PassActivityNoticeBubble:UpdateContent()
  self:_Shake()
  self:_SetProgress()
end

function PassActivityNoticeBubble:_Show()
  self:_RemoveTween()
  self.m_backgroundGo:SetActive(true)
  self.m_backgroundTransform.localScale = Vector3.zero
  self.m_showTween = DOTween:Sequence()
  self.m_showTween:Append(self.m_backgroundTransform:DOScale(1, 0.3):SetEase(Ease.OutBack))
  self.m_showTween:AppendCallback(function()
    self:_RemoveBubbleDelayed()
  end)
end

function PassActivityNoticeBubble:_Shake()
  self:_RemoveTween()
  self.m_backgroundTransform.localScale = Vector3.one
  self.m_showTween = DOTween:Sequence()
  self.m_showTween:Append(self.m_backgroundTransform:DOScale(1.1, 0.2))
  self.m_showTween:Append(self.m_backgroundTransform:DOScale(1, 0.2):SetEase(Ease.OutBack))
  self.m_showTween:AppendCallback(function()
    self:_RemoveBubbleDelayed()
  end)
end

function PassActivityNoticeBubble:_RemoveBubbleDelayed()
  self:_RemoveTween()
  self.m_removeTween = DOVirtual.DelayedCall(1, function()
    self.m_showTween = DOTween:Sequence()
    self.m_showTween:Append(self.m_backgroundTransform:DOScale(0, 0.2))
    self.m_showTween:AppendCallback(function()
      self.m_orderArea:RemovePassActivityNoticeBubble(self.m_task)
    end)
  end)
end

function PassActivityNoticeBubble:_SetProgress()
  local model = GM.ActivityManager:GetModel(self.m_activityType)
  local finishedCount = model:GetFinishedCount(self.m_task, true)
  self.m_progressText.text = finishedCount .. "/" .. self.m_task:GetFinalCount()
  self.m_progressText.gameObject:SetActive(true)
  self.m_checkGo:SetActive(false)
  if finishedCount >= self.m_task:GetFinalCount() then
    if self.m_bAwaked then
      DelayExecuteFuncInView(function()
        self:_ShowCheck()
      end, 0.4, self)
    else
      self:_ShowCheck()
    end
  end
end

function PassActivityNoticeBubble:_ShowCheck()
  self.m_progressText.gameObject:SetActive(false)
  self.m_checkGo:SetActive(true)
end

function PassActivityNoticeBubble:_RemoveTween()
  if self.m_removeTween ~= nil then
    self.m_removeTween:Kill()
    self.m_removeTween = nil
  end
  if self.m_showTween ~= nil then
    self.m_showTween:Kill()
    self.m_showTween = nil
  end
end
