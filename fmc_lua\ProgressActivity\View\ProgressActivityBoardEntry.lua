ProgressActivityBoardEntry = {}
ProgressActivityBoardEntry.__index = ProgressActivityBoardEntry

function ProgressActivityBoardEntry:Init(model, orderArea)
  self.m_model = model
  self.m_activityType = self.m_model:GetType()
  self.m_activityDefinition = ProgressActivityDefinition[self.m_activityType]
  self.m_orderArea = orderArea
  self.m_iconArea:Init(self.m_activityType)
  self.m_iconArea:SetInBoardView()
  self:UpdatePerSecond()
end

function ProgressActivityBoardEntry:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if self.m_model:HasFinishedAllLevel() or self.m_model:GetState() == ActivityState.Ended then
    self.m_countDownText.text = GM.GameTextModel:GetText("countdown_finished")
  else
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function ProgressActivityBoardEntry:OnBtnClicked()
  if self.m_model:GetState() == ActivityState.Started then
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, false)
  end
end

function ProgressActivityBoardEntry:GetIconArea()
  return self.m_iconArea
end
