ItemAccelerateItemEventType = {UpdateView = 1}
ItemAccelerateTime = setmetatable({}, BaseItemComponent)
ItemAccelerateTime.__index = ItemAccelerateTime

function ItemAccelerateTime.Create()
  local itemAccelerateTime = setmetatable({}, ItemAccelerateTime)
  itemAccelerateTime:Init()
  return itemAccelerateTime
end

function ItemAccelerateTime:Init()
  self.event = PairEvent.Create(self)
  self.m_lastUpdateTime = 0
  self.m_time = 0
end

function ItemAccelerateTime:FromSerialization(dbTable)
  self.m_time = dbTable.accelerateTime
  self.m_lastUpdateTime = dbTable.accelerateLastUpdateTime
end

function ItemAccelerateTime:ToSerialization(dbTable)
  dbTable.accelerateTime = self.m_time
  dbTable.accelerateLastUpdateTime = self.m_lastUpdateTime
end

function ItemAccelerateTime:UpdateAccelerateTime()
  local newTime = 0
  local boardModel = self.m_itemModel:GetBoardModel()
  local itemPostiion = self.m_itemModel:GetPosition()
  for _, direction in ipairs(BaseItemLayerModel.Directions8Way) do
    local item = boardModel:GetItem(itemPostiion + direction)
    local itemAccelerate = item and item:GetComponent(ItemAccelerate)
    if itemAccelerate ~= nil and itemAccelerate:IsActivated() then
      local accelerateTime = itemAccelerate:GetStartTimer() + itemAccelerate:GetTimerDuration()
      newTime = math.max(newTime, accelerateTime)
    end
  end
  local serverTime = GM.GameModel:GetServerTime()
  if newTime <= serverTime then
    newTime = 0
  end
  if self.m_time ~= 0 or newTime ~= 0 then
    self.m_lastUpdateTime = serverTime
    self.m_time = newTime
    self.m_itemModel:GetBoardModel():SaveItemProperty(self.m_itemModel)
  end
  self.event:Call(ItemAccelerateItemEventType.UpdateView)
end

function ItemAccelerateTime:GetTime()
  return self.m_time
end

function ItemAccelerateTime:GetLastUpdateTime()
  return self.m_lastUpdateTime
end

function ItemAccelerateTime:IsAccelerated()
  return self.m_time ~= 0
end
