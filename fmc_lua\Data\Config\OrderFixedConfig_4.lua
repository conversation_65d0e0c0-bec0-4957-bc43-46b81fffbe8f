return {
  {
    Id = "40010",
    GroupId = 1,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_3_1_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "40020",
    GroupId = 1,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_21", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40030",
    GroupId = 1,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_2", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "40040",
    GroupId = 1,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "40050",
    GroupId = 1,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_friedsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40060",
    GroupId = 1,
    ChapterId = 4,
    Requirement_1 = {Type = "it_4_2_5", Count = 1},
    Requirement_2 = {Type = "ds_juice_8", Count = 1}
  },
  {
    Id = "40070",
    GroupId = 1,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "40080",
    GroupId = 2,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_1", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_5",
      Count = 1
    }
  },
  {
    Id = "40090",
    GroupId = 2,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_fd_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40100",
    GroupId = 2,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_8", Count = 1}
  },
  {
    Id = "40110",
    GroupId = 2,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "40120",
    GroupId = 2,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40130",
    GroupId = 2,
    ChapterId = 4,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_10",
      Count = 1
    }
  },
  {
    Id = "40140",
    GroupId = 2,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "40150",
    GroupId = 3,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_9", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40160",
    GroupId = 3,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "ds_fd_6", Count = 1}
  },
  {
    Id = "40170",
    GroupId = 3,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_12", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    }
  },
  {
    Id = "40180",
    GroupId = 3,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_2_1_3", Count = 1},
    Requirement_2 = {Type = "it_1_1_1_3", Count = 1}
  },
  {
    Id = "40190",
    GroupId = 3,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_chopfru_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40200",
    GroupId = 3,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "40210",
    GroupId = 3,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "40220",
    GroupId = 4,
    ChapterId = 4,
    Requirement_1 = {Type = "it_4_2_4", Count = 1},
    Requirement_2 = {Type = "ds_fd_15", Count = 1}
  },
  {
    Id = "40230",
    GroupId = 4,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_17", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "40240",
    GroupId = 4,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_2_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40250",
    GroupId = 4,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_6", Count = 1}
  },
  {
    Id = "40260",
    GroupId = 4,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_1_1_3", Count = 1}
  },
  {
    Id = "40270",
    GroupId = 4,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfru_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40280",
    GroupId = 4,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {Type = "it_2_3_5", Count = 1}
  },
  {
    Id = "40290",
    GroupId = 5,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_13", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "40300",
    GroupId = 5,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_10", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "40310",
    GroupId = 5,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_16", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40320",
    GroupId = 5,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_23", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "40330",
    GroupId = 5,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {Type = "it_5_2_7", Count = 1}
  },
  {
    Id = "40340",
    GroupId = 5,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40350",
    GroupId = 5,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "40360",
    GroupId = 6,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_13", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "40370",
    GroupId = 6,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_mixdrk_7",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_12", Count = 1}
  },
  {
    Id = "40380",
    GroupId = 6,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_9", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40390",
    GroupId = 6,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_6", Count = 1}
  },
  {
    Id = "40400",
    GroupId = 6,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {Type = "it_2_3_5", Count = 1}
  },
  {
    Id = "40410",
    GroupId = 6,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40420",
    GroupId = 6,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_2_6", Count = 1}
  },
  {
    Id = "40430",
    GroupId = 7,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {Type = "it_4_2_5", Count = 1}
  },
  {
    Id = "40440",
    GroupId = 7,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_14", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "40450",
    GroupId = 7,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_2_6", Count = 1},
    Requirement_2 = {Type = "it_2_1_8", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40460",
    GroupId = 7,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "40470",
    GroupId = 7,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_1_1_3", Count = 1},
    Requirement_2 = {Type = "it_2_2_6", Count = 1}
  },
  {
    Id = "40480",
    GroupId = 7,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40490",
    GroupId = 7,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_3_5", Count = 1}
  },
  {
    Id = "40500",
    GroupId = 8,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_10",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "40510",
    GroupId = 8,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "ds_fd_19", Count = 1}
  },
  {
    Id = "40520",
    GroupId = 8,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_14", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40530",
    GroupId = 8,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_1_1_2", Count = 1}
  },
  {
    Id = "40540",
    GroupId = 8,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40550",
    GroupId = 8,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_15", Count = 1}
  },
  {
    Id = "40560",
    GroupId = 8,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_8", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_2",
      Count = 1
    }
  },
  {
    Id = "40570",
    GroupId = 9,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "40580",
    GroupId = 9,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_15", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "40590",
    GroupId = 9,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_8", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40600",
    GroupId = 9,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "40610",
    GroupId = 9,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_23", Count = 1},
    Requirement_2 = {Type = "it_4_2_7", Count = 1}
  },
  {
    Id = "40620",
    GroupId = 9,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_2_6", Count = 1},
    Requirement_2 = {Type = "it_2_1_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40630",
    GroupId = 9,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedve_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "40640",
    GroupId = 10,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_11", Count = 1},
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "40650",
    GroupId = 10,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_14", Count = 1},
    Requirement_2 = {Type = "ds_fd_19", Count = 1}
  },
  {
    Id = "40660",
    GroupId = 10,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_2_1_2", Count = 1},
    Requirement_2 = {Type = "it_1_1_8", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40670",
    GroupId = 10,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "40680",
    GroupId = 10,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40690",
    GroupId = 10,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "it_5_2_7", Count = 1}
  },
  {
    Id = "40700",
    GroupId = 10,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "40710",
    GroupId = 11,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_mixdrk_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "40720",
    GroupId = 11,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {Type = "it_2_2_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40730",
    GroupId = 11,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_10", Count = 2},
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "40740",
    GroupId = 11,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40750",
    GroupId = 11,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_mixdrk_7",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_7", Count = 1}
  },
  {
    Id = "40760",
    GroupId = 11,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_21", Count = 1}
  },
  {
    Id = "40770",
    GroupId = 11,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_8", Count = 1}
  },
  {
    Id = "40780",
    GroupId = 12,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_2_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "40790",
    GroupId = 12,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_sal_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40800",
    GroupId = 12,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_9", Count = 1}
  },
  {
    Id = "40810",
    GroupId = 12,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "40820",
    GroupId = 12,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_1_8", Count = 1},
    Requirement_2 = {Type = "ds_fd_23", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40830",
    GroupId = 12,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_15", Count = 1}
  },
  {
    Id = "40840",
    GroupId = 12,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "40850",
    GroupId = 13,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "40860",
    GroupId = 13,
    ChapterId = 4,
    Requirement_1 = {Type = "it_1_2_1_2", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40870",
    GroupId = 13,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_10", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "40880",
    GroupId = 13,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_3_1_3", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "40890",
    GroupId = 13,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_13", Count = 1},
    Requirement_2 = {Type = "it_4_1_9", Count = 1}
  },
  {
    Id = "40900",
    GroupId = 13,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40910",
    GroupId = 13,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "40920",
    GroupId = 14,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "40930",
    GroupId = 14,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_10",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_1_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40940",
    GroupId = 14,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_2_5", Count = 1}
  },
  {
    Id = "40950",
    GroupId = 14,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "40960",
    GroupId = 14,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {Type = "ds_fd_15", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "40970",
    GroupId = 14,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "40980",
    GroupId = 14,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_13", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "40990",
    GroupId = 15,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "41000",
    GroupId = 15,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41010",
    GroupId = 15,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_20", Count = 1},
    Requirement_2 = {Type = "it_4_2_7", Count = 1}
  },
  {
    Id = "41020",
    GroupId = 15,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_16", Count = 1}
  },
  {
    Id = "41030",
    GroupId = 15,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_1",
      Count = 2
    },
    Requirement_2 = {Type = "ds_juice_8", Count = 1}
  },
  {
    Id = "41040",
    GroupId = 15,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41050",
    GroupId = 15,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "41060",
    GroupId = 16,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedsf_4",
      Count = 1
    }
  },
  {
    Id = "41070",
    GroupId = 16,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "ds_fd_12", Count = 1}
  },
  {
    Id = "41080",
    GroupId = 16,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "it_1_2_1_3", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41090",
    GroupId = 16,
    ChapterId = 4,
    Requirement_1 = {Type = "it_4_2_4", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "41100",
    GroupId = 16,
    ChapterId = 4,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "41110",
    GroupId = 16,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41120",
    GroupId = 16,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    }
  },
  {
    Id = "41130",
    GroupId = 17,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "41140",
    GroupId = 17,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_9", Count = 1},
    Requirement_2 = {Type = "it_1_2_1_3", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41150",
    GroupId = 17,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_23", Count = 1}
  },
  {
    Id = "41160",
    GroupId = 17,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "41170",
    GroupId = 17,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41180",
    GroupId = 17,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "41190",
    GroupId = 17,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_2",
      Count = 1
    }
  },
  {
    Id = "41200",
    GroupId = 18,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedve_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_2", Count = 1}
  },
  {
    Id = "41210",
    GroupId = 18,
    ChapterId = 4,
    Requirement_1 = {Type = "it_4_2_4", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "41220",
    GroupId = 18,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_2_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41230",
    GroupId = 18,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_15", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "41240",
    GroupId = 18,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "it_1_2_1_3", Count = 1}
  },
  {
    Id = "41250",
    GroupId = 18,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41260",
    GroupId = 18,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedmt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "41270",
    GroupId = 19,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_2_5", Count = 1}
  },
  {
    Id = "41280",
    GroupId = 19,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_8", Count = 1}
  },
  {
    Id = "41290",
    GroupId = 19,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_friedve_2",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41300",
    GroupId = 19,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_3_1_4", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "41310",
    GroupId = 19,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {Type = "ds_fd_9", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41320",
    GroupId = 19,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_13", Count = 1}
  },
  {
    Id = "41330",
    GroupId = 19,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "41340",
    GroupId = 20,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    }
  },
  {
    Id = "41350",
    GroupId = 20,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillmt_10",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_18", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41360",
    GroupId = 20,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "41370",
    GroupId = 20,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_9", Count = 1}
  },
  {
    Id = "41380",
    GroupId = 20,
    ChapterId = 4,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_3", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "41390",
    GroupId = 20,
    ChapterId = 4,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "41400",
    GroupId = 20,
    ChapterId = 4,
    Requirement_1 = {Type = "ds_fd_17", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  }
}
