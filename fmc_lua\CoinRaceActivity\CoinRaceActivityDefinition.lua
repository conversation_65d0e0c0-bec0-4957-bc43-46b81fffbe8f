CoinRaceActivityDefinition = {
  [ActivityType.CoinRace] = {
    EntryButtonKey = ESceneViewHudButtonKey.CoinRace,
    ActivityDataTableName = VirtualDBTableName.CoinRace,
    StateChangedEvent = EEventType.CoinRaceStateChanged,
    SignupBIType = EBIType.CoinRaceSignup,
    RewardBIType = EBIType.CoinRaceReward,
    RankBIType = EBIType.CoinRaceRank,
    RewardWindowPrefabName = UIPrefabConfigName.CoinRaceRewardWindow,
    CompleteWindowPrefabName = UIPrefabConfigName.CoinRaceCompleteWindow,
    MainWindowPrefabName = UIPrefabConfigName.CoinRaceMainWindow,
    EntryPrefabName = UIPrefabConfigName.CoinRaceEntry,
    NoticeWindowPrefabName = UIPrefabConfigName.CoinRaceNoticeWindow,
    OrderCellPrefabName = UIPrefabConfigName.CoinRaceBoardEntry,
    HelpWindowPrefabName = UIPrefabConfigName.CoinRaceHelpWindow,
    CoinRaceBoxRankImgPrefix = "coin_race_box_rank_",
    CoinRaceBoardEntryRankImgPrefix = "coinRace_rank_board_",
    CoinRaceEntryRankImgPrefix = "coinRace_rank_",
    TokenImageName = ImageFileConfigName.coinRace_token_icon,
    ActivityTokenPropertyType = EPropertyType.CoinRaceToken,
    TutorialStartCondition = ETutorialStartCondition.CoinRaceStart,
    TutorialEntryId = ETutorialId.CoinRaceEntryTutorial,
    TutorialFirstRoundId = ETutorialId.CoinRaceFirstRoundTutorial,
    TutorialSecondRoundId = ETutorialId.CoinRaceSecondRoundTutorial,
    TutorialOrderId = ETutorialId.CoinRaceOrderTutorial,
    TitleTextKey = "coin_race_title",
    MainTitleTextKey = "coin_race_main_title",
    StartTutorial1TextKey = "coin_race_start_tutorial_1",
    ResourceLabels = {
      AddressableLabel.CoinRace
    }
  }
}
