EDBValueType = {
  Text = "text",
  Integer = "integer",
  Real = "real"
}
setmetatable(EDBValueType, {
  __index = function(_, key)
    Log.Error("EDBValueType try to index a nil key:" .. tostring(key))
    return nil
  end
})
local ItemConfigCreator = function(name)
  return {
    name = name,
    columns = {
      id = EDBValueType.Text,
      codeStr = EDBValueType.Text,
      locked = EDBValueType.Integer,
      costEnergy = EDBValueType.Real,
      costEnergyCurDay = EDBValueType.Real,
      shopGemCost = EDBValueType.Real,
      bubbleGemCost = EDBValueType.Real,
      cookGemCost = EDBValueType.Real,
      cookSkipPropCost = EDBValueType.Real,
      spreadState = EDBValueType.Integer,
      spreadStartTimer = EDBValueType.Integer,
      spreadStorageRestNumber = EDBValueType.Integer,
      spreadCount = EDBValueType.Integer,
      spreadCodeWeightPairs = EDBValueType.Text,
      spreadWeightType = EDBValueType.Integer,
      bubbleStartTimer = EDBValueType.Integer,
      spreadEnergyFree = EDBValueType.Integer,
      spreadAddItem = EDBValueType.Integer,
      spreadTierUpCount = EDBValueType.Integer,
      spreadTierUpLevel = EDBValueType.Integer,
      choices = EDBValueType.Text,
      choiceDate = EDBValueType.Text,
      materialInfo = EDBValueType.Text,
      cookState = EDBValueType.Integer,
      cookStartTimer = EDBValueType.Integer,
      cookSpeedTime = EDBValueType.Integer,
      cookRecipe = EDBValueType.Text,
      cookLastUpdateTime = EDBValueType.Integer,
      transformStartTimer = EDBValueType.Integer,
      spreadInherit = EDBValueType.Integer,
      boostCount = EDBValueType.Integer,
      spreadItemBoxChain = EDBValueType.Text
    },
    primaryKey = {"id"},
    arrOrder = {
      "id",
      "codeStr",
      "locked",
      "costEnergy",
      "shopGemCost",
      "bubbleGemCost",
      "cookGemCost",
      "cookSkipPropCost",
      "spreadState",
      "spreadStartTimer",
      "spreadStorageRestNumber",
      "spreadCount",
      "spreadCodeWeightPairs",
      "spreadWeightType",
      "bubbleStartTimer",
      "spreadEnergyFree",
      "spreadAddItem",
      "spreadTierUpCount",
      "spreadTierUpLevel",
      "choices",
      "choiceDate",
      "materialInfo",
      "cookState",
      "cookStartTimer",
      "cookSpeedTime",
      "transformStartTimer",
      "spreadInherit",
      "costEnergyCurDay",
      "boostCount",
      "spreadItemBoxChain",
      "cookRecipe",
      "cookLastUpdateTime"
    }
  }
end
local BoardConfigCreator = function(name)
  return {
    name = name,
    columns = {
      id = EDBValueType.Text,
      itemId = EDBValueType.Text
    },
    primaryKey = {"id"},
    arrOrder = {"id", "itemId"}
  }
end
local CacheItemConfigCreator = function(name)
  return {
    name = name,
    columns = {
      id = EDBValueType.Text,
      type = EDBValueType.Integer,
      codeStr = EDBValueType.Text,
      cost = EDBValueType.Text
    },
    primaryKey = {"id"},
    arrOrder = {
      "id",
      "type",
      "codeStr",
      "cost"
    }
  }
end
local OrderConfigCreator = function(name)
  return {
    name = name,
    columns = {
      id = EDBValueType.Text,
      groupId = EDBValueType.Integer,
      chapterId = EDBValueType.Integer,
      type = EDBValueType.Integer,
      avatarId = EDBValueType.Integer,
      requirementStr = EDBValueType.Text,
      createTime = EDBValueType.Integer,
      rewards = EDBValueType.Text,
      cleanGoldCount = EDBValueType.Integer
    },
    primaryKey = {"id"},
    arrOrder = {
      "id",
      "groupId",
      "chapterId",
      "avatarId",
      "type",
      "requirementStr",
      "createTime",
      "rewards",
      "cleanGoldCount"
    }
  }
end
EDBTableConfigs = {
  CachedRequests = {
    name = "cached_requests2",
    ignoreValueCommaCheck = true
  },
  CDN = {name = "cdn2", ignoreValueCommaCheck = true},
  Local = {name = "local", ignoreValueCommaCheck = true},
  User = {name = "user", ignoreTypeCheck = true},
  Misc = {name = "misc", ignoreTypeCheck = true},
  Task = {name = "mainTask"},
  OrderMeta = {name = "orderMeta"},
  Account = {name = "account", ignoreValueCommaCheck = true},
  Config = {name = "config", ignoreValueCommaCheck = true},
  Survey = {name = "survey"},
  OpenFunc = {
    name = "openFunc",
    columns = {
      id = EDBValueType.Text,
      state = EDBValueType.Integer
    },
    primaryKey = {"id"}
  },
  Energy = {
    name = "energy",
    columns = {
      id = EDBValueType.Text,
      energyValue = EDBValueType.Integer,
      generateTime = EDBValueType.Integer,
      infiniteTime = EDBValueType.Integer
    },
    primaryKey = {"id"},
    arrOrder = {
      "id",
      "energyValue",
      "generateTime",
      "infiniteTime"
    }
  },
  Item = ItemConfigCreator("item"),
  Board = BoardConfigCreator("board"),
  CacheItem = CacheItemConfigCreator("cacheItem"),
  Inventory = {
    name = "inventory",
    columns = {
      itemId = EDBValueType.Text,
      storeTime = EDBValueType.Integer,
      codeStr = EDBValueType.Text
    },
    primaryKey = {"itemId"},
    arrOrder = {
      "itemId",
      "storeTime",
      "codeStr"
    }
  },
  Slot = {
    name = "slot",
    columns = {
      slot = EDBValueType.Text,
      id = EDBValueType.Text
    },
    primaryKey = {"slot"},
    arrOrder = {"slot", "id"}
  },
  ShopItem = {
    name = "shopItem",
    columns = {
      id = EDBValueType.Text,
      shopType = EDBValueType.Text,
      leftCount = EDBValueType.Integer,
      startCount = EDBValueType.Integer,
      costCount = EDBValueType.Integer,
      costType = EDBValueType.Text,
      itemCode = EDBValueType.Text,
      multiplier = EDBValueType.Integer,
      maxCostCount = EDBValueType.Integer
    },
    primaryKey = {"id"},
    arrOrder = {
      "id",
      "shopType",
      "leftCount",
      "costCount",
      "costType",
      "itemCode",
      "multiplier",
      "maxCostCount",
      "startCount"
    }
  },
  Shop = {name = "shop", ignoreTypeCheck = true},
  ItemUnlock = {
    name = "ItemUnlock",
    columns = {
      type = EDBValueType.Text,
      state = EDBValueType.Integer
    },
    primaryKey = {"type"},
    arrOrder = {"type", "state"}
  },
  IAPOrder = {
    name = "iapOrder",
    columns = {
      orderId = EDBValueType.Text,
      status = EDBValueType.Integer
    },
    primaryKey = {"orderId"}
  },
  Bundle = {
    name = "bundle",
    columns = {
      bundleId = EDBValueType.Text,
      leftCount = EDBValueType.Integer,
      lastTime = EDBValueType.Integer,
      refreshTime = EDBValueType.Integer,
      bundleType = EDBValueType.Text,
      purchaseId = EDBValueType.Text,
      groupId = EDBValueType.Integer
    },
    primaryKey = {"bundleId"},
    arrOrder = {
      "bundleId",
      "leftCount",
      "lastTime",
      "refreshTime",
      "bundleType",
      "purchaseId",
      "groupId"
    }
  },
  BundleMeta = {name = "bundleMeta"},
  Bundles = {
    name = "bundles",
    columns = {
      name = EDBValueType.Text,
      data = EDBValueType.Text
    },
    primaryKey = {"name"},
    arrOrder = {"name", "data"},
    ignoreValueCommaCheck = true
  },
  Tutorial = {
    name = "tutorial",
    columns = {
      id = EDBValueType.Text,
      state = EDBValueType.Integer,
      ongoingDatas = EDBValueType.Text
    },
    primaryKey = {"id"},
    arrOrder = {
      "id",
      "state",
      "ongoingDatas"
    }
  },
  Order = OrderConfigCreator("orders"),
  Activity = {
    name = "activity",
    columns = {
      name = EDBValueType.Text,
      data = EDBValueType.Text
    },
    primaryKey = {"name"},
    arrOrder = {"name", "data"},
    ignoreValueCommaCheck = true
  },
  WeddingDay = {
    name = "weddingDay",
    columns = {
      name = EDBValueType.Text,
      data = EDBValueType.Text
    },
    primaryKey = {"name"},
    arrOrder = {"name", "data"}
  },
  Rate = {
    name = "rate",
    columns = {
      id = EDBValueType.Text,
      configId = EDBValueType.Integer,
      popupCount = EDBValueType.Integer,
      canPopup = EDBValueType.Integer,
      rated = EDBValueType.Integer,
      triggeredConfig = EDBValueType.Text,
      activeConfigId = EDBValueType.Integer
    },
    primaryKey = {"id"}
  },
  Ad = {
    name = "ad",
    columns = {
      name = EDBValueType.Text,
      count = EDBValueType.Integer,
      lastShowTime = EDBValueType.Integer
    },
    primaryKey = {"name"},
    arrOrder = {
      "name",
      "count",
      "lastShowTime"
    }
  },
  Skin = {
    name = "skin",
    columns = {
      id = EDBValueType.Integer,
      value = EDBValueType.Integer
    },
    primaryKey = {"id"},
    arrOrder = {"id", "value"}
  },
  NewSkin = {
    name = "newSkin",
    columns = {
      id = EDBValueType.Text,
      value = EDBValueType.Integer
    },
    primaryKey = {"id"},
    arrOrder = {"id", "value"}
  },
  NoticeState = {
    name = "noticeState",
    columns = {
      id = EDBValueType.Text,
      state = EDBValueType.Integer
    },
    primaryKey = {"id"},
    arrOrder = {"id", "state"}
  },
  UserProfile = {
    name = "userProfile"
  },
  ReturnUser = {name = "returnUser"},
  TaskMetaData = {name = "taskMeta", ignoreTypeCheck = true},
  FinishedTasks = {
    name = "finishedTasks",
    columns = {
      chapterId = EDBValueType.Integer,
      taskId = EDBValueType.Integer,
      value = EDBValueType.Integer
    },
    primaryKey = {"chapterId", "taskId"},
    arrOrder = {
      "chapterId",
      "taskId",
      "value"
    }
  },
  LocalRewards = {
    name = "localRewards"
  },
  BISync = {name = "biSync", ignoreTypeCheck = true}
}
if GameConfig.IsTestMode() then
  EDBTableConfigs.Test = {name = "Test", ignoreValueCommaCheck = true}
  local validTypes = {}
  for k, v in pairs(EDBValueType) do
    validTypes[v] = true
  end
  local columns
  for k, data in pairs(EDBTableConfigs) do
    if data.columns then
      for key, valueType in pairs(data.columns) do
        if not validTypes[valueType] then
          Log.Error("[DBTableConfigs]Column Type Error in Table [" .. data.name .. "] Column [" .. key .. "] Type [" .. valueType .. "]")
        end
      end
    end
    if data.arrOrder ~= nil then
      columns = Table.ShallowCopy(data.columns)
      for _, column in ipairs(data.arrOrder) do
        if columns[column] == nil then
          Log.Error("[DBTableConfigs]invalid column [" .. column .. "] in arrOrder of Table [" .. k .. "]")
        else
          columns[column] = nil
        end
      end
      for column, _ in pairs(columns) do
        Log.Error("[DBTableConfigs]arrOrder doesn't include column [" .. column .. "] in Table [" .. k .. "]")
      end
    end
  end
end
