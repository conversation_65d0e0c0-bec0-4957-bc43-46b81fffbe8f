BakeOutEntry = setmetatable({}, HudGeneralButton)
BakeOutEntry.__index = BakeOutEntry

function BakeOutEntry:Awake()
  HudGeneralButton.Awake(self)
  self.m_model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  if self.m_model == nil then
    return
  end
  AddHandlerAndRecordMap(self.m_model.event, BakeOutEventType.StateChanged, {
    obj = self,
    method = self.UpdateContent
  })
  AddHandlerAndRecordMap(self.m_model.event, BakeOutEventType.AcquireToken, {
    obj = self,
    method = self.UpdateContent
  })
  AddHandlerAndRecordMap(self.m_model.event, BakeOutEventType.UpdateRank, {
    obj = self,
    method = self.UpdateContent
  })
  AddHandlerAndRecordMap(self.m_model.event, BakeOutEventType.ModeChanged, {
    obj = self,
    method = self.UpdateContent
  })
  self:UpdateContent()
end

function BakeOutEntry:OnD<PERSON>roy()
  HudGeneralButton.OnDestroy(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
end

function BakeOutEntry:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    if self.m_model:GetState() == ActivityState.Ended then
      self.m_countDownText.text = GM.GameTextModel:GetText("bakeout_settlement_title")
    else
      local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
      self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
    end
  else
    self.m_countDownText.text = GM.GameTextModel:GetText("bakeout_end_title")
  end
end

function BakeOutEntry:UpdateContent()
  if self.m_model == nil then
    return
  end
  local bakeoutOn = self.m_model:IsBakeOutModeOn()
  if bakeoutOn ~= self.gameObject.activeSelf then
    self.gameObject:SetActive(bakeoutOn)
  end
  if not bakeoutOn then
    return
  end
  self.gameObject:SetActive(true)
  local rank = self.m_model:GetDisplayCurRank()
  self.m_rankText.text = rank or ""
  UIUtil.SetActive(self.m_rankGo, rank ~= nil)
  self.m_tokenText.text = self.m_model:GetToken() or "0"
  self:UpdatePerSecond()
end

function BakeOutEntry:OnBtnClicked()
  if self.m_model == nil then
    return
  end
  if self.m_model:GetState() == ActivityState.Started then
    if self.m_model:IsEnlist() then
      GM.UIManager:OpenView(UIPrefabConfigName.BakeOutMainWindow, nil, true)
    else
      self.m_model:OnEnlist()
    end
  elseif self.m_model:GetState() == ActivityState.Ended then
    GM.UIManager:OpenView(UIPrefabConfigName.BakeOutInSettlementWindow, true)
  elseif self.m_model:GetState() == ActivityState.Released then
    if self.m_model:GetFinalRankInfos() ~= nil then
      GM.UIManager:OpenView(UIPrefabConfigName.BakeOutResultWindow, true)
    else
      self.m_model:SetWindowReopen()
      self.m_model:OnActivityFinished(true)
    end
  end
end
