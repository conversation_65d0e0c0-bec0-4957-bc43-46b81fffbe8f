BundleOneByOneModel = setmetatable({}, BundleChainModel)
BundleOneByOneModel.__index = BundleOneByOneModel
local BUNDLE_COUNT = 3

function BundleOneByOneModel:_LoadLocalConfigs(...)
  BundleChainModel._LoadLocalConfigs(self, ...)
  local data
  for _, dataGroup in ipairs(self.m_dataGroups) do
    for index = 1, #dataGroup:GetBundleIds() do
      data = dataGroup:GetConfigDataByIndex(index)
      Log.Assert(not data:IsFree(), "OneByOne礼包 " .. data:GetBundleId() .. " 必须全为付费项！")
      if data:IsCircle() then
        Log.Error("OneByOne礼包禁止配置循环！" .. data:GetBundleId())
      end
      local strDiscountInfo = data:GetDiscountTag()
      if not (index ~= 1 or StringUtil.IsNilOrEmpty(strDiscountInfo)) or 1 < index and tonumber(strDiscountInfo) == nil then
        Log.Error("OneByOne礼包折扣信息配置错误！" .. data:GetBundleId())
      end
    end
    Log.Assert(#dataGroup:GetBundleIds() == BUNDLE_COUNT, "OneByOne礼包 " .. dataGroup:GetGroupId() .. " 必须配置为三轮！")
  end
end

function BundleOneByOneModel:_IsGroupDataEligible(dataGroup)
  if not dataGroup then
    return false
  end
  if self:GetRestDuration(dataGroup) >= 0 and self:GetCurChainStep(dataGroup) <= BUNDLE_COUNT then
    return true
  end
  return BundleChainModel._IsGroupDataEligible(self, dataGroup)
end

function BundleOneByOneModel:_OnBuyFinished(dataGroup, bundleId)
  if self:GetCurChainStep(dataGroup) == 2 then
    self:_RecordBuyNumData(dataGroup)
  end
  if self:HasPurchaseFinished(dataGroup) then
    self:OnBundleStateChanged(dataGroup)
    self:_FinishAndEnterBuyCD(dataGroup)
  end
end
