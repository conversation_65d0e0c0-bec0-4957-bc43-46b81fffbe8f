TestAccumulatedLogInfo = {}
local strCreateTestLog = ""
local TestFuncAddLog = function(strLog)
  if GameConfig.IsTestMode() then
    strCreateTestLog = strCreateTestLog .. " " .. strLog .. "\n"
  end
end
local PrintSortListTableAndEnter = function(ListTable, isNumber)
  local sortList = {}
  local strList = {}
  for Key, _ in pairs(ListTable) do
    local strKey = Key
    if isNumber then
      strKey = tonumber(strKey)
    end
    if strKey == nil then
      table.insert(strList, Key)
    else
      table.insert(sortList, strKey)
    end
  end
  table.sort(sortList)
  table.sort(strList)
  for _, Key in pairs(sortList) do
    if isNumber then
      Key = tostring(Key)
    end
    local Value = ListTable[Key]
    TestFuncAddLog(Key .. " : " .. tostring(Value))
  end
  for _, Key in pairs(strList) do
    if isNumber then
      Key = tostring(Key)
    end
    local Value = ListTable[Key]
    TestFuncAddLog(Key .. " : " .. tostring(Value))
  end
  TestFuncAddLog("--------------------------------------------")
end
local PrintListSort = function(ItemList)
  local sortList = {}
  for _, value in pairs(ItemList) do
    table.insert(sortList, value)
  end
  table.sort(sortList)
  local strTarget = ""
  for _, Value in pairs(sortList) do
    strTarget = strTarget .. tostring(Value) .. " "
  end
  TestFuncAddLog(strTarget)
  TestFuncAddLog("--------------------------------------------")
end

function TestAccumulatedLogInfo.Start()
  strCreateTestLog = ""
end

function TestAccumulatedLogInfo.TestPrintShopFlashSaleInfo(aLastShopItem, mapChainLastShopNoBuyItem, directResult, mapPool, mapNoSellItems, mapWeight, arrPocketSelected, arrResult)
  TestFuncAddLog("--------------商城闪售刷新了----------------")
  local aListType = {}
  for k, v in pairs(aLastShopItem) do
    aListType[v.itemCode] = true
  end
  TestFuncAddLog("上一轮闪售的棋子")
  PrintSortListTableAndEnter(aListType)
  TestFuncAddLog("上一次商城中出现的合成线，没有购买哪些棋子")
  PrintSortListTableAndEnter(mapChainLastShopNoBuyItem)
  TestFuncAddLog("订单最终需求棋子及数量")
  PrintSortListTableAndEnter(directResult)
  TestFuncAddLog("扩展前的售卖池及数量")
  PrintSortListTableAndEnter(mapPool)
  TestFuncAddLog("扩展后因不可售卖而被剔除的棋子")
  PrintSortListTableAndEnter(mapNoSellItems)
  TestFuncAddLog("扩展后的售卖池及权重")
  PrintSortListTableAndEnter(mapWeight)
  TestFuncAddLog("兜底随机出的棋子")
  PrintListSort(arrPocketSelected)
  TestFuncAddLog("输出结果")
  TestFuncAddLog("结果 code")
  if arrResult == nil then
    arrResult = {}
  end
  for k, v in pairs(arrResult) do
    TestFuncAddLog(v.itemCode)
  end
end

function TestAccumulatedLogInfo.TestPrintBakeOutOrderCreation(mapExtraWeight, mapExtraWeightStr, orderId, requirement)
  TestFuncAddLog("--------------通关活动随机订单刷新了----------------")
  TestFuncAddLog("随机订单：" .. orderId)
  local mapRandomDishes = GM.MainBoardModel:GetOrderModel().dataModel:GetRandomDishMap()
  TestFuncAddLog("配置在当前房间的随机订单菜品")
  PrintSortListTableAndEnter(mapRandomDishes)
  local mapAllMaterials = {}
  for dishType, _ in pairs(mapRandomDishes) do
    local arrAllMaterials = GM.ItemDataModel:GetAllMaterials(dishType)
    local str = ""
    for _, materialType in ipairs(arrAllMaterials) do
      str = str .. materialType .. " "
    end
    mapAllMaterials[dishType] = str
  end
  TestFuncAddLog("每个菜品需要的所有食材")
  PrintSortListTableAndEnter(mapAllMaterials)
  local codeCountMap = GM.MainBoardModel:GetCodeCountMap(true, false, true)
  local mapFilteredOut = {}
  local itemConfig
  for itemCode, _ in pairs(codeCountMap) do
    itemConfig = GM.ItemDataModel:GetModelConfig(itemCode, true)
    if not (itemConfig and itemConfig.Category) or not Table.ListContain(itemConfig.Category, 1) then
      mapFilteredOut[itemCode] = true
    end
  end
  for itemCode, _ in pairs(mapFilteredOut) do
    codeCountMap[itemCode] = nil
  end
  TestFuncAddLog("棋盘和仓库包含的棋子及数量")
  PrintSortListTableAndEnter(codeCountMap)
  TestFuncAddLog("菜品额外权重")
  PrintSortListTableAndEnter(mapExtraWeight)
  TestFuncAddLog("菜品额外权重细节")
  PrintSortListTableAndEnter(mapExtraWeightStr)
  TestFuncAddLog("随机结果：" .. requirement)
end

function TestAccumulatedLogInfo.TestPrintCleanTaskCost(mapCleanGoldCost, totalCleanGold)
  TestFuncAddLog("--------------任务清场金币消耗刷新了----------------")
  TestFuncAddLog("当前剩余清场金币数量：" .. totalCleanGold)
  TestFuncAddLog("每个任务添加的清场金币数量：")
  PrintSortListTableAndEnter(mapCleanGoldCost)
end

function TestAccumulatedLogInfo.TestPrintCleanOrderCreation(arrNotInConfigUnrelaventTypes, mapAvailableCleanItems, arrAvailableTypes, arrUncookableDs, arrAvailableCleanItems)
  TestFuncAddLog("--------------清场订单刷新了----------------")
  TestFuncAddLog("（棋盘+仓库中）当前订单组用不上，但 「不可」 用于清场订单的棋子类型：")
  PrintSortListTableAndEnter(arrNotInConfigUnrelaventTypes)
  TestFuncAddLog("（棋盘+仓库中）当前订单组用不上，且可用于清场订单的棋子个数：")
  PrintSortListTableAndEnter(mapAvailableCleanItems)
  TestFuncAddLog("（棋盘+仓库中）当前订单组用不上，且可用于清场订单的棋子类型（按分数从高到低排序）：")
  PrintSortListTableAndEnter(arrAvailableTypes)
  TestFuncAddLog("因没有对应的厨具而不能做的菜DS：")
  PrintSortListTableAndEnter(arrUncookableDs)
  TestFuncAddLog("最终可选取的棋子列表（按分数从高到低排序）：")
  PrintSortListTableAndEnter(arrAvailableCleanItems)
end

function TestAccumulatedLogInfo.LogNow()
  if not GM.UIManager:CanShowTestUI() then
    return
  end
  Log.Info(strCreateTestLog)
end
