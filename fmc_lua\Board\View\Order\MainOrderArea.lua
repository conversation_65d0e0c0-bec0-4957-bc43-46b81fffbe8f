local nStart = 0
local autoIncrease = function()
  nStart = nStart + 1
  return nStart
end
EOrderAreaButtonPriority = {
  TaskBubble = autoIncrease(),
  BoardCacheRoot = autoIncrease(),
  Other = autoIncrease()
}
MainOrderArea = setmetatable({}, BaseOrderArea)
MainOrderArea.__index = MainOrderArea

function MainOrderArea:Init(boardView)
  BaseOrderArea.Init(self, boardView)
  EventDispatcher.AddListener(EEventType.StrongTutorialStart, self, self.ResetToOrderGroupAnim)
  EventDispatcher.AddListener(EEventType.OrderStatusChanged, self, self._OnOrderStatusChanged)
  EventDispatcher.AddListener(EEventType.DailyTaskFinishTask, self, self._OnDailyTaskFinishTask)
  EventDispatcher.AddListener(EEventType.OrderAnimationFinished, self, self.HideSurpriseChestRewardBubble)
  EventDispatcher.AddListener(EEventType.CacheRootNodeStateChanged, self, self.OnCacheRootNodeChanged)
  for _, activityDefinition in pairs(SurpriseChestActivityDefinition) do
    if activityDefinition.StateChangedEvent ~= nil then
      EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self.HideSurpriseChestRewardBubble)
    end
  end
  local models = GM.ActivityManager:GetModels()
  for _, model in pairs(models) do
    if model.GetBoardEntryShowConfig then
      local entryConfig = model:GetBoardEntryShowConfig()
      local entryCountName = GetEntryCountName(entryConfig.eEntryRootKey)
      self[entryCountName] = self[entryCountName] or 0
      local func = self:_StateChangedFunctionCreator(model, entryConfig)
      EventDispatcher.AddListener(entryConfig.statusChangeEvent, self, func)
      if entryConfig.extraListenEvent then
        EventDispatcher.AddListener(entryConfig.extraListenEvent, self, func)
      end
      func()
    end
  end
  if GM.ConfigModel:UseNewCacheLayout() then
    UIUtil.SetActive(self.m_cacheRootNode.gameObject, true)
    self.m_taskBubble.transform:SetParent(self.m_cacheRootNode)
    self.m_boardCacheRoot.transform:SetParent(self.m_cacheRootNode)
  else
    UIUtil.SetActive(self.m_cacheRootNode.gameObject, false)
  end
end

function MainOrderArea:GetTaskBubble()
  return self.m_taskBubble
end

function MainOrderArea:GetOrderRoot()
  return self.m_orderRoot
end

function MainOrderArea:GetDashActivityOrder()
  for activityType, _ in pairs(DashActivityDefinition) do
    local orderName = "m_" .. activityType .. "Order"
    if self[orderName] ~= nil then
      return self[orderName]
    end
  end
  return nil
end

local HostOrderCells = {}

function MainOrderArea:_GetHostOrderCells()
  for k, _ in pairs(HostOrderCells) do
    HostOrderCells[k] = nil
  end
  return HostOrderCells
end

function MainOrderArea:OnCacheRootNodeChanged()
  if not GM.ConfigModel:UseNewCacheLayout() then
    return
  end
  local paddingR = 20
  local bShowing = self.m_boardCacheRoot:IsShowing() or self.m_taskBubble:IsShowing()
  self.m_cacheRootLayout.padding.right = bShowing and paddingR or 0
  self:ForceRebuildLayout()
end

function MainOrderArea:ScrollToBoardCacheRoot(needAnimation)
  local targetTrans = GM.ConfigModel:UseNewCacheLayout() and self.m_cacheRootNode or self.m_boardCacheRoot
  self:ScrollToRectTransformVisible(targetTrans, needAnimation)
end

function MainOrderArea:ScrollToBoardTaskBubble(needAnimation)
  local targetTrans = GM.ConfigModel:UseNewCacheLayout() and self.m_cacheRootNode or self.m_taskBubble
  self:ScrollToRectTransformVisible(targetTrans, needAnimation)
end

function MainOrderArea:ScrollToActivityEntry(activityType, needAnimation, toLeft)
  local model = GM.ActivityManager:GetModel(activityType)
  local entryConfig = model and model.GetBoardEntryShowConfig and model:GetBoardEntryShowConfig()
  if entryConfig and entryConfig.eEntryRootKey then
    if toLeft then
      return self:_ScrollToRectTransform(self[GetEntryRootName(entryConfig.eEntryRootKey)], needAnimation)
    end
    return self:ScrollToRectTransformVisible(self[GetEntryRootName(entryConfig.eEntryRootKey)], needAnimation)
  end
  return 0
end

function MainOrderArea:_OnOrderStatusChanged()
  if self.m_mapCells ~= nil then
    local order
    for _, cell in pairs(self.m_mapCells) do
      order = cell:GetOrder()
      if order:GetState() ~= OrderState.Finished then
        cell:Init(order, self)
      end
    end
  end
  self.m_boardView:OnOrderStateChanged()
end

function MainOrderArea:_StateChangedFunctionCreator(model, entryCfg)
  return function()
    local entryTbName = self:_GetOrderTbNameByActivityType(model:GetType())
    local entryRootName = GetEntryRootName(entryCfg.eEntryRootKey)
    local entryRootTrans = self[entryRootName]
    if entryRootTrans == nil then
      Log.Error(entryRootName .. " 为空! " .. tostring(entryCfg.entryPrefabName))
      return
    end
    local entryCountName = GetEntryCountName(entryCfg.eEntryRootKey)
    if entryCfg.checkFun() then
      UIUtil.SetActive(entryRootTrans.gameObject, true)
      if self[entryTbName] == nil then
        local config = GM.DataResource.UIPrefabConfig:GetConfig(entryCfg.entryPrefabName)
        local callback = function(gameObject)
          if self[entryTbName] or not entryCfg.checkFun() then
            AddressableLoader.Destroy(gameObject)
            self[entryCountName] = self[entryCountName] - 1
            return
          end
          local boardBubble = gameObject:GetLuaTable()
          boardBubble:Init(model, self)
          self[entryTbName] = boardBubble
          LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_content)
        end
        self[entryCountName] = self[entryCountName] + 1
        GM.ResourceLoader:LoadPrefab(config, entryRootTrans, V3Zero, callback)
      elseif entryCfg.refreshOrderWhenStateChange and self[entryTbName].OnRefreshOrder ~= nil then
        self[entryTbName]:OnRefreshOrder()
      end
    else
      if self[entryTbName] ~= nil then
        AddressableLoader.Destroy(self[entryTbName].gameObject)
        self[entryTbName] = nil
        self[entryCountName] = self[entryCountName] - 1
        DelayExecuteFuncInView(function()
          LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_content)
        end, 0.1, self)
      end
      UIUtil.SetActive(entryRootTrans.gameObject, self[entryCountName] and 0 < self[entryCountName])
    end
  end
end

function MainOrderArea:_GetOrderTbNameByActivityType(activityType)
  Log.Assert(activityType ~= nil, "should not be nil")
  return "m_" .. tostring(activityType) .. "Order"
end

function MainOrderArea:GetOrderTbByActivityType(activityType)
  local orderName = self:_GetOrderTbNameByActivityType(activityType)
  local orderGoTb = self[orderName]
  return orderGoTb
end

function MainOrderArea:GetIconAreaByActivityType(activityType)
  local orderGoTb = self:GetOrderTbByActivityType(activityType)
  if orderGoTb ~= nil and orderGoTb.GetIconArea ~= nil then
    return orderGoTb:GetIconArea()
  end
end

function MainOrderArea:_OnDailyTaskFinishTask(msg)
  if GM.ActivityManager:GetModel(ActivityType.DailyTask):CanAcquireFinalRewards() then
    self:ScrollToDailyTaskBoardBubble(false)
  end
end

function MainOrderArea.CalculateRealFlyTargetPos(endPos, priority)
  local mainOrderArea = MainBoardView.GetInstance():GetOrderArea()
  local ratio = mainOrderArea.transform.lossyScale.x
  local deltaPosX = 0
  if not GM.ConfigModel:UseNewCacheLayout() then
    if priority > EOrderAreaButtonPriority.TaskBubble then
      local taskBubble = mainOrderArea:GetTaskBubble()
      if taskBubble and (taskBubble:IsPlayingAnimation() or taskBubble:CanPlayAnimaion()) then
        deltaPosX = deltaPosX + taskBubble:GetAnimationLeftWidth()
      end
    end
    if priority > EOrderAreaButtonPriority.BoardCacheRoot then
      local boardCacheRoot = mainOrderArea:GetBoardCacheRoot()
      if boardCacheRoot ~= nil and (boardCacheRoot:IsPlayingAnimation() or boardCacheRoot:CanPlayAnimation()) then
        deltaPosX = deltaPosX + boardCacheRoot:GetAnimationLeftWidth()
      end
    end
  end
  deltaPosX = deltaPosX + mainOrderArea:GetScrollDeltaPosX()
  endPos = endPos + Vector3(deltaPosX * ratio, 0, 0)
  return endPos
end

function MainOrderArea:GetScrollDeltaPosX()
  local contentWidth = self.m_content.rect.width
  local width = self.transform.rect.width
  if self.m_mapCells ~= nil then
    local order
    for _, cell in pairs(self.m_mapCells) do
      order = cell:GetOrder()
      if order:GetState() == OrderState.Finished and cell:GetSurpriseChest() == nil then
        contentWidth = contentWidth - cell.transform.rect.width - self.m_orderRootLayoutGroup.spacing
      end
    end
  end
  for _, order in pairs(self.m_boardView:GetModel():GetOrders()) do
    if self.m_mapCells[order] == nil then
      contentWidth = contentWidth + OrderCell.GetCellSizeDelta(order).x + self.m_orderRootLayoutGroup.spacing
    end
  end
  local curPosX = self.m_content.anchoredPosition.x
  if width <= curPosX + contentWidth then
    return 0
  end
  if width >= contentWidth then
    return -curPosX
  end
  return width - (curPosX + contentWidth)
end

function MainOrderArea:AnimToOrderGroup()
  local animNodeTrans = self:_GetActivityNodeTrans()
  if #animNodeTrans == 0 then
    return
  end
  local seq = DOTween.Sequence()
  self.m_anim2OrderGroupSeq = seq
  local animTrans
  local delay = 0.3
  local totalDelay = 0
  self.m_anim2OrderGroupTrans = {}
  for i = 1, #animNodeTrans do
    animTrans = animNodeTrans[i]:GetChild(0)
    if animTrans then
      local originY = animTrans.anchoredPosition.y
      local originScale = animTrans.localScale
      self.m_anim2OrderGroupTrans[animTrans] = {originY = originY, originScale = originScale}
      seq:Insert(totalDelay, animTrans:DOAnchorPosY(originY + 30, 0.15):SetEase(Ease.OutSine))
      seq:Insert(totalDelay + 0.15, animTrans:DOAnchorPosY(originY, 0.15):SetEase(Ease.InSine))
      seq:Insert(totalDelay + 0.3, animTrans:DOAnchorPosY(originY + 20, 0.1):SetEase(Ease.OutSine))
      seq:Insert(totalDelay + 0.4, animTrans:DOAnchorPosY(originY, 0.1):SetEase(Ease.InSine))
      seq:Insert(totalDelay + 0.06, animTrans:DOScale(originScale * 1.05, 0.15):SetEase(Ease.InOutSine))
      seq:Insert(totalDelay + 0.06 + 0.15, animTrans:DOScale(originScale, 0.15):SetEase(Ease.InOutSine))
      totalDelay = totalDelay + delay
      delay = delay * 0.8
    end
  end
  seq:InsertCallback(totalDelay + 0.3, function()
    self:ScrollToOrderGroup(true)
    self.m_anim2OrderGroupSeq = nil
    self.m_anim2OrderGroupTrans = nil
  end)
end

function MainOrderArea:ResetToOrderGroupAnim()
  if self.m_anim2OrderGroupSeq ~= nil then
    self.m_anim2OrderGroupSeq:Kill()
    self.m_anim2OrderGroupSeq = nil
  end
  if self.m_anim2OrderGroupTrans ~= nil then
    for trans, info in pairs(self.m_anim2OrderGroupTrans) do
      UIUtil.SetAnchoredPosition(trans, nil, info.originY)
      trans.localScale = info.originScale
    end
    self.m_anim2OrderGroupTrans = nil
  end
end

function MainOrderArea:_GetActivityNodeTrans()
  local result = {}
  local parentTrans = self.m_content
  local endTrans = self.m_cacheRootNode
  local childCount = parentTrans.childCount
  local child
  for i = 0, childCount - 1 do
    child = parentTrans:GetChild(i)
    if child == endTrans then
      break
    end
    if child.gameObject.activeSelf and child ~= self.m_boardCacheRoot.transform and child ~= self.m_taskBubble.transform and child ~= self.m_noOrderTaskBubble.transform and child ~= self.m_noOrderBubbleTrans then
      table.insert(result, child)
    end
  end
  return result
end

function MainOrderArea:ShowSurpriseChestRewardBubble(activityType, activityDefinition, rewards, viewData)
  if self.m_surpriseChestRewardBubble == nil then
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.SurpriseChestRewardBubble), self.m_surpriseChestRewardBubbleNode, Vector3.zero, function(go)
      if go ~= nil and not go:IsNull() then
        self.m_surpriseChestRewardBubble = go:GetLuaTable()
        self.m_surpriseChestRewardBubble:Show(activityType, activityDefinition, rewards, viewData)
      end
    end)
  else
    self.m_surpriseChestRewardBubble:Show(activityType, activityDefinition, rewards, viewData)
  end
  local scrollRect = self.m_scrollRect
  if scrollRect.horizontalNormalizedPosition < 0 then
    scrollRect.horizontalNormalizedPosition = 0
  elseif scrollRect.horizontalNormalizedPosition > 1 then
    scrollRect.horizontalNormalizedPosition = 1
  end
  self.m_skipHideRewardTipOnce = true
end

function MainOrderArea:HideSurpriseChestRewardBubble()
  if self.m_surpriseChestRewardBubble ~= nil then
    self.m_surpriseChestRewardBubble:Hide()
  end
end

function MainOrderArea:OnScrolled()
  BaseOrderArea.OnScrolled(self)
  if self.m_skipHideRewardTipOnce then
    self.m_skipHideRewardTipOnce = nil
    return
  end
  self:HideSurpriseChestRewardBubble()
end
