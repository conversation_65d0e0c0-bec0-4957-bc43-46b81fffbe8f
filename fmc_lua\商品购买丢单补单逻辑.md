# 商品购买丢单后的补单逻辑及内容

## 1. 概述

游戏中的商品购买系统具有完善的丢单检测和补单机制，确保玩家在支付成功但未收到商品时能够自动或手动恢复购买的商品。

## 2. 核心补单机制

### 2.1 订单状态记录
- **订单ID记录**: 每个成功的购买都会记录唯一的订单ID
- **状态标记**: 使用 `_SetOrderIdAwarded(orderId)` 标记订单已发放奖励
- **重复检查**: 通过 `_CanAwardForOrderId(purchaseInfo)` 检查订单是否已发放

### 2.2 购买信息存储
购买过程中会临时存储关键信息，用于补单：

```lua
-- 存储购买信息的关键字段
local purchaseInfo = {
    eIAPType = eIAPType,           -- 商品类型
    productId = productData.productId,  -- 产品ID
    orderId = orderId,             -- 订单ID
    realMoney = realMoney,         -- 实际金额
    currency = currency,           -- 货币类型
    rewards = rewards,             -- 奖励内容
    purchaseToken = purchaseToken  -- 购买凭证
}
```

## 3. 补单触发机制

### 3.1 自动补单触发时机
1. **游戏启动时**: 自动检查未完成的购买
2. **网络恢复时**: 重新验证待处理的订单
3. **支付回调时**: 处理延迟到达的支付结果

### 3.2 手动补单触发
- **恢复购买按钮**: 玩家可在商店界面手动触发 `RestorePurchase()`
- **测试补单**: 开发模式下可通过测试界面触发补单

## 4. 不同商品类型的补单逻辑

### 4.1 宝石商品补单
```lua
-- 宝石商品的补单逻辑
if eIAPType == EIAPType.getgem_1 or ... or EIAPType.getgem_6 then
    local data = GM.ShopDataModel:GetIAPConfigData(EShopType.Diamonds, eIAPType)
    GM.PropertyDataManager:AcquireWithCollectAnimation(data.goods, EPropertySource.Buy)
    return true
end
```

### 4.2 礼包商品补单
礼包商品有三种补单策略：

#### 策略1: 优先使用存储的购买数据
```lua
-- 从存储中获取购买信息进行补发
local purchaseInfo = self:_GetRecordPurchaseInfo(dataGroup:GetGroupId())
if purchaseInfo ~= nil and purchaseInfo.purchaseId == eIAPType then
    self:_AcquireRecoverRewards(rewards, purchaseInfo.uiCode)
    Log.Info("链式礼包补单，优先取存储的购买数据进行补发")
    return true
end
```

#### 策略2: 当前步骤匹配补单
```lua
-- 当前step与购买项正好相等的情况
if curDataGroup ~= nil then
    if self:GetBundleState(curDataGroup) == EBundleState.Opening then
        self:_SetPaidThisRound(curDataGroup, true)
        self:_Set2NextStep(curDataGroup)
        recoverRewards = curBundleData:GetGoods()
    end
    Log.Info("链式礼包补单，第二选择: 当前step与购买项正好相等的情况")
    return true
end
```

#### 策略3: 兜底补单
```lua
-- 最终兜底就是任选一个支付项尝试补发
if backupBundleData ~= nil and backupDataGroup ~= nil then
    local rewards = self:_GetRecordPurchaseRewards(backupDataGroup, true, backupBundleData:GetStep())
    self:_AcquireRecoverRewards(rewards, backupDataGroup:GetBundleUIType())
    Log.Info("链式礼包补单，最终兜底就是任选一个支付项尝试补发")
    return true
end
```

### 4.3 小猪银行补单
```lua
function PiggyBankModel:RestoreIapRewards(iapType)
    -- 检查是否有记录的购买信息
    if iapType == self.m_dbTable:GetValue(DBKey.RecordIapType, DBColumnValue) then
        local accumulatedGemNum = tonumber(self.m_dbTable:GetValue(DBKey.RecordBuyNum, DBColumnValue))
        local full = tonumber(self.m_dbTable:GetValue(DBKey.RecordFull, DBColumnValue)) == 1
        self:_OnBuySuccess(accumulatedGemNum, full, iapType)
        return true
    end
    return false
end
```

### 4.4 活动商品补单
```lua
function SpreeActivityShopModel:RestoreIapRewards(iapType)
    local data = self:_GetBundleDataWithPurchaseId(iapType)
    if data == nil then
        return false
    end
    -- 发放奖励并标记已购买
    RewardApi.AcquireRewards(data.Rewards, EPropertySource.Buy, EBIType.Restore)
    local bundleBaughtKey = string.format(SpreeActivityShopModel.BundleBaughtKey, data.Index)
    self.m_dbTable:Set(bundleBaughtKey, "value", 1)
    return true
end
```

## 5. 补单验证流程

### 5.1 服务器验证
```lua
function InAppPurchaseModel:_VerifyPurchase(purchaseInfo)
    -- 检查订单是否已发放
    if not self:_CanAwardForOrderId(purchaseInfo) then
        self:_ShowFailTip(purchaseInfo)
        return
    end
    
    -- 向服务器发送验证请求
    PayMessage.PaidProof(purchaseInfo, function(result, tbData, reqCtx)
        if tbData.rcode == 0 then
            self:_OnVerifySuccess(purchaseInfo)
        else
            self:_ShowFailTip(purchaseInfo)
        end
    end)
end
```

### 5.2 防重复发放机制
```lua
function InAppPurchaseModel:_CanAwardForOrderId(purchaseInfo)
    local orderId = purchaseInfo.orderId
    if StringUtil.IsNilOrEmpty(orderId) then
        return false
    end
    
    -- 检查Google Play的购买凭证
    if not StringUtil.IsNilOrEmpty(purchaseInfo.purchaseToken) then
        local fakeOrderId = MD5.Encode(purchaseInfo.purchaseToken)
        if self.m_dbTable:GetValue(fakeOrderId, DBColumnOrderStatus) == "1" then
            return false
        end
    end
    
    -- 检查订单状态
    return self.m_dbTable:GetValue(orderId, DBColumnOrderStatus) ~= 1
end
```

## 6. 补单数据存储

### 6.1 购买信息记录
```lua
-- 链式礼包购买信息记录
function BundleChainModel:_RecordPurchaseInfo(groupId, info)
    local encodedInfo = StringUtil.Replace(json.encode(info), ",", "@")
    self:_SetBundleDBData(groupId, DBKey.ChainPurchaseInfo, encodedInfo)
end

-- 获取记录的购买信息
function BundleChainModel:_GetRecordPurchaseInfo(groupId)
    local strInfo = self:_GetBundleDBData(groupId, DBKey.ChainPurchaseInfo)
    if not StringUtil.IsNilOrEmpty(strInfo) then
        return json.decode(StringUtil.Replace(strInfo, "@", ","))
    end
    return nil
end
```

### 6.2 订单状态持久化
- **本地数据库**: 使用DBTable存储订单状态
- **订单ID映射**: 维护订单ID到状态的映射关系
- **购买凭证**: 存储平台特定的购买凭证信息

## 7. 错误处理和日志

### 7.1 补单失败处理
```lua
-- 补单失败时的处理
if not awarded then
    -- 记录失败日志
    self:_LogPayAction(EBIType.FP_RestoreFailed, eIAPType, orderId)
    
    -- 显示失败提示
    if purchaseInfo.userTriggeredRestore then
        GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, 
            "恢复购买失败", "未找到可恢复的购买记录")
    end
    return false
end
```

### 7.2 补单成功日志
```lua
-- 补单成功时的日志记录
self:_LogPayAction(EBIType.SuccessPurchase, purchaseInfo.eIAPType, 
    purchaseInfo.orderId, purchaseInfo.strScene)
GM.BIManager:LogPay(purchaseInfo.productId, purchaseInfo.orderId, 
    purchaseInfo.strScene)
```

## 8. 特殊情况处理

### 8.1 网络异常补单
- **离线购买**: 支持离线状态下的购买记录
- **网络恢复**: 网络恢复后自动重试验证
- **超时重试**: 设置合理的重试机制和超时时间

### 8.2 平台差异处理
- **iOS平台**: 使用App Store的收据验证
- **Android平台**: 使用Google Play的购买凭证
- **测试环境**: 提供模拟支付和补单测试功能

## 9. 补单监控和统计

### 9.1 补单成功率统计
- **补单触发次数**: 记录补单功能的使用频率
- **补单成功率**: 统计补单的成功和失败比例
- **补单类型分布**: 分析不同商品类型的补单情况

### 9.2 异常监控
- **重复补单**: 监控同一订单的重复补单尝试
- **异常订单**: 识别和处理异常的订单状态
- **补单延迟**: 监控补单处理的时间延迟

## 10. 总结

游戏的补单系统通过多层次的验证和恢复机制，确保玩家购买的商品能够可靠地发放。系统支持多种商品类型的补单，具有完善的错误处理和监控机制，为玩家提供良好的购买体验。
