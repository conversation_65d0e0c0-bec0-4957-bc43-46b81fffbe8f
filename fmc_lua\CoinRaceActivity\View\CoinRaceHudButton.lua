CoinRaceHudButton = setmetatable({}, HudPropertyButton)
CoinRaceHudButton.__index = CoinRaceHudButton

function CoinRaceHudButton:Init(activityType)
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_activityDefinition = CoinRaceActivityDefinition[activityType]
  HudPropertyButton.Init(self, self.m_activityDefinition.ActivityTokenPropertyType)
end

function CoinRaceHudButton:_AddListeners()
  if HudPropertyButton._AddListeners(self) then
    EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self.UpdateContent)
  end
  if self.m_activityDefinition ~= nil then
    EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self.OnCoinRaceStateChanged, true)
  end
end

function CoinRaceHudButton:OnCoinRaceStateChanged()
  if self.m_model:GetState() ~= ActivityState.Ended then
    self:UpdateContent()
  end
end

function CoinRaceHudButton:SyncToModelValue()
  HudPropertyButton.SyncToModelValue(self)
  self.m_model:UpdateScoreProgress()
end

function CoinRaceHudButton:UpdateContent()
  HudPropertyButton.SyncToModelValue(self)
end

function CoinRaceHudButton:UpdateValueText()
  HudPropertyButton.UpdateValueText(self)
  local score = math.floor(self.m_value + 0.5)
  local targetScore = self.m_model:GetTargetScore()
  self.m_slider.value = score / targetScore
  self.m_valueText.text = score .. "/" .. targetScore
end
