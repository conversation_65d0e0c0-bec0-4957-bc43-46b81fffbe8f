RoomRole = {}
RoomRole.__index = RoomRole

function RoomRole:Init(roleName)
  self.roleName = roleName
end

function RoomRole:SetFlipX(bFlip)
  self.m_content.transform:SetLocalScaleX(bFlip and -1 or 1)
end

function RoomRole:PlayAnimation()
  Log.Error("virtual method.")
end

RoomChef = setmetatable({}, RoomRole)
RoomChef.__index = RoomChef

function RoomChef:Init(...)
  RoomRole.Init(self, ...)
  local callback = function()
    self:_OnAnimationEnd()
  end
  self.m_loopSpine:SetAnimationCompleteCallback(callback)
end

function RoomChef:PlayAnimation(animationName, onFinish)
  if self.m_onFinish then
    self.m_onFinish()
  end
  self.m_loopSpine:PlayAnimation(animationName)
  self.m_onFinish = onFinish
end

function RoomChef:_OnAnimationEnd()
  if self.m_onFinish then
    self.m_onFinish()
    self.m_onFinish = nil
  end
end

RoomCustomer = setmetatable({}, RoomRole)
RoomCustomer.__index = RoomCustomer

function RoomCustomer:SitOn(seatTrans, bFlipX)
  self:SetFlipX(bFlipX)
  self.transform:SetParent(seatTrans, false)
end
