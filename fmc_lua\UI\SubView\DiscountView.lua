DiscountView = {}
DiscountView.__index = DiscountView

function DiscountView:UpdateContent(discountStr)
  if discountStr ~= nil then
    if string.find(discountStr, ",") then
      local arrStr = StringUtil.Split(discountStr, ",")
      local key = arrStr[1]
      table.remove(arrStr, 1)
      self.m_text.text = GM.GameTextModel:GetText(key, table.unpack(arrStr))
    else
      self.m_text.text = GM.GameTextModel:GetText(discountStr)
    end
  end
  self.gameObject:SetActive(discountStr ~= nil)
end

function DiscountView:UpdateOffDiscount(discount)
  self.m_text.text = GM.GameTextModel:GetText("off_tag", discount)
  UIUtil.SetActive(self.gameObject, true)
end

local emptyText = ""

function DiscountView:UpdateText(text)
  self.m_text.text = text or emptyText
  UIUtil.SetActive(self.gameObject, text ~= nil)
end
