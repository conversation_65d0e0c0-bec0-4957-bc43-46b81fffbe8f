return {
  {
    Id = "30010",
    GroupId = 1,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "it_4_2_4", Count = 1}
  },
  {
    Id = "30020",
    GroupId = 1,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "it_1_2_1_2", Count = 1}
  },
  {
    Id = "30030",
    GroupId = 1,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_friedsf_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30040",
    GroupId = 1,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_3_5", Count = 1}
  },
  {
    Id = "30050",
    GroupId = 1,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30060",
    GroupId = 1,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_friedsf_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "30070",
    GroupId = 1,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_1",
      Count = 1
    }
  },
  {
    Id = "30080",
    GroupId = 2,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_1",
      Count = 2
    }
  },
  {
    Id = "30090",
    GroupId = 2,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillsf_2",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30100",
    GroupId = 2,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "30110",
    GroupId = 2,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_3_4", Count = 1}
  },
  {
    Id = "30120",
    GroupId = 2,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 2
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30130",
    GroupId = 2,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_2_1_1", Count = 1}
  },
  {
    Id = "30140",
    GroupId = 2,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_4", Count = 1}
  },
  {
    Id = "30150",
    GroupId = 3,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_8", Count = 1}
  },
  {
    Id = "30160",
    GroupId = 3,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_4", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30170",
    GroupId = 3,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "it_1_2_5", Count = 1}
  },
  {
    Id = "30180",
    GroupId = 3,
    ChapterId = 3,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "30190",
    GroupId = 3,
    ChapterId = 3,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_2",
      Count = 1
    }
  },
  {
    Id = "30200",
    GroupId = 3,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_3_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30210",
    GroupId = 3,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_3_1_4", Count = 1}
  },
  {
    Id = "30220",
    GroupId = 4,
    ChapterId = 3,
    Requirement_1 = {Type = "ds_juice_1", Count = 2},
    Requirement_2 = {
      Type = "ds_friedmt_4",
      Count = 1
    }
  },
  {
    Id = "30230",
    GroupId = 4,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    }
  },
  {
    Id = "30240",
    GroupId = 4,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30250",
    GroupId = 4,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_3_5", Count = 1},
    Requirement_2 = {Type = "it_1_1_1_1", Count = 1}
  },
  {
    Id = "30260",
    GroupId = 4,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_2",
      Count = 1
    }
  },
  {
    Id = "30270",
    GroupId = 4,
    ChapterId = 3,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30280",
    GroupId = 4,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_2_1_2", Count = 1}
  },
  {
    Id = "30290",
    GroupId = 5,
    ChapterId = 3,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "30300",
    GroupId = 5,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_6",
      Count = 1
    }
  },
  {
    Id = "30310",
    GroupId = 5,
    ChapterId = 3,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30320",
    GroupId = 5,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "30330",
    GroupId = 5,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30340",
    GroupId = 5,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "30350",
    GroupId = 5,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_2",
      Count = 1
    }
  },
  {
    Id = "30360",
    GroupId = 6,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_3_1_4", Count = 1},
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    }
  },
  {
    Id = "30370",
    GroupId = 6,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30380",
    GroupId = 6,
    ChapterId = 3,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "30390",
    GroupId = 6,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_1_1_3", Count = 1},
    Requirement_2 = {Type = "it_1_2_4", Count = 1}
  },
  {
    Id = "30400",
    GroupId = 6,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_3_5", Count = 1},
    Requirement_2 = {Type = "it_2_2_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30410",
    GroupId = 6,
    ChapterId = 3,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "it_2_1_9", Count = 1}
  },
  {
    Id = "30420",
    GroupId = 6,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_2",
      Count = 1
    }
  },
  {
    Id = "30430",
    GroupId = 7,
    ChapterId = 3,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_7",
      Count = 1
    }
  },
  {
    Id = "30440",
    GroupId = 7,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_4", Count = 2}
  },
  {
    Id = "30450",
    GroupId = 7,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30460",
    GroupId = 7,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_10",
      Count = 1
    }
  },
  {
    Id = "30470",
    GroupId = 7,
    ChapterId = 3,
    Requirement_1 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "30480",
    GroupId = 7,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30490",
    GroupId = 7,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 2
    }
  },
  {
    Id = "30500",
    GroupId = 8,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "30510",
    GroupId = 8,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_2_1_3", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30520",
    GroupId = 8,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    }
  },
  {
    Id = "30530",
    GroupId = 8,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 2
    },
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "30540",
    GroupId = 8,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "it_4_2_4", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30550",
    GroupId = 8,
    ChapterId = 3,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "30560",
    GroupId = 8,
    ChapterId = 3,
    Requirement_1 = {Type = "ds_juice_8", Count = 1}
  },
  {
    Id = "30570",
    GroupId = 9,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_mixdrk_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    }
  },
  {
    Id = "30580",
    GroupId = 9,
    ChapterId = 3,
    Requirement_1 = {Type = "it_4_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "30590",
    GroupId = 9,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30600",
    GroupId = 9,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_1_1_4", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "30610",
    GroupId = 9,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "30620",
    GroupId = 9,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "30630",
    GroupId = 9,
    ChapterId = 3,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "it_2_3_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30640",
    GroupId = 10,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    }
  },
  {
    Id = "30650",
    GroupId = 10,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    }
  },
  {
    Id = "30660",
    GroupId = 10,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "30670",
    GroupId = 10,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30680",
    GroupId = 10,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_6",
      Count = 1
    }
  },
  {
    Id = "30690",
    GroupId = 10,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "30700",
    GroupId = 10,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30710",
    GroupId = 11,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "30720",
    GroupId = 11,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_9", Count = 1}
  },
  {
    Id = "30730",
    GroupId = 11,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "30740",
    GroupId = 11,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_1_1_2", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30750",
    GroupId = 11,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_3_6", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "30760",
    GroupId = 11,
    ChapterId = 3,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 2
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30770",
    GroupId = 11,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_2_7", Count = 1},
    Requirement_2 = {Type = "it_4_2_4", Count = 1}
  },
  {
    Id = "30780",
    GroupId = 12,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_3_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "30790",
    GroupId = 12,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30800",
    GroupId = 12,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "30810",
    GroupId = 12,
    ChapterId = 3,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "30820",
    GroupId = 12,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_10",
      Count = 1
    }
  },
  {
    Id = "30830",
    GroupId = 12,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_1_1_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30840",
    GroupId = 12,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_5",
      Count = 1
    }
  },
  {
    Id = "30850",
    GroupId = 13,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_mixdrk_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 2
    }
  },
  {
    Id = "30860",
    GroupId = 13,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "30870",
    GroupId = 13,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {Type = "it_1_1_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30880",
    GroupId = 13,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "30890",
    GroupId = 13,
    ChapterId = 3,
    Requirement_1 = {Type = "ds_dst_1", Count = 2},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "30900",
    GroupId = 13,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30910",
    GroupId = 13,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 2
    },
    Requirement_2 = {Type = "it_2_3_5", Count = 1}
  },
  {
    Id = "30920",
    GroupId = 14,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_6",
      Count = 2
    }
  },
  {
    Id = "30930",
    GroupId = 14,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "30940",
    GroupId = 14,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30950",
    GroupId = 14,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "30960",
    GroupId = 14,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "30970",
    GroupId = 14,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "30980",
    GroupId = 14,
    ChapterId = 3,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "it_2_3_1_4", Count = 1}
  },
  {
    Id = "30990",
    GroupId = 15,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    }
  },
  {
    Id = "31000",
    GroupId = 15,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_2_6", Count = 1},
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "31010",
    GroupId = 15,
    ChapterId = 3,
    Requirement_1 = {Type = "it_1_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "31020",
    GroupId = 15,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "31030",
    GroupId = 15,
    ChapterId = 3,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "31040",
    GroupId = 15,
    ChapterId = 3,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_4", Count = 2},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "31050",
    GroupId = 15,
    ChapterId = 3,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {Type = "it_2_3_4", Count = 1}
  }
}
