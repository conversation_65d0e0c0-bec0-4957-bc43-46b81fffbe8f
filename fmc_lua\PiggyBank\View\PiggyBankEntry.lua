PiggyBankEntry = setmetatable({}, HudCountDownButton)
PiggyBankEntry.__index = PiggyBankEntry

function PiggyBankEntry:Awake()
  HudCountDownButton.Awake(self)
  self.m_model = GM.ActivityManager:GetModel(ActivityType.PiggyBank)
  if self.m_model == nil then
    return
  end
  self:UpdateContent()
  EventDispatcher.AddListener(EEventType.PiggyBankStateChanged, self, self.UpdateContent)
  EventDispatcher.AddListener(EEventType.PiggyBankAccumulateGem, self, self.UpdateContent)
end

function PiggyBankEntry:UpdateContent()
  if self.m_model:GetState() == ActivityState.Started then
    self.m_finishTime = self.m_model:GetNextStateTime()
    self:UpdatePerSecond()
  else
    self.m_finishTime = nil
  end
  if self.m_model:IsActivityOpen() then
    local gemNum = self.m_model:GetAccumulatedNum()
    local full = gemNum >= self.m_model:GetFullLine()
    UIUtil.SetActive(self.m_fullTextGo, full)
    UIUtil.SetActive(self.m_numContentGo, not full)
    self.m_accumulateText.text = gemNum
    local curIconImage = ImageFileConfigName.icon_piggy_bank_1
    if gemNum == 0 then
      curIconImage = ImageFileConfigName.icon_piggy_bank_0
    elseif full then
      curIconImage = ImageFileConfigName.icon_piggy_bank_2
    end
    if self.m_curIconImage ~= curIconImage then
      self.m_curIconImage = curIconImage
      SpriteUtil.SetImage(self.m_iconImg, self.m_curIconImage, true)
    end
    UIUtil.SetActive(self.m_lockGo, not self.m_model:CanBuy())
  end
end

function PiggyBankEntry:OnClicked()
  if self.m_model:IsActivityOpen() then
    GM.UIManager:OpenView(UIPrefabConfigName.PiggyBankMainWindow, true)
  end
end
