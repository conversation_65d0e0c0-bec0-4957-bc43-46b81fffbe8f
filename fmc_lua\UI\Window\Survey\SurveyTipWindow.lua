SurveyTipWindow = setmetatable({}, BaseWindow)
SurveyTipWindow.__index = SurveyTipWindow

function SurveyTipWindow:Init(rewards, title, desc, isTip)
  self.m_subTitle.text = title
  self.m_desc.text = desc
  local rewards = rewards
  local space = 0
  if #rewards == 2 then
    space = -150
  end
  self.m_rewardLayoutGroup.spacing = space
  local rewardEle
  for i = 1, 3 do
    rewardEle = self["m_reward" .. i]
    if rewards[i] ~= nil then
      rewardEle.gameObject:SetActive(true)
      rewardEle:Init(rewards[i])
    else
      rewardEle.gameObject:SetActive(false)
    end
  end
  GM.BIManager:LogSurvey(1, nil, GM.SurveyModel:GetBIData())
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_contentTransf)
end

function SurveyTipWindow:OnStartClicked()
  self:Close()
  GM.UIManager:OpenView(UIPrefabConfigName.SurveyWindow)
  GM.BIManager:Log<PERSON>urvey(205, nil, GM.SurveyModel:GetBIData())
end
