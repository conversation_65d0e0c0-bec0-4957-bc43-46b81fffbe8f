PurchaseFailWindow = setmetatable({}, BaseWindow)
PurchaseFailWindow.__index = PurchaseFailWindow

function PurchaseFailWindow:Init(msgKey)
  if StringUtil.IsNilOrEmpty(msgKey) then
    return
  end
  self.m_normalContentGo:SetActive(false)
  self.m_msgText.gameObject:SetActive(true)
  self.m_msgText.text = GM.GameTextModel:GetText(msgKey)
end

function PurchaseFailWindow:Ok()
  self:Close()
end

function PurchaseFailWindow:Restore()
  self:Close()
  GM.InAppPurchaseModel:RestorePurchase(true)
end
