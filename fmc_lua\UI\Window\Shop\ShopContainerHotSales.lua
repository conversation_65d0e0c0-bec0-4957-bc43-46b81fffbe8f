ShopContainerHotSales = setmetatable({}, BaseShopListContainer)
ShopContainerHotSales.__index = ShopContainerHotSales

function ShopContainerHotSales:Awake()
  BaseShopListContainer.Init(self, EShopType.FlashSale, true, 3)
end

function ShopContainerHotSales:_GetCellData()
  local data = {}
  local ids = GM.ShopModel:GetItemIdsFlashSale() or {}
  for _, id in ipairs(ids) do
    local itemData = GM.ShopModel:GetItemData(id)
    table.insert(data, {
      Id = id,
      Code = itemData.itemCode,
      Count = itemData.leftCount,
      Price = itemData.costCount
    })
  end
  return data
end

function ShopContainerHotSales:_OnCellClicked(cell)
  local success, type, count = GM.ShopModel:BuyItem(self.m_shopType, cell:GetData().Id, false)
  if success then
    self:_UpdateContent()
  else
    GM.ShopModel:OnLackOfGem(count)
  end
end

function ShopContainerHotSales:_GetRefreshTime()
  return GM.ShopModel:GetRefreshTimeFlashSale()
end

function ShopContainerHotSales:_DoRefresh()
  return GM.ShopModel:RefreshFlashSaleByGemCost()
end
