TestFileReplaceCell = {}
TestFileReplaceCell.__index = TestFileReplaceCell

function TestFileReplaceCell:Init(window, configName, arrOptions, selectedOption)
  self.m_window = window
  self.m_configNameText.text = configName
  self.configName = configName
  self.arrOptions = arrOptions
  if string.find(configName, "_") then
    self.m_configNameText.color = CSColor.blue
  end
  for i = 1, #arrOptions do
    local switchGo = GameObject.Instantiate(self.m_switchGo, self.gameObject.transform)
    local switchTb = switchGo:GetLuaTable()
    self["m_switch" .. i] = switchTb
    switchGo:SetActive(false)
  end
  local functionOnToggle = function(isOn, index)
    if isOn then
      self:OnSelectIndex(index)
    else
      self:OnDeselectIndex(index)
    end
  end
  for i = 1, #arrOptions do
    self["m_switch" .. i].gameObject:SetActive(true)
    self["m_switch" .. i]:Init(functionOnToggle, i)
    self["m_switch" .. i]:SetText(arrOptions[i])
    self["m_switch" .. i]:SetOn(arrOptions[i] == selectedOption, false)
    if arrOptions[i] == selectedOption then
      self.m_selectedIndex = i
    end
  end
end

function TestFileReplaceCell:Reset()
  self.m_selectedIndex = nil
  for i = 1, #self.arrOptions do
    self["m_switch" .. i]:SetOn(false, false)
  end
  self:_RebuildLayout()
end

function TestFileReplaceCell:OnSelectOption(option)
  local index = Table.GetIndex(self.arrOptions, option)
  if 0 < index then
    self["m_switch" .. index]:SetOn(true, false)
    self:_DoSelectIndex(index)
  end
end

function TestFileReplaceCell:_DoSelectIndex(index)
  self.m_selectedIndex = index
  for i = 1, #self.arrOptions do
    if self["m_switch" .. i]:IsOn() and i ~= index then
      self["m_switch" .. i]:SetOn(false, false)
    end
  end
  self:_RebuildLayout()
end

function TestFileReplaceCell:OnSelectIndex(index)
  self:_DoSelectIndex(index)
  self.m_window:OnCellSelectOne(self, self.arrOptions[self.m_selectedIndex])
end

function TestFileReplaceCell:OnDeselectIndex(index)
  if self.m_selectedIndex == index then
    self.m_selectedIndex = nil
  end
  self:_RebuildLayout()
end

function TestFileReplaceCell:_RebuildLayout()
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.gameObject.transform.parent)
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.gameObject.transform)
end

function TestFileReplaceCell:GetSelectedOption()
  if self.m_selectedIndex then
    return self.arrOptions[self.m_selectedIndex]
  end
  return nil
end
