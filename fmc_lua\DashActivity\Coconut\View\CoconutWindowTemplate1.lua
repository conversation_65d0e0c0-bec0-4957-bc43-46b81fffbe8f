CoconutWindowTemplate1 = setmetatable({}, CoconutBaseWindow)
CoconutWindowTemplate1.__index = CoconutWindowTemplate1

function CoconutWindowTemplate1:Init()
  CoconutBaseWindow.Init(self, true)
  local displayConfig = {
    skin = 3,
    rewards = {
      {
        [PROPERTY_TYPE] = "4001",
        [PROPERTY_COUNT] = 1
      },
      {
        [PROPERTY_TYPE] = "2901",
        [PROPERTY_COUNT] = 1
      },
      {
        [PROPERTY_TYPE] = "2504",
        [PROPERTY_COUNT] = 1
      }
    }
  }
  self.m_trunkCell:Init(displayConfig)
end

CoconutNoticeWindow = setmetatable({}, CoconutWindowTemplate1)
CoconutNoticeWindow.__index = CoconutNoticeWindow

function CoconutNoticeWindow:Init()
  CoconutWindowTemplate1.Init(self)
  self:UpdatePerSecond()
end

function CoconutNoticeWindow:UpdatePerSecond()
  if self.m_model ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function CoconutNoticeWindow:OnCloseFinish()
  CoconutWindowTemplate1.OnCloseFinish(self)
  GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.CashDash)
end

CoconutReadyWindow = setmetatable({}, CoconutWindowTemplate1)
CoconutReadyWindow.__index = CoconutReadyWindow
