TestOrderGroupInfoCell = {}
TestOrderGroupInfoCell.__index = TestOrderGroupInfoCell

function TestOrderGroupInfoCell:Init(tbWindow, orderGroupData)
  self.tbWindow = tbWindow
  self.orderGroupData = orderGroupData
  self.m_nameText.text = "NAME:" .. (orderGroupData.name or "")
  self.m_cells = {}
  for i = 1, 7 do
    local cell = Object.Instantiate(self.m_prefab, self.m_content)
    local cellTb = cell:GetLuaTable()
    cellTb:Init(self.orderGroupData.arrOrderData[i])
    self.m_cells[#self.m_cells + 1] = cellTb
  end
  self.m_switch:Init(function(...)
    self:OnClickToggle(...)
  end)
  self.m_switch:SetOn(false)
end

function TestOrderGroupInfoCell:OnClickToggle(isOn)
  self.selected = isOn
  if isOn then
    local isValid, tipStr = self:IsDataValid()
    if not isValid then
      self.m_switch:SetOn(false, false)
      self.selected = false
      GM.UIManager:ShowTestPrompt(tipStr or "此订单组配置不完整！")
    end
  end
end

function TestOrderGroupInfoCell:IsDataValid()
  local atLeastOneValid = false
  for _, cell in ipairs(self.m_cells) do
    if not cell:IsEmpty() then
      local isValid, tipStr = cell:IsDataValid()
      if not isValid then
        return false, tipStr
      else
        atLeastOneValid = true
      end
    end
  end
  return atLeastOneValid
end

function TestOrderGroupInfoCell:OnClickEdit()
  GM.UIManager:OpenView(UIPrefabConfigName.TestOrderGroupEditWindow, self.orderGroupData)
end

function TestOrderGroupInfoCell:OnClickDelete()
  GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "Are You Sure?", "此删除操作不可回退哦！", "删除", "取消", function(tbWindow)
    Table.ListRemove(self.tbWindow.arrOrderGroupData, self.orderGroupData)
    self.tbWindow:UpdateContent()
    GM.TestModel:SaveOrderGroups()
    tbWindow:Close()
  end)
end

function TestOrderGroupInfoCell:OnClickToTop()
  Table.ListRemove(self.tbWindow.arrOrderGroupData, self.orderGroupData)
  table.insert(self.tbWindow.arrOrderGroupData, 1, self.orderGroupData)
  self.tbWindow:UpdateContent()
  GM.TestModel:SaveOrderGroups()
end
