BlindChestModel = setmetatable({}, BaseActivityModel)
BlindChestModel.__index = BlindChestModel
BlindChestModel.GroupKey = "Group"
BlindChestModel.KeyCountKey = "KeyCount"
BlindChestModel.RoundKey = "Round"
BlindChestModel.TurnKey = "Turn"
BlindChestModel.SlotOpenedKey = "SlotOpened%d"
BlindChestModel.RewardTakenKey = "RewardTaken%d"
BlindChestModel.ScrolledToBoardEntryBubble = "ScrolledToBoardEntryBubble"
BlindChestModel.BundleWindowShownKey = "BundleWindowShown"
BlindChestModel.TurnCountKey = "TurnCount"
BlindChestModel.HasAddToken = "hasAddToken"
BlindChestModel.EmptyRewardIndex = -1
BlindChestModel.TopRewardIndex = 0

function BlindChestModel:Init(activityType, virtualDBTable)
  self.m_activityDefinition = BlindChestDefinition[activityType]
  self.m_tokenHelper = ActivityTokenHelper.Create(self, virtualDBTable, EFlyElementLabelStyle.Default)
  BaseActivityModel.Init(self, activityType, virtualDBTable)
end

function BlindChestModel:Destroy()
  EventDispatcher.RemoveTarget(self)
  self.m_tokenHelper:Destroy()
end

function BlindChestModel:_DropData()
  BaseActivityModel._DropData(self)
  self.m_bTriggerLackToken = false
end

function BlindChestModel:_LoadOtherServerConfig(config)
  self.m_tokenHelper:LoadConfig(config)
  local maxRound = 0
  local roundConfig = {}
  self.m_config.FinalRewards = {}
  for _, item in ipairs(config.blindChest_rewards) do
    if not roundConfig[item.round] then
      roundConfig[item.round] = {}
    end
    if item.turn == 0 then
      self.m_config.FinalRewards[item.round] = item.bigprize
    else
      local data = {
        SlotNumber = item.boxnum,
        TopRewards = item.bigprize,
        OtherRewards = item.otherprizes or {}
      }
      roundConfig[item.round][item.turn] = data
    end
    maxRound = math.max(maxRound, item.round)
  end
  self.m_config.Round = roundConfig
  local drawConfig = {}
  for _, item in ipairs(config.blindChest_probability) do
    if drawConfig[item.round] == nil then
      drawConfig[item.round] = {}
    end
    if drawConfig[item.round][item.group] == nil then
      drawConfig[item.round][item.group] = {}
    end
    local chanceMap = {}
    for i = 1, 12 do
      chanceMap[i] = item["getin" .. i]
    end
    drawConfig[item.round][item.group][item.turn] = chanceMap
  end
  self.m_config.drawChanceConfig = drawConfig
  self.m_round = maxRound
  if GameConfig.IsTestMode() then
    if Table.IsEmpty(self.m_config.Round) then
      Log.Error("[BlindChest] 缺少blindChest_rewards配置")
    end
    if Table.IsEmpty(self.m_config.drawChanceConfig) then
      Log.Error("[BlindChest] 缺少blindChest_probability配置")
    end
    local maxTurn, boxNum, chanceMap
    for i = 1, self.m_round do
      if self.m_config.Round[i] ~= nil then
        maxTurn = #self.m_config.Round[i]
        for j = 1, maxTurn do
          boxNum = self.m_config.Round[i][j].SlotNumber
          for group, drawCfg in ipairs(self.m_config.drawChanceConfig[i]) do
            chanceMap = drawCfg[j]
            if chanceMap ~= nil then
              local bValid = false
              for index, chance in ipairs(chanceMap) do
                if index <= boxNum and chance == 100 then
                  bValid = true
                  break
                end
              end
              if not bValid then
                Log.Error(string.format("[BlindChest] probability表round: %d; group: %d; turn: %d; 的概率配置不符合boxNum的要求, 请检查!", i, group, j))
              end
            else
              Log.Error("[BlindChest] blindChest_probability配置和rewards未对应, 请检查！")
            end
          end
        end
      else
        Log.Error("[BlindChest] 缺少第" .. i .. "轮的blindChest_rewards配置")
      end
    end
  end
end

function BlindChestModel:_CalculateState()
  if self.m_config == nil then
    return ActivityState.Released, nil
  end
  local serverTime = GM.GameModel:GetServerTime()
  if serverTime < self.m_config.sTime then
    return ActivityState.Preparing, self.m_config.sTime
  elseif serverTime < self.m_config.eTime and not self:HasFinishedAllRound() then
    return ActivityState.Started, self.m_config.eTime
  elseif self.m_config.rTime and serverTime < self.m_config.rTime then
    return ActivityState.Ended, self.m_config.rTime
  else
    return ActivityState.Released, nil
  end
end

function BlindChestModel:_OnStateChanged()
  if self:GetState() == ActivityState.Started and self.m_dbTable:GetValue(BlindChestModel.KeyCountKey, "value") == nil then
    self.m_dbTable:Set(BlindChestModel.KeyCountKey, "value", 1)
    GM.BIManager:LogAcquire(self.m_activityDefinition.ActivityTokenPropertyType, 1, self.m_activityDefinition.TutorialGetKeyBIType, true)
    self.m_dbTable:Set(BlindChestModel.TurnCountKey, "value", 0)
    self:LogActivity(EBIType.ActivityRankUp, 0)
  end
  if self:GetState() ~= ActivityState.Released and self.m_dbTable:GetValue(BlindChestModel.GroupKey, "value") == nil then
    self:StartNewRound()
  end
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
end

function BlindChestModel:GetResourceLabels()
  return self.m_activityDefinition.ResourceLabels
end

function BlindChestModel:GetBoardEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = self.m_activityDefinition.EntryRootKey,
    entryPrefabName = self.m_activityDefinition.BoardEntryPrefabName,
    checkFun = function()
      return self:IsActivityOpen()
    end
  }
end

function BlindChestModel:GetMapEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = self.m_activityDefinition.EntryRootKey,
    entryPrefabName = self.m_activityDefinition.EntryPrefabName,
    hudKey = self.m_activityDefinition.EntryButtonKey,
    checkFun = function()
      return self:IsActivityOpen()
    end
  }
end

function BlindChestModel:GetCurRoundTurnNum()
  local round = self:GetRound()
  local config = self.m_config.Round[round] or self.m_config.Round[0]
  return config and #config or 0
end

function BlindChestModel:GetTurnConfig(round, turn)
  round = round or self:GetRound()
  turn = turn or self:GetTurn()
  local config = self.m_config.Round[round] or self.m_config.Round[0]
  return config[turn]
end

function BlindChestModel:GetFinalRewards(round)
  return self.m_config.FinalRewards[round] or self.m_config.FinalRewards[0]
end

function BlindChestModel:GetKeyCount()
  return self.m_dbTable:GetValue(BlindChestModel.KeyCountKey, "value") or 0
end

function BlindChestModel:GetRound()
  return self.m_dbTable:GetValue(BlindChestModel.RoundKey, "value") or 0
end

function BlindChestModel:GetTurn()
  return self.m_dbTable:GetValue(BlindChestModel.TurnKey, "value") or 1
end

function BlindChestModel:IsSlotOpened(slot)
  local key = string.format(BlindChestModel.SlotOpenedKey, slot)
  return self.m_dbTable:GetValue(key, "value") or false
end

function BlindChestModel:HasScrolledToBoardBubble()
  return self.m_dbTable:GetValue(BlindChestModel.ScrolledToBoardEntryBubble, "value") or false
end

function BlindChestModel:SetScrolledToBoardBubble()
  self.m_dbTable:Set(BlindChestModel.ScrolledToBoardEntryBubble, "value", true)
end

function BlindChestModel:HasBundleWindowShown(groupId)
  return self.m_dbTable:GetValue(BlindChestModel.BundleWindowShownKey .. (groupId or ""), "value") or false
end

function BlindChestModel:SetBundleWindowShown(groupId)
  self.m_dbTable:Set(BlindChestModel.BundleWindowShownKey .. (groupId or ""), "value", true)
end

function BlindChestModel:TryOpenView()
  if not self:HasWindowOpenedOnce(ActivityState.Started) then
    GM.UIManager:OpenView(self.m_activityDefinition.ReadyWindowPrefabName, self, self:GetState())
  else
    self:TryStartNewRound()
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self, self:GetState())
  end
end

function BlindChestModel:TryStartNewRound()
  if self.m_dbTable:GetValue(BlindChestModel.GroupKey, "value") == nil then
    self:StartNewRound()
  end
  if self:GetTurn() > self:GetCurRoundTurnNum() then
    self:StartNewRound()
  end
end

function BlindChestModel:StartNewRound()
  local round = self:GetRound()
  self.m_dbTable:Set(BlindChestModel.RoundKey, "value", round + 1)
  self.m_dbTable:Set(BlindChestModel.TurnKey, "value", 1)
  local groupNum = #self.m_config.drawChanceConfig[round + 1]
  local newGroup = math.random(1, groupNum)
  local group = self.m_dbTable:GetValue(BlindChestModel.GroupKey, "value")
  if newGroup == group then
    newGroup = (newGroup + 1) % groupNum
  end
  if newGroup == 0 then
    newGroup = groupNum
  end
  self.m_dbTable:Set(BlindChestModel.GroupKey, "value", newGroup)
  Log.Info("[BlindChest] round: " .. round + 1 .. "; group: " .. newGroup)
  self:_ClearCurTurnData(self:GetTurnConfig(round + 1, 1))
  EventDispatcher.DispatchEvent(self.m_activityDefinition.TurnChangedEvent)
end

function BlindChestModel:Open(slot)
  local keyCount = self:GetKeyCount()
  self.m_dbTable:Set(BlindChestModel.KeyCountKey, "value", keyCount - 1)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.KeyChangedEvent, -1)
  GM.BIManager:LogUseItem(self.m_activityDefinition.ActivityTokenPropertyType, 1, self:GetType(), self:GetType())
  local round = self:GetRound()
  local turn = self:GetTurn()
  local drawConfig = self:_GetDrawConfig(round, turn)
  local turnData = self:GetTurnConfig(round, turn)
  local openedCount = 0
  for index = 1, turnData.SlotNumber do
    if self:IsSlotOpened(index) then
      openedCount = openedCount + 1
    end
  end
  local result
  local chance = drawConfig[openedCount + 1]
  if chance >= math.random(100) then
    result = BlindChestModel.TopRewardIndex
    local rewards = Table.ShallowCopy(turnData.TopRewards)
    RewardApi.CryptRewards(rewards)
    RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, self.m_activityDefinition.GetTopRewardBIType, nil, CacheItemType.Stack)
    local nextTurn = turn + 1
    self.m_dbTable:Set(BlindChestModel.TurnKey, "value", nextTurn)
    local nextTurnData = self:GetTurnConfig(round, nextTurn)
    if nextTurnData == nil then
      local rewards = Table.ShallowCopy(self:GetFinalRewards(round))
      RewardApi.CryptRewards(rewards)
      RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, self.m_activityDefinition.GetFinalRewardBIType, nil, CacheItemType.Stack)
      if self:HasFinishedAllRound() then
        EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
      end
    else
      self:_ClearCurTurnData(nextTurnData)
    end
    EventDispatcher.DispatchEvent(self.m_activityDefinition.TurnChangedEvent)
    local biTurn = self.m_dbTable:GetValue(BlindChestModel.TurnCountKey, "value") + 1
    self:LogActivity(EBIType.ActivityRankUp, biTurn)
    self.m_dbTable:Set(BlindChestModel.TurnCountKey, "value", biTurn)
  else
    local rewardIndexes = {}
    for index = 1, #turnData.OtherRewards do
      local key = string.format(BlindChestModel.RewardTakenKey, index)
      local isTaken = self.m_dbTable:GetValue(key, "value") or false
      if not isTaken then
        table.insert(rewardIndexes, index)
      end
    end
    for i = #rewardIndexes + 1, turnData.SlotNumber - openedCount - 1 do
      table.insert(rewardIndexes, BlindChestModel.EmptyRewardIndex)
    end
    result = Table.ListRandomSelectOne(rewardIndexes)
    if result ~= BlindChestModel.EmptyRewardIndex then
      local rewards = {
        Table.ShallowCopy(turnData.OtherRewards[result])
      }
      RewardApi.CryptRewards(rewards)
      RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, self.m_activityDefinition.GetRewardBIType, nil, CacheItemType.Stack)
      local key = string.format(BlindChestModel.RewardTakenKey, result)
      self.m_dbTable:Set(key, "value", true)
    end
    local key = string.format(BlindChestModel.SlotOpenedKey, slot)
    self.m_dbTable:Set(key, "value", true)
  end
  if self:GetKeyCount() == 0 and self.m_dbTable:GetValue(BlindChestModel.HasAddToken, "value") == 1 then
    self.m_bTriggerLackToken = true
  end
  return result
end

function BlindChestModel:TryTriggerLackChestToken()
  if self.m_bTriggerLackToken then
    self.m_bTriggerLackToken = false
    GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.LackChestToken, nil, nil, nil, true)
  end
end

function BlindChestModel:OnLackChestToken()
  if self.m_dbTable:GetValue(BlindChestModel.HasAddToken, "value") == 1 then
    self.m_bTriggerLackToken = false
    return GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.LackChestToken, nil, nil, nil, true)
  end
end

function BlindChestModel:IsActivityOpen()
  return self:GetState() == ActivityState.Started and not self:HasFinishedAllRound()
end

function BlindChestModel:HasFinishedAllRound()
  return self.m_round ~= nil and (self:GetRound() > self.m_round or self:GetRound() == self.m_round and self:GetTurn() > self:GetCurRoundTurnNum())
end

function BlindChestModel:_GetDrawConfig(round, turn)
  local group = self.m_dbTable:GetValue(BlindChestModel.GroupKey, "value")
  local turnDrawMap = self.m_config.drawChanceConfig[round][group]
  return turnDrawMap[turn]
end

function BlindChestModel:_ClearCurTurnData(turnData)
  for index = 1, turnData.SlotNumber do
    local key = string.format(BlindChestModel.SlotOpenedKey, index)
    self.m_dbTable:Set(key, "value", false)
  end
  for index = 1, #turnData.OtherRewards do
    local key = string.format(BlindChestModel.RewardTakenKey, index)
    self.m_dbTable:Set(key, "value", false)
  end
end

function BlindChestModel.GetActivityTypeByKeyType(type)
  for activityType, definition in pairs(BlindChestDefinition) do
    if (type == EPropertyType.BlindChestKey or definition.ActivityTokenPropertyType == type) and GM.ActivityManager:GetModel(activityType):GetState() ~= ActivityState.Released then
      return activityType
    end
  end
end

function BlindChestModel:CanAddScore()
  return self:IsActivityOpen()
end

function BlindChestModel:CheckActivityTokenValid()
  return self:GetState() ~= ActivityState.Released and not self:HasFinishedAllRound() and (not self:HasWindowOpenedOnce(ActivityState.Ended) or GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName))
end

function BlindChestModel:AcquireActivityToken(num)
  self.m_dbTable:Set(BlindChestModel.KeyCountKey, "value", num + self:GetKeyCount())
  if self.m_dbTable:GetValue(BlindChestModel.HasAddToken, "value") ~= 1 then
    self.m_dbTable:Set(BlindChestModel.HasAddToken, "value", 1)
  end
  EventDispatcher.DispatchEvent(self.m_activityDefinition.KeyChangedEvent, num)
  EventDispatcher.DispatchEvent(EEventType.AcquireActivityToken, {
    num = num,
    activityType = self:GetType()
  })
end

function BlindChestModel:GetActivityTokenNumber()
  return self:GetKeyCount()
end

function BlindChestModel:GetAllStateChangedEvent()
  return {
    self.m_activityDefinition.StateChangedEvent
  }
end

function BlindChestModel.GetButtonTarget(propertyType)
  local mainWindow
  for activityType, activityDefinition in pairs(BlindChestDefinition) do
    if propertyType == activityDefinition.ActivityTokenPropertyType then
      mainWindow = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
      if mainWindow ~= nil then
        return mainWindow:GetHudButton()
      elseif GM.SceneManager:GetGameMode() == EGameMode.Board then
        local boardView = MainBoardView.GetInstance()
        if boardView ~= nil then
          local orderArea = boardView:GetOrderArea()
          if orderArea ~= nil then
            return orderArea:GetIconAreaByActivityType(activityType)
          end
        end
      elseif GM.SceneManager:GetGameMode() == EGameMode.Main then
        return TutorialHelper.GetHudButton(activityDefinition.EntryButtonKey)
      end
    end
  end
  return nil
end
