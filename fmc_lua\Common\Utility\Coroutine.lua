Coroutine = {}
local coroutineCreate = coroutine.create

function coroutine.create(func)
  return coroutineCreate(function(...)
    SafeCall(func, nil, ...)
  end)
end

function Coroutine.Wait(duration)
  while 0 < duration do
    duration = duration - CS.UnityEngine.Time.deltaTime
    coroutine.yield()
  end
end

function Coroutine.WaitForTween(tween)
  while tween:IsActive() do
    coroutine.yield()
  end
end
