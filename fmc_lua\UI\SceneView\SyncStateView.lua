SyncStateView = {}
SyncStateView.__index = SyncStateView

function SyncStateView:Awake()
  self:_UpdateView()
  self:_OnChangeGameMode()
  EventDispatcher.AddListener(EEventType.SyncStateChanged, self, self._UpdateView)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnChangeGameMode)
end

function SyncStateView:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function SyncStateView:_UpdateView()
  local active = GM.SyncModel.eSyncState == ESyncState.VersionError
  if active ~= self.m_imgGo.activeSelf then
    self.m_imgGo:SetActive(active)
    if active then
      GM.BIManager:LogProject(EBIProjectType.BigDataErrorMonitor, EBIProjectType.BigDataErrorMonitorAction.VersionError)
    end
  end
end

function SyncStateView:_OnChangeGameMode()
  local isMap = GM.SceneManager:GetGameMode() == EGameMode.Main
  UIUtil.SetAnchoredPosition(self.m_imgGo.transform, nil, isMap and -31 or 122)
end

function SyncStateView:OnClick()
  GM.UIManager:OpenView(UIPrefabConfigName.AccountNoticeWindow, {
    type = EAccountNotice.LowVersion
  })
end
