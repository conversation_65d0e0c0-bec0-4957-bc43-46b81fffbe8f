DiscoveriesButton = setmetatable({}, HudGeneralButton)
DiscoveriesButton.__index = DiscoveriesButton

function DiscoveriesButton:Awake()
  HudGeneralButton.Awake(self)
  EventDispatcher.AddListener(EEventType.FunctionOpen, self, self._CheckOpen)
  EventDispatcher.AddListener(EEventType.DiscoveriesUpdate, self, self._UpdateRedPoint)
  EventDispatcher.AddListener(EEventType.ChapterChangeFinished, self, self.OnGameModeChanged)
  self:_CheckOpen()
  self:_UpdateRedPoint()
end

function DiscoveriesButton:_NeedDisplay()
  if not HudGeneralButton._NeedDisplay(self) then
    return false
  end
  local curActiveChapterName = GM.ChapterManager.curActiveChapterName
  return curActiveChapterName and curActiveChapterName == GM.TaskManager:GetOngoingChapterName()
end

function DiscoveriesButton:_OnDisplay()
  HudGeneralButton._OnDisplay(self)
  self:_UpdateRedPoint()
end

function DiscoveriesButton:_CheckOpen()
  local open = GM.OpenFunctionModel:IsFunctionOpen(EFunction.Discoveries)
  if self.gameObject.activeSelf ~= open then
    self.gameObject:SetActive(open)
    self:_UpdateRedPoint()
  end
end

function DiscoveriesButton:_UpdateRedPoint()
  if not self.m_bDisplay or not self.gameObject.activeSelf then
    return
  end
  local count = GM.ItemDataModel:GetDiscoveriesCanRewardCount()
  self.m_tipGo:SetActive(0 < count)
  self.m_numText.text = count
end

function DiscoveriesButton:OnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.DiscoveriesWindow)
end
