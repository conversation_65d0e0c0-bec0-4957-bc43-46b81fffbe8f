BreakEggBaseArea = {}
BreakEggBaseArea.__index = BreakEggBaseArea

function BreakEggBaseArea:Init()
  self.m_activityModel = GM.ActivityManager:GetModel(ActivityType.BreakEgg)
  self.m_canvasGroup = self.transform:GetComponent(typeof(CS.UnityEngine.CanvasGroup))
end

function BreakEggBaseArea:UpdateContent()
  Log.Assert(false, "UpdateContent()为抽象接口")
end

function BreakEggBaseArea:FadeIn()
  self.gameObject:SetActive(true)
  self.m_canvasGroup.alpha = 0
  self.m_canvasGroup:DOFade(1, 0.5)
end

function BreakEggBaseArea:FadeOut()
  self.m_canvasGroup.alpha = 1
  local sequence = DOTween.Sequence()
  sequence:Append(self.m_canvasGroup:DOFade(0, 0.5))
  sequence:AppendCallback(function()
    self.gameObject:SetActive(false)
  end)
end
