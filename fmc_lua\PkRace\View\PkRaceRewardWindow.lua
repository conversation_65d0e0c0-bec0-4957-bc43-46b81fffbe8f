PkRaceRewardWindow = setmetatable({showWindowMask = false}, RewardWindow)
PkRaceRewardWindow.__index = PkRaceRewardWindow

function PkRaceRewardWindow:Init(rank, arrRewards, closeCallback, rewardStartPos)
  RewardWindow.Init(self, arrRewards, nil, true, nil, closeCallback)
  local textKey = rank == 1 and "pkrace_first_reward_desc" or "pkrace_second_reward_desc"
  self.m_descText.text = GM.GameTextModel:GetText(textKey)
  self:PlayRewardEnterAnim(rewardStartPos)
  GM.AudioModel:PlayEffectLoop(AudioFileConfigName.SfxPkRaceActivityFinish1, self, 1.84)
end

function PkRaceRewardWindow:OnDestroy()
  RewardWindow.OnDestroy(self)
  if self.m_seq ~= nil then
    self.m_seq:Kill()
    self.m_seq = nil
  end
  if self.m_bEventLocked then
    GM.UIManager:SetEventLock(false)
    self.m_bEventLocked = false
  end
  GM.AudioModel:StopEffectLoop(self)
end

function PkRaceRewardWindow:PlayRewardEnterAnim(startPos)
  local rewardItem = self.m_rewardContent:GetRewardItem(1)
  if rewardItem == nil then
    return
  end
  self.m_canvasGroup.alpha = 0
  local iconImg = rewardItem:GetIcon()
  local flyGo = GameObject.Instantiate(iconImg.gameObject, self.transform)
  flyGo.transform.position = startPos
  UIUtil.SetActive(flyGo, false)
  local canvasGroup = rewardItem.gameObject:GetComponent(typeof(CS.UnityEngine.CanvasGroup))
  canvasGroup.alpha = 0
  UIUtil.SetActive(rewardItem.gameObject, false)
  self.m_bEventLocked = true
  GM.UIManager:SetEventLock(true)
  local flyTime = 0.4
  local seq = DOTween.Sequence()
  local rect = flyGo.transform
  seq:AppendInterval(0)
  seq:AppendCallback(function()
    UIUtil.SetActive(flyGo, true)
    self.m_boomEffectGo.transform.position = startPos
    UIUtil.SetActive(self.m_boomEffectGo, true)
  end)
  local originScale = rect.localScale
  local parentScale = 1.32
  local targetScale = Vector3(originScale.x * parentScale, originScale.y * parentScale, 1)
  seq:Append(rect:DOJump(iconImg.transform.position, 1, 1, flyTime):SetEase(Ease.InCubic))
  seq:Join(rect:DOScale(targetScale, flyTime):SetEase(Ease.OutCubic))
  seq:Join(self.m_canvasGroup:DOFade(1, flyTime):SetEase(Ease.InCubic))
  seq:AppendCallback(function()
    UIUtil.SetActive(self.m_yanhuaGo, true)
    UIUtil.SetActive(rewardItem.gameObject, true)
  end)
  seq:Append(rect:DOScale(Vector3(targetScale.x + 0.2, targetScale.y + 0.2, 1), 0.1))
  seq:Append(rect:DOScale(Vector3(targetScale.x, targetScale.y, 1), 0.2))
  seq:Append(canvasGroup:DOFade(1, 0.3))
  seq:AppendCallback(function()
    self.m_seq = nil
    GM.UIManager:SetEventLock(false)
    UIUtil.SetActive(flyGo, false)
    self.m_bEventLocked = false
  end)
  self.m_seq = seq
end

PkRaceRoundRewardWindow = setmetatable({}, RewardWindow)
PkRaceRoundRewardWindow.__index = PkRaceRoundRewardWindow

function PkRaceRoundRewardWindow:Init(round, arrRewards, closeCallback)
  RewardWindow.Init(self, arrRewards, nil, true, nil, closeCallback)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPkRaceActivityFinish2)
  self.m_descText.text = GM.GameTextModel:GetText("pkrace_round_reward_title", round)
end
