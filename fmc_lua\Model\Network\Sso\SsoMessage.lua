SsoMessage = {DEFAULT_TIME_OUT = 16000, DEFAULT_RETRY = 2}
SsoMessage.__index = SsoMessage
local Serialization = require("Model.Network.Sso.SsoInterface")
ESsoActionType = {
  Lookup = 0,
  Switch = 3,
  Select = 4
}
ESsoSocialType = {
  Device = 0,
  Facebook = 1,
  Wechat = 2,
  QQ = 3,
  TMGP_Wechat = 4,
  TMGP_QQ = 5,
  Vivo = 6,
  Oppo = 7,
  Phone_Number = 8,
  Sim = 9,
  <PERSON>hoo = 10,
  <PERSON><PERSON><PERSON> = 11,
  <PERSON><PERSON> = 12,
  <PERSON> = 13,
  Samsung = 14,
  Google = 18
}
ESsoActionStatus = {
  NewRegister = 0,
  OldUser = 1,
  ServerTimeout = 2,
  SocialConflict = 3,
  MultipleUserId = 4,
  UnbindNotAllowed = 5,
  CannotComplete = 6,
  VerificationFailed = 7,
  NoAvailableBindingInfo = 8,
  ActionTypeNotAllowed = 9,
  InvalidGameToken = 10
}
ESsoDeleteUserActionType = {DeleteDirectly = 4}
ESsoDeleteUserStatus = {DeleteSuccess = 0, HasBeenDeleted = 1}

function SsoMessage.GenerateRequest(eActionType, eSocialType, socialID, accessToken, socialName, email, pictureUrl, refreshToken)
  Log.Assert(eActionType and eSocialType, "SsoMessage.GenerateRequest")
  local idfaValue = DeviceInfo.GetAdvertisingIdentifier()
  GM.SsoManager:SaveRegisterIdfa(idfaValue)
  Log.Info("SSO idfa is " .. idfaValue)
  return {
    game_token = GameConfig.GetGameToken(),
    deviceId = DeviceInfo.GetDeviceID(),
    vendorId = DeviceInfo.GetVendorID(),
    openUDID = DeviceInfo.GetOpenUDID(),
    installationId = GM.UserModel:GetInstallUuid(),
    idfa = idfaValue,
    adid = "",
    imei = DeviceInfo.GetIMEI(),
    oaid = DeviceInfo.GetOAID(),
    imsi = "",
    imei_meid = "",
    wifiMAC = DeviceInfo.GetMacAddress(),
    bluetoothMAC = "",
    currentUserId = GM.UserModel:GetUserId(),
    action_type = eActionType,
    firstOpenTime = PlayerPrefs.GetInt(EPlayerPrefKey.FirstOpenTime, 0) * 1000,
    socialId = socialID or "",
    socialType = eSocialType,
    social_name = socialName or "",
    social_icon = pictureUrl or "",
    social_appid = "",
    social_openid = "",
    social_access_token = accessToken or "",
    social_refresh_token = refreshToken or "",
    social_email = email or "",
    phone_verify_code = "",
    token_access = "",
    adjust_id = "::" .. CSAppsFlyerManager:GetAppsFlyerId(),
    ctime = TimeUtil.GetTimeInMS(),
    extappid = ""
  }
end

function SsoMessage.SendByHttp(tbSsoRequest, ssoToken, callback, bShowMask, bIsRegister, opName, isLoading)
  if bShowMask then
    GM.UIManager:ShowMask()
  end
  local strOpName = "SOAccountBind"
  if opName then
    strOpName = opName
  end
  local strUrl = NetworkConfig.GetHttpServerUrl(strOpName)
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(strUrl, "POST", SsoMessage.DEFAULT_TIME_OUT, SsoMessage.DEFAULT_RETRY)
  local session = GM.SsoManager:GetSession()
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader())
  reqCtx:SetRetryHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader(2))
  if not StringUtil.IsNilOrEmpty(ssoToken) then
    reqCtx:SetHeader(NetworkConfig.TokenHeaderKey, ssoToken)
  end
  if isLoading or bShowMask then
    reqCtx:SetLogAll()
  else
    reqCtx:SetLogFail()
  end
  if not SsoMessage.Serialize(reqCtx, strOpName, tbSsoRequest) then
    Log.Warning("SsoMessage Serialize failed for " .. strOpName)
  end
  local headers, tbMsgResp
  reqCtx:SetCheckResponse(function()
    if GM == nil then
      return nil
    end
    headers = GM.HttpManager:ConvertHeaders(reqCtx.ResponseHeaders)
    if headers["process-time"] == nil then
      return "Missing process-time header"
    end
    if math.floor(reqCtx.Status / 100) == 2 then
      tbMsgResp = SsoMessage.Deserialize(reqCtx, strOpName)
      if tbMsgResp == nil then
        return "Failed to deserialize response"
      end
    end
    return nil
  end)
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    if bShowMask then
      GM.UIManager:HideMask()
    end
    if reqCtx.Rcode == ResultCode.Succeeded then
      local strToken = headers.token
      if not StringUtil.IsNilOrEmpty(strToken) then
        callback(true, tbMsgResp, strToken, reqCtx)
      elseif strOpName == "SODeleteUser" then
        callback(true, tbMsgResp, nil, reqCtx)
      else
        callback(false, tbMsgResp, "", reqCtx)
      end
      return
    end
    if reqCtx.Rcode == ResultCode.Not2xx then
      if not bIsRegister and (reqCtx.Status == 401 or reqCtx.Status == 403) then
        reqCtx:Retain()
        GM.SsoManager:OnTokenExpired(reqCtx:GetHeader(NetworkConfig.TokenHeaderKey), reqCtx, session)
        return
      end
      if (reqCtx.Status == 404 or reqCtx.Status == 502 or reqCtx.Status == 503) and reqCtx.MaxRetry > 0 then
        reqCtx.MaxRetry = reqCtx.MaxRetry - 1
        reqCtx:Retain()
        reqCtx:Send()
        return
      end
    end
    if SsoMessage.TryFallbackRequest(reqCtx) then
      return
    end
    Log.Warning("Sso Message failed, error message is " .. reqCtx.ErrorMsg, LogTag.Network)
    callback(false, {
      Rcode = reqCtx.Rcode,
      ErrorMsg = reqCtx.ErrorMsg
    }, "", reqCtx)
  end)
  reqCtx:Send()
end

function SsoMessage.Serialize(reqCtx, strOpName, tbMsgReq)
  local writer = CSNetLibManager:CreateBufferBlockWriter(reqCtx.ReqBody)
  Serialization.MessageHeader.Serialize(writer, {
    ProtocolMd5 = Serialization.ProtocolMd5,
    MessageId = reqCtx.MessageId,
    Operation = strOpName
  })
  local serializer = Serialization.reqNameMap[strOpName]
  local result = serializer.Serialize(writer, tbMsgReq)
  CSNetLibManager:ReleaseBufferBlockWriter(writer)
  return result
end

function SsoMessage.Deserialize(reqCtx, strOpName)
  local reader = CSNetLibManager:CreateBufferBlockReader(reqCtx.RespBody)
  local deserializer = Serialization.respNameMap[strOpName]
  local bRet, body
  bRet, body = deserializer.Deserialize(reader)
  local left = reader:GetLeftLength()
  CSNetLibManager:ReleaseBufferBlockReader(reader)
  if not bRet or left ~= 0 then
    return nil
  end
  return body
end

function SsoMessage.GenerateDeleteUserRequest(eActionType)
  Log.Assert(eActionType, "SsoMessage.GenerateDeleteUserRequest")
  return {
    game_token = GameConfig.GetGameToken(),
    userId = GM.UserModel:GetUserId(),
    action_type = eActionType,
    phone_number = ""
  }
end

function SsoMessage.TryFallbackRequest(reqCtx)
  if GameConfig.IsTestMode() then
    return GM.HttpManager:TryFallbackRequest(reqCtx, NetworkConfig.OfflineFallbackIp)
  else
    return GM.HttpManager:TryFallbackRequest(reqCtx, "*************", "https://sso-ga.mergecola.com")
  end
end
