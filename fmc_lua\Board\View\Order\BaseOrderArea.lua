OrderAreaScrollPriority = {Normal = 1, High = 2}
local mapOrderEnergyDiff = {}
BaseOrderArea = {}
BaseOrderArea.__index = BaseOrderArea

function BaseOrderArea:Init(boardView)
  local scale = ScreenFitter.GetBoardScale()
  UIUtil.SetSizeDelta(self.transform, ScreenFitter.GetScreenAdjustSize().x / scale)
  self.m_boardView = boardView
  self.m_boardCacheRoot:Init(boardView)
  self.m_orderGroupButton:Init(boardView)
  self.m_mapCells = {}
  self.m_passActivityNoticeBubbles = {}
  self.m_passActivityTimelimitBubbleCount = {}
  self.m_passActivityBonusBubbleCount = {}
  self.m_passActivityVIPBubbleCount = {}
  EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self._OnChangeGameMode)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._OnCloseView)
  EventDispatcher.AddListener(EEventType.ShowNewOrders, self, self._OnShowNewOrders)
  for activityType, activityDefinition in pairs(PassActivityDefinition) do
    EventDispatcher.AddActiveListener(activityDefinition.TaskProgressChangedEvent, self, function(_, message)
      self:_OnPassActivityTaskProgressChanged(activityType, message)
    end)
  end
end

function BaseOrderArea:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BaseOrderArea:GetBoardView()
  return self.m_boardView
end

function BaseOrderArea:GetBoardCacheRoot()
  return self.m_boardCacheRoot
end

function BaseOrderArea:GetOrderGroupButton()
  return self.m_orderGroupButton
end

function BaseOrderArea:GetCell(order)
  if self.m_mapCells[order] ~= nil then
    return self.m_mapCells[order]
  end
  return nil
end

function BaseOrderArea:GetCellById(orderId)
  for order, cell in pairs(self.m_mapCells) do
    if order:GetId() == orderId then
      return cell
    end
  end
  return nil
end

function BaseOrderArea:GetCells()
  return self.m_mapCells
end

function BaseOrderArea:RemoveCell(order)
  if self.m_mapCells[order] ~= nil then
    local cell = self.m_mapCells[order]
    Log.Assert(cell ~= nil and cell:GetOrder():GetState() == OrderState.Finished, "remove a cell with order unfinished")
    if self.m_bIsPlayingAnimation then
      cell.toBeRemoved = true
    else
      cell.gameObject:RemoveSelf()
    end
    self.m_mapCells[order] = nil
    mapOrderEnergyDiff[order] = nil
  end
end

function BaseOrderArea:_RemoveHostCell(order)
end

function BaseOrderArea:OnOrderStateChanged(codeCountMap, hasFlyingOrderItem, directDishLack)
  local sortedOrders, addedNew = self:_GetSortedOrders()
  local slotCells = self.m_mapCells
  local orderCellsPos = {}
  for _, order in ipairs(sortedOrders) do
    local cell = slotCells[order]
    if cell ~= nil then
      orderCellsPos[cell] = {
        old = cell.transform.anchoredPosition
      }
    end
  end
  local resort = not self:IsPlayingOrderAnimation() and not addedNew and not hasFlyingOrderItem and not self.m_bShowNewOrders
  for index, order in ipairs(sortedOrders) do
    local cell = slotCells[order]
    if cell == nil then
      local cellObject = Object.Instantiate(self.m_cellPrefab, self.m_orderRoot)
      cell = cellObject:GetLuaTable()
      cell:Init(order, self)
      slotCells[order] = cell
    end
    if resort or self.m_bShowNewOrders then
      cell.transform:SetSiblingIndex(index - 1)
    end
  end
  if addedNew and GM.SceneManager:GetGameMode() == EGameMode.Board then
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxOrderShow2)
  end
  local width = 0
  local cellCount = 0
  local centerOrder
  local orderDeliverStateChanged = false
  for _, cell in pairs(slotCells) do
    cellCount = cellCount + 1
    orderDeliverStateChanged = cell:OnOrderStateChanged(codeCountMap, directDishLack) or orderDeliverStateChanged
    width = width + cell:GetCellWidth()
    if cell:GetCanCenterOrderAndReset() then
      centerOrder = cell
    end
  end
  if centerOrder and not self.m_boardView:NeedScroll2OrderGroup() then
    self:ForceRebuildLayout()
    self:ScrollToRectTransformMiddle(centerOrder.transform)
  end
  local orderRootGroup = self.m_orderRootLayoutGroup
  if width == 0 then
    UIUtil.SetSizeDelta(self.m_orderRoot, orderRootGroup.padding.horizontal)
  else
    UIUtil.SetSizeDelta(self.m_orderRoot, width + orderRootGroup.padding.horizontal + (cellCount - 1) * orderRootGroup.spacing)
    if resort then
      self:ForceRebuildLayout()
      for _, order in ipairs(sortedOrders) do
        local cell = slotCells[order]
        if cell ~= nil then
          orderCellsPos[cell].new = cell.transform.anchoredPosition
          orderCellsPos[cell].siblingIndex = cell.transform:GetSiblingIndex()
        end
      end
      self:_PlayOrderMoveAnimation(orderCellsPos)
    elseif self.m_bShowNewOrders and addedNew then
      self:_HideNewOrders()
      self.m_bWaitForNewOrders = true
      self.m_boardView:OnOrderStateChanged()
    elseif self.m_bShowNewOrders and self.m_bWaitForNewOrders then
      self:_ScrollToEnd()
      DelayExecuteFuncInView(function()
        self.m_bWaitForNewOrders = nil
      end, 0.01, self)
    end
  end
  if orderDeliverStateChanged then
    EventDispatcher.DispatchEvent(EEventType.RefreshPrompt)
  end
end

function BaseOrderArea:_OnPassActivityTaskProgressChanged(activityType, message)
  local bubble = self.m_passActivityNoticeBubbles[message.Task]
  if bubble == nil then
    local activityDefinition = PassActivityDefinition[activityType]
    local model = GM.ActivityManager:GetModel(activityType)
    local config
    local eTaskType = message.Task.Type
    if eTaskType == EPassActivityTaskType.Timelimit then
      config = GM.DataResource.UIPrefabConfig:GetConfig(activityDefinition.TimelimitNoticeBubblePrefabName)
      local target = message.Task:GetFinalTarget()
      local bubbleCount = self.m_passActivityTimelimitBubbleCount[target]
      if bubbleCount and 0 < bubbleCount or not model:CanShowNoticeBubble(message.Task) then
        return
      end
      self.m_passActivityTimelimitBubbleCount[target] = (bubbleCount or 0) + 1
    elseif eTaskType == EPassActivityTaskType.Bonus and activityDefinition.BonusNoticeBubblePrefabName then
      config = GM.DataResource.UIPrefabConfig:GetConfig(activityDefinition.BonusNoticeBubblePrefabName)
      local target = message.Task:GetFinalTarget()
      local bubbleCount = self.m_passActivityBonusBubbleCount[target]
      if bubbleCount and 0 < bubbleCount then
        return
      end
      self.m_passActivityBonusBubbleCount[target] = (bubbleCount or 0) + 1
    elseif eTaskType == EPassActivityTaskType.Cycle and activityDefinition.CycleNoticeBubblePrefabName then
      config = GM.DataResource.UIPrefabConfig:GetConfig(activityDefinition.CycleNoticeBubblePrefabName)
    elseif eTaskType == EPassActivityTaskType.VIP and activityDefinition.BonusNoticeBubblePrefabName then
      config = GM.DataResource.UIPrefabConfig:GetConfig(activityDefinition.BonusNoticeBubblePrefabName)
      local target = message.Task:GetFinalTarget()
      local bubbleCount = self.m_passActivityVIPBubbleCount[target]
      if bubbleCount and 0 < bubbleCount then
        return
      end
      self.m_passActivityVIPBubbleCount[target] = (bubbleCount or 0) + 1
    end
    if not config then
      Log.Error("BP Bubble config error")
      return
    end
    local callback = function(gameObject)
      if self.m_passActivityNoticeBubbles[message.Task] then
        self:RemovePassActivityNoticeBubble(message.Task)
      end
      local newBubble = gameObject:GetLuaTable()
      newBubble:Init(activityType, self, message.Task)
      self.m_passActivityNoticeBubbles[message.Task] = newBubble
      LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_passActivityNoticeBubbleNode)
    end
    GM.ResourceLoader:LoadPrefab(config, self.m_passActivityNoticeBubbleNode, V3Zero, callback)
  else
    bubble:UpdateContent()
  end
end

function BaseOrderArea:RemovePassActivityNoticeBubble(task)
  if task.Type == EPassActivityTaskType.Timelimit then
    local target = task:GetFinalTarget()
    local bubbleCount = self.m_passActivityTimelimitBubbleCount[target]
    self.m_passActivityTimelimitBubbleCount[target] = bubbleCount and bubbleCount - 1 or 0
  elseif task.Type == EPassActivityTaskType.Bonus then
    local target = task:GetFinalTarget()
    local bubbleCount = self.m_passActivityBonusBubbleCount[target]
    self.m_passActivityBonusBubbleCount[target] = bubbleCount and bubbleCount - 1 or 0
  elseif task.Type == EPassActivityTaskType.VIP then
    local target = task:GetFinalTarget()
    local bubbleCount = self.m_passActivityVIPBubbleCount[target]
    self.m_passActivityVIPBubbleCount[target] = bubbleCount and bubbleCount - 1 or 0
  end
  Object.Destroy(self.m_passActivityNoticeBubbles[task].gameObject)
  self.m_passActivityNoticeBubbles[task] = nil
end

function BaseOrderArea:ForceRebuildLayout(refreshOrderRootWidth)
  if refreshOrderRootWidth then
    local width = 0
    local cellCount = 0
    for _, cell in pairs(self.m_mapCells) do
      cellCount = cellCount + 1
      width = width + cell:GetCellWidth()
    end
    local orderRootGroup = self.m_orderRootLayoutGroup
    if width == 0 then
      UIUtil.SetSizeDelta(self.m_orderRoot, orderRootGroup.padding.horizontal)
    else
      UIUtil.SetSizeDelta(self.m_orderRoot, width + orderRootGroup.padding.horizontal + (cellCount - 1) * orderRootGroup.spacing)
    end
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_orderRoot)
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_content)
end

function BaseOrderArea:SetScrollEnabled(enabled)
  self.m_scrollRect.enabled = enabled
  if not enabled then
    self.m_scrollRect:DOKill()
  end
end

function BaseOrderArea:ScrollToFront(needAnimation)
  return self:ScrollToNormalizedPosition(0, needAnimation, OrderAreaScrollPriority.Normal)
end

function BaseOrderArea:ScrollToOrderRoot(needAnimation)
  return self:_ScrollToRectTransform(self.m_orderRoot, needAnimation)
end

function BaseOrderArea:ScrollToOrderGroup(needAnimation, rightAlignment)
  return self:_ScrollToRectTransform(self.m_orderGroupButton.transform, needAnimation, rightAlignment, 0.4)
end

function BaseOrderArea:ScrollToRectTransformVisible(transform, needAnimation)
  local length = self.m_content.rect.width - self.transform.rect.width
  if length <= 0 then
    return 0
  end
  local localPosition = self.m_content:InverseTransformPoint(transform.position)
  local maxLeftX = localPosition.x - transform.pivot.x * transform.rect.width
  local minLeftX = maxLeftX - self.transform.rect.width + transform.rect.width
  local maxPos = math.min(maxLeftX / length, 1)
  local minPos = math.max(math.min(minLeftX / length, 1), -0.1)
  if minPos <= self.m_scrollRect.horizontalNormalizedPosition and maxPos >= self.m_scrollRect.horizontalNormalizedPosition then
    return 0
  end
  return self:_ScrollToRectTransform(transform, needAnimation)
end

function BaseOrderArea:_ScrollToRectTransform(transform, needAnimation, rightAlignment, dt)
  local length = self.m_content.rect.width - self.transform.rect.width
  if length <= 0 then
    return 0
  end
  local localPosition = self.m_content:InverseTransformPoint(transform.position)
  local normalizedPosition = 1
  local x = 0
  if rightAlignment == true then
    x = localPosition.x + transform.pivot.x * transform.rect.width * transform.localScale.x - self.transform.rect.width
  else
    x = localPosition.x - transform.pivot.x * transform.rect.width * transform.localScale.x
  end
  normalizedPosition = math.min(x / length, 1)
  normalizedPosition = math.max(normalizedPosition, 0)
  return self:ScrollToNormalizedPosition(normalizedPosition, needAnimation, OrderAreaScrollPriority.Normal, dt)
end

function BaseOrderArea:ScrollToRectTransformMiddle(transform)
  local length = self.m_content.rect.width - self.transform.rect.width
  if length <= 0 then
    return 0
  end
  self:ForceRebuildLayout()
  local localPosition = self.m_content:InverseTransformPoint(transform.position)
  local normalizedPosition = 0
  local deltaX = localPosition.x - self.transform.rect.width / 2
  if 0 < deltaX then
    normalizedPosition = math.min(deltaX / length, 1)
  end
  return self:ScrollToNormalizedPosition(normalizedPosition, true, OrderAreaScrollPriority.Normal)
end

function BaseOrderArea:GetNormalizedPosition()
  return self.m_scrollRect.horizontalNormalizedPosition
end

function BaseOrderArea:ScrollToNormalizedPosition(normalizedPosition, needAnimation, priority, dt)
  priority = priority or OrderAreaScrollPriority.Normal
  if self.m_scrollPriority ~= nil and priority < self.m_scrollPriority then
    return 0
  end
  self.m_scriptScroll = true
  if not needAnimation then
    self.m_scrollRect:DOKill()
    self.m_scrollRect.horizontalNormalizedPosition = normalizedPosition
    self.m_scriptScroll = false
    self.m_scriptScrolledOnce = true
    return 0
  end
  self.m_scrollPriority = priority
  if self.m_bShowNewOrders then
    local dur = self.m_orderRoot.rect.width / 450 * 0.35
    self.m_scrollRect:DOHorizontalNormalizedPos(normalizedPosition, dur):OnKill(function()
      self.m_scrollPriority = nil
      self.m_scriptScroll = false
    end):SetEase(Ease.InOutBack, 1.2)
    return dur
  else
    dt = dt or 0.2
    self.m_scrollRect:DOHorizontalNormalizedPos(normalizedPosition, dt):OnKill(function()
      self.m_scrollPriority = nil
      self.m_scriptScroll = false
    end):SetEase(Ease.InOutBack, 1.2)
    return dt
  end
end

function BaseOrderArea:_GetHostOrderCells()
  return Table.Empty
end

local compareOrder = function(a, b)
  local stateA = a:GetState()
  if stateA == OrderState.Finished then
    stateA = OrderState.CanDeliver
  end
  local stateB = b:GetState()
  if stateB == OrderState.Finished then
    stateB = OrderState.CanDeliver
  end
  if stateA == OrderState.CanDeliver and stateB ~= OrderState.CanDeliver then
    return true
  end
  if stateB == OrderState.CanDeliver and stateA ~= OrderState.CanDeliver then
    return false
  end
  local isTimelimitA = a:IsTimelimitOrder()
  local isTimelimitB = b:IsTimelimitOrder()
  if isTimelimitA ~= isTimelimitB then
    return isTimelimitA
  end
  local compareInfoA = a:GetOrderCompareInfo()
  local compareInfoB = b:GetOrderCompareInfo()
  if compareInfoA.unfilledCount == 0 and compareInfoB.unfilledCount ~= 0 then
    return true
  end
  if compareInfoB.unfilledCount == 0 and compareInfoA.unfilledCount ~= 0 then
    return false
  end
  if compareInfoA.unfilledCount == 0 and compareInfoB.unfilledCount == 0 and compareInfoA.remainCookDur ~= compareInfoB.remainCookDur then
    return compareInfoA.remainCookDur < compareInfoB.remainCookDur
  end
  if GM.ConfigModel:IsOrderSequenceByEnergyDiff() and mapOrderEnergyDiff[a] ~= nil and mapOrderEnergyDiff[b] ~= nil and mapOrderEnergyDiff[a] ~= mapOrderEnergyDiff[b] and (compareInfoA.unfilledCount ~= 0 or compareInfoB.unfilledCount ~= 0) then
    return mapOrderEnergyDiff[a] < mapOrderEnergyDiff[b]
  end
  if compareInfoA.filledCount ~= compareInfoB.filledCount then
    return compareInfoA.filledCount > compareInfoB.filledCount
  end
  if compareInfoA.cookedCount ~= compareInfoB.cookedCount then
    return compareInfoA.cookedCount > compareInfoB.cookedCount
  end
  if compareInfoA.cookingCount ~= compareInfoB.cookingCount then
    return compareInfoA.cookingCount > compareInfoB.cookingCount
  end
  if a:GetStateChange() ~= b:GetStateChange() then
    return a:GetStateChange() > b:GetStateChange()
  end
  if a:GetStateChangeTime() ~= b:GetStateChangeTime() then
    if a:GetStateChange() == OrderStateChange.Higher then
      return a:GetStateChangeTime() < b:GetStateChangeTime()
    else
      return a:GetStateChangeTime() > b:GetStateChangeTime()
    end
  end
  return a:GetId() < b:GetId()
end

function BaseOrderArea:_UpdateOrderEnergyDiff(order, boardModel)
  if not GM.ConfigModel:IsOrderSequenceByEnergyDiff() then
    return
  end
  if mapOrderEnergyDiff[order] == nil then
    mapOrderEnergyDiff[order] = boardModel:GetOrderFillEnergyDiff(order)
  end
end

function BaseOrderArea:_GetSortedOrders()
  local boardModel = self.m_boardView:GetModel()
  local orders = {}
  local order
  for _, cell in pairs(self.m_mapCells) do
    order = cell:GetOrder()
    table.insert(orders, order)
    self:_UpdateOrderEnergyDiff(order, boardModel)
  end
  table.sort(orders, compareOrder)
  local addedNew = false
  if not self:IsPlayingOrderAnimation() then
    local newOrders = {}
    for _, order in pairs(boardModel:GetOrders()) do
      if self.m_mapCells[order] == nil and not Table.ListContain(orders, order) and (not self.m_bShowNewOrders or not addedNew) then
        table.insert(newOrders, order)
        self:_UpdateOrderEnergyDiff(order, boardModel)
        addedNew = true
      end
    end
    table.sort(newOrders, compareOrder)
    Table.ListAppend(orders, newOrders)
  end
  return orders, addedNew
end

function BaseOrderArea:IsWaitingNewOrders()
  return self.m_bWaitForNewOrders
end

function BaseOrderArea:GetInBoardOrders()
  local orders = {}
  local states = {}
  if self.m_bWaitForNewOrders then
    return orders, states
  end
  for _, cell in pairs(self.m_mapCells) do
    table.insert(orders, cell:GetOrder())
    table.insert(states, cell:GetOrderViewState())
  end
  return orders, states
end

function BaseOrderArea:_OnChangeGameMode()
  if GM.SceneManager:GetGameMode() == self.m_boardView:GetModel():GetGameMode() then
    self:_ToggleAvatarVisible()
  end
end

function BaseOrderArea:_PlayOrderMoveAnimation(orderCellsPos)
  if not self.m_orderRootLayoutGroup or GM.SceneManager:GetGameMode() ~= EGameMode.Board or self:IsPlayingOrderAnimation() then
    return
  end
  local moved = false
  local cellCount = 0
  for _, pos in pairs(orderCellsPos) do
    if math.abs(pos.new.x - pos.old.x) > 20 then
      moved = true
    end
    cellCount = cellCount + 1
  end
  if moved == false then
    return
  end
  self.m_orderRootLayoutGroup.enabled = false
  self.m_bIsPlayingAnimation = true
  local index = cellCount - 1
  local showTween = DOTween.Sequence()
  for cell, pos in pairs(orderCellsPos) do
    cell.transform.anchoredPosition = pos.old
    if pos.new.x < pos.old.x then
      showTween:Insert(0.3, cell.transform:DOAnchorPos(pos.new, 0.6))
      showTween:Insert(0.0, cell.transform:DOScale(Vector3(0.9, 0.9, 1), 0.3))
      showTween:Insert(0.3, cell.transform:DOScale(Vector3(1.45, 1.45, 1), 0.3))
      showTween:Insert(0.6, cell.transform:DOScale(Vector3(0.8, 0.8, 1), 0.3))
      showTween:Insert(0.9, cell.transform:DOScale(V3One, 0.3))
      cell.transform:SetSiblingIndex(index)
      index = index - 1
    else
      showTween:Insert(0.3, cell.transform:DOAnchorPos(pos.new, 0.6))
      showTween:Insert(0.0, cell.transform:DOScale(Vector3(0.8, 0.8, 1), 0.3))
      showTween:Insert(0.9, cell.transform:DOScale(V3One, 0.3))
    end
  end
  showTween:OnComplete(function()
    for cell, pos in pairs(orderCellsPos) do
      if cell.toBeRemoved then
        cell.gameObject:RemoveSelf()
      elseif not cell.gameObject:IsNull() then
        cell.transform:SetSiblingIndex(pos.siblingIndex)
      end
    end
    self.m_orderRootLayoutGroup.enabled = true
    self.m_bIsPlayingAnimation = nil
    EventDispatcher.DispatchEvent(EEventType.OrderAnimationFinished)
  end)
end

function BaseOrderArea:IsPlayingOrderAnimation()
  for _, cell in pairs(self.m_mapCells) do
    if cell:IsPlayingAnimation() or cell:GetOrder():GetState() == OrderState.Finished then
      return true
    end
  end
  return self.m_bIsPlayingAnimation or false
end

function BaseOrderArea:OnScrolled()
  self:_ToggleAvatarVisible()
  local last = self.last
  local cur = self.m_scrollRect.horizontalNormalizedPosition
  self.last = cur
  if last == nil or self.m_scriptScroll or math.abs(cur - last) < 0.01 then
    return
  end
  EventDispatcher.DispatchEvent(EEventType.RefreshPrompt)
end

function BaseOrderArea:Update()
  if self:IsPlayingOrderAnimation() or self.m_scriptScroll or self.m_scriptScrolledOnce then
    self:_ToggleAvatarVisible()
    self.m_scriptScrolledOnce = false
  end
end

function BaseOrderArea:IsScriptScroll()
  return self.m_scriptScroll
end

function BaseOrderArea:_ToggleAvatarVisible()
  self:ForceRebuildLayout()
  local width = self.transform.sizeDelta.x
  local worldPos, relativePos
  for _, cell in pairs(self.m_mapCells) do
    worldPos = cell.transform.position
    relativePos = self.transform:InverseTransformPoint(worldPos).x
    if self.m_bHideAvatars then
      cell:ToggleAvatarVisible(false)
    elseif not cell.highlightForTutorial and (relativePos <= -width / 2 - 250 or relativePos >= width / 2 + 250) then
      cell:ToggleAvatarVisible(false)
    else
      cell:ToggleAvatarVisible(true)
    end
  end
end

function BaseOrderArea:_OnShowNewOrders()
  self.m_bShowNewOrders = true
end

function BaseOrderArea:IsShowNewOrders()
  return self.m_bShowNewOrders
end

function BaseOrderArea:_OnCloseView(msg)
  if msg and msg.name == UIPrefabConfigName.OrderDayRefreshWindow and self.m_bShowNewOrders then
    GM.UIManager:SetEventLock(true)
    self.m_bHideAvatars = true
    self:_ToggleAvatarVisible()
    DelayExecuteFuncInView(function()
      self.m_bHideAvatars = nil
      self:_ToggleAvatarVisible()
    end, 0.2, self)
    for _, cell in pairs(self.m_mapCells) do
      cell:PlayEnterAnimation(true)
    end
    local scrollToCacheRoot = false
    if GM.ConfigModel:UseNewCacheLayout() and self.m_cacheRootNode and self.m_boardCacheRoot:CanScrollToCacheRoot(true) then
      self.m_boardCacheRoot:ResetScrollToCacheRoot()
      scrollToCacheRoot = true
    end
    DelayExecuteFuncInView(function()
      self.m_orderRootLayoutGroup.enabled = true
      if scrollToCacheRoot then
        self:ScrollToRectTransformVisible(self.m_cacheRootNode, true)
      else
        self:ScrollToRectTransformVisible(self.m_orderGroupButton.transform, true)
      end
    end, 0.4, self)
    local dur = self.m_orderRoot.rect.width / 450 * 0.35
    DelayExecuteFuncInView(function()
      GM.UIManager:SetEventLock(false)
      self.m_bIsPlayingAnimation = nil
      self:OnOrderEnterFinish()
      DelayExecuteFuncInView(function()
        self.m_bShowNewOrders = nil
      end, 0.1, self)
    end, 0.4 + dur, self)
  end
end

function BaseOrderArea:_HideNewOrders()
  for _, cell in pairs(self.m_mapCells) do
    cell.transform.localScale = V3Zero
  end
end

function BaseOrderArea:_ScrollToEnd()
  for _, cell in pairs(self.m_mapCells) do
    cell.transform.localScale = V3One
  end
  self:ForceRebuildLayout()
  self.m_orderRootLayoutGroup.enabled = false
  for _, cell in pairs(self.m_mapCells) do
    cell.transform.localScale = V3Zero
  end
  self:ForceRebuildLayout()
  self:ScrollToNormalizedPosition(1, false)
  self.m_bIsPlayingAnimation = true
end

function BaseOrderArea:OnOrderEnterFinish()
  self:GetBoardView():OnOrderStateChanged()
  if not self:IsPlayingOrderAnimation() then
    EventDispatcher.DispatchEvent(EEventType.OrderAnimationFinished)
  end
end

function BaseOrderArea:ToggleLayoutGroup(enable)
  self.m_orderRootLayoutGroup.enabled = enable
end

function BaseOrderArea:GetOrderRoot()
  return self.m_orderRoot
end

function BaseOrderArea:GetScrollRect()
  return self.m_scrollRect
end
