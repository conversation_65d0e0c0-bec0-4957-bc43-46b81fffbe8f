ItemBubble = setmetatable({}, BaseItemComponent)
ItemBubble.__index = ItemBubble
local COUNT_DOWN = 60
local COUNT_DOWN_AUTORUN = 1

function ItemBubble.Create(innerItemCode)
  local itemBubble = setmetatable({}, ItemBubble)
  itemBubble:Init(innerItemCode)
  return itemBubble
end

function ItemBubble:Init(innerItemCode)
  self.m_innerItemCode = innerItemCode
  self.m_startTimer = GM.GameModel:GetServerTime()
  self.m_duration = IsAutoRun() and COUNT_DOWN_AUTORUN or COUNT_DOWN
end

function ItemBubble:FromSerialization(dbTable)
  self.m_startTimer = dbTable.bubbleStartTimer
end

function ItemBubble:ToSerialization(dbTable)
  dbTable.bubbleStartTimer = self.m_startTimer
end

function ItemBubble:UpdatePerSecond()
  if not self.m_lockBreak and self:GetTimerAmount() == 1 and GM.UIManager.allWindowClosed and not GM.UIManager:IsEventLock() and not GM.UIManager:IsMaskVisible() and not GM.TutorialModel:HasAnyStrongTutorialOngoing() then
    self:_Disappear()
    GM.BIManager:LogAction(EBIType.BubbleDisappear, self.m_innerItemCode)
  end
end

function ItemBubble:OnBreak(isFree)
  local cost = self:GetBreakCost()
  self:_Break(isFree, cost)
end

function ItemBubble:_Break(isFree, cost)
  local boardModel = self.m_itemModel:GetBoardModel()
  if not isFree then
    local gemNumber = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
    if cost > gemNumber then
      boardModel.event:Call(BoardEventType.LackGem, {
        LackNumber = cost - gemNumber
      })
      return
    end
    GM.PropertyDataManager:Consume(EPropertyType.Gem, cost, EBIType.BreakBubble, false, self.m_innerItemCode)
    EventDispatcher.DispatchEvent(EEventType.BuyBubble, {
      source = self.m_itemModel,
      cost = cost,
      newItemCode = self.m_innerItemCode
    })
  end
  local itemCost
  if not isFree then
    itemCost = {bubbleGemCost = cost}
  end
  self:_BubbleDisposed(self.m_innerItemCode, itemCost)
  GM.BIManager:LogStore(EPropertyType.Gem, cost, self.m_innerItemCode, 1, EShopType.BreakBubble)
end

function ItemBubble:OnSpeedUp()
  self:_Disappear()
  GM.BIManager:LogAction(EBIType.RemoveBubble, self.m_innerItemCode)
end

function ItemBubble:GetInnerItemCode()
  return self.m_innerItemCode
end

function ItemBubble:GetStartTimer()
  return self.m_startTimer
end

function ItemBubble:GetBreakCost()
  if self.m_bInTutorial then
    return 0
  end
  local config = GM.ItemDataModel:GetPropertyConfig(self.m_innerItemCode)
  if config and config.BubblePrice then
    return config.BubblePrice
  end
  Log.Error(tostring(self.m_innerItemCode) .. "没有价格")
  return 999
end

function ItemBubble:GetTimerAmount()
  local elapsedTime = GM.GameModel:GetServerTime() - self.m_startTimer
  local result = elapsedTime / self.m_duration
  return math.min(result, 1)
end

function ItemBubble:_Disappear()
  local config = GM.ConfigModel:GetLocalConfig(LocalConfigKey.BubbleConfig)
  local rewards = Table.ListWeightSelectOne(config).Rewards
  if rewards == nil then
    self:_BubbleDisposed()
  elseif GM.PropertyDataManager:IsPropertyType(rewards[PROPERTY_TYPE]) then
    self:_BubbleDisposed(nil, nil, {rewards})
  else
    self:_BubbleDisposed(rewards[PROPERTY_TYPE])
  end
end

function ItemBubble:_BubbleDisposed(newItemCode, itemCost, rewards)
  local boardModel = self.m_itemModel:GetBoardModel()
  local eventInfo
  if newItemCode then
    local newItem = boardModel:ReplaceItem(self.m_itemModel, newItemCode, itemCost)
    eventInfo = {
      Source = self.m_itemModel,
      New = newItem
    }
  else
    boardModel:RemoveItem(self.m_itemModel)
    if rewards ~= nil then
      RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.BubbleDisappear)
    end
    eventInfo = {
      Source = self.m_itemModel,
      Rewards = rewards
    }
  end
  boardModel.event:Call(BoardEventType.BubbleDisposed, eventInfo)
  EventDispatcher.DispatchEvent(EEventType.BubbleDisposed, eventInfo)
end

function ItemBubble:SetLockBreak(lockBreak)
  self.m_lockBreak = lockBreak
end

function ItemBubble:SetTutorialCD()
  self.m_bInTutorial = true
  self:SetLockBreak(true)
  EventDispatcher.DispatchEvent(EEventType.ItemTutorial, {
    item = self.m_itemModel
  })
end
