TestLuaRemoteWindow = setmetatable({
  canClickWindowMask = false,
  canCloseByAndroidBack = false,
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestLuaRemoteWindow.__index = TestLuaRemoteWindow

function TestLuaRemoteWindow:Init()
  self.defaultSrc = CS.LuaRemoteHelper.cdnUrl
  self:_InitUseRemote()
  TestLuaRemoteWindow.m_InputField = self.m_InputField
end

function TestLuaRemoteWindow:_InitUseRemote()
  self.m_remoteSwitchToggle.isOn = PlayerPrefs.GetInt("use_remote_code", 0) == 1
  self.m_ipInputInpu.text = PlayerPrefs.GetString("use_remote_code_ip", "")
  self.m_InputField.text = "请输入 " .. self.defaultSrc .. " 路径下文件夹的名字，如：Default"
end

function TestLuaRemoteWindow:OnUpdateUseRemoteCode()
  PlayerPrefs.SetInt("use_remote_code", self.m_remoteSwitchToggle.isOn and 1 or 0)
  if self.m_ipInputInpu.text ~= "" then
    PlayerPrefs.SetString("use_remote_code_ip", self.m_ipInputInpu.text)
  end
end

function TestLuaRemoteWindow:_OnUploadLuaCode()
  if Application.platform ~= CS.UnityEngine.RuntimePlatform.OSXEditor and Application.platform ~= CS.UnityEngine.RuntimePlatform.WindowsEditor then
    self.m_InputField.text = "只能在PC端进行此操作"
    return
  end
  local name = self.m_InputField.text
  if not name or name == "" or name == "请输入 " .. self.defaultSrc .. " 路径下文件夹的名字，如：Default" or name == "只能在PC端进行此操作" then
    self.m_InputField.text = "请输入 " .. self.defaultSrc .. " 路径下文件夹的名字，如：Default"
    return
  end
  CS.LuaRemoteHelper.UploadLuaFile(function(isOk)
  end, name)
end

function TestLuaRemoteWindow:_OnDownloadCode()
  local name = self.m_InputField.text
  if not name or name == "" or name == "请输入 " .. self.defaultSrc .. " 路径下文件夹的名字，如：Default" or name == "只能在PC端进行此操作" then
    self.m_InputField.text = "请输入 " .. self.defaultSrc .. " 路径下文件夹的名字，如：Default"
    return
  end
  CS.LuaRemoteHelper.Instance:DownloadLuaCode(self.defaultSrc .. name)
end

function TestLuaRemoteWindow.SetInputText(_, msg)
  if TestLuaRemoteWindow.m_InputField and not TestLuaRemoteWindow.m_InputField.gameObject:IsNull() then
    TestLuaRemoteWindow.m_InputField.text = msg
  end
end

function TestLuaRemoteWindow:_OnRestart()
  if self.m_ipInputInpu.text ~= "" then
    PlayerPrefs.SetString("use_remote_code_ip", self.m_ipInputInpu.text)
  end
  PlatformInterface.ExitGame()
end

function TestLuaRemoteWindow:_OnDefault()
  TestLuaRemoteWindow.m_InputField.text = "Default"
end
