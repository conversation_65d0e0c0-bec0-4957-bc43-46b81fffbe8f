SignOutWindow = setmetatable({}, BaseWindow)
SignOutWindow.__index = SignOutWindow

function SignOutWindow:Init()
  self.m_accountAvatar:SetAvatar(EAvatarFrame.Normal, GM.AccountManager:GetAccountPictureUrl())
  self.m_nameText.text = GM.AccountManager:GetAccountName()
  local bindTag = ImageFileConfigName.icon_frame_fb
  if GM.AccountManager:GetAccountType() == ESsoSocialType.Apple then
    bindTag = ImageFileConfigName.icon_frame_apple
  elseif GM.AccountManager:GetAccountType() == ESsoSocialType.Google then
    bindTag = ImageFileConfigName.icon_frame_google
  end
  SpriteUtil.SetImage(self.m_bingTag, bindTag, true)
end

function SignOutWindow:OnSignOutButtonClicked()
  self:Close()
  GM.AccountManager:Logout(true)
end
