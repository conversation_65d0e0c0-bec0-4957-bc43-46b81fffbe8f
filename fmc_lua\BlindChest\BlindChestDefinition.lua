BlindChestDefinition = {
  [ActivityType.BlindChest1] = {
    EntryButtonKey = ESceneViewHudButtonKey.BlindChest1,
    EntryRootKey = EEntryRootKey.BlindChest,
    ActivityDataTableName = VirtualDBTableName.BlindChest1,
    StateChangedEvent = EEventType.BlindChest1StateChanged,
    TurnChangedEvent = EEventType.BlindChest1TurnChanged,
    KeyChangedEvent = EEventType.BlindChest1KeyChanged,
    TutorialGetKeyBIType = EBIType.BlindChest1GetFreeKey,
    GetKeyBIType = EBIType.BlindChest1ObtainKey,
    GetRewardBIType = EBIType.BlindChest1TakeReward,
    GetTopRewardBIType = EBIType.BlindChest1TakeTopReward,
    GetFinalRewardBIType = EBIType.BlindChest1TakeFinalReward,
    ActivityTokenPropertyType = EPropertyType.BlindChest1,
    ActivityTokenImage = ImageFileConfigName.blindchest1_key_icon,
    FinalRewardIcon = ImageFileConfigName.blindchest1_reward_box,
    FinalRewardScale = 1.7,
    EntryPrefabName = UIPrefabConfigName.BlindChest1Entry,
    BoardEntryPrefabName = UIPrefabConfigName.BlindChest1BoardEntry,
    MainWindowPrefabName = UIPrefabConfigName.BlindChest1MainWindow,
    HelpWindowPrefabName = UIPrefabConfigName.BlindChest1HelpWindow,
    ReadyWindowPrefabName = UIPrefabConfigName.BlindChest1ReadyWindow,
    SuccessWindowPrefabName = UIPrefabConfigName.BlindChest1SuccessWindow,
    EndWindowPrefabName = UIPrefabConfigName.BlindChest1EndWindow,
    EndConfirmWindowPrefabName = UIPrefabConfigName.BlindChest1EndConfirmWindow,
    TutorialStartCondition = ETutorialStartCondition.BlindChestStart,
    ResourceLabels = {
      AddressableLabel.BlindChest1,
      AddressableLabel.BlindChestCommon
    }
  }
}
for activityType, activityDefinition in pairs(BlindChestDefinition) do
  EPropertySprite[activityDefinition.ActivityTokenPropertyType] = activityDefinition.ActivityTokenImage
  EPropertySpriteBig[activityDefinition.ActivityTokenPropertyType] = activityDefinition.ActivityTokenImage
end
