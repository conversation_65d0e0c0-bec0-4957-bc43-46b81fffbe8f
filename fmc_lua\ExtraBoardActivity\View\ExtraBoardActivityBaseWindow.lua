ExtraBoardActivityBaseWindow = setmetatable({bIgnoreCloseByState = false}, BaseWindow)
ExtraBoardActivityBaseWindow.__index = ExtraBoardActivityBaseWindow

function ExtraBoardActivityBaseWindow:Init(activityType, bUserClick)
  self.m_activityType = activityType
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_activityDefinition = ExtraBoardActivityDefinition[self.m_activityType]
  self.m_model:SetWindowOpened()
  if not self.bIgnoreCloseByState then
    AddHandlerAndRecordMap(self.m_model.event, ExtraBoardActivityEventType.StateChanged, {
      obj = self,
      method = self.Close
    })
  end
  self:_LogWindowOpen(bUserClick)
  if self.m_countDownText ~= nil then
    self.m_finishTime = self.m_model:GetNextStateTime()
  end
  self:UpdatePerSecond()
  self:UpdateCommonSortingOrder()
  if self.m_commonEffectRoot ~= nil then
    UIUtil.SetActive(self.m_commonEffectRoot.gameObject, false)
    DelayExecuteFuncInView(function()
      UIUtil.SetActive(self.m_commonEffectRoot.gameObject, true)
    end, 0.1, self)
  end
end

function ExtraBoardActivityBaseWindow:_LogWindowOpen(bUserClick)
  self:LogWindowAction(EBIType.UIActionType.Open, {
    bUserClick and EBIReferType.UserClick or EBIReferType.AutoPopup
  })
end

function ExtraBoardActivityBaseWindow:UpdatePerSecond()
  if self.m_countDownText and self.m_finishTime ~= nil then
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(math.max(0, self.m_finishTime - GM.GameModel:GetServerTime()), 2, false, false)
  end
end

function ExtraBoardActivityBaseWindow:OnCloseView(bWithoutAnimation)
  BaseWindow.OnCloseView(self, bWithoutAnimation)
  self:LogWindowAction(EBIType.UIActionType.Close)
  if self.m_commonEffectRoot ~= nil then
    UIUtil.SetActive(self.m_commonEffectRoot.gameObject, false)
  end
end

function ExtraBoardActivityBaseWindow:OnDestroy()
  BaseWindow.OnDestroy(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
end

function ExtraBoardActivityBaseWindow:UpdateCommonSortingOrder()
  local baseSortingOrder = self:GetSortingOrder()
  if self.m_commonEffectRoot ~= nil then
    local arrRenderer = self.m_commonEffectRoot:GetComponentsInChildren(typeof(Renderer), true)
    for i = 0, arrRenderer.Length - 1 do
      arrRenderer[i].sortingOrder = (arrRenderer[i].sortingOrder or 0) + baseSortingOrder
    end
  end
end
