local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer
local Step = {
  ClickGrid = "1",
  ClickGrid2 = "2",
  ShowTarget = "3"
}
local EStep2TextKey = {
  [Step.ClickGrid] = "treasure_dig_guide_desc1",
  [Step.ClickGrid2] = "treasure_dig_guide_desc2",
  [Step.ShowTarget] = "treasure_dig_guide_desc3"
}

function Executer:OnStart()
  self.m_mapActivityDefinition = {}
  for actType, activityDefinition in pairs(DigActivityDefinition) do
    self.m_mapActivityDefinition[actType] = activityDefinition
  end
  for _, activityDefinition in pairs(self.m_mapActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
    EventDispatcher.AddListener(activityDefinition.GridChangedEvent, self, self._OnDigGrid)
  end
  EventDispatcher.AddListener(EEventType.OpenView, self, self.OnViewOpened)
end

function Executer:_OnStateChanged()
  if self.m_window ~= nil then
    self:Finish(self.m_gesture)
  end
end

function Executer:OnViewOpened(msg)
  for activityType, activityDefinition in pairs(self.m_mapActivityDefinition) do
    if msg.name == activityDefinition.MainWindowPrefabName then
      local window = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
      local model = GM.ActivityManager:GetModel(activityType)
      if window ~= nil and model ~= nil then
        model:SetWindowOpened(activityDefinition.MainWindowPrefabName)
        self.m_window = window
        self.m_activityModel = model
        self.m_activityType = activityType
        self.m_activityDefinition = activityDefinition
        GM.UIManager:SetEventLock(true)
        DelayExecuteFunc(function()
          GM.UIManager:SetEventLock(false)
          self:_StartExecuteTutorial()
        end, 0.5)
      end
    end
  end
end

function Executer:_OnDigGrid()
  if self.m_strOngoingDatas == Step.ClickGrid2 then
    if self.m_window ~= nil then
      self.m_window:SetTargetGridForTutorial(false)
    end
    if self.m_boardViewRect ~= nil then
      TutorialHelper.DehighlightForUI(self.m_boardViewRect)
      self.m_boardViewRect = nil
    end
    TutorialHelper.HideDialog()
    TutorialHelper.HideTutorialLayer(self.m_gesture)
    self.m_gesture = nil
    self.m_model:SetTutorialFinished(self:GetTutorialId())
    GM.UIManager:SetEventLock(true)
    DelayExecuteFunc(function()
      GM.UIManager:SetEventLock(false)
      self:_ExecuteStep3()
    end, 3)
  elseif self.m_strOngoingDatas == Step.ClickGrid then
    self:_ExecuteStep2()
  end
end

function Executer:_StartExecuteTutorial()
  if self.m_window == nil or self.m_activityModel == nil then
    return
  end
  local arrPos = self.m_window:GetTwoValidGridPos()
  if Table.IsEmpty(arrPos) then
    Log.Error("关卡不包含1x2的宝物, 请检查配置")
    self:Finish()
    return
  end
  if not self.m_activityModel:IsGridDig(arrPos[1].x, arrPos[1].y) then
    self:_ExecuteStep1()
  elseif not self.m_activityModel:IsGridDig(arrPos[2].x, arrPos[2].y) then
    self:_ExecuteStep2()
  else
    self:Finish()
    Log.Error("DigActivity First Step Tutorial Error")
  end
end

function Executer:_ExecuteStep1()
  if self.m_window == nil or self.m_activityModel == nil or self.m_window.gameObject == nil or self.m_window.gameObject:IsNull() then
    self:Finish(self.m_gesture)
    return
  end
  self.m_strOngoingDatas = Step.ClickGrid
  local boardViewRect = self.m_window:GetBoardViewRect()
  local arrPos = self.m_window:GetTwoValidGridPos()
  local gridRect = self.m_window:GetGridRectTrans(arrPos[1].x, arrPos[1].y)
  if boardViewRect ~= nil and gridRect ~= nil then
    self:SetStrongTutorial(true)
    self.m_window:SetTargetGridForTutorial(true, arrPos[1])
    TutorialHelper.SetMaskAlphaOnce(EWindowMaskAlpha.Dark)
    TutorialHelper.WholeMask()
    TutorialHelper.HighlightForUI(boardViewRect)
    local firstDigRect = self.m_window:GetGridRectTrans(1, 1)
    TutorialHelper.ShowDialogWithTrans(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), firstDigRect, -30)
    self.m_boardViewRect = boardViewRect
    self.m_gesture = TutorialHelper.TapOnCustomRectTrans(gridRect)
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  else
    self:Finish()
  end
end

function Executer:_ExecuteStep2()
  if self.m_window == nil or self.m_activityModel == nil or self.m_window.gameObject == nil or self.m_window.gameObject:IsNull() then
    self:Finish(self.m_gesture)
    return
  end
  self.m_strOngoingDatas = Step.ClickGrid2
  TutorialHelper.HideDialog()
  TutorialHelper.HideTutorialLayer(self.m_gesture)
  self.m_gesture = nil
  local boardViewRect = self.m_window:GetBoardViewRect()
  local arrPos = self.m_window:GetTwoValidGridPos()
  local gridRect = self.m_window:GetGridRectTrans(arrPos[2].x, arrPos[2].y)
  if boardViewRect ~= nil and gridRect ~= nil then
    self:SetStrongTutorial(true)
    self.m_window:SetTargetGridForTutorial(true, arrPos[2])
    TutorialHelper.SetMaskAlphaOnce(EWindowMaskAlpha.Dark)
    TutorialHelper.WholeMask()
    if self.m_boardViewRect == nil then
      TutorialHelper.HighlightForUI(boardViewRect)
      self.m_boardViewRect = boardViewRect
    end
    local firstDigRect = self.m_window:GetGridRectTrans(1, 1)
    TutorialHelper.ShowDialogWithTrans(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), firstDigRect, -30)
    self.m_gesture = TutorialHelper.TapOnCustomRectTrans(gridRect)
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  else
    self:Finish()
  end
end

function Executer:_ExecuteStep3()
  if self.m_window == nil or self.m_activityModel == nil or self.m_window.gameObject == nil or self.m_window.gameObject:IsNull() then
    self:Finish(self.m_gesture)
    return
  end
  self.m_strOngoingDatas = Step.ShowTarget
  self.m_titleContentRect = self.m_window:GetTitleContentRect()
  if self.m_titleContentRect ~= nil then
    TutorialHelper.SetMaskAlphaOnce(EWindowMaskAlpha.Dark)
    TutorialHelper.WholeMask(function()
      if self.m_window ~= nil and self.m_titleContentRect ~= nil then
        TutorialHelper.DehighlightForUI(self.m_titleContentRect)
        self.m_titleContentRect = nil
        self.m_window:ShowHandEffectToHelpBtn(true)
      end
      self:Finish()
    end)
    TutorialHelper.HighlightForUI(self.m_titleContentRect)
    local key = EStep2TextKey[self.m_strOngoingDatas]
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(key), 45)
  else
    self:Finish()
  end
end

function Executer:Finish(gesture, arrow)
  if self.m_window ~= nil then
    self.m_window:SetTargetGridForTutorial(false)
  end
  if self.m_boardViewRect ~= nil then
    TutorialHelper.DehighlightForUI(self.m_boardViewRect)
  end
  TutorialExecuter.Finish(self, gesture, arrow)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
