SsoManager = {}
SsoManager.__index = SsoManager

function SsoManager:Init()
  self.m_arrPendingReqs = {}
  self.m_bIsRequesting = false
  self.m_session = 0
end

function SsoManager:LateInit()
  if GM.UserModel.newInstallUser and not DeviceInfo.IsSystemIOS() and PlayerPrefs.GetString(EPlayerPrefKey.SsoRegisterIdfa, "") == "" then
    local scheduler = function()
      if DeviceInfo.GetAdvertisingIdentifier() ~= "" and PlayerPrefs.GetString(EPlayerPrefKey.SsoRegisterIdfa, "") == "" and not self.m_bIsRequesting then
        Log.Info("SSO register again to sync idfa")
        self:Register()
        Scheduler.UnscheduleTarget(self)
        CSFirebaseManager:SetUserProperty("idfa", DeviceInfo.GetAdvertisingIdentifier())
      elseif PlayerPrefs.GetString(EPlayerPrefKey.SsoRegisterIdfa, "") ~= "" then
        Scheduler.UnscheduleTarget(self)
      end
    end
    Scheduler.Schedule(scheduler, self, 2, 60)
  end
end

function SsoManager:SaveRegisterIdfa(idfa)
  PlayerPrefs.SetString(EPlayerPrefKey.SsoRegisterIdfa, idfa)
end

function SsoManager:GetToken()
  return GM.UserModel:Get(EUserLocalDataKey.SSOToken) or ""
end

function SsoManager:GetSession()
  return self.m_session
end

function SsoManager:SetToken(strToken)
  GM.UserModel:Set(EUserLocalDataKey.SSOToken, strToken)
end

function SsoManager:OnTokenExpired(oldToken, reqCtx, session)
  Log.Info("SsoManager:OnTokenExpired")
  if StringUtil.IsNilOrEmpty(oldToken) then
    if reqCtx ~= nil then
      reqCtx:Discard()
    end
    return
  end
  local strToken = self:GetToken()
  if strToken == oldToken then
    self:SetToken("")
    if reqCtx ~= nil then
      self:WaitForToken(reqCtx)
    end
    self:Register()
  elseif reqCtx ~= nil then
    if strToken == "" then
      self:WaitForToken(reqCtx)
    elseif session ~= self.m_session then
      reqCtx:Discard()
    else
      reqCtx:SetHeader(NetworkConfig.TokenHeaderKey, strToken)
      reqCtx:Send()
    end
  end
end

function SsoManager:Register(callback, isLoading, installId)
  callback = callback or function()
  end
  if self.m_bIsRequesting then
    callback(false)
    return
  end
  local startTime = NetTimeStamp.Create(EBIType.NetworkCheckAction.StartSSO)
  GM.BIManager:LogNet(EBIType.NetworkCheckAction.StartSSO, nil, nil, nil, isLoading)
  self.m_bIsRequesting = true
  local request = SsoMessage.GenerateRequest(ESsoActionType.Lookup, ESsoSocialType.Device)
  request.installationId = installId or request.installationId
  SsoMessage.SendByHttp(request, "", function(bIsSucc, tbMsgResp, strToken, reqCtx)
    local timeInterval = startTime:EndAndGetDur()
    self.m_bIsRequesting = false
    if not bIsSucc then
      self:_FailPendingRequests()
      local msg = Log.Encode(tbMsgResp)
      callback(false, msg)
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.SSOFailed, msg, reqCtx, timeInterval, isLoading)
      return
    end
    local status = tbMsgResp.registrationStatus
    local bSuccessStatus = status == ESsoActionStatus.NewRegister or status == ESsoActionStatus.OldUser
    if not bSuccessStatus or tbMsgResp.mainUserId == 0 or StringUtil.IsNilOrEmpty(strToken) then
      self:_FailPendingRequests()
      local msg = Log.Encode(tbMsgResp)
      callback(false, msg)
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.SSOFailed, msg, reqCtx, timeInterval, isLoading)
      return
    end
    if not StringUtil.IsNilOrEmpty(strToken) then
      self:SetToken(strToken)
    end
    ApplicationManager.Instance.userId = tbMsgResp.mainUserId
    ApplicationManager.Instance.isNewUser = status == ESsoActionStatus.NewRegister
    local restart = false
    local userId = GM.UserModel:GetUserId()
    if userId == 0 or userId == tbMsgResp.mainUserId then
      GM.UserModel:SetUserId(tbMsgResp.mainUserId)
      self:_ResendPendingRequests()
      callback(true)
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.SSOSuccess, nil, reqCtx, timeInterval, isLoading)
    else
      self:_FailPendingRequests()
      callback(false, " IDChange")
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.SSOSuccess, "newId=" .. tostring(tbMsgResp.mainUserId), reqCtx, timeInterval, isLoading)
      GM.SyncModel:ClearAllData()
      if not StringUtil.IsNilOrEmpty(strToken) then
        self:SetToken(strToken)
      end
      GM.UserModel:SetUserId(tbMsgResp.mainUserId)
      restart = true
    end
    if status == ESsoActionStatus.NewRegister then
      local serverTime = tbMsgResp.service_time
      GM.OperBIManager:TrackFirstInstall(tostring(serverTime))
      GM.UserModel:Set(EUserLocalDataKey.RegisterVersion, GameConfig.GetCurrentVersion())
    else
      GM.UserModel:Set(EUserLocalDataKey.RegisterVersion, tbMsgResp.reg_version)
    end
    if restart then
      GM:RestartGame(ERestartType.WithHotfix, EBIProjectType.RestartGameAction.SSO)
    end
  end, false, true, nil, isLoading)
end

function SsoManager:SendRequest(request, callback, ssoToken)
  ssoToken = ssoToken == nil and self:GetToken() or ssoToken
  SsoMessage.SendByHttp(request, ssoToken, function(success, tbResp, respToken)
    if callback then
      callback(success, request, tbResp, respToken)
    end
  end, true, false)
end

function SsoManager:WaitForToken(reqCtx)
  if Table.ListContain(self.m_arrPendingReqs, reqCtx) then
    Log.Error("SsoManager:_AddToPendingReqs duplicate")
    return
  end
  table.insert(self.m_arrPendingReqs, reqCtx)
end

function SsoManager:_ResendPendingRequests()
  local strToken = self:GetToken()
  local reqCtx
  while not Table.IsEmpty(self.m_arrPendingReqs) do
    reqCtx = table.remove(self.m_arrPendingReqs)
    reqCtx:SetHeader(NetworkConfig.TokenHeaderKey, strToken)
    reqCtx:Send()
  end
end

function SsoManager:_FailPendingRequests()
  local reqCtx
  while not Table.IsEmpty(self.m_arrPendingReqs) do
    reqCtx = table.remove(self.m_arrPendingReqs)
    reqCtx:Discard()
  end
  self.m_session = self.m_session + 1
end

function SsoManager:ThrowPendingRequests()
  local strToken = self:GetToken()
  local reqCtx
  while not Table.IsEmpty(self.m_arrPendingReqs) do
    reqCtx = table.remove(self.m_arrPendingReqs)
    reqCtx:SetHeader(NetworkConfig.TokenHeaderKey, strToken)
    reqCtx.MaxRetry = 0
    reqCtx:Send()
  end
  self.m_session = self.m_session + 1
end

function SsoManager:SendDeleteUserRequest(request, callback)
  SsoMessage.SendByHttp(request, self:GetToken(), function(success, tbResp)
    if callback then
      callback(success, tbResp)
    end
  end, true, false, "SODeleteUser")
end
