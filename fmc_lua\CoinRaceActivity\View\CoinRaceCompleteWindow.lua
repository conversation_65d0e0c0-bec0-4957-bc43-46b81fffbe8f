CoinRaceCompleteWindow = setmetatable({}, BaseWindow)
CoinRaceCompleteWindow.__index = CoinRaceCompleteWindow

function CoinRaceCompleteWindow:Init(activityType)
  local model = GM.ActivityManager:GetModel(activityType)
  model:SetWindowOpened()
  local roundCnt = model:GetRoundCount()
  local arrMedal = {}
  for i = 1, roundCnt do
    local rank = model:GetRoundRanks(i)
    if rank and rank <= 5 then
      table.insert(arrMedal, rank)
    end
  end
  local bMedal = not Table.IsEmpty(arrMedal)
  self.m_medalGo:SetActive(bMedal)
  if bMedal then
    UIUtil.SetActive(self.m_medalBgGo2, 8 < #arrMedal)
    local luaTable
    for index, value in ipairs(arrMedal) do
      if index == 1 then
        luaTable = self.m_medal:GetLuaTable()
      else
        luaTable = Object.Instantiate(self.m_medal, 8 < index and self.m_medalBgGo2.transform or self.m_medalBgGo.transform):GetLuaTable()
      end
      luaTable:Init(value, activityType)
    end
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_medalBgTransf)
  end
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.AutoPopup
  })
end

CoinRaceMedal = {}
CoinRaceMedal.__index = CoinRaceMedal

function CoinRaceMedal:Init(rank, activityType)
  if 0 < rank and rank <= 5 then
    local prefix = CoinRaceActivityDefinition[activityType].CoinRaceBoardEntryRankImgPrefix
    SpriteUtil.SetImage(self.m_medalImg, prefix .. rank, true)
  end
end
