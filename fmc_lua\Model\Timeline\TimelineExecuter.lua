TimelineExecuter = {
  [ETimelineAction.SlotActions] = {
    ExecuteFunc = function(timelineData, stepData, onFinish)
      local roomView = GM.ChapterManager:GetActiveRoomView()
      local slotId = stepData.slotId
      local state = stepData:GetFinalState(timelineData)
      local hasChanged = false
      local changeAfterAnimation = false
      local totalCount = #stepData.arrSlotActions
      local finishedCount = 0
      local changeState = function()
        for _, slotAction in ipairs(stepData.arrSlotActions) do
          if slotAction.eSlotAnimationType ~= ESlotAnimationType.Effect then
            roomView:SetSlotState(slotId, state)
            break
          end
        end
      end
      if 0 < stepData.changeDelay then
        DelayExecuteFuncInView(function()
          hasChanged = true
          changeState()
          if finishedCount >= totalCount and onFinish then
            onFinish(stepData)
          end
        end, stepData.changeDelay, roomView)
      else
        changeAfterAnimation = true
      end
      local funcOnAnimationEnd = function()
        finishedCount = finishedCount + 1
        if finishedCount >= totalCount then
          if changeAfterAnimation then
            changeState()
            if onFinish then
              onFinish(stepData)
            end
          elseif hasChanged and onFinish then
            onFinish(stepData)
          end
        end
      end
      for _, actionData in ipairs(stepData.arrSlotActions) do
        if actionData.eSlotAnimationType == ESlotAnimationType.Trans then
          roomView:PlayTransAnimation(slotId, actionData.animationConfig, function()
            funcOnAnimationEnd()
          end, actionData.delay)
        elseif actionData.eSlotAnimationType == ESlotAnimationType.Effect then
          roomView:PlayEffectOnSlot(slotId, actionData.animationConfig, function()
            funcOnAnimationEnd()
          end, actionData.delay)
        elseif actionData.eSlotAnimationType == ESlotAnimationType.Place then
          roomView:PlayPlaceAnimation(slotId, actionData.animationConfig, state, function()
            funcOnAnimationEnd()
          end, actionData.delay)
        elseif actionData.eSlotAnimationType == ESlotAnimationType.CameraShake then
          roomView:ShakeCamera(actionData.animationConfig, actionData.delay, actionData.duration, funcOnAnimationEnd)
        elseif actionData.eSlotAnimationType == ESlotAnimationType.Window then
          if UIPrefabConfigName.HasConfig(actionData.animationConfig) then
            GM.UIManager:OpenView(actionData.animationConfig, funcOnAnimationEnd)
          else
            Log.Error("slotAction 尝试打开的弹窗不存在：" .. tostring(actionData.animationConfig) .. "，请资源策划检查！")
            funcOnAnimationEnd()
          end
        end
      end
      if totalCount == 0 and changeAfterAnimation and onFinish then
        changeState()
        onFinish(stepData)
      end
    end,
    StopFunc = function(timelineData, stepData, onFinish)
      local roomView = GM.ChapterManager:GetActiveRoomView()
      Scheduler.UnscheduleTarget(roomView)
      local slotId = stepData.slotId
      local state = stepData:GetFinalState(timelineData)
      roomView:SetSlotState(slotId, state)
      if onFinish then
        onFinish(stepData)
      end
    end
  },
  [ETimelineAction.SlotAnimator] = {
    ExecuteFunc = function(timelineData, stepData, onFinish)
      local roomView = GM.ChapterManager:GetActiveRoomView()
      local slotId = stepData.slotId
      local state = stepData:GetFinalState(timelineData)
      roomView:PlayAnimator(slotId, state, onFinish, stepData.delay)
    end,
    StopFunc = function(timelineData, stepData, onFinish)
      local roomView = GM.ChapterManager:GetActiveRoomView()
      Scheduler.UnscheduleTarget(roomView)
      local slotId = stepData.slotId
      local state = stepData:GetFinalState(timelineData)
      roomView:SetSlotState(slotId, state)
      if onFinish then
        onFinish(stepData)
      end
    end
  },
  [ETimelineAction.SlotSpines] = {
    ExecuteFunc = function(timelineData, stepData, onFinish)
      local roomView = GM.ChapterManager:GetActiveRoomView()
      local slotId = stepData.slotId
      local state = stepData:GetFinalState(timelineData)
      local totalCount = #stepData.arrSlotActions
      local finishedCount = 0
      local funcOnAnimationEnd = function()
        finishedCount = finishedCount + 1
        if finishedCount >= totalCount and onFinish then
          onFinish(stepData)
        end
      end
      for _, actionData in ipairs(stepData.arrSlotActions) do
        roomView:PlaySpineAnimation(slotId, actionData.state or state, actionData.animationName, funcOnAnimationEnd, actionData.delay)
      end
      if totalCount == 0 and onFinish then
        onFinish(stepData)
      end
    end,
    StopFunc = function(timelineData, stepData, onFinish)
      local roomView = GM.ChapterManager:GetActiveRoomView()
      local slotId = stepData.slotId
      local state = stepData:GetFinalState(timelineData)
      local slotView = roomView:GetSlotView(slotId)
      slotView:SpeedUpSpine(state)
      if onFinish then
        onFinish(stepData)
      end
    end
  },
  [ETimelineAction.Plot] = {
    ExecuteFunc = function(timelineData, stepData, onFinish)
      local funcOpenStory = function()
        local storyId = stepData.storyId
        local isSpecialStory = timelineData.chapterName == nil
        GM.UIManager:OpenView(UIPrefabConfigName.StoryWindow, storyId, isSpecialStory, onFinish, true)
      end
      if stepData.delay > 0 then
        GM.UIManager:SetEventLock(true)
        DelayExecuteFunc(function()
          GM.UIManager:SetEventLock(false)
          funcOpenStory()
        end, stepData.delay)
      else
        funcOpenStory()
      end
    end
  },
  [ETimelineAction.CameraMove] = {
    ExecuteFunc = function(timelineData, stepData, onFinish)
      local roomView = GM.ChapterManager:GetActiveRoomView()
      DelayExecuteFuncInView(function()
        roomView:MoveCamera(stepData.pos, stepData.scale, stepData.duration, onFinish)
      end, stepData.delayTime, roomView)
    end
  },
  [ETimelineAction.RoleAction] = {
    ExecuteFunc = function(timelineData, stepData, onFinish)
      local roomView = GM.ChapterManager:GetActiveRoomView()
      DelayExecuteFuncInView(function()
        roomView:PlayRoleAnimaion(stepData.animationName, stepData.pos, stepData.bReverse, stepData.waitTime, onFinish)
      end, stepData.delayTime, roomView)
    end
  },
  [ETimelineAction.ConstructionAudio] = {
    ExecuteFunc = function(timelineData, stepData, onFinish)
      local roomView = GM.ChapterManager:GetActiveRoomView()
      for _, audioData in ipairs(stepData.arrAudioData) do
        DelayExecuteFuncInView(function()
          GM.AudioModel:PlayEffect(audioData.audioName)
        end, audioData.delayTime, roomView)
      end
      if onFinish then
        onFinish()
      end
    end
  },
  [ETimelineAction.Cover] = {
    ExecuteFunc = function(timelineData, stepData, onFinish)
      local roomView = GM.ChapterManager:GetActiveRoomView()
      DelayExecuteFuncInView(function()
        roomView:CoverSlot(stepData.slotId)
        if onFinish then
          onFinish()
        end
      end, stepData.delayTime, roomView)
    end
  }
}
