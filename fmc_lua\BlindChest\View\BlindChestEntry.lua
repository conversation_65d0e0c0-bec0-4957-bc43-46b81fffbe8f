BlindChestEntry = setmetatable({}, HudGeneralButton)
BlindChestEntry.__index = BlindChestEntry

function BlindChestEntry:Init(model)
  self.m_model = model
  self.m_activityType = model:GetType()
  self.m_activityDefinition = BlindChestDefinition[self.m_activityType]
  EventDispatcher.AddListener(self.m_activityDefinition.KeyChangedEvent, self, self._UpdateRedDot)
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self._UpdateRedDot)
  self:UpdatePerSecond()
  self:_UpdateRedDot()
end

function BlindChestEntry:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BlindChestEntry:UpdatePerSecond()
  if self.m_model ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function BlindChestEntry:_UpdateRedDot()
  if not self.m_model or self.m_model:GetState() == ActivityState.Released then
    return
  end
  local keyCount = self.m_model:GetKeyCount()
  if keyCount == 0 then
    self.m_exclamationGo:SetActive(false)
  else
    self.m_exclamationGo:SetActive(true)
    self.m_exclamationText.text = keyCount
  end
end

function BlindChestEntry:OnBtnClicked()
  self.m_model:TryOpenView()
end
