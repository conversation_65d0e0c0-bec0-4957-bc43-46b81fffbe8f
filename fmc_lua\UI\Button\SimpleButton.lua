SimpleButton = {}
SimpleButton.__index = SimpleButton

function SimpleButton:Awake()
  self:SetEnabled(true)
end

function SimpleButton:Init(text, callback)
  if text ~= nil then
    self:SetText(text)
  end
  self.m_callback = callback
  self:SetEnabled(true)
end

function SimpleButton:OnBtnClicked()
  if not self.m_bEnabled then
    return
  end
  if string.len(self.m_strSoundEffectName) > 0 then
    GM.AudioModel:PlayEffect(self.m_strSoundEffectName)
  end
  if self.m_callback then
    self.m_callback()
  end
end

function SimpleButton:SetText(strText)
  self.m_text.text = strText
end

local enableTextColor = CSColor.white
local disableTextColor = CSColor.white

function SimpleButton:SetEnabled(enable)
  self:SetUIEnabled(enable)
  if self.m_bEnabled ~= enable then
    self.m_bEnabled = enable
    self.m_btn.enabled = self.m_bEnabled
  end
end

function SimpleButton:SetUIEnabled(enable)
  if self.m_bUIEnabled ~= enable and self.m_image then
    self.m_bUIEnabled = enable
    self.m_image.sprite = enable and self.m_enableSprite or self.m_disableSprite
    self.m_text.color = enable and enableTextColor or disableTextColor
  end
end
