SurpriseChestActivityDefinition = {
  [ActivityType.SurpriseChest] = {
    ActivityDataTableName = VirtualDBTableName.SurpriseChest,
    EntryButtonKey = ESceneViewHudButtonKey.SurpriseChest,
    StateChangedEvent = EEventType.SurpriseChestStateChanged,
    ChestDataStateChanged = EEventType.SurpriseChestDataStateChanged,
    SurpriseChestViewPrefabName = UIPrefabConfigName.SurpriseChestView,
    MainWindowPrefabName = UIPrefabConfigName.SurpriseChestMainWindow,
    HelpWindowPrefabName = UIPrefabConfigName.SurpriseChestHelpWindow,
    ResourceLabels = {
      AddressableLabel.SurpriseChest,
      AddressableLabel.SurpriseChestCommon
    }
  }
}
