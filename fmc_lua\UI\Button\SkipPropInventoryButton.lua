SkipPropInventoryButton = setmetatable({
  HitAudio = AudioFileConfigName.SfxSpeedCard
}, HudPropertyButton)
SkipPropInventoryButton.__index = SkipPropInventoryButton

function SkipPropInventoryButton:Awake()
  HudPropertyButton.Awake(self)
  HudPropertyButton.Init(self, EPropertyType.SkipProp)
end

function SkipPropInventoryButton:OnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.SkipPropHelpWindow)
end

function SkipPropInventoryButton:GetFlyTargetPosition()
  return self.transform.position
end
