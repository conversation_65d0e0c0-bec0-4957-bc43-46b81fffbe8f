ItemLockerEventType = {Unlock = 1, Tap = 2}
ItemLocker = setmetatable({}, BaseItemComponent)
ItemLocker.__index = ItemLocker

function ItemLocker.Create(startPosition, width, height, unlockDay)
  local itemLocker = setmetatable({}, ItemLocker)
  itemLocker:Init(startPosition, width, height, unlockDay)
  return itemLocker
end

function ItemLocker:Init(startPosition, width, height, unlockDay)
  self.startPosition = startPosition
  self.width = width
  self.height = height
  self.unlockDay = unlockDay
  EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self._CheckUnlock)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._CheckUnlock)
  self.event = PairEvent.Create(self)
end

function ItemLocker:SetItemModel(itemModel)
  if itemModel:GetPosition() == self.startPosition then
    BaseItemComponent.SetItemModel(self, itemModel)
    self.m_itemModel.locked = 1
    local boardModel = self.m_itemModel:GetBoardModel()
    boardModel:SaveItemProperty(self.m_itemModel)
    local items = self:GetCoveredItems()
    for _, item in ipairs(items) do
      if item ~= self.m_itemModel then
        item:AddComponent(self)
      end
    end
    self:_CheckUnlock()
  end
end

function ItemLocker:OnTap()
  self.event:Call(ItemLockerEventType.Tap)
end

function ItemLocker:_CheckUnlock()
  if not (GM.SceneManager:GetGameMode() == self.m_itemModel:GetBoardModel():GetGameMode() and GM.UIManager.allWindowClosed) or self.unlocked then
    return
  end
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local curDay = orderModel:GetCurOrderDay()
  if curDay < self.unlockDay then
    return
  end
  self.unlocked = true
  self.m_itemModel.locked = 0
  local boardModel = self.m_itemModel:GetBoardModel()
  boardModel:SaveItemProperty(self.m_itemModel)
  local itemPaperBoxType = ItemPaperBox
  local itemCobwebType = ItemCobweb
  local itemDataModel = GM.ItemDataModel
  local items = self:GetCoveredItems()
  for _, item in ipairs(items) do
    item:RemoveComponent(self)
    if not item:GetComponent(itemPaperBoxType) then
      if item:GetComponent(itemCobwebType) then
        itemDataModel:SetLocked(item:GetComponent(itemCobwebType):GetInnerItemCode())
      else
        itemDataModel:SetUnlocked(item:GetType())
      end
    end
  end
  self.event:Call(ItemLockerEventType.Unlock)
end

function ItemLocker:GetCoveredItems()
  local boardModel = self.m_itemModel:GetBoardModel()
  local array = {}
  for i = 0, self.width - 1 do
    for j = 0, self.height - 1 do
      local pos = self.startPosition + Vector2(i, j)
      local itemModel = boardModel:GetItem(pos)
      if itemModel then
        array[#array + 1] = itemModel
      else
        Log.Error("进度锁定区域下没有棋子，暂不支持如此配置！")
      end
    end
  end
  return array
end

function ItemLocker:HideItems()
  local items = self:GetCoveredItems()
  self.m_itemModel:GetBoardModel().event:Call(BoardEventType.HideItems, {Items = items})
end

function ItemLocker:ShowItems()
  local items = self:GetCoveredItems()
  self.m_itemModel:GetBoardModel().event:Call(BoardEventType.ShowItems, {Items = items})
end
