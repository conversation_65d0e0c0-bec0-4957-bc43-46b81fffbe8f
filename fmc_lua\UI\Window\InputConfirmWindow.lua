InputConfirmWindow = setmetatable({}, TwoButtonWindow)
InputConfirmWindow.__index = InputConfirmWindow

function InputConfirmWindow:Init(titleText, descText, confirmInputText, confirmButtonText, cancelButtonText, confirmCallback)
  TwoButtonWindow._InitWithText(self, titleText, descText, confirmButtonText, cancelButtonText, confirmCallback, nil, true)
  self.m_confirmText = confirmInputText
  self:_UpdateConfirmButton(false)
end

function InputConfirmWindow:OnInputEditing()
  self:_UpdateConfirmButton(self.m_inputField.text == self.m_confirmText)
end

function InputConfirmWindow:_UpdateConfirmButton(enable)
  self.m_redButtonTb:SetEnabled(enable)
end
