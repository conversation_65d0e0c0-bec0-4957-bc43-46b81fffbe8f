BundleChainWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BundleBaseWindow)
BundleChainWindow.__index = BundleChainWindow

function BundleChainWindow:UpdatePerSecond()
  if not self.m_bundleEndTime then
    self.m_bundleEndTime = self.m_model:GetBundleTriggerEndTime(self.m_dataGroup)
  end
  local restDuration = self.m_bundleEndTime - GM.GameModel:GetServerTime()
  if self.m_countDownText then
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(restDuration, 2, false, false)
  end
  if restDuration <= 0 then
    UIUtil.SetActive(self.m_countDownGo, false)
    return
  end
end

function BundleChainWindow:OnBundleDataRefreshed(msg)
  if self.m_model ~= nil and self.m_dataGroup ~= nil and self.m_model:HasPurchaseFinished(self.m_dataGroup) then
    self:Close()
    return
  end
  BundleBaseWindow.OnBundleDataRefreshed(self, msg)
end

function BundleChainWindow:OnDestroy()
  BundleBaseWindow.OnDestroy(self)
  if self.m_seq ~= nil then
    self.m_seq:Kill()
    self.m_seq = nil
  end
  if self.m_bEventLocked then
    GM.UIManager:SetEventLock(false)
    self.m_bEventLocked = false
  end
end

function BundleChainWindow:Init(...)
  BundleBaseWindow.Init(self, ...)
  self.m_model = GM.BundleManager:GetModel(self.m_dataGroup:GetBundleType())
  local uiType = self.m_dataGroup:GetBundleUIType()
  self.m_uiStyle = BundleUIType[uiType]
  UIUtil.SetActive(self.m_cellOrigin)
  self.m_mountNodes = {}
  self.m_mountNodesOriginChildCount = {}
  self.m_cells = {}
  local cell
  self.m_lastIndex = self.m_model:GetCurChainStep(self.m_dataGroup)
  for i = 1, 6 do
    self.m_mountNodes[i] = self["m_mountNode" .. i]
    self.m_mountNodesOriginChildCount[i] = self.m_mountNodes[i].childCount
    cell = GameObject.Instantiate(self.m_cellOrigin, self.m_mountNodes[i]):GetLuaTable()
    local curData = self.m_dataGroup:GetConfigDataByIndex(self.m_lastIndex)
    self.m_mountNodes[i].gameObject:SetActive(curData)
    if curData ~= nil then
      cell.gameObject:SetActive(true)
      cell:Init(self, self.m_dataGroup, self.m_lastIndex, i == 1)
    end
    self.m_cells[i] = cell
    self.m_lastIndex = self.m_lastIndex + 1
  end
  self.m_mountNodes[0] = self.m_mountNode7
  self:UpdatePerSecond()
  self:PlayEnterAnim()
  EventDispatcher.AddListener(EEventType.BundleIAPRestoreSuccess, self, self.OnBundleIAPRestoreSuccess)
end

function BundleChainWindow:OnBundleIAPRestoreSuccess(groupId)
  if self.m_dataGroup ~= nil and self.m_dataGroup:GetGroupId() == groupId then
    self:Close()
  end
end

function BundleChainWindow:PlayEnterAnim()
  if self.m_titleSpine ~= nil then
    self.m_titleSpine:Initialize()
    self.m_titleSpine.AnimationState:SetAnimation(0, "appear", false)
    self.m_titleSpine.AnimationState:AddAnimation(0, "idle", true, 0)
  end
end

function BundleChainWindow:GetPurchaseIds()
  return self.m_model:GetCurChainStep(self.m_dataGroup)
end

function BundleChainWindow:PlayNextStepAnimation()
  for i = 1, 6 do
    self.m_cells[i].transform:SetParent(self.m_mountNodes[i - 1], true)
  end
  local firstCell = table.remove(self.m_cells, 1)
  table.insert(self.m_cells, firstCell)
  GM.UIManager:SetEventLock(true)
  self.m_bEventLocked = true
  local sequence = DOTween.Sequence()
  sequence:Insert(0, firstCell.transform:DOScale(0, 0.4))
  sequence:InsertCallback(0.45, function()
    local curData = self.m_dataGroup:GetConfigDataByIndex(self.m_lastIndex)
    if curData == nil then
      UIUtil.SetActive(firstCell.gameObject, false)
      self.m_lastIndex = self.m_lastIndex + 1
      return
    end
    firstCell:UpdateContent(curData, self.m_uiStyle, false)
    firstCell:UpdateStep(self.m_lastIndex)
    firstCell:GetLockGo().transform.localScale = V3One
    self.m_lastIndex = self.m_lastIndex + 1
    firstCell.transform.localPosition = V3Zero
    firstCell.transform:SetParent(self.m_mountNodes[6], true)
  end)
  local playMountNodeAnimation = function(index)
    local childCount = self.m_mountNodes[index].childCount
    local duration0 = index == 6 and (index - 1) * 0.13 or (index - 1) * 0.15
    local timeDelta = 0.45 / (childCount + 2)
    local pIndex = 0
    for ind = 0, childCount - 1 do
      if StringUtil.StartWith(self.m_mountNodes[index]:GetChild(ind).gameObject.name, "p") then
        pIndex = pIndex + 1
        DelayExecuteFuncInView(function()
          if not self.m_cells[index].gameObject.activeInHierarchy or self.m_mountNodes[index].childCount == self.m_mountNodesOriginChildCount[index] then
            local childP = self.m_mountNodes[index]:GetChild(ind).gameObject
            UIUtil.SetActive(childP, false)
          end
        end, duration0 + pIndex * timeDelta, self)
      end
    end
  end
  for i = 1, 6 do
    local transform = self.m_cells[i].transform
    sequence:Insert(0.2 + (i - 1) * 0.15, transform:DOLocalMove(V3Zero, 0.65):SetEase(Ease.OutQuart))
    playMountNodeAnimation(i)
    if i == 1 then
      local transform = self.m_cells[i]:GetLockGo().transform
      sequence:Insert(0.6, transform:DOShakeAnchorPos(0.5, 4, 20, 90, true))
      sequence:Insert(1.1, transform:DOLocalMoveY(transform.localPosition.y + 20, 0.05):SetEase(Ease.OutQuart))
      sequence:Insert(1.15, transform:DOLocalMoveY(transform.localPosition.y - 1000, 0.45):SetEase(Ease.InQuart))
      sequence:InsertCallback(1.6, function()
        self.m_cells[i]:SetUnlocked()
      end)
    end
    if i == 6 then
      sequence:Insert(0.2 + (i - 1) * 0.15, transform:DOScale(1, 0.65):SetEase(Ease.OutQuart))
    end
  end
  sequence:OnComplete(function()
    GM.UIManager:SetEventLock(false)
    self.m_bEventLocked = false
    self.m_seq = nil
  end)
  self.m_seq = sequence
end

function BundleChainWindow:OnCloseView()
  BundleBaseWindow.OnCloseView(self)
  if self.m_dataGroup ~= nil and self.m_model ~= nil and self.m_dataGroup:IsDisposable() then
    self.m_model:TryRecoverFreeRewards(self.m_dataGroup, true)
  end
end

BundleChainCell = {}
BundleChainCell.__index = BundleChainCell

function BundleChainCell:Init(window, dataGroup, index, bUnlocked)
  self.m_window = window
  self.m_model = GM.BundleManager:GetModel(dataGroup:GetBundleType())
  self.m_dataGroup = dataGroup
  self.m_step = index
  self.m_uiStyle = BundleUIType[self.m_dataGroup:GetBundleUIType()]
  self.m_lockPosition = self.m_lockGo.transform.localPosition
  self:UpdateContent(self.m_dataGroup:GetConfigDataByIndex(index), bUnlocked)
end

function BundleChainCell:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BundleChainCell:UpdateStep(index)
  self.m_step = index
end

function BundleChainCell:UpdateContent(data, bUnlocked)
  self.m_data = data
  self:_UpdateRewards()
  local skin = self.m_data:GetSkin()
  self.m_bgImg.sprite = self["m_bgSprite" .. skin]
  if not self.m_ignoreSetNativeSize then
    self.m_bgImg:SetNativeSize()
  end
  local skinGoName
  for i = 1, 3 do
    skinGoName = "m_skinGo" .. i
    if self[skinGoName] ~= nil then
      UIUtil.SetActive(self[skinGoName], tostring(i) == skin)
    end
  end
  local isFree = self.m_data:GetPurchaseId() == nil
  UIUtil.SetActive(self.m_freeButtonGo, isFree)
  UIUtil.SetActive(self.m_IAPButtonGo, not isFree)
  if not isFree then
    self.m_IAPText.text = GM.InAppPurchaseModel:GetLocalizedPrice(self.m_data:GetPurchaseId())
  end
  self.m_bUnlocked = bUnlocked
  self:UpdateLockState()
end

function BundleChainCell:_UpdateRewards()
  self.m_rewardContent:Init(self.m_data:GetGoods())
end

function BundleChainCell:SetUnlocked()
  self.m_bUnlocked = true
  self:UpdateLockState()
end

function BundleChainCell:UpdateLockState()
  local unlock = self:IsInCurStep()
  UIUtil.SetActive(self.m_lockGo, not unlock)
  self.m_lockGo.transform.localPosition = self.m_lockPosition
end

function BundleChainCell:IsInCurStep()
  return self.m_bUnlocked and self.m_model:GetCurChainStep(self.m_dataGroup) == self:GetStep()
end

function BundleChainCell:GetStep()
  return self.m_step
end

function BundleChainCell:OnBtnClicked()
  if self:IsInCurStep() then
    self.m_model:BuyBundle(self.m_dataGroup, function(rewards)
      if self.m_data:GetPurchaseId() == nil then
        self.m_rewardContent:PlayRewardAnimation()
        self.m_window:PlayNextStepAnimation()
      else
        GM.UIManager:OpenView(UIPrefabConfigName.BundleRewardWindow, rewards, self.m_uiStyle.titleText, true, nil, function()
          if self.gameObject:IsNull() or self.m_window.gameObject:IsNull() then
            return
          end
          self.m_window:PlayNextStepAnimation()
        end)
      end
    end)
  else
    GM.UIManager:ShowPromptWithKey("chain_bundle_unlock_note")
  end
end

function BundleChainCell:GetLockGo()
  return self.m_lockGo
end
