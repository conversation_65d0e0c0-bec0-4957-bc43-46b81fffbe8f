CacheItemType = {Stack = 2, ExtraBoard = 3}
ItemCacheModel = {}
ItemCacheModel.__index = ItemCacheModel
local DBColumnCacheCode = "codeStr"
local DBColumnCacheType = "type"
local DBColumnCost = "cost"

function ItemCacheModel.Create(dbTable, idGenerator)
  local itemCacheModel = setmetatable({}, ItemCacheModel)
  itemCacheModel:Init(dbTable, idGenerator)
  return itemCacheModel
end

function ItemCacheModel:Init(dbTable, idGenerator)
  self.m_dbTable = dbTable
  self.m_idGenerator = idGenerator
end

function ItemCacheModel:OnSyncDataFinished()
  self:ResetVar()
  for id, data in pairs(self.m_dbTable:GetValues()) do
    local itemCode = data[DBColumnCacheCode]
    if GM.ItemDataModel:GetModelConfig(itemCode, true) then
      table.insert(self.m_itemIdList, id)
      self.m_mapCodeCount[itemCode] = (self.m_mapCodeCount[itemCode] or 0) + 1
    else
      local removed = GM.ItemTypeDeleteModel:OnItemRemoved(itemCode)
      if not removed then
        GM.BIManager:LogProject(EBIType.ItemNotFound, "ICM itemId:" .. tostring(itemCode))
      end
      self.m_dbTable:Remove(id)
    end
  end
  self:_SortItemIdList()
end

function ItemCacheModel:ResetVar()
  self.m_itemIdList = {}
  self.m_mapCodeCount = {}
end

function ItemCacheModel:PushItems(cachedItemCodes, type, costList)
  if costList ~= nil then
    Log.Assert(#cachedItemCodes == #costList, "cachedItemCodes 长度与 costList 长度不符")
  else
    costList = {}
  end
  for index, code in ipairs(cachedItemCodes) do
    local id = self.m_idGenerator:Generate()
    type = type or CacheItemType.Stack
    self.m_dbTable:Set(id, DBColumnCacheCode, code)
    self.m_dbTable:Set(id, DBColumnCacheType, type)
    self.m_dbTable:Set(id, DBColumnCost, StringUtil.Replace(json.encode(costList[index] or {}), ",", "@"))
    table.insert(self.m_itemIdList, id)
    self.m_mapCodeCount[code] = (self.m_mapCodeCount[code] or 0) + 1
  end
  self:_SortItemIdList()
end

function ItemCacheModel:PopItem()
  if self:GetItemCount() ~= 0 then
    local code, cost = self:GetItem(1)
    self:RemoveItem(1)
    return code, cost
  end
  return nil
end

function ItemCacheModel:RemoveItem(index)
  local id = table.remove(self.m_itemIdList, index)
  local code = self.m_dbTable:GetValue(id, DBColumnCacheCode)
  self.m_dbTable:Remove(id)
  local typeCount = self.m_mapCodeCount[code] or 0
  if 0 < typeCount then
    self.m_mapCodeCount[code] = typeCount - 1
  else
    Log.Error("缓存队列逻辑错误")
  end
end

function ItemCacheModel:RemoveItemByCode(code)
  if self:GetCountByCode(code) < 0 then
    return false
  end
  local index = self:_GetOneIndexByCode(code)
  if not index then
    return false
  end
  self:RemoveItem(index)
  return true
end

function ItemCacheModel:GetItem(index)
  local id = self.m_itemIdList[index]
  if id then
    local code = self.m_dbTable:GetValue(id, DBColumnCacheCode)
    local cost = self.m_dbTable:GetValue(id, DBColumnCost)
    cost = json.decode(StringUtil.Replace(cost, "@", ","))
    if IsNil(cost) then
      cost = nil
    end
    return code, cost
  end
  return nil
end

function ItemCacheModel:_GetOneIndexByCode(itemCode)
  local id, code
  for i = 1, self:GetItemCount() do
    id = self.m_itemIdList[i]
    code = self:GetItem(i)
    if code == itemCode then
      return i
    end
  end
  return nil
end

function ItemCacheModel:SetCurDayEnergyCostToPastDay()
  local id, code, cost
  for i = 1, self:GetItemCount() do
    id = self.m_itemIdList[i]
    code, cost = self:GetItem(i)
    if cost and cost.costEnergyCurDay and cost.costEnergyCurDay > 0 then
      cost.costEnergy = (cost.costEnergy or 0) + cost.costEnergyCurDay
      cost.costEnergyCurDay = 0
      self.m_dbTable:Set(id, DBColumnCost, StringUtil.Replace(json.encode(cost), ",", "@"))
    end
  end
end

function ItemCacheModel:GetItemCount()
  if self.m_dbTable:IsEmpty() and #self.m_itemIdList > 0 then
    self:ResetVar()
  end
  return #self.m_itemIdList
end

function ItemCacheModel:_SortItemIdList()
  local eTypeStack = CacheItemType.Stack
  local funcStartWith = StringUtil.StartWith
  local itemDataModel = GM.ItemDataModel
  local bCachePinEqPdToTop = GM.ConfigModel:CachePinEqPdToTop()
  local itemIdComparer = function(a, b)
    local typeA = self.m_dbTable:GetValue(a, DBColumnCacheType) or eTypeStack
    local typeB = self.m_dbTable:GetValue(b, DBColumnCacheType) or eTypeStack
    if typeA ~= typeB then
      return typeA > typeB
    end
    local codeA = self.m_dbTable:GetValue(a, DBColumnCacheCode) or ""
    local codeB = self.m_dbTable:GetValue(b, DBColumnCacheCode) or ""
    local isSA = RewardApi.IsImmediateEffectItem(codeA, true)
    local isSB = RewardApi.IsImmediateEffectItem(codeB, true)
    if isSA and not isSB then
      return true
    elseif isSB and not isSA then
      return false
    end
    if bCachePinEqPdToTop then
      local eqOrPdA = funcStartWith(codeA, "eq_") or funcStartWith(codeA, "pd_")
      local eqOrPdB = funcStartWith(codeB, "eq_") or funcStartWith(codeB, "pd_")
      if eqOrPdA and not eqOrPdB then
        return true
      elseif eqOrPdB and not eqOrPdA then
        return false
      end
    end
    return b < a
  end
  table.sort(self.m_itemIdList, itemIdComparer)
end

function ItemCacheModel:TryPinOneItemCodeToTop(itemCode)
  if self:GetCountByCode(itemCode) <= 0 then
    return
  end
  local index = self:_GetOneIndexByCode(itemCode)
  if not index then
    return
  end
  local id = self.m_itemIdList[index]
  table.remove(self.m_itemIdList, index)
  table.insert(self.m_itemIdList, 1, id)
end

function ItemCacheModel:GetCountByCode(itemCode)
  return self.m_mapCodeCount[itemCode] or 0
end
