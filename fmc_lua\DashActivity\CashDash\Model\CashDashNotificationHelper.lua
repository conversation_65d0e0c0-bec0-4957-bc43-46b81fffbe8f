CashDashNotificationHelper = setmetatable({}, DashActivityNotificationHelper)
CashDashNotificationHelper.__index = CashDashNotificationHelper

function CashDashNotificationHelper.IsSceneExist(strScene)
  if strScene == NotificationScene.CashDashStart or strScene == NotificationScene.CashDashEnd then
    return true
  end
  return false
end

function CashDashNotificationHelper.Generate(strScene)
  local results = {}
  local model = GM.ActivityManager:GetModel(ActivityType.CashDash)
  local state = model:GetState()
  local strTileKey, strDescKey = GM.NotificationModel:GetTextTileAndDesc(strScene)
  if state == ActivityState.Preparing and strScene == NotificationScene.CashDashStart then
    strTileKey = strTileKey ~= "" and strTileKey or "push_cash_dash_open_title"
    strDescKey = strDescKey ~= "" and strDescKey or "push_cash_dash_open_desc"
    DashActivityNotificationHelper._GenerateDashStartNotification(results, model, NotificationType.CashDash, strTileKey, strDescKey)
  elseif state == ActivityState.Started and strScene == NotificationScene.CashDashEnd then
    strTileKey = strTileKey ~= "" and strTileKey or "push_cash_dash_end_title"
    strDescKey = strDescKey ~= "" and strDescKey or "push_cash_dash_end_desc"
    DashActivityNotificationHelper._GenerateDashEndNotification(results, model, NotificationType.CashDash, strTileKey, strDescKey, 10800)
  end
  return results
end
