return {
  {
    id = 1,
    itemCode = "additem_1",
    leftCount = 5,
    costType = "gem",
    costCount = 2,
    multiplier = 2,
    maxCostCount = 10
  },
  {
    id = 2,
    itemCode = "enebox_1",
    leftCount = 1,
    costType = "gem",
    costCount = 20
  },
  {
    id = 3,
    itemCode = "scissors_1",
    leftCount = 1,
    costType = "gem",
    costCount = 100
  },
  {
    id = 4,
    itemCode = "freebox_1",
    leftCount = 1,
    costType = "gem",
    costCount = 0
  }
}
