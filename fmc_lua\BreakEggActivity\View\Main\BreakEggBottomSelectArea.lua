BreakEggBottomSelectArea = setmetatable({}, BreakEggBaseArea)
BreakEggBottomSelectArea.__index = BreakEggBottomSelectArea

function BreakEggBottomSelectArea:Init()
  BreakEggBaseArea.Init(self)
  self.m_rewardArea:Init()
end

function BreakEggBottomSelectArea:UpdateContent()
  local active = self.m_activityModel:GetBreakEggState() ~= BreakEggState.NoStart
  self.gameObject:SetActive(active)
  if not active then
    return
  end
  self.m_rewardArea:UpdateContent()
  local showCollectButton = self.m_activityModel:GetCurrentStep() ~= 1
  self.m_collectButtonGo:SetActive(showCollectButton)
end

function BreakEggBottomSelectArea:DisplayFirstStep()
  self.m_collectButtonGo:SetActive(false)
  self.m_rewardArea:UpdateContent()
  self:FadeIn()
end

function BreakEggBottomSelectArea:DisplayStart()
  self.m_collectButtonGo:SetActive(true)
  self.m_rewardArea:UpdateContent()
  self:FadeOut()
end

function BreakEggBottomSelectArea:DisplayNewStep(rewardData)
  self.m_collectButtonGo:SetActive(true)
  self.m_rewardArea:AddRewardData(rewardData)
end

function BreakEggBottomSelectArea:OnCollectButtonClicked()
  local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BreakEggMainWindow)
  mainWindow:OnCollectButtonClicked()
end

function BreakEggBottomSelectArea:GetRewardTargetPosition()
  return self.m_rewardTargetNode.position
end
