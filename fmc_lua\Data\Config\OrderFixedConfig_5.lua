return {
  {
    Id = "50010",
    GroupId = 1,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_1_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "50020",
    GroupId = 1,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_2", Count = 1},
    Requirement_2 = {Type = "ds_fd_8", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50030",
    GroupId = 1,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    }
  },
  {
    Id = "50040",
    GroupId = 1,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_19", Count = 1}
  },
  {
    Id = "50050",
    GroupId = 1,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50060",
    GroupId = 1,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_mixdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "50070",
    GroupId = 1,
    ChapterId = 5,
    Requirement_1 = {Type = "it_5_2_7", Count = 1},
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "50080",
    GroupId = 2,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "50090",
    GroupId = 2,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50100",
    GroupId = 2,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_mixdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "50110",
    GroupId = 2,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "50120",
    GroupId = 2,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50130",
    GroupId = 2,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_13", Count = 1}
  },
  {
    Id = "50140",
    GroupId = 2,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "50150",
    GroupId = 3,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50160",
    GroupId = 3,
    ChapterId = 5,
    Requirement_1 = {Type = "it_1_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "50170",
    GroupId = 3,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_5", Count = 1}
  },
  {
    Id = "50180",
    GroupId = 3,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_mixdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "50190",
    GroupId = 3,
    ChapterId = 5,
    Requirement_1 = {Type = "it_5_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50200",
    GroupId = 3,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_3_5", Count = 1},
    Requirement_2 = {
      Type = "ds_e1icytre_2",
      Count = 1
    }
  },
  {
    Id = "50210",
    GroupId = 3,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "50220",
    GroupId = 4,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "50230",
    GroupId = 4,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_fd_12", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    }
  },
  {
    Id = "50240",
    GroupId = 4,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50250",
    GroupId = 4,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1},
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "50260",
    GroupId = 4,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    }
  },
  {
    Id = "50270",
    GroupId = 4,
    ChapterId = 5,
    Requirement_1 = {Type = "it_5_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50280",
    GroupId = 4,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_e3juice_11",
      Count = 1
    }
  },
  {
    Id = "50290",
    GroupId = 5,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "50300",
    GroupId = 5,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_friedve_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_10",
      Count = 1
    }
  },
  {
    Id = "50310",
    GroupId = 5,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_fd_15", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50320",
    GroupId = 5,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_10",
      Count = 1
    }
  },
  {
    Id = "50330",
    GroupId = 5,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "50340",
    GroupId = 5,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_fd_16", Count = 1},
    Requirement_2 = {Type = "it_2_2_4", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50350",
    GroupId = 5,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillve_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e6stewmt_1",
      Count = 1
    }
  },
  {
    Id = "50360",
    GroupId = 6,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_fd_19", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_6",
      Count = 1
    }
  },
  {
    Id = "50370",
    GroupId = 6,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_1",
      Count = 1
    }
  },
  {
    Id = "50380",
    GroupId = 6,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50390",
    GroupId = 6,
    ChapterId = 5,
    Requirement_1 = {Type = "it_5_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_9",
      Count = 1
    }
  },
  {
    Id = "50400",
    GroupId = 6,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_5", Count = 1}
  },
  {
    Id = "50410",
    GroupId = 6,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_fd_11", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50420",
    GroupId = 6,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "50430",
    GroupId = 7,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "50440",
    GroupId = 7,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_e4sf_12", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_12",
      Count = 1
    }
  },
  {
    Id = "50450",
    GroupId = 7,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50460",
    GroupId = 7,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1icytre_2",
      Count = 1
    }
  },
  {
    Id = "50470",
    GroupId = 7,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {Type = "it_7_2_6", Count = 1}
  },
  {
    Id = "50480",
    GroupId = 7,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50490",
    GroupId = 7,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_8",
      Count = 1
    }
  },
  {
    Id = "50500",
    GroupId = 8,
    ChapterId = 5,
    Requirement_1 = {Type = "it_4_2_4", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "50510",
    GroupId = 8,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_10",
      Count = 1
    }
  },
  {
    Id = "50520",
    GroupId = 8,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50530",
    GroupId = 8,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_fd_8", Count = 1},
    Requirement_2 = {Type = "it_7_2_6", Count = 1}
  },
  {
    Id = "50540",
    GroupId = 8,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50550",
    GroupId = 8,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_e6soup_1",
      Count = 1
    }
  },
  {
    Id = "50560",
    GroupId = 8,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_13",
      Count = 1
    }
  },
  {
    Id = "50570",
    GroupId = 9,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "50580",
    GroupId = 9,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    }
  },
  {
    Id = "50590",
    GroupId = 9,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_12", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50600",
    GroupId = 9,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_4", Count = 1}
  },
  {
    Id = "50610",
    GroupId = 9,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_12",
      Count = 1
    }
  },
  {
    Id = "50620",
    GroupId = 9,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50630",
    GroupId = 9,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "50640",
    GroupId = 10,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "50650",
    GroupId = 10,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "50660",
    GroupId = 10,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {Type = "it_5_2_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50670",
    GroupId = 10,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_4", Count = 1}
  },
  {
    Id = "50680",
    GroupId = 10,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_3", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50690",
    GroupId = 10,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "50700",
    GroupId = 10,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "ds_e5mt_1", Count = 1}
  },
  {
    Id = "50710",
    GroupId = 11,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "50720",
    GroupId = 11,
    ChapterId = 5,
    Requirement_1 = {Type = "it_5_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50730",
    GroupId = 11,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "50740",
    GroupId = 11,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50750",
    GroupId = 11,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_14",
      Count = 1
    }
  },
  {
    Id = "50760",
    GroupId = 11,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "50770",
    GroupId = 11,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1icytre_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_4",
      Count = 1
    }
  },
  {
    Id = "50780",
    GroupId = 12,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "it_2_1_6", Count = 1}
  },
  {
    Id = "50790",
    GroupId = 12,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50800",
    GroupId = 12,
    ChapterId = 5,
    Requirement_1 = {Type = "it_1_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    }
  },
  {
    Id = "50810",
    GroupId = 12,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    }
  },
  {
    Id = "50820",
    GroupId = 12,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50830",
    GroupId = 12,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "50840",
    GroupId = 12,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_19",
      Count = 1
    }
  },
  {
    Id = "50850",
    GroupId = 13,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    }
  },
  {
    Id = "50860",
    GroupId = 13,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50870",
    GroupId = 13,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillve_4",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_1_6", Count = 1}
  },
  {
    Id = "50880",
    GroupId = 13,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillmt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "50890",
    GroupId = 13,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillve_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_5_2_7", Count = 1}
  },
  {
    Id = "50900",
    GroupId = 13,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_15", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50910",
    GroupId = 13,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_4",
      Count = 1
    }
  },
  {
    Id = "50920",
    GroupId = 14,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_4",
      Count = 1
    }
  },
  {
    Id = "50930",
    GroupId = 14,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "ds_sal_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50940",
    GroupId = 14,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_5",
      Count = 1
    }
  },
  {
    Id = "50950",
    GroupId = 14,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_fd_9", Count = 1},
    Requirement_2 = {Type = "it_7_1_6", Count = 1}
  },
  {
    Id = "50960",
    GroupId = 14,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {Type = "it_5_2_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "50970",
    GroupId = 14,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillve_4",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "50980",
    GroupId = 14,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "ds_e6dst_2", Count = 1}
  },
  {
    Id = "50990",
    GroupId = 15,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "51000",
    GroupId = 15,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfru_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51010",
    GroupId = 15,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_e6cockt_7",
      Count = 1
    }
  },
  {
    Id = "51020",
    GroupId = 15,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_fd_12", Count = 1},
    Requirement_2 = {Type = "ds_juice_9", Count = 1}
  },
  {
    Id = "51030",
    GroupId = 15,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "51040",
    GroupId = 15,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51050",
    GroupId = 15,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_e1hotdrk_2",
      Count = 1
    }
  },
  {
    Id = "51060",
    GroupId = 16,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "51070",
    GroupId = 16,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_5", Count = 1}
  },
  {
    Id = "51080",
    GroupId = 16,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {Type = "it_2_1_8", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51090",
    GroupId = 16,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "51100",
    GroupId = 16,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "51110",
    GroupId = 16,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_16", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51120",
    GroupId = 16,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillve_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_16",
      Count = 1
    }
  },
  {
    Id = "51130",
    GroupId = 17,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "51140",
    GroupId = 17,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51150",
    GroupId = 17,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_12", Count = 1}
  },
  {
    Id = "51160",
    GroupId = 17,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_fd_10", Count = 1}
  },
  {
    Id = "51170",
    GroupId = 17,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {Type = "it_5_2_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51180",
    GroupId = 17,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_7",
      Count = 1
    }
  },
  {
    Id = "51190",
    GroupId = 17,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "51200",
    GroupId = 18,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "51210",
    GroupId = 18,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillve_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_6", Count = 1}
  },
  {
    Id = "51220",
    GroupId = 18,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillmt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51230",
    GroupId = 18,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_7", Count = 1}
  },
  {
    Id = "51240",
    GroupId = 18,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_fd_21", Count = 1},
    Requirement_2 = {Type = "it_7_2_3", Count = 1}
  },
  {
    Id = "51250",
    GroupId = 18,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_2_3", Count = 1},
    Requirement_2 = {Type = "it_4_2_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51260",
    GroupId = 18,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_20",
      Count = 1
    }
  },
  {
    Id = "51270",
    GroupId = 19,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    }
  },
  {
    Id = "51280",
    GroupId = 19,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "51290",
    GroupId = 19,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_e1icytre_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51300",
    GroupId = 19,
    ChapterId = 5,
    Requirement_1 = {Type = "it_5_2_7", Count = 1},
    Requirement_2 = {Type = "it_7_2_6", Count = 1}
  },
  {
    Id = "51310",
    GroupId = 19,
    ChapterId = 5,
    Requirement_1 = {Type = "it_4_2_4", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51320",
    GroupId = 19,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    }
  },
  {
    Id = "51330",
    GroupId = 19,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_14", Count = 1}
  },
  {
    Id = "51340",
    GroupId = 20,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "it_7_2_3", Count = 1}
  },
  {
    Id = "51350",
    GroupId = 20,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_friedve_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51360",
    GroupId = 20,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "51370",
    GroupId = 20,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "51380",
    GroupId = 20,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_e6soup_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51390",
    GroupId = 20,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_fd_15", Count = 1},
    Requirement_2 = {Type = "it_2_2_4", Count = 1}
  },
  {
    Id = "51400",
    GroupId = 20,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_15",
      Count = 1
    }
  },
  {
    Id = "51410",
    GroupId = 21,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_5", Count = 1}
  },
  {
    Id = "51420",
    GroupId = 21,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51430",
    GroupId = 21,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_15", Count = 1}
  },
  {
    Id = "51440",
    GroupId = 21,
    ChapterId = 5,
    Requirement_1 = {Type = "it_4_2_2", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_18",
      Count = 1
    }
  },
  {
    Id = "51450",
    GroupId = 21,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_chopve_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51460",
    GroupId = 21,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "51470",
    GroupId = 21,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_17",
      Count = 1
    }
  },
  {
    Id = "51480",
    GroupId = 22,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_5", Count = 1}
  },
  {
    Id = "51490",
    GroupId = 22,
    ChapterId = 5,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51500",
    GroupId = 22,
    ChapterId = 5,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "51510",
    GroupId = 22,
    ChapterId = 5,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "51520",
    GroupId = 22,
    ChapterId = 5,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "51530",
    GroupId = 22,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "51540",
    GroupId = 22,
    ChapterId = 5,
    Requirement_1 = {Type = "it_7_2_3", Count = 1},
    Requirement_2 = {Type = "ds_e6sf_16", Count = 1}
  }
}
