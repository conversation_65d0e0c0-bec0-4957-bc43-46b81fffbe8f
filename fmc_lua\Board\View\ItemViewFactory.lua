ItemViewFactory = {}
ItemViewFactory.__index = ItemViewFactory
local componentView = {
  ItemSpread = "m_itemSpreadViewPrefab",
  ItemAccelerate = "m_itemAccelerateViewPrefab",
  ItemAccelerateTime = "m_itemAccelerateTimeViewPrefab",
  ItemCook = "m_itemCookViewPrefab",
  ItemSplit = "m_itemSplitViewPrefab",
  ItemChoose = "m_itemChooseViewPrefab",
  ItemRewardBubble = "m_itemRewardBubbleViewPrefab"
}

function ItemViewFactory:Create(parent, itemModel, inBoard, onSpineLoaded)
  local itemObject = Object.Instantiate(self.m_itemViewPrefab, parent)
  local itemView = itemObject:GetLuaTable()
  itemView:Init(itemModel, inBoard, onSpineLoaded)
  local rootTrans = itemView:GetRootTrans()
  if itemModel:GetComponent(ItemCobweb) ~= nil then
    Object.Instantiate(self.m_itemCobwebViewPrefab, rootTrans)
  end
  local itemTransform = itemModel:GetComponent(ItemTransform)
  if itemTransform ~= nil and itemTransform:GetDuration() ~= nil then
    local itemTransformObject = Object.Instantiate(self.m_itemTransformViewPrefab, rootTrans)
    local itemTransformView = itemTransformObject:GetLuaTable()
    itemTransformView:Init(itemTransform)
    itemView:AddComponent(itemTransformView)
  end
  for k, v in pairs(componentView) do
    local component = itemModel:GetComponent(_ENV[k])
    if component ~= nil then
      local object = Object.Instantiate(self[v], rootTrans)
      local view = object:GetLuaTable()
      if view then
        view:Init(component)
        itemView:AddComponent(view)
      end
    end
  end
  return itemView
end

function ItemViewFactory:CreateItemLockerCmp(boardView, itemModel, itemView)
  local lockerCmp = itemModel:GetComponent(ItemLocker)
  local prefabName = "m_itemLockerViewPrefab" .. lockerCmp.width .. "X" .. lockerCmp.height
  local prefabNameWithLevel = prefabName .. "_" .. lockerCmp.unlockDay
  if self[prefabNameWithLevel] ~= nil then
    prefabName = prefabNameWithLevel
  end
  if self[prefabName] == nil then
    Log.Error("进度锁定棋子没有相关的prefab！" .. prefabName)
  else
    local object = Object.Instantiate(self[prefabName], boardView:GetItemsTransform())
    local view = object:GetLuaTable()
    view:Init(lockerCmp, boardView:GetCamera())
    itemView:AddComponent(view)
  end
end
