ExtraBoardActivityBoardView = setmetatable({}, BaseUIBoardView)
ExtraBoardActivityBoardView.__index = ExtraBoardActivityBoardView

function ExtraBoardActivityBoardView:Init(boardModel, itemDeleteButton, itemTipButton)
  self.m_activityDefinition = ExtraBoardActivityDefinition[boardModel:GetActivityType()]
  self.m_cbModelViewMap = {}
  BaseUIBoardView.Init(self, boardModel, itemDeleteButton, itemTipButton)
  self:_UpdateBoardBg()
  REGISTER_BOARD_EVENT_HANDLER(self, "MergeCobwebItem")
end

function ExtraBoardActivityBoardView:OnDestroy()
  BaseUIBoardView.OnDestroy(self)
  Scheduler.UnscheduleTarget(self)
end

function ExtraBoardActivityBoardView:ConvertWorldPositionToScreenPosition(position)
  return ExtraBoardActivityBoardContainer.GetInstance():ConvertWorldPositionToScreenPosition(position)
end

function ExtraBoardActivityBoardView:ConvertBoardPositionToScreenPosition(boardPosition)
  local cbItem = self.m_model:GetCobwebItemByPosition(boardPosition)
  local worldPosition
  if cbItem == nil then
    local plottingPosition = boardPosition:ToLocalPosition()
    worldPosition = self:GetItemsTransform():TransformPoint(Vector3(plottingPosition.x + BaseBoardModel.TileSize / 2, plottingPosition.y + BaseBoardModel.TileSize / 2, 0))
  else
    worldPosition = self.m_cbModelViewMap[cbItem].transform.position
  end
  return self:ConvertWorldPositionToScreenPosition(worldPosition)
end

function ExtraBoardActivityBoardView:ConvertScreenPositionToWorldPosition(position)
  return ExtraBoardActivityBoardContainer.GetInstance():ConvertScreenPositionToWorldPosition(position)
end

function ExtraBoardActivityBoardView:_GetBoardWindow()
  return GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
end

function ExtraBoardActivityBoardView:_UpdateTile(...)
  if self.m_activityDefinition.TileImageName1 ~= nil and self.m_activityDefinition.TileImageName2 ~= nil then
    BaseUIBoardView._UpdateTile(self, self.m_activityDefinition.TileImageName1, self.m_activityDefinition.TileImageName2)
  else
    BaseUIBoardView._UpdateTile(self, ...)
  end
end

function ExtraBoardActivityBoardView:_UpdateBoardBg()
  if self.m_activityDefinition.BoardBgImageName ~= nil then
    local offset = Vector2(20, 20)
    SpriteUtil.SetSpriteRenderer(self.m_boardBgSprite, self.m_activityDefinition.BoardBgImageName, function()
      self.m_boardBgSprite.size = Vector2(BaseBoardModel.TileSize * self.m_model:GetHorizontalTiles(), BaseBoardModel.TileSize * self.m_model:GetVerticalTiles()) + offset
    end)
    UIUtil.SetLocalPosition(self.m_boardBgSprite.transform, BaseBoardModel.TileSize * self.m_model:GetHorizontalTiles() / 2, BaseBoardModel.TileSize * self.m_model:GetVerticalTiles() / 2)
  end
end

function ExtraBoardActivityBoardView:UpdateCobwebBoard(cobwebRect)
  self.m_cobwebRect = cobwebRect
  self.m_cobwebCodes = self.m_model:GetCobwebItemCodes()
  self:_UpdateCobwebTiles()
  self:_LoadCobwebItems()
  self:_UpdateCobwebMask()
  self:UpdateCobwebItemVisible()
end

function ExtraBoardActivityBoardView:_UpdateCobwebMask()
  if self.m_cobwebRect == nil then
    return
  end
  local uiWorldPosition = self.m_cobwebRect.position
  local worldPosition = ExtraBoardActivityBoardContainer.GetInstance():ConvertUIWorldPositionToWorldPosition(uiWorldPosition)
  local localPosition = self.transform:InverseTransformPoint(worldPosition)
  UIUtil.SetLocalPosition(self.m_cbMaskTransform, localPosition.x, localPosition.y)
  DelayExecuteFuncInView(function()
    local rect = self.m_cobwebRect
    local corners = CS.System.Array.CreateInstance(typeof(Vector3), 4)
    rect:GetWorldCorners(corners)
    local ld = ExtraBoardActivityBoardContainer.GetInstance():ConvertUIWorldPositionToWorldPosition(corners[0])
    local rd = ExtraBoardActivityBoardContainer.GetInstance():ConvertUIWorldPositionToWorldPosition(corners[3])
    ld = self.transform:InverseTransformPoint(ld)
    rd = self.transform:InverseTransformPoint(rd)
    local width = math.abs(rd.x - ld.x)
    UIUtil.SetLocalScale(self.m_cbMaskTransform, width / 100)
    self.m_maskWidth = width
    self.m_maskRatio = width / self.m_cobwebRect.sizeDelta.x
  end, 0.1, self)
end

function ExtraBoardActivityBoardView:_UpdateCobwebTiles()
  self.m_cbTilesTransform:DestroyAllChildren()
  local itemCodes = self.m_cobwebCodes
  if Table.IsEmpty(itemCodes) then
    return
  end
  local tileObject, tileSprite
  local horizontalNum = self.m_model:GetHorizontalTiles()
  local startOffsetRatio = (horizontalNum - #itemCodes + 1) / 2
  for i = 1, #itemCodes do
    tileObject = Object.Instantiate(self.m_tilePrefab, self.m_cbTilesTransform)
    tileSprite = tileObject:GetComponent(typeof(SpriteRenderer))
    tileSprite.maskInteraction = SpriteMaskInteraction.VisibleInsideMask
    SpriteUtil.SetSpriteRenderer(tileSprite, ImageFileConfigName.cobweb_tile_bg1)
    tileObject.transform.localPosition = self:_GetCobwebTileLocalPosition(i)
  end
end

function ExtraBoardActivityBoardView:UpdateCobwebItemVisible()
  local bShow = self.m_model:HasFinishedAllMainCobweb()
  UIUtil.SetActive(self.m_cbMaskTransform.gameObject, bShow)
end

function ExtraBoardActivityBoardView:_GetCobwebTileLocalPosition(index)
  local interval = 10
  local horizontalTiles = self.m_model:GetHorizontalTiles()
  local yPos = self:_GetCobwebTilePosY()
  local itemNum = #(self.m_cobwebCodes or {})
  local startOffsetRatio = (horizontalTiles - itemNum + 1) / 2
  local startIntervalOffset = (itemNum - 1) * -interval / 2
  return Vector3(BaseBoardModel.TileSize * (index - 1 + startOffsetRatio) + startIntervalOffset + (index - 1) * interval, yPos, 0)
end

function ExtraBoardActivityBoardView:_GetCobwebTilePosY()
  if self.m_cobwebRect ~= nil then
    local uiWorldPosition = self.m_cobwebRect.position
    local worldPosition = ExtraBoardActivityBoardContainer.GetInstance():ConvertUIWorldPositionToWorldPosition(uiWorldPosition)
    local localPosition = self.m_cbTilesTransform:InverseTransformPoint(worldPosition)
    return localPosition.y
  end
  local verticalTiles = self.m_model:GetVerticalTiles()
  local yPos = BaseBoardModel.TileSize * (verticalTiles + 0.5) + 40
  return yPos
end

function ExtraBoardActivityBoardView:_GetBoardPosition(worldPosition)
  local itemCobwebView = self:GetCobwebItemView(worldPosition)
  if itemCobwebView ~= nil then
    return itemCobwebView:GetModel():GetPosition()
  end
  return BaseUIBoardView._GetBoardPosition(self, worldPosition)
end

function ExtraBoardActivityBoardView:GetCobwebItemView(worldPosition)
  if Table.IsEmpty(self.m_cbModelViewMap) or not self.m_model:HasFinishedAllMainCobweb() then
    return
  end
  local halfTileSize = BaseBoardModel.TileSize / 2
  local checkFunc = function(itemWorldPosition)
    return worldPosition.x > itemWorldPosition.x - halfTileSize and worldPosition.x < itemWorldPosition.x + halfTileSize and worldPosition.y > itemWorldPosition.y - halfTileSize and worldPosition.y < itemWorldPosition.y + halfTileSize
  end
  for _, view in pairs(self.m_cbModelViewMap) do
    if checkFunc(view.transform.position) then
      return view
    end
  end
end

function ExtraBoardActivityBoardView:IsBoardPositionValid(boardPosition)
  if boardPosition:IsValid() then
    return true
  end
  return self.m_model:GetItem(boardPosition) ~= nil
end

function ExtraBoardActivityBoardView:_AddCobwebItemView(itemModel)
  local itemView = self:CreateItemView(self.m_cbItemsTransform, itemModel, false)
  self.m_cbModelViewMap[itemModel] = itemView
  self.m_modelViewMap[itemModel] = itemView
  local renderers = itemView.gameObject:GetComponentsInChildren(typeof(Renderer), true)
  for i = 0, renderers.Length - 1 do
    if renderers[i].maskInteraction ~= nil then
      renderers[i].maskInteraction = SpriteMaskInteraction.VisibleInsideMask
    end
  end
  return itemView
end

function ExtraBoardActivityBoardView:_RemoveCobwebItemView(itemView)
  if itemView == nil then
    return
  end
  local itemModel = itemView:GetModel()
  self.m_cbModelViewMap[itemModel] = nil
  self.m_modelViewMap[itemModel] = nil
  itemView.gameObject:RemoveSelf()
  self:AddCobwebCompleteView(itemModel:GetPosition():GetX())
end

function ExtraBoardActivityBoardView:_LoadCobwebItems()
  self.m_cbItemsTransform:DestroyAllChildren()
  self.m_cbCompleteRoot:DestroyAllChildren()
  local itemCodes = self.m_cobwebCodes
  if Table.IsEmpty(itemCodes) then
    return
  end
  local cbItems = self.m_model:GetCobwebItems()
  local index = 1
  local itemView
  for i, code in ipairs(itemCodes) do
    if code == ExtraBoardActivityModel.CobwebCompleteFlag then
      self:AddCobwebCompleteView(i)
    elseif cbItems[index] ~= nil then
      itemView = self:_AddCobwebItemView(cbItems[index])
      index = index + 1
      itemView.transform.localPosition = self:_GetCobwebTileLocalPosition(i)
    end
  end
end

function ExtraBoardActivityBoardView:AddCobwebCompleteView(index)
  local completeGo = GameObject.Instantiate(self.m_cbCompleteGo, self.m_cbCompleteRoot)
  local localPos = self:_GetCobwebTileLocalPosition(index)
  completeGo.transform.localPosition = Vector3(localPos.x, localPos.y - 5, localPos.z)
  completeGo:SetActive(true)
end

function ExtraBoardActivityBoardView:_UpdateIndicator(item, playAnimation)
  self.m_selectedBoardPosition = item and item:GetPosition()
  if item ~= nil and self.m_model:IsCobwebItem(item) then
    self.m_indicator:UpdateIndicatorWithoutBoard(self.m_cbModelViewMap[item], playAnimation)
  else
    self.m_indicator:UpdateIndicator(item, playAnimation)
  end
end

function ExtraBoardActivityBoardView:_OnMergeCobwebItem(message)
  self:_UpdateIndicator(message.New, true)
  self:UpdateBoardInfoBar(message.New)
  local newItemType = message.New:GetType()
  local level = GM.ItemDataModel:GetChainLevel(message.New:GetType())
  local sfx
  if level <= 2 then
    sfx = AudioFileConfigName.SfxMergelv2
  elseif level < 9 then
    sfx = AudioFileConfigName["SfxMergelv" .. level]
  else
    sfx = AudioFileConfigName.SfxMergelv9
  end
  GM.AudioModel:PlayEffect(sfx)
  local sourceItemView = self:GetItemView(message.Source)
  sourceItemView.toBeRemoved = true
  local targetItemView = self.m_cbModelViewMap[message.Target]
  targetItemView.toBeRemoved = true
  local newItemView = self:_AddItemView(message.New)
  if sourceItemView == nil or targetItemView == nil then
    self:_RemoveItemView(sourceItemView)
    self:_RemoveCobwebItemView(targetItemView)
    return
  end
  local targetPosition = targetItemView.transform.localPosition
  local sequence = DOTween.Sequence()
  sequence:Insert(0, sourceItemView.transform:DOLocalMove(targetPosition, 0.1))
  sequence:Insert(0, sourceItemView.transform:DOScale(0.3, 0.1))
  sequence:InsertCallback(0.1, function()
    self:_RemoveItemView(sourceItemView)
  end)
  targetItemView:MergeLightDisappear()
  sequence:Insert(0, targetItemView.transform:DOScale(0.3, 0.1))
  sequence:InsertCallback(0.1, function()
    self:_RemoveCobwebItemView(targetItemView)
  end)
  if newItemView ~= nil then
    newItemView.transform.localScale = Vector3.zero
    sequence:InsertCallback(0.1, function()
      newItemView.transform.localScale = 0.3 * V3One
      self:_PlayJumpAnimation(newItemView, sourceItemView.transform.localPosition, newItemView.transform.localPosition)
    end)
    sequence:Insert(0.1, newItemView.transform:DOScale(1.3, 0.2))
    sequence:Insert(0.3, newItemView.transform:DOScale(1, 0.1))
  end
  local effectPos = targetItemView.transform.position
  sequence:InsertCallback(0.1, function()
    local mergeEffectPrefab = self.m_mergeEffectManager:GetPrefab(level)
    if mergeEffectPrefab ~= nil then
      local gameObject = Object.Instantiate(mergeEffectPrefab, effectPos, Quaternion.identity, self.m_effectRoot)
      DOVirtual.DelayedCall(1, function()
        if not gameObject:IsNull() then
          gameObject:RemoveSelf()
        end
      end)
    end
  end)
end

function ExtraBoardActivityBoardView:PlayCobwebEnterAnim(interval, animTime1, animTime2, animTime3, forwardDis, behindDis)
  self:SetCobwebBoxStartState()
  UIUtil.SetLocalPosition(self.m_cbRootRect, -self.m_maskWidth or 0)
  local originLocalPos = self.m_cbRootRect.localPosition
  local arrPath = {
    Vector3(forwardDis * self.m_maskRatio, originLocalPos.y, originLocalPos.z),
    Vector3(behindDis * self.m_maskRatio, originLocalPos.y, originLocalPos.z),
    Vector3(0, originLocalPos.y, originLocalPos.z)
  }
  local sequence = DOTween.Sequence()
  sequence:AppendInterval(interval)
  sequence:Append(self.m_cbRootRect:DOLocalMoveX(forwardDis * self.m_maskRatio, animTime1):SetEase(Ease.InOutSine))
  sequence:InsertCallback(animTime1 + interval - 0.2, function()
    self:PlayCobwebBoxAnim()
  end)
  sequence:Append(self.m_cbRootRect:DOLocalMoveX(behindDis * self.m_maskRatio, animTime2):SetEase(Ease.InOutSine))
  sequence:Append(self.m_cbRootRect:DOLocalMoveX(0, animTime3):SetEase(Ease.InOutSine))
  sequence:AppendCallback(function()
    self.m_enterSeq = nil
  end)
  self.m_enterSeq = sequence
end

function ExtraBoardActivityBoardView:SetCobwebBoxStartState()
  self.m_arrBoxGo = {}
  local items = self.m_model:GetCobwebItems()
  local itemView, go
  for _, item in ipairs(items) do
    itemView = self.m_cbModelViewMap[item]
    go = GameObject.Instantiate(self.m_cbBoxGo, self.m_cbItemsTransform)
    UIUtil.SetActive(go, true)
    go.transform.localPosition = itemView.transform.localPosition
    itemView.transform.localScale = V3Zero
    table.insert(self.m_arrBoxGo, go)
  end
end

function ExtraBoardActivityBoardView:PlayCobwebBoxAnim()
  if Table.IsEmpty(self.m_arrBoxGo) then
    return
  end
  local seq = DOTween.Sequence()
  local items = self.m_model:GetCobwebItems()
  for index, item in ipairs(items) do
    local itemView = self.m_cbModelViewMap[item]
    seq:InsertCallback((#items - index) * 0.2, function()
      local position = itemView.transform.localPosition
      self:PlayPaperBoxDisappearAnim(position, itemView, self.m_cbItemsTransform, function()
        if self.m_arrBoxGo ~= nil and self.m_arrBoxGo[index] ~= nil and not self.m_arrBoxGo[index]:IsNull() then
          GameObject.Destroy(self.m_arrBoxGo[index])
        end
      end)
    end)
  end
end

function ExtraBoardActivityBoardView:PlayCobwebExitAnim(interval, animTime1, animTime2, animTime3, forwardDis, behindDis)
  local exitGo = GameObject.Instantiate(self.m_cbRootRect.gameObject, self.m_cbRootRect.parent)
  local sequence = DOTween.Sequence()
  local completeRootTrans = exitGo.transform:Find("CobwebComplete")
  self.m_arrCompleteTrans = {}
  for i = 1, completeRootTrans.childCount do
    table.insert(self.m_arrCompleteTrans, completeRootTrans:GetChild(i - 1))
  end
  table.sort(self.m_arrCompleteTrans, function(a, b)
    return a.localPosition.x < b.localPosition.x
  end)
  self:PlayCobwebExitEffects(exitGo.transform, interval)
  local originLocalPos = exitGo.transform.localPosition
  sequence:AppendInterval(interval)
  sequence:Append(exitGo.transform:DOLocalMoveX(self.m_maskWidth + forwardDis * self.m_maskRatio, animTime1):SetEase(Ease.InOutSine))
  sequence:AppendCallback(function()
    GameObject.Destroy(exitGo)
  end)
  self.m_exitSeq = sequence
end

function ExtraBoardActivityBoardView:PlayCobwebExitEffects(parentTrans, animTime)
  local mergeEffectPrefab = self.m_mergeEffectManager:GetPrefab(1)
  if mergeEffectPrefab == nil then
    return
  end
  local lastStartTime = animTime - 0.2
  local intervalTime = lastStartTime / (#self.m_cobwebCodes - 1)
  local seq = DOTween.Sequence()
  for index, _ in ipairs(self.m_cobwebCodes) do
    local localPosition = self:_GetCobwebTileLocalPosition(index)
    seq:InsertCallback((#self.m_cobwebCodes - index) * intervalTime, function()
      if self.m_arrCompleteTrans[index] ~= nil then
        local gameObject = Object.Instantiate(mergeEffectPrefab, parentTrans:TransformPoint(localPosition), Quaternion.identity, parentTrans)
        DelayExecuteFunc(function()
          if not gameObject:IsNull() then
            gameObject:RemoveSelf()
          end
        end, 1)
        self:PlayScaleAnim(self.m_arrCompleteTrans[index])
      end
    end)
  end
end

function ExtraBoardActivityBoardView:PlayScaleAnim(trans)
  local seq = DOTween.Sequence()
  seq:Append(trans:DOScale(Vector3(1.5, 1.5, 1), 0.05))
  seq:Append(trans:DOScale(Vector3(1, 1, 1), 0.15):SetEase(Ease.OutCubic))
end

function ExtraBoardActivityBoardView:EnterNextCobwebRound(interval, animTime1, animTime2, animTime3, forwardDis, behindDis)
  self:PlayCobwebExitAnim(interval, animTime1, animTime2, animTime3, forwardDis, behindDis)
  self.m_cobwebCodes = self.m_model:GetCobwebItemCodes()
  self:_UpdateCobwebTiles()
  self:_LoadCobwebItems()
  self:PlayCobwebEnterAnim(interval, animTime1, animTime2, animTime3, forwardDis, behindDis)
end
