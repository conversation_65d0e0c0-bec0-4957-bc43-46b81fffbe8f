PromptManager = {}
PromptManager.__index = PromptManager
SpecialPromptPrefabName = {
  Window = UIPrefabConfigName.PromptOnWindow,
  Window2 = UIPrefabConfigName.PromptOnWindow2,
  Prompt = UIPrefabConfigName.Prompt
}

function PromptManager.Create()
  local instance = setmetatable({}, PromptManager)
  instance:_Init()
  return instance
end

function PromptManager:_Init()
  self.m_curOrder = 0
  self.m_currentId = 0
end

function PromptManager:GetCurrentId()
  return self.m_currentId
end

function PromptManager:Show(text, uiWorldPos, stayDuration, bTMP, forTest, sortingOrder, specialPrefab)
  self:_Show(text, uiWorldPos, stayDuration, bTMP, forTest, sortingOrder, specialPrefab)
end

function PromptManager:_Show(text, uiWorldPos, stayDuration, bTMP, forTest, sortingOrder, specialPrefab)
  local id = self.m_currentId + 1
  self.m_currentId = id
  EventDispatcher.DispatchEvent(EEventType.ShowPrompt)
  local loadCallback = function(go)
    self:_ShowPrompt(go, id, text, uiWorldPos, stayDuration, bTMP, forTest, sortingOrder)
  end
  local prefabName
  if specialPrefab ~= nil then
    prefabName = specialPrefab
  else
    local bInWindow = GM.UIManager:GetOpenedViewCountByType(EViewType.Window) > 0 and GM.ModeViewController:GetExtraBoardActivityBoardView() == nil
    prefabName = bInWindow and UIPrefabConfigName.PromptOnWindow or UIPrefabConfigName.Prompt
  end
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(prefabName), GM.UIManager:GetCanvasRoot(), Vector3.zero, loadCallback)
end

function PromptManager:_ShowPrompt(go, id, text, uiWorldPos, stayDuration, bTMP, forTest, sortingOrder)
  local callback = function()
    GameObjectPool:Recycle(go)
    self.m_curOrder = self.m_curOrder - 1
  end
  local prompt = go:GetLuaTable()
  self.m_curOrder = self.m_curOrder + 1
  local targetOrder = (sortingOrder or ESpecialViewSortingOrder.Prompt) + self.m_curOrder
  prompt:SetSortingOrder(targetOrder)
  prompt:Init(id, text, uiWorldPos, stayDuration, callback, bTMP, forTest)
end
