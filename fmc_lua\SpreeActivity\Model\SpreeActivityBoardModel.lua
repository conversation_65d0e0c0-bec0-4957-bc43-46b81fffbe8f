SpreeActivityBoardModel = setmetatable({}, BaseSceneBoardModel)
SpreeActivityBoardModel.__index = SpreeActivityBoardModel

function SpreeActivityBoardModel.Create(activityModel, itemDataTable, itemLayerDataTable, itemCacheDataTable, orderMetaDataTable, orderDataTable)
  local boardModel = setmetatable({}, SpreeActivityBoardModel)
  boardModel:Init(activityModel, itemDataTable, itemLayerDataTable, itemCacheDataTable, orderMetaDataTable, orderDataTable)
  return boardModel
end

function SpreeActivityBoardModel:Init(activityModel, itemDataTable, itemLayerDataTable, itemCacheDataTable, orderMetaDataTable, orderDataTable)
  local activityType = activityModel:GetType()
  local activityDefinition = SpreeActivityDefinition[activityType]
  self.m_activityDefinition = activityDefinition
  self.m_itemLayerDataTable = itemLayerDataTable
  local itemManager = ItemManager.Create(itemDataTable, self)
  local itemCacheModel = ItemCacheModel.Create(itemCacheDataTable, itemManager:GetIdGenerator())
  local orderModel = SpreeActivityOrderModel.Create(activityType, orderMetaDataTable, orderDataTable, self)
  BaseSceneBoardModel.Init(self, activityDefinition.GameMode, itemManager, itemCacheModel, 7, 9, orderModel)
  self.m_activityModel = activityModel
end

function SpreeActivityBoardModel:_CreateItemLayerModel()
  local activityDefinition = self.m_activityDefinition
  return SceneItemLayerModel.Create(self, self.m_itemLayerDataTable, self.m_itemManager, activityDefinition.BoardModelConfigFileName or "SpreeBoardModelConfig_" .. activityDefinition.ConfigSuffix)
end

function SpreeActivityBoardModel:IsOrderFinished(orderId)
  return self.m_orderModel:IsOrderFinished(orderId)
end
