BuyEnergyWindow = setmetatable({
  disableEffectWhenCloseView = true,
  Type = EnergyType.Main
}, BaseWindow)
BuyEnergyWindow.__index = BuyEnergyWindow

function BuyEnergyWindow.EnergyType2WindowName(energyType)
  if energyType == EnergyType.Event then
    return UIPrefabConfigName.BuyEventEnergyWindow
  else
    return UIPrefabConfigName.BuyEnergyWindow
  end
end

function BuyEnergyWindow:Init(popupForLackingEnergy, inShopWindow)
  self.m_energyModel = GM.EnergyModel
  self.m_shopModel = GM.ShopModel
  self.m_energyCount = GM.ShopDataModel:GetBuyEnergyCount()
  self.m_inShopWindow = inShopWindow
  self.m_popupForLackingEnergy = popupForLackingEnergy
  local energyStr = "+" .. tostring(self.m_energyCount)
  self.m_numText.text = energyStr
  self:UpdateContent()
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self.UpdateContent)
  if popupForLackingEnergy then
    local isAutoRun = IsAutoRun()
    local disableTouchDuration = isAutoRun and 0.2 or GM.ConfigModel:GetAntiMisTouchDuration()
    if disableTouchDuration ~= nil and 0 < disableTouchDuration then
      self.m_bDisableTouch = true
      DelayExecuteFuncInView(function()
        self.m_bDisableTouch = nil
        if isAutoRun then
          self.m_propertyButton:OnBtnClicked()
        end
      end, disableTouchDuration, self)
    end
  end
  DelayExecuteFuncInView(function()
    self.m_effectGo:SetActive(true)
  end, 0.2, self)
  self:LogWindowAction(EBIType.UIActionType.Open, popupForLackingEnergy and EBIReferType.ClickEle or EBIReferType.UserClick)
end

function BuyEnergyWindow:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
end

function BuyEnergyWindow:GetBtnRectTrans()
  return self.m_propertyButton.gameObject.transform
end

function BuyEnergyWindow:UpdateContent()
  if self.m_callback == nil then
    function self.m_callback()
      if self.m_bDisableTouch then
        return
      end
      self.m_bBuySuccess = self.m_shopModel:BuyEnergy(self.Type)
      if self.m_bBuySuccess then
        self.m_boostBonusResult = self.m_boostBonusDuration
      end
      self:Close()
    end
  end
  self.m_free2Refill = self.m_shopModel:IsEnergyFreeRefill(self.Type)
  local energyData = self.m_shopModel:GetEnergyDatas(self.Type)
  self.m_freeTextGo:SetActive(self.m_free2Refill)
  self.m_priceTextGo:SetActive(not self.m_free2Refill)
  self.m_propertyButton:Init(EPropertyType.Gem, energyData[1].costNum, self.m_callback)
  self:_UpdateBoostBonus(self.m_free2Refill and 0 or energyData[1].costNum)
end

function BuyEnergyWindow:_UpdateBoostBonus(buyEnergyCost)
  local bonusDuration = GM.EnergyBoostModel:GetBoostBonusDurationInMin(buyEnergyCost)
  self.m_boostBonusDuration = bonusDuration
  if bonusDuration <= 0 then
    self.m_boostBonusGo:SetActive(false)
  else
    self.m_boostBonusGo:SetActive(true)
    self.m_boostBonusText.text = "+" .. bonusDuration .. GM.GameTextModel:GetText("common_time_m")
  end
end

function BuyEnergyWindow:OnCloseFinish()
  if self.m_bBuySuccess then
    local viewRewards = {}
    viewRewards[#viewRewards + 1] = {
      [PROPERTY_TYPE] = EnergyModel.EnergyType2PropertyType(self.Type),
      [PROPERTY_COUNT] = self.m_energyCount or 0
    }
    if self.m_boostBonusResult and 0 < self.m_boostBonusResult then
      viewRewards[#viewRewards + 1] = {
        [PROPERTY_TYPE] = EPropertyType.EnergyBoost,
        [PROPERTY_COUNT] = 1
      }
    end
    local viewData = {
      eventLock = false,
      interval = 0.3,
      arrNoDiffusion = {false, true},
      arrFlyPause = {0, 0.5},
      arrFlyStartScale = {1, 1.5}
    }
    RewardApi.AcquireRewardsInView(viewRewards, viewData)
  elseif self.m_bBuySuccess == false then
    GM.ShopModel:OnLackOfGem(self.m_shopModel:GetEnergyDatas(self.Type)[1].costNum - GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem))
  elseif self.m_inShopWindow then
    GM.UIManager:OpenView(UIPrefabConfigName.ShopWindow)
  elseif self.Type == EnergyType.Main then
    GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.EnergyEmpty)
    if self.m_popupForLackingEnergy and not self.m_free2Refill then
      GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.LackEnergy)
    end
  end
  BaseWindow.OnCloseFinish(self)
end

function BuyEnergyWindow:OnClickBoostBonus()
  GM.UIManager:OpenView(UIPrefabConfigName.EnergyBoostHelpWindow)
end

BuyEventEnergyWindow = setmetatable({
  Type = EnergyType.Event
}, BuyEnergyWindow)
BuyEventEnergyWindow.__index = BuyEventEnergyWindow
