TaskButton = setmetatable({}, HudGeneralButton)
TaskButton.__index = TaskButton

function TaskButton:Awake()
  HudGeneralButton.Awake(self)
  self:UpdateContent()
  EventDispatcher.AddListener(EEventType.ChapterChangeFinished, self, self.OnGameModeChanged)
  EventDispatcher.AddListener(EEventType.TaskProgressRewardClaimed, self, self.UpdateContent)
  EventDispatcher.AddListener(EEventType.PropertyConsumed, self, self.UpdateExclaimation)
  EventDispatcher.AddListener(EEventType.PropertyAcquired, self, self.UpdateExclaimation)
  EventDispatcher.AddListener(EEventType.NewTasksUnlocked, self, self.UpdateExclaimation)
end

function TaskButton:_NeedDisplay()
  if not HudGeneralButton._NeedDisplay(self) then
    return false
  end
  local curActiveChapterName = GM.ChapterManager.curActiveChapterName
  return curActiveChapterName and curActiveChapterName == GM.TaskManager:GetOngoingChapterName()
end

function TaskButton:OnClicked()
  if GM.TaskManager:IsAllFinished() then
    GM.UIManager:OpenView(UIPrefabConfigName.TaskClearWindow)
    return
  end
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.TaskWindow) then
    return
  end
  GM.UIManager:OpenView(UIPrefabConfigName.TaskWindow, ETaskWindowRefer.TaskButton)
end

function TaskButton:UpdateContent()
  local finished, total = GM.TaskManager:GetProgressRewardProgress()
  self.m_progressText.text = tostring(finished) .. "/" .. tostring(0 < total and total or "MAX")
  self.m_slider.value = 0 < total and finished / total or 1
  self:UpdateExclaimation()
end

function TaskButton:UpdateExclaimation()
  self.m_exclamationGo:SetActive(GM.TaskManager:CanFinishOngoingTask())
end

function TaskButton:IconScaleAnimation(...)
  HudGeneralButton.IconScaleAnimation(self, ...)
  self:UpdateContent()
end

function TaskButton:GetBoxPosAndScale()
  return self.m_boxImage.transform.position, 0.4
end
