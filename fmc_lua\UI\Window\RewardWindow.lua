local imageType = typeof(Image)
RewardWindow = setmetatable({
  disableWindowMaskOnOpenView = 0.5,
  canCloseByAndroidBack = false,
  canClickWindowMask = true,
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  hideHudAnchorType = {
    EHudAnchorType.All
  },
  disableEffectWhenCloseView = true
}, BaseWindow)
RewardWindow.__index = RewardWindow

function RewardWindow:Init(arrRewards, titleTextKey, playRewardAnim, initCallback, closeCallback, bLockEventDuringAnimation)
  self.m_arrRewards = arrRewards
  self.m_bPlayRewardAnimation = playRewardAnim
  self.m_bLockEventDuringAnimation = bLockEventDuringAnimation
  self.m_closeCallback = closeCallback
  if titleTextKey then
    self.m_titleText.text = GM.GameTextModel:GetText(titleTextKey)
  end
  self.m_rewardContent:Init(self.m_arrRewards, 9)
  UIUtil.UpdateSortingOrder(self.m_rewardContent.gameObject, self:GetSortingOrder() - 1)
  UIUtil.UpdateSortingOrder(self.m_effectGo, self:GetSortingOrder() - 1)
  if #arrRewards == 1 then
    self.m_rewardContent.transform:SetLocalScaleXY(1.1)
  end
  if initCallback then
    initCallback(self)
  end
  if IsAutoRun() then
    DOVirtual.DelayedCall(0.2, function()
      self:OnWindowMaskClicked()
    end)
  end
end

function RewardWindow:OnWindowMaskClicked()
  if self.m_bClosing then
    return
  end
  self.m_bClosing = true
  BaseWindow.OnWindowMaskClicked(self)
end

function RewardWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  if self.m_closeCallback then
    self.m_closeCallback(self)
    self.m_closeCallback = nil
  end
  if self.m_bPlayRewardAnimation then
    self.m_rewardContent:PlayRewardAnimation(self.m_bLockEventDuringAnimation)
  else
    for _, rewardData in ipairs(self.m_arrRewards) do
      if GM.PropertyDataManager:IsPropertyType(rewardData[PROPERTY_TYPE]) then
        GM.PropertyDataManager:PlayPropertyIncreaseAnimation({rewardData})
      end
    end
  end
end
