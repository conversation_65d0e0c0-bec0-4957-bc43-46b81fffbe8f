PassActivityBuyMaxTicketWindow = setmetatable({bNeedSetWindowOpened = false, disableEffectWhenCloseView = true}, PassActivityBaseWindow)
PassActivityBuyMaxTicketWindow.__index = PassActivityBuyMaxTicketWindow

function PassActivityBuyMaxTicketWindow:Init(activityType, ext)
  PassActivityBaseWindow.Init(self, activityType, false, ext)
  if not self:CheckValid() then
    self:Close()
    return
  end
  self:UpdateContent()
  EventDispatcher.AddListener(self.m_activityDefinition.BuyTicketSuccessEvent, self, self._OnBuyTicketSuccess)
  EventDispatcher.AddListener(self.m_activityDefinition.BuyMaxTicketSuccessEvent, self, self._OnBuyMaxTicketSuccess)
end

function PassActivityBuyMaxTicketWindow:UpdateContent()
  self.m_rewardBubble:Init(self.m_model:GetAllExtraRewards(), self:GetSortingOrder() + 8)
  local config = self.m_model:GetLevelConfigs()[1]
  self.m_energyNumText1.text = config.vipRewards[1][PROPERTY_COUNT]
  self.m_energyNumText2.text = config.vipRewards[1][PROPERTY_COUNT]
  local tokenNum = self.m_model:GetMaxTicketTokenNumber()
  local tokenRatio = self.m_model:GetMaxTicketTokenRatio()
  self.m_tokenNumText.text = tostring(tokenNum)
  self.m_tokenRatioText.text = "x" .. tostring(tokenRatio)
  self.m_iapButtonTicket:Init(GM.InAppPurchaseModel:GetLocalizedPrice(self.m_model:GetPassTicketIapType()))
  self.m_iapButtonMaxTicket:Init(GM.InAppPurchaseModel:GetLocalizedPrice(self.m_model:GetMaxPassTicektIapType()))
end

function PassActivityBuyMaxTicketWindow:CheckValid()
  return self.m_model:CanBuyMaxTicket() and self.m_model:GetTicketState() == EBPTicketState.None
end

function PassActivityBuyMaxTicketWindow:OnBuyTicketButtonClicked()
  self.m_model:BuyTicket()
  self.m_rewardBubble:HideBubble()
end

function PassActivityBuyMaxTicketWindow:_OnBuyTicketSuccess()
  PassActivityViewHelper.SetViewChain(true)
  GM.UIManager:OpenView(UIPrefabConfigName.PassActivityBuyTicketSuccessWindow1, self.m_activityType)
  self:Close()
end

function PassActivityBuyMaxTicketWindow:OnBuyMaxTicketButtonClicked()
  self.m_model:BuyMaxTicket()
  self.m_rewardBubble:HideBubble()
end

function PassActivityBuyMaxTicketWindow:_OnBuyMaxTicketSuccess()
  PassActivityViewHelper.SetViewChain(true)
  GM.UIManager:OpenView(UIPrefabConfigName.PassActivityBuyTicketSuccessWindow3, self.m_activityType, true)
  self:Close()
end

function PassActivityBuyMaxTicketWindow:OnTipButtonClicked()
  self.m_rewardBubble:ShowBubble({
    bRight = true,
    pos = self.m_tipRect.position + Vector3(150, -30, 0)
  })
end

function PassActivityBuyMaxTicketWindow:OnMaskClicked()
  self.m_rewardBubble:HideBubble()
end

function PassActivityBuyMaxTicketWindow:OnCloseBtnClick()
  self.m_rewardBubble:HideBubble()
  PassActivityBaseWindow.OnCloseBtnClick(self)
end

PassActivityBuyUpTicketWindow = setmetatable({bNeedSetWindowOpened = false, disableEffectWhenCloseView = true}, PassActivityBaseWindow)
PassActivityBuyUpTicketWindow.__index = PassActivityBuyUpTicketWindow

function PassActivityBuyUpTicketWindow:Init(activityType, ext)
  PassActivityBaseWindow.Init(self, activityType, false, ext)
  if not self:CheckValid() then
    self:Close()
    return
  end
  self:UpdateContent()
  EventDispatcher.AddListener(self.m_activityDefinition.BuyUpTicketSuccessEvent, self, self._OnBuyUpTicketSuccess)
end

function PassActivityBuyUpTicketWindow:UpdateContent()
  self.m_rewardBubble:Init(self.m_model:GetAllExtraRewards(), self:GetSortingOrder() + 8)
  local config = self.m_model:GetLevelConfigs()[1]
  self.m_energyNumText.text = config.vipRewards[1][PROPERTY_COUNT]
  local tokenNum = self.m_model:GetMaxTicketTokenNumber()
  local tokenRatio = self.m_model:GetMaxTicketTokenRatio()
  self.m_tokenNumText.text = tostring(tokenNum)
  self.m_tokenRatioText.text = "x" .. tostring(tokenRatio)
  self.m_iapButtonUpTicket:Init(GM.InAppPurchaseModel:GetLocalizedPrice(self.m_model:GetUpPassTicketIapType()))
end

function PassActivityBuyUpTicketWindow:CheckValid()
  return self.m_model:CanBuyMaxTicket() and self.m_model:GetTicketState() == EBPTicketState.Upgrade
end

function PassActivityBuyUpTicketWindow:OnBuyUpTicketButtonClicked()
  self.m_rewardBubble:HideBubble()
  self.m_model:BuyUpTicket()
end

function PassActivityBuyUpTicketWindow:_OnBuyUpTicketSuccess()
  self:Close()
  PassActivityViewHelper.SetViewChain(true)
  GM.UIManager:OpenView(UIPrefabConfigName.PassActivityBuyTicketSuccessWindow3, self.m_activityType, false)
end

function PassActivityBuyUpTicketWindow:OnTipButtonClicked()
  self.m_rewardBubble:ShowBubble({
    bRight = true,
    pos = self.m_tipRect.position + Vector3(150, -30, 0)
  })
end

function PassActivityBuyUpTicketWindow:OnMaskClicked()
  self.m_rewardBubble:HideBubble()
end

function PassActivityBuyUpTicketWindow:OnCloseBtnClick()
  self.m_rewardBubble:HideBubble()
  PassActivityBaseWindow.OnCloseBtnClick(self)
end

PassActivityBuyMaxTicketPopupWindow = setmetatable({bNeedSetWindowOpened = false}, PassActivityBaseWindow)
PassActivityBuyMaxTicketPopupWindow.__index = PassActivityBuyMaxTicketPopupWindow

function PassActivityBuyMaxTicketPopupWindow:Init(activityType, ext)
  PassActivityBaseWindow.Init(self, activityType, false, ext)
  if not self:CheckValid() then
    self:Close()
    return
  end
  self.m_model:SetBuyTicketPopupWindowOpened()
  self:UpdateContent()
  EventDispatcher.AddListener(self.m_activityDefinition.BuyTicketSuccessEvent, self, self._OnBuyTicketSuccess)
  EventDispatcher.AddListener(self.m_activityDefinition.BuyMaxTicketSuccessEvent, self, self._OnBuyMaxTicketSuccess)
end

function PassActivityBuyMaxTicketPopupWindow:UpdateContent()
  local rewards = self.m_model:GetVipRewards()
  self.m_ticketRewardContent:Init(rewards)
  self.m_maxTicketRewardContent:Init(rewards)
  local tokenNum = self.m_model:GetMaxTicketTokenNumber()
  local tokenRatio = self.m_model:GetMaxTicketTokenRatio()
  self.m_tokenNumText.text = tostring(tokenNum)
  self.m_tokenRatioText.text = "x" .. tostring(tokenRatio)
  self.m_iapButtonTicket:Init(GM.InAppPurchaseModel:GetLocalizedPrice(self.m_model:GetPassTicketIapType()))
  self.m_iapButtonMaxTicket:Init(GM.InAppPurchaseModel:GetLocalizedPrice(self.m_model:GetMaxPassTicektIapType()))
end

function PassActivityBuyMaxTicketPopupWindow:CheckValid()
  return self.m_model:CanBuyMaxTicket() and self.m_model:GetTicketState() == EBPTicketState.None
end

function PassActivityBuyMaxTicketPopupWindow:OnBuyTicketButtonClicked()
  self.m_model:BuyTicket()
end

function PassActivityBuyMaxTicketPopupWindow:_OnBuyTicketSuccess()
  self:Close()
  PassActivityViewHelper.SetViewChain(true)
  GM.UIManager:OpenView(self.m_activityDefinition.BuyTicketUnlockedWindowPrefabName, self.m_activityType)
end

function PassActivityBuyMaxTicketPopupWindow:OnBuyMaxTicketButtonClicked()
  self.m_model:BuyMaxTicket()
end

function PassActivityBuyMaxTicketPopupWindow:_OnBuyMaxTicketSuccess()
  self:Close()
  PassActivityViewHelper.SetViewChain(true)
  GM.UIManager:OpenView(UIPrefabConfigName.PassActivityBuyTicketSuccessWindow3, self.m_activityType, true)
end

function PassActivityBuyMaxTicketPopupWindow:OnCloseBtnClick()
  if self.m_model:GetState() == ActivityState.Started then
    self:Close()
    PassActivityViewHelper.ContinueViewChain(self.m_activityType)
  end
end

PassActivityBuyMaxTicketSuccessWindow = setmetatable({bNeedSetWindowOpened = false}, PassActivityBaseWindow)
PassActivityBuyMaxTicketSuccessWindow.__index = PassActivityBuyMaxTicketSuccessWindow

function PassActivityBuyMaxTicketSuccessWindow:Init(activityType, ext)
  PassActivityBaseWindow.Init(self, activityType, false, ext)
  self:UpdateContent()
end

function PassActivityBuyMaxTicketSuccessWindow:UpdateContent()
  self.m_rewardBubble:Init(self.m_model:GetAllExtraRewards(), self:GetSortingOrder() + 8)
  if self.m_energyNumberText ~= nil then
    local config = self.m_model:GetLevelConfigs()[1]
    self.m_energyNumberText.text = config.vipRewards[1][PROPERTY_COUNT]
  end
  local tokenNum = self.m_model:GetMaxTicketTokenNumber()
  local tokenRatio = self.m_model:GetMaxTicketTokenRatio()
  self.m_tokenNumText.text = tostring(tokenNum)
  self.m_tokenRatioText.text = "x" .. tostring(tokenRatio)
end

function PassActivityBuyMaxTicketSuccessWindow:OnTipButtonClicked()
  self.m_rewardBubble:ShowBubble({
    bRight = true,
    pos = self.m_tipRect.position + Vector3(150, -30, 0)
  })
end

function PassActivityBuyMaxTicketSuccessWindow:OnMaskClicked()
  self.m_rewardBubble:HideBubble()
end

function PassActivityBuyMaxTicketSuccessWindow:OnCloseBtnClick()
  self.m_rewardBubble:HideBubble()
  PassActivityBaseWindow.OnCloseBtnClick(self)
  local tokenCount = self.m_model:GetMaxTicketTokenNumber()
  if not GM.UIManager:IsViewOpen(self.m_activityDefinition.MainWindowPrefabName) then
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, math.max(0, self.m_model:GetTokenNumber() - tokenCount))
  end
  GM.UIManager:OpenView(self.m_activityDefinition.TokenWindowPrefabName, self.m_activityType, tokenCount)
end
