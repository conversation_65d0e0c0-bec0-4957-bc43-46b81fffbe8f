TestMatchListCell = {}
TestMatchListCell.__index = TestMatchListCell

function TestMatchListCell:Init(showStr, parent, idx)
  self.m_strText = showStr
  self.m_parent = parent
  self.m_btnText.text = showStr
  self.m_idx = idx
  self:SetSelect(idx == parent.selectIdx)
end

function TestMatchListCell:_OnClick()
  self.m_parent:OnCellClick(self.m_strText)
end

function TestMatchListCell:SetSelect(select)
  self.m_selectGo:SetActive(select)
end
