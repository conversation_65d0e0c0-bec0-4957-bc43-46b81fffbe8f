return {
  {
    Id = "110010",
    GroupId = 1,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "110020",
    GroupId = 1,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110030",
    GroupId = 1,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6assort_10",
      Count = 1
    }
  },
  {
    Id = "110040",
    GroupId = 1,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "it_4_2_3", Count = 1}
  },
  {
    Id = "110050",
    GroupId = 1,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1scsau_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110060",
    GroupId = 1,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    }
  },
  {
    Id = "110070",
    GroupId = 1,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "110080",
    GroupId = 2,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "110090",
    GroupId = 2,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e1tato_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110100",
    GroupId = 2,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_e1icytre_1",
      Count = 1
    }
  },
  {
    Id = "110110",
    GroupId = 2,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    }
  },
  {
    Id = "110120",
    GroupId = 2,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_11e2tato_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110130",
    GroupId = 2,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "110140",
    GroupId = 2,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "110150",
    GroupId = 3,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110160",
    GroupId = 3,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {
      Type = "ds_11e1scsau_3",
      Count = 1
    }
  },
  {
    Id = "110170",
    GroupId = 3,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "110180",
    GroupId = 3,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "110190",
    GroupId = 3,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e4sf_25",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e3icytre_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110200",
    GroupId = 3,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "110210",
    GroupId = 3,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "110220",
    GroupId = 4,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e6saus_26",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "110230",
    GroupId = 4,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "110240",
    GroupId = 4,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e3icytre_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110250",
    GroupId = 4,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "110260",
    GroupId = 4,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "110270",
    GroupId = 4,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110280",
    GroupId = 4,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4assort_3",
      Count = 1
    }
  },
  {
    Id = "110290",
    GroupId = 5,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "110300",
    GroupId = 5,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "110310",
    GroupId = 5,
    ChapterId = 11,
    Requirement_1 = {Type = "it_4_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_11e3icytre_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110320",
    GroupId = 5,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e2veg_11",
      Count = 1
    }
  },
  {
    Id = "110330",
    GroupId = 5,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "110340",
    GroupId = 5,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e6dst_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_11", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110350",
    GroupId = 5,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_13",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "110360",
    GroupId = 6,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "110370",
    GroupId = 6,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e1scsau_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "110380",
    GroupId = 6,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_11e6nibble_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110390",
    GroupId = 6,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "110400",
    GroupId = 6,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "110410",
    GroupId = 6,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110420",
    GroupId = 6,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "110430",
    GroupId = 7,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    }
  },
  {
    Id = "110440",
    GroupId = 7,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_11e6stewmt_4",
      Count = 1
    }
  },
  {
    Id = "110450",
    GroupId = 7,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110460",
    GroupId = 7,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_11e5fd_26",
      Count = 1
    }
  },
  {
    Id = "110470",
    GroupId = 7,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_9", Count = 1}
  },
  {
    Id = "110480",
    GroupId = 7,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1scsau_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110490",
    GroupId = 7,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6rice_8",
      Count = 1
    }
  },
  {
    Id = "110500",
    GroupId = 8,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "110510",
    GroupId = 8,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "110520",
    GroupId = 8,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e3icytre_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1tato_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110530",
    GroupId = 8,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "110540",
    GroupId = 8,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110550",
    GroupId = 8,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "110560",
    GroupId = 8,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "110570",
    GroupId = 9,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_fd_12", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "110580",
    GroupId = 9,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1tato_1",
      Count = 1
    }
  },
  {
    Id = "110590",
    GroupId = 9,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110600",
    GroupId = 9,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e3icytre_10",
      Count = 1
    }
  },
  {
    Id = "110610",
    GroupId = 9,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e5tato_12",
      Count = 1
    }
  },
  {
    Id = "110620",
    GroupId = 9,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6dst_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110630",
    GroupId = 9,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "110640",
    GroupId = 10,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_6", Count = 1}
  },
  {
    Id = "110650",
    GroupId = 10,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e3icytre_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "110660",
    GroupId = 10,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110670",
    GroupId = 10,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "110680",
    GroupId = 10,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110690",
    GroupId = 10,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "110700",
    GroupId = 10,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e3icytre_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_9",
      Count = 1
    }
  },
  {
    Id = "110710",
    GroupId = 11,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "110720",
    GroupId = 11,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1assort_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110730",
    GroupId = 11,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    }
  },
  {
    Id = "110740",
    GroupId = 11,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_23",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110750",
    GroupId = 11,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "110760",
    GroupId = 11,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "110770",
    GroupId = 11,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "110780",
    GroupId = 12,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "110790",
    GroupId = 12,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_11e2tato_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110800",
    GroupId = 12,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "110810",
    GroupId = 12,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "110820",
    GroupId = 12,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110830",
    GroupId = 12,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_11e4tato_24",
      Count = 1
    }
  },
  {
    Id = "110840",
    GroupId = 12,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6rice_10",
      Count = 1
    }
  },
  {
    Id = "110850",
    GroupId = 13,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    }
  },
  {
    Id = "110860",
    GroupId = 13,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_8e6nibble_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1scsau_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110870",
    GroupId = 13,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    }
  },
  {
    Id = "110880",
    GroupId = 13,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    }
  },
  {
    Id = "110890",
    GroupId = 13,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e4tato_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4dst_12",
      Count = 1
    }
  },
  {
    Id = "110900",
    GroupId = 13,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110910",
    GroupId = 13,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e5saus_25",
      Count = 1
    }
  },
  {
    Id = "110920",
    GroupId = 14,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    }
  },
  {
    Id = "110930",
    GroupId = 14,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e3icytre_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110940",
    GroupId = 14,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_11e1tato_6",
      Count = 1
    }
  },
  {
    Id = "110950",
    GroupId = 14,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_9e6assort_5",
      Count = 1
    }
  },
  {
    Id = "110960",
    GroupId = 14,
    ChapterId = 11,
    Requirement_1 = {Type = "it_4_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_11e3icytre_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "110970",
    GroupId = 14,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    }
  },
  {
    Id = "110980",
    GroupId = 14,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "110990",
    GroupId = 15,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111000",
    GroupId = 15,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6porr_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111010",
    GroupId = 15,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillmt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "111020",
    GroupId = 15,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_3",
      Count = 1
    }
  },
  {
    Id = "111030",
    GroupId = 15,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1scsau_4",
      Count = 1
    }
  },
  {
    Id = "111040",
    GroupId = 15,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111050",
    GroupId = 15,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "111060",
    GroupId = 16,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "111070",
    GroupId = 16,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_11e4tato_8",
      Count = 1
    }
  },
  {
    Id = "111080",
    GroupId = 16,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111090",
    GroupId = 16,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_11e4tato_21",
      Count = 1
    }
  },
  {
    Id = "111100",
    GroupId = 16,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111110",
    GroupId = 16,
    ChapterId = 11,
    Requirement_1 = {
      Type = "it_a11_1_6_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111120",
    GroupId = 16,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "111130",
    GroupId = 17,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    }
  },
  {
    Id = "111140",
    GroupId = 17,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e5saus_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111150",
    GroupId = 17,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111160",
    GroupId = 17,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_11e3scsau_2",
      Count = 1
    }
  },
  {
    Id = "111170",
    GroupId = 17,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111180",
    GroupId = 17,
    ChapterId = 11,
    Requirement_1 = {
      Type = "it_a11_1_4_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "111190",
    GroupId = 17,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6porr_1",
      Count = 1
    }
  },
  {
    Id = "111200",
    GroupId = 18,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_1", Count = 1},
    Requirement_2 = {Type = "ds_7e5mt_6", Count = 1}
  },
  {
    Id = "111210",
    GroupId = 18,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_2",
      Count = 1
    }
  },
  {
    Id = "111220",
    GroupId = 18,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "it_a11_1_6_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111230",
    GroupId = 18,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "111240",
    GroupId = 18,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e2mt_15",
      Count = 1
    }
  },
  {
    Id = "111250",
    GroupId = 18,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e6dst_11",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111260",
    GroupId = 18,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_5",
      Count = 1
    }
  },
  {
    Id = "111270",
    GroupId = 19,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "111280",
    GroupId = 19,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    }
  },
  {
    Id = "111290",
    GroupId = 19,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e5fd_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111300",
    GroupId = 19,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    }
  },
  {
    Id = "111310",
    GroupId = 19,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_18",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111320",
    GroupId = 19,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e3icytre_10",
      Count = 1
    }
  },
  {
    Id = "111330",
    GroupId = 19,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111340",
    GroupId = 20,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    }
  },
  {
    Id = "111350",
    GroupId = 20,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111360",
    GroupId = 20,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "111370",
    GroupId = 20,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e4tato_9",
      Count = 1
    }
  },
  {
    Id = "111380",
    GroupId = 20,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111390",
    GroupId = 20,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e4tato_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_20",
      Count = 1
    }
  },
  {
    Id = "111400",
    GroupId = 20,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111410",
    GroupId = 21,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "111420",
    GroupId = 21,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111430",
    GroupId = 21,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e1assort_8",
      Count = 1
    }
  },
  {
    Id = "111440",
    GroupId = 21,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "111450",
    GroupId = 21,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_11e4tato_19",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111460",
    GroupId = 21,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_11e3icytre_10",
      Count = 1
    }
  },
  {
    Id = "111470",
    GroupId = 21,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111480",
    GroupId = 22,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "111490",
    GroupId = 22,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111500",
    GroupId = 22,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e5fd_24",
      Count = 1
    }
  },
  {
    Id = "111510",
    GroupId = 22,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111520",
    GroupId = 22,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1},
    Requirement_2 = {
      Type = "it_a11_1_4_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111530",
    GroupId = 22,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "111540",
    GroupId = 22,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6assort_4",
      Count = 1
    }
  },
  {
    Id = "111550",
    GroupId = 23,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_11e3scsau_1",
      Count = 1
    }
  },
  {
    Id = "111560",
    GroupId = 23,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111570",
    GroupId = 23,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "111580",
    GroupId = 23,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_2",
      Count = 1
    }
  },
  {
    Id = "111590",
    GroupId = 23,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111600",
    GroupId = 23,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "it_a11_1_6_1",
      Count = 1
    }
  },
  {
    Id = "111610",
    GroupId = 23,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "111620",
    GroupId = 24,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111630",
    GroupId = 24,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e4tato_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1scsau_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111640",
    GroupId = 24,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    }
  },
  {
    Id = "111650",
    GroupId = 24,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "111660",
    GroupId = 24,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e3icytre_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111670",
    GroupId = 24,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6dst_11",
      Count = 1
    }
  },
  {
    Id = "111680",
    GroupId = 24,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "111690",
    GroupId = 25,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    }
  },
  {
    Id = "111700",
    GroupId = 25,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillmt_10",
      Count = 1
    },
    Requirement_2 = {Type = "ds_sal_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111710",
    GroupId = 25,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "111720",
    GroupId = 25,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e4tato_15",
      Count = 1
    }
  },
  {
    Id = "111730",
    GroupId = 25,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111740",
    GroupId = 25,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "it_a11_1_6_1",
      Count = 1
    }
  },
  {
    Id = "111750",
    GroupId = 25,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111760",
    GroupId = 26,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "111770",
    GroupId = 26,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_11e5mt_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111780",
    GroupId = 26,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_friedve_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111790",
    GroupId = 26,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_11e1tato_14",
      Count = 1
    }
  },
  {
    Id = "111800",
    GroupId = 26,
    ChapterId = 11,
    Requirement_1 = {Type = "it_4_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111810",
    GroupId = 26,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e1tato_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "111820",
    GroupId = 26,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_11",
      Count = 1
    }
  },
  {
    Id = "111830",
    GroupId = 27,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "111840",
    GroupId = 27,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111850",
    GroupId = 27,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_9",
      Count = 1
    }
  },
  {
    Id = "111860",
    GroupId = 27,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_11e1tato_13",
      Count = 1
    }
  },
  {
    Id = "111870",
    GroupId = 27,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111880",
    GroupId = 27,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "111890",
    GroupId = 27,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111900",
    GroupId = 28,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "111910",
    GroupId = 28,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e3icytre_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111920",
    GroupId = 28,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "111930",
    GroupId = 28,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1tato_7",
      Count = 1
    }
  },
  {
    Id = "111940",
    GroupId = 28,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e4tato_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111950",
    GroupId = 28,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    }
  },
  {
    Id = "111960",
    GroupId = 28,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    }
  },
  {
    Id = "111970",
    GroupId = 29,
    ChapterId = 11,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "it_a11_1_6_1",
      Count = 1
    }
  },
  {
    Id = "111980",
    GroupId = 29,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_11e2tato_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "111990",
    GroupId = 29,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6nibble_6",
      Count = 1
    }
  },
  {
    Id = "112000",
    GroupId = 29,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_14", Count = 1}
  },
  {
    Id = "112010",
    GroupId = 29,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "112020",
    GroupId = 29,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6sf_22",
      Count = 1
    }
  },
  {
    Id = "112030",
    GroupId = 29,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "112040",
    GroupId = 30,
    ChapterId = 11,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "112050",
    GroupId = 30,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_22",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "112060",
    GroupId = 30,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_3",
      Count = 1
    }
  },
  {
    Id = "112070",
    GroupId = 30,
    ChapterId = 11,
    Requirement_1 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "112080",
    GroupId = 30,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "it_a11_1_6_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "112090",
    GroupId = 30,
    ChapterId = 11,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "112100",
    GroupId = 30,
    ChapterId = 11,
    Requirement_1 = {
      Type = "it_a11_1_4_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  }
}
