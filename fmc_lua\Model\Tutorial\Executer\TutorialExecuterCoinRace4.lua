local Step = {HighlightOrder = "1", HighlightEntry = "2"}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  for _, activityDefinition in pairs(CoinRaceActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnCoinRaceStateChange)
  end
end

function Executer:TryStartTutorial()
  local orderCell = TutorialHelper.GetMainFirstOrderCell()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board or orderCell == nil then
    return false
  end
  for activityType, activityDefinition in pairs(CoinRaceActivityDefinition) do
    if activityDefinition.TutorialOrderId == self:GetTutorialId() then
      local model = GM.ActivityManager:GetModel(activityType)
      if model ~= nil and model:IsInRace() then
        self.m_activityDefinition = activityDefinition
        self.m_activityType = activityType
        self.m_activityModel = model
        self.m_orderCell = orderCell
        self:_ExecuteStep1()
        return true
      end
    end
  end
end

function Executer:_OnCoinRaceStateChange(msg)
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    self:Finish()
  end
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.HighlightOrder
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  local orderCell = self.m_orderCell
  if orderCell == nil or orderCell.gameObject == nil or orderCell.gameObject:IsNull() then
    self:Finish()
    return
  end
  TutorialHelper.ScrollToNormalOrder(orderCell, true)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    if self.m_activityModel ~= nil and self.m_activityModel:GetState() ~= ActivityState.Started then
      self:Finish()
      return
    end
    local callback = function()
      self:_ExecuteStep2()
    end
    TutorialHelper.UpdateMaskOnBoard(orderCell.transform.position, orderCell.transform.sizeDelta, callback, false)
    local percent = 30
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText("coin_race_board_tutorial_1"), percent)
  end, 1)
end

function Executer:_ExecuteStep2()
  self.m_strOngoingDatas = Step.HighlightEntry
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local orderCell = TutorialHelper.GetActivityBoardEntry(self.m_activityType)
  if orderCell == nil or orderCell.gameObject == nil or orderCell.gameObject:IsNull() then
    self:Finish()
    return
  end
  TutorialHelper.ScrollToActivityEntry(self.m_activityType, false)
  local callback = function()
    self:Finish()
  end
  TutorialHelper.UpdateMaskOnBoard(orderCell.transform.position, orderCell.transform.sizeDelta, callback, false)
  local percent = 30
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText("coin_race_board_tutorial_2"), percent)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
