ItemDishDetailWindow = setmetatable({}, ItemDetailWindow)
ItemDishDetailWindow.__index = ItemDishDetailWindow

function ItemDishDetailWindow:_UpdateView(itemType, mode)
  self.m_arrTweens = {}
  self.mode = mode
  self.nextRefer = EItemDetailWindowRefer.DishDetailWin
  self.m_arrCells = {}
  self.m_titleText.text = GM.GameTextModel:GetText(ItemNameDefinition.GetName(itemType))
  local arrNeedMaterials = GM.ItemDataModel:GetMaterials(itemType, false, false)
  for _, material in ipairs(arrNeedMaterials) do
    self:_AddCell(material, true, false, true, true, self.m_materialRoot)
  end
  local mapCookingRecipes = {}
  local mapCookedRecipes = {}
  local arrMaterialCells = Table.ShallowCopy(self.m_arrCells)
  local unlockedInstrument, duration = GM.ItemDataModel:GetDishUnlockedInstru(itemType)
  local instrumentChain = GM.ItemDataModel:GetChainId(unlockedInstrument)
  local text = TimeUtil.ParseTimeDescription(duration)
  text = StringUtil.Replace(text, " ", "")
  self.m_cookTimeTxt.text = text
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_cookTimeTrans)
  local txtWidth = self.m_cookTimeTxt.preferredWidth > 80 and 80 or self.m_cookTimeTxt.preferredWidth
  UIUtil.SetSizeDelta(self.m_cookTimeTxt.transform, txtWidth)
  UIUtil.SetSizeDelta(self.m_cookTimeTrans, 28 + txtWidth)
  self.m_arrCells[#self.m_arrCells + 1] = self.m_instrument
  self.m_arrCells[#self.m_arrCells + 1] = self.m_dish
  self.m_instrument:Init(unlockedInstrument, false, false, true, true, self)
  self.m_dish:Init(itemType, false, false, true, false, self)
  self.m_dish:ShowAsSelected()
  self:_UpdateHotSaleDisplay()
  EventDispatcher.AddListener(EEventType.ShopBuyItemSuccess, self, self._UpdateHotSaleDisplay)
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self._UpdateHotSaleDisplay)
  if 4 < #arrNeedMaterials then
    UIUtil.AddSizeDelta(self.m_materialBgTrans, nil, 200)
    UIUtil.AddSizeDelta(self.m_contentRectTrans, nil, 200)
  end
  self:_UpdateTestMsg(itemType)
  if mode ~= ItemDetailWindowMode.Order then
    self.m_helpGo:SetActive(false)
    return
  end
  local boardModel = BoardModelHelper.GetActiveModel()
  local allCookCmp = boardModel:GetAllItemCookCmp()
  local cooked = false
  local cooking = false
  local inCookMaterials = {}
  for _, cookCmp in ipairs(allCookCmp) do
    local state = cookCmp:GetState()
    local recipe = cookCmp:GetRecipe()
    if not cooked and GM.ItemDataModel:GetChainId(cookCmp:GetItemModel():GetCode()) == instrumentChain then
      if state == EItemCookState.Cooked and recipe == itemType then
        cooked = true
      elseif state == EItemCookState.Cooking and recipe == itemType then
        cooking = true
      elseif state == EItemCookState.Prepare or state == EItemCookState.CanCook then
        local materials = cookCmp:GetCurMaterialsArray()
        for _, material in ipairs(materials) do
          if Table.ListContain(arrNeedMaterials, material) then
            inCookMaterials[material] = true
          end
        end
      end
    end
    if state == EItemCookState.Cooked then
      mapCookedRecipes[recipe] = true
    elseif state == EItemCookState.Cooking then
      mapCookingRecipes[recipe] = true
    end
  end
  self:_TryAddTipForDishMaterials(arrMaterialCells, mapCookingRecipes, mapCookedRecipes)
  if cooked then
    self.m_instrument:ChangeBg(EItemDetailCellBg.Yellow)
    self.m_dish:ChangeBg(EItemDetailCellBg.Yellow)
    self.m_cookedGo:SetActive(true)
    local cookedTween = ItemCookView.Shake(self.m_cookedGo.transform)
    self:_AddTween(cookedTween)
    self.m_cookingSpine.gameObject:SetActive(false)
    return
  elseif cooking then
    local spinePrefab = unlockedInstrument .. "_spine"
    if GM.DataResource.ScenePrefabConfig:HasConfig(spinePrefab) then
      GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(spinePrefab), self.m_instruBgImg.transform, Vector3.zero, function(go)
        self:_OnLoadItemSpine(go, itemType)
      end)
    end
    self.m_cookingSpine:Init()
    self.m_cookingSpine:PlayAnimation("cook", nil, true)
    return
  end
  local codeCountMap = boardModel:GetCodeCountMap(true, false, true)
  local boardCodeCountMap = boardModel:GetCodeCountMap(true, false, false)
  local cell
  for index, material in ipairs(arrNeedMaterials) do
    cell = self.m_arrCells[index]
    if inCookMaterials[material] ~= nil then
      cell:ChangeBg(EItemDetailCellBg.Yellow)
    elseif codeCountMap[material] ~= nil and OrderStateHelper.TryFillOrderRequirement(boardModel, material, 1) then
      cell:ChangeBg(EItemDetailCellBg.DarkGreen)
      local onBoard = boardCodeCountMap[material] ~= nil and OrderStateHelper.TryFillOrderRequirement(boardModel, material, 1, false)
      if not onBoard and not cell.hasCornerTip then
        local go = GameObject.Instantiate(self.m_inventoryTip, cell.transform)
        go:SetActive(true)
      end
    end
  end
end

function ItemDishDetailWindow:OnDestroy()
  ItemDetailWindow.OnDestroy(self)
  Scheduler.UnscheduleTarget(self)
  for _, tween in ipairs(self.m_arrTweens) do
    tween:Kill()
    tween = nil
  end
  self.m_arrTweens = {}
end

function ItemDishDetailWindow:OnCloseView()
  self:_ToggleItemSpine(false)
  ItemDetailWindow.OnCloseView(self)
end

function ItemDishDetailWindow:_AddTween(tween)
  self.m_arrTweens[#self.m_arrTweens + 1] = tween
end

function ItemDishDetailWindow:_OnLoadItemSpine(go, recipe)
  self.m_itemSpine = go:GetLuaTable()
  self.m_itemSpine:Init()
  UIUtil.UpdateSortingOrder(self.m_itemSpine.gameObject, self:GetSortingOrder() + 1)
  local uiLayer = LayerMask.NameToLayer("UI")
  self.m_itemSpine.transform:SetLayerRecursively(uiLayer)
  local origin, special = GM.ItemDataModel:GetCookAnimationName(recipe)
  local animName = self.m_itemSpine:HasAnimation(special) and special or origin
  self.m_itemSpine:PlayLoopAnimation(animName)
  self.m_itemSpine.gameObject:SetActive(false)
  DelayExecuteFuncInView(function()
    self:_ToggleItemSpine(true)
  end, 0.1, self, false)
end

function ItemDishDetailWindow:_ToggleItemSpine(visible)
  if not self.m_itemSpine then
    return
  end
  self.m_instruItemGo:SetActive(not visible)
  self.m_instruBgImg.enabled = not visible
  self.m_itemSpine.gameObject:SetActive(visible)
end

function ItemDishDetailWindow:_TryAddTipForDishMaterials(arrMaterialCells, mapCookingRecipes, mapCookedRecipes)
  for _, cell in ipairs(arrMaterialCells) do
    if GM.ItemDataModel:IsDishes(cell.itemType) then
      if mapCookedRecipes[cell.itemType] then
        local go = GameObject.Instantiate(self.m_cookedGo, cell.transform)
        go:SetActive(true)
        local cookedTween = ItemCookView.Shake(go.transform)
        self:_AddTween(cookedTween)
        cell.hasCornerTip = true
      elseif mapCookingRecipes[cell.itemType] then
        local go = GameObject.Instantiate(self.m_cookingSpine.gameObject, cell.transform)
        go:SetActive(true)
        local spine = go:GetLuaTable()
        spine:Init()
        spine:PlayAnimation("cook", nil, true)
        cell.hasCornerTip = true
      end
    end
  end
end

function ItemDishDetailWindow:_UpdateTestMsg(itemType)
  if not GM.UIManager:CanShowTestUI() then
    self.m_testText.gameObject:SetActive(false)
  else
    self.m_testText.gameObject:SetActive(true)
    local strInfo = string.format([[
%s
score: %s
gold: %s
]], itemType, GM.ItemDataModel:GetItemScore(itemType), GM.ItemDataModel:GetItemGoldNum(itemType))
    self.m_testText.text = strInfo
  end
end

function ItemDishDetailWindow:OnHelpClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.HowToCookWindow)
end

HowToCookWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  canClickWindowMask = true
}, BaseWindow)
HowToCookWindow.__index = HowToCookWindow
