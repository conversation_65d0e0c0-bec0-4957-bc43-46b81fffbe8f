BundleNormalModel = setmetatable({}, BundleActivityBaseModel)
BundleNormalModel.__index = BundleNormalModel

function BundleNormalModel:GetCurBundleData(dataGroup)
  return dataGroup:GetConfigData(dataGroup:GetBundleIds()[1])
end

function BundleNormalModel:BuyBundle(dataGroup, callback)
  local bundleData = self:GetCurBundleData(dataGroup)
  GM.BundleManager:Buy(self.m_bundleType, bundleData, callback)
end

function BundleNormalModel:GetCurBundleGemByDataGroup(dataGroup)
  if not dataGroup then
    return 0
  end
  local bundleData = self:GetCurBundleData(dataGroup)
  if bundleData then
    local gemNum = 0
    local goods = bundleData:GetGoods()
    for _, v in pairs(goods or {}) do
      if v[PROPERTY_TYPE] == EPropertyType.Gem then
        gemNum = gemNum + v[PROPERTY_COUNT]
      end
    end
    return gemNum
  end
  return 0
end
