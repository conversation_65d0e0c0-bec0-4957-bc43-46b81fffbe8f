RatePopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
RatePopupHelper.__index = RatePopupHelper

function RatePopupHelper.Create()
  local helper = setmetatable({}, RatePopupHelper)
  helper:Init()
  return helper
end

function RatePopupHelper:Init()
  BasePopupHelper.Init(self)
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self._OnMainTaskFinished)
  EventDispatcher.AddListener(EEventType.OnClaimedOrderGroupReward, self, self._OnClaimedOrderGroupReward)
end

function RatePopupHelper:_OnMainTaskFinished()
  self.m_needCheckPopup = true
end

function RatePopupHelper:_OnClaimedOrderGroupReward()
  self.m_needCheckPopup = true
end

function RatePopupHelper:CheckPopup()
  return GM.RateModel:CheckPopup()
end
