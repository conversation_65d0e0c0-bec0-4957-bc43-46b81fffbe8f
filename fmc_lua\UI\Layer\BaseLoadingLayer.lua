BaseLoadingLayer = {}
BaseLoadingLayer.__index = BaseLoadingLayer

function BaseLoadingLayer:Start()
  GM:CheckSceneChanged()
  CS.System.GC.Collect()
  CS.System.GC.WaitForPendingFinalizers()
  collectgarbage("collect")
  self.m_easeSlider = ApplicationManager.Instance:GetEaseSlider()
  self.m_infoDisplay = ApplicationManager.Instance:GetDebugInfo()
  self:_UpdateInfo()
end

function BaseLoadingLayer:_UpdateInfo()
  if self.m_infoDisplay and GM and GM.UserModel then
    self.m_infoDisplay:SetInfo("v" .. GameConfig.GetCurrentVersion() .. "\n" .. GM.UserModel:GetDisplayUserId())
  end
end

function BaseLoadingLayer:Update(dt)
  if GM.IsGameRestarting then
    return false
  end
  self:_UpdateLoadingProgress(dt)
  return true
end

function BaseLoadingLayer:_OpenNetworkErrorWindow(code)
  local callback = function()
    self.m_eStage = self.m_eStage - 1
    self.m_easeSlider:Resume()
    self:Update(0)
  end
  if self.m_infoDisplay then
    self.m_infoDisplay:SetErrorCode(tostring(code))
  end
  self.m_easeSlider:Pause()
  GM.UIManager:OpenView(UIPrefabConfigName.NetworkErrorWindow, callback)
end

local HoldProgress = 90

function BaseLoadingLayer:_UpdateLoadingProgress(dt)
  if dt == 0 then
    return
  end
  if self.m_eStage == nil then
    return
  end
  if self.m_eLastStage ~= self.m_eStage then
    if self.m_mapProgressGameTextKey[self.m_eStage] ~= nil then
      ApplicationManager.Instance:UpdateSliderDescText(GM.GameTextModel:GetText(self.m_mapProgressGameTextKey[self.m_eStage]))
    end
    self.m_eLastStage = self.m_eStage
  end
  self.m_easeSlider:SetValue(self:_GetTargetProgress())
  ApplicationManager.Instance:UpdateSliderText(math.ceil(self.m_easeSlider:GetValueForDisplay() * 100) .. "%")
  if self.m_updateProgressCheckFunc then
    self.m_updateProgressCheckFunc()
  end
end

function BaseLoadingLayer:_GetTargetProgress()
  local targetProgress
  local doneProgress = self.m_mapTargetProgress[self.m_eStage] or 100
  local nextProgress = self.m_mapTargetProgress[self.m_eStage + 1] or 100
  if nextProgress == doneProgress then
    targetProgress = doneProgress
  else
    local percent = GM.ResourceLoader:GetLoadingPercent()
    targetProgress = doneProgress + (nextProgress - doneProgress) * percent
  end
  return targetProgress * 0.01
end
