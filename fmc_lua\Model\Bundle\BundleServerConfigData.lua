local sec2Day = Sec2Day
local DEFAULT_POPUP_DAILY_COUNT = 1
BundleServerConfigData = {}
BundleServerConfigData.__index = BundleServerConfigData

function BundleServerConfigData.Create(configData, groupData)
  local data = setmetatable(Table.ShallowCopy(configData), BundleServerConfigData)
  data:Init(groupData)
  return data
end

function BundleServerConfigData:Init(groupData)
  self.groupData = groupData
  RewardApi.CryptRewards(self.content, true)
end

function BundleServerConfigData:GetBundleId()
  return self.bundleId
end

function BundleServerConfigData:GetPurchaseId()
  return self.payID
end

function BundleServerConfigData:GetGoods()
  return self.content
end

function BundleServerConfigData:GetDiscountTag()
  return self.discountTag
end

function BundleServerConfigData:GetGroupData()
  return self.groupData
end

BundleServerConfigDataGroup = {
  GiftContentKey = "bundleContent",
  ConfigDataClass = BundleServerConfigData
}
BundleServerConfigDataGroup.__index = BundleServerConfigDataGroup

function BundleServerConfigDataGroup.Create(groupData)
  local data = setmetatable({}, BundleServerConfigDataGroup)
  data:Init(groupData)
  return data
end

function BundleServerConfigDataGroup:Init(groupData)
  self.m_id = groupData.id
  self.groupId = groupData.groupId
  self.m_popOrder = groupData.popOrder
  self.m_eTime = groupData.eTime
  self.m_sTime = groupData.sTime
  self.bundleType = groupData.specialType
  self.m_order = groupData.order
  self.m_bundleUIType = groupData.uiCode
  self.m_maxBuyNum = groupData.maxBuyNum
  self.m_week = groupData.week
  self.m_dailyBuyNum = groupData.dailyBuyNum
  self.m_dailyShowNum = groupData.dailyShowNum
  self.m_duration = groupData.duration and groupData.duration * 60 or 0
  self.m_buyCD = groupData.buyCD and groupData.buyCD * 60 or nil
  self.m_popCD = groupData.popCD and groupData.popCD * 60 or nil
  self.m_generalBundleConf = {}
  local arrGeneralConf = groupData.generalBundleConf
  if not Table.IsEmpty(arrGeneralConf) then
    for _, conf in ipairs(arrGeneralConf) do
      Log.Assert(self.m_generalBundleConf[conf.confType] == nil, "Same confType overwrite!" .. tostring(conf.confType))
      self.m_generalBundleConf[conf.confType] = conf
    end
  end
  if groupData.bundleCondition then
    self.m_conditionData = BundleConditionData.Create(groupData.bundleCondition[1])
  end
  local mapTrigger = {}
  if not Table.IsEmpty(groupData.bundleTrigger) then
    self.m_triggerDatas = {}
    for _, config in ipairs(groupData.bundleTrigger) do
      table.insert(self.m_triggerDatas, BundleTriggerData.Create(config, self))
      if not mapTrigger[config.trigger] then
        mapTrigger[config.trigger] = true
      elseif GameConfig.IsTestMode() then
        Log.Error("礼包trigger有重复, groupId:" .. self.groupId .. " trigger:" .. config.trigger)
      end
    end
  end
  self:_InitConfigDatas(groupData)
  if GameConfig.IsTestMode() and self.m_bundleUIType ~= nil and BundleUIType[self.m_bundleUIType] ~= nil then
    local uiDefinition = BundleUIType[self.m_bundleUIType]
    if uiDefinition.bundleType ~= self.bundleType then
      Log.Error("[BundleConfig] uicode config error, specialType and uicode do not match!!! group:" .. self:GetGroupId())
    end
    local maxRewardCount = uiDefinition.maxRewardCount
    if maxRewardCount ~= nil then
      local maxCount = 0
      for _, bundleData in pairs(self.m_mapConfigData) do
        maxCount = math.max(maxCount, Table.IsEmpty(bundleData.content) and 0 or #bundleData.content)
      end
      if maxRewardCount < maxCount then
        Log.Error(string.format("[BundleConfig] BundleContent配置出错, 当前皮肤下奖励最多只允许%d个 groupId:%s", maxRewardCount, self:GetGroupId()))
      end
    end
    local maxBundleCount = uiDefinition.maxBundleCount
    if maxBundleCount and maxBundleCount < #self:GetBundleIds() then
      Log.Error(string.format("[BundleConfig] BundleContent配置出错, 当前皮肤下礼包最多只允许%d个 groupId:%s", maxBundleCount, self:GetGroupId()))
    end
  end
end

function BundleServerConfigDataGroup:_InitConfigDatas(groupData)
  self.m_mapConfigData = {}
  self.m_arrBundleIds = {}
  local configData
  for _, data in ipairs(groupData[self.GiftContentKey]) do
    configData = self.ConfigDataClass.Create(data, self)
    self.m_mapConfigData[configData:GetBundleId()] = configData
  end
  local count = 1
  for _, id in ipairs(groupData.order or {}) do
    if self.m_mapConfigData[id] then
      self.m_arrBundleIds[count] = id
      count = count + 1
    elseif GameConfig.IsTestMode() then
      Log.Error("礼包配置缺失 : " .. id .. ", type:" .. (self:GetBundleType() or "unKnown"))
    end
  end
end

function BundleServerConfigDataGroup:GetId()
  return self.m_id
end

function BundleServerConfigDataGroup:GetGroupId()
  return self.groupId
end

function BundleServerConfigDataGroup:GetBundleIds()
  return self.m_arrBundleIds
end

function BundleServerConfigDataGroup:GetBundleType()
  return self.bundleType
end

function BundleServerConfigDataGroup:GetConfigData(bundleId)
  return self.m_mapConfigData[bundleId]
end

function BundleServerConfigDataGroup:GetConfigDataIndexByBundleId(bundleId)
  return Table.GetIndex(self.m_arrBundleIds, bundleId)
end

function BundleServerConfigDataGroup:GetConfigDataByPurchaseId(purchaseId)
  for _, configData in pairs(self.m_mapConfigData) do
    if configData:GetPurchaseId() == purchaseId then
      return configData
    end
  end
  return nil
end

function BundleServerConfigDataGroup:GetLastDuration()
  return self.m_duration
end

function BundleServerConfigDataGroup:IsDisposable()
  return self.m_duration == 0
end

function BundleServerConfigDataGroup:GetBundleUIType()
  return self.m_bundleUIType
end

function BundleServerConfigDataGroup:GetMaxBuyNum()
  return self.m_maxBuyNum
end

function BundleServerConfigDataGroup:GetDailyBuyNum()
  return self.m_dailyBuyNum
end

function BundleServerConfigDataGroup:GetDailyShowNum()
  return self.m_dailyShowNum
end

function BundleServerConfigDataGroup:GetBuyCD()
  return self.m_buyCD
end

function BundleServerConfigDataGroup:GetPopCD()
  return self.m_popCD
end

function BundleServerConfigDataGroup:GetBundleEndTime()
  return self.m_eTime
end

function BundleServerConfigDataGroup:GetWeekConfig()
  return self.m_week
end

function BundleServerConfigDataGroup:GetPopOrder()
  return self.m_popOrder or 0
end

function BundleServerConfigDataGroup:GetConditionData()
  return self.m_conditionData
end

function BundleServerConfigDataGroup:GetGeneralConfig(configType, eGeneralConfigParam)
  Log.Assert(eGeneralConfigParam ~= nil, "BundleServerConfigData:GetGeneralConfig() eGeneralConfigParam is nil")
  return self.m_generalBundleConf and self.m_generalBundleConf[configType] and self.m_generalBundleConf[configType][eGeneralConfigParam]
end

function BundleServerConfigDataGroup:CanTrigger(eTriggerType, triggerArg, bundleModel)
  if Table.IsEmpty(self.m_triggerDatas) then
    return false
  end
  for _, triggerData in ipairs(self.m_triggerDatas) do
    if triggerData:CanTrigger(eTriggerType, triggerArg, bundleModel) then
      return true
    end
  end
  return false
end

function BundleServerConfigDataGroup:CanPopup(eTriggerType, hasTriggerNum)
  if Table.IsEmpty(self.m_triggerDatas) then
    return false
  end
  for _, triggerData in ipairs(self.m_triggerDatas) do
    if triggerData:CanPopup(eTriggerType, hasTriggerNum) then
      return true
    end
  end
  return false
end

function BundleServerConfigDataGroup:GetPopOrderConfig(eTriggerType)
  local defaultNum = eTriggerType == EBundleTriggerType.Login and 2 or 1
  local triggerData = self:GetTargetTriggerData(eTriggerType)
  if triggerData ~= nil then
    return {
      popOrder = triggerData.popOrder,
      popNum = triggerData.popNum
    }
  end
end

function BundleServerConfigDataGroup:GetTargetTriggerData(eTriggerType)
  for _, triggerData in ipairs(self.m_triggerDatas or {}) do
    if triggerData.trigger == eTriggerType then
      return triggerData
    end
  end
end
