TaskWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
TaskWindow.__index = TaskWindow
ETaskWindowRefer = {
  TaskButton = 1,
  RoomBubble = 2,
  BoardBubble = 3
}

function TaskWindow:BeforeOpenCheck()
  return GM.SceneManager:GetGameMode() == EGameMode.Main and not GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.TaskWindow) and GM.ChapterManager.curActiveChapterName == GM.TaskManager:GetOngoingChapterName() and not GM.TimelineManager:IsPlayingTimeline() and GM.TaskManager:GetOngoingTask() ~= nil
end

function TaskWindow:Init(refer)
  self:LogWindowAction(EBIType.UIActionType.Open, refer)
  self.taskId = GM.TaskManager:GetOngoingTask()
  self.roomModel = GM.ChapterManager.roomModel
  local iconKey = self.roomModel.chapterName .. "_task_icon_" .. self.taskId
  if ImageFileConfigName.HasConfig(iconKey) then
    SpriteUtil.SetImage(self.m_iconImg, iconKey)
  end
  self.m_costText.text = GM.TaskManager:GetTaskCost(self.taskId)
  local rewards = GM.TaskManager:GetTaskRewards(self.taskId, true)
  self.m_layoutGroup.spacing = Vector2(4 <= #rewards and 20 or 50, 0)
  self.m_rewardContent:Init(rewards)
  GM.ItemDataModel:SetRewardsLocked(rewards)
  local finished, total = GM.TaskManager:GetProgressRewardProgress()
  self.m_progressText.text = tostring(finished) .. "/" .. tostring(0 < total and total or "MAX")
  self.m_progressSlider.value = 0 < total and finished / total or 1
  local progressRewards = GM.TaskManager:GetProgressReward()
  GM.ItemDataModel:SetRewardsLocked(progressRewards)
  local showTestInfo = GM.UIManager:CanShowTestUI()
  self.m_testText.gameObject:SetActive(showTestInfo)
  if showTestInfo then
    local strTest = "TASKID:" .. self.taskId .. "  "
    local slotChanges = GM.TaskManager:GetTaskSlotsChanges(self.taskId)
    for _, changeData in ipairs(slotChanges) do
      strTest = strTest .. changeData.Slot .. ":" .. changeData.State .. ", "
    end
    strTest = strTest .. [[

GROUPID:]] .. GM.TaskManager:GetOngoingGroupId()
    strTest = strTest .. [[

CLEAN_COST:]] .. GM.TaskManager:GetTaskCleanGoldCost(self.taskId)
    strTest = strTest .. [[

EXP:]] .. GM.TaskManager:GetExpRewardCount(self.taskId)
    self.m_testText.text = strTest
  end
  UIUtil.SetActive(self.m_rewardTip.gameObject, false)
  if IsAutoRun() then
    DOVirtual.DelayedCall(0.2, function()
      self:OnGoButtonClicked()
    end)
  end
end

function TaskWindow:OnGoButtonClicked()
  self:HideRewardTip(true)
  EventDispatcher.DispatchEvent(EEventType.TaskGoClicked)
  if GM.ChapterManager.curActiveChapterName ~= GM.TaskManager:GetOngoingChapterName() then
    self:Close()
    return
  end
  if not self:HasEnoughGold() then
    GM.UIManager:OpenView(UIPrefabConfigName.GoPlayWindow)
    self:Close()
  elseif GM.TaskManager:GoFinishTask(self.taskId) then
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxTaskStart)
    self:Close()
  end
end

function TaskWindow:HasEnoughGold()
  local coins = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gold)
  local cost = GM.TaskManager:GetTaskCost(self.taskId, self.roomModel.chapterName)
  return coins >= cost
end

function TaskWindow:GetGoBtnTrans()
  return self.m_goBtnTrans
end

function TaskWindow:OnBoxClick()
  local rewards = GM.TaskManager:GetProgressReward()
  self.m_rewardTip:Show(rewards, self.m_boxTrans, -4, 42.5)
end

function TaskWindow:HideRewardTip(bIgnoreAnim)
  self.m_rewardTip:Hide(bIgnoreAnim)
end

function TaskWindow:OnMaskClicked()
  self:HideRewardTip()
end
