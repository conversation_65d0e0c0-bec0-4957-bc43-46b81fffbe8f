TestOrderInfoCell = {}
TestOrderInfoCell.__index = TestOrderInfoCell

function TestOrderInfoCell:Init(arrRequirementData)
  self.arrRequirementData = arrRequirementData
  arrRequirementData = arrRequirementData or {}
  self.m_requirementGo1:SetActive(arrRequirementData[1] and not StringUtil.IsNilOrEmpty(arrRequirementData[1].Type))
  if self.m_requirementGo1.activeSelf then
    if arrRequirementData[1].Type == CLEAN_ITEM_CODE then
      SpriteUtil.SetImage(self.m_requirementImg1, ImageFileConfigName.common_btn_help, false)
    else
      SpriteUtil.SetImage(self.m_requirementImg1, arrRequirementData[1].Type, true)
    end
    self.m_requirementCount1.text = arrRequirementData[1].Count
  end
  self.m_requirementGo2:SetActive(arrRequirementData[2] and not StringUtil.IsNilOrEmpty(arrRequirementData[2].Type))
  if self.m_requirementGo2.activeSelf then
    if arrRequirementData[2].Type == CLEAN_ITEM_CODE then
      SpriteUtil.SetImage(self.m_requirementImg2, ImageFileConfigName.common_btn_help, false)
    else
      SpriteUtil.SetImage(self.m_requirementImg2, arrRequirementData[2].Type, true)
    end
    self.m_requirementCount2.text = arrRequirementData[2].Count
  end
end

function TestOrderInfoCell:IsEmpty()
  if (not self.arrRequirementData[1] or StringUtil.IsNilOrEmpty(self.arrRequirementData[1].Type)) and (not self.arrRequirementData[2] or StringUtil.IsNilOrEmpty(self.arrRequirementData[2].Type)) then
    return true
  end
  return false
end

function TestOrderInfoCell:IsDataValid()
  if not self.arrRequirementData then
    return false
  end
  if self:IsEmpty() then
    return false
  end
  if self.arrRequirementData[1] and self.arrRequirementData[1].Type == CLEAN_ITEM_CODE and 1 < self.arrRequirementData[1].Count then
    return false, "清场棋子数量只能为一"
  end
  if self.arrRequirementData[2] and self.arrRequirementData[2].Type == CLEAN_ITEM_CODE and 1 < self.arrRequirementData[2].Count then
    return false, "清场棋子数量只能为一"
  end
  return true
end
