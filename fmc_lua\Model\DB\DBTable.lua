local next = next
local DEFAULTCOLUMNS = {
  key = EDBValueType.Text,
  value = EDBValueType.Text
}
local DEFAULTPRIMARYKEY = "key"
DB_VALUE_KEY = "value"
local DEFAULTARRORDER = {
  "key",
  DB_VALUE_KEY
}
local MODIFIED_FLAG = 1
local NOT_MODIFIED_FLAG = 0
DBTable = setmetatable({}, BaseDBTable)
DBTable.__index = DBTable

function DBTable:Init(tableConfig)
  self.tableName = tableConfig.name
  self.m_bIgnoreTypeCheck = tableConfig.ignoreTypeCheck
  self.m_bIgnoreValueCommaCheck = tableConfig.ignoreValueCommaCheck
  self.tableConfig = tableConfig
  self.m_strQuerySql = "select * from " .. self.tableName
  self.m_strSerializationKey = EPlayerPrefKey.TableLastSerializationTimePrefix .. self.tableName
  self.m_strModificationKey = EPlayerPrefKey.TableModificationPrefix .. self.tableName
  self.m_strHasDataKey = EPlayerPrefKey.TableHasDataPrefix .. self.tableName
  self.lastSerializationTimestamp = PlayerPrefs.GetInt(self.m_strSerializationKey, 0)
  self.m_modificationFlag = PlayerPrefs.GetInt(self.m_strModificationKey, MODIFIED_FLAG)
  self.m_mapCacheValue = {}
  self.m_mapDeleteRows = {}
  self.m_mapInsertRows = {}
  self.m_mapUpdateRows = {}
  self.m_bClearTable = false
  self.m_bCachedDataChanged = false
  self.m_nSequence = 0
  self.m_arrVisit = {}
  self._arrOrder = self.tableConfig.arrOrder or DEFAULTARRORDER
  self._columns = self.tableConfig.columns or DEFAULTCOLUMNS
  self._primaryKey = self.tableConfig.primaryKey or {DEFAULTPRIMARYKEY}
  for i = 1, #self._primaryKey do
    local value = self._primaryKey[i]
    if IsString(value) and string.find(value, ",") ~= nil then
      Log.Error(self.tableName .. " primary Key has invalid value contains ',' which is not allowed, " .. value)
    end
  end
  self.m_arrColsNoPK = {}
  for field, _ in pairs(self._columns) do
    if not self:_IsPrimaryKey(field) then
      self.m_arrColsNoPK[#self.m_arrColsNoPK + 1] = field
    end
  end
end

function DBTable:GetRows(...)
  local args = {
    ...
  }
  assert(#args <= #self._primaryKey)
  return self:_GetRows(args)
end

function DBTable:GetValue(...)
  local args = {
    ...
  }
  assert(#args == #self._primaryKey + 1)
  local cache = self:_GetRows(args)
  if cache ~= nil then
    return cache[args[#args]]
  end
  return self:_GetFieldValue(args[#args])
end

function DBTable:GetValueInNumber(...)
  local value = self:GetValue(...)
  return tonumber(value) or 0
end

function DBTable:GetValues()
  return Table.DeepCopy(self.m_mapCacheValue)
end

function DBTable:Get(...)
  local args = {
    ...
  }
  assert(#args > #self._primaryKey)
  local cache = self:_GetRows(args)
  if cache == nil then
    return nil
  end
  local data = {}
  for i = #self._primaryKey + 1, #args do
    data[args[i]] = cache[args[i]]
  end
  return data
end

function DBTable:HasRow(...)
  return self:GetRows(...) ~= nil
end

function DBTable:HasColumn(...)
  local args = {
    ...
  }
  assert(#args == #self._primaryKey + 1)
  local cache = self:_GetRows(args)
  if cache == nil then
    return false
  end
  return cache[args[#args]] ~= nil
end

function DBTable:Set(...)
  local args = {
    ...
  }
  assert(#args > #self._primaryKey)
  if not self.m_bIgnoreValueCommaCheck then
    for i = #self._primaryKey + 1, #args do
      local value = args[i]
      if IsString(value) and string.find(value, ",") ~= nil then
        Log.Error(self.tableName .. " set invalid value contains ',' which is not allowed, " .. value)
      end
    end
  end
  local cache = self:_GetRows(args)
  if cache == nil then
    self:_Insert(args)
  else
    self:_Update(cache, args)
  end
end

function DBTable:BatchSet(mapContent)
  self:_Foreach(mapContent, function(depth, data)
    if depth < #self._primaryKey then
      return false
    end
    for field, value in pairs(data) do
      depth = depth + 1
      self.m_arrVisit[depth] = field
      depth = depth + 1
      self.m_arrVisit[depth] = value
    end
    if depth > #self._primaryKey then
      self:Set(table.unpack(self.m_arrVisit))
    end
    return true
  end)
end

function DBTable:Remove(...)
  local args = {
    ...
  }
  if #args <= #self._primaryKey then
    self:_Delete(args)
    if self:IsEmpty() then
      self:_UpdateWhenEmpty()
      self.m_bCachedDataChanged = true
    end
    return
  end
  local cache = self:_GetRows(args)
  if cache == nil then
    return
  end
  for i = #args, #self._primaryKey + 2, -1 do
    args[2 * i - #self._primaryKey - 1] = args[i]
    args[2 * i - #self._primaryKey - 2] = nil
  end
  self:_Update(cache, args)
end

function DBTable:GetAllInTable()
  return self:GetValues()
end

function DBTable:IsEmpty()
  return next(self.m_mapCacheValue) == nil
end

function DBTable:ClearContent()
  if next(self.m_mapCacheValue) ~= nil then
    self.m_mapCacheValue = {}
  end
end

function DBTable:ConfirmDataSynced()
  if next(self.m_mapDeleteRows) ~= nil then
    self.m_mapDeleteRows = {}
  end
  if next(self.m_mapInsertRows) ~= nil then
    self.m_mapInsertRows = {}
  end
  if next(self.m_mapUpdateRows) ~= nil then
    self.m_mapUpdateRows = {}
  end
  self.m_bClearTable = false
end

function DBTable:Clear()
  local bClear = false
  if next(self.m_mapCacheValue) ~= nil then
    self.m_mapCacheValue = {}
    bClear = true
  end
  if next(self.m_mapDeleteRows) ~= nil then
    self.m_mapDeleteRows = {}
    bClear = true
  end
  if next(self.m_mapInsertRows) ~= nil then
    self.m_mapInsertRows = {}
    bClear = true
  end
  if next(self.m_mapUpdateRows) ~= nil then
    self.m_mapUpdateRows = {}
    bClear = true
  end
  if bClear then
    self.m_bClearTable = true
    self:_UpdateWhenEmpty()
    self.m_bCachedDataChanged = true
  end
end

function DBTable:_UpdateWhenEmpty()
  self:_OnContentUpdated("{}")
  self:_ResetPlayerPrefs()
end

function DBTable:_ResetPlayerPrefs()
  self.lastSerializationTimestamp = 0
  PlayerPrefs.SetInt(self.m_strSerializationKey, self.lastSerializationTimestamp)
  PlayerPrefs.SetInt(self.m_strHasDataKey, 0)
end

function DBTable:ToJson(isAlwaysSerialize)
  if not self.m_strJson then
    self.m_strJson = json.encode(self.m_mapCacheValue)
    if self:IsEmpty() and self.lastSerializationTimestamp > 0 and not isAlwaysSerialize then
      local strLog = self.tableName .. " is Empty"
      GM.BIManager:LogErrorInfo(EBIType.DataCracked, strLog)
    end
  end
  return self.m_strJson
end

function DBTable:FromJson(strJson)
  self:Clear()
  strJson = strJson or ""
  local tbContent = json.decode(strJson)
  if strJson ~= "" and tbContent == nil then
    local strLog = self.tableName .. " Server Data Cracked : " .. strJson
    GM.BIManager:LogErrorInfo(EBIType.DataCracked, strLog)
  end
  tbContent = tbContent or {}
  self:BatchSet(tbContent)
  if Table.IsEmpty(tbContent) then
    self:_ResetPlayerPrefs()
  end
end

function DBTable:ToUploadArr()
  local concatTable = {}
  local arrCount = 1
  local arr = {}
  self:_Foreach(self.m_mapCacheValue, function(depth, data)
    if depth < #self._primaryKey then
      return false
    end
    for i = 1, #self._arrOrder do
      concatTable[i] = data[self._arrOrder[i]] or ""
    end
    arr[arrCount] = table.concat(concatTable, ",")
    arrCount = arrCount + 1
    return true
  end)
  return arr
end

function DBTable:FromArr(strArr)
  self:Clear()
  local datas, value, type
  for _, str in ipairs(strArr) do
    datas = StringUtil.Split(str, ",")
    local row = {}
    if #datas > #self._arrOrder then
      if GM and GM.BIManager then
        GM.BIManager:LogErrorInfo(EBIProjectType.DBColumnOverflow, "tableName:" .. self.tableName .. "str:" .. str)
      end
      datas[#self._arrOrder] = table.concat(datas, ",", #self._arrOrder)
      for i = #self._arrOrder + 1, #datas do
        datas[i] = nil
      end
    end
    for i = 1, #datas do
      value = datas[i]
      type = self._columns[self._arrOrder[i]]
      if type == EDBValueType.Integer or type == EDBValueType.Real then
        value = tonumber(value)
      end
      row[self._arrOrder[i]] = value
    end
    local args = {}
    for i = 1, #self._primaryKey do
      args[#args + 1] = row[self._primaryKey[i]]
      row[self._primaryKey[i]] = nil
    end
    for field, value in pairs(row) do
      args[#args + 1] = field
      args[#args + 1] = value
    end
    self:_Insert(args)
  end
  if self:IsEmpty() then
    self:_ResetPlayerPrefs()
  end
end

function DBTable:CheckContent(isAlwaysSerialize)
  if self.m_bCachedDataChanged or isAlwaysSerialize then
    GM.DBTableManager:SetData(self, isAlwaysSerialize)
    self:_OnContentUpdated()
  end
end

function DBTable:_OnContentUpdated()
  self.m_bCachedDataChanged = false
  self.lastSerializationTimestamp = TimeUtil.GetTimeInSecond()
  self:SetModified()
  if self.m_bUploading then
    self.m_bModifiedDuringUploading = true
  end
  self.m_strJson = nil
  PlayerPrefs.SetInt(self.m_strSerializationKey, self.lastSerializationTimestamp)
  self:_CheckAndSetHasData()
end

function DBTable:_CheckAndSetHasData()
  PlayerPrefs.SetInt(self.m_strHasDataKey, self:IsEmpty() and 0 or 1)
end

function DBTable:IsCracked()
  if self:IsEmpty() and PlayerPrefs.GetInt(self.m_strHasDataKey, 0) == 1 then
    GM.BIManager:LogErrorInfo(EBIType.DataCracked, self.tableName .. " is empty now")
    return true
  end
  return false
end

function DBTable:IsModified()
  return self.m_modificationFlag == MODIFIED_FLAG
end

function DBTable:SetModified()
  self.m_modificationFlag = MODIFIED_FLAG
  PlayerPrefs.SetInt(self.m_strModificationKey, self.m_modificationFlag)
end

function DBTable:SetUploading()
  self.m_bUploading = true
end

function DBTable:OnUploadedFinished(forceSynced)
  if not self.m_bUploading and not forceSynced then
    return
  end
  self.m_bUploading = false
  self.m_modificationFlag = not (not self.m_bModifiedDuringUploading or forceSynced) and MODIFIED_FLAG or NOT_MODIFIED_FLAG
  self.m_bModifiedDuringUploading = false
  PlayerPrefs.SetInt(self.m_strModificationKey, self.m_modificationFlag)
end

function DBTable:_IsPrimaryKey(field)
  for i = 1, #self._primaryKey do
    if self._primaryKey[i] == field then
      return true
    end
  end
  return false
end

function DBTable:_GetRows(args)
  return DBTable._GetMap(self.m_mapCacheValue, args, math.min(#args, #self._primaryKey))
end

function DBTable._GetMap(cache, args, depth)
  for i = 1, depth do
    cache = cache[args[i]]
    if cache == nil then
      return nil
    end
  end
  return cache
end

function DBTable._AddMap(cache, args, depth)
  for i = 1, depth do
    if cache[args[i]] == nil then
      cache[args[i]] = {}
    end
    cache = cache[args[i]]
  end
  return cache
end

function DBTable:_Insert(args)
  local cache = DBTable._AddMap(self.m_mapCacheValue, args, #self._primaryKey)
  self.m_bCachedDataChanged = true
  for i = 1, #self._primaryKey do
    cache[self._primaryKey[i]] = args[i]
  end
  for i = 1, #self.m_arrColsNoPK do
    cache[self.m_arrColsNoPK[i]] = self:_GetFieldValue(self.m_arrColsNoPK[i])
  end
  for i = #self._primaryKey + 1, #args, 2 do
    self:_Reset(cache, args[i], args[i + 1])
  end
  DBTable._AddMap(self.m_mapInsertRows, args, #self._primaryKey)
end

function DBTable:_Update(cache, args)
  local arrChanged = {}
  for i = #self._primaryKey + 1, #args, 2 do
    if self:_Reset(cache, args[i], args[i + 1]) then
      arrChanged[#arrChanged + 1] = args[i]
    end
  end
  if #arrChanged == 0 then
    return
  end
  if DBTable._GetMap(self.m_mapInsertRows, args, #self._primaryKey) ~= nil then
    return
  end
  local update = DBTable._AddMap(self.m_mapUpdateRows, args, #self._primaryKey)
  for i = 1, #arrChanged do
    update[arrChanged[i]] = true
  end
  self.m_bCachedDataChanged = true
end

function DBTable:_Delete(args)
  if not DBTable._ClearRows(self.m_mapCacheValue, args) then
    return
  end
  self.m_bCachedDataChanged = true
  DBTable._ClearRows(self.m_mapInsertRows, args)
  DBTable._ClearRows(self.m_mapUpdateRows, args)
  if self.m_bClearTable then
    return
  end
  local i = 1
  local last
  local delete = self.m_mapDeleteRows
  while i <= #args and delete[args[i]] ~= nil do
    last = delete
    delete = delete[args[i]]
    if next(delete) == nil then
      return
    end
    i = i + 1
  end
  if i > #args then
    last[args[#args]] = {}
    return
  end
  while i <= #args do
    delete[args[i]] = {}
    delete = delete[args[i]]
    i = i + 1
  end
end

function DBTable._ClearRows(data, args)
  local path = {}
  for i = 1, #args do
    if data[args[i]] == nil then
      return false
    end
    path[#path + 1] = data
    data = data[args[i]]
  end
  path[#args][args[#args]] = nil
  for i = #args - 1, 1, -1 do
    if next(path[i][args[i]]) ~= nil then
      return true
    end
    path[i][args[i]] = nil
  end
  return true
end

function DBTable:_Reset(cache, field, value)
  if not self.m_bIgnoreTypeCheck and not self.IsValidValueType(self._columns[field], value) then
    Log.Warning(self.tableName .. "表" .. field .. "列值类型错误,异常值=" .. tostring(value))
  end
  value = self:_GetFieldValue(self._columns[field], value)
  if cache[field] ~= value then
    self.m_bCachedDataChanged = true
    cache[field] = value
    return true
  end
  return false
end

function DBTable:_Foreach(cache, handle)
  DBTable._ForeachEntry(cache, 1, self.m_arrVisit, handle)
end

function DBTable._ForeachEntry(cache, i, visit, handle)
  for key, value in pairs(cache) do
    visit[i] = key
    if not handle(i, value) then
      DBTable._ForeachEntry(value, i + 1, visit, handle)
    end
    while i <= #visit do
      visit[#visit] = nil
    end
  end
end

function DBTable:_GetFieldValue(field, value)
  if value == nil and self._columns[field] == EDBValueType.Text then
    value = ""
  end
  return value
end

function DBTable:LoadData()
  local query = GM.DatabaseModel:DatabaseQuery(self.m_strQuerySql)
  if query.Error ~= 0 then
    query:Release()
    return false
  end
  for i = 0, query.Rows - 1 do
    local data = {}
    for j = 0, query.Names.Count - 1 do
      data[query.Names[j]] = self:_GetFieldValue(query.Names[j], query:GetData(i, j))
    end
    local args = {}
    for j = 1, #self._primaryKey do
      args[#args + 1] = data[self._primaryKey[j]]
    end
    local cache = DBTable._AddMap(self.m_mapCacheValue, args, #self._primaryKey)
    for field, value in pairs(data) do
      cache[field] = value
    end
  end
  query:Release()
  return true
end

function DBTable:CreateTable()
  local columnTable = {}
  for field, type in pairs(self._columns) do
    columnTable[#columnTable + 1] = GM.DatabaseModel:CreateColumn(field, type, self:_IsPrimaryKey(field))
  end
  return GM.DatabaseModel:CreateDatabaseTable(self.tableName, columnTable)
end
