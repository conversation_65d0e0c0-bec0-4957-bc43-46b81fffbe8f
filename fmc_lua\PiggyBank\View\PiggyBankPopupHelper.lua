PiggyBankPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
PiggyBankPopupHelper.__index = PiggyBankPopupHelper

function PiggyBankPopupHelper.Create()
  local helper = setmetatable({}, PiggyBankPopupHelper)
  helper:Init()
  return helper
end

function PiggyBankPopupHelper:Init()
  BasePopupHelper.Init(self)
  self.m_model = GM.ActivityManager:GetModel(ActivityType.PiggyBank)
  EventDispatcher.AddListener(EEventType.PiggyBankStateChanged, self, self._OnStateChanged)
end

function PiggyBankPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function PiggyBankPopupHelper:CheckPopup()
  local state = self.m_model:GetState()
  if state == ActivityState.Started and self.m_model:IsActivityOpen() then
    if not self.m_model:HasWindowOpenedOnce(ActivityState.Started) then
      return UIPrefabConfigName.PiggyBankMainWindow
    end
  elseif state == ActivityState.Ended and self.m_model:CanBuy() and not self.m_model:HasWindowOpenedOnce(ActivityState.Ended) then
    return UIPrefabConfigName.PiggyBankMainWindow
  end
  return nil
end
