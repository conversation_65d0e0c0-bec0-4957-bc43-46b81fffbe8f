TutorialItemLayerModel = setmetatable({}, BaseItemLayerModel)
TutorialItemLayerModel.__index = TutorialItemLayerModel

function TutorialItemLayerModel.Create(boardModel)
  local itemLayerModel = setmetatable({}, TutorialItemLayerModel)
  itemLayerModel:Init(boardModel)
  return itemLayerModel
end

function TutorialItemLayerModel:GetItem(position)
  return self.m_items:GetValueOnPosition(position)
end

function TutorialItemLayerModel:SetItem(position, item)
  self.m_items:SetValueOnPosition(position, item)
end
