CoconutTrunkCell = {}
CoconutTrunkCell.__index = CoconutTrunkCell

function CoconutTrunkCell:Init(config)
  if tonumber(config.skin) ~= 1 then
    self.m_bracketImage.sprite = self["m_bracketSprite" .. config.skin]
    self.m_rewardBackgroundLeftImage.sprite = self["m_rewardBackgroundSprite" .. config.skin]
    SpriteUtil.SetNativeSize(self.m_rewardBackgroundLeftImage)
    self.m_rewardBackgroundRightImage.sprite = self["m_rewardBackgroundSprite" .. config.skin]
    SpriteUtil.SetNativeSize(self.m_rewardBackgroundRightImage)
  end
  if config.skin == 2 then
    UIUtil.SetAnchoredPosition(self.m_rewardNode, nil, 12)
    UIUtil.SetAnchoredPosition(self.m_shadowNode, -158, 0)
  elseif config.skin == 3 then
    UIUtil.SetAnchoredPosition(self.m_rewardNode, nil, 24)
    UIUtil.SetAnchoredPosition(self.m_shadowNode, -170, 15)
  end
  if #config.rewards == 1 then
    self.m_rewardImage1.transform.anchoredPosition = Vector2(0, -12)
    self.m_rewardImage1.transform.localScale = Vector3(1.1, 1.1, 1)
  elseif #config.rewards == 2 then
    self.m_rewardImage1.transform.anchoredPosition = Vector2(-40, -12)
    self.m_rewardImage2.transform.anchoredPosition = Vector2(40, -12)
  end
  for index, reward in ipairs(config.rewards) do
    local image = self["m_rewardImage" .. index]
    image.gameObject:SetActive(true)
    local spriteName = ConfigUtil.GetCurrencyImageName(reward)
    SpriteUtil.SetImage(image, spriteName, true)
  end
end

function CoconutTrunkCell:SetLevelFinished()
  self.m_rewardNode.gameObject:SetActive(false)
end

function CoconutTrunkCell:GetRewardTransform(index)
  return self["m_rewardImage" .. index].transform
end
