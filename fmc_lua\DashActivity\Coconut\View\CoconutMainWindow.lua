CoconutMainWindow = setmetatable({}, CoconutBaseWindow)
CoconutMainWindow.__index = CoconutMainWindow

function CoconutMainWindow:Init(autoOpen)
  CoconutBaseWindow.Init(self, autoOpen)
  self.m_layer1 = self.m_sceneLayer1.gameObject:GetLuaTable()
  self.m_layer1:Init()
  local level = self.m_model:GetLevel()
  local levelConfig = self.m_model:GetLevelConfigs()[level]
  if levelConfig == nil then
    self.m_countdownGo:SetActive(false)
    self.m_buttonGo:SetActive(false)
    self.m_descriptionText.text = GM.GameTextModel:GetText("activity_coconut_end")
    UIUtil.SetAnchoredPosition(self.m_descriptionText.transform, nil, 26)
  elseif self.m_model:GetState() ~= ActivityState.Started then
    self.m_countdownGo:SetActive(false)
    self.m_buttonGo:SetActive(false)
    self.m_descriptionText.text = GM.GameTextModel:GetText("activity_coconut_lose")
    UIUtil.SetAnchoredPosition(self.m_descriptionText.transform, nil, 26)
  end
  self:UpdatePerSecond()
end

function CoconutMainWindow:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  local nextStateTime = self.m_model:GetNextStateTime()
  if nextStateTime ~= nil then
    local delta = nextStateTime - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function CoconutMainWindow:PlayTutorialAnimation(onComplete)
  self.m_sceneScrollRect.verticalNormalizedPosition = 1
  local sequence = DOTween.Sequence()
  sequence:AppendInterval(1)
  sequence:Append(self.m_sceneScrollRect:DOVerticalNormalizedPos(0, 6))
  sequence:OnComplete(onComplete)
end

function CoconutMainWindow:OnButtonClicked()
  self:Close()
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    GM.SceneManager:ChangeGameMode(EGameMode.Board)
  end
end

function CoconutMainWindow:OnSceneScrolled()
  local anchoredPositionY = self.m_sceneLayer1.anchoredPosition.y
  UIUtil.SetAnchoredPosition(self.m_sceneLayer2, nil, anchoredPositionY / 2)
  UIUtil.SetAnchoredPosition(self.m_sceneLayer3, nil, anchoredPositionY / 3)
end

function CoconutMainWindow:SetSceneOffset(offset, duration)
  local totalHeight = self.m_sceneLayer1.rect.height - self.m_sceneScrollRect.transform.rect.height
  local verticalNormalizedPosition = math.min(offset / totalHeight, 1)
  if duration == 0 then
    self.m_sceneScrollRect.verticalNormalizedPosition = verticalNormalizedPosition
  else
    self.m_layer1:RetainFlyAnimation()
    local onComplete = function()
      self.m_layer1:ReleaseFlyAnimation()
    end
    self.m_sceneScrollRect:DOVerticalNormalizedPos(verticalNormalizedPosition, duration):SetEase(Ease.Linear):OnComplete(onComplete)
  end
end

function CoconutMainWindow:GetButtonTransform()
  return self.m_buttonGo.transform
end
