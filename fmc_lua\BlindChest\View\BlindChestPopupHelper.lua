BlindChestPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
BlindChestPopupHelper.__index = BlindChestPopupHelper

function BlindChestPopupHelper.Create()
  local helper = setmetatable({}, BlindChestPopupHelper)
  helper:Init()
  return helper
end

function BlindChestPopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(BlindChestDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnStateChanged)
end

function BlindChestPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function BlindChestPopupHelper:CheckPopup()
  for activityType, activityDefinition in pairs(BlindChestDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Ended and not model:HasWindowOpenedOnce(ActivityState.Ended) and model:HasWindowOpenedOnce(ActivityState.Started) and not model:HasFinishedAllRound() then
      model:TryStartNewRound()
      return activityDefinition.MainWindowPrefabName, {
        model,
        state,
        true
      }
    end
  end
  for activityType, activityDefinition in pairs(BlindChestDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Started and not model:HasFinishedAllRound() and not model:HasWindowOpenedOnce(ActivityState.Started) then
      return activityDefinition.ReadyWindowPrefabName, {
        model,
        state,
        true
      }
    end
  end
end
