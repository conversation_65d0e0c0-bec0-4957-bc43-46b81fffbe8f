GeneralMsgWindow = setmetatable({}, BaseWindow)
GeneralMsgWindow.__index = GeneralMsgWindow

function GeneralMsgWindow:Init(titleText, descriptionText, buttonText, buttonCallback, closeCallback, withCloseButton, canCloseByChangeMode)
  self.m_buttonCallback = buttonCallback
  self.m_closeCallback = closeCallback
  self.m_titleText.text = titleText
  self.m_descText.text = descriptionText
  local preferredHeight = self.m_descText.preferredHeight
  local defaultTextSizeDelta = self.m_descText.transform.sizeDelta
  if preferredHeight > defaultTextSizeDelta.y then
    local defaultSizeDelta = self.m_contentRect.sizeDelta
    defaultSizeDelta.y = defaultSizeDelta.y + preferredHeight - defaultTextSizeDelta.y
    self.m_contentRect.sizeDelta = defaultSizeDelta
    defaultTextSizeDelta.y = preferredHeight
    self.m_descText.transform.sizeDelta = defaultTextSizeDelta
  end
  self.m_button:Init(buttonText, function()
    self:_OnButtonClicked()
  end)
  withCloseButton = withCloseButton or false
  self.m_closeBtnGo:SetActive(withCloseButton)
  self.canCloseByAndroidBack = withCloseButton
  self.canClickWindowMask = withCloseButton
  if canCloseByChangeMode ~= nil then
    self.canCloseByChangeGameMode = canCloseByChangeMode
  else
    self.canCloseByChangeGameMode = withCloseButton
  end
end

function GeneralMsgWindow:_OnButtonClicked()
  self:Close()
  if self.m_buttonCallback ~= nil then
    self.m_buttonCallback()
  end
end

function GeneralMsgWindow:OnCloseFinish()
  BaseWindow.OnCloseFinish(self)
  if self.m_closeCallback ~= nil then
    self.m_closeCallback()
  end
end

SystemGeneralMsgWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.SystemGeneralWindow
}, GeneralMsgWindow)
SystemGeneralMsgWindow.__index = SystemGeneralMsgWindow
ResourceDownloadWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.DataConflictWindow - 1
}, GeneralMsgWindow)
ResourceDownloadWindow.__index = ResourceDownloadWindow

function ResourceDownloadWindow:BeforeOpenCheck()
  return GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.ResourceDownloadWindow) == nil
end

function ResourceDownloadWindow:Init()
  local title = GM.GameTextModel:GetText("resource_download_require_title")
  local description = GM.GameTextModel:GetText("resource_download_require_desc")
  local buttonText = GM.GameTextModel:GetText("resource_download_require_btn")
  local buttonCallback = function()
    GM:RestartGame(nil, EBIProjectType.RestartGameAction.DownloadRequiredResource)
  end
  SystemGeneralMsgWindow.Init(self, title, description, buttonText, buttonCallback)
end
