ItemTransformView = setmetatable({}, BaseItemViewComponent)
ItemTransformView.__index = ItemTransformView

function ItemTransformView:Init(itemTransformModel)
  self.m_model = itemTransformModel
  self:_OnStateChanged()
  AddHandlerAndRecordMap(self.m_model.event, ItemTransformEventType.StateChanged, {
    obj = self,
    method = self._OnStateChanged
  })
end

function ItemTransformView:OnDestroy()
  RemoveAllHandlers(self.m_model.event, self)
end

function ItemTransformView:_OnStateChanged()
  if self.m_fillAmountTween ~= nil then
    self.m_fillAmountTween:Kill()
  end
  self.m_countDown:SetPercentage(self.m_model:GetTimerAmount())
  self.m_countDown:GrowPercentageTo(self.m_model:GetNextTimerAmount())
  if self.m_model:ShowAcceleratedCountDownAnim() then
    self.m_countDown:StartAnimation()
  else
    self.m_countDown:StopAnimation()
  end
end
