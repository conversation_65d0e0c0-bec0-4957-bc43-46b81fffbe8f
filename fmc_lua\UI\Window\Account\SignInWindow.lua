SignInWindow = setmetatable({}, BaseWindow)
SignInWindow.__index = SignInWindow

function SignInWindow:Init()
  self.m_appleGo:SetActive(DeviceInfo.IsSystemIOS())
  self.m_googleGo:SetActive(not DeviceInfo.IsSystemIOS())
  if GM.AccountManager:ShowBindReward() then
    self.m_desc.text = GM.GameTextModel:GetText("setting_login_first_desc", ACCOUNT_BIND_REWARD_COUNT)
  else
    self.m_desc.text = GM.GameTextModel:GetText("save_progress_desc")
  end
end

function SignInWindow:OnFacebookButtonClicked()
  self:_Login(ESsoSocialType.Facebook)
end

function SignInWindow:OnAppleButtonClicked()
  self:_Login(ESsoSocialType.Apple)
end

function SignInWindow:OnGoogleButtonClicked()
  self:_Login(ESsoSocialType.Google)
end

function SignInWindow:_Login(socialType)
  self:Close()
  if Application.internetReachability == NetworkReachability.NotReachable then
    local title = GM.GameTextModel:GetText("download_no_connection_title")
    local description = GM.GameTextModel:GetText("download_no_connection_desc")
    local buttonText = GM.GameTextModel:GetText("download_no_connection_btn")
    GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, title, description, buttonText)
  else
    GM.AccountManager:Login(socialType)
  end
end
