ItemRecycleAnimationWindow = setmetatable({}, TransparentBaseWindow)
ItemRecycleAnimationWindow.__index = ItemRecycleAnimationWindow
local FLY_ANIMATION = 0.6

function ItemRecycleAnimationWindow:Init(arrDishes, arrInstrus, arrOthers, mapInInstrumentItems, energyCount)
  self.m_energyText.text = "+" .. energyCount
  self.m_energyCanvasGroup.alpha = 1
  self.m_energyCanvasGroup.transform.localScale = V3Zero
  self.m_effectGo:SetActive(false)
  local s = DOTween.Sequence()
  s:AppendInterval(0.3)
  s:AppendCallback(function()
    self:StartAnimation(arrDishes, arrInstrus, arrOthers, mapInInstrumentItems)
  end)
  s:AppendInterval(FLY_ANIMATION)
  s:AppendCallback(function()
    self.m_effectGo:SetActive(true)
  end)
  s:Append(self.m_energyCanvasGroup.transform:DOScale(1, 0.3):SetEase(Ease.OutBack))
  s:AppendInterval(0.2)
  s:AppendCallback(function()
    RewardApi.AcquireRewardsInView({
      {
        [PROPERTY_TYPE] = EPropertyType.Energy,
        [PROPERTY_COUNT] = energyCount
      }
    })
  end)
  s:Append(self.m_energyCanvasGroup:DOFade(0, 0.3):SetEase(Ease.InSine))
  s:AppendCallback(function()
    self:Close()
  end)
end

function ItemRecycleAnimationWindow:StartAnimation(arrDishes, arrInstrus, arrOthers, mapInInstrumentItems)
  local boardView = MainBoardView.GetInstance()
  local boardModel = GM.MainBoardModel
  local arrBoardItemViews = {}
  local arrInventoryItems = {}
  local arrCacheItems = {}
  for _, arr in ipairs({
    arrDishes,
    arrInstrus,
    arrOthers
  }) do
    for _, itemModel in ipairs(arr) do
      if itemModel.eRecycleTag == EItemRecycleTag.InBoard then
        arrBoardItemViews[#arrBoardItemViews + 1] = boardView:GetItemView(itemModel)
      elseif itemModel.eRecycleTag == EItemRecycleTag.InInventory then
        arrInventoryItems[#arrInventoryItems + 1] = itemModel
      elseif itemModel.eRecycleTag == EItemRecycleTag.InCache then
        arrCacheItems[#arrCacheItems + 1] = itemModel
      end
    end
  end
  local cookItem
  local mapBoardInCookItems = {}
  for itemModel, cookCmp in pairs(mapInInstrumentItems) do
    cookItem = cookCmp:GetItemModel()
    if boardModel:HasBoardItem(cookItem) then
      mapBoardInCookItems[itemModel] = boardView:GetItemView(cookItem)
    else
      arrInventoryItems[#arrInventoryItems + 1] = itemModel
    end
  end
  self:PlayBoardAnimation(arrBoardItemViews, mapBoardInCookItems)
  local inventoryPosition = TutorialHelper.GetHudButton(ESceneViewHudButtonKey.Inventory).transform.position
  self:PlayNonBoardAnimation(arrInventoryItems, inventoryPosition)
  local cachePosition = self:GetUIPosition(boardView:GetOrderArea():GetBoardCacheRoot().transform.position)
  self:PlayNonBoardAnimation(arrCacheItems, cachePosition)
  EventDispatcher.DispatchEvent(EEventType.ItemRetrieved)
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
end

function ItemRecycleAnimationWindow:GetUIPosition(boardWorldPosition)
  local screenPosition = MainBoardView:GetInstance():ConvertWorldPositionToScreenPosition(boardWorldPosition)
  local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
  uiWorldPosition.z = 0
  return uiWorldPosition
end

function ItemRecycleAnimationWindow:PlayBoardAnimation(arrBoardItemViews, mapBoardInCookItems)
  local targetPosition = V3Zero
  local targetScale = 2
  for itemModel, cookItemView in pairs(mapBoardInCookItems) do
    local uiWorldPosition = self:GetUIPosition(cookItemView.transform.position)
    local itemType = itemModel:GetType()
    PropertyAnimationManager.PlayFlyElementAnimation(itemType, FLY_ANIMATION, uiWorldPosition, targetPosition, 0, targetScale, false, true)
  end
  for _, itemView in ipairs(arrBoardItemViews) do
    local screenPosition = MainBoardView:GetInstance():ConvertWorldPositionToScreenPosition(itemView.transform.position)
    local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
    uiWorldPosition.z = 0
    local itemType = itemView:GetModel():GetType()
    local onStart = function()
      GM.MainBoardModel.event:Call(BoardEventType.BatchRemoveItems, {
        Removed = {
          itemView:GetModel()
        },
        Dur = 0
      })
    end
    PropertyAnimationManager.PlayFlyElementAnimation(itemType, FLY_ANIMATION, uiWorldPosition, targetPosition, 1, targetScale, false, true, nil, onStart)
  end
end

function ItemRecycleAnimationWindow:PlayNonBoardAnimation(arrItems, fromPosition)
  local flyCount = math.min(5, #arrItems)
  local targetPosition = V3Zero
  local targetScale = 2
  for i = 1, flyCount do
    PropertyAnimationManager.PlayFlyElementAnimation(arrItems[i]:GetType(), FLY_ANIMATION, fromPosition, targetPosition, 0, targetScale, false, true)
  end
end
