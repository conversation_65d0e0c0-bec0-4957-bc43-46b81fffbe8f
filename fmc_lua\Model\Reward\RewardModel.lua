RewardModel = {}
RewardModel.__index = RewardModel
ERewardKey = {
  AccountBind = "account_bind"
}
ERewardStatus = {NotReceived = 0, HasReceived = 1}
ERewardRespStatus = {
  Success = 0,
  RespFail = 1,
  HasReceived = 2,
  Expired = 3
}

function RewardModel:Init()
  self.m_localRewardsDbTable = GM.DBTableManager:GetTable(EDBTableConfigs.LocalRewards)
  self:_LoadCachedTokens()
end

function RewardModel:LoadServerConfig(tbLoginResp)
  local arrRewards = tbLoginResp.rewards
  if arrRewards then
    self.m_mapRewardInfo = {}
    for i = 1, #arrRewards do
      arrRewards[i].content = json.decode(arrRewards[i].content)
      self.m_mapRewardInfo[arrRewards[i].key] = arrRewards[i]
    end
  end
end

function RewardModel:HasReceivedReward(key)
  if not self.m_mapRewardInfo then
    return true
  elseif self.m_mapRewardInfo[key] and self.m_mapRewardInfo[key].status == ERewardStatus.HasReceived then
    return true
  else
    return false
  end
end

function RewardModel:MarkRewardReceived(key)
  local arrKeys = StringUtil.Split(key, ",")
  for i = 1, #arrKeys do
    self.m_mapRewardInfo[arrKeys[i]] = {
      key = arrKeys[i],
      status = ERewardStatus.HasReceived
    }
  end
end

function RewardModel:ReceiveReward(key, callback)
  ApiMessage.ReceiveRewards(key, self:_GetToken(key), function(bSuccess, resp)
    local eRewardRespStatus
    if bSuccess and resp then
      self:_ClearToken(key)
      if resp.rcode == 0 then
        self:MarkRewardReceived(key)
        eRewardRespStatus = ERewardRespStatus.Success
      elseif resp.rcode == 1 then
        eRewardRespStatus = ERewardRespStatus.HasReceived
      elseif resp.rcode == 2 then
        eRewardRespStatus = ERewardRespStatus.Expired
      else
        self:MarkRewardReceived(key)
        eRewardRespStatus = ERewardRespStatus.Success
      end
    else
      eRewardRespStatus = ERewardRespStatus.RespFail
    end
    if callback then
      callback(eRewardRespStatus)
    end
  end)
end

function RewardModel:_GetToken(key)
  local token = self.m_mapTokens[key]
  if token then
    return token
  end
  token = GM.UserModel:GetInstallUuid() .. GM.GameModel:GetServerTime() .. key
  self.m_mapTokens[key] = token
  self:_SaveTokens()
  return token
end

function RewardModel:_ClearToken(key)
  self.m_mapTokens[key] = nil
  self:_SaveTokens()
end

function RewardModel:_LoadCachedTokens()
  local str = PlayerPrefs.GetString(EPlayerPrefKey.RewardTokenMap, nil)
  if str then
    self.m_mapTokens = json.decode(str)
  end
  self.m_mapTokens = self.m_mapTokens or {}
end

function RewardModel:_SaveTokens()
  PlayerPrefs.SetString(EPlayerPrefKey.RewardTokenMap, json.encode(self.m_mapTokens))
end

function RewardModel:GetRewardInfoMap()
  return self.m_mapRewardInfo
end

function RewardModel:FromSyncData(dataArr)
  self.m_localRewardsDbTable:FromArr(dataArr)
end

function RewardModel:GetData()
  return self.m_localRewardsDbTable
end

function RewardModel:MarkReceiveLocalReward(key)
  if self:HasReceivedLocalReward(key) then
    Log.Error("之前已经领取过本地奖励了：" .. tostring(key))
    return false
  end
  self.m_localRewardsDbTable:Set(key, DB_VALUE_KEY, "1")
  return true
end

function RewardModel:HasReceivedLocalReward(key)
  return self.m_localRewardsDbTable:GetValue(key, DB_VALUE_KEY) == "1"
end
