return {
  {
    ChapterId = "DimSum",
    Id = 1,
    Cost = 879,
    <PERSON><PERSON>s = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "floorA", State = 9},
      {Slot = "oldFloorA", State = 100},
      {
        Slot = "floorATrash",
        State = 100
      }
    }
  },
  {
    ChapterId = "DimSum",
    Id = 2,
    StartConditions = {1},
    Cost = 774,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashMidA", State = 100}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 3,
    StartConditions = {2},
    Cost = 722,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashMidB", State = 100}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 4,
    StartConditions = {3},
    Cost = 774,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashStage", State = 1}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 5,
    StartConditions = {4},
    Cost = 722,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashStage", State = 100}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 6,
    StartConditions = {5},
    Cost = 879,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightWall", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 7,
    StartConditions = {6},
    Cost = 931,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 1}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 8,
    StartConditions = {7},
    Cost = 879,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 2}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 9,
    StartConditions = {8},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 3}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 10,
    StartConditions = {9},
    Cost = 774,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 4}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 11,
    StartConditions = {10},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 5}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 12,
    StartConditions = {11},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 6}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 13,
    StartConditions = {12},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 7}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 14,
    StartConditions = {13},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 8}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 15,
    StartConditions = {14},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 16,
    StartConditions = {15},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 10}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 17,
    StartConditions = {16},
    Cost = 859,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftWall", State = 1},
      {Slot = "leftLight", State = 100}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 18,
    StartConditions = {17},
    Cost = 927,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftWall", State = 2}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 19,
    StartConditions = {18},
    Cost = 859,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftWall", State = 3}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 20,
    StartConditions = {19},
    Cost = 994,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftWall", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 21,
    StartConditions = {20},
    Cost = 792,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashMidD", State = 100}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 22,
    StartConditions = {21},
    Cost = 927,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashMidE", State = 100}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 23,
    StartConditions = {22},
    Cost = 859,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "oldCounterL",
        State = 100
      }
    }
  },
  {
    ChapterId = "DimSum",
    Id = 24,
    StartConditions = {23},
    Cost = 1062,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterSmall",
        State = 9
      }
    }
  },
  {
    ChapterId = "DimSum",
    Id = 25,
    StartConditions = {24},
    Cost = 770,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "floorB", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 26,
    StartConditions = {25},
    Cost = 915,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "counterL", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 27,
    StartConditions = {26},
    Cost = 987,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterItemsL",
        State = 9
      }
    }
  },
  {
    ChapterId = "DimSum",
    Id = 28,
    StartConditions = {27},
    Cost = 987,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "steam", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 29,
    StartConditions = {28},
    Cost = 1060,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "shelfBig", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 30,
    StartConditions = {29},
    Cost = 1060,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "gas", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 31,
    StartConditions = {30},
    Cost = 987,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterVase",
        State = 9
      }
    }
  },
  {
    ChapterId = "DimSum",
    Id = 32,
    StartConditions = {31},
    Cost = 915,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midTableA", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 33,
    StartConditions = {32},
    Cost = 1116,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midChairA", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 34,
    StartConditions = {33},
    Cost = 1030,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midTableB", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 35,
    StartConditions = {34},
    Cost = 1116,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midChairB", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 36,
    StartConditions = {35},
    Cost = 1203,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashMidC", State = 100}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 37,
    StartConditions = {36},
    Cost = 1116,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "counterR", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 38,
    StartConditions = {37},
    Cost = 1116,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterItemsR",
        State = 9
      }
    }
  },
  {
    ChapterId = "DimSum",
    Id = 39,
    StartConditions = {38},
    Cost = 770,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "tableR", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 40,
    StartConditions = {39},
    Cost = 943,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "utensilR", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 41,
    StartConditions = {40},
    Cost = 770,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "chairR", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 42,
    StartConditions = {41},
    Cost = 1017,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midTableC", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 43,
    StartConditions = {42},
    Cost = 1110,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midChairC", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 44,
    StartConditions = {43},
    Cost = 1017,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midTableD", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 45,
    StartConditions = {44},
    Cost = 1110,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midChairD", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 46,
    StartConditions = {45},
    Cost = 1204,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outWallL", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 47,
    StartConditions = {46},
    Cost = 1017,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outPlantL", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 48,
    StartConditions = {47},
    Cost = 1204,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outWallR", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 49,
    StartConditions = {48},
    Cost = 1017,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outPlantR", State = 9}
    }
  },
  {
    ChapterId = "DimSum",
    Id = 50,
    StartConditions = {49},
    Cost = 1204,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stoneLion", State = 9}
    }
  }
}
