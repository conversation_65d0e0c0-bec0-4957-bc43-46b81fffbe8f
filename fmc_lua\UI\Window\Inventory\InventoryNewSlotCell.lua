InventoryNewSlotCell = {}
InventoryNewSlotCell.__index = InventoryNewSlotCell

function InventoryNewSlotCell:Init()
  self:Reset()
  local slotConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.InventorySlot)
  local boughtCount = GM.MiscModel:GetInventoryBoughtCapInNumber()
  self.m_gemCostNum = slotConfig.UnlockCost[boughtCount + 1] or 9999
  self.m_countText.text = self.m_gemCostNum
  if self.m_countText.preferredWidth > 75 then
    UIUtil.SetSizeDelta(self.m_countText.transform, 75)
  else
    UIUtil.SetSizeDelta(self.m_countText.transform, self.m_countText.preferredWidth)
  end
end

function InventoryNewSlotCell:Reset()
  if self.m_canvasGroup ~= nil then
    self.m_canvasGroup.alpha = 1
  end
  if self.m_shadowImg ~= nil then
    UIUtil.SetColor(self.m_shadowImg, nil, nil, nil, 1)
  end
  if self.m_spine ~= nil then
    self.m_spine:Init()
    self.m_spine:PlayAnimation("idle", nil, true)
  end
end

function InventoryNewSlotCell:OnButtonClicked()
  if GM.MainBoardModel:BuyStoreSlot() then
    return
  end
  GM.UIManager:CloseView(UIPrefabConfigName.InventoryWindow)
  GM.ShopModel:OnLackOfGem(self.m_gemCostNum)
end

function InventoryNewSlotCell:OnOpened()
  local fadeDt = 0.2
  local s = DOTween.Sequence()
  s:Append(self.m_canvasGroup:DOFade(0, fadeDt))
  s:Join(self.m_shadowImg:DOFade(1, fadeDt))
  s:InsertCallback(fadeDt - 0.1, function()
    self.m_spine:PlayAnimation("hit")
  end)
  return fadeDt + 0.667
end
