NoticeButton = setmetatable({}, HudGeneralButton)
NoticeButton.__index = NoticeButton

function NoticeButton:Awake()
  HudGeneralButton.Awake(self)
  self:UpdatePerSecond()
  EventDispatcher.AddListener(EEventType.UpdateNotice, self, self._UpdateExclamation)
end

function NoticeButton:UpdatePerSecond()
  self:_UpdateExclamation()
end

function NoticeButton:_UpdateExclamation()
  local hasUnreadNotice = GM.NoticeModel:HasUnreadSystemNotice()
  self.m_exclamationGo:SetActive(hasUnreadNotice)
end

function NoticeButton:OnClicked()
  GM.NoticeModel:CheckNewNotice(function(success)
    GM.UIManager:OpenView(UIPrefabConfigName.NoticeWindow)
  end)
end
