BoardBackground = {}
BoardBackground.__index = BoardBackground
EBoardBackgroundElement = {
  BgTop = "bgTop",
  BgBottom = "bgBottom",
  Counter = "counter",
  ItemBoardBg = "itemBoardBg"
}

function BoardBackground:Awake()
  self.m_mapInitPos = {}
  self.m_mapTransform = {}
  self.m_mapRenderer = {}
  self.m_mapInitSize = {}
  self.m_mapInitScale = {}
  self.m_mapInitSprite = {}
  local spriteRenderer
  for _, v in pairs(EBoardBackgroundElement) do
    if self["m_" .. v .. "Trans"] then
      self.m_mapTransform[v] = self["m_" .. v .. "Trans"]
      self.m_mapInitPos[v] = self.m_mapTransform[v].localPosition
      self.m_mapRenderer[v] = self.m_mapTransform[v].gameObject:GetComponent(typeof(SpriteRenderer))
      self.m_mapInitSize[v] = self.m_mapRenderer[v].size
      self.m_mapInitScale[v] = self.m_mapTransform[v].localScale
      self.m_mapInitSprite[v] = self.m_mapRenderer[v].sprite
    end
  end
  self.m_bIgnoreFirstEnable = true
end

function BoardBackground:OnEnable()
  if self.m_bIgnoreFirstEnable == true then
    self.m_bIgnoreFirstEnable = nil
    return
  end
  if self.m_bAdjusted == true then
    return
  end
  self.m_bAdjusted = true
  local adjustSize = ScreenFitter.GetScreenAdjustSize()
  for k, _ in pairs(EBoardBackgroundElement) do
    if self["_Adjust" .. k] then
      self["_Adjust" .. k](self, adjustSize)
    end
  end
end

function BoardBackground:UpdateBgTop(imageConfig)
  local key = EBoardBackgroundElement.BgTop
  SpriteUtil.SetSpriteRenderer(self.m_mapRenderer[key], imageConfig, nil, function()
    self.m_mapRenderer[key].sprite = self.m_mapInitSprite[key]
  end, true)
end

function BoardBackground:_AdjustBgTop(adjustSize)
  local k = EBoardBackgroundElement.BgTop
  local scaleX = adjustSize.x / ScreenFitter.GetStandardWidth() / ScreenFitter.GetBoardScale()
  local scaleY = 1
  local height = self.m_mapInitPos[k].y + self.m_mapInitSize[k].y * 0.5 + self.m_boardViewTrans.localPosition.y + self.transform.localPosition.y
  if height < adjustSize.y / 2 then
    scaleY = (adjustSize.y / 2 - height) / self.m_mapInitSize[k].y + 1
  end
  local scale = math.max(scaleX, scaleY)
  self.m_mapTransform[k].localScale = self.m_mapInitScale[k] * scale
  self.m_mapTransform[k].localPosition = self.m_mapInitPos[k] + Vector3(0, self.m_mapInitSize[k].y * (scale - 1) / 2, 0)
end

function BoardBackground:_AdjustBgBottom(adjustSize)
  local k = EBoardBackgroundElement.BgBottom
  local width = math.ceil(adjustSize.x / ScreenFitter.GetBoardScale() / 166)
  self.m_mapRenderer[k].size = Vector2(166 * width, self.m_mapInitSize[k].y)
end

function BoardBackground:_AdjustCounter(adjustSize)
  local k = EBoardBackgroundElement.Counter
  local scale = math.max(1, adjustSize.x / ScreenFitter.GetStandardWidth() / ScreenFitter.GetBoardScale())
  self.m_mapTransform[k].localScale = Vector3(self.m_mapInitScale[k].x * scale, self.m_mapInitScale[k].y, self.m_mapInitScale[k].z)
end
