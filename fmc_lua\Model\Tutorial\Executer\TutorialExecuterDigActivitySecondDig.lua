local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer
local Step = {WindowTip = "1"}
local EStep2TextKey = {
  [Step.WindowTip] = "treasure_dig_guide_desc4"
}
local EStep2TextAnchorPercent = {
  [Step.WindowTip] = 20
}

function Executer:OnStart()
  self.m_mapActivityDefinition = {}
  for actType, activityDefinition in pairs(DigActivityDefinition) do
    self.m_mapActivityDefinition[actType] = activityDefinition
  end
  for _, activityDefinition in pairs(self.m_mapActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
    EventDispatcher.AddListener(activityDefinition.GridChangedEvent, self, self._OnDigGrid)
  end
  EventDispatcher.AddListener(EEventType.OpenView, self, self.OnViewOpened)
end

function Executer:_OnStateChanged()
  if self.m_window ~= nil then
    self:Finish()
  end
end

function Executer:OnViewOpened(msg)
  for activityType, activityDefinition in pairs(self.m_mapActivityDefinition) do
    if msg.name == activityDefinition.MainWindowPrefabName then
      local window = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
      local model = GM.ActivityManager:GetModel(activityType)
      if window ~= nil and model ~= nil and model:GetScore() > 0 then
        self.m_window = window
        self.m_activityDefinition = activityDefinition
        DelayExecuteFuncInView(function()
          self:_ExecuteStep1()
        end, 5, window)
      end
    end
    if self.m_window ~= nil and msg.name == activityDefinition.HelpWindowPrefabName then
      self:Finish()
    end
  end
end

function Executer:_OnDigGrid()
  if self.m_window ~= nil then
    self:Finish()
  end
end

function Executer:_ExecuteStep1()
  if self.m_window == nil or self.m_window.gameObject:IsNull() or GM.UIManager:GetOpenedTopView() ~= self.m_window then
    self.m_window = nil
    return
  end
  self.m_strOngoingDatas = Step.WindowTip
  local boardViewRect = self.m_window:GetBoardViewRect()
  local helpBtnRect = self.m_window:GetHelpBtnRect()
  if boardViewRect ~= nil and helpBtnRect ~= nil then
    self:SetStrongTutorial(true)
    TutorialHelper.SetMaskAlphaOnce(EWindowMaskAlpha.Dark)
    TutorialHelper.WholeMask(function()
      if boardViewRect ~= nil and not boardViewRect:IsNull() then
        TutorialHelper.DehighlightForUI(boardViewRect)
        self.m_boardViewRect = nil
      end
      if helpBtnRect ~= nil and not helpBtnRect:IsNull() then
        TutorialHelper.DehighlightForUI(helpBtnRect)
        self.m_helpBtnRect = nil
      end
      self:Finish()
    end)
    TutorialHelper.HighlightForUI(helpBtnRect)
    TutorialHelper.HighlightForUI(boardViewRect)
    local key = EStep2TextKey[self.m_strOngoingDatas]
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(key), EStep2TextAnchorPercent[self.m_strOngoingDatas])
    self.m_boardViewRect = boardViewRect
    self.m_helpBtnRect = helpBtnRect
  else
    self:Finish()
  end
end

function Executer:Finish(gesture, arrow)
  self.m_window = nil
  if self.m_boardViewRect ~= nil and not self.m_boardViewRect:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_boardViewRect)
    self.m_boardViewRect = nil
  end
  if self.m_helpBtnRect ~= nil and not self.m_helpBtnRect:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_helpBtnRect)
    self.m_helpBtnRect = nil
  end
  TutorialExecuter.Finish(self, gesture, arrow)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
