StoryWindow = setmetatable({canCloseByAndroidBack = false, showWindowMask = false}, BaseWindow)
StoryWindow.__index = StoryWindow

function StoryWindow:BeforeOpenCheck(storyId, isSpecial, finishCallback, bIsTimeline)
  if not GM.ChapterManager:GetActiveRoomView() then
    Log.<PERSON>rror("剧情窗口只可在改造场景打开！")
    return false
  end
  if bIsTimeline and not GM.TimelineManager:IsPlayingTimeline() then
    return false
  end
  if GM.TimelineManager:IsPlayingTimeline() and GM.TimelineManager:IsSkipping() then
    return false
  end
  return true
end

function StoryWindow:Init(storyId, isSpecialStory, finishCallback)
  self.m_storyId = storyId
  self.m_currentStep = 0
  self.m_currentCompType = nil
  self.m_finishCallback = finishCallback
  self.m_openWindowTime = GM.GameModel:GetServerTime()
  local cmp
  for _, eCompType in pairs(EStoryComponentType) do
    cmp = self:GetComponent(eCompType)
    if cmp then
      cmp:Init()
    end
  end
  self.m_skipBtnTrans.gameObject:SetActive(false)
  self.m_darkTopTrans:SetLocalScaleY(0)
  self.m_darkBtmTrans:SetLocalScaleY(0)
  GM.StoryDataModel:GetStoryData(storyId, isSpecialStory, function(storyData)
    self.m_arrStepData = storyData
    self:Enter(function()
      self.m_initialized = true
      self:_Next()
    end)
  end)
  local info = {
    c_id = GM.ChapterDataModel:GetChapterIdByName(GM.TaskManager:GetOngoingChapterName()),
    id = self.m_storyId
  }
  local actionString = GM.BIManager:TableToString(info)
  GM.BIManager:LogAction(EBIType.StartStory, actionString)
end

function StoryWindow:OnDestroy()
  local info = {
    c_id = GM.ChapterDataModel:GetChapterIdByName(GM.TaskManager:GetOngoingChapterName()),
    id = self.m_storyId,
    d = GM.GameModel:GetServerTime() - self.m_openWindowTime,
    w = #self.m_arrStepData
  }
  local actionString = GM.BIManager:TableToString(info)
  GM.BIManager:LogAction(EBIType.StoryDuration, actionString)
  BaseWindow.OnDestroy(self)
  GM.UIManager:RemoveAllEventLocks(self)
  if self.m_darkTween then
    self.m_darkTween:Complete(true)
    self.m_darkTween = nil
  end
end

function StoryWindow:GetComponent(eCompType)
  if eCompType == EStoryComponentType.Left then
    return self.m_leftComponent
  elseif eCompType == EStoryComponentType.Right then
    return self.m_rightComponent
  else
    return nil
  end
end

function StoryWindow:OnMaskClicked()
  if not self.m_initialized then
    return
  end
  self:_Next()
end

function StoryWindow:_ForwardStep(nextScene)
  local stepData = self:_GetCurrentStepData()
  local maxStep = #self.m_arrStepData
  if not nextScene or maxStep <= self.m_currentStep then
    self.m_currentStep = self.m_currentStep + 1
  else
    self.m_currentStep = maxStep + 1
  end
end

function StoryWindow:_GetCurrentStepData()
  return self.m_arrStepData[self.m_currentStep]
end

function StoryWindow:_Next(nextScene)
  self:_ForwardStep(nextScene)
  local currentComponent = self:GetComponent(self.m_currentCompType)
  local nextStepData = self:_GetCurrentStepData()
  if nextStepData == nil then
    if currentComponent then
      currentComponent:Leave()
    end
    self:Leave(function()
      if self.m_finishCallback then
        self.m_finishCallback()
      end
      self:Close()
    end)
  else
    local nextCompType = nextStepData:GetCompType()
    local nextComponent = self:GetComponent(nextCompType)
    self:_NextComponent(currentComponent, nextComponent, nextStepData)
  end
end

function StoryWindow:_NextComponent(currentComponent, nextComponent, nextStepData)
  local isStoryFirstStepInTimeline = GM.TimelineManager:IsPlayingTimeline() and self.m_currentStep == 1
  if not isStoryFirstStepInTimeline then
    if nextStepData.animationName then
      local chapterView = GM.ChapterManager:GetActiveRoomView()
      local loop = nextStepData.bLoopAnimation
      local callback
      if nextComponent == nil then
        Log.Assert(not loop, "没有剧情对话时，角色动作不能循环")
        
        function callback()
          self:_Next()
        end
      end
    end
  else
    Log.Assert(nextComponent ~= nil, "第一个剧情对话不应为空")
  end
  if nextComponent then
    nextComponent:UpdateContent(nextStepData)
  end
  if currentComponent ~= nextComponent then
    self.m_currentCompType = nextStepData:GetCompType()
    if currentComponent then
      currentComponent:Leave()
    end
    if nextComponent then
      nextComponent.transform:SetAsLastSibling()
      nextComponent:Enter()
    end
  end
  if IsAutoRun() then
    DOVirtual.DelayedCall(0.2, function()
      self:_Next()
    end)
  end
end

function StoryWindow:Enter(onFinish)
  self:_DoEnterAnimation(onFinish)
end

function StoryWindow:_DoEnterAnimation(onFinish)
  if self.m_darkTween then
    self.m_darkTween:Complete(true)
    self.m_darkTween = nil
  end
  local dt = 0.35
  local s = DOTween.Sequence():SetEase(Ease.OutSine)
  s:Append(self.m_darkTopTrans:DOScaleY(1, dt))
  s:Join(self.m_darkBtmTrans:DOScaleY(1, dt))
  if onFinish then
    s:AppendCallback(onFinish)
  end
  self.m_darkTween = s
end

function StoryWindow:Leave(onFinish)
  if self.m_bHasLeft then
    return
  end
  self.m_bHasLeft = true
  self:_DoLeaveAnimation(onFinish)
end

function StoryWindow:_DoLeaveAnimation(onFinish)
  GM.UIManager:SetEventLock(true, self)
  self.m_skipBtnTrans.gameObject:SetActive(false)
  if self.m_darkTween then
    self.m_darkTween:Complete(true)
    self.m_darkTween = nil
  end
  local dt = 0.35
  local s = DOTween.Sequence():SetEase(Ease.OutSine)
  s:Append(self.m_darkTopTrans:DOScaleY(0, dt))
  s:Join(self.m_darkBtmTrans:DOScaleY(0, dt))
  s:AppendCallback(function()
    GM.UIManager:SetEventLock(false, self)
    if onFinish then
      onFinish()
    end
  end)
  self.m_darkTween = s
end

function StoryWindow:GetSkipBIInfo()
  local action = {
    s_id = self.m_storyId,
    s_stp = self.m_currentStep
  }
  return action
end

function StoryWindow:OnSkipButtonClicked()
  local action = {
    c_id = GM.ChapterDataModel:GetChapterIdByName(GM.TaskManager:GetOngoingChapterName()),
    id = self.m_storyId,
    step = self.m_currentStep
  }
  local actionString = GM.BIManager:TableToString(action)
  GM.BIManager:LogAction(EBIType.SkipStory, actionString)
  self:_Next(true)
end
