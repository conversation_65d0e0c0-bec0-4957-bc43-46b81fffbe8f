BlindChestReadyWindow = setmetatable({canCloseByAndroidBack = false}, BlindChestBaseWindow)
BlindChestReadyWindow.__index = BlindChestReadyWindow

function BlindChestReadyWindow:Init(model, state, bAutoPopup)
  BlindChestBaseWindow.Init(self, model, state, bAutoPopup)
  if self.m_model:HasFinishedAllRound() then
    self:SetCloseBtnActive(true)
  else
    self:SetCloseBtnActive(false)
  end
  self:UpdatePerSecond()
end

function BlindChestReadyWindow:OnCloseBtnClick()
  BlindChestBaseWindow.OnCloseBtnClick(self)
  if self.m_model:HasFinishedAllRound() then
    return
  end
  self.m_model:TryOpenView()
end

function BlindChestReadyWindow:UpdatePerSecond()
  if self.m_model == nil or self.m_model:GetNextStateTime() == nil then
    return
  end
  if self.m_model:GetState() == ActivityState.Ended then
    UIUtil.SetActive(self.m_countdownGo, false)
  else
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

BlindChestSuccessWindow = setmetatable({canCloseByAndroidBack = false}, BlindChestReadyWindow)
BlindChestSuccessWindow.__index = BlindChestSuccessWindow

function BlindChestSuccessWindow:Init(...)
  BlindChestReadyWindow.Init(self, ...)
  if self.m_model:HasFinishedAllRound() then
    self.m_descText.text = GM.GameTextModel:GetText("blindchest_end_desc_find1")
    UIUtil.SetActive(self.m_bottomDescGo, true)
  else
    self.m_descText.text = GM.GameTextModel:GetText("blindchest_end_desc_find")
    UIUtil.SetActive(self.m_bottomDescGo, false)
  end
end

BlindChestEndWindow = setmetatable({canCloseByAndroidBack = false}, BlindChestBaseWindow)
BlindChestEndWindow.__index = BlindChestEndWindow

function BlindChestEndWindow:Init(model, state)
  BlindChestBaseWindow.Init(self, model, state, true)
end

function BlindChestEndWindow:OnCloseBtnClick()
  BaseWindow.OnCloseBtnClick(self)
end

BlindChestEndConfirmWindow = setmetatable({canCloseByAndroidBack = false}, BlindChestBaseWindow)
BlindChestEndConfirmWindow.__index = BlindChestEndConfirmWindow

function BlindChestEndConfirmWindow:Init(model, state, redCallback)
  BlindChestBaseWindow.Init(self, model, state, true)
  self.m_redCallback = redCallback
  self.m_numText.text = "x" .. self.m_model:GetKeyCount()
  SpriteUtil.SetImage(self.m_icon, self.m_definition.ActivityTokenImage, true)
  self.m_descText.text = GM.GameTextModel:GetText("blindchest_endwindow_desc", self.m_definition.ActivityTokenImage, self.m_definition.ActivityTokenImage)
end

function BlindChestEndConfirmWindow:OnRedClick()
  self:Close()
  if self.m_redCallback ~= nil then
    self.m_redCallback()
  end
  self:LogWindowAction(EBIType.UIActionType.Close, {
    EBIReferType.UserClick
  }, 1)
end

function BlindChestEndConfirmWindow:OnCloseBtnClick()
  self:LogWindowAction(EBIType.UIActionType.Close, {
    EBIReferType.UserClick
  })
  self:Close()
end

function BlindChestEndConfirmWindow:OnCloseView()
  BlindChestBaseWindow.OnCloseView(self)
end
