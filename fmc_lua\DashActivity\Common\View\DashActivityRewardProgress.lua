DashActivityRewardProgress = {}
DashActivityRewardProgress.__index = DashActivityRewardProgress

function DashActivityRewardProgress:Init(activityType, allFinished)
  local model = GM.ActivityManager:GetModel(activityType)
  local configs = model:GetLevelConfigs()
  local currentLevel = model:GetLevel()
  local maxLevel = model:GetMaxRewardLevel()
  for level = 1, maxLevel do
    local config = configs[level]
    if config ~= nil then
      local cellName = "m_cellGo" .. #config.rewards
      local gameObject = GameObject.Instantiate(self[cellName], self.m_rewardsRectTrans)
      gameObject:SetActive(true)
      gameObject:GetLuaTable():Init(config.rewards)
    end
    if level <= currentLevel or allFinished then
      local gameObject = GameObject.Instantiate(self.m_fillCellGo, self.m_fillsRectTrans)
      gameObject:SetActive(true)
      local image = gameObject:GetComponent(typeof(Image))
      if allFinished then
        image.sprite = self.m_fillDoneSprite
      else
        image.sprite = level == currentLevel and self.m_fillCurrentSprite or self.m_fillDoneSprite
      end
    end
  end
  if self.m_rewardsCanv then
    self.m_rewardsCanv.alpha = 1
  end
end

function DashActivityRewardProgress:PlayFadeOut()
  if self.m_rewardsCanv then
    self.m_rewardsCanv:DOFade(0, 0.3)
  end
end

function DashActivityRewardProgress:SetRewardActive(bActive)
  if self.m_rewardsGo then
    self.m_rewardsGo:SetActive(bActive)
  end
end

DashActivityRewardProgressCell = {}
DashActivityRewardProgressCell.__index = DashActivityRewardProgressCell

function DashActivityRewardProgressCell:Init(rewards)
  for index, reward in ipairs(rewards) do
    local image = self["m_image" .. index]
    image.enabled = true
    local spriteName = ConfigUtil.GetCurrencyImageName(reward)
    SpriteUtil.SetImage(image, spriteName, true)
  end
end
