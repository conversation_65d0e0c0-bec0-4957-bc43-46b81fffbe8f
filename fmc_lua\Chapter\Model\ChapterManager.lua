ChapterManager = {}
ChapterManager.__index = ChapterManager

function ChapterManager:Init()
end

function ChapterManager:LateInit()
  Log.Assert(not StringUtil.IsNilOrEmpty(self.curActiveChapterName), "TaskManager 需根据进度设置初始加载章节")
  self:ChangeChapter(self.curActiveChapterName)
end

function ChapterManager:_CreateRoomModel()
  if self.roomModel then
    self.roomModel:Destroy()
  end
  local slotStateMap = self:CalculateSlotStatesByTaskProgress(self.curActiveChapterName)
  self.roomModel = setmetatable({}, RoomModel)
  self.roomModel:Init(self.curActiveChapterName, slotStateMap)
end

function ChapterManager:GetActiveRoomView()
  return self.roomModel and self.roomModel.view
end

function ChapterManager:ChangeChapter(chapterName)
  self.curActiveChapterName = chapterName
  self:_CreateRoomModel()
end

function ChapterManager:CalculateSlotStatesByTaskProgress(chapterName)
  local mapNextTaskIds = GM.TaskDataModel:GetTaskNextIdsByChapter(chapterName)
  local mapPreTaskIds = GM.TaskDataModel:GetTaskPreIdsByChapter(chapterName)
  local arrInitialTaskIds = GM.TaskDataModel:GetTaskInitialIdsByChapter(chapterName)
  local slotStateMap = {}
  local taskManager = GM.TaskManager
  local chapterId = GM.ChapterDataModel:GetChapterIdByName(chapterName)
  local mapMarkedTaskIds = {}
  local funcAddSlotStates
  
  function funcAddSlotStates(taskId)
    if taskManager:IsTaskFinished(chapterId, taskId) then
      for _, preTaskId in ipairs(mapPreTaskIds[taskId] or {}) do
        if not mapMarkedTaskIds[preTaskId] then
          return
        end
      end
      local slotChanges = taskManager:GetTaskSlotsChanges(taskId, chapterName)
      for _, changeData in ipairs(slotChanges) do
        slotStateMap[changeData.Slot] = changeData.State
      end
      mapMarkedTaskIds[taskId] = true
      for _, nextTaskId in ipairs(mapNextTaskIds[taskId] or {}) do
        funcAddSlotStates(nextTaskId)
      end
    end
  end
  
  for _, initTaskId in ipairs(arrInitialTaskIds) do
    funcAddSlotStates(initTaskId)
  end
  return slotStateMap
end
