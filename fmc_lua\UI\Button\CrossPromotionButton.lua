CrossPromotionButton = setmetatable({}, HudGeneralButton)
CrossPromotionButton.__index = CrossPromotionButton

function CrossPromotionButton:Awake()
  HudGeneralButton.Awake(self)
  self:UpdateContent()
  AddHandlerAndRecordMap(GM.CrossPromotionModel.event, ECPEventType.StateChanged, {
    obj = self,
    method = self.UpdateContent
  })
end

function CrossPromotionButton:OnDestroy()
  HudGeneralButton.OnDestroy(self)
  RemoveAllHandlers(GM.CrossPromotionModel.event, self)
end

function CrossPromotionButton:UpdateContent()
  self:UpdateActivityIcon()
  if self.gameObject.activeSelf then
    self:UpdatePerSecond()
  end
end

function CrossPromotionButton:UpdateActivityIcon()
  local activityOpen = GM.CrossPromotionModel:IsActivityOpen()
  self.gameObject:SetActive(activityOpen)
  if not activityOpen then
    return
  end
  self.m_exclamationGo:SetActive(GM.CrossPromotionModel:IsStrongTipShow())
  local downUrl = GM.CrossPromotionModel.iconPic .. ".png"
  local funUpdateSprite = function(sprite)
    if sprite and self.m_iconImg and not self.m_iconImg:IsNull() then
      self.m_iconImg.sprite = sprite
      self.m_iconImg.enabled = true
      self.m_iconImg.color = CSColor.white
      SpriteUtil.SetNativeSize(self.m_iconImg)
    end
  end
  local canUseDefaultIcon = GM.CrossPromotionModel.iconPicName ~= nil and ImageFileConfigName[GM.CrossPromotionModel.iconPicName] ~= nil
  LoadUrlImage:LoadSprite(downUrl, function(bSuccess, sprite)
    if bSuccess then
      funUpdateSprite(sprite)
    end
  end, true, canUseDefaultIcon and GM.CrossPromotionModel.iconPicName or nil, canUseDefaultIcon and funUpdateSprite or nil)
end

function CrossPromotionButton:UpdatePerSecond()
  if GM.CrossPromotionModel:GetEndTime() == 0 then
    return
  end
  local remainTime = GM.CrossPromotionModel:GetEndTime() - GM.GameModel:GetServerTime()
  if remainTime <= 0 then
    self:UpdateContent()
    return
  end
  local timeStr = TimeUtil.ParseTimeDescription(math.max(0, remainTime))
  self.m_timeText.text = timeStr
end

function CrossPromotionButton:OnClicked()
  GM.CrossPromotionModel:OpenCrossPromotionWindow()
end
