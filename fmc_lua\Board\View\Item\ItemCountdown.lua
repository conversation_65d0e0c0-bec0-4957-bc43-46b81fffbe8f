ItemCountdown = {}
ItemCountdown.__index = ItemCountdown

function ItemCountdown:Awake()
  self.m_autoPointerTrans:DOLocalRotate(Vector3(0, 0, -360), 5, RotateType.FastBeyond360):SetLoops(-1):SetEase(Ease.Linear)
end

function ItemCountdown:OnDestroy()
  if self.m_fillAmountTween ~= nil then
    self.m_fillAmountTween:Kill()
    self.m_fillAmountTween = nil
  end
  self:StopAnimation()
  self.m_autoPointerTrans:DOKill()
end

function ItemCountdown:SetPercentage(percent)
  self.m_countDownMaskImage.fillAmount = 1 - percent
  self.m_pointerTrans:SetLocalRotationZ(-360 * percent)
end

function ItemCountdown:GrowPercentageTo(percent)
  if self.m_fillAmountTween ~= nil then
    self.m_fillAmountTween:Kill()
  end
  local s = DOTween.Sequence()
  self.m_fillAmountTween = s
  s:Append(self.m_countDownMaskImage:DOFillAmount(1 - percent, 1):SetEase(Ease.Linear))
  s:Join(self.m_pointerTrans:DOLocalRotateQuaternion(Quaternion.Euler(0, 0, -360 * percent), 1):SetEase(Ease.Linear))
end

function ItemCountdown:StartAnimation()
  if self.m_countDownAniTween == nil then
    local trans = self.m_countDownTrans
    local sequence = DOTween.Sequence():SetLoops(-1)
    sequence:Append(trans:DOScale(Vector3(0.9900000000000001, 0.81, 1), 0.25))
    sequence:Append(trans:DOScale(Vector3(0.81, 0.9900000000000001, 1), 0.5))
    sequence:Append(trans:DOScale(Vector3(0.9, 0.9, 1), 0.25))
    self.m_countDownAniTween = sequence
  end
end

function ItemCountdown:StopAnimation()
  if self.m_countDownAniTween ~= nil then
    self.m_countDownAniTween:Kill()
    self.m_countDownAniTween = nil
    self.m_countDownTrans.localScale = Vector3(0.9, 0.9, 1)
  end
end
