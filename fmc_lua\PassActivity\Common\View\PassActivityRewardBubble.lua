PassActivityRewardBubble = {}
PassActivityRewardBubble.__index = PassActivityRewardBubble
local ERewardPrefixPriority = {
  enebox_ = 2000,
  ene_ = 1000,
  gem_ = 900,
  greenbox_ = 700
}
local ERewardPriority = {energy = 2100, gem = 950}

function PassActivityRewardBubble:Init(rewards, sortingOrder)
  if not Table.IsEmpty(rewards) then
    table.sort(rewards, function(a, b)
      local priorityA = self:GetRewardPriority(a[PROPERTY_TYPE])
      local priorityB = self:GetRewardPriority(b[PROPERTY_TYPE])
      if priorityA == priorityB then
        return a[PROPERTY_TYPE] > b[PROPERTY_TYPE]
      else
        return self:GetRewardPriority(a[PROPERTY_TYPE]) > self:GetRewardPriority(b[PROPERTY_TYPE])
      end
    end)
    self.m_rewardContent:Init(rewards)
  end
  self:ForceHideBubble()
  self.m_canvas.sortingOrder = sortingOrder
end

function PassActivityRewardBubble:GetRewardPriority(rewardType)
  if StringUtil.IsNilOrEmpty(rewardType) then
    return 0
  end
  local priority = 0
  local levelNum = 0
  if GM.ItemDataModel:IsItemExist(rewardType) then
    levelNum = tonumber(GM.ItemDataModel:GetChainLevel(rewardType))
  end
  for prefix, num in pairs(ERewardPrefixPriority) do
    if StringUtil.StartWith(rewardType, prefix) then
      priority = num
      break
    end
  end
  for rewardName, num in pairs(ERewardPriority) do
    if rewardName == rewardType then
      priority = num
      break
    end
  end
  if priority == 0 then
    return 0
  end
  return priority + levelNum
end

function PassActivityRewardBubble:UpdateContent(msg)
  if Table.IsEmpty(msg) then
    return
  end
  if msg.pos ~= nil then
    UIUtil.SetPosition(self.transform, msg.pos.x, msg.pos.y)
  end
  if msg.offset ~= nil then
    self.transform.position = self.transform.position + Vector3(msg.offset.x, msg.offset.y, 0)
  end
  local bRight = msg.bRight or false
  UIUtil.SetActive(self.m_arrowLeftGo, not bRight)
  UIUtil.SetActive(self.m_arrowRightGo, bRight)
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_rewardContentRectTrans)
end

function PassActivityRewardBubble:ShowBubble(message, bAutoHide)
  self:UpdateContent(message)
  if self.m_rewardBubbleTween ~= nil then
    self.m_rewardBubbleTween:Kill()
    self.m_rewardBubbleTween = nil
  end
  if bAutoHide then
    self.m_currentSelected = CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject
  end
  self.m_rewardContentRectTrans.gameObject:SetActive(true)
  self.m_rewardContentRectTrans:SetLocalScaleXY(0)
  local seq = DOTween.Sequence()
  seq:Append(self.m_rewardContentRectTrans:DOScale(1, 0.2))
  seq:AppendCallback(function()
    self.m_rewardBubbleTween = nil
  end)
  self.m_rewardBubbleTween = seq
  self.m_bShow = true
end

function PassActivityRewardBubble:HideBubble()
  if not self.m_bShow then
    return
  end
  if self.m_rewardBubbleTween ~= nil then
    self.m_rewardBubbleTween:Kill()
    self.m_rewardBubbleTween = nil
  end
  local seq = DOTween.Sequence()
  seq:Append(self.m_rewardContentRectTrans:DOScale(0, 0.1))
  seq:AppendCallback(function()
    self.m_rewardBubbleTween = nil
    self.m_rewardContentRectTrans.gameObject:SetActive(false)
  end)
  self.m_rewardBubbleTween = seq
  self.m_bShow = false
end

function PassActivityRewardBubble:ForceHideBubble()
  self.m_rewardContentRectTrans.gameObject:SetActive(false)
end

function PassActivityRewardBubble:Update()
  local selectGo = CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject
  local bRewardHelpBtn = selectGo ~= nil and not selectGo:IsNull() and selectGo:GetLuaTable() ~= nil and selectGo:GetLuaTable().bRewardHelpBtn
  if self.m_currentSelected ~= nil and self.m_currentSelected ~= selectGo and selectGo ~= self.m_scrollViewGo and not bRewardHelpBtn then
    self.m_currentSelected = nil
    self:HideBubble()
  end
end

PassActivityRewardBubbleItem = setmetatable({}, RewardItemWithItemTipButton)
PassActivityRewardBubbleItem.__index = PassActivityRewardBubbleItem

function PassActivityRewardBubbleItem:Init(data, bShowTipButton)
  RewardItemWithItemTipButton.Init(self, data, bShowTipButton)
  self:SetTipButtonVisible(bShowTipButton)
end
