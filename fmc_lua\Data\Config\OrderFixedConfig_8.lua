return {
  {
    Id = "80010",
    GroupId = 1,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e4friedmt_11",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_5", Count = 1}
  },
  {
    Id = "80020",
    GroupId = 1,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6semi_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80030",
    GroupId = 1,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "80040",
    GroupId = 1,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    }
  },
  {
    Id = "80050",
    GroupId = 1,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80060",
    GroupId = 1,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "80070",
    GroupId = 1,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_3",
      Count = 1
    }
  },
  {
    Id = "80080",
    GroupId = 2,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_12", Count = 1}
  },
  {
    Id = "80090",
    GroupId = 2,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80100",
    GroupId = 2,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e1sushi_36",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "80110",
    GroupId = 2,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "80120",
    GroupId = 2,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80130",
    GroupId = 2,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    }
  },
  {
    Id = "80140",
    GroupId = 2,
    ChapterId = 8,
    Requirement_1 = {Type = "it_4_2_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80150",
    GroupId = 3,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80160",
    GroupId = 3,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e5sf_23",
      Count = 1
    }
  },
  {
    Id = "80170",
    GroupId = 3,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "80180",
    GroupId = 3,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_19", Count = 1}
  },
  {
    Id = "80190",
    GroupId = 3,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80200",
    GroupId = 3,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80210",
    GroupId = 3,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "80220",
    GroupId = 4,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80230",
    GroupId = 4,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "80240",
    GroupId = 4,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e1sushi_35",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80250",
    GroupId = 4,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_1_7", Count = 1}
  },
  {
    Id = "80260",
    GroupId = 4,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_fd_16", Count = 1},
    Requirement_2 = {
      Type = "ds_e1icytre_1",
      Count = 1
    }
  },
  {
    Id = "80270",
    GroupId = 4,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80280",
    GroupId = 4,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6assort_4",
      Count = 1
    }
  },
  {
    Id = "80290",
    GroupId = 5,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80300",
    GroupId = 5,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "80310",
    GroupId = 5,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e1sushi_26",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80320",
    GroupId = 5,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "80330",
    GroupId = 5,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "80340",
    GroupId = 5,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80350",
    GroupId = 5,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {Type = "ds_fd_20", Count = 1}
  },
  {
    Id = "80360",
    GroupId = 6,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "it_4_2_4", Count = 1}
  },
  {
    Id = "80370",
    GroupId = 6,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_8",
      Count = 1
    }
  },
  {
    Id = "80380",
    GroupId = 6,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80390",
    GroupId = 6,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e1sushi_33",
      Count = 1
    }
  },
  {
    Id = "80400",
    GroupId = 6,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80410",
    GroupId = 6,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_6e2flb_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80420",
    GroupId = 6,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "80430",
    GroupId = 7,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80440",
    GroupId = 7,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_12",
      Count = 1
    }
  },
  {
    Id = "80450",
    GroupId = 7,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_19",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80460",
    GroupId = 7,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "80470",
    GroupId = 7,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "80480",
    GroupId = 7,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_23", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80490",
    GroupId = 7,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_14",
      Count = 1
    }
  },
  {
    Id = "80500",
    GroupId = 8,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillmt_4",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_5", Count = 1}
  },
  {
    Id = "80510",
    GroupId = 8,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "80520",
    GroupId = 8,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_6e2mt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80530",
    GroupId = 8,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_20",
      Count = 1
    }
  },
  {
    Id = "80540",
    GroupId = 8,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_7e5mt_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80550",
    GroupId = 8,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "80560",
    GroupId = 8,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80570",
    GroupId = 9,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80580",
    GroupId = 9,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    }
  },
  {
    Id = "80590",
    GroupId = 9,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_mixdrk_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80600",
    GroupId = 9,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_23",
      Count = 1
    }
  },
  {
    Id = "80610",
    GroupId = 9,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "80620",
    GroupId = 9,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e1icytre_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80630",
    GroupId = 9,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_4",
      Count = 1
    }
  },
  {
    Id = "80640",
    GroupId = 10,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80650",
    GroupId = 10,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e1sushi_6",
      Count = 1
    },
    Requirement_2 = {Type = "it_a8_2_6", Count = 1}
  },
  {
    Id = "80660",
    GroupId = 10,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_friedsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80670",
    GroupId = 10,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {Type = "it_a8_2_5", Count = 1}
  },
  {
    Id = "80680",
    GroupId = 10,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e1sushi_32",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80690",
    GroupId = 10,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_fd_15", Count = 1}
  },
  {
    Id = "80700",
    GroupId = 10,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "80710",
    GroupId = 11,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_34",
      Count = 1
    }
  },
  {
    Id = "80720",
    GroupId = 11,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80730",
    GroupId = 11,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1icytre_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_9", Count = 1}
  },
  {
    Id = "80740",
    GroupId = 11,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80750",
    GroupId = 11,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_16", Count = 1}
  },
  {
    Id = "80760",
    GroupId = 11,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6assort_4",
      Count = 1
    }
  },
  {
    Id = "80770",
    GroupId = 11,
    ChapterId = 8,
    Requirement_1 = {Type = "it_4_1_7", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80780",
    GroupId = 12,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_3",
      Count = 1
    }
  },
  {
    Id = "80790",
    GroupId = 12,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_6e3preingre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80800",
    GroupId = 12,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_25",
      Count = 1
    }
  },
  {
    Id = "80810",
    GroupId = 12,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_19",
      Count = 1
    }
  },
  {
    Id = "80820",
    GroupId = 12,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80830",
    GroupId = 12,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_mixdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80840",
    GroupId = 12,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_14",
      Count = 1
    }
  },
  {
    Id = "80850",
    GroupId = 13,
    ChapterId = 8,
    Requirement_1 = {Type = "it_4_2_7", Count = 1}
  },
  {
    Id = "80860",
    GroupId = 13,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80870",
    GroupId = 13,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "80880",
    GroupId = 13,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "80890",
    GroupId = 13,
    ChapterId = 8,
    Requirement_1 = {Type = "it_1_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    }
  },
  {
    Id = "80900",
    GroupId = 13,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80910",
    GroupId = 13,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_5",
      Count = 1
    }
  },
  {
    Id = "80920",
    GroupId = 14,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "it_4_2_3", Count = 1}
  },
  {
    Id = "80930",
    GroupId = 14,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80940",
    GroupId = 14,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "80950",
    GroupId = 14,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "80960",
    GroupId = 14,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillmt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e1sushi_26",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "80970",
    GroupId = 14,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "80980",
    GroupId = 14,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    }
  },
  {
    Id = "80990",
    GroupId = 15,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "81000",
    GroupId = 15,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_34",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81010",
    GroupId = 15,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_7",
      Count = 1
    }
  },
  {
    Id = "81020",
    GroupId = 15,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "81030",
    GroupId = 15,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2flb_5",
      Count = 1
    }
  },
  {
    Id = "81040",
    GroupId = 15,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_22",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81050",
    GroupId = 15,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_30",
      Count = 1
    }
  },
  {
    Id = "81060",
    GroupId = 16,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "81070",
    GroupId = 16,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_3",
      Count = 1
    }
  },
  {
    Id = "81080",
    GroupId = 16,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6sala_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81090",
    GroupId = 16,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_9", Count = 1}
  },
  {
    Id = "81100",
    GroupId = 16,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "81110",
    GroupId = 16,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81120",
    GroupId = 16,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4stewmt_2",
      Count = 1
    }
  },
  {
    Id = "81130",
    GroupId = 17,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_e4sf_12", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    }
  },
  {
    Id = "81140",
    GroupId = 17,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_22",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81150",
    GroupId = 17,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "81160",
    GroupId = 17,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_1",
      Count = 1
    }
  },
  {
    Id = "81170",
    GroupId = 17,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfru_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81180",
    GroupId = 17,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "81190",
    GroupId = 17,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "81200",
    GroupId = 18,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "it_4_2_3", Count = 1}
  },
  {
    Id = "81210",
    GroupId = 18,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "81220",
    GroupId = 18,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81230",
    GroupId = 18,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_18",
      Count = 1
    }
  },
  {
    Id = "81240",
    GroupId = 18,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    }
  },
  {
    Id = "81250",
    GroupId = 18,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81260",
    GroupId = 18,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e1sushi_19",
      Count = 1
    }
  },
  {
    Id = "81270",
    GroupId = 19,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_19",
      Count = 1
    }
  },
  {
    Id = "81280",
    GroupId = 19,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e1sushi_16",
      Count = 1
    },
    Requirement_2 = {Type = "it_a8_2_5", Count = 1}
  },
  {
    Id = "81290",
    GroupId = 19,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81300",
    GroupId = 19,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "81310",
    GroupId = 19,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81320",
    GroupId = 19,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "81330",
    GroupId = 19,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "81340",
    GroupId = 20,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_3",
      Count = 1
    }
  },
  {
    Id = "81350",
    GroupId = 20,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81360",
    GroupId = 20,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4flb_3",
      Count = 1
    }
  },
  {
    Id = "81370",
    GroupId = 20,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_13",
      Count = 1
    }
  },
  {
    Id = "81380",
    GroupId = 20,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81390",
    GroupId = 20,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e1sushi_22",
      Count = 1
    }
  },
  {
    Id = "81400",
    GroupId = 20,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_6e3preingre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "81410",
    GroupId = 21,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillmt_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "81420",
    GroupId = 21,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81430",
    GroupId = 21,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e2flb_5",
      Count = 1
    }
  },
  {
    Id = "81440",
    GroupId = 21,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "81450",
    GroupId = 21,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81460",
    GroupId = 21,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_2_4", Count = 1}
  },
  {
    Id = "81470",
    GroupId = 21,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e1sushi_24",
      Count = 1
    }
  },
  {
    Id = "81480",
    GroupId = 22,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_11",
      Count = 1
    }
  },
  {
    Id = "81490",
    GroupId = 22,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81500",
    GroupId = 22,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    }
  },
  {
    Id = "81510",
    GroupId = 22,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "81520",
    GroupId = 22,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81530",
    GroupId = 22,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6rice_5",
      Count = 1
    }
  },
  {
    Id = "81540",
    GroupId = 22,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    }
  },
  {
    Id = "81550",
    GroupId = 23,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    }
  },
  {
    Id = "81560",
    GroupId = 23,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_29",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81570",
    GroupId = 23,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e6soup_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_6", Count = 1}
  },
  {
    Id = "81580",
    GroupId = 23,
    ChapterId = 8,
    Requirement_1 = {Type = "it_4_1_7", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "81590",
    GroupId = 23,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sala_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81600",
    GroupId = 23,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_6",
      Count = 1
    }
  },
  {
    Id = "81610",
    GroupId = 23,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "81620",
    GroupId = 24,
    ChapterId = 8,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "81630",
    GroupId = 24,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e1sushi_27",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81640",
    GroupId = 24,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "81650",
    GroupId = 24,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e1sushi_26",
      Count = 1
    }
  },
  {
    Id = "81660",
    GroupId = 24,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_6e2mt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81670",
    GroupId = 24,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_28",
      Count = 1
    }
  },
  {
    Id = "81680",
    GroupId = 24,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "81690",
    GroupId = 25,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "81700",
    GroupId = 25,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81710",
    GroupId = 25,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_e4sf_15", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_2",
      Count = 1
    }
  },
  {
    Id = "81720",
    GroupId = 25,
    ChapterId = 8,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_31",
      Count = 1
    }
  },
  {
    Id = "81730",
    GroupId = 25,
    ChapterId = 8,
    Requirement_1 = {Type = "ds_fd_14", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "81740",
    GroupId = 25,
    ChapterId = 8,
    Requirement_1 = {Type = "it_a8_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_8e1sushi_7",
      Count = 1
    }
  },
  {
    Id = "81750",
    GroupId = 25,
    ChapterId = 8,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  }
}
