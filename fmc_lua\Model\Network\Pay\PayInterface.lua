local Serialization = require("Model.Network.Serialization")()
Serialization.ProtocolMd5 = {
  1,
  59,
  30,
  242,
  206,
  24,
  34,
  115,
  137,
  61,
  6,
  62,
  251,
  133,
  14,
  130
}
Serialization.ProofAndGetStatusReq = {}
Serialization.ProofAndGetStatusReq.__index = Serialization.ProofAndGetStatusReq

function Serialization.ProofAndGetStatusReq.Serialize(writer, value)
  local bRet
  assert(value.appId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.appId)
  if not bRet then
    return false
  end
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.adid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.adid)
  if not bRet then
    return false
  end
  assert(value.idfa ~= nil)
  bRet = Serialization.String.Serialize(writer, value.idfa)
  if not bRet then
    return false
  end
  assert(value.idfv ~= nil)
  bRet = Serialization.String.Serialize(writer, value.idfv)
  if not bRet then
    return false
  end
  assert(value.mac ~= nil)
  bRet = Serialization.String.Serialize(writer, value.mac)
  if not bRet then
    return false
  end
  assert(value.android_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.android_id)
  if not bRet then
    return false
  end
  assert(value.gps_adid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.gps_adid)
  if not bRet then
    return false
  end
  assert(value.channelId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.channelId)
  if not bRet then
    return false
  end
  assert(value.payMethodId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.payMethodId)
  if not bRet then
    return false
  end
  assert(value.purchaseDate ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.purchaseDate)
  if not bRet then
    return false
  end
  assert(value.orderId ~= nil)
  bRet = Serialization.String.Serialize(writer, value.orderId)
  if not bRet then
    return false
  end
  assert(value.productId ~= nil)
  bRet = Serialization.String.Serialize(writer, value.productId)
  if not bRet then
    return false
  end
  assert(value.amount ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.amount)
  if not bRet then
    return false
  end
  assert(value.level ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.level)
  if not bRet then
    return false
  end
  assert(value.currency ~= nil)
  bRet = Serialization.String.Serialize(writer, value.currency)
  if not bRet then
    return false
  end
  assert(value.country ~= nil)
  bRet = Serialization.String.Serialize(writer, value.country)
  if not bRet then
    return false
  end
  assert(value.payResult ~= nil)
  bRet = Serialization.VarInt64.Serialize(writer, value.payResult)
  if not bRet then
    return false
  end
  assert(value.receiptData ~= nil)
  bRet = Serialization.String.Serialize(writer, value.receiptData)
  if not bRet then
    return false
  end
  assert(value.receiptSignature ~= nil)
  bRet = Serialization.String.Serialize(writer, value.receiptSignature)
  if not bRet then
    return false
  end
  assert(value.verifyResult ~= nil)
  bRet = Serialization.VarInt64.Serialize(writer, value.verifyResult)
  if not bRet then
    return false
  end
  assert(value.verifyJSON ~= nil)
  bRet = Serialization.String.Serialize(writer, value.verifyJSON)
  if not bRet then
    return false
  end
  assert(value.strScene ~= nil)
  bRet = Serialization.String.Serialize(writer, value.strScene)
  if not bRet then
    return false
  end
  assert(value.ctime ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.ctime)
  if not bRet then
    return false
  end
  return true
end

function Serialization.ProofAndGetStatusReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.ProofAndGetStatusReq)
  bRet, value.appId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.adid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.idfa = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.idfv = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.mac = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.android_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.gps_adid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.channelId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.payMethodId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.purchaseDate = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orderId = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.productId = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.amount = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.level = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.currency = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.country = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.payResult = Serialization.VarInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.receiptData = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.receiptSignature = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.verifyResult = Serialization.VarInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.verifyJSON = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.strScene = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ctime = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.ProofAndGetStatusResp = {}
Serialization.ProofAndGetStatusResp.__index = Serialization.ProofAndGetStatusResp

function Serialization.ProofAndGetStatusResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.expire_date ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.expire_date)
  if not bRet then
    return false
  end
  assert(value.vip_id ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.vip_id)
  if not bRet then
    return false
  end
  assert(value.sub_type ~= nil)
  bRet = Serialization.VarInt64.Serialize(writer, value.sub_type)
  if not bRet then
    return false
  end
  assert(value.stime ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.stime)
  if not bRet then
    return false
  end
  return true
end

function Serialization.ProofAndGetStatusResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.ProofAndGetStatusResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.expire_date = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.vip_id = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.sub_type = Serialization.VarInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.stime = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.IAPReq = {}
Serialization.IAPReq.__index = Serialization.IAPReq

function Serialization.IAPReq.Serialize(writer, value)
  local bRet
  assert(value.appId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.appId)
  if not bRet then
    return false
  end
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.channelId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.channelId)
  if not bRet then
    return false
  end
  assert(value.amount ~= nil)
  bRet = Serialization.VarInt64.Serialize(writer, value.amount)
  if not bRet then
    return false
  end
  assert(value.realMoney ~= nil)
  bRet = Serialization.VarInt64.Serialize(writer, value.realMoney)
  if not bRet then
    return false
  end
  assert(value.currency ~= nil)
  bRet = Serialization.String.Serialize(writer, value.currency)
  if not bRet then
    return false
  end
  assert(value.productID ~= nil)
  bRet = Serialization.String.Serialize(writer, value.productID)
  if not bRet then
    return false
  end
  assert(value.productOrder ~= nil)
  bRet = Serialization.String.Serialize(writer, value.productOrder)
  if not bRet then
    return false
  end
  assert(value.payChannel ~= nil)
  bRet = Serialization.VarInt64.Serialize(writer, value.payChannel)
  if not bRet then
    return false
  end
  assert(value.iccid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.iccid)
  if not bRet then
    return false
  end
  assert(value.level ~= nil)
  bRet = Serialization.VarInt64.Serialize(writer, value.level)
  if not bRet then
    return false
  end
  assert(value.strScene ~= nil)
  bRet = Serialization.String.Serialize(writer, value.strScene)
  if not bRet then
    return false
  end
  assert(value.ctime ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.ctime)
  if not bRet then
    return false
  end
  return true
end

function Serialization.IAPReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.IAPReq)
  bRet, value.appId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.channelId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.amount = Serialization.VarInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.realMoney = Serialization.VarInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.currency = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.productID = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.productOrder = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.payChannel = Serialization.VarInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.iccid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.level = Serialization.VarInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.strScene = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ctime = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.IAPResp = {}
Serialization.IAPResp.__index = Serialization.IAPResp

function Serialization.IAPResp.Serialize(writer, value)
  local bRet
  assert(value.resultCode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.resultCode)
  if not bRet then
    return false
  end
  return true
end

function Serialization.IAPResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.IAPResp)
  bRet, value.resultCode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.ReissueOrderReq = {}
Serialization.ReissueOrderReq.__index = Serialization.ReissueOrderReq

function Serialization.ReissueOrderReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.channelId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.channelId)
  if not bRet then
    return false
  end
  assert(value.productId ~= nil)
  bRet = Serialization.String.Serialize(writer, value.productId)
  if not bRet then
    return false
  end
  return true
end

function Serialization.ReissueOrderReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.ReissueOrderReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.channelId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.productId = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.ReissueOrderResp = {}
Serialization.ReissueOrderResp.__index = Serialization.ReissueOrderResp

function Serialization.ReissueOrderResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.order_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.order_id)
  if not bRet then
    return false
  end
  assert(value.product_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.product_id)
  if not bRet then
    return false
  end
  assert(value.token ~= nil)
  bRet = Serialization.String.Serialize(writer, value.token)
  if not bRet then
    return false
  end
  return true
end

function Serialization.ReissueOrderResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.ReissueOrderResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.order_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.product_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.token = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.reqNameMap = {
  ACProofAndGetStatus = Serialization.ProofAndGetStatusReq,
  ACIAP = Serialization.IAPReq,
  ACReissueOrder = Serialization.ReissueOrderReq
}
Serialization.respNameMap = {
  ACProofAndGetStatus = Serialization.ProofAndGetStatusResp,
  ACIAP = Serialization.IAPResp,
  ACReissueOrder = Serialization.ReissueOrderResp
}
return Serialization
