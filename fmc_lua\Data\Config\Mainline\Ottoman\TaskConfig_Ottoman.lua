return {
  {
    ChapterId = "Ottoman",
    Id = 1,
    Cost = 604,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashMidA", State = 100}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 2,
    StartConditions = {1},
    Cost = 563,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashMidB", State = 100}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 3,
    StartConditions = {2},
    Cost = 604,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashMidC", State = 100}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 4,
    StartConditions = {3},
    Cost = 728,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midFloor", State = 9},
      {
        Slot = "midFloorOld",
        State = 100
      },
      {Slot = "fountain", State = 1}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 5,
    StartConditions = {4},
    Cost = 728,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "fountain", State = 2}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 6,
    StartConditions = {5},
    Cost = 687,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "fountain", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 7,
    StartConditions = {6},
    Cost = 563,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldTreeA", State = 100}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 8,
    StartConditions = {7},
    Cost = 563,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "treeA", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 9,
    StartConditions = {8},
    Cost = 575,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "grassA", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 10,
    StartConditions = {9},
    Cost = 675,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "grassB", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 11,
    StartConditions = {10},
    Cost = 675,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "tableA", State = 1}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 12,
    StartConditions = {11},
    Cost = 625,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "tableA", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 13,
    StartConditions = {12},
    Cost = 675,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashLeftA", State = 100}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 14,
    StartConditions = {13},
    Cost = 625,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashLeftB", State = 100}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 15,
    StartConditions = {14},
    Cost = 675,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashLeftC", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 16,
    StartConditions = {15},
    Cost = 675,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftRoof", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 17,
    StartConditions = {16},
    Cost = 664,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftRag", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 18,
    StartConditions = {17},
    Cost = 617,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftCakeA", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 19,
    StartConditions = {18},
    Cost = 664,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftCakeB", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 20,
    StartConditions = {19},
    Cost = 617,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftCakeC", State = 9},
      {
        Slot = "leftCakeRag",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 21,
    StartConditions = {20},
    Cost = 805,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftSecFloor",
        State = 9
      },
      {
        Slot = "leftSecDoor",
        State = 100
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 22,
    StartConditions = {21},
    Cost = 664,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftSecWin", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 23,
    StartConditions = {22},
    Cost = 664,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftSecRag", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 24,
    StartConditions = {23},
    Cost = 664,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftSecChair",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 25,
    StartConditions = {24},
    Cost = 670,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftSecTable",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 26,
    StartConditions = {25},
    Cost = 778,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterLeft",
        State = 1
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 27,
    StartConditions = {26},
    Cost = 778,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterLeft",
        State = 2
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 28,
    StartConditions = {27},
    Cost = 724,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterLeft",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 29,
    StartConditions = {28},
    Cost = 670,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterCakeC",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 30,
    StartConditions = {29},
    Cost = 670,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftTeller", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 31,
    StartConditions = {30},
    Cost = 561,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftSunshade",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 32,
    StartConditions = {31},
    Cost = 670,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldTreeB", State = 100}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 33,
    StartConditions = {32},
    Cost = 674,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "treeB", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 34,
    StartConditions = {33},
    Cost = 727,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "grassC", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 35,
    StartConditions = {34},
    Cost = 779,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "truck", State = 1}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 36,
    StartConditions = {35},
    Cost = 727,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "truck", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 37,
    StartConditions = {36},
    Cost = 727,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "tableB", State = 1}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 38,
    StartConditions = {37},
    Cost = 674,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "tableB", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 39,
    StartConditions = {38},
    Cost = 727,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashRight", State = 1}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 40,
    StartConditions = {39},
    Cost = 727,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashRight", State = 2}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 41,
    StartConditions = {40},
    Cost = 818,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashRight", State = 3}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 42,
    StartConditions = {41},
    Cost = 762,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightRoof", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 43,
    StartConditions = {42},
    Cost = 707,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightRag", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 44,
    StartConditions = {43},
    Cost = 707,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightCandyA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 45,
    StartConditions = {44},
    Cost = 707,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightCandyB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 46,
    StartConditions = {45},
    Cost = 707,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightCandyC",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 47,
    StartConditions = {46},
    Cost = 928,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightSecFloor",
        State = 9
      },
      {
        Slot = "rightSecDoor",
        State = 100
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 48,
    StartConditions = {47},
    Cost = 707,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightSecWin",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 49,
    StartConditions = {48},
    Cost = 707,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightSecRag",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 50,
    StartConditions = {49},
    Cost = 759,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightSecChair",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 51,
    StartConditions = {50},
    Cost = 698,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightSecTable",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 52,
    StartConditions = {51},
    Cost = 759,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterRightA",
        State = 1
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 53,
    StartConditions = {52},
    Cost = 883,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterRightA",
        State = 2
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 54,
    StartConditions = {53},
    Cost = 636,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterRightA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 55,
    StartConditions = {54},
    Cost = 759,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterRightB",
        State = 1
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 56,
    StartConditions = {55},
    Cost = 883,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterRightB",
        State = 2
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 57,
    StartConditions = {56},
    Cost = 821,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterRightB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 58,
    StartConditions = {57},
    Cost = 821,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "counterRightC",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 59,
    StartConditions = {58},
    Cost = 652,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightSunshade",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 60,
    StartConditions = {59},
    Cost = 949,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midWall", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 61,
    StartConditions = {60},
    Cost = 889,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midCabinet", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 62,
    StartConditions = {61},
    Cost = 889,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midCounter", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 63,
    StartConditions = {62},
    Cost = 771,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "cabinetTea", State = 9}
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 64,
    StartConditions = {63},
    Cost = 830,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cabinetPlant",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 65,
    StartConditions = {64},
    Cost = 830,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "midCounterA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 66,
    StartConditions = {65},
    Cost = 830,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "midCounterB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Ottoman",
    Id = 67,
    StartConditions = {66},
    Cost = 830,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "midCounterC",
        State = 9
      }
    }
  }
}
