UpcomingEventWindow = setmetatable({}, BaseWindow)
UpcomingEventWindow.__index = UpcomingEventWindow

function UpcomingEventWindow:BeforeOpenCheck()
  return GM.UpcomingEventModel:HasUpcomingEvent()
end

function UpcomingEventWindow:Init()
  local model = GM.UpcomingEventModel
  if model:HasUpcomingEvent() then
    self.m_titleText.text = model:GetTitleText()
    self.m_descText.text = model:GetDescText()
    self.m_buttonText.text = model:GetButtonText()
    self.m_picImg.enabled = false
    if model:GetPictureLink() ~= nil then
      LoadUrlImage:LoadSprite(model:GetPictureLink(), function(bSuccess, sprite)
        if bSuccess and sprite and self.m_picImg and not self.m_picImg:IsNull() then
          self.m_picImg.sprite = sprite
          self.m_picImg.enabled = true
          SpriteUtil.SetNativeSize(self.m_picImg)
        end
      end, true)
    end
  end
  self:LogWindowAction(EBIType.UIActionType.Open, EBIReferType.UserClick, tostring(model:GetId()))
  EventDispatcher.AddListener(EEventType.UpcomingEventStateChanged, self, self.OnUpcomingEventStateChanged)
end

function UpcomingEventWindow:OnUpcomingEventStateChanged()
  if not GM.UpcomingEventModel:HasUpcomingEvent() then
    self:Close()
  end
end
