local Step = {HighlightOwnCell = "1", ClickGo = "2"}
local EStep2TextKey = {
  [Step.HighlightOwnCell] = "tutorial_bakeout_1",
  [Step.ClickGo] = "tutorial_bakeout_2"
}
local EStep2TextAnchorPercent = {
  [Step.HighlightOwnCell] = {30, 57},
  [Step.ClickGo] = 70
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self.OnViewOpened)
  EventDispatcher.AddListener(EEventType.OnViewWillClose, self, self.OnViewWillClose)
  EventDispatcher.AddListener(EEventType.BakeOutStateChanged, self, self.OnBakeOutStateChanged)
  self:OnBakeOutStateChanged()
end

function Executer:OnViewOpened(msg)
  if not self.m_bExecuteStep1 and msg.name == UIPrefabConfigName.BakeOutMainWindow then
    self:_ExecuteStep1()
  end
end

function Executer:OnViewWillClose(msg)
  if self.m_strOngoingDatas == Step.ClickGo and msg.name == UIPrefabConfigName.BakeOutMainWindow then
    if self.m_highightTransform ~= nil then
      TutorialHelper.DehighlightForUI(self.m_highightTransform)
      self.m_highightTransform = nil
    end
    self:Finish(self.m_gesture)
  elseif self.m_strOngoingDatas == Step.HighlightOwnCell and msg.name == UIPrefabConfigName.SettingWindow then
    self:_ExecuteStep2()
  end
end

function Executer:OnBakeOutStateChanged()
  if StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    return
  end
  local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  if bakeOutModel ~= nil and bakeOutModel:GetState(false) ~= ActivityState.Started then
    if self.m_highightTransform ~= nil then
      TutorialHelper.DehighlightForUI(self.m_highightTransform)
      self.m_highightTransform = nil
    end
    self:Finish(self.m_gesture)
  end
end

function Executer:_ExecuteStep1()
  if self:CheckOtherTutorialInStrong() then
    return
  end
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  self.m_bExecuteStep1 = true
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.HighlightOwnCell
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BakeOutMainWindow)
    if mainWindow ~= nil and mainWindow:GetOwnCell() ~= nil then
      local transform = mainWindow:GetOwnCell().gameObject.transform
      TutorialHelper.UpdateMaskOnBoard(transform.position, transform.sizeDelta, function()
        self:_ExecuteStep2()
      end, false)
      local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
      local top3 = bakeOutModel and bakeOutModel:GetCurRank() <= 3
      TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas][top3 and 2 or 1])
    else
      TutorialHelper.WholeMask(function()
        self:_ExecuteStep2()
      end)
      TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas][2])
    end
  end, 0.5)
end

function Executer:_ExecuteStep2()
  if GM.UserProfileModel:IsNowDefaultName() and not GM.TutorialModel:IsTutorialFinished(ETutorialId.ChangeName) then
    TutorialHelper.HideDialog()
    GM.UIManager:OpenView(UIPrefabConfigName.SettingWindow)
    return
  end
  self.m_strOngoingDatas = Step.ClickGo
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BakeOutMainWindow)
  if mainWindow ~= nil and mainWindow:GetGoBtnGo() ~= nil then
    self.m_highightTransform = mainWindow:GetGoBtnGo().transform
    TutorialHelper.HighlightForUI(self.m_highightTransform)
  end
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(self.m_highightTransform)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
