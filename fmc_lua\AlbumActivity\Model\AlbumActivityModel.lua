AlbumGoldCardStarBase = 100
AlbumStarType = {
  star1 = 1,
  star2 = 2,
  star3 = 3,
  star4 = 4,
  star5 = 5,
  gstar1 = AlbumGoldCardStarBase + 1,
  gstar2 = AlbumGoldCardStarBase + 2,
  gstar3 = AlbumGoldCardStarBase + 3,
  gstar4 = AlbumGoldCardStarBase + 4,
  gstar5 = AlbumGoldCardStarBase + 5
}
AlbumStarTypePrefix = {Normal = "star", Gold = "gstar"}
EAlbumPackPrefix = {Pack = "pack_", GPack = "gpack_"}
local AlbumStarBIName = "album_star"
AlbumActivityModel = setmetatable({}, BaseActivityModel)
AlbumActivityModel.__index = AlbumActivityModel
local DBColumnValue = "value"
local NotNewCardStarPre = "notNewCardStar"
local CacheProperty = "cacheProperty"
local RedHintKey = "redHintKey"
local RecyleCardKey = "recyleCardKey"
local AlreadyCycleCardKey = "AlreadyCycleCardKey"
local StarRedTipsGear = "srtg"
local JokerCardKey = "jokerCardKey"
local AlbumGrade = "albumGrade"
AlbumActivityModel.EventKey = {
  RedRefresh = 1,
  CycleRefresh = 2,
  JokerCountRefresh = 3,
  GetNewCard = 4
}

function AlbumActivityModel:Init(activityType, virtualDBTable)
  self.m_activityDefinition = AlbumActivityDefinition[activityType]
  self.m_tokenHelper = ActivityTokenHelper.Create(self, virtualDBTable)
  BaseActivityModel.Init(self, activityType, virtualDBTable)
  self:LoadDefaultConfig()
end

function AlbumActivityModel:Destroy()
  BaseActivityModel.Destroy(self)
  self.m_tokenHelper:Destroy()
end

function AlbumActivityModel.GetActiveModel()
  local activeAlbumModel
  for albumType, _ in pairs(AlbumActivityDefinition) do
    local model = GM.ActivityManager:GetModel(albumType)
    if model:IsActivityOpen() then
      activeAlbumModel = model
      break
    end
  end
  return activeAlbumModel
end

function AlbumActivityModel:LoadDefaultConfig()
  self.m_cacheProperty = {}
  local strCache = self.m_dbTable:GetValue(CacheProperty, "value")
  if strCache ~= nil and strCache ~= "" then
    self.m_cacheProperty = json.decode(strCache)
  end
  self.m_redPointData = {}
  local strRedCache = self.m_dbTable:GetValue(RedHintKey, "value")
  if strRedCache ~= nil and strRedCache ~= "" then
    self.m_redPointData = json.decode(strRedCache)
  end
  self.m_recyleCardData = {}
  local strRecyleCache = self.m_dbTable:GetValue(RecyleCardKey, "value")
  if strRecyleCache ~= nil and strRecyleCache ~= "" then
    self.m_recyleCardData = json.decode(strRecyleCache)
  end
  self.m_alReadyrecyleCards = {}
  local strCycleCardsCache = self.m_dbTable:GetValue(AlreadyCycleCardKey, "value")
  if strCycleCardsCache ~= nil and strCycleCardsCache ~= "" then
    self.m_alReadyrecyleCards = json.decode(strCycleCardsCache)
  end
end

function AlbumActivityModel:LoadFileConfig()
  if not Table.IsEmpty(self.m_mapCards) then
    return
  end
  local arrCardConfig = require("Data.Config.AlbumCardConfig")
  self.m_mapAllCards = {}
  for i, v in ipairs(arrCardConfig) do
    self.m_mapAllCards[v.cardId] = v
  end
end

function AlbumActivityModel:_LoadOtherServerConfig(config)
  self:LoadFileConfig()
  self.m_tokenHelper:LoadConfig(config)
  self.m_maxCardStar = 0
  self.m_mapCards = {}
  self.m_mapCardByStar = {}
  self.m_albumConfig = config.album_config[1]
  self.m_mapCardPacks = {}
  for i, v in ipairs(config.album_pack_config) do
    self.m_mapCardPacks[v.packId] = v
  end
  self.m_mapCardSet = {}
  self.m_arrCardSet = {}
  local mapStar = {}
  local cardInfo
  for _, v in ipairs(config.album_set_config) do
    self.m_mapCardSet[v.setId] = v
    self.m_arrCardSet[#self.m_arrCardSet + 1] = v
    for _, cardId in ipairs(v.cardGroup or {}) do
      cardInfo = Table.ShallowCopy(self.m_mapAllCards[cardId])
      if cardInfo ~= nil then
        cardInfo.setId = v.setId
        if cardInfo.star > self.m_maxCardStar then
          self.m_maxCardStar = cardInfo.star
        end
        if cardInfo.gold == 1 then
          cardInfo.star = cardInfo.star + AlbumGoldCardStarBase
        end
        self.m_mapCards[cardId] = cardInfo
        self.m_mapCardByStar[cardInfo.star] = self.m_mapCardByStar[cardInfo.star] or {}
        self.m_mapCardByStar[cardInfo.star][#self.m_mapCardByStar[cardInfo.star] + 1] = cardInfo.cardId
        mapStar[cardInfo.star] = true
      else
        Log.Error("卡牌id不存在: " .. cardId)
      end
    end
  end
  self.m_arrAllStar = Table.GetKeys(mapStar)
  table.sort(self.m_arrAllStar)
  table.sort(self.m_arrCardSet, function(a, b)
    return a.order < b.order
  end)
  if config.album_recycle_config ~= nil then
    self.m_recyleConfigs = Table.ShallowCopy(config.album_recycle_config or {})
    table.sort(self.m_recyleConfigs, function(a, b)
      return a.star < b.star
    end)
    self:GetEvent():Call(AlbumActivityModel.EventKey.CycleRefresh)
  end
  self:UpdateContent()
end

function AlbumActivityModel:UpdateContent()
  if Table.IsEmpty(self.m_mapCards) then
    return
  end
  self.m_mapCacheStar = {}
  self.m_arrCacheNoCollect = {}
  local ct = 0
  for k, v in pairs(self.m_mapCards) do
    ct = self:GetCardCount(k)
    if 0 < ct then
      self.m_mapCacheStar[v.star] = self.m_mapCacheStar[v.star] or 0
      self.m_mapCacheStar[v.star] = self.m_mapCacheStar[v.star] + ct
    else
      self.m_arrCacheNoCollect[#self.m_arrCacheNoCollect + 1] = k
    end
  end
  local strStar
  local star = 0
  local isGold
  for k, v in pairs(self.m_mapCardPacks) do
    star = 0
    isGold = false
    if v.fixedSpread then
      for k1, v1 in pairs(v.fixedSpread) do
        strStar = v1[PROPERTY_TYPE]
        local tCount = v1[PROPERTY_COUNT]
        local tstar = self:GetStarNumber(strStar)
        tstar = tonumber(tstar)
        if star < tstar then
          star = tstar
        end
        if self:IsGoldStar(strStar) then
          isGold = true
        end
      end
    end
    v.cardTipInfo = {
      star = star,
      count = v.spreadNum,
      isGold = isGold
    }
  end
  self:_UpdateProgress()
  self:LoadDefaultConfig()
  self:UpdateSurplusCardData()
end

function AlbumActivityModel:GetCardTipInfo(packId)
  local PackConfig = self:GetCardPackConfig(packId)
  if PackConfig == nil then
    return nil
  end
  return PackConfig.cardTipInfo
end

function AlbumActivityModel:GetAlbumConfig()
  return self.m_albumConfig
end

function AlbumActivityModel:GetAlbumPack()
  return self.m_mapCardPacks
end

function AlbumActivityModel:GetCardSets()
  return self.m_arrCardSet
end

function AlbumActivityModel:GetCardSetRewards(setId)
  return self.m_mapCardSet[setId].reward
end

function AlbumActivityModel:GetCardInfo(cardId)
  return self.m_mapCards[cardId]
end

function AlbumActivityModel:GetSetCollectProgress(setId)
  return self.m_mapProgressSets[setId], #self.m_mapCardSet[setId].cardGroup
end

function AlbumActivityModel:GetAlbumCollectProgress()
  return self.m_albumCurProgress, self.m_albumMaxProgress
end

function AlbumActivityModel:GetSetCompleteProgress()
  local curProgress = 0
  for k, v in pairs(self.m_mapProgressSets) do
    if v >= #self.m_mapCardSet[k].cardGroup then
      curProgress = curProgress + 1
    end
  end
  return curProgress, #self.m_albumConfig.setGroup
end

function AlbumActivityModel:GetCardCountByStar(star)
  return self.m_mapCacheStar[star] or 0
end

function AlbumActivityModel:GetNotNewCardStarCount(star)
  return self.m_dbTable:GetValue(NotNewCardStarPre .. star, DBColumnValue) or 0
end

function AlbumActivityModel:SetNotNewCardStarCount(star, count)
  self.m_dbTable:Set(NotNewCardStarPre .. star, DBColumnValue, count)
end

function AlbumActivityModel:AddNotNewCardStarCount(star)
  local count = self:GetNotNewCardStarCount(star)
  self:SetNotNewCardStarCount(star, count + 1)
end

function AlbumActivityModel:IsGoldCard(cardId)
  if self.m_mapCards[cardId] == nil then
    return false
  end
  return self.m_mapCards[cardId].gold == 1
end

function AlbumActivityModel:IsGoldSet(setID)
  for i, cardId in ipairs(self.m_mapCardSet[setID].cardGroup) do
    if self:IsGoldCard(cardId) then
      return true
    end
  end
  return false
end

function AlbumActivityModel:IsGoldStar(strStar)
  return StringUtil.StartWith(strStar, AlbumStarTypePrefix.Gold)
end

function AlbumActivityModel:GetShowStar(star)
  return star % AlbumGoldCardStarBase
end

function AlbumActivityModel:GetCardCount(cardId, bIsRealy)
  local num = self.m_dbTable:GetValue(cardId, DBColumnValue) or 0
  if bIsRealy then
    local cycleNum = self.m_alReadyrecyleCards[cardId] or 0
    num = num - cycleNum
  end
  return num
end

function AlbumActivityModel:AcquireCard(cardId, scene)
  local count = self:GetCardCount(cardId)
  self.m_dbTable:Set(cardId, DBColumnValue, count + 1)
  local star = self.m_mapCards[cardId].star
  self.m_mapCacheStar[star] = self.m_mapCacheStar[star] or 0
  self.m_mapCacheStar[star] = self.m_mapCacheStar[star] + 1
  GM.BIManager:LogAcquire(cardId, 1, scene, true)
  if 0 < count then
    self:AddNotNewCardStarCount(star)
    self:AddSurplusCard(cardId)
    GM.BIManager:LogAcquire(AlbumStarBIName, self:GetShowStar(star), scene, true)
  else
    self.m_albumCurProgress = self.m_albumCurProgress + 1
    local cardInfo = self:GetCardInfo(cardId)
    self.m_mapProgressSets[cardInfo.setId] = self.m_mapProgressSets[cardInfo.setId] + 1
    Table.ListRemove(self.m_arrCacheNoCollect, cardId)
    self:SetNotNewCardStarCount(star, 0)
    self:ShowRedPoint(cardInfo.setId, cardId)
    self:LogActivity(EBIType.ActivityAddScore, cardId)
    local setId
    local bAlbum = false
    local curPro, maxPro = self:GetSetCollectProgress(cardInfo.setId)
    if curPro == maxPro then
      setId = cardInfo.setId
      local rewards = self.m_mapCardSet[cardInfo.setId].reward
      RewardApi.CryptRewards(rewards, true)
      RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.AlbumSetRewards, EGameMode.Board, CacheItemType.Stack)
      local setPro = self:GetSetCompleteProgress()
      self:LogActivity(EBIType.ActivityRankUp, setPro)
    end
    if self.m_albumCurProgress == self.m_albumMaxProgress then
      bAlbum = true
      local albumReward = self.m_albumConfig.reward
      RewardApi.CryptRewards(albumReward, true)
      RewardApi.AcquireRewardsLogic(albumReward, EPropertySource.Give, EBIType.AlbumRewards, EGameMode.Board, CacheItemType.Stack)
    end
    self.event:Call(AlbumActivityModel.EventKey.RedRefresh)
    return setId, bAlbum
  end
end

function AlbumActivityModel:_UpdateProgress()
  self.m_albumCurProgress = 0
  self.m_albumMaxProgress = 0
  local arrSets
  local curSetProgress = 0
  self.m_mapProgressSets = {}
  for i, setId in ipairs(self.m_albumConfig.setGroup) do
    arrSets = self.m_mapCardSet[setId]
    curSetProgress = 0
    for i, cardId in ipairs(arrSets.cardGroup) do
      if 0 < self:GetCardCount(cardId) then
        curSetProgress = curSetProgress + 1
      end
      self.m_albumMaxProgress = self.m_albumMaxProgress + 1
    end
    self.m_mapProgressSets[setId] = curSetProgress
    self.m_albumCurProgress = self.m_albumCurProgress + curSetProgress
  end
end

function AlbumActivityModel:IsAllGoldCardUncollect()
  if Table.IsEmpty(self.m_arrCacheNoCollect) then
    return false
  end
  for i, v in ipairs(self.m_arrCacheNoCollect) do
    if not self:IsGoldCard(v) then
      return false
    end
  end
  return true
end

function AlbumActivityModel:IsActivityOpen()
  return self:GetState() == ActivityState.Started and not self:HasCollectAllCard()
end

function AlbumActivityModel:HasCollectAllCard()
  return self.m_albumCurProgress == self.m_albumMaxProgress
end

function AlbumActivityModel:GetMapEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.Album,
    entryPrefabName = self.m_activityDefinition.EntryPrefabName,
    hudKey = self.m_activityDefinition.EntryButtonKey,
    checkFun = function()
      return self:IsActivityOpen()
    end
  }
end

function AlbumActivityModel.GetButtonTarget(propertyType, flyCount)
  if (propertyType == EPropertyType.JokerCommonCard or propertyType == EPropertyType.JokerGoldenCard) and flyCount ~= nil then
    local activityModel = AlbumActivityModel.GetActiveModel()
    local definition = activityModel and activityModel:GetActivityDefinition()
    if definition == nil then
      return
    end
    local prefabName = activityModel:GetJokerCount() > 0 and UIPrefabConfigName.AlbumJokerCollectEntry or definition.CollectEntryPrefabName
    if GM.SceneManager:GetGameMode() == EGameMode.Main then
      return TutorialHelper.GetHudButton(definition.EntryButtonKey)
    else
      do
        local go = BoardEntryShowArea.Add(prefabName, flyCount)
        if go ~= nil then
          local button = go:GetLuaTable()
          button:RegisterFlyEndCallback(function()
            BoardEntryShowArea.Finish(prefabName)
          end)
          return button
        end
      end
    end
  end
end

function AlbumActivityModel:_DropData()
  BaseActivityModel._DropData(self)
  self:LoadDefaultConfig()
  self:UpdateContent()
end

function AlbumActivityModel:_CalculateState()
  if self.m_config == nil then
    return ActivityState.Released, nil
  end
  local serverTime = GM.GameModel:GetServerTime()
  if serverTime < self.m_config.sTime then
    return ActivityState.Preparing, self.m_config.sTime
  elseif serverTime < self.m_config.eTime and self.m_albumCurProgress < self.m_albumMaxProgress then
    return ActivityState.Started, self.m_config.eTime
  elseif self.m_config.rTime and serverTime < self.m_config.rTime then
    return ActivityState.Ended, self.m_config.rTime
  else
    return ActivityState.Released, nil
  end
end

function AlbumActivityModel:_OnStateChanged()
  local state = self:GetState(false)
  if state ~= ActivityState.Started then
    self.m_cacheProperty = {}
    self:SaveCacheProperty()
  end
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
  self.event:Call(AlbumActivityModel.EventKey.RedRefresh)
  self.event:Call(AlbumActivityModel.EventKey.JokerCountRefresh)
end

function AlbumActivityModel:GetResourceLabels()
  return self.m_activityDefinition.ResourceLabels
end

function AlbumActivityModel:CanAddScore()
  return self:IsActivityOpen()
end

function AlbumActivityModel:GetCardPackConfig(PackType)
  if self.m_mapCardPacks == nil then
    return nil
  end
  return self.m_mapCardPacks[PackType]
end

function AlbumActivityModel:GetStarNumber(strStar)
  return AlbumStarType[strStar]
end

function AlbumActivityModel:GetGachaCardsByStar(star)
  return self.m_mapCardByStar[star]
end

function AlbumActivityModel:CanPositiveBuff(star)
  local insures = self.m_albumConfig.insure
  for _, eleInsure in pairs(insures) do
    local curStar = self:GetStarNumber(eleInsure[PROPERTY_TYPE])
    if curStar == star then
      local limitNumber = eleInsure[PROPERTY_COUNT]
      if self:GetNotNewCardStarCount(star) >= limitNumber - 1 then
        return true
      end
    end
  end
end

function AlbumActivityModel:AddTestLog(TestLog, strKey, strValue)
  if not GameConfig.IsTestMode() or TestLog == nil then
    return
  end
  TestLog[strKey] = strValue
end

function AlbumActivityModel:RemoveDuplicatesCards(collectedCards, CurCards)
  local target = {}
  for index, screenCard in ipairs(CurCards) do
    local bFind = false
    for _, strCard in pairs(collectedCards) do
      if screenCard == strCard then
        bFind = true
        break
      end
    end
    if not bFind then
      table.insert(target, screenCard)
    end
  end
  return target
end

function AlbumActivityModel:FilterCardsByStar(PackConfig, TestLog)
  local FixedSpreadStar = PackConfig.fixedSpread or {}
  local RandomSpreadStar = PackConfig.spreadWeight or {}
  local listTarget = {}
  for _, v in pairs(FixedSpreadStar) do
    local strStar = v[PROPERTY_TYPE]
    local eleCount = v[PROPERTY_COUNT]
    for i = 1, eleCount do
      table.insert(listTarget, strStar)
    end
  end
  self:AddTestLog(TestLog, "FixedStarNum", FixedSpreadStar)
  local remainRandomNum = PackConfig.spreadNum - #listTarget
  if remainRandomNum <= 0 then
    return listTarget
  end
  local allWeight = 0
  for _, ele in pairs(RandomSpreadStar) do
    allWeight = allWeight + ele.Weight
  end
  local AddRandomFunc = function(num, SpreadConfig)
    for i = 1, num do
      local randNum = math.random() * allWeight
      local curWeight = 0
      for _, ele in ipairs(SpreadConfig) do
        curWeight = curWeight + ele.Weight
        if randNum < curWeight then
          table.insert(listTarget, ele.Item)
          break
        end
      end
    end
  end
  AddRandomFunc(remainRandomNum, RandomSpreadStar)
  self:AddTestLog(TestLog, "resultNature", Table.ShallowCopy(listTarget))
  return listTarget
end

function AlbumActivityModel:TestOpenPack()
  local cardIdList = {}
  local finishedSetList = {}
  local allAlbumFinished = false
  local gachaValues = TestAlbumSelectedGachaContent.GetInstance():GetValues()
  for _, value in ipairs(gachaValues) do
    local cardId = self.m_arrCardSet[value.SetIndex].cardGroup[value.CardIndex]
    table.insert(cardIdList, cardId)
    local finishedSet, allAlbumFinishedFlag = self:AcquireCard(cardId, EBIType.Test)
    table.insert(finishedSetList, finishedSet)
    allAlbumFinished = allAlbumFinished or allAlbumFinishedFlag
  end
  EventDispatcher.DispatchEvent(EEventType.ActiveQuestProgressGain, {
    type = "card",
    num = #cardIdList,
    needwait = true
  })
  return cardIdList, finishedSetList, allAlbumFinished
end

function AlbumActivityModel:CheckPityGachaCard(i32Star, CanGachaCards)
  local ScreenGachaCards = {}
  local testState = 0
  if self:CanPositiveBuff(i32Star) then
    for _, card in ipairs(CanGachaCards) do
      if self:GetCardCount(card) == 0 then
        table.insert(ScreenGachaCards, card)
      end
    end
    testState = 2
  else
    ScreenGachaCards = CanGachaCards
    testState = 1
  end
  return ScreenGachaCards, testState
end

function AlbumActivityModel:OpenPackOneConfig(PackType, TestLog)
  local PackConfig = self:GetCardPackConfig(PackType)
  local listTarget = self:FilterCardsByStar(PackConfig, TestLog)
  local GachCardFinishSet = {}
  local bAllAlbum = false
  local GachaCard = {}
  local AllGachaCard = {}
  local openGoldCard = {}
  local mapNewCard = {}
  for _, strStar in ipairs(listTarget) do
    local i32Star = self:GetStarNumber(strStar)
    local CanGachaCards = self:GetGachaCardsByStar(i32Star) or {}
    local ScreenGachaCards, testState = self:CheckPityGachaCard(i32Star, CanGachaCards)
    local ScreenTempCards = self:RemoveDuplicatesCards(AllGachaCard, ScreenGachaCards)
    if #ScreenTempCards == 0 then
      testState = testState * 10
      ScreenTempCards = self:RemoveDuplicatesCards(AllGachaCard, CanGachaCards)
    end
    ScreenGachaCards = ScreenTempCards
    if #ScreenGachaCards == 0 then
      Log.Assert(false, "当前星级没有卡片" .. tostring(i32Star))
    else
      local selectRandom = {}
      for _, eleCardType in ipairs(ScreenGachaCards) do
        local cardConfig = self:GetCardInfo(eleCardType)
        table.insert(selectRandom, cardConfig)
      end
      local strRandCard = Table.ListWeightSelectOne(selectRandom, "weight")
      local bNewCard = false
      if self:GetCardCount(strRandCard.cardId) == 0 then
        mapNewCard[strRandCard.cardId] = true
        bNewCard = true
      end
      local finishSet, curbAllAlbum = self:AcquireCard(strRandCard.cardId, EBIType.AlbumAcquirePackCard)
      table.insert(GachCardFinishSet, finishSet)
      bAllAlbum = bAllAlbum or curbAllAlbum
      table.insert(GachaCard, strRandCard.cardId)
      table.insert(AllGachaCard, strRandCard.cardId)
      if self:IsGoldCard(strRandCard.cardId) then
        table.insert(openGoldCard, strRandCard.cardId)
      end
      bAllAlbum = bAllAlbum or curbAllAlbum
      table.insert(AllGachaCard, strRandCard.cardId)
      if GameConfig.IsTestMode() and TestLog ~= nil then
        local eleGachaInfo = {}
        eleGachaInfo.state = testState
        eleGachaInfo.optionals = selectRandom
        eleGachaInfo.targetcard = strRandCard.cardId
        eleGachaInfo.i32Star = i32Star
        eleGachaInfo.newCard = bNewCard
        table.insert(TestLog.listGachaResult, eleGachaInfo)
      end
    end
  end
  table.sort(GachaCard, function(cardId1, cardId2)
    return self:CompareCard(cardId1, cardId2, mapNewCard)
  end)
  GM.BIManager:LogUseItem(PackType, 1, EBIType.AlbumOpenPack, self:GetType())
  return GachaCard, mapNewCard, GachCardFinishSet, bAllAlbum
end

function AlbumActivityModel:GetUncollectedCardsByStarLevel(i32Star)
  local target = {}
  local CanGachaCards = self:GetGachaCardsByStar(i32Star)
  for _, card in ipairs(CanGachaCards) do
    if self:GetCardCount(card) == 0 then
      table.insert(target, card)
    end
  end
  return target, CanGachaCards
end

function AlbumActivityModel:ComparePack(pack1, pack2, listStrKey)
  if Table.IsEmpty(listStrKey) then
    return pack2 < pack1
  end
  local strKey = listStrKey[1]
  table.remove(listStrKey, 1)
  local value1 = StringUtil.StartWith(pack1, strKey)
  local value2 = StringUtil.StartWith(pack2, strKey)
  if value1 and value2 then
    return self:ComparePack(pack1, pack2, listStrKey)
  elseif value1 then
    return false
  elseif value2 then
    return true
  end
  return pack2 < pack1
end

function AlbumActivityModel:CompareCard(card1, card2, mapNewCards)
  local bNew1 = mapNewCards[card1] or false
  local bNew2 = mapNewCards[card2] or false
  if bNew1 ~= bNew2 then
    return bNew1
  end
  local cardData1 = self.m_mapCards[card1]
  local cardData2 = self.m_mapCards[card2]
  return cardData1.star > cardData2.star
end

function AlbumActivityModel:GetItemCachePropertys()
  if self.m_cacheProperty == nil then
    return
  end
  local target = {}
  for key, count in pairs(self.m_cacheProperty) do
    for i = 1, count do
      table.insert(target, key)
    end
  end
  table.sort(target, function(a, b)
    return self:ComparePack(a, b, {"pack_", "gpack_"})
  end)
  return target
end

function AlbumActivityModel:ConsumeOneCardPack(propertyType, startPos)
  self.m_cacheProperty[propertyType] = self.m_cacheProperty[propertyType] - 1
  self:SaveCacheProperty()
  local openCards, mapNewCards, finishSetList, allAlbumFinished = self:OpenPackOneConfig(propertyType)
  GM.UIManager:OpenView(UIPrefabConfigName.AlbumActivityGachaWindow, self:GetType(), openCards, mapNewCards, finishSetList, allAlbumFinished, propertyType, startPos)
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
end

function AlbumActivityModel:ConsumeNextCardPack(startPos)
  local arrProperty = self:GetItemCachePropertys()
  if not Table.IsEmpty(arrProperty) then
    self:ConsumeOneCardPack(arrProperty[1], startPos)
  end
end

function AlbumActivityModel:ConsumeAllCardPacks()
  local arrProperty = self:GetItemCachePropertys()
  if Table.IsEmpty(arrProperty) then
    return
  end
  local mapCard2Count = {}
  local mapNewCards = {}
  local finishSetList = {}
  local allAlbumFinished = false
  local tmpCards, tmpMapNewCards, tmpSetList
  for _, propertyType in ipairs(arrProperty) do
    if allAlbumFinished then
      break
    end
    tmpCards, tmpMapNewCards, tmpSetList, allAlbumFinished = self:OpenPackOneConfig(propertyType)
    self.m_cacheProperty[propertyType] = self.m_cacheProperty[propertyType] - 1
    for _, cardId in ipairs(tmpCards) do
      mapCard2Count[cardId] = (mapCard2Count[cardId] or 0) + 1
    end
    for cardId, _ in pairs(tmpMapNewCards) do
      mapNewCards[cardId] = true
    end
    Table.ListAppend(finishSetList, tmpSetList)
  end
  self:SaveCacheProperty()
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
  return mapCard2Count, mapNewCards, finishSetList, allAlbumFinished
end

function AlbumActivityModel:IsAlbumPack(propertyType)
  return self.m_mapCardPacks and self.m_mapCardPacks[propertyType] ~= nil
end

function AlbumActivityModel.IsAlbumPackReward(propertyType)
  for _, prefix in pairs(EAlbumPackPrefix) do
    if StringUtil.StartWith(propertyType, prefix) then
      return true
    end
  end
  return false
end

function AlbumActivityModel.CheckAlbumPackValid(propertyType)
  local activeAlbumModel = AlbumActivityModel.GetActiveModel()
  return activeAlbumModel and activeAlbumModel:IsAlbumPack(propertyType)
end

function AlbumActivityModel.IsAlbumJokerCard(propertyType)
  return propertyType == EPropertyType.JokerCommonCard or propertyType == EPropertyType.JokerGoldenCard
end

function AlbumActivityModel.CheckJokerCardValid(propertyType)
  return AlbumActivityModel.GetActiveModel() ~= nil
end

function AlbumActivityModel:IsGoldenJokerCardInCurrent()
  local arr = self:GetJokerCards()
  return not Table.IsEmpty(arr) and arr[1].type == EPropertyType.JokerGoldenCard
end

function AlbumActivityModel:GetCurrentJokerCardImg()
  local img = ImageFileConfigName.album_joker_card
  if self:IsGoldenJokerCardInCurrent() then
    img = ImageFileConfigName.album_joker_card
  end
  return img
end

function AlbumActivityModel:IsGoldenJokerCardInUse()
  local arr = self:GetJokerCards()
  if Table.IsEmpty(arr) then
    return false
  end
  local jokerCard = arr[1]
  if AlbumActivityModel.UseNewJokerCard then
    jokerCard = arr[#arr]
  end
  return jokerCard.type == EPropertyType.JokerGoldenCard
end

function AlbumActivityModel:GetUseJokerCardImg()
  local img = ImageFileConfigName.album_joker_card
  if self:IsGoldenJokerCardInUse() then
    img = ImageFileConfigName.album_joker_card
  end
  return img
end

function AlbumActivityModel:AcquireActivityCardPack(Property)
  if not self:IsActivityOpen() then
    return
  end
  if self.m_cacheProperty[Property[PROPERTY_TYPE]] == nil then
    self.m_cacheProperty[Property[PROPERTY_TYPE]] = 0
  end
  self.m_cacheProperty[Property[PROPERTY_TYPE]] = self.m_cacheProperty[Property[PROPERTY_TYPE]] + Property[PROPERTY_COUNT]
  self:SaveCacheProperty()
  EventDispatcher.DispatchEvent(EEventType.CacheActivityItems, {
    eGameMode = EGameMode.Board,
    hasAnimation = true
  })
end

function AlbumActivityModel:SaveCacheProperty()
  self.m_dbTable:Set(CacheProperty, DBColumnValue, json.encode(self.m_cacheProperty))
end

function AlbumActivityModel:ShowRedPoint(setID, cardId)
  if self.m_redPointData[setID] == nil then
    self.m_redPointData[setID] = {}
  end
  self.m_redPointData[setID][cardId] = true
  self:SaveRedData()
end

function AlbumActivityModel:IsShowRedPointIcon()
  return not Table.IsEmpty(self.m_redPointData) or self:GetJokerCount() > 0
end

function AlbumActivityModel:GetRedPointInfoBySetID(setID)
  return self.m_redPointData[setID] or {}
end

function AlbumActivityModel:HideRedPoint(setID)
  self.m_redPointData[setID] = nil
  self:SaveRedData()
  self.event:Call(AlbumActivityModel.EventKey.RedRefresh)
end

function AlbumActivityModel:SaveRedData()
  self.m_dbTable:Set(RedHintKey, "value", json.encode(self.m_redPointData))
end

function AlbumActivityModel:GetEvent()
  return self.event
end

function AlbumActivityModel:GetActivityDefinition()
  return self.m_activityDefinition
end

function AlbumActivityModel:GetCurrentCycleInfo()
  return self.m_recyleCardData
end

function AlbumActivityModel:GetCycleCardConfig()
  return self.m_recyleConfigs
end

function AlbumActivityModel:AddSurplusCard(cardId)
  local cardConfig = self.m_mapCards[cardId]
  if cardConfig == nil then
    return
  end
  local star = self:GetShowStar(cardConfig.star)
  table.insert(self.m_SurplusCardList[star], cardId)
  table.sort(self.m_SurplusCardList[star])
  self:GetEvent():Call(AlbumActivityModel.EventKey.CycleRefresh)
end

function AlbumActivityModel:UpdateSurplusCardData()
  self.m_SurplusCardList = {}
  for i = 1, self.m_maxCardStar do
    self.m_SurplusCardList[i] = {}
  end
  for _, cardInfo in pairs(self.m_mapCards) do
    if 1 < self:GetCardCount(cardInfo.cardId, true) then
      local num = self:GetCardCount(cardInfo.cardId, true) - 1
      for i = 1, num do
        table.insert(self.m_SurplusCardList[self:GetShowStar(cardInfo.star)], cardInfo.cardId)
      end
    end
  end
  for i = 1, self.m_maxCardStar do
    table.sort(self.m_SurplusCardList[i])
  end
end

function AlbumActivityModel:GetSurplusCardStar()
  local star = 0
  for i = 1, self.m_maxCardStar do
    local otherStar = 0
    if self.m_SurplusCardList[i] ~= nil then
      otherStar = #self.m_SurplusCardList[i]
    end
    star = star + i * otherStar
  end
  return star
end

function AlbumActivityModel:TryBuyCfg(index)
  if self.m_recyleConfigs[index] == nil then
    return false
  end
  if self.m_recyleCardData[tostring(index)] ~= nil and self.m_recyleCardData[tostring(index)] > GM.GameModel:GetServerTime() then
    return false
  end
  if self:GetSurplusCardStar() < self.m_recyleConfigs[index].star then
    return false
  end
  local needStar = self.m_recyleConfigs[index].star
  local comb, useStar = self:FindBestCombination(needStar)
  if useStar == 0 then
    Log.Error("理论至少存在一种组合，请检查!")
    return false
  end
  local selectCards = {}
  for star, num in ipairs(comb) do
    for i = 1, num do
      table.insert(selectCards, self.m_SurplusCardList[star][i])
    end
  end
  return selectCards, useStar
end

local CombNode = {}
CombNode.__index = CombNode

function CombNode:New(reached, count)
  local instance = {
    reached = reached or false,
    count = count or {}
  }
  for i = 1, 5 do
    instance.count[i] = instance.count[i] or 0
  end
  setmetatable(instance, self)
  return instance
end

function CombNode:Compare(other)
  if self.reached ~= other.reached then
    return not self.reached
  end
  if not self.reached and not other.reached then
    return false
  end
  for i = 1, 5 do
    if self.count[i] ~= other.count[i] then
      return self.count[i] < other.count[i]
    end
  end
  return false
end

function AlbumActivityModel:FindBestCombination(needStar)
  local maxNeedStar = needStar + 4
  local maxCardStar = self.m_maxCardStar
  local dp = {}
  for i = 0, maxNeedStar do
    dp[i] = CombNode:New()
  end
  local cards_type_count = {}
  for i = 1, maxCardStar do
    cards_type_count[i] = #self.m_SurplusCardList[i]
  end
  dp[0].reached = true
  local cards = {}
  for i = 1, maxCardStar do
    local count = cards_type_count[i]
    local offset = 1
    while 0 < count do
      if count >= offset then
        table.insert(cards, {
          value = i * offset,
          type = i
        })
        count = count - offset
      else
        table.insert(cards, {
          value = i * count,
          type = i
        })
        count = 0
      end
      offset = offset * 2
    end
  end
  for _, card in ipairs(cards) do
    for j = maxNeedStar, card.value, -1 do
      local temp = Table.DeepCopy(dp[j - card.value])
      temp.count[card.type] = temp.count[card.type] + card.value // card.type
      if dp[j]:Compare(temp) then
        dp[j] = temp
      end
    end
  end
  local ans = needStar
  while maxNeedStar >= ans and not dp[ans].reached do
    ans = ans + 1
  end
  if maxNeedStar < ans then
    return {}, 0
  end
  return dp[ans].count, ans
end

function AlbumActivityModel:BuyRecyleCard(index, selectCards, star)
  local bWillRemove = {}
  for _, cardId in ipairs(selectCards) do
    bWillRemove[cardId] = true
  end
  for star = 1, self.m_maxCardStar do
    local curCardList = self.m_SurplusCardList[star]
    for i = 1, #curCardList do
      local cardId = curCardList[i]
      if bWillRemove[cardId] then
        bWillRemove[cardId] = nil
      end
    end
  end
  if next(bWillRemove) ~= nil then
    return false
  end
  for _, cardId in ipairs(selectCards) do
    self.m_alReadyrecyleCards[cardId] = self.m_alReadyrecyleCards[cardId] or 0
    self.m_alReadyrecyleCards[cardId] = self.m_alReadyrecyleCards[cardId] + 1
  end
  GM.BIManager:LogUseItem(AlbumStarBIName, star, EBIType.AlbumExchangeStar, index)
  self:UpdateSurplusCardData()
  self.m_recyleCardData[tostring(index)] = GM.GameModel:GetServerTime() + self.m_recyleConfigs[index].cdTime
  local rewards = self.m_recyleConfigs[index].reward
  RewardApi.CryptRewards(rewards, true)
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.AlbumStarRewards, EGameMode.Board, CacheItemType.Stack)
  self:SaveCycleCardData()
  self:SetStarShopRedTipsLevel(self:GetCanExchangeMaxRecycleIndex())
  return true
end

function AlbumActivityModel:OnSkipCycleCard(index)
  if self.m_recyleCardData[tostring(index)] == nil or self.m_recyleCardData[tostring(index)] < GM.GameModel:GetServerTime() then
    return
  end
  local gem = self:GetSkipCycleGem(index)
  if GM.PropertyDataManager:Consume(EPropertyType.Gem, gem, EBIType.AlbumPurchaseCD, index) then
    self.m_recyleCardData[tostring(index)] = 0
    self:SaveCycleCardData()
    self:GetEvent():Call(AlbumActivityModel.EventKey.CycleRefresh)
    return true
  end
  return gem - GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
end

function AlbumActivityModel:UpdatePerSecond()
  BaseActivityModel.UpdatePerSecond(self)
  for index, _ in ipairs(self.m_recyleConfigs or {}) do
    if self.m_recyleCardData[tostring(index)] ~= nil and self.m_recyleCardData[tostring(index)] < GM.GameModel:GetServerTime() then
      self.m_recyleCardData[tostring(index)] = 0
      self:SaveCycleCardData()
      self:GetEvent():Call(AlbumActivityModel.EventKey.CycleRefresh)
    end
  end
end

function AlbumActivityModel:GetSkipCycleGem(index)
  if self.m_recyleCardData[tostring(index)] == nil or self.m_recyleCardData[tostring(index)] < GM.GameModel:GetServerTime() then
    return 0
  end
  local time = self.m_recyleConfigs[index].cdTime
  local leftTime = self.m_recyleCardData[tostring(index)] - GM.GameModel:GetServerTime()
  return math.ceil(self.m_recyleConfigs[index].skipPrice[PROPERTY_COUNT] * leftTime / time)
end

function AlbumActivityModel:SaveCycleCardData()
  self.m_dbTable:Set(RecyleCardKey, "value", json.encode(self.m_recyleCardData))
  self.m_dbTable:Set(AlreadyCycleCardKey, "value", json.encode(self.m_alReadyrecyleCards))
end

function AlbumActivityModel:GetStarShopRedTipsLevel()
  return self.m_dbTable:GetValue(StarRedTipsGear, "value") or 0
end

function AlbumActivityModel:SetStarShopRedTipsLevel(level)
  self.m_dbTable:Set(StarRedTipsGear, "value", level)
  self:GetEvent():Call(AlbumActivityModel.EventKey.CycleRefresh)
end

function AlbumActivityModel:AddJokerCard(property, scene)
  local arrCards = self:GetJokerCards()
  local time = GM.GameModel:GetServerTime()
  for i = 1, property[PROPERTY_COUNT] do
    arrCards[#arrCards + 1] = {
      time = time,
      type = property[PROPERTY_TYPE]
    }
  end
  table.sort(arrCards, function(a, b)
    return a.time < b.time
  end)
  self.m_dbTable:Set(JokerCardKey, DBColumnValue, json.encode(arrCards))
  self:GetEvent():Call(AlbumActivityModel.EventKey.JokerCountRefresh)
  self.m_acquiredJokerCard = property[PROPERTY_TYPE]
  EventDispatcher.DispatchEvent(EEventType.AlbumPopStateChanged)
end

function AlbumActivityModel:GetJokerCards()
  local strData = self.m_dbTable:GetValue(JokerCardKey, DBColumnValue)
  if StringUtil.IsNilOrEmpty(strData) then
    return {}
  end
  return json.decode(strData)
end

function AlbumActivityModel:JokerExchange(cardId)
  local isGoldCard = self:IsGoldCard(cardId)
  local isGoldJoker = self:IsGoldenJokerCardInUse()
  if isGoldCard and not isGoldJoker then
    Log.Assert(false, "金卡不能兑换小丑卡")
    GM.UIManager:ShowPrompt("exchange error try again")
    return false
  end
  local arrJokerCards = self:GetJokerCards()
  if Table.IsEmpty(arrJokerCards) then
    Log.Assert(false, "没有小丑卡")
    return
  end
  if AlbumActivityModel.UseNewJokerCard then
    table.remove(arrJokerCards, #arrJokerCards)
  else
    table.remove(arrJokerCards, 1)
  end
  self.m_dbTable:Set(JokerCardKey, DBColumnValue, json.encode(arrJokerCards))
  self:AcquireCard(cardId, EBIType.AlbumAcquireJokerCard)
  self:SetNewCardId(cardId)
  EventDispatcher.DispatchEvent(EEventType.ActiveQuestProgressGain, {type = "card", num = 1})
  self:GetEvent():Call(AlbumActivityModel.EventKey.GetNewCard)
  local jokerId = EPropertyType.JokerCommonCard
  if isGoldJoker then
    jokerId = EPropertyType.JokerGoldenCard
  end
  self:ClearNewJokerState()
  GM.BIManager:LogUseItem(jokerId, 1, EBIType.AlbumUseJoker, self:GetType())
  self:GetEvent():Call(AlbumActivityModel.EventKey.JokerCountRefresh)
end

function AlbumActivityModel:GetMaxJokerCardTime()
  local arrJokerCards = self:GetJokerCards()
  if #arrJokerCards == 0 then
    return 0
  end
  local time = arrJokerCards[#arrJokerCards].time
  return time
end

function AlbumActivityModel:GetMinJokerCardTime()
  local arrJokerCards = self:GetJokerCards()
  if #arrJokerCards == 0 then
    return 0
  end
  local time = arrJokerCards[1].time
  return time
end

function AlbumActivityModel:GetJokerCount()
  if self:GetState() ~= ActivityState.Started then
    return 0
  end
  local arrJokerCards = self:GetJokerCards()
  return #arrJokerCards
end

function AlbumActivityModel:HasExpiredJokerCard()
  local arrJokerCards = self:GetJokerCards()
  if #arrJokerCards == 0 then
    return false
  end
  local time = arrJokerCards[1].time
  if time + Sec2Day < GM.GameModel:GetServerTime() then
    return true
  end
  return false
end

function AlbumActivityModel:CanPopNewJokerWindow()
  return self.m_acquiredJokerCard ~= nil and self:GetJokerCount() > 0
end

function AlbumActivityModel:ClearNewJokerState()
  self.m_acquiredJokerCard = nil
end

function AlbumActivityModel:GetNewJokerWindowName()
  if self.m_acquiredJokerCard == EPropertyType.JokerCommonCard then
    return UIPrefabConfigName.AlbumGetNewJokerWindow
  end
end

function AlbumActivityModel:SetNewCardId(cardId)
  self.m_newCardId = cardId
end

function AlbumActivityModel:GetNewCardId()
  return self.m_newCardId
end

function AlbumActivityModel:CanShowRecyleRedPoint()
  local index = self:GetCanExchangeMaxRecycleIndex()
  return index ~= 0 and index > self:GetStarShopRedTipsLevel()
end

function AlbumActivityModel:GetCanExchangeMaxRecycleIndex()
  if self.m_recyleConfigs == nil then
    return 0
  end
  local star = self:GetSurplusCardStar()
  local config
  for i = #self.m_recyleConfigs, 1, -1 do
    config = self.m_recyleConfigs[i]
    if (self.m_recyleCardData[tostring(i)] == nil or self.m_recyleCardData[tostring(i)] < GM.GameModel:GetServerTime()) and star >= config.star then
      return i
    end
  end
  return 0
end

function AlbumActivityModel:CanExchangeStarReward()
  local star = self:GetSurplusCardStar()
  for i, v in ipairs(self.m_recyleConfigs) do
    if star >= v.star then
      return true
    end
  end
  return false
end

function AlbumActivityModel:GetCycleCardState(index)
  local cfg = self.m_recyleConfigs[index]
  local time = self.m_recyleCardData[tostring(index)]
  if time ~= nil and time >= GM.GameModel:GetServerTime() then
    return 2
  end
  local star = self:GetSurplusCardStar()
  if star < cfg.star then
    return 1
  end
  return 0
end

function AlbumActivityModel:GetMainWindowName()
  return self.m_activityDefinition.MainWindowPrefabName
end

function AlbumActivityModel:GetCardName(cardId)
  return cardId .. "_name"
end

function AlbumActivityModel:ClearAllCard()
  self.m_dbTable:Drop()
  self:UpdateContent()
end

function AlbumActivityModel:TestClearCacheProperty()
  if not Table.IsEmpty(self.m_cacheProperty) then
    self.m_cacheProperty = {}
    self:SaveCacheProperty()
  end
end

function AlbumActivityModel:TestCompleteAlbum()
  if not GameConfig.IsTestMode() then
    return
  end
  for cardId, cardInfo in pairs(self.m_mapCards) do
    if self:GetCardCount(cardInfo.cardId, true) < 1 then
      self.m_dbTable:Set(cardId, DBColumnValue, 1)
    end
  end
  self:UpdateContent()
  GM.UIManager:ShowPrompt("卡册已完成！")
end

function AlbumActivityModel:TestCompleteNormalCard()
  if not GameConfig.IsTestMode() then
    return
  end
  for cardId, cardInfo in pairs(self.m_mapCards) do
    if self:GetCardCount(cardInfo.cardId, true) < 1 and not self:IsGoldCard(cardId) then
      self.m_dbTable:Set(cardId, DBColumnValue, 1)
    end
  end
  self:UpdateContent()
  GM.UIManager:ShowPrompt("普通卡已集齐")
end
