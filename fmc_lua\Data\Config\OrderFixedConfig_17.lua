return {
  {
    Id = "170010",
    GroupId = 1,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "170020",
    GroupId = 1,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e1semi_36",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170030",
    GroupId = 1,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_19",
      Count = 1
    }
  },
  {
    Id = "170040",
    GroupId = 1,
    ChapterId = 17,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_17e1roll_2",
      Count = 1
    }
  },
  {
    Id = "170050",
    GroupId = 1,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170060",
    GroupId = 1,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "170070",
    GroupId = 1,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "170080",
    GroupId = 2,
    ChapterId = 17,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5maca_4",
      Count = 1
    }
  },
  {
    Id = "170090",
    GroupId = 2,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e6nibble_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170100",
    GroupId = 2,
    ChapterId = 17,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "170110",
    GroupId = 2,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6rice_18",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170120",
    GroupId = 2,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170130",
    GroupId = 2,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    }
  },
  {
    Id = "170140",
    GroupId = 2,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5mt_12",
      Count = 1
    }
  },
  {
    Id = "170150",
    GroupId = 3,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6semi_33",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170160",
    GroupId = 3,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_15e1fd_40",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170170",
    GroupId = 3,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "170180",
    GroupId = 3,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6pho_1",
      Count = 1
    }
  },
  {
    Id = "170190",
    GroupId = 3,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e5fd_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170200",
    GroupId = 3,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4assort_3",
      Count = 1
    }
  },
  {
    Id = "170210",
    GroupId = 3,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "170220",
    GroupId = 4,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_2",
      Count = 1
    }
  },
  {
    Id = "170230",
    GroupId = 4,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e3semi_20",
      Count = 1
    }
  },
  {
    Id = "170240",
    GroupId = 4,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6semi_35",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170250",
    GroupId = 4,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_6e2flb_5",
      Count = 1
    }
  },
  {
    Id = "170260",
    GroupId = 4,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e1roll_5",
      Count = 1
    }
  },
  {
    Id = "170270",
    GroupId = 4,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170280",
    GroupId = 4,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_12",
      Count = 1
    }
  },
  {
    Id = "170290",
    GroupId = 5,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "170300",
    GroupId = 5,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    }
  },
  {
    Id = "170310",
    GroupId = 5,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4assort_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170320",
    GroupId = 5,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170330",
    GroupId = 5,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6pho_10",
      Count = 1
    }
  },
  {
    Id = "170340",
    GroupId = 5,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_mixdrk_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e2appe_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170350",
    GroupId = 5,
    ChapterId = 17,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "170360",
    GroupId = 6,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6nibble_6",
      Count = 1
    }
  },
  {
    Id = "170370",
    GroupId = 6,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5maca_2",
      Count = 1
    }
  },
  {
    Id = "170380",
    GroupId = 6,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1hotdrk_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170390",
    GroupId = 6,
    ChapterId = 17,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170400",
    GroupId = 6,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_16e1sala_20",
      Count = 1
    }
  },
  {
    Id = "170410",
    GroupId = 6,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170420",
    GroupId = 6,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "170430",
    GroupId = 7,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "170440",
    GroupId = 7,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_15e5maca_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170450",
    GroupId = 7,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170460",
    GroupId = 7,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "170470",
    GroupId = 7,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_16e1cockt_34",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_12",
      Count = 1
    }
  },
  {
    Id = "170480",
    GroupId = 7,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6rice_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170490",
    GroupId = 7,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_1_4", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "170500",
    GroupId = 8,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170510",
    GroupId = 8,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    }
  },
  {
    Id = "170520",
    GroupId = 8,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170530",
    GroupId = 8,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "170540",
    GroupId = 8,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170550",
    GroupId = 8,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6porr_2",
      Count = 1
    }
  },
  {
    Id = "170560",
    GroupId = 8,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "170570",
    GroupId = 9,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_2",
      Count = 1
    }
  },
  {
    Id = "170580",
    GroupId = 9,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170590",
    GroupId = 9,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1roll_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170600",
    GroupId = 9,
    ChapterId = 17,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "170610",
    GroupId = 9,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_14e1nutt_7",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "170620",
    GroupId = 9,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e6rice_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6rice_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170630",
    GroupId = 9,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6rice_16",
      Count = 1
    }
  },
  {
    Id = "170640",
    GroupId = 10,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "170650",
    GroupId = 10,
    ChapterId = 17,
    Requirement_1 = {Type = "ds_7e5mt_3", Count = 1}
  },
  {
    Id = "170660",
    GroupId = 10,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1hotdrk_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e1rice_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170670",
    GroupId = 10,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_sal_1", Count = 1}
  },
  {
    Id = "170680",
    GroupId = 10,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170690",
    GroupId = 10,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e6stewmt_1",
      Count = 1
    }
  },
  {
    Id = "170700",
    GroupId = 10,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170710",
    GroupId = 11,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_11e1tato_6",
      Count = 1
    }
  },
  {
    Id = "170720",
    GroupId = 11,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e4friedmt_26",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170730",
    GroupId = 11,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_9",
      Count = 1
    }
  },
  {
    Id = "170740",
    GroupId = 11,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e1roll_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170750",
    GroupId = 11,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    }
  },
  {
    Id = "170760",
    GroupId = 11,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e6rice_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "170770",
    GroupId = 11,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_4",
      Count = 1
    }
  },
  {
    Id = "170780",
    GroupId = 12,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170790",
    GroupId = 12,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6pho_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6nood_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170800",
    GroupId = 12,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "170810",
    GroupId = 12,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e1roll_7",
      Count = 1
    }
  },
  {
    Id = "170820",
    GroupId = 12,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170830",
    GroupId = 12,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_mixdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1nutt_3",
      Count = 1
    }
  },
  {
    Id = "170840",
    GroupId = 12,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "170850",
    GroupId = 13,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_12e6porr_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "170860",
    GroupId = 13,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4friedmt_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170870",
    GroupId = 13,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6nood_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170880",
    GroupId = 13,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "170890",
    GroupId = 13,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5fd_33",
      Count = 1
    }
  },
  {
    Id = "170900",
    GroupId = 13,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1rice_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6nood_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170910",
    GroupId = 13,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "170920",
    GroupId = 14,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_11e4tato_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "170930",
    GroupId = 14,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6nibble_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170940",
    GroupId = 14,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_15e5dst_29",
      Count = 1
    }
  },
  {
    Id = "170950",
    GroupId = 14,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6saus_26",
      Count = 1
    }
  },
  {
    Id = "170960",
    GroupId = 14,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5mt_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "170970",
    GroupId = 14,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_17e4friedmt_26",
      Count = 1
    }
  },
  {
    Id = "170980",
    GroupId = 14,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "170990",
    GroupId = 15,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e3semi_20",
      Count = 1
    }
  },
  {
    Id = "171000",
    GroupId = 15,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1roll_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171010",
    GroupId = 15,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_14e4curry_24",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "171020",
    GroupId = 15,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_24",
      Count = 1
    }
  },
  {
    Id = "171030",
    GroupId = 15,
    ChapterId = 17,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_13",
      Count = 1
    }
  },
  {
    Id = "171040",
    GroupId = 15,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_17e6nood_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171050",
    GroupId = 15,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "171060",
    GroupId = 16,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "171070",
    GroupId = 16,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "171080",
    GroupId = 16,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6pho_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171090",
    GroupId = 16,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_22",
      Count = 1
    }
  },
  {
    Id = "171100",
    GroupId = 16,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    }
  },
  {
    Id = "171110",
    GroupId = 16,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_11e4tato_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6semi_34",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171120",
    GroupId = 16,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_14e4sf_30",
      Count = 1
    }
  },
  {
    Id = "171130",
    GroupId = 17,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6assort_4",
      Count = 1
    }
  },
  {
    Id = "171140",
    GroupId = 17,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5mt_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171150",
    GroupId = 17,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1hotdrk_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "171160",
    GroupId = 17,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_16e1cockt_30",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "171170",
    GroupId = 17,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1roll_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171180",
    GroupId = 17,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "171190",
    GroupId = 17,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "171200",
    GroupId = 18,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    }
  },
  {
    Id = "171210",
    GroupId = 18,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "171220",
    GroupId = 18,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6nood_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171230",
    GroupId = 18,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_13e6rice_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "171240",
    GroupId = 18,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e4friedmt_26",
      Count = 1
    }
  },
  {
    Id = "171250",
    GroupId = 18,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171260",
    GroupId = 18,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_15e5fd_37",
      Count = 1
    }
  },
  {
    Id = "171270",
    GroupId = 19,
    ChapterId = 17,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "171280",
    GroupId = 19,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e5semi_16",
      Count = 1
    }
  },
  {
    Id = "171290",
    GroupId = 19,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_17e6pho_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171300",
    GroupId = 19,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6sf_22",
      Count = 1
    }
  },
  {
    Id = "171310",
    GroupId = 19,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1hotdrk_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e3semi_20",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171320",
    GroupId = 19,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e2sf_34",
      Count = 1
    }
  },
  {
    Id = "171330",
    GroupId = 19,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "171340",
    GroupId = 20,
    ChapterId = 17,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "171350",
    GroupId = 20,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1soup_18",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171360",
    GroupId = 20,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "171370",
    GroupId = 20,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_16",
      Count = 1
    }
  },
  {
    Id = "171380",
    GroupId = 20,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_17e6pho_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171390",
    GroupId = 20,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1icytre_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "171400",
    GroupId = 20,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e6pho_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "171410",
    GroupId = 21,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_16e1cockt_34",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_7",
      Count = 1
    }
  },
  {
    Id = "171420",
    GroupId = 21,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171430",
    GroupId = 21,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    }
  },
  {
    Id = "171440",
    GroupId = 21,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "171450",
    GroupId = 21,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1nutt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171460",
    GroupId = 21,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_4",
      Count = 1
    }
  },
  {
    Id = "171470",
    GroupId = 21,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_5",
      Count = 1
    }
  },
  {
    Id = "171480",
    GroupId = 22,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e6rice_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e1roll_4",
      Count = 1
    }
  },
  {
    Id = "171490",
    GroupId = 22,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171500",
    GroupId = 22,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "171510",
    GroupId = 22,
    ChapterId = 17,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "171520",
    GroupId = 22,
    ChapterId = 17,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_9e6assort_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171530",
    GroupId = 22,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e2sf_27",
      Count = 1
    }
  },
  {
    Id = "171540",
    GroupId = 22,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "171550",
    GroupId = 23,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "171560",
    GroupId = 23,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171570",
    GroupId = 23,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1hotdrk_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "171580",
    GroupId = 23,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_16e6taji_12",
      Count = 1
    }
  },
  {
    Id = "171590",
    GroupId = 23,
    ChapterId = 17,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_17e4friedmt_27",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171600",
    GroupId = 23,
    ChapterId = 17,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "171610",
    GroupId = 23,
    ChapterId = 17,
    Requirement_1 = {Type = "it_2_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6porr_2",
      Count = 1
    }
  },
  {
    Id = "171620",
    GroupId = 24,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_11e3icytre_7",
      Count = 1
    }
  },
  {
    Id = "171630",
    GroupId = 24,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171640",
    GroupId = 24,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_6",
      Count = 1
    }
  },
  {
    Id = "171650",
    GroupId = 24,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_17",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "171660",
    GroupId = 24,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1roll_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171670",
    GroupId = 24,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "171680",
    GroupId = 24,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_5",
      Count = 1
    }
  },
  {
    Id = "171690",
    GroupId = 25,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_8e6nibble_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_8",
      Count = 1
    }
  },
  {
    Id = "171700",
    GroupId = 25,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6semi_31",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171710",
    GroupId = 25,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "171720",
    GroupId = 25,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6stewmt_6",
      Count = 1
    }
  },
  {
    Id = "171730",
    GroupId = 25,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e5semi_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171740",
    GroupId = 25,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1hotdrk_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "171750",
    GroupId = 25,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_12e6porr_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "171760",
    GroupId = 26,
    ChapterId = 17,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "171770",
    GroupId = 26,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5fd_32",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171780",
    GroupId = 26,
    ChapterId = 17,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "171790",
    GroupId = 26,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_25",
      Count = 1
    }
  },
  {
    Id = "171800",
    GroupId = 26,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171810",
    GroupId = 26,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1hotdrk_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "171820",
    GroupId = 26,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5dst_29",
      Count = 1
    }
  },
  {
    Id = "171830",
    GroupId = 27,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e6rice_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "171840",
    GroupId = 27,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171850",
    GroupId = 27,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "171860",
    GroupId = 27,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_9",
      Count = 1
    }
  },
  {
    Id = "171870",
    GroupId = 27,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6nood_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171880",
    GroupId = 27,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_16e1cockt_32",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    }
  },
  {
    Id = "171890",
    GroupId = 27,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e4dst_32",
      Count = 1
    }
  },
  {
    Id = "171900",
    GroupId = 28,
    ChapterId = 17,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "171910",
    GroupId = 28,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171920",
    GroupId = 28,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_mixdrk_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6nood_6",
      Count = 1
    }
  },
  {
    Id = "171930",
    GroupId = 28,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e1roll_2",
      Count = 1
    }
  },
  {
    Id = "171940",
    GroupId = 28,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e4friedmt_27",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e1roll_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171950",
    GroupId = 28,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e4friedmt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4sf_30",
      Count = 1
    }
  },
  {
    Id = "171960",
    GroupId = 28,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "171970",
    GroupId = 29,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1icytre_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "171980",
    GroupId = 29,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6semi_30",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "171990",
    GroupId = 29,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    }
  },
  {
    Id = "172000",
    GroupId = 29,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "172010",
    GroupId = 29,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_16e1cockt_32",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "172020",
    GroupId = 29,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e6pho_5",
      Count = 1
    }
  },
  {
    Id = "172030",
    GroupId = 29,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_15e1dst_30",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_7",
      Count = 1
    }
  },
  {
    Id = "172040",
    GroupId = 30,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6dst_7",
      Count = 1
    }
  },
  {
    Id = "172050",
    GroupId = 30,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_8", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "172060",
    GroupId = 30,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "172070",
    GroupId = 30,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_19", Count = 1}
  },
  {
    Id = "172080",
    GroupId = 30,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1soup_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e1roll_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "172090",
    GroupId = 30,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_17e1roll_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6sf_22",
      Count = 1
    }
  },
  {
    Id = "172100",
    GroupId = 30,
    ChapterId = 17,
    Requirement_1 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_17e2sf_34",
      Count = 1
    }
  }
}
