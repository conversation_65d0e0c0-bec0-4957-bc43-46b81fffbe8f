CrossPromotionTaskCell = {}
CrossPromotionTaskCell.__index = CrossPromotionTaskCell

function CrossPromotionTaskCell:Init(tbTaskWindow, tbTaskEntity)
  self.m_tbTaskWindow = tbTaskWindow
  self:UpdateContent(tbTaskEntity)
end

function CrossPromotionTaskCell:UpdateContent(tbTaskEntity)
  Log.Assert(tbTaskEntity ~= nil, "CrossPromotionTaskCell:UpdateContent")
  self.taskEntity = tbTaskEntity
  local rewardTextCcolor = EStandardColor.MainText
  if self.taskEntity.eProgressStatus == ECPTaskStatus.Doing then
    self.m_doneFlagGo:SetActive(false)
    self.m_awardBgGo:SetActive(false)
    self.m_lightGo:SetActive(false)
    self.m_completeFlagGo:SetActive(false)
    self.m_actionBtnGo:SetActive(false)
    self.m_helpButtonGo:SetActive(true)
    self.m_iconBgImg.enabled = true
    self.m_iconAwardBgGo:SetActive(false)
  elseif self.taskEntity.eProgressStatus == ECPTaskStatus.Done then
    self.m_doneFlagGo:SetActive(false)
    self.m_awardBgGo:SetActive(true)
    self.m_lightGo:SetActive(true)
    self.m_completeFlagGo:SetActive(true)
    self.m_actionBtnGo:SetActive(true)
    self.m_helpButtonGo:SetActive(false)
    self.m_iconBgImg.enabled = false
    self.m_iconAwardBgGo:SetActive(true)
    rewardTextCcolor = CSColor(0.7333333333333333, 0.32941176470588235, 0 / 255, 1)
    GM.BIManager:LogAction(EBIType.CrossPromotionTaskFinish, self.taskEntity.id)
  else
    self.m_doneFlagGo:SetActive(true)
    self.m_awardBgGo:SetActive(false)
    self.m_actionBtnGo:SetActive(false)
    self.m_helpButtonGo:SetActive(false)
    self.m_lightGo:SetActive(false)
    self.m_completeFlagGo:SetActive(false)
    self.m_iconBgImg.enabled = true
    self.m_iconAwardBgGo:SetActive(false)
  end
  local strName = GM.GameTextModel:GetText(tbTaskEntity.desc)
  strName = strName .. "(" .. tbTaskEntity:GetProgress() .. ")"
  self.m_taskNameText.text = strName
  self.m_taskNameText.color = rewardTextCcolor
  SpriteUtil.SetImage(self.m_taskIcon, self.taskEntity.icon, true)
  self.m_rewardDescText.color = rewardTextCcolor
  for i = 1, 2 do
    self["m_rewardIconImg" .. i].gameObject:SetActive(false)
    self["m_rewardNumText" .. i].gameObject:SetActive(false)
    self["m_rewardNumText" .. i].color = rewardTextCcolor
  end
  local rewards = self.taskEntity.rewards
  if not Table.IsEmpty(rewards) then
    for i, reward in ipairs(rewards) do
      local rewardType = reward[PROPERTY_TYPE]
      local iconFileKey
      if GM.PropertyDataManager:IsPropertyType(rewardType) then
        iconFileKey = GM.PropertyDataManager:GetPropertySpriteKey(reward[PROPERTY_TYPE])
      else
        iconFileKey = rewardType
      end
      if iconFileKey then
        SpriteUtil.SetImage(self["m_rewardIconImg" .. i], iconFileKey, true)
      end
      self["m_rewardNumText" .. i].text = reward[PROPERTY_COUNT]
      self["m_rewardIconImg" .. i].gameObject:SetActive(true)
      self["m_rewardNumText" .. i].gameObject:SetActive(true)
    end
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_descriptionsRectTrans)
  self:UpdateBoardOpacity()
end

function CrossPromotionTaskCell:UpdateBoardOpacity()
  local targetAlpha = self.taskEntity.eProgressStatus == ECPTaskStatus.Awarded and 0.5 or 1
  local imageColor = CSColor(1, 1, 1, targetAlpha)
  self.m_bgCanvasGroup.alpha = targetAlpha
  self.m_iconBgImg.color = imageColor
  self.m_taskIcon.color = imageColor
  local color = self.m_rewardDescText.color
  self.m_rewardDescText.color = CSColor(self.m_rewardDescText.color.r, self.m_rewardDescText.color.g, self.m_rewardDescText.color.b, targetAlpha)
  self.m_taskNameText.color = CSColor(self.m_taskNameText.color.r, self.m_taskNameText.color.g, self.m_taskNameText.color.b, targetAlpha)
  for i = 1, 2 do
    self["m_rewardIconImg" .. i].color = imageColor
    self["m_rewardNumText" .. i].color = CSColor(self["m_rewardNumText" .. i].color.r, self["m_rewardNumText" .. i].color.g, self["m_rewardNumText" .. i].color.b, targetAlpha)
  end
end

function CrossPromotionTaskCell:OnActionBtnPressed()
  if self.taskEntity == nil then
    GM.BIManager:LogErrorInfo(EBIType.TaskEntityError, "CrossPromotionTaskCell")
    if self.m_tbTaskWindow then
      self.m_tbTaskWindow:Close()
    end
    return
  end
  if self.taskEntity.eProgressStatus == ECPTaskStatus.Done then
    GM.RewardModel:ReceiveReward(self.taskEntity:GetTaskRewardKey(), function(eRewardRespStatus)
      if eRewardRespStatus == ERewardRespStatus.Success then
        self.taskEntity.eProgressStatus = ECPTaskStatus.Awarded
        RewardApi.AcquireRewards(self.taskEntity:GetRewards(), EPropertySource.Give, EBIType.CrossPromotionTaskReward, nil, EGameMode.Board, CacheItemType.Stack)
        if self.m_tbTaskWindow then
          self.m_tbTaskWindow:SetAcquireReward()
        end
        if not self.gameObject:IsNull() and not self.m_actionBtnGo:IsNull() then
          self.m_actionBtnGo:SetActive(false)
          self:UpdateContent(self.taskEntity)
        end
      end
    end)
  elseif self.taskEntity.eProgressStatus == ECPTaskStatus.Doing then
    self.m_tbTaskWindow:Close()
    GM.CrossPromotionModel:Go2NewGame()
  end
end
