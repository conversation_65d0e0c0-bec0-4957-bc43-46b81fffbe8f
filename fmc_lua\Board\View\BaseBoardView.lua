BaseBoardView = {
  Tile1Sprite = ImageFileConfigName.game_bg2,
  Tile2Sprite = ImageFileConfigName.game_bg3
}
BaseBoardView.__index = BaseBoardView

function REGISTER_BOARD_EVENT_HANDLER(view, eventName)
  local functionName = "_On" .. eventName
  AddHandlerAndRecordMap(view.m_model.event, BoardEventType[eventName], {
    obj = view,
    method = view[functionName]
  })
end

function BaseBoardView:Init(boardModel)
  self.m_model = boardModel
  self.m_tileMap = {}
  self.m_modelViewMap = {}
  self:_InitTileItemView()
  REGISTER_BOARD_EVENT_HANDLER(self, "SpreadItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "TransformItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "HideItems")
  REGISTER_BOARD_EVENT_HANDLER(self, "ShowItems")
  REGISTER_BOARD_EVENT_HANDLER(self, "BatchRemoveItems")
  for itemModel, itemView in pairs(self.m_modelViewMap) do
    local lockerCmp = itemModel:GetComponent(ItemLocker)
    if lockerCmp ~= nil and lockerCmp.startPosition == itemModel:GetPosition() then
      self.m_itemViewFactory:CreateItemLockerCmp(self, itemModel, itemView)
    end
  end
end

function BaseBoardView:_InitTileItemView()
  local boardModel = self.m_model
  local position
  for y = 1, boardModel:GetVerticalTiles() do
    for x = 1, boardModel:GetHorizontalTiles() do
      position = boardModel:CreatePosition(x, y)
      self:_AddTile(position)
      local itemModel = boardModel:GetItem(position)
      if itemModel ~= nil then
        self:_AddItemView(itemModel)
      end
    end
  end
  self:_UpdateTile()
end

function BaseBoardView:OnDestroy()
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
end

function BaseBoardView:GetModel()
  return self.m_model
end

function BaseBoardView:GetItemsTransform()
  return self.m_itemsTransform
end

function BaseBoardView:CreateItemView(parent, itemModel, inBoard, onSpineLoaded)
  return self.m_itemViewFactory:Create(parent, itemModel, inBoard, onSpineLoaded)
end

function BaseBoardView:_GetBoardPosition(worldPosition)
  local localPosition = self.transform:InverseTransformPoint(worldPosition)
  return self.m_model:CreatePositionFromLocalPosition(localPosition.x, localPosition.y)
end

function BaseBoardView:ConvertWorldPositionToScreenPosition(position)
  Log.Assert(false, "ConvertWorldPositionToScreenPosition()是抽象接口")
end

function BaseBoardView:ConvertBoardPositionToScreenPosition(boardPosition)
  Log.Assert(false, "ConvertBoardPositionToScreenPosition()是抽象接口")
end

function BaseBoardView:ConvertScreenPositionToWorldPosition(position)
  Log.Assert(false, "ConvertScreenPositionToWorldPosition()是抽象接口")
end

function BaseBoardView:GetItemView(itemModel)
  return self.m_modelViewMap[itemModel]
end

function BaseBoardView:_AddItemView(itemModel)
  local position = itemModel:GetPosition()
  local currentItem = self.m_model:GetItem(position)
  if currentItem ~= nil and currentItem ~= itemModel then
    local info = position:GetX() .. "_" .. position:GetY() .. ", cur logic item id is " .. currentItem:GetId() .. ", code is " .. currentItem:GetCode() .. ", add item id is " .. itemModel:GetId() .. ", code is " .. itemModel:GetCode() .. "\n" .. debug.traceback()
    GM.BIManager:LogErrorInfo(EBIProjectType.ElementOverlapLogic, info)
    return
  end
  local itemView = self:CreateItemView(self.m_itemsTransform, itemModel, true)
  self.m_modelViewMap[itemModel] = itemView
  return itemView
end

function BaseBoardView:_RemoveItemView(itemView)
  if itemView == nil then
    return
  end
  local itemModel = itemView:GetModel()
  local currentView = self.m_modelViewMap[itemModel]
  if itemModel ~= nil and itemModel:GetPosition() ~= nil and currentView == nil then
    local position = itemModel:GetPosition()
    local info = position:GetX() .. "_" .. position:GetY() .. ", item id is " .. itemModel:GetId() .. ", code is " .. itemModel:GetCode()
    GM.BIManager:LogErrorInfo(EBIProjectType.ElementNoViewWhenRemove, info)
  end
  itemView.gameObject:RemoveSelf()
  self.m_modelViewMap[itemModel] = nil
end

function BaseBoardView:_AddTile(boardPosition)
  local tileObject = Object.Instantiate(self.m_tilePrefab, self.m_tilesTransform)
  local tileSprite = tileObject:GetComponent(typeof(SpriteRenderer))
  self.m_tileMap[boardPosition] = tileSprite
  local localPosition = boardPosition:ToLocalPosition()
  tileObject.transform.localPosition = Vector3(localPosition.x + BaseBoardModel.TileSize / 2, localPosition.y + BaseBoardModel.TileSize / 2, 0)
end

function BaseBoardView:_UpdateTile(tile1Sprite, tile2Sprite)
  local tile1 = tile1Sprite and tile1Sprite or self.Tile1Sprite
  local tile2 = tile2Sprite and tile2Sprite or self.Tile2Sprite
  for position, tileSprite in pairs(self.m_tileMap) do
    local isEven = (position:GetX() + position:GetY()) % 2 == 0
    SpriteUtil.SetSpriteRenderer(tileSprite, isEven and tile2 or tile1)
  end
end

function BaseBoardView:_CanShowMergeLight(itemModel, lastItemModel)
  return false
end

function BaseBoardView:_TryShowMergeLight(worldPosition)
  local boardPosition = self:_GetBoardPosition(worldPosition)
  local itemModel = self.m_model:GetItem(boardPosition)
  local itemView = self:GetItemView(itemModel)
  if itemView == self.m_lastMergeLightItem then
    return
  end
  self:_ClearMergeLight()
  local lastItemModel = self.m_lastTouchedItem:GetModel()
  if itemModel == nil or itemModel == lastItemModel then
    return
  end
  local forceTargetPos = GM.TutorialModel:GetForceTargetBoardPosition()
  if forceTargetPos ~= nil and forceTargetPos ~= boardPosition then
    return
  end
  if self:_CanShowMergeLight(itemModel, lastItemModel) then
    self:_ShowMergeLight(itemView)
  end
end

function BaseBoardView:_ShowMergeLight(itemView)
  itemView:MergeLightAppear()
  self.m_lastMergeLightItem = itemView
end

function BaseBoardView:_ClearMergeLight()
  if self.m_lastMergeLightItem ~= nil then
    if not self.m_lastMergeLightItem.gameObject:IsNull() then
      self.m_lastMergeLightItem:MergeLightDisappear()
    end
    self.m_lastMergeLightItem = nil
  end
end

function BaseBoardView:_PlayJumpAnimation(itemView, sourcePosition, targetPosition, callback, boostLevelSpan, boostEnergySpared)
  local sourcePositionZero = Vector3(sourcePosition.x, sourcePosition.y, -1)
  local targetPositionZero = Vector3(targetPosition.x, targetPosition.y, 0)
  local deltaPosition = targetPositionZero - sourcePositionZero
  local transform = itemView.gameObject.transform
  transform.localPosition = sourcePositionZero
  transform.localScale = Vector3(0.5, 0.5, 1)
  itemView:SetFlying(true)
  local sequence = DOTween.Sequence()
  sequence:Insert(0.05, transform:DOLocalJump(sourcePositionZero + deltaPosition * 0.9, 60, 1, 0.45):SetEase(Ease.OutSine))
  sequence:Insert(0.5, transform:DOLocalMove(targetPosition, 0.3):SetEase(Ease.OutQuad))
  sequence:InsertCallback(0.6, function()
    itemView:ShowSpreadLight(boostLevelSpan)
  end)
  sequence:Insert(0.05, transform:DOScale(Vector3(1.5, 1.5, 1), 0.25):SetEase(Ease.OutSine))
  sequence:Insert(0.3, transform:DOScale(Vector3(0.8, 0.8, 1), 0.2))
  sequence:Insert(0.5, transform:DOScale(Vector3(1.15, 1.15, 1), 0.2))
  sequence:Insert(0.7, transform:DOScale(V3One, 0.1))
  itemView:SetJumpTween(sequence, function()
    itemView:SetFlying(false)
    if boostEnergySpared ~= nil and 0 < boostEnergySpared then
      local boostEnergySparedReward = {
        [PROPERTY_TYPE] = EPropertyType.Energy,
        [PROPERTY_COUNT] = boostEnergySpared
      }
      local worldPosition = itemView and itemView.transform.position or V3Zero
      local screenPosition = self:ConvertWorldPositionToScreenPosition(worldPosition)
      local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
      RewardApi.AcquireRewardsInView({boostEnergySparedReward}, {
        arrWorldPos = {uiWorldPosition},
        eventLock = false
      })
    end
    if callback then
      callback()
    end
  end)
end

function BaseBoardView:_GetSpreadAudio(message)
  if message.New:GetComponent(ItemBubble) ~= nil then
    return AudioFileConfigName.SfxMergeBubbleSpawn
  end
  return AudioFileConfigName.SfxMergeSpawnManual
end

function BaseBoardView:_OnSpreadItem(message)
  local sourceItemView = self:GetItemView(message.Source)
  local newItemView = self:_AddItemView(message.New)
  if newItemView ~= nil then
    self:_PlayJumpAnimation(newItemView, sourceItemView.transform.localPosition, newItemView.transform.localPosition)
  end
end

function BaseBoardView:_OnTransformItem(message)
  local sourceItemView = self:GetItemView(message.Source)
  sourceItemView.toBeRemoved = true
  local sequence = DOTween.Sequence()
  sequence:Insert(0, sourceItemView.transform:DOScale(0.3, 0.1))
  sequence:InsertCallback(0.1, function()
    self:_RemoveItemView(sourceItemView)
  end)
  local newItemView = self:_AddItemView(message.New)
  if newItemView ~= nil then
    newItemView.transform.localScale = Vector3.zero
    sequence:InsertCallback(0.1, function()
      newItemView.transform.localScale = 0.3 * V3One
    end)
    sequence:Insert(0.1, newItemView.transform:DOScale(1.3, 0.2))
    sequence:Insert(0.3, newItemView.transform:DOScale(1, 0.1))
  end
end

function BaseBoardView:_OnHideItems(message)
  local items = message.Items
  for _, item in ipairs(items) do
    local itemView = self:GetItemView(item)
    if itemView then
      itemView.transform.localScale = V3Zero
    end
  end
end

function BaseBoardView:_OnShowItems(message)
  local items = message.Items
  local sequence = DOTween.Sequence()
  for _, item in ipairs(items) do
    local itemView = self:GetItemView(item)
    if itemView then
      sequence:Insert(0, itemView.transform:DOScale(1.2, 0.2))
      sequence:Insert(0.2, itemView.transform:DOScale(1, 0.1))
    end
  end
end

function BaseBoardView:_OnBatchRemoveItems(message)
  local dur = message.Dur or 0.5
  for _, itemModel in ipairs(message.Removed) do
    local itemView = self:GetItemView(itemModel)
    itemView.toBeRemoved = true
    itemView.transform:DOScale(0, dur):OnComplete(function()
      self:_RemoveItemView(itemView)
    end)
    if self.m_selectedBoardPosition == itemModel:GetPosition() then
      self:_UpdateIndicator()
      self:UpdateBoardInfoBar()
    end
  end
end

function BaseBoardView:UpdateBoardInfoBar(itemModel, inBoard)
end

function BaseBoardView:_UpdateIndicator()
end

function BaseBoardView:GetPromptIgnoreItems()
  return {}
end
