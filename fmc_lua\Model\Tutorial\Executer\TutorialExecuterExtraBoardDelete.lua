local Step = {ClickItem = "1"}
local EStep2TextKey = {
  [Step.ClickItem] = "extraboard_guide_5"
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._OnCloseView)
  EventDispatcher.AddListener(EEventType.ItemSpread, self, self._TryStartTutorial)
  EventDispatcher.AddListener(EEventType.ExtraBoardViewCreated, self, self._TryStartTutorial)
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self._TryStartTutorial)
  EventDispatcher.AddListener(EEventType.ItemDeleteButtonUpdate, self, self._OnItemDeleteButtonUpdate)
  EventDispatcher.AddListener(EEventType.ItemReplaced, self, self._TryStartTutorial)
  for _, v in pairs(ExtraBoardActivityDefinition) do
    EventDispatcher.AddListener(v.StateChangedEvent, self, self._OnActivityStateChanged)
  end
end

function Executer:_OnOpenView(msg)
  local name = msg and msg.name
  for k, v in pairs(ExtraBoardActivityDefinition) do
    if name == v.MainWindowPrefabName then
      local model = GM.ActivityManager:GetModel(k)
      local window = GM.UIManager:GetOpenedViewByName(name)
      if model ~= nil and window ~= nil and window:GetDeleteButton() ~= nil then
        self.m_activityType = k
        break
      end
    end
  end
end

function Executer:_OnCloseView(msg)
  if self.m_activityType ~= nil and msg and msg.name and msg.name == ExtraBoardActivityDefinition[self.m_activityType].MainWindowPrefabName then
    self.m_activityType = nil
  end
end

function Executer:_OnActivityStateChanged()
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    GM.TutorialModel:SetForceSourceBoardPosition(nil)
    GM.TutorialModel:SetForceTargetBoardPosition(nil)
    self:Finish(self.m_gesture)
  end
end

function Executer:_TryStartTutorial()
  if self:_CanStartItemDeleteTutorial() then
    self:_ExecuteStep1()
  end
end

function Executer:_OnItemDeleteButtonUpdate()
  if self.m_strOngoingDatas == Step.ClickItem then
    local boardView = GM.ModeViewController:GetExtraBoardActivityBoardView()
    if boardView ~= nil and boardView:GetSelectedItemModel() == self.m_selectItem then
      GM.TutorialModel:SetForceSourceBoardPosition(nil)
      GM.TutorialModel:SetForceTargetBoardPosition(nil)
      self:_ExecuteStep2()
    end
  end
end

function Executer:_CanStartItemDeleteTutorial()
  local boardModel = self:_GetBoardModel()
  return self.m_activityType ~= nil and GM.ModeViewController:GetExtraBoardActivityBoardView() ~= nil and not GM.UIManager:IsEventLock() and not self.m_bExecuteStep1 and not GM.TutorialModel:HasAnyStrongTutorialOngoing() and not self.m_model:IsTutorialFinished(self:GetTutorialId()) and boardModel ~= nil and boardModel:CanShowDeleteButton() and self:_GetItemToSell() ~= nil
end

function Executer:_GetBoardModel()
  local model = self.m_activityType and GM.ActivityManager:GetModel(self.m_activityType)
  return model and model:GetBoardModel()
end

function Executer:_GetItemToSell()
  local boardItems = self:_GetBoardModel():FilterItems(function(itemModel)
    return self:_GetBoardModel():CanItemSell(itemModel) and ExtraBoardActivityModel.IsExtraBoardMainItem(itemModel:GetType())
  end)
  if Table.IsEmpty(boardItems) then
    return
  end
  local minLevelItem, level
  local minLevel = 10000
  for _, itemModel in ipairs(boardItems) do
    level = GM.ItemDataModel:GetChainLevel(itemModel:GetType())
    if minLevel > level then
      minLevelItem = itemModel
      minLevel = level
    end
  end
  return minLevelItem
end

function Executer:_ExecuteStep1()
  self.m_bExecuteStep1 = true
  self.m_strOngoingDatas = Step.ClickItem
  self:SetStrongTutorial(true)
  self.m_selectItem = self:_GetItemToSell()
  if self.m_selectItem == nil then
    Debug.LogError("[TutorialExecuterItemDelete] Error: 执行引导前需要先检查是否有满足条件的元素")
    return
  end
  GM.TutorialModel:SetTutorialFinished(self:GetTutorialId())
  self:LogTutorialStepFinish(self.m_selectItem:GetCode())
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    if self.m_bFinished then
      return
    end
    local tapPosition = self.m_selectItem:GetPosition()
    local boardView = GM.ModeViewController:GetExtraBoardActivityBoardView()
    TutorialHelper.MaskOnGivenBoard(boardView, tapPosition, tapPosition)
    self.m_gesture = TutorialHelper.TapOnGivenBoard(boardView, tapPosition)
    TutorialHelper.ShowDialogWithGivenBoardMaskArea(boardView, GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), tapPosition, tapPosition, nil, nil, 100)
    GM.TutorialModel:SetForceSourceBoardPosition(tapPosition)
    GM.TutorialModel:SetForceTargetBoardPosition(tapPosition)
  end, 0.5)
end

function Executer:_ExecuteStep2()
  self:Finish(self.m_gesture)
  local window = GM.UIManager:GetOpenedViewByName(ExtraBoardActivityDefinition[self.m_activityType].MainWindowPrefabName)
  self.m_deleteButton = window:GetDeleteButton()
  if self.m_deleteButton ~= nil then
    self.m_deleteButton:ShowTapEffect()
  end
end

function Executer:Finish(...)
  TutorialExecuter.Finish(self, ...)
  GM.TutorialModel:SetForceSourceBoardPosition(nil)
  GM.TutorialModel:SetForceTargetBoardPosition(nil)
  self.m_bFinished = true
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
