BaseInteractiveBoardModel = setmetatable({}, BaseBoardModel)
BaseInteractiveBoardModel.__index = BaseInteractiveBoardModel

function BaseInteractiveBoardModel:Init(gameMode, itemManager, itemCacheModel, width, height)
  self.m_gameMode = gameMode
  self.m_itemManager = itemManager
  self.m_itemCacheModel = itemCacheModel
  BaseBoardModel.Init(self, width, height)
  DEFINE_ITEM_EVENT(self, "Open")
  DEFINE_ITEM_EVENT(self, "SpeedUp")
  DEFINE_ITEM_EVENT(self, "Choose")
  DEFINE_ITEM_EVENT(self, "Activate")
  self.m_lastBreakBubbleType = nil
end

function BaseInteractiveBoardModel:Destroy()
  BaseBoardModel.Destroy(self)
  EventDispatcher.RemoveTarget(self)
  self.m_itemManager:Destroy()
end

function BaseInteractiveBoardModel:GetCodeCountMap(includeBoard, includeCache, includeStore)
  local codeCountMap = BaseBoardModel.GetCodeCountMap(self, includeBoard, includeCache, includeStore)
  includeCache = includeCache ~= false
  if includeCache then
    local code
    for i = 1, self:GetCachedItemCount() do
      code = self:GetCachedItem(i)
      if code ~= nil then
        if codeCountMap[code] == nil then
          codeCountMap[code] = 0
        end
        codeCountMap[code] = codeCountMap[code] + 1
      end
    end
  end
  return codeCountMap
end

function BaseInteractiveBoardModel:UpdatePerSecond()
  BaseBoardModel.UpdatePerSecond(self)
  for item, _ in pairs(self:GetAllBoardItems(true)) do
    item:DispatchComponentEvent("UpdateAccelerateTime")
  end
end

function BaseInteractiveBoardModel:_UpdateAccelerate(item1, item2)
  local checkFunc = function(item)
    if item == nil then
      return false
    end
    return item:GetComponent(ItemSpread) ~= nil and item:GetComponent(ItemSpread):CanAccelerate() or item:GetComponent(ItemTransform) ~= nil and item:GetComponent(ItemTransform):CanAccelerate() or item:GetComponent(ItemAccelerate) ~= nil and item:GetComponent(ItemAccelerate):IsActivated()
  end
  if checkFunc(item1) or checkFunc(item2) then
    self:UpdatePerSecond()
    self:UpdatePerSecond()
  end
end

function BaseInteractiveBoardModel:CreateMatrix()
  return BaseBoardModel._CreateMatrix(self)
end

function BaseInteractiveBoardModel:CreatePosition(x, y)
  return BaseBoardModel._CreatePosition(self, x, y)
end

function BaseInteractiveBoardModel:CreatePositionFromLocalPosition(localPositionX, localPositionY)
  return BaseBoardModel._CreatePositionFromLocalPosition(self, localPositionX, localPositionY)
end

function BaseInteractiveBoardModel:GetGameMode()
  return self.m_gameMode
end

function BaseInteractiveBoardModel:HasBoardItem(itemModel)
  return self.m_itemLayerModel:HasItem(itemModel)
end

function BaseInteractiveBoardModel:SaveItemProperty(item)
  self.m_itemManager:SetItem(item)
end

function BaseInteractiveBoardModel:_RemoveItemProperty(item)
  BaseBoardModel._RemoveItemProperty(self, item)
  self.m_itemManager:RemoveItem(item:GetId())
end

function BaseInteractiveBoardModel:CacheItems(cachedItemCodes, type, cost, hasAnimation)
  self.m_itemCacheModel:PushItems(cachedItemCodes, type, cost)
  for _, code in ipairs(cachedItemCodes) do
    GM.BIManager:LogAction(EBIType.CacheItem, tostring(code), self:GetGameMode())
  end
  EventDispatcher.DispatchEvent(EEventType.CacheItems, {
    hasAnimation = hasAnimation,
    eGameMode = self:GetGameMode()
  })
end

function BaseInteractiveBoardModel:PopCachedItem()
  if self:IsBoardFull() then
    return false
  end
  local position = self:FindEmptyPositionForCacheItem()
  if position == nil then
    return false
  end
  local code, cost = self.m_itemCacheModel:PopItem()
  if StringUtil.IsNilOrEmpty(code) then
    Log.Assert(false, "code不能为空")
    return false
  end
  local newItem = self:GenerateItem(position, code, cost)
  local message = {
    New = newItem,
    GameMode = self:GetGameMode()
  }
  EventDispatcher.DispatchEvent(EEventType.PopCachedItem, message)
  self.event:Call(BoardEventType.PopCachedItem, message)
  GM.BIManager:LogAction(EBIType.PopCacheItem, code, self:GetGameMode())
  self:_Try2LogBoardFull()
  return true
end

function BaseInteractiveBoardModel:FindEmptyPositionForCacheItem()
  return self:FindEmptyPositionInValidOrder()
end

function BaseInteractiveBoardModel:RemoveCachedItem(index)
  return self.m_itemCacheModel:RemoveItem(index)
end

function BaseInteractiveBoardModel:RemoveCachedItemByCode(code)
  return self.m_itemCacheModel:RemoveItemByCode(code)
end

function BaseInteractiveBoardModel:GetCachedItem(index)
  return self.m_itemCacheModel:GetItem(index)
end

function BaseInteractiveBoardModel:GetCachedItemCount()
  return self.m_itemCacheModel:GetItemCount()
end

function BaseInteractiveBoardModel:GetCachedCountByCode(code)
  return self.m_itemCacheModel:GetCountByCode(code)
end

function BaseInteractiveBoardModel:TryPinOneItemCodeToCacheTop(itemCode)
  return self.m_itemCacheModel:TryPinOneItemCodeToTop(itemCode)
end

function BaseInteractiveBoardModel:UpdateOpeningItem()
  for item, _ in pairs(self:GetAllBoardItems()) do
    local itemSpread = item:GetComponent(ItemSpread)
    if itemSpread ~= nil and itemSpread:GetState() == ItemSpreadState.Opening then
      self.m_bHasOpeningItem = true
      self.event:Call(BoardEventType.UpdateOpeningItem)
      return
    end
  end
  self.m_bHasOpeningItem = false
  self.event:Call(BoardEventType.UpdateOpeningItem)
  return
end

function BaseInteractiveBoardModel:HasOpeningItem()
  return self.m_bHasOpeningItem
end

function BaseInteractiveBoardModel:GenerateItem(position, code, cost)
  local newItem = BaseBoardModel.GenerateItem(self, position, code, cost)
  if self.m_mergeAllIgnoredItems ~= nil then
    self.m_mergeAllIgnoredItems[newItem] = true
  end
  return newItem
end

function BaseInteractiveBoardModel:SpreadItem(sourceItem, newItemPosition, newItemCode, logSpread, cost, boostLevelSpan, boostEnergySpared)
  local message = BaseBoardModel.SpreadItem(self, sourceItem, newItemPosition, newItemCode, logSpread, cost, boostLevelSpan, boostEnergySpared)
  EventDispatcher.DispatchEvent(EEventType.ItemSpread, message)
  if logSpread then
    GM.BIManager:LogSpread(sourceItem:GetCode(), newItemCode, {
      eb = boostLevelSpan and 1 or 0
    }, self:GetGameMode())
  end
  self:_Try2LogBoardFull()
end

function BaseInteractiveBoardModel:DragItem(item, targetPosition)
  if not targetPosition:IsValid() then
    item:SetPosition(item:GetPosition())
    GM.AudioModel:PlayEffect(AudioFileConfigName.sfxDragItemEnd)
    return
  end
  local sourceItem = self:GetItem(item:GetPosition())
  if sourceItem ~= item then
    item:SetPosition(item:GetPosition())
    GM.AudioModel:PlayEffect(AudioFileConfigName.sfxDragItemEnd)
    return
  end
  local targetItem = self:GetItem(targetPosition)
  if targetItem == nil or targetItem == item then
    self:_SetItem(item:GetPosition(), nil, false)
    self:_SetItem(targetPosition, item)
    if targetItem ~= item then
      self:_LogMove(item, item:GetPosition(), targetPosition)
    end
    item:SetPosition(targetPosition)
    self:_UpdateAccelerate(item, targetItem)
    GM.AudioModel:PlayEffect(AudioFileConfigName.sfxDragItemEnd)
    return
  end
  if self:CanItemAffect(item, targetItem) then
    if self:_AffectItem(item, targetItem) then
      return
    end
  elseif item:GetComponent(ItemSplit) ~= nil and self:IsBoardFull() then
    self.event:Call(BoardEventType.SpreadFailed, {
      Item = item,
      Reason = SpreadFailedReason.BoardFull
    })
  end
  if self:CanTwoItemsMerge(item, targetItem) then
    self:_MergeItem(item, targetItem, targetPosition)
    return
  end
  local targetItemCook = targetItem:GetComponent(ItemCook)
  if targetItemCook ~= nil and targetItemCook:CanAddMaterial(item) then
    self:AddMaterialToCook(item, targetItemCook)
    return
  end
  local canNotSwap = self:_CannotSwapWithEQ(targetItem, item)
  if canNotSwap or not self:CanItemMove(targetItem) then
    item:SetPosition(item:GetPosition())
    GM.AudioModel:PlayEffect(AudioFileConfigName.sfxDragItemEnd)
    PlatformInterface.Vibrate(EVibrationType.Medium)
    if canNotSwap then
      self.event:Call(BoardEventType.ShowPrompt, {
        Item = targetItem,
        Key = "cannot_move_it_to_eq"
      })
    end
    GM.BIManager:LogAction(EBIType.ItemSwapFailed, {
      from = item:GetCode(),
      to = targetItem:GetCode()
    }, self:GetGameMode())
    return
  end
  local newPosition = item:GetPosition()
  self:_SetItem(item:GetPosition(), nil, false)
  self:_SetItem(targetPosition, item, false)
  self:_SetItem(newPosition, targetItem)
  self:_LogMove(item, item:GetPosition(), targetPosition)
  self:_LogMove(targetItem, targetPosition, newPosition)
  item:SetPosition(targetPosition)
  targetItem:SetPosition(newPosition)
  self:_UpdateAccelerate(item, targetItem)
  GM.AudioModel:PlayEffect(AudioFileConfigName.sfxDragItemSwitch)
end

function BaseInteractiveBoardModel:_CannotSwapWithEQ(targetItem, srcItem)
  local targetItemCook = targetItem:GetComponent(ItemCook)
  if targetItemCook == nil then
    return false
  end
  if not StringUtil.StartWith(targetItem:GetType(), "eq_") then
    return false
  end
  local srcType = srcItem:GetType()
  if StringUtil.StartWith(srcType, "it_") or StringUtil.StartWith(srcType, "ds_") then
    return true
  end
  return false
end

function BaseInteractiveBoardModel:ShockNeighborItems(centerPosition)
  for _, direction in ipairs(BaseItemLayerModel.Directions4Way) do
    local neighborPos = centerPosition + direction
    local neightItem = self:GetItem(neighborPos)
    if neightItem ~= nil then
      neightItem:DispatchComponentEvent("OnShock")
    end
  end
end

function BaseInteractiveBoardModel:_MergeItem(item, targetItem, targetPosition)
  self:RemoveItem(item)
  local newItem = self:ReplaceItem(targetItem, item:GetMergedType(), ItemModelHelper.MergeCost(item, targetItem), false)
  local itemSpread = newItem:GetComponent(ItemSpread)
  if itemSpread then
    itemSpread:InheritFromMergeSource(item, targetItem)
  end
  local mergeMessage = {
    Source = item,
    Target = targetItem,
    New = newItem
  }
  self.event:Call(BoardEventType.MergeItem, mergeMessage)
  mergeMessage.GameMode = self:GetGameMode()
  EventDispatcher.DispatchEvent(EEventType.ItemMerged, mergeMessage)
  self:ShockNeighborItems(targetPosition)
  local bubbleChance
  if GM.OpenFunctionModel:IsFunctionOpen(EFunction.Bubble) then
    bubbleChance = newItem:GetBubbleChance()
  end
  if self.m_lastBreakBubbleType == targetItem:GetType() and GM.ConfigModel:HasBubbleBuyBonus() and bubbleChance ~= nil and 0 < bubbleChance then
    bubbleChance = 100
  end
  if bubbleChance ~= nil and 0 < bubbleChance and GameConfig.IsTestMode() and PlayerPrefs.GetInt(EPlayerPrefKey.TestAlwaysBubble, 0) ~= 0 then
    bubbleChance = 100
  end
  local bubbleType
  if not self:IsBoardFull() and bubbleChance ~= nil and bubbleChance >= math.random(100) and self:IsUnfilledOrderRequirementsChainOrPdChainItem(newItem:GetType(), false) then
    local filter = function(itemModel)
      local itemBubble = itemModel:GetComponent(ItemBubble)
      return itemBubble ~= nil
    end
    local curBubbles = self:FilterItems(filter)
    local bubblePosition = self:FindEmptyPositionInSpreadOrder(newItem:GetPosition())
    if #curBubbles < 2 and bubblePosition ~= nil then
      bubbleType = newItem:GetType()
      local bubbleCode = ItemCodePrefix.Bubble .. bubbleType
      self:SpreadItem(newItem, bubblePosition, bubbleCode, false)
    end
  end
  local dropOnMerged = GM.ItemDataModel:GetModelConfig(newItem:GetType()).DropOnMerged or {}
  for _, v in ipairs(dropOnMerged) do
    if not self:IsBoardFull() then
      local dropPosition = self:FindEmptyPositionInSpreadOrder(newItem:GetPosition())
      if dropPosition ~= nil then
        self:SpreadItem(newItem, dropPosition, v[PROPERTY_TYPE], false)
      end
    end
  end
  local ext = {
    bubble = bubbleType,
    cobWeb = (item:GetComponent(ItemCobweb) ~= nil or targetItem:GetComponent(ItemCobweb) ~= nil) and 1 or nil
  }
  PlatformInterface.Vibrate(EVibrationType.Medium)
  GM.BIManager:LogMerge(item:GetType(), newItem:GetCode(), ext, self:GetGameMode())
  self.m_lastBreakBubbleType = nil
end

function BaseInteractiveBoardModel:MergeAll()
  if not GameConfig.IsTestMode() then
    return
  end
  self.m_mergeAllIgnoredItems = self:GetMergeAllIgnoreItems()
  local pair = self:FindMergePair(self.m_mergeAllIgnoredItems)
  while pair ~= nil do
    self:_MergeItem(pair[1], pair[2], pair[2]:GetPosition())
    pair = self:FindMergePair(self.m_mergeAllIgnoredItems)
  end
end

function BaseInteractiveBoardModel:GetMergeAllIgnoreItems()
  local mergeAllIgnoredItems = {}
  local filter = function(itemModel)
    local itemSplit = itemModel:GetComponent(ItemSplit)
    return itemSplit ~= nil
  end
  local curSplits = self:FilterItems(filter)
  for _, item in ipairs(curSplits) do
    mergeAllIgnoredItems[item] = true
  end
  return mergeAllIgnoredItems
end

function BaseInteractiveBoardModel:_AffectItem(item, targetItem)
  local affected
  local itemBooster = item:GetComponent(ItemBooster)
  if itemBooster ~= nil then
    affected = itemBooster:TakeEffect(targetItem)
  end
  local itemSplit = item:GetComponent(ItemSplit)
  if itemSplit ~= nil then
    if targetItem:GetComponent(ItemSplit) == nil then
      item:SetPosition(item:GetPosition())
      local type = targetItem:GetType()
      local chainId = GM.ItemDataModel:GetChainId(type)
      local level = GM.ItemDataModel:GetChainLevel(type)
      local chain = GM.ItemDataModel:GetChain(chainId)
      local newItemCode = chain[level - 1]
      if newItemCode == nil then
        Log.Error(type .. " 不能被剪刀作用，请检查棋子配置！")
        return false
      end
      GM.UIManager:OpenView(UIPrefabConfigName.LevelDownConfirmWindow, type, newItemCode, function()
        itemSplit:SplitItem(targetItem, newItemCode)
        self:_Try2LogBoardFull()
      end)
      affected = true
    else
      affected = itemSplit:MergeItem(targetItem)
    end
  end
  if affected then
    self:_Try2LogBoardFull()
  end
  return affected
end

function BaseInteractiveBoardModel:SellItem(item)
  local cost = item:GetSellPrice()
  if cost[PROPERTY_TYPE] ~= DELETE_TAG then
    local exchanges = {cost}
    GM.PropertyDataManager:Acquire(exchanges, EPropertySource.Give, EBIType.ItemSell)
    GM.BIManager:LogStore(item:GetCode(), 1, cost[PROPERTY_TYPE], cost[PROPERTY_COUNT], EShopType.SellItem, self:GetGameMode())
  else
    GM.BIManager:LogStore(item:GetCode(), 1, EPropertyType.Gold, 0, EShopType.SellItem, self:GetGameMode())
  end
  GM.BIManager:LogAction(EBIType.ItemSell, ItemModelHelper.FormatCost2String(item), self:GetGameMode())
  self:RemoveItem(item)
  self.event:Call(BoardEventType.SellItem, {Source = item})
end

function BaseInteractiveBoardModel:UndoSellItem(item)
  local cost = item:GetSellPrice()
  if cost[PROPERTY_TYPE] ~= DELETE_TAG then
    GM.PropertyDataManager:Consume(cost[PROPERTY_TYPE], cost[PROPERTY_COUNT], EBIType.UndoSellItem, false, item:GetCode())
    GM.BIManager:LogStore(cost[PROPERTY_TYPE], cost[PROPERTY_COUNT], item:GetCode(), 1, EShopType.SellItem, self:GetGameMode())
  else
    GM.BIManager:LogStore(EPropertyType.Gold, 0, item:GetCode(), 1, EShopType.SellItem, self:GetGameMode())
  end
  self:SaveItemProperty(item)
  local position = item:GetPosition()
  local removedItem
  local originalItem = self:GetItem(item:GetPosition())
  if originalItem ~= nil then
    local otherPosition = self:FindEmptyPositionInSpreadOrder(item:GetPosition())
    if otherPosition == nil then
      removedItem = originalItem
      self:RemoveItem(originalItem)
      self:CacheItems({
        originalItem:GetCode()
      }, CacheItemType.Stack, {
        ItemModelHelper.GetCost(originalItem)
      })
    else
      position = otherPosition
    end
  end
  item:SetPosition(position)
  self:_SetItem(position, item)
  self.event:Call(BoardEventType.UndoSellItem, {Source = item, Removed = removedItem})
end

function BaseInteractiveBoardModel:BreakItem(item, isFree)
  item:DispatchComponentEvent("OnBreak", isFree)
  local itemBubble = item:GetComponent(ItemBubble)
  if itemBubble ~= nil then
    self.m_lastBreakBubbleType = itemBubble:GetInnerItemCode()
  end
end

function BaseInteractiveBoardModel:_Try2LogBoardFull()
  if self:IsBoardFull() then
    GM.BIManager:LogAction(EBIType.BoardFull, GM.BIManager:TableToString({
      itf = self.IsInventoryFull and self:IsInventoryFull() and 1 or 0,
      m = GM.SceneManager:GetGameMode() == EGameMode.Board and 1 or 0
    }), self:GetGameMode())
  end
end

function BaseInteractiveBoardModel:_LogMove(item, sourcePosition, targetPosition)
  local positionToString = function(position)
    return position:GetX() .. "," .. position:GetY()
  end
  local action = {
    code = item:GetCode(),
    source = positionToString(sourcePosition),
    target = positionToString(targetPosition),
    event_id = BoardModelHelper.GetActiveActivityModelEventId()
  }
  local actionString = GM.BIManager:TableToString(action)
  GM.BIManager:LogAction(EBIType.MoveItem, actionString, self:GetGameMode())
end

function BaseInteractiveBoardModel:AddMaterialToCook(materialItem, itemCookCmp)
  local added = itemCookCmp:AddMaterial(materialItem)
  if not added then
    return
  end
  self:_SetItem(materialItem:GetPosition(), nil)
  materialItem:SetPosition(nil, true)
  self.event:Call(BoardEventType.BatchRemoveItems, {
    Removed = {materialItem},
    Dur = 0
  })
end

function BaseInteractiveBoardModel:PutBackMaterial(itemCode, itemId, startWorldPos)
  local position = self.m_itemLayerModel:FindEmptyPositionFromBottom2Top()
  if position == nil then
    return
  end
  local item = self.m_itemManager:GetItem(itemId)
  if item == nil then
    item = self:GenerateItem(position, itemCode)
  else
    self:_SetItem(position, item)
    item:SetPosition(position)
  end
  self.event:Call(BoardEventType.PutBackMaterial, {Item = item, Pos = startWorldPos})
  self:_Try2LogBoardFull()
  return item
end

function BaseInteractiveBoardModel:TakeOutCookedRecipe(itemModel, materialIds, recipeCode, replace)
  local newPosition
  if not replace then
    newPosition = self:FindEmptyPositionInSpreadOrder(itemModel:GetPosition())
    if newPosition == nil then
      return
    end
  end
  local totalCost = {
    cookSkipPropCost = itemModel.cookSkipPropCost,
    cookGemCost = itemModel.cookGemCost
  }
  itemModel:ClearCookCost()
  for _, materialId in ipairs(materialIds) do
    local item = self.m_itemManager:GetItem(materialId)
    if item then
      totalCost = ItemModelHelper.MergeCost(totalCost, item)
      item:Destroy()
      self:_RemoveItemProperty(item)
    end
  end
  if replace then
    local newItem = self:ReplaceItem(itemModel, recipeCode, totalCost)
    local eventInfo = {Source = itemModel, New = newItem}
    self.event:Call(BoardEventType.TransformItem, eventInfo)
  else
    self:SpreadItem(itemModel, newPosition, recipeCode, true, totalCost)
  end
  EventDispatcher.DispatchEvent(EEventType.TakeOutDishItem, recipeCode)
end

function BaseInteractiveBoardModel:GetAllItemCookCmp(includBoard, includeStore)
  local arrResult = {}
  if includBoard == false then
    return arrResult
  end
  for item, _ in pairs(self:GetAllBoardItems()) do
    local itemCook = item:GetComponent(ItemCook)
    if itemCook ~= nil then
      arrResult[#arrResult + 1] = itemCook
    end
  end
  return arrResult
end
