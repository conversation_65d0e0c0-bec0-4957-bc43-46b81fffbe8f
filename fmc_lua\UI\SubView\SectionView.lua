SectionView = {}
SectionView.__index = SectionView

function SectionView:Awake()
  function self.m_eventTrigger.OnLuaBeginDrag(eventData)
    self:_OnBeginDrag(eventData)
  end
  
  self.m_switchTween = DOTween.Sequence()
  self.m_switchTween:InsertCallback(5, function()
    self:_AutoSwitch()
  end)
  self.m_switchTween:SetLoops(-1)
  self.m_cellWidth = self.m_cellGo.transform.sizeDelta.x
end

function SectionView:OnDestroy()
  if self.m_switchTween ~= nil then
    self.m_switchTween:Kill()
    self.m_switchTween = nil
  end
  Scheduler.UnscheduleTarget(self)
end

function SectionView:UpdateSwitchCallback(callback)
  self.m_switchCallback = callback
end

function SectionView:GetCell(index)
  if self.m_cellCount == nil or index > self.m_cellCount then
    return
  end
  return self.m_arrCells[index]
end

function SectionView:GetCellCount()
  return self.m_cellCount
end

function SectionView:UpdateCells(cellDatas, flushAnimation)
  self.m_cellCount = #cellDatas
  if self.m_arrCells == nil then
    self.m_arrCells = {}
  end
  local cell
  for i = 1, self.m_cellCount do
    if self.m_arrCells[i] == nil then
      cell = GameObject.Instantiate(self.m_cellGo, self.m_contentRectTrans)
      self.m_arrCells[i] = cell:GetLuaTable()
    end
    UIUtil.SetActive(self.m_arrCells[i].gameObject, true)
    self.m_arrCells[i]:Init(cellDatas[i])
  end
  for i = self.m_cellCount + 1, #self.m_arrCells do
    UIUtil.SetActive(self.m_arrCells[i].gameObject, false)
  end
  if self.m_arrDots == nil then
    self.m_arrDots = {
      self.m_normalDotGo
    }
  end
  UIUtil.SetActive(self.m_dotsGo, self.m_cellCount > 1)
  for i = 1, self.m_cellCount - 1 do
    if self.m_arrDots[i] == nil then
      self.m_arrDots[i] = GameObject.Instantiate(self.m_normalDotGo, self.m_dotsRectTrans)
    end
    UIUtil.SetActive(self.m_arrDots[i], true)
  end
  for i = self.m_cellCount, #self.m_arrDots do
    UIUtil.SetActive(self.m_arrDots[i], false)
  end
  DOTween.Kill(self.m_contentRectTrans)
  if self.m_cellCount > 1 and flushAnimation then
    self:_SwitchTo(self.m_cellCount, false)
    DelayExecuteFuncInView(function()
      self:_SwitchTo(1, true, true)
    end, 1, self)
  elseif self.m_cellCount > 0 then
    self:_SwitchTo(1, false)
  end
end

function SectionView:_OnBeginDrag(eventData)
  if self.m_cellCount == nil then
    return
  end
  if self.m_switchTween ~= nil then
    self.m_switchTween:Restart()
  end
  if eventData.delta.x < 0 then
    if self.m_currentIndex < self.m_cellCount then
      self:_SwitchTo(self.m_currentIndex + 1, true)
    end
  elseif self.m_currentIndex > 1 then
    self:_SwitchTo(self.m_currentIndex - 1, true)
  end
end

function SectionView:_SwitchTo(index, withAnimation, autoSwitch)
  self.m_currentIndex = index
  self.m_currentDotRectTrans:SetSiblingIndex(index - 1)
  local targetPositionX = -(index - 1) * self.m_cellWidth
  if withAnimation then
    self.m_contentRectTrans:DOAnchorPosX(targetPositionX, 0.6)
  else
    local bundlePosition = self.m_contentRectTrans.anchoredPosition
    bundlePosition.x = targetPositionX
    self.m_contentRectTrans.anchoredPosition = bundlePosition
  end
  if self.m_switchCallback then
    self.m_switchCallback(index, withAnimation, autoSwitch)
  end
end

function SectionView:_AutoSwitch()
  if self.m_cellCount == nil then
    return
  end
  if self.m_currentIndex < self.m_cellCount then
    self:_SwitchTo(self.m_currentIndex + 1, true, true)
  else
    self:_SwitchTo(1, true, true)
  end
end
