SurveySingleChoiceFillBlankImgQuestion = setmetatable({}, BaseSurveyQuestion)
SurveySingleChoiceFillBlankImgQuestion.__index = SurveySingleChoiceFillBlankImgQuestion

function SurveySingleChoiceFillBlankImgQuestion:Init(questionData)
  self.m_questionData = questionData
  self.m_arrChoice = {}
  self.m_title.text = self.m_questionData.title
  self:_InitPicture()
  local bFixedSeq = self.m_questionData.fixedSeq
  local options = self.m_questionData.options
  if not bFixedSeq and not Table.IsEmpty(options) then
    local lastOptios
    if options[#options].is_open then
      lastOptios = options[#options]
      table.remove(options, #options)
    end
    options = Table.ListAlwaysRandomSelectN(options, #options)
    if lastOptios then
      options[#options + 1] = lastOptios
    end
  end
  local choiceGo, choiceCmp, inputFieldGo
  for i = 1, #options do
    choiceGo = i == 1 and self.m_choiceGo or GameObject.Instantiate(self.m_choiceGo, self.m_choiceContainer)
    choiceCmp = choiceGo:GetLuaTable()
    if i == #options and options[i].is_open then
      inputFieldGo = self.m_inputFieldGo
    else
      inputFieldGo = nil
    end
    choiceCmp:Init(options[i], inputFieldGo)
    choiceCmp:SetParent(self)
    self.m_arrChoice[#self.m_arrChoice + 1] = choiceCmp
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_choiceContainer)
end

function SurveySingleChoiceFillBlankImgQuestion:UpdateView()
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_choiceContainer)
end

function SurveySingleChoiceFillBlankImgQuestion:CanNextStep()
  if self.m_questionData.rule == "free" then
    return true
  end
  local seletedCount = 0
  for i = 1, #self.m_arrChoice do
    if self.m_arrChoice[i]:IsSelected() then
      seletedCount = seletedCount + 1
    end
  end
  if seletedCount == 0 then
    GM.UIManager:ShowPromptWithKey("question_error_tips")
    return false
  end
  return true
end

function SurveySingleChoiceFillBlankImgQuestion:GetUserAnswer()
  local answers = {}
  local nextQIds = {}
  local choiceCmp, choiceData, vmemo
  for i = 1, #self.m_arrChoice do
    choiceCmp = self.m_arrChoice[i]
    if choiceCmp:IsSelected() then
      choiceData = choiceCmp.choiceData
      if answers[EAnswerKey.AnserId] == nil then
        answers[EAnswerKey.AnserId] = {}
      end
      answers[EAnswerKey.AnserId][#answers[EAnswerKey.AnserId] + 1] = choiceData.cid
      if choiceData.visible_ids ~= nil then
        nextQIds = choiceData.visible_ids
      end
      vmemo = choiceCmp:GetInput()
      if not StringUtil.IsNilOrEmpty(vmemo) then
        if answers[EAnswerKey.Vmemo] == nil then
          answers[EAnswerKey.Vmemo] = vmemo
        else
          answers[EAnswerKey.Vmemo] = answers[EAnswerKey.Vmemo] .. ";" .. vmemo
        end
      end
    end
  end
  return answers, nextQIds
end
