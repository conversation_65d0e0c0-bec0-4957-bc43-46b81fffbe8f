ShopModel = {}
ShopModel.__index = ShopModel
local sec2Day = Sec2Day
local DBColumnFreshTimeDailyDeals = "dailyRefreshTime"
local DBColumnFreshTimeFlashSale = "flashRefreshTime"
local DBColumnFreshTimeEnergy = "energyRefreshTime"
local DBColumnBuyCostEnergy = "energyBuyCost"
local DBColumnBuyCostEventEnergy = "eventEnergyBuyCost"
local DBColumnEnergyDiscountCount = "energyDiscountCount"
local DBColumnEnergyDiscountPrice = "energyDiscountPrice"
local DBColumnEnergyDiscountLastTime = "energyDiscountLastTime"
local DBColumnEnergyDiscountCooldownTime = "energyDiscountCooldownTime"
local DBColumnResetTimeForRefreshCost = "refreshCostResetTime"
local DBColumnRefreshCost = "refreshCost"
local ShopPropertyTypeAd = "ad"
local FLASH_SALE_REFRESH_DURATION = 28800

function ShopModel:Init()
  self.m_shopDB = GM.DBTableManager:GetTable(EDBTableConfigs.Shop)
  self.m_itemDBManager = ShopItemDBManager
  self.m_itemDBManager:Init()
end

function ShopModel:LoadServerConfig()
  local md5 = GM.ConfigModel:GetServerConfigMD5(ServerConfigKey.EnergyBuyDiscount)
  if md5 ~= self.m_energyDiscountConfigMd5 then
    self.m_energyDiscountConfigMd5 = md5
    self.m_energyDiscountData = nil
    local data = GM.ConfigModel:GetServerConfig(ServerConfigKey.EnergyBuyDiscount)
    if data ~= nil and data[1] ~= nil then
      data = data[1]
      self.m_energyDiscountData = {}
      self.m_energyDiscountData.cooldownDuration = data.cool_time * 60
      self.m_energyDiscountData.dailyTimeLimit = data.daily_time_limit
      self.m_energyDiscountData.lastDuration = data.discount_last_time * 60
      for k, arrWeightData in pairs(data) do
        if StringUtil.StartWith(k, "discount_probability_") then
          local energyCount = StringUtil.Replace(k, "discount_probability_", "")
          local energyCount = tonumber(energyCount)
          self.m_energyDiscountData[energyCount] = {}
          for _, v in ipairs(arrWeightData) do
            local weightData = StringUtil.SplitToNum(v, "-")
            self.m_energyDiscountData[energyCount][#self.m_energyDiscountData[energyCount] + 1] = {
              Price = weightData[1],
              Weight = weightData[2]
            }
          end
        end
      end
    end
  end
end

function ShopModel:OnSyncDataFinished()
  if self.m_shopDB:IsEmpty() then
    self:_CreateNewUser()
  end
end

function ShopModel:LateInit()
  self.m_lateInited = true
  self:UpdatePerSecond()
  local bAllValid = true
  local ids = self:GetItemIdsFlashSale() or {}
  for _, id in ipairs(ids) do
    local itemData = self:GetItemData(id)
    local code = itemData.itemCode
    if not GM.ItemDataModel:IsItemExist(code) then
      bAllValid = false
      GM.BIManager:LogProject(EBIType.ItemNotFound, "Shop itemCode:" .. tostring(code))
    end
  end
  if not bAllValid then
    self:RefreshFlashSaleByDayRefresh()
  end
end

function ShopModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function ShopModel:_CreateNewUser()
  local data = {
    [DBColumnFreshTimeDailyDeals] = {
      [DB_VALUE_KEY] = 0
    },
    [DBColumnFreshTimeFlashSale] = {
      [DB_VALUE_KEY] = 0
    },
    [DBColumnFreshTimeEnergy] = {
      [DB_VALUE_KEY] = 0
    },
    [DBColumnBuyCostEnergy] = {
      [DB_VALUE_KEY] = GM.ShopDataModel:GetBuyEnergyBaseGemCost()
    },
    [DBColumnBuyCostEventEnergy] = {
      [DB_VALUE_KEY] = GM.ShopDataModel:GetBuyEnergyBaseGemCost()
    },
    [DBColumnRefreshCost] = {
      [DB_VALUE_KEY] = GM.ShopDataModel:GeRefreshBaseGemCost()
    }
  }
  self.m_shopDB:BatchSet(data)
end

function ShopModel:UpdatePerSecond()
  if not self.m_lateInited then
    return
  end
  local serverTime = GM.GameModel:GetServerTime()
  self:_TryToRefreshDailyDeals(serverTime)
  self:_TryToRefreshFlashSale(serverTime)
  self:_TryToRefreshEnergy(serverTime)
  self:_TryToResetRefreshCost(serverTime)
end

function ShopModel:GetItemData(itemId)
  return self.m_itemDBManager:GetShopItemData(itemId)
end

function ShopModel:BuyItem(eShopType, itemId, watchAd, uiPos)
  local shopData = self.m_itemDBManager:GetShopItemData(itemId)
  if shopData.itemCode ~= nil then
    if shopData.leftCount <= 0 then
      return false
    end
    if not watchAd and 0 < shopData.costCount and not GM.PropertyDataManager:Consume(shopData.costType, shopData.costCount, EBIType.ShopBuy, false, shopData.itemCode) then
      return false, shopData.costType, shopData.costCount - GM.PropertyDataManager:GetPropertyNum(shopData.costType)
    end
    local cost
    if not watchAd and 0 < shopData.costCount and shopData.costType == EPropertyType.Gem then
      cost = {
        shopGemCost = shopData.costCount
      }
    end
    local formattedRewards = {
      {
        [PROPERTY_TYPE] = shopData.itemCode,
        [PROPERTY_COUNT] = 1,
        cost = cost
      }
    }
    RewardApi.AcquireRewardsLogic(formattedRewards, EPropertySource.Buy, EBIType.ShopBuy, EGameMode.Board, CacheItemType.Stack)
    if watchAd then
      GM.BIManager:LogStore(ShopPropertyTypeAd, 1, shopData.itemCode, 1, shopData.shopType)
    else
      GM.BIManager:LogStore(shopData.costType, shopData.costCount, shopData.itemCode, 1, shopData.shopType)
      if GM.UIManager:IsViewExisting(UIPrefabConfigName.ShopWindow) then
        GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, {
          {
            [PROPERTY_TYPE] = shopData.itemCode,
            [PROPERTY_COUNT] = 1
          }
        }, "rewards_window_title_shop", false)
      end
      GM.AudioModel:PlayEffect(AudioFileConfigName.SfxShopBuy)
    end
    if shopData.leftCount > 0 and self.m_itemDBManager:CostLeftCount(eShopType, itemId) then
      EventDispatcher.DispatchEvent(EEventType.ShopBuyItemSuccess, {shopData = shopData, uiPos = uiPos})
      return true
    end
  end
  return false
end

function ShopModel:GetItemIdsDailyDeals()
  if not self:IsShopTypeUnlocked(EShopType.DailyDeals) then
    return nil
  end
  return self.m_itemDBManager:GetShopItemIds(EShopType.DailyDeals)
end

function ShopModel:HasFreeDailyDeals()
  local itemIds = self:GetItemIdsDailyDeals()
  if itemIds ~= nil and 0 < #itemIds then
    local data
    for _, itemId in ipairs(itemIds) do
      data = self:GetItemData(itemId)
      if data.costCount == 0 and 0 < data.leftCount then
        return true
      end
    end
  end
  return false
end

function ShopModel:GetRefreshTimeDailyDeals()
  return (self.m_shopDB:GetValueInNumber(DBColumnFreshTimeDailyDeals, DB_VALUE_KEY) // sec2Day + 1) * sec2Day
end

function ShopModel:_TryToRefreshDailyDeals(serverTime)
  if serverTime >= self:GetRefreshTimeDailyDeals() then
    self:_RefreshDailyDeals(serverTime)
  end
end

function ShopModel:_RefreshDailyDeals(serverTime)
  self.m_shopDB:Set(DBColumnFreshTimeDailyDeals, DB_VALUE_KEY, serverTime)
  self.m_itemDBManager:RemoveShopItemDatas(EShopType.DailyDeals)
  self.m_itemDBManager:AddShopItemDatas(EShopType.DailyDeals, GM.ShopDataModel:GetNewItemDatasForDailyDeals())
  EventDispatcher.DispatchEvent(EEventType.ShopRefreshed, {
    shopType = EShopType.DailyDeals
  })
end

function ShopModel:GetItemIdsFlashSale()
  return self.m_itemDBManager:GetShopItemIds(EShopType.FlashSale)
end

function ShopModel:IsItemInStock(shopType, itemCode)
  if not self:IsShopTypeUnlocked(shopType) then
    return false
  end
  return self.m_itemDBManager:IsItemInStock(shopType, itemCode)
end

function ShopModel:GetFlashSalePassTimePercentage()
  local nextRefreshTime = self:GetRefreshTimeFlashSale()
  local leftTime = nextRefreshTime - GM.GameModel:GetServerTime()
  if leftTime < 0 then
    return 1
  end
  local percentage = 1 - leftTime / FLASH_SALE_REFRESH_DURATION
  return percentage
end

function ShopModel:GetResetTimeForRefreshCost()
  return (self.m_shopDB:GetValueInNumber(DBColumnResetTimeForRefreshCost, DB_VALUE_KEY) // sec2Day + 1) * sec2Day
end

function ShopModel:_TryToResetRefreshCost(serverTime)
  if serverTime >= self:GetResetTimeForRefreshCost() then
    self:_ResetRefreshCost(serverTime)
  end
end

function ShopModel:_ResetRefreshCost(serverTime)
  self.m_shopDB:Set(DBColumnResetTimeForRefreshCost, DB_VALUE_KEY, serverTime)
  self.m_shopDB:Set(DBColumnRefreshCost, DB_VALUE_KEY, GM.ShopDataModel:GeRefreshBaseGemCost())
  EventDispatcher.DispatchEvent(EEventType.ShopRefreshed, {
    shopType = EShopType.FlashSale
  })
end

function ShopModel:GetRefreshGemCost()
  local cost = self.m_shopDB:GetValueInNumber(DBColumnRefreshCost, DB_VALUE_KEY)
  if cost == 0 then
    cost = GM.ShopDataModel:GeRefreshBaseGemCost()
  end
  return cost
end

function ShopModel:RefreshFlashSaleByGemCost()
  local cost = self:GetRefreshGemCost()
  if GM.PropertyDataManager:Consume(EPropertyType.Gem, cost, EBIType.ShopRefresh, false, EBIConsumerType.ShopRefresh) then
    self:_RefreshFlashSale(GM.GameModel:GetServerTime(), true)
    self.m_shopDB:Set(DBColumnRefreshCost, DB_VALUE_KEY, GM.ShopDataModel:GetNextRefreshGemCost(cost))
    return true
  end
  return false
end

function ShopModel:RefreshFlashSaleByDayRefresh()
  Log.Info("RefreshFlashSaleByDayRefresh")
  self:_RefreshFlashSale(GM.GameModel:GetServerTime(), false)
end

function ShopModel:GetRefreshTimeFlashSale()
  local startTime = self.m_shopDB:GetValueInNumber(DBColumnFreshTimeFlashSale, DB_VALUE_KEY)
  return startTime + FLASH_SALE_REFRESH_DURATION
end

function ShopModel:_TryToRefreshFlashSale(serverTime)
  if serverTime >= self:GetRefreshTimeFlashSale() then
    self:_RefreshFlashSale(serverTime)
  end
end

function ShopModel:_RefreshFlashSale(serverTime, IsManualRefresh)
  if not self:IsShopTypeUnlocked(EShopType.FlashSale) then
    return
  end
  self.m_shopDB:Set(DBColumnFreshTimeFlashSale, DB_VALUE_KEY, serverTime)
  local aLastShopItem = {}
  if IsManualRefresh then
    aLastShopItem = self.m_itemDBManager:GetShopItemDatas(EShopType.FlashSale) or {}
  end
  self.m_itemDBManager:RemoveShopItemDatas(EShopType.FlashSale)
  self.m_itemDBManager:AddShopItemDatas(EShopType.FlashSale, GM.ShopDataModel:GetNewItemDatasForFlashSale(aLastShopItem))
  EventDispatcher.DispatchEvent(EEventType.ShopRefreshed, {
    shopType = EShopType.FlashSale
  })
end

local mapEnergyDatas

function ShopModel:GetEnergyDatas(energyType)
  if mapEnergyDatas == nil then
    mapEnergyDatas = {
      [EPropertyType.Energy] = {
        {
          icon = "energy",
          goods = {
            {
              [PROPERTY_TYPE] = EPropertyType.Energy,
              [PROPERTY_COUNT] = GM.ShopDataModel:GetBuyEnergyCount()
            }
          },
          costType = EPropertyType.Gem
        }
      }
    }
  end
  local propertyType = EnergyModel.EnergyType2PropertyType(energyType)
  mapEnergyDatas[propertyType][1].costNum = self:GetBuyEnergyGemCost(energyType)
  mapEnergyDatas[propertyType][1].originalCostNum = self:GetBuyEnergyGemCost(energyType, true)
  return mapEnergyDatas[propertyType]
end

function ShopModel:GetRefreshTimeEnergy()
  return (self.m_shopDB:GetValueInNumber(DBColumnFreshTimeEnergy, DB_VALUE_KEY) // sec2Day + 1) * sec2Day
end

function ShopModel:GetBuyEnergyGemCost(energyType, original)
  if energyType == EnergyType.Event then
    local cost = self.m_shopDB:GetValueInNumber(DBColumnBuyCostEventEnergy, DB_VALUE_KEY)
    return 0 < cost and cost or GM.ShopDataModel:GetBuyEnergyBaseGemCost()
  elseif original or not self:IsEnergyBuyInDiscount() then
    local cost = self.m_shopDB:GetValueInNumber(DBColumnBuyCostEnergy, DB_VALUE_KEY)
    return 0 < cost and cost or GM.ShopDataModel:GetBuyEnergyBaseGemCost()
  else
    return self.m_shopDB:GetValueInNumber(DBColumnEnergyDiscountPrice, DB_VALUE_KEY)
  end
end

local TRIGGER_ENERGY_DISCOUNT_PRICE_RATIO = 0.5

function ShopModel:Try2TriggerEnergyBuyDiscount()
  if self.m_energyDiscountData == nil or self:IsEnergyBuyInDiscount() or self.m_energyDiscountData.dailyTimeLimit <= self:_GetEnergyDiscountCount() or self:_IsEnergyDiscountInCooldown() or self.m_energyDiscountData[self:GetBuyEnergyGemCost(EnergyType.Main, true)] == nil or GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem) >= self:GetBuyEnergyGemCost(EnergyType.Main, true) * TRIGGER_ENERGY_DISCOUNT_PRICE_RATIO then
    return
  end
  local serverTime = GM.GameModel:GetServerTime()
  self.m_shopDB:Set(DBColumnEnergyDiscountCount, DB_VALUE_KEY, self:_GetEnergyDiscountCount() + 1)
  self.m_shopDB:Set(DBColumnEnergyDiscountCooldownTime, DB_VALUE_KEY, serverTime + self.m_energyDiscountData.cooldownDuration)
  local discountData = Table.ListWeightSelectOne(self.m_energyDiscountData[self:GetBuyEnergyGemCost(EnergyType.Main, true)])
  GM.BIManager:LogAction(EBIType.TriggerEnergyDiscount, json.encode({
    c = self:_GetEnergyDiscountCount(),
    oP = self:GetBuyEnergyGemCost(EnergyType.Main, true),
    dP = discountData.Price
  }))
  if discountData.Price ~= self:GetBuyEnergyGemCost(EnergyType.Main, true) then
    self.m_shopDB:Set(DBColumnEnergyDiscountLastTime, DB_VALUE_KEY, serverTime + self.m_energyDiscountData.lastDuration)
    self.m_shopDB:Set(DBColumnEnergyDiscountPrice, DB_VALUE_KEY, discountData.Price)
  end
end

function ShopModel:OpenEnergyWindow(energyType, popupForLackingEnergy, inShopWindow)
  GM.UIManager:OpenView(BuyEnergyWindow.EnergyType2WindowName(energyType), popupForLackingEnergy, inShopWindow)
end

function ShopModel:IsEnergyBuyInDiscount()
  return not StringUtil.IsNilOrEmpty(self.m_shopDB:GetValue(DBColumnEnergyDiscountPrice, DB_VALUE_KEY))
end

function ShopModel:GetEnergyDiscountLastTime()
  return self.m_shopDB:GetValueInNumber(DBColumnEnergyDiscountLastTime, DB_VALUE_KEY)
end

function ShopModel:IsEnergyFreeRefill(energyType)
  return energyType == EnergyType.Main and GM.MiscModel:GetFreeRefillEnergyInNumber() == 0
end

function ShopModel:BuyEnergy(energyType)
  local free2Refill = self:IsEnergyFreeRefill(energyType)
  local buyGemCost = self:GetBuyEnergyGemCost(energyType)
  local buyGemCostOrigin = self:GetBuyEnergyGemCost(energyType, true)
  local buyEnergyCount = GM.ShopDataModel:GetBuyEnergyCount()
  local propertyType = EnergyModel.EnergyType2PropertyType(energyType)
  if free2Refill then
    local formattedRewards = {
      {
        [PROPERTY_TYPE] = propertyType,
        [PROPERTY_COUNT] = buyEnergyCount,
        [PROPERTY_CRYPT] = Crypt.CryptCurrency(buyEnergyCount)
      }
    }
    RewardApi.AcquireRewardsLogic(formattedRewards, EPropertySource.Give, EBIType.FreeRefillEnergy, EGameMode.Board, CacheItemType.Stack)
    GM.MiscModel:SetFreeRefillEnergy(1)
    GM.BIManager:LogStore(EPropertyType.Gem, 0, energyType, buyEnergyCount, EShopType.Energy)
    self.m_shopDB:Set(DBColumnBuyCostEnergy, DB_VALUE_KEY, GM.ShopDataModel:GetBuyEnergyBaseGemCost())
    return true
  elseif GM.PropertyDataManager:Consume(EPropertyType.Gem, buyGemCost, EBIType.BuyEnergy, false, EBIConsumerType.Energy) then
    local formattedRewards = {
      {
        [PROPERTY_TYPE] = propertyType,
        [PROPERTY_COUNT] = buyEnergyCount,
        [PROPERTY_CRYPT] = Crypt.CryptCurrency(buyEnergyCount)
      }
    }
    RewardApi.AcquireRewardsLogic(formattedRewards, EPropertySource.Buy, EBIType.BuyEnergy, EGameMode.Board, CacheItemType.Stack)
    GM.BIManager:LogStore(EPropertyType.Gem, buyGemCost, propertyType, buyEnergyCount, EShopType.Energy)
    self.m_shopDB:Set(energyType == EnergyType.Event and DBColumnBuyCostEventEnergy or DBColumnBuyCostEnergy, DB_VALUE_KEY, GM.ShopDataModel:GetNextBuyEnergyGemCost(buyGemCostOrigin))
    if energyType == EnergyType.Main and self:IsEnergyBuyInDiscount() then
      self.m_shopDB:Remove(DBColumnEnergyDiscountLastTime, DB_VALUE_KEY)
      self.m_shopDB:Remove(DBColumnEnergyDiscountPrice, DB_VALUE_KEY)
    end
    EventDispatcher.DispatchEvent(EEventType.BuyEnergySuccess, buyGemCost)
    return true
  end
  return false
end

function ShopModel:_TryToRefreshEnergy(serverTime)
  if serverTime >= self:GetRefreshTimeEnergy() then
    self:_RefreshEnergy(serverTime)
    self:_ClearEnergyDiscountData()
  else
    local discountLastTime = self:GetEnergyDiscountLastTime()
    if 0 < discountLastTime and serverTime >= discountLastTime then
      self.m_shopDB:Remove(DBColumnEnergyDiscountLastTime, DB_VALUE_KEY)
      self.m_shopDB:Remove(DBColumnEnergyDiscountPrice, DB_VALUE_KEY)
      EventDispatcher.DispatchEvent(EEventType.ShopRefreshed, {
        shopType = EShopType.Energy
      })
    end
  end
end

function ShopModel:_RefreshEnergy(serverTime)
  self.m_shopDB:Set(DBColumnFreshTimeEnergy, DB_VALUE_KEY, serverTime)
  self.m_shopDB:Set(DBColumnBuyCostEnergy, DB_VALUE_KEY, GM.ShopDataModel:GetBuyEnergyBaseGemCost())
  self.m_shopDB:Set(DBColumnBuyCostEventEnergy, DB_VALUE_KEY, GM.ShopDataModel:GetBuyEnergyBaseGemCost())
  EventDispatcher.DispatchEvent(EEventType.ShopRefreshed, {
    shopType = EShopType.Energy
  })
end

function ShopModel:_ClearEnergyDiscountData()
  self.m_shopDB:Remove(DBColumnEnergyDiscountCount, DB_VALUE_KEY)
  self.m_shopDB:Remove(DBColumnEnergyDiscountPrice, DB_VALUE_KEY)
  self.m_shopDB:Remove(DBColumnEnergyDiscountLastTime, DB_VALUE_KEY)
  self.m_shopDB:Remove(DBColumnEnergyDiscountCooldownTime, DB_VALUE_KEY)
end

function ShopModel:_GetEnergyDiscountCount()
  return self.m_shopDB:GetValueInNumber(DBColumnEnergyDiscountCount, DB_VALUE_KEY)
end

function ShopModel:_IsEnergyDiscountInCooldown()
  return self.m_shopDB:GetValue(DBColumnEnergyDiscountCooldownTime, DB_VALUE_KEY) and GM.GameModel:GetServerTime() < self.m_shopDB:GetValueInNumber(DBColumnEnergyDiscountCooldownTime, DB_VALUE_KEY)
end

function ShopModel:GetDiamondsDatas()
  return GM.ShopDataModel:GetIAPConfigDatas(EShopType.Diamonds)
end

function ShopModel:BuyDiamonds(data, callback)
  if data.purchaseId ~= nil then
    GM.InAppPurchaseModel:StartPurchase(data.purchaseId, function()
      local rewards = data.goods
      RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Buy, EBIType.IAP, EGameMode.Board, CacheItemType.Stack)
      callback(true, rewards)
    end, EBIType.ShopBuy)
  else
    callback(false)
  end
end

function ShopModel:GetShopItemDB()
  return self.m_itemDBManager:GetData()
end

function ShopModel:GetData()
  return self.m_shopDB
end

function ShopModel:FromSyncData(dataArr)
  self.m_shopDB:FromArr(dataArr)
end

function ShopModel:SyncShopItem(dataArr)
  self:GetShopItemDB():FromArr(dataArr)
  self.m_itemDBManager:UpdateShopItemIdMap()
end

function ShopModel:GetUnlockedOrderConfigs()
  local mapShopOrderConfig = GM.ShopDataModel:GetShopOrderConfig()
  local mapUnlockedShopOrderConfig = {}
  for _, eShopType in pairs(EShopType) do
    if self:IsShopTypeUnlocked(eShopType) then
      mapUnlockedShopOrderConfig[eShopType] = mapShopOrderConfig[eShopType]
    end
  end
  return mapUnlockedShopOrderConfig
end

function ShopModel:IsShopTypeUnlocked(eShopType)
  local mapShopOrderConfig = GM.ShopDataModel:GetShopOrderConfig()
  local orderConfig = mapShopOrderConfig[eShopType]
  if not orderConfig then
    return false
  end
  return GM.LevelModel:GetCurrentLevel() >= orderConfig.UnlockLevel
end

function ShopModel:OnLackOfGem(lackNum)
  local openShopWindowFunc = function()
    local shopWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.ShopWindow)
    if shopWindow ~= nil then
      shopWindow:LocateShopType(EShopType.Diamonds)
      if GM.UIManager:IsViewExisting(UIPrefabConfigName.ItemDetailWindow) then
        ItemDetailWindow.CloseAll()
      end
    elseif not GM.UIManager:IsViewExisting(UIPrefabConfigName.ShopWindow) then
      GM.UIManager:OpenView(UIPrefabConfigName.ShopWindow, EShopType.Diamonds)
    end
  end
  if GM.BundleManager:OnLackGem(lackNum, openShopWindowFunc) then
    return
  end
  openShopWindowFunc()
end
