ProgressActivityModel = setmetatable({}, BaseActivityModel)
ProgressActivityModel.__index = ProgressActivityModel
local LevelKey = "level"
local ScoreKey = "score"
local LastDisplayLevelKey = "last_level"
local LastDisplayScoreKey = "last_score"

function ProgressActivityModel:Init(activityType, virtualDBTable)
  self.m_definition = ProgressActivityDefinition[activityType]
  self.m_tokenHelper = ActivityTokenHelper.Create(self, virtualDBTable, EFlyElementLabelStyle.Default)
  self.m_allLevelConfigs = {}
  BaseActivityModel.Init(self, activityType, virtualDBTable)
end

function ProgressActivityModel:Destroy()
  self.m_tokenHelper:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function ProgressActivityModel:_LoadOtherServerConfig(config)
  local rewardCfg = config.progressRewards or Table.Empty
  self.m_allLevelConfigs = {}
  local bRewardError = false
  for _, lConfig in ipairs(rewardCfg) do
    local levelConfigItem = {}
    levelConfigItem.Score = lConfig.score
    levelConfigItem.Level = lConfig.level
    levelConfigItem.Rewards = lConfig.rewards and lConfig.rewards[1]
    if not lConfig.rewards or #lConfig.rewards > 1 then
      bRewardError = true
    end
    RewardApi.CryptOneReward(levelConfigItem.Rewards)
    self.m_allLevelConfigs[lConfig.level] = levelConfigItem
  end
  self.m_tokenHelper:LoadConfig(config)
  if bRewardError then
    Log.Error("进度条活动奖励配置错误")
  end
end

function ProgressActivityModel:_OnStateChanged()
  EventDispatcher.DispatchEvent(ProgressActivityDefinition[self:GetType()].StateChangedEvent)
  EventDispatcher.DispatchEvent(EEventType.OrderStatusChanged)
end

function ProgressActivityModel:GetResourceLabels()
  return self.m_definition.ResourceLabels
end

function ProgressActivityModel:GetDefinition()
  return self.m_definition
end

function ProgressActivityModel:AcquireActivityToken(num)
  if not self:CanAddScore() then
    return
  end
  self:_AddScore(num)
end

function ProgressActivityModel:GetActivityTokenNumber()
  return self.m_dbTable:GetValue(ScoreKey, "value") or 0
end

function ProgressActivityModel:GetLastDisplayLevel()
  return self.m_dbTable:GetValue(LastDisplayLevelKey, "value") or 1
end

function ProgressActivityModel:SetLastDisplayLevel(score)
  self.m_dbTable:Set(LastDisplayLevelKey, "value", score)
end

function ProgressActivityModel:GetLastDisplayScore()
  return self.m_dbTable:GetValue(LastDisplayScoreKey, "value") or 0
end

function ProgressActivityModel:SetLastDisplayScore(score)
  self.m_dbTable:Set(LastDisplayScoreKey, "value", score)
end

function ProgressActivityModel:GetLevelConfigs()
  return self.m_allLevelConfigs
end

function ProgressActivityModel:GetLevelCount()
  return #self.m_allLevelConfigs
end

function ProgressActivityModel:GetTotalRequire()
  local total = 0
  for _, config in ipairs(self.m_allLevelConfigs) do
    total = total + config.Score
  end
  return total
end

function ProgressActivityModel:GetLevelRequire(level)
  return self.m_allLevelConfigs[level] and self.m_allLevelConfigs[level].Score
end

function ProgressActivityModel:GetLevelReward(level)
  return self.m_allLevelConfigs[level] and self.m_allLevelConfigs[level].Rewards
end

function ProgressActivityModel:GetLevel()
  return self.m_dbTable:GetValue(LevelKey, "value") or 1
end

function ProgressActivityModel:CanAddScore()
  if self:GetState() ~= ActivityState.Started then
    return false
  end
  return not self:HasFinishedAllLevel()
end

function ProgressActivityModel:HasFinishedAllLevel()
  local level = self:GetLevel()
  return level > #self.m_allLevelConfigs
end

function ProgressActivityModel:GetOrderExtraReward(orderScore, coinNum, orderId)
  if self.m_tokenHelper:IsAcquireTypeValid(EActTokenAcquireType.FinishOrder) then
    return self.m_tokenHelper:GetOrderExtraReward(orderScore, orderId)
  end
  return nil
end

function ProgressActivityModel:_AddScore(addScore)
  self:LogActivity(EBIType.ActivityAddScore, addScore)
  local level = self:GetLevel()
  local score = self:GetActivityTokenNumber()
  self.m_dbTable:Set(ScoreKey, "value", score + addScore)
  GM.BIManager:LogAction(self.m_definition.GetScoreBIType, {score = addScore, level = level})
  EventDispatcher.DispatchEvent(ProgressActivityDefinition[self:GetType()].ScoreChangedEvent)
end

function ProgressActivityModel:CanAcquireReward()
  if self:GetState() ~= ActivityState.Started and self:GetState() ~= ActivityState.Ended then
    return false
  end
  local level = self:GetLevel()
  local levelConfig = self.m_allLevelConfigs[level]
  local score = self:GetActivityTokenNumber()
  if levelConfig ~= nil and score >= levelConfig.Score then
    return true
  end
  return false
end

function ProgressActivityModel:AcquireReward()
  if not self:CanAcquireReward() then
    return false
  end
  local level = self:GetLevel()
  local levelConfig = self.m_allLevelConfigs[level]
  local rewards = {}
  local score = self:GetActivityTokenNumber()
  while levelConfig ~= nil and score >= levelConfig.Score do
    Table.ListAppend(rewards, {
      levelConfig.Rewards
    })
    RewardApi.AcquireRewardsLogic({
      levelConfig.Rewards
    }, EPropertySource.Give, self.m_definition.GetRewardsBIType, EGameMode.Board, CacheItemType.Stack)
    self:LogActivity(EBIType.ActivityRankUp, level)
    self:LogActivity(EBIType.ActivityGetRewards, level)
    local action = {level = level}
    GM.BIManager:LogAction(self.m_definition.GetRewardsBIType, action)
    score = score - levelConfig.Score
    level = level + 1
    levelConfig = self.m_allLevelConfigs[level]
  end
  self.m_dbTable:Set(LevelKey, "value", level)
  self.m_dbTable:Set(ScoreKey, "value", score)
  EventDispatcher.DispatchEvent(ProgressActivityDefinition[self:GetType()].ScoreChangedEvent)
  EventDispatcher.DispatchEvent(ProgressActivityDefinition[self:GetType()].LevelChangedEvent)
  if levelConfig == nil then
    EventDispatcher.DispatchEvent(EEventType.OrderStatusChanged)
  end
end

function ProgressActivityModel:GetBoardEntryShowConfig()
  return {
    statusChangeEvent = self.m_definition.StateChangedEvent,
    extraListenEvent = self.m_definition.ScoreChangedEvent,
    eEntryRootKey = EEntryRootKey.ProgressActivity,
    entryPrefabName = self.m_definition.BoardEntryPrefabName,
    checkFun = function()
      return self:CanAddScore() or self:CanAcquireReward()
    end
  }
end

function ProgressActivityModel:GetMapEntryShowConfig()
  return {
    statusChangeEvent = self.m_definition.StateChangedEvent,
    extraListenEvent = self.m_definition.ScoreChangedEvent,
    eEntryRootKey = EEntryRootKey.ProgressActivity,
    entryPrefabName = self.m_definition.EntryPrefabName,
    hudKey = self.m_definition.EntryButtonKey,
    checkFun = function()
      return self:CanAddScore() or self:CanAcquireReward() or self:GetState() == ActivityState.Preparing
    end
  }
end
