ChapterUnlockWindow = setmetatable({
  canCloseByAndroidBack = false,
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BaseWindow)
ChapterUnlockWindow.__index = ChapterUnlockWindow
local DRAK_COLOR = CSColor(0.6, 0.6, 0.6, 1)

function ChapterUnlockWindow:Init(arrChapterUnlockRewards)
  self.chapterName = GM.TaskManager:GetOngoingChapterName()
  self.m_titleText.text = GM.ChapterDataModel:GetChapterNameText(self.chapterName)
  local imageName = GM.ChapterDataModel:GetChapterImageKey(self.chapterName)
  SpriteUtil.SetImage(self.m_image, imageName, true, function()
    if self.m_loadingGo and not self.m_loadingGo:IsNull() then
      self.m_loadingGo:SetActive(false)
    end
  end, nil, true)
  self.m_boxGo:SetActive(arrChapterUnlockRewards ~= nil)
  self.m_image.color = DRAK_COLOR
  DelayExecuteFuncInView(function()
    self.m_lockAnimator:SetTrigger("Start")
    self.m_image:DOColor(CSColor.white, 0.2):SetDelay(0.2)
    if IsAutoRun() then
      self:OnClickNext()
    end
  end, 0.7, self, true)
end

function ChapterUnlockWindow:OnClickNext()
  self:Close()
  GM.TaskManager:SetAutoChangingChapter(true)
  GM.SceneManager:ChangeChapterWithTransition(self.chapterName)
end
