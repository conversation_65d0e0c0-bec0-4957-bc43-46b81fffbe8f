OrderType = {
  Fixed = 1,
  Random = 2,
  BakeOut = 3
}
OrderState = {
  Init = 1,
  PartiallyFinished = 2,
  CanDeliver = 3,
  Finished = 4
}
ERequirementFillState = {
  Init = 1,
  Cooking = 2,
  Cooked = 3,
  Filled = 4
}
OrderStateChange = {Higher = 1, Lower = 2}
BaseOrder = {}
BaseOrder.__index = BaseOrder

function BaseOrder:Init(id, requirements, createTime)
  Log.Assert(id ~= nil, "订单需要有唯一id")
  self.m_id = id
  self.m_requirements = {}
  self.m_requirementFillStates = {}
  self.m_createTime = createTime
  self.m_mapLockedCookCmp = {}
  local itemDataModel = GM.ItemDataModel
  for _, requirement in ipairs(requirements) do
    if not StringUtil.IsNilOrEmpty(requirement) then
      local itemConfig = itemDataModel:GetModelConfig(requirement, true)
      if itemConfig then
        self.m_requirements[#self.m_requirements + 1] = requirement
        self.m_requirementFillStates[#self.m_requirementFillStates + 1] = ERequirementFillState.Init
        itemDataModel:SetLocked(requirement)
        if itemDataModel:IsDishes(requirement) then
          local arrNonDishMaterials, arrDishMaterials = itemDataModel:GetAllMaterials(requirement)
          for _, material in ipairs(arrNonDishMaterials) do
            itemDataModel:SetLocked(material)
          end
          for _, material in ipairs(arrDishMaterials) do
            itemDataModel:SetLocked(material)
          end
        end
      else
        Log.Error("订单 " .. id .. " 创建错误，棋子不存在 " .. requirement)
      end
    end
  end
  self.m_orderCompareInfo = {
    filledCount = 0,
    cookedCount = 0,
    cookingCount = 0
  }
  self:_SetState(OrderState.Init, {})
end

function BaseOrder:GetId()
  return self.m_id
end

function BaseOrder:GetAvatarName()
  Log.Assert(false, "GetAvatarName()是抽象接口")
end

function BaseOrder:GetRequirements()
  return self.m_requirements
end

function BaseOrder:GetUniqueRequirements()
  local countMap = {}
  local arrOrder = {}
  for _, itemType in ipairs(self.m_requirements) do
    if not countMap[itemType] then
      countMap[itemType] = 0
      arrOrder[#arrOrder + 1] = itemType
    end
    countMap[itemType] = countMap[itemType] + 1
  end
  return countMap, arrOrder
end

function BaseOrder:GetRequirementFillStates()
  return self.m_requirementFillStates
end

function BaseOrder:GetOrderCompareInfo()
  return self.m_orderCompareInfo
end

function BaseOrder:GetState()
  return self.m_state
end

function BaseOrder:_SetState(state, oldCompareInfo)
  Log.Assert(state ~= OrderState.Finished, "BaseOrder:_SetState")
  if self.m_state ~= state then
    if self.m_state == nil or state > self.m_state then
      self.m_stateChange = OrderStateChange.Higher
    else
      self.m_stateChange = OrderStateChange.Lower
    end
    self.m_state = state
    self.m_stateChangeTime = GM.GameModel:GetServerTime()
  elseif oldCompareInfo.unfilledCount ~= 0 and self.m_orderCompareInfo.unfilledCount == 0 then
    self.m_stateChange = OrderStateChange.Higher
    self.m_stateChangeTime = GM.GameModel:GetServerTime()
  elseif oldCompareInfo.unfilledCount == 0 and self.m_orderCompareInfo.unfilledCount ~= 0 then
    self.m_stateChange = OrderStateChange.Lower
    self.m_stateChangeTime = GM.GameModel:GetServerTime()
  elseif oldCompareInfo.filledCount < self.m_orderCompareInfo.filledCount or oldCompareInfo.filledCount == self.m_orderCompareInfo.filledCount and oldCompareInfo.cookedCount < self.m_orderCompareInfo.cookedCount or oldCompareInfo.filledCount == self.m_orderCompareInfo.filledCount and oldCompareInfo.cookedCount == self.m_orderCompareInfo.cookedCount and oldCompareInfo.cookingCount < self.m_orderCompareInfo.cookingCount then
    self.m_stateChange = OrderStateChange.Higher
    self.m_stateChangeTime = GM.GameModel:GetServerTime()
  elseif oldCompareInfo.filledCount > self.m_orderCompareInfo.filledCount or oldCompareInfo.filledCount == self.m_orderCompareInfo.filledCount and oldCompareInfo.cookedCount > self.m_orderCompareInfo.cookedCount or oldCompareInfo.filledCount == self.m_orderCompareInfo.filledCount and oldCompareInfo.cookedCount == self.m_orderCompareInfo.cookedCount and oldCompareInfo.cookingCount > self.m_orderCompareInfo.cookingCount then
    self.m_stateChange = OrderStateChange.Lower
    self.m_stateChangeTime = GM.GameModel:GetServerTime()
  end
end

function BaseOrder:UpdateState(boardModel, codeCountMap, itemCookCmp)
  if self.m_state == OrderState.Finished then
    return
  end
  local oldCompareInfo = self.m_orderCompareInfo
  local filledCount = 0
  local unfilledCount = 0
  local cookedCount = 0
  local cookingCount = 0
  local remainCookDur = 0
  local itemCookCmpCopy = Table.ShallowCopy(itemCookCmp)
  table.sort(itemCookCmpCopy, function(a, b)
    local durA = a:GetState() == EItemCookState.Cooking and a:GetRemainCookDuration() or 0
    local durB = b:GetState() == EItemCookState.Cooking and b:GetRemainCookDuration() or 0
    return durA > durB
  end)
  for i, requirement in ipairs(self.m_requirements) do
    local count = 1
    for k = i - 1, 1, -1 do
      if requirement == self.m_requirements[k] then
        count = count + 1
      end
    end
    if codeCountMap[requirement] ~= nil and count <= codeCountMap[requirement] and OrderStateHelper.TryFillOrderRequirement(boardModel, requirement, count) then
      self.m_requirementFillStates[i] = ERequirementFillState.Filled
      filledCount = filledCount + 1
    else
      self.m_requirementFillStates[i] = ERequirementFillState.Init
      local cookCmp
      for _, itemCook in ipairs(itemCookCmpCopy) do
        local recipe = itemCook:GetRecipe()
        if recipe == requirement then
          if itemCook:GetState() == EItemCookState.Cooked then
            self.m_requirementFillStates[i] = ERequirementFillState.Cooked
            cookCmp = itemCook
          elseif itemCook:GetState() == EItemCookState.Cooking and self.m_requirementFillStates[i] ~= ERequirementFillState.Cooked then
            self.m_requirementFillStates[i] = ERequirementFillState.Cooking
            cookCmp = itemCook
          end
        end
      end
      if cookCmp then
        Table.ListRemove(itemCookCmpCopy, cookCmp)
      end
      if self.m_requirementFillStates[i] == ERequirementFillState.Cooked then
        cookedCount = cookedCount + 1
      elseif self.m_requirementFillStates[i] == ERequirementFillState.Cooking then
        cookingCount = cookingCount + 1
        local remainTime = math.max(cookCmp:GetRemainCookDuration(), 1)
        remainCookDur = math.max(remainCookDur, remainTime)
      elseif self.m_requirementFillStates[i] == ERequirementFillState.Init then
        unfilledCount = unfilledCount + 1
      end
    end
  end
  self.m_orderCompareInfo = {
    filledCount = filledCount,
    unfilledCount = unfilledCount,
    cookedCount = cookedCount,
    cookingCount = cookingCount,
    remainCookDur = remainCookDur
  }
  if filledCount == 0 then
    self:_SetState(OrderState.Init, oldCompareInfo)
  elseif filledCount == #self.m_requirements then
    self:_SetState(OrderState.CanDeliver, oldCompareInfo)
  else
    self:_SetState(OrderState.PartiallyFinished, oldCompareInfo)
  end
end

function BaseOrder:SetFinished()
  self.m_state = OrderState.Finished
  self:TryUnlockAllCookCmp()
end

function BaseOrder:IsFinished()
  return self.m_state == OrderState.Finished
end

function BaseOrder:GetStateChange()
  return self.m_stateChange
end

function BaseOrder:GetStateChangeTime()
  return self.m_stateChangeTime
end

function BaseOrder:GetCreateTime()
  return self.m_createTime
end

function BaseOrder:GetType()
  Log.Assert(false, "GetType()是抽象接口")
end

function BaseOrder:GetMaxPossibleRewardNumber()
  Log.Assert(false, "GetMaxPossibleRewardNumber()是抽象接口")
end

function BaseOrder:GetRewards()
  Log.Assert(false, "GetRewards()是抽象接口")
end

function BaseOrder:_AppendActivityRewards(rewards)
  local orderScore = self:GetOrderScore()
  local coinNum = self:GetScore(true)
  local tokenType, activityModel
  for _, activityType in ipairs(ActivityTypeSequence) do
    activityModel = GM.ActivityManager:GetModel(activityType)
    if activityModel ~= nil and activityModel.CanAddOrderReward ~= nil and activityModel.GetOrderExtraReward ~= nil and activityModel:CanAddOrderReward(self.m_type) then
      local extraRewards = activityModel:GetOrderExtraReward(orderScore, coinNum, self:GetId())
      if not Table.IsEmpty(extraRewards) and extraRewards[PROPERTY_COUNT] > 0 then
        table.insert(rewards, extraRewards)
      end
    end
  end
end

function BaseOrder:GetScore(includeGold)
  return self:_CalculateScore(self:GetRewards(), includeGold)
end

function BaseOrder:_CalculateScore(rewards, includeGold)
  local score = 0
  for i = 1, #rewards do
    if includeGold and rewards[i][PROPERTY_TYPE] == EPropertyType.Gold or rewards[i][PROPERTY_TYPE] == EPropertyType.BakeOutToken then
      score = score + rewards[i][PROPERTY_COUNT]
    end
  end
  return score
end

function BaseOrder:GetOrderScore()
  local totalScore = 0
  local score
  for _, itemType in ipairs(self.m_requirements) do
    score = GM.ItemDataModel:GetItemScore(itemType)
    if score ~= nil then
      totalScore = totalScore + score
    end
  end
  return totalScore
end

function BaseOrder:GetPiggyBankAccumulateGemNum()
  return nil
end

function BaseOrder:IsTimelimitOrder()
  return false
end

function BaseOrder:GetBIRequirements()
  local content = {}
  for _, requirement in ipairs(self.m_requirements) do
    if content[requirement] == nil then
      content[requirement] = 0
    end
    content[requirement] = content[requirement] + 1
  end
  return GM.BIManager:TableToString(content)
end

function BaseOrder:TryLockCookCmp(itemCook)
  local chain = GM.ItemDataModel:GetChainId(itemCook:GetItemModel():GetType())
  if self.m_mapLockedCookCmp[chain] == itemCook then
    return
  end
  Log.Assert(not self.m_mapLockedCookCmp[chain], "重复锁定")
  self.m_mapLockedCookCmp[chain] = itemCook
  itemCook.__belongOrder = self
end

function BaseOrder:TryUnlockCookCmp(itemCook)
  local chain = GM.ItemDataModel:GetChainId(itemCook:GetItemModel():GetType())
  Log.Assert(self.m_mapLockedCookCmp[chain] == itemCook, "无效解锁")
  self.m_mapLockedCookCmp[chain] = nil
  itemCook.__belongOrder = nil
end

function BaseOrder:TryUnlockAllCookCmp()
  for _, itemCook in pairs(self.m_mapLockedCookCmp) do
    itemCook.__belongOrder = nil
  end
  self.m_mapLockedCookCmp = {}
end

function BaseOrder:CanUseCookCmp(itemCook)
  if itemCook.__belongOrder and itemCook.__belongOrder ~= self then
    return false
  end
  local chain = GM.ItemDataModel:GetChainId(itemCook:GetItemModel():GetType())
  return self.m_mapLockedCookCmp[chain] == itemCook or not self.m_mapLockedCookCmp[chain]
end
