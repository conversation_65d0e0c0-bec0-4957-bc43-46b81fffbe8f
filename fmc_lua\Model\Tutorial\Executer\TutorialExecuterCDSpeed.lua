local Step = {ClickCache = "1", TapItem = "2"}
local EStep2TextKey = {
  [Step.ClickCache] = "tutorial_cd_speed_1",
  [Step.TapItem] = "tutorial_cd_speed_2"
}
local EStep2TextAnchorPercent = {
  [Step.ClickCache] = 40
}
local itemId = "skiptime_1"
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OrderGroupRefreshed, self, self._OnOrderGroupRefreshed)
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self._OnPopCachedItem)
  EventDispatcher.AddListener(EEventType.PopCacheFailed, self, self._OnPopCacheFailed)
  EventDispatcher.AddListener(EEventType.ItemSpeederClicked, self, self._OnItemSpeederClicked)
end

function Executer:_OnOrderGroupRefreshed()
  if self.m_bAdded then
    return
  end
  local orderModel = GM.MainBoardModel:GetOrderModel()
  if orderModel:IsOrderGroupFinished(3, 0) then
    self:Finish()
    return
  end
  if self:_CanExecuteStep1() then
    GM.TutorialModel:AddAutoPopup(self)
    self.m_bAdded = true
  end
end

function Executer:TryStartTutorial()
  self:_ExecuteStep1()
  return true
end

function Executer:_OnPopCachedItem(msg)
  if self.m_strOngoingDatas == Step.ClickCache and self.m_gesture and msg.GameMode == EGameMode.Board then
    TutorialHelper.DehighlightCacheRoot()
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    self:_ExecuteStep2()
  end
end

function Executer:_OnPopCacheFailed()
  if self.m_strOngoingDatas == Step.ClickCache and self.m_gesture then
    self:Finish(self.m_gesture)
  end
end

function Executer:_OnItemSpeederClicked()
  if self.m_strOngoingDatas == Step.TapItem and self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
  end
end

function Executer:_CanExecuteStep1()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return false
  end
  local items = ItemSpeeder.GetEffectedItems(GM.MainBoardModel)
  local canTutorial = false
  for _, item in ipairs(items) do
    local spread = item:GetComponent(ItemSpread)
    local remainDuration = (1 - spread:GetTimerAmount()) * spread:GetTimerDuration()
    if spread:GetStorageRestNumber(true) == 0 and 10 < remainDuration then
      canTutorial = true
    end
  end
  return canTutorial
end

function Executer:_ExecuteStep1()
  local boardModel = BoardModelHelper.GetActiveModel()
  boardModel:CacheItems({itemId}, CacheItemType.Stack, {
    {}
  }, false)
  boardModel:TryPinOneItemCodeToCacheTop(itemId)
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickCache
  self:_SaveOngoingDatas()
  local delayTime = GM.ConfigModel:UseNewCacheLayout() and 0.5 or 0
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    TutorialHelper.WholeMask()
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
    GM.UIManager:SetEventLock(false)
    local cacheRoot = TutorialHelper.HighlightCacheRoot()
    self.m_gesture = TutorialHelper.TapCustomPos(cacheRoot:GetFlyTargetPosition())
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  end, delayTime)
end

function Executer:_ExecuteStep2()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.TapItem
  self:_SaveOngoingDatas()
  local items = TutorialHelper.GetItems(itemId)
  if 0 < #items then
    local boardView = MainBoardView.GetInstance()
    if boardView ~= nil then
      boardView:UpdateSelectedItem(items[1])
    end
  else
    GM.TutorialModel:ClearTempDatas()
    self:Finish()
    return
  end
  local item = items[1]
  local tapPos = item:GetPosition()
  TutorialHelper.MaskOnItemBoard(tapPos, tapPos)
  TutorialHelper.ShowDialogWithBoardMaskArea(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), tapPos, tapPos)
  self.m_gesture = TutorialHelper.TapOnBoard(tapPos)
  if not self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish()
    return
  end
  GM.TutorialModel:SetForceSourceBoardPosition(tapPos)
  GM.TutorialModel:SetForceTargetBoardPosition(tapPos)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
