ShopCellBundle = setmetatable({}, BaseShopCell)
ShopCellBundle.__index = ShopCellBundle

function ShopCellBundle:UpdateContent(data)
  BaseShopCell.UpdateContent(self, data)
  if self.m_arrItems == nil then
    self.m_arrItems = {
      self.m_itemGo:GetLuaTable()
    }
  end
  local groupIndex
  for i, bundleId in ipairs(data:GetGroupData():GetBundleIds()) do
    if bundleId == data:GetBundleId() then
      self.m_titleText.text = GM.GameTextModel:GetText(BundleDefinition.GetCellTitleText(data:GetGroupData():GetBundleUIType(), i))
    end
  end
  for i = 1, #self.m_data:GetGoods() do
    if not self.m_arrItems[i] then
      self.m_arrItems[i] = Object.Instantiate(self.m_itemGo, self.m_itemRoot):GetLuaTable()
    end
    self.m_arrItems[i].gameObject:SetActive(true)
    self.m_arrItems[i]:Init(self.m_data:GetGoods()[i])
  end
  for i = #self.m_data:GetGoods() + 1, #self.m_arrItems do
    self.m_arrItems[i].gameObject:SetActive(false)
  end
  self.m_IAPButton:Init(GM.InAppPurchaseModel:GetLocalizedPrice(self.m_data:GetPurchaseId()), self.m_callback)
  self.m_discountView:UpdateContent(self.m_data:GetDiscountTag())
end

function ShopCellBundle:GetIAPButton()
  return self.m_IAPButton
end
