local Step = {ClickTask = "1", StartTask = "2"}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:_InitArgs(args)
  self.m_taskIndex = args and args[1] or 1
end

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnGameModeChanged)
  EventDispatcher.AddListener(EEventType.TaskGoClicked, self, self._OnTaskGoClicked)
end

function Executer:TryStartTutorial()
  local boardView = MainBoardView.GetInstance()
  if boardView == nil then
    return
  end
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    if orderArea:IsPlayingOrderAnimation() then
      return
    end
  end
  if GM.TaskManager:CanFinishOngoingTask() and TutorialHelper.CanHighlightBoardTask() then
    self:_ExecuteStep1()
    return true
  end
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickTask
  self:_SaveOngoingDatas()
  self.m_bStep1Executed = true
  TutorialHelper.WholeMask()
  local textKey = "tutorial_finish_quest" .. self.m_taskIndex .. "_1"
  if GM.GameTextModel:HasText(textKey) then
    local anchorPercent = GM.SceneManager:GetGameMode() == EGameMode.Board and 40 or 65
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(textKey), anchorPercent)
  end
  if GM.SceneManager:GetGameMode() == EGameMode.Board then
    local taskCell = TutorialHelper.HighlightBoardTask()
    self.m_gesture = TutorialHelper.TapOnCustomRectTrans(taskCell:GetContentRectTrans())
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  else
    TutorialHelper.HighlightHudButton(ESceneViewHudButtonKey.Task)
    self.m_gesture = TutorialHelper.TapOnHudButton(ESceneViewHudButtonKey.Task, Vector3(-20, 0, 0))
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
    self.m_bHighlightTask = true
  end
end

function Executer:_ClearStep1()
  if self.m_strOngoingDatas == Step.ClickTask and GM.SceneManager:GetGameMode() == EGameMode.Main and self.m_gesture then
    TutorialHelper.HideDialog()
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
    if self.m_bHighlightTask then
      TutorialHelper.DehighlightHudButton(ESceneViewHudButtonKey.Task)
    else
      TutorialHelper.DehighlightBoardTask()
    end
  end
end

function Executer:_OnGameModeChanged()
  self:_ClearStep1()
end

function Executer:_OnOpenView(msg)
  if msg and msg.name == UIPrefabConfigName.TaskWindow and self.m_strOngoingDatas == Step.ClickTask and self.m_bStep1Executed then
    self:_ClearStep1()
    self:_ExecuteStep2()
  end
end

function Executer:_ExecuteStep2()
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  self.m_strOngoingDatas = Step.StartTask
  local window = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.TaskWindow)
  if not window then
    self:Finish(self.m_gesture, self.m_arrow)
    return
  end
  self.m_goBtnTrans = window:GetGoBtnTrans()
  TutorialHelper.WholeMask()
  TutorialHelper.HighlightForUI(self.m_goBtnTrans)
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(self.m_goBtnTrans)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_OnTaskGoClicked()
  if self.m_goBtnTrans then
    TutorialHelper.DehighlightForUI(self.m_goBtnTrans)
    self.m_goBtnTrans = nil
    self:Finish(self.m_gesture, self.m_arrow)
  end
end

return function(tutorialId, strDatas, args)
  local copy = Table.DeepCopy(Executer)
  copy:_InitArgs(args)
  return TutorialExecuter.CreateExecuter(copy, tutorialId, strDatas)
end
