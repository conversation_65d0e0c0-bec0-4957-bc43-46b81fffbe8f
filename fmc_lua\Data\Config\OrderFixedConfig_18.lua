return {
  {
    Id = "180010",
    GroupId = 1,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e1dst_33",
      Count = 1
    }
  },
  {
    Id = "180020",
    GroupId = 1,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180030",
    GroupId = 1,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "180040",
    GroupId = 1,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180050",
    GroupId = 1,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180060",
    GroupId = 1,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e1nutt_7",
      Count = 1
    }
  },
  {
    Id = "180070",
    GroupId = 1,
    ChapterId = 18,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Requirement_2 = {
      Type = "ds_11e2mt_15",
      Count = 1
    }
  },
  {
    Id = "180080",
    GroupId = 2,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "180090",
    GroupId = 2,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e4assort_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180100",
    GroupId = 2,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5maca_7",
      Count = 1
    }
  },
  {
    Id = "180110",
    GroupId = 2,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6bibim_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180120",
    GroupId = 2,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_9e6soup_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180130",
    GroupId = 2,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "180140",
    GroupId = 2,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_14e4sf_30",
      Count = 1
    }
  },
  {
    Id = "180150",
    GroupId = 3,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6rice_21",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180160",
    GroupId = 3,
    ChapterId = 18,
    Requirement_1 = {Type = "ds_fd_14", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180170",
    GroupId = 3,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "180180",
    GroupId = 3,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "180190",
    GroupId = 3,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e1sala_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180200",
    GroupId = 3,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_7",
      Count = 1
    }
  },
  {
    Id = "180210",
    GroupId = 3,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e6stewmt_1",
      Count = 1
    }
  },
  {
    Id = "180220",
    GroupId = 4,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e4assort_17",
      Count = 1
    }
  },
  {
    Id = "180230",
    GroupId = 4,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6rice_22",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180240",
    GroupId = 4,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_13e6bec_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180250",
    GroupId = 4,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_17e1icytre_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1dst_21",
      Count = 1
    }
  },
  {
    Id = "180260",
    GroupId = 4,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e4assort_21",
      Count = 1
    }
  },
  {
    Id = "180270",
    GroupId = 4,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6rice_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180280",
    GroupId = 4,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1icytre_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e4assort_18",
      Count = 1
    }
  },
  {
    Id = "180290",
    GroupId = 5,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "180300",
    GroupId = 5,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_mixdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180310",
    GroupId = 5,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180320",
    GroupId = 5,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "180330",
    GroupId = 5,
    ChapterId = 18,
    Requirement_1 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "180340",
    GroupId = 5,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e4rice_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180350",
    GroupId = 5,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_16e6taji_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6sf_22",
      Count = 1
    }
  },
  {
    Id = "180360",
    GroupId = 6,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6dst_34",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180370",
    GroupId = 6,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_3",
      Count = 1
    }
  },
  {
    Id = "180380",
    GroupId = 6,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180390",
    GroupId = 6,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "180400",
    GroupId = 6,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    }
  },
  {
    Id = "180410",
    GroupId = 6,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_sal_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180420",
    GroupId = 6,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e4rice_27",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_3",
      Count = 1
    }
  },
  {
    Id = "180430",
    GroupId = 7,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_9e6assort_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "180440",
    GroupId = 7,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180450",
    GroupId = 7,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e1kimbap_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180460",
    GroupId = 7,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_25",
      Count = 1
    }
  },
  {
    Id = "180470",
    GroupId = 7,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_13e6appe_5",
      Count = 1
    }
  },
  {
    Id = "180480",
    GroupId = 7,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_14e1cockt_28",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180490",
    GroupId = 7,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e1cockt_31",
      Count = 1
    }
  },
  {
    Id = "180500",
    GroupId = 8,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_10e6rice_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "180510",
    GroupId = 8,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_1",
      Count = 1
    }
  },
  {
    Id = "180520",
    GroupId = 8,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e4assort_20",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1scsau_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180530",
    GroupId = 8,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180540",
    GroupId = 8,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180550",
    GroupId = 8,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "180560",
    GroupId = 8,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e6bibim_7",
      Count = 1
    }
  },
  {
    Id = "180570",
    GroupId = 9,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e1pickled_4",
      Count = 1
    }
  },
  {
    Id = "180580",
    GroupId = 9,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180590",
    GroupId = 9,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_18e4rice_28",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180600",
    GroupId = 9,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_17e1icytre_18",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_5", Count = 1}
  },
  {
    Id = "180610",
    GroupId = 9,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "180620",
    GroupId = 9,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_15e1dst_31",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e3scsau_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180630",
    GroupId = 9,
    ChapterId = 18,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "180640",
    GroupId = 10,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e5semi_16",
      Count = 1
    }
  },
  {
    Id = "180650",
    GroupId = 10,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_15e5maca_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180660",
    GroupId = 10,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_18e1kimbap_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180670",
    GroupId = 10,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "180680",
    GroupId = 10,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6dst_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180690",
    GroupId = 10,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_12",
      Count = 1
    }
  },
  {
    Id = "180700",
    GroupId = 10,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2flb_5",
      Count = 1
    }
  },
  {
    Id = "180710",
    GroupId = 11,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "180720",
    GroupId = 11,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6bibim_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180730",
    GroupId = 11,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "180740",
    GroupId = 11,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e4rice_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180750",
    GroupId = 11,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_13e6bec_1",
      Count = 1
    }
  },
  {
    Id = "180760",
    GroupId = 11,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    }
  },
  {
    Id = "180770",
    GroupId = 11,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e1sala_20",
      Count = 1
    }
  },
  {
    Id = "180780",
    GroupId = 12,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    }
  },
  {
    Id = "180790",
    GroupId = 12,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180800",
    GroupId = 12,
    ChapterId = 18,
    Requirement_1 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "180810",
    GroupId = 12,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_16e1cockt_32",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e4rice_34",
      Count = 1
    }
  },
  {
    Id = "180820",
    GroupId = 12,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_18e1rice_30",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180830",
    GroupId = 12,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_chopfru_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6stewmt_6",
      Count = 1
    }
  },
  {
    Id = "180840",
    GroupId = 12,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_12e6porr_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_19",
      Count = 1
    }
  },
  {
    Id = "180850",
    GroupId = 13,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "180860",
    GroupId = 13,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e6bibim_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180870",
    GroupId = 13,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_mixdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e1rice_33",
      Count = 1
    }
  },
  {
    Id = "180880",
    GroupId = 13,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5mt_12",
      Count = 1
    }
  },
  {
    Id = "180890",
    GroupId = 13,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e1rice_31",
      Count = 1
    }
  },
  {
    Id = "180900",
    GroupId = 13,
    ChapterId = 18,
    Requirement_1 = {Type = "ds_fd_11", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180910",
    GroupId = 13,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4curry_24",
      Count = 1
    }
  },
  {
    Id = "180920",
    GroupId = 14,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e6bibim_4",
      Count = 1
    }
  },
  {
    Id = "180930",
    GroupId = 14,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180940",
    GroupId = 14,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_17e1icytre_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    }
  },
  {
    Id = "180950",
    GroupId = 14,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_2",
      Count = 1
    }
  },
  {
    Id = "180960",
    GroupId = 14,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6soup_19",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "180970",
    GroupId = 14,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "180980",
    GroupId = 14,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e1icytre_4",
      Count = 1
    }
  },
  {
    Id = "180990",
    GroupId = 15,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "181000",
    GroupId = 15,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_17e1hotdrk_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5maca_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181010",
    GroupId = 15,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_16e1cockt_32",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e6rice_21",
      Count = 1
    }
  },
  {
    Id = "181020",
    GroupId = 15,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_14e6soup_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "181030",
    GroupId = 15,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e4assort_17",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "181040",
    GroupId = 15,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181050",
    GroupId = 15,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_11e4tato_20",
      Count = 1
    }
  },
  {
    Id = "181060",
    GroupId = 16,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6nibble_6",
      Count = 1
    }
  },
  {
    Id = "181070",
    GroupId = 16,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "181080",
    GroupId = 16,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6bibim_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181090",
    GroupId = 16,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6nibble_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e6rice_20",
      Count = 1
    }
  },
  {
    Id = "181100",
    GroupId = 16,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_13e2sf_27",
      Count = 1
    }
  },
  {
    Id = "181110",
    GroupId = 16,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181120",
    GroupId = 16,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_14e1dst_24",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5mt_12",
      Count = 1
    }
  },
  {
    Id = "181130",
    GroupId = 17,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "181140",
    GroupId = 17,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6dst_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181150",
    GroupId = 17,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    }
  },
  {
    Id = "181160",
    GroupId = 17,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "181170",
    GroupId = 17,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_10e6rice_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6porr_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181180",
    GroupId = 17,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_18e4appe_9",
      Count = 1
    }
  },
  {
    Id = "181190",
    GroupId = 17,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e1kimbap_5",
      Count = 1
    }
  },
  {
    Id = "181200",
    GroupId = 18,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_8",
      Count = 1
    }
  },
  {
    Id = "181210",
    GroupId = 18,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e1pickled_1",
      Count = 1
    }
  },
  {
    Id = "181220",
    GroupId = 18,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_13e6bec_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181230",
    GroupId = 18,
    ChapterId = 18,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_2",
      Count = 1
    }
  },
  {
    Id = "181240",
    GroupId = 18,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e4rice_34",
      Count = 1
    }
  },
  {
    Id = "181250",
    GroupId = 18,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_9e5saus_25",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4curry_24",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181260",
    GroupId = 18,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_15e5fd_33",
      Count = 1
    }
  },
  {
    Id = "181270",
    GroupId = 19,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "181280",
    GroupId = 19,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "181290",
    GroupId = 19,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Requirement_2 = {Type = "ds_7e5mt_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181300",
    GroupId = 19,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e1appe_4",
      Count = 1
    }
  },
  {
    Id = "181310",
    GroupId = 19,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_18e6nibble_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181320",
    GroupId = 19,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5fd_37",
      Count = 1
    }
  },
  {
    Id = "181330",
    GroupId = 19,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_15",
      Count = 1
    }
  },
  {
    Id = "181340",
    GroupId = 20,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4friedmt_19",
      Count = 1
    }
  },
  {
    Id = "181350",
    GroupId = 20,
    ChapterId = 18,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181360",
    GroupId = 20,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e6dst_34",
      Count = 1
    }
  },
  {
    Id = "181370",
    GroupId = 20,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_mixdrk_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    }
  },
  {
    Id = "181380",
    GroupId = 20,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6bibim_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181390",
    GroupId = 20,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_14", Count = 1}
  },
  {
    Id = "181400",
    GroupId = 20,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e4assort_19",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_4",
      Count = 1
    }
  },
  {
    Id = "181410",
    GroupId = 21,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_2",
      Count = 1
    }
  },
  {
    Id = "181420",
    GroupId = 21,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_mixdrk_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181430",
    GroupId = 21,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e1pickled_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e1kimbap_2",
      Count = 1
    }
  },
  {
    Id = "181440",
    GroupId = 21,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "181450",
    GroupId = 21,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e1rice_30",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181460",
    GroupId = 21,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_13e6bec_4",
      Count = 1
    }
  },
  {
    Id = "181470",
    GroupId = 21,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e1cockt_28",
      Count = 1
    }
  },
  {
    Id = "181480",
    GroupId = 22,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_15e1dst_26",
      Count = 1
    }
  },
  {
    Id = "181490",
    GroupId = 22,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5maca_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181500",
    GroupId = 22,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_11e4tato_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "181510",
    GroupId = 22,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_13e6bec_5",
      Count = 1
    }
  },
  {
    Id = "181520",
    GroupId = 22,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6rice_19",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e6bibim_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181530",
    GroupId = 22,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "181540",
    GroupId = 22,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e6bibim_7",
      Count = 1
    }
  },
  {
    Id = "181550",
    GroupId = 23,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_17e1hotdrk_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e1appe_4",
      Count = 1
    }
  },
  {
    Id = "181560",
    GroupId = 23,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_18e1kimbap_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181570",
    GroupId = 23,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_13e6bec_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "181580",
    GroupId = 23,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6bibim_6",
      Count = 1
    }
  },
  {
    Id = "181590",
    GroupId = 23,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_16e6taji_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181600",
    GroupId = 23,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "181610",
    GroupId = 23,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e1rice_29",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_16",
      Count = 1
    }
  },
  {
    Id = "181620",
    GroupId = 24,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6rice_23",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "181630",
    GroupId = 24,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6rice_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181640",
    GroupId = 24,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_10e6rice_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "181650",
    GroupId = 24,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e1pickled_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6sf_29",
      Count = 1
    }
  },
  {
    Id = "181660",
    GroupId = 24,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e1rice_30",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181670",
    GroupId = 24,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e2appe_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e1kimbap_4",
      Count = 1
    }
  },
  {
    Id = "181680",
    GroupId = 24,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "181690",
    GroupId = 25,
    ChapterId = 18,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "181700",
    GroupId = 25,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_17e1icytre_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181710",
    GroupId = 25,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "181720",
    GroupId = 25,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_12e1nutt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e1rice_32",
      Count = 1
    }
  },
  {
    Id = "181730",
    GroupId = 25,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_16e4friedmt_23",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181740",
    GroupId = 25,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {Type = "ds_e6dst_2", Count = 1}
  },
  {
    Id = "181750",
    GroupId = 25,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4assort_14",
      Count = 1
    }
  },
  {
    Id = "181760",
    GroupId = 26,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_8e6nibble_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "181770",
    GroupId = 26,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1nutt_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181780",
    GroupId = 26,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_1",
      Count = 1
    }
  },
  {
    Id = "181790",
    GroupId = 26,
    ChapterId = 18,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_18e1kimbap_2",
      Count = 1
    }
  },
  {
    Id = "181800",
    GroupId = 26,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e1pickled_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181810",
    GroupId = 26,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "181820",
    GroupId = 26,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4sf_30",
      Count = 1
    }
  },
  {
    Id = "181830",
    GroupId = 27,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_18e4rice_25",
      Count = 1
    }
  },
  {
    Id = "181840",
    GroupId = 27,
    ChapterId = 18,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181850",
    GroupId = 27,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e1rice_31",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "181860",
    GroupId = 27,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "181870",
    GroupId = 27,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e1sala_22",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181880",
    GroupId = 27,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "181890",
    GroupId = 27,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_16e1cockt_34",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1nutt_5",
      Count = 1
    }
  },
  {
    Id = "181900",
    GroupId = 28,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "181910",
    GroupId = 28,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e2appe_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181920",
    GroupId = 28,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_13e5mt_12",
      Count = 1
    }
  },
  {
    Id = "181930",
    GroupId = 28,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_16e1cockt_32",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "181940",
    GroupId = 28,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e4appe_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181950",
    GroupId = 28,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_1_10", Count = 1},
    Requirement_2 = {
      Type = "ds_13e4appe_3",
      Count = 1
    }
  },
  {
    Id = "181960",
    GroupId = 28,
    ChapterId = 18,
    Requirement_1 = {Type = "it_2_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5fd_41",
      Count = 1
    }
  },
  {
    Id = "181970",
    GroupId = 29,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_11e6nibble_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5maca_6",
      Count = 1
    }
  },
  {
    Id = "181980",
    GroupId = 29,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_15e5fd_28",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "181990",
    GroupId = 29,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e6dst_34",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "182000",
    GroupId = 29,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e1sala_23",
      Count = 1
    }
  },
  {
    Id = "182010",
    GroupId = 29,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_17e1mdrk_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "182020",
    GroupId = 29,
    ChapterId = 18,
    Requirement_1 = {Type = "it_7_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6curry_26",
      Count = 1
    }
  },
  {
    Id = "182030",
    GroupId = 29,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_15e5fd_33",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "182040",
    GroupId = 30,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e4rice_27",
      Count = 1
    }
  },
  {
    Id = "182050",
    GroupId = 30,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "182060",
    GroupId = 30,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_18e4rice_25",
      Count = 1
    }
  },
  {
    Id = "182070",
    GroupId = 30,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "182080",
    GroupId = 30,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "182090",
    GroupId = 30,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    }
  },
  {
    Id = "182100",
    GroupId = 30,
    ChapterId = 18,
    Requirement_1 = {
      Type = "ds_18e4rice_24",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e1sala_21",
      Count = 1
    }
  }
}
