DailyTaskTaskCell = {}
DailyTaskTaskCell.__index = DailyTaskTaskCell
local DailyTaskType2ImgSpriteName = {}
local DailyTaskType2TextKey = {
  [DailyTaskType.UseEnergy] = "daily_task_type_energy",
  [DailyTaskType.CollectGold] = "daily_task_type_gold",
  [DailyTaskType.ServeCustomer] = "daily_task_type_order",
  [DailyTaskType.SpendGem] = "daily_task_type_gem"
}

function DailyTaskTaskCell:Init(data, playFinishAnimation)
  self.m_model = GM.ActivityManager:GetModel(ActivityType.DailyTask)
  self.m_data = data
  self.m_index = self.m_data:GetIndex()
  self.m_type = data:GetType()
  SpriteUtil.SetImage(self.m_titleIconImg, DailyTaskType2ImgSpriteName[self.m_type], true)
  self.m_titleText.text = GM.GameTextModel:GetText(DailyTaskType2TextKey[self.m_type])
  self.m_completeTitleText.text = GM.GameTextModel:GetText(DailyTaskType2TextKey[self.m_type])
  self.m_rewardItem:Init(data:GetRewards()[1])
  self.m_finishScore = data:GetScore()
  if playFinishAnimation then
    self:UpdateContent(playFinishAnimation)
    self:PlayFinishAnimation()
  else
    self:UpdateContent()
  end
  self.m_testButtonGo:SetActive(GameConfig.IsTestMode())
end

function DailyTaskTaskCell:UpdateContent(showFull)
  local curScore = self.m_model:GetScore(self.m_type)
  self.m_slider.value = showFull and 1 or math.min(1, curScore / self.m_finishScore)
  self.m_sliderText.text = (showFull and self.m_finishScore or math.min(curScore, self.m_finishScore)) .. "/" .. self.m_finishScore
  UIUtil.SetActive(self.m_completeContentGo, showFull or self.m_model:HasFinishedTask(self.m_index))
  UIUtil.SetActive(self.m_normalContentGo, showFull or not self.m_model:HasFinishedTask(self.m_index))
end

DailyTaskTaskCell.FinishAnimationDuration = 3.6

function DailyTaskTaskCell:PlayFinishAnimation()
  local worldPosition = self.m_rewardItem:GetIconRectTrans().position
  local rewards = self.m_data:GetRewards()
  self.m_completeCanvasGroup.alpha = 0
  self.m_canvasGroup.alpha = 0
  self.transform.localScale = V3One * 0.4
  local seq = DOTween.Sequence()
  seq:AppendInterval(0.5)
  seq:Append(self.transform:DOScale(1, 0.4):SetEase(Ease.OutBack))
  seq:Join(self.m_canvasGroup:DOFade(1, 0.4))
  seq:AppendInterval(0.8)
  seq:AppendCallback(function()
    RewardApi.AcquireRewardsInView(rewards, {
      arrWorldPos = Table.ListRep(worldPosition, #rewards),
      noDelayTime = true,
      alwaysInMiddle = true
    })
    self.m_rewardItem.gameObject:SetActive(false)
  end)
  seq:Append(self.m_completeCanvasGroup:DOFade(1, 0.4))
  seq:AppendInterval(1.2)
  seq:Append(self.transform:DOScale(0, 0.4):SetEase(Ease.OutQuart))
  seq:Join(self.m_canvasGroup:DOFade(0, 0.4))
end

function DailyTaskTaskCell:TestFinish()
  if not GameConfig.IsTestMode() or self.m_model:GetState() ~= ActivityState.Started or self.m_model:HasFinishedTask(self.m_index) then
    return
  end
  self.m_model:_FinishTask(self.m_index)
end
