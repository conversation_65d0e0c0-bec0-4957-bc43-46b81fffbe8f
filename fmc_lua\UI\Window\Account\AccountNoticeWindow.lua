AccountNoticeWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.AccountNoticeWindow,
  canCloseByChangeGameMode = false
}, TwoButtonWindow)
AccountNoticeWindow.__index = AccountNoticeWindow
EAccountNotice = {
  LowVersion = 1,
  AccountDeleted = 2,
  Cheat = 3
}

function AccountNoticeWindow:Init(info)
  if info.type == EAccountNotice.LowVersion then
    TwoButtonWindow.Init(self, "sync_ver_error_title", "sync_ver_error_desc", "common_button_ok", "common_button_ok", nil, function(tbWindow)
      tbWindow:Close()
      GM.UpdateHintModel:TryGoToStorePage()
    end, true)
    self.m_redButtonTb.gameObject:SetActive(false)
    UIUtil.SetLocalPosition(self.m_greenButtonTrans, 0)
    UIUtil.SetSizeDelta(self.m_greenButtonTrans, 474)
  elseif info.type == EAccountNotice.AccountDeleted then
    local deleteTime = info.msg and info.msg // 1000 or GM.GameModel:GetServerTime()
    local deleteDate = TimeUtil.ToDate(deleteTime, ETimeFormat.YMD)
    local descText = GM.GameTextModel:GetText("clearAll_remind_desc", GM.UserModel:GetDisplayUserId(), deleteDate)
    TwoButtonWindow.Init(self, "clearAll_remind_title", descText, "clearAll_remind_contact_btn", "clearAll_remind_ok_btn", function()
      GM.SDKHelper:OpenCustomerCenter()
    end, function(tbWindow)
      tbWindow:Close()
      GM.SDKHelper:LogOut()
      GM.SyncModel:ClearAllData()
      GM:RestartGame(nil, EBIProjectType.RestartGameAction.DeleteAccountAlready)
    end, false)
  elseif info.type == EAccountNotice.Cheat then
    TwoButtonWindow.Init(self, "cheat_warn_title", "cheat_warn_desc", "common_exit_button", "settings_contact_btn", function()
      PlatformInterface.ExitGame()
    end, function()
      GM.SDKHelper:OpenCustomerCenter()
    end, false)
  else
    Log.Assert(false, "unknown type")
  end
end
