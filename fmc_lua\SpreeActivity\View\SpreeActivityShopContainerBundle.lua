SpreeActivityShopContainerBundle = setmetatable({}, BaseShopListContainer)
SpreeActivityShopContainerBundle.__index = SpreeActivityShopContainerBundle

function SpreeActivityShopContainerBundle:Init(activityType, data)
  self.m_activityType = activityType
  self.m_data = data
  local activityDefinition = SpreeActivityDefinition[activityType]
  BaseShopListContainer.Init(self, activityDefinition.BundleShopType, false, 1)
end

function SpreeActivityShopContainerBundle:_OnShopRefreshed()
end

function SpreeActivityShopContainerBundle:_GetCellData()
  local data = Table.ShallowCopy(self.m_data)
  data.ActivityType = self.m_activityType
  return {data}
end

function SpreeActivityShopContainerBundle:_OnCellClicked(cell)
  local data = self.m_data
  local shopModel = GM.ActivityManager:GetModel(self.m_activityType):GetShopModel()
  local callback = function()
    GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, data.Rewards, "rewards_window_title_shop", false)
  end
  shopModel:BuyBundle(data, callback)
end

function SpreeActivityShopContainerBundle:_InitContainer()
  BaseShopListContainer._InitContainer(self)
  self.m_titleText.text = GM.GameTextModel:GetText(self.m_data.Name)
end

function SpreeActivityShopContainerBundle:SetData(data)
  self.m_data = data
  self:_UpdateContent()
  self.m_titleText.text = GM.GameTextModel:GetText(self.m_data.Name)
end
