SurpriseChestView = {}
SurpriseChestView.__index = SurpriseChestView

function SurpriseChestView:Init(model, chestData, orderCell, orderArea)
  if model == nil or chestData == nil or orderCell == nil or orderArea == nil then
    return
  end
  self.m_model = model
  self.m_chestData = chestData
  self.m_orderCell = orderCell
  self.m_orderArea = orderArea
  self.m_triggerEndTime = self.m_model:GetTriggerEndTime()
  local imgKey = chestData:GetUICode() or ImageFileConfigName.box_blue
  SpriteUtil.SetImage(self.m_icon, imgKey, true)
  self:UpdatePerSecond()
end

function SurpriseChestView:OnDestroy()
  Scheduler.UnscheduleTarget(self)
end

function SurpriseChestView:UpdatePerSecond()
  if self.m_triggerEndTime ~= nil then
    local delta = self.m_triggerEndTime - GM.GameModel:GetServerTime()
    if 0 <= delta then
      self.m_timeText.text = TimeUtil.ToMSOrHMS(delta)
    elseif not self.m_bPlayingAnim then
      self.m_animator:SetTrigger("Disappear")
      self.m_orderArea:HideSurpriseChestRewardBubble()
      DelayExecuteFuncInView(function()
        self.m_orderCell:UpdateSurpriseChestState()
      end, 1.5, self)
    end
  end
end

function SurpriseChestView:CanPlayRewardAnim()
  return self.m_model ~= nil and self.m_chestData ~= nil
end

function SurpriseChestView:PlayRewardAnim()
  if not self:CanPlayRewardAnim() or self.m_orderCell == nil then
    return
  end
  local order = self.m_orderCell:GetOrder()
  local rewards = self.m_model:GetAcquiredRewardsByOrderId(order and order:GetId())
  self.m_bPlayingAnim = true
  if not Table.IsEmpty(rewards) then
    local cam = GM.ModeViewController:GetBoardInfo()
    local screenPos = cam:WorldToScreenPoint(self.gameObject.transform.position)
    local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPos)
    DelayExecuteFuncInView(function()
      RewardApi.AcquireRewardsInView(rewards, {
        noDelayTime = false,
        eventLock = false,
        arrWorldPos = {uiWorldPosition},
        spriteScale = 0,
        simpleCacheRoot = true
      })
    end, 0.4, self, false)
    self.m_animator:SetTrigger("Open")
  end
end

function SurpriseChestView:GetRewardAnimTime()
  return 1
end

function SurpriseChestView:OnClicked()
  if self.m_orderArea ~= nil then
    self.m_orderArea:ShowSurpriseChestRewardBubble(self.m_model:GetType(), self.m_model:GetActivityDefinition(), self.m_chestData:GetValidRewards(), self:_GetRewardBubbleViewData())
  end
end

function SurpriseChestView:_GetRewardBubbleViewData()
  local cam = GM.ModeViewController:GetBoardInfo()
  local screenPos = cam:WorldToScreenPoint(self.gameObject.transform.position)
  local bLeft = screenPos.x <= Screen.width / 2
  local localPos = bLeft and Vector3(-38, -68, 0) or Vector3(-420, -68, 0)
  local pos = self.transform:TransformPoint(localPos)
  return {bLeft = bLeft, pos = pos}
end
