GM.DBTableManager = DBTableManager
GM.ConfigModel = ConfigModel
GM.CDNResourceManager = CDNResourceManager
GM.HttpManager = HttpManager
GM.SsoManager = SsoManager
GM.BIManager = BIManager
GM.ResourceLoader = ResourceLoader
GM.GameTextModel = GameTextModel
GM.OpenFunctionModel = OpenFunctionModel
GM.SystemConfigModel = SystemConfigModel
GM.GameModel = GameModel
GM.SyncModel = SyncModel
GM.SceneManager = SceneManager
GM.DataResource = DataResource
GM.PropertyDataManager = PropertyDataManager
GM.UserModel = UserModel
GM.UserProfileModel = UserProfileModel
GM.MiscModel = MiscModel
GM.EnergyModel = EnergyModel
GM.AudioModel = AudioModel
GM.SDKHelper = SDKHelper
GM.InAppPurchaseModel = InAppPurchaseModel
GM.InGameProfiler = InGameProfiler
GM.LevelModel = LevelModel
GM.TaskManager = TaskManager
GM.TaskDataModel = TaskDataModel
GM.TimelineDataModel = TimelineDataModel
GM.TimelineManager = TimelineManager
GM.ItemDataModel = ItemDataModel
GM.MainBoardModel = MainBoardModel
GM.ShopDataModel = ShopDataModel
GM.ShopModel = ShopModel
GM.TutorialModel = TutorialModel
GM.OperManager = OperManager
GM.OperBIManager = OperBIManager
GM.BundleManager = BundleManager
GM.UpdateHintModel = UpdateHintModel
GM.CDNResourceManager = CDNResourceManager
GM.ActivityManager = ActivityManager
GM.RateModel = RateModel
GM.NotificationModel = NotificationModel
GM.PushTokenModel = PushTokenModel
GM.AccountManager = AccountManager
GM.DownloadManager = DownloadManager
GM.CrossPromotionModel = CrossPromotionModel
GM.MoreGameModel = MoreGameModel
GM.RewardModel = RewardModel
GM.HeartBeatManager = HeartBeatManager
GM.NoticeModel = NoticeModel
GM.UIManager = UIManager
GM.TestModel = TestModel
GM.TestAutoRunModel = TestAutoRunModel
GM.ModeViewController = ModeViewController
GM.StoryDataModel = StoryDataModel
GM.ReturnUserModel = ReturnUserModel
GM.ChapterManager = ChapterManager
GM.ChapterDataModel = ChapterDataModel
GM.TimelineLayer = TimelineLayer
GM.EnergyBoostModel = EnergyBoostModel
GM.ItemRecycleModel = ItemRecycleModel
StringUtil = StringUtil
