DBTableManager = {}
DBTableManager.__index = DBTableManager
local transactionTime = 1

function DBTableManager:_CreateDBTable(tableConfig)
  local instance = setmetatable({}, DBTable)
  instance:Init(tableConfig)
  return instance
end

function DBTableManager:Init()
  self.m_bHealth = true
  self.m_dbTables = {}
  for _, config in pairs(EDBTableConfigs) do
    self.m_dbTables[config.name] = self:_CreateDBTable(config)
  end
  self:CreateFile()
  if GM.DatabaseModel.hasDB then
    self.m_bHealth = self:_LoadTables(true)
    if self.m_bHealth then
      self:TrySaveAll(true)
    end
    GM.DatabaseModel:DeleteDatabase()
  elseif not self.dataBase:IsEmpty() then
    self.m_bHealth = self:_LoadTables()
  end
  if not self.m_bHealth then
    self.m_bLogFailedInit = true
  end
  self:_ResetInternal()
end

function DBTableManager:CreateFile()
  self.dataBase = EncryptedPersistentDict.Create("DBTableManager")
end

function DBTableManager:LateInit()
  if self.m_bLogFailedInit then
    self.m_bLogFailedInit = nil
    GM.BIManager:LogProject(EBIProjectType.SQLite, "Failed to load all tables")
  end
end

function DBTableManager:LateUpdate()
  if transactionTime ~= 0 and self.m_needSaveLateUpdate ~= true then
    return
  end
  self:SaveAllAndCheck()
end

function DBTableManager:SaveAllAndCheck()
  if self:TrySaveAll() then
    return
  end
  if self:HealthCheck() then
    self.m_bHealth = false
    GM.SyncModel:CheckUpload()
  end
end

function DBTableManager:UpdatePerSecond()
  if transactionTime == 0 or self.m_saveInterval == nil then
    return
  end
  self.m_saveInterval = self.m_saveInterval + 1
  if self.m_saveInterval > transactionTime then
    self:SaveAllAndCheck()
  end
end

function DBTableManager:_ResetInternal()
  self.m_saveInterval = 0
end

function DBTableManager:SaveAllWhenLateUpdate()
  self.m_needSaveLateUpdate = true
end

function DBTableManager:Destroy()
  if self:TrySaveAll() then
    return
  end
  GM.SyncModel:CheckUpload()
end

function DBTableManager:TrySaveAll(isAlwaysSerialize)
  if GM.destroying and (GM.restartGameAction == EBIProjectType.RestartGameAction.UseServerData or GM.restartGameAction == EBIProjectType.RestartGameAction.DeleteAccountAlready or GM.restartGameAction == EBIProjectType.RestartGameAction.DeleteAccountSuccess or GM.skipDBSerialize) then
    return true
  end
  self:FlushTables(isAlwaysSerialize)
  self.dataBase:Serialize()
  self.m_bHealth = true
  self:_ResetInternal()
  self.m_needSaveLateUpdate = false
  return true
end

function DBTableManager:_LoadTables(isFromSqlite)
  if isFromSqlite then
    if not GM.DatabaseModel:CheckDatabase() then
      return false
    end
    if not self:CreateTables() then
      Log.Info("数据库创建失败，清空本地所有数据表。")
      GM.DatabaseModel:DeleteDatabase()
      return false
    end
    for _, dbTable in pairs(self.m_dbTables) do
      if not dbTable:LoadData() then
        self:ClearTables()
        GM.DatabaseModel:DeleteDatabase()
        return false
      end
    end
    return true
  end
  local tbContent = self.dataBase:GetContent()
  local dbTable
  for dbName, value in pairs(tbContent) do
    dbTable = self.m_dbTables[dbName]
    if dbTable == nil then
      return false
    end
    dbTable:FromJson(value)
  end
  return true
end

function DBTableManager:FlushTables(isAlwaysSerialize)
  for _, dbTable in pairs(self.m_dbTables) do
    dbTable:CheckContent(isAlwaysSerialize)
  end
end

function DBTableManager:GetTable(tableConfig)
  self.m_dbTables = self.m_dbTables or {}
  local dbTable = self.m_dbTables[tableConfig.name]
  if dbTable == nil then
    Log.Warning("Please create table " .. tableConfig.name .. "with DatabaseModel")
    dbTable:FromJson("")
    self.m_dbTables[tableConfig.name] = dbTable
  end
  return dbTable
end

function DBTableManager:Clear()
  self.m_dbTables = self.m_dbTables or {}
  local testDBName = EDBTableConfigs.Test and EDBTableConfigs.Test.name
  for _, dbTable in pairs(self.m_dbTables) do
    if dbTable.tableName ~= testDBName then
      dbTable:Clear()
    end
  end
end

function DBTableManager:HealthCheck()
  return self.m_bHealth
end

function DBTableManager:ClearTables()
  for _, dbTable in pairs(self.m_dbTables) do
    dbTable:ClearContent()
  end
end

function DBTableManager:ConfirmDataSynced()
  for _, dbTable in pairs(self.m_dbTables) do
    dbTable:ConfirmDataSynced()
  end
end

function DBTableManager:SetData(dbTable, isAlwaysSerialize)
  self.dataBase:Set(dbTable.tableName, dbTable:ToJson(isAlwaysSerialize), true)
end

function DBTableManager:IsCracked()
  return self.dataBase:IsCracked()
end

function DBTableManager:GetFileSize()
  if self.dataBase then
    return self.dataBase:GetFileSize()
  else
    return 0
  end
end

function DBTableManager:CreateTables()
  for _, dbTable in pairs(self.m_dbTables) do
    if not dbTable:CreateTable() then
      Log.Info(_.name .. " 数据表创建失败。")
      return false
    end
  end
  return true
end
