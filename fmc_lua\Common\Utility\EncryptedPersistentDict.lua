EncryptedPersistentDict = setmetatable({}, SimpleStringDict)
EncryptedPersistentDict.__index = EncryptedPersistentDict
local CSEncryptedPersistentDict = CS.EncryptedPersistentDict

function EncryptedPersistentDict.Create(fileName)
  local instance = setmetatable({}, EncryptedPersistentDict)
  instance:_Init(fileName)
  return instance
end

function EncryptedPersistentDict:_Init(fileName)
  self.fileName = fileName
  self.m_dataDict = CSEncryptedPersistentDict.Create(fileName)
  self.m_mapCacheValue = {}
end
