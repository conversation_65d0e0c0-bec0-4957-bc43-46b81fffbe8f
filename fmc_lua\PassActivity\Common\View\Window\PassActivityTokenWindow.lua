PassActivityTokenWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, PassActivityBaseWindow)
PassActivityTokenWindow.__index = PassActivityTokenWindow

function PassActivityTokenWindow:Init(activityType, tokenCount, tokenRatio)
  self.m_tokenRatio = tokenRatio or 1
  self.m_tokenCount = tokenCount
  PassActivityBaseWindow.Init(self, activityType, false)
  self.m_numberText.text = tokenCount
  self:TryPlayUpgradeAnim()
end

function PassActivityTokenWindow:OnDestroy()
  PassActivityBaseWindow.OnDestroy(self)
  if self.m_upgradeSeq ~= nil then
    self.m_upgradeSeq:Kill()
    self.m_upgradeSeq = nil
  end
end

function PassActivityTokenWindow:TryPlayUpgradeAnim()
  UIUtil.SetLocalScale(self.m_numberText2.transform, 0, 0)
  self.m_ratioCanvasGroup.alpha = 0
  UIUtil.SetActive(self.m_flagSpine.gameObject, false)
  self.m_flagSpine:Initialize(false)
  if self.m_tokenRatio <= 1 then
    return
  end
  self.m_numberText2.text = math.ceil(self.m_tokenCount * self.m_tokenRatio)
  self.m_ratioText.text = "x" .. self.m_tokenRatio
  UIUtil.SetActive(self.m_flagSpine.gameObject, true)
  self.m_flagSpine.AnimationState:SetAnimation(0, "appear", false)
  local seq = DOTween.Sequence()
  seq:Insert(0.15, self.m_ratioCanvasGroup:DOFade(1, 0.2):SetEase(Ease.OutSine))
  seq:Insert(0.7, self.m_numberText.transform:DOScale(Vector3(0, 0, 1), 0.1):SetEase(Ease.InOutSine))
  seq:Insert(0.8, self.m_numberText2.transform:DOScale(Vector3(1.2, 1.2, 1), 0.2):SetEase(Ease.InSine))
  seq:Insert(0.9, self.m_numberText2.transform:DOScale(Vector3(1, 1, 1), 0.1):SetEase(Ease.OutSine))
  self.m_upgradeSeq = seq
end

function PassActivityTokenWindow:OnCloseBtnClick()
  local mainWindow = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
  if mainWindow ~= nil and self.m_tokenCount ~= 0 and not self.m_bClosed then
    local startPosition = self.m_tokenTransform.position
    mainWindow:PlayLevelProgressAnimation(startPosition)
    self:Close()
    self.m_bClosed = true
    return
  end
  self:Continue()
end

function PassActivityTokenWindow:Continue()
  self:Close()
  PassActivityViewHelper.ContinueViewChain(self.m_activityType)
end
