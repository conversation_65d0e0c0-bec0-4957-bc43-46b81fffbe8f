LayEffectMask = {}
LayEffectMask.__index = LayEffectMask

function LayEffectMask:Init(spriteRenderer, animType, callback)
  local spriteSize = spriteRenderer.localBounds.size
  local duration = 1
  self.m_effectMaskSprite.sprite = spriteRenderer.sprite
  local seq = DOTween.Sequence()
  spriteRenderer.maskInteraction = SpriteMaskInteraction.VisibleInsideMask
  if string.find(animType, "T") or string.find(animType, "B") then
    local startPos = string.find(animType, "T") and spriteSize.y / 2 or -spriteSize.y / 2
    self.m_spriteMaskTrans.localPosition = Vector3(0, startPos, 0)
    seq:Append(self.m_spriteMaskTrans:DOLocalMoveY(0, duration))
    self.m_spriteMaskTrans.localScale = Vector3(spriteSize.x / 100, 0, 1)
    seq:Join(self.m_spriteMaskTrans:DOScaleY(spriteSize.y / 100, duration))
    self.m_effectTrans.localEulerAngles = V3Zero
    self.m_effectTrans.localPosition = Vector3(0, startPos, 0)
    seq:Join(self.m_effectTrans:DOLocalMoveY(-startPos, duration))
  else
    local startPos = string.find(animType, "R") and spriteSize.x / 2 or -spriteSize.x / 2
    self.m_spriteMaskTrans.localPosition = Vector3(startPos, 0, 0)
    seq:Append(self.m_spriteMaskTrans:DOLocalMoveX(0, duration))
    self.m_spriteMaskTrans.localScale = Vector3(0, spriteSize.y / 100, 1)
    seq:Join(self.m_spriteMaskTrans:DOScaleX(spriteSize.x / 100, duration))
    self.m_effectTrans.localEulerAngles = Vector3(0, 0, 90)
    self.m_effectTrans.localPosition = Vector3(startPos, 0, 0)
    seq:Join(self.m_effectTrans:DOLocalMoveX(-startPos, duration))
  end
  self.m_particleSystem:Play()
  seq:AppendCallback(function()
    spriteRenderer.maskInteraction = SpriteMaskInteraction.None
    self.gameObject:RemoveSelf()
    if callback then
      callback()
    end
  end)
end
