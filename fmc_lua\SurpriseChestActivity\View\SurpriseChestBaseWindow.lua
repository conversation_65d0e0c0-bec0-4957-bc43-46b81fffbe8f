SurpriseChestBaseWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BaseWindow)
SurpriseChestBaseWindow.__index = SurpriseChestBaseWindow

function SurpriseChestBaseWindow:Init(activityType, bUserClick)
  self.m_activityDefinition = SurpriseChestActivityDefinition[activityType]
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_activityType = activityType
  if self.m_activityDefinition == nil or self.m_model == nil then
    self:Close()
  end
  self.m_model:SetWindowOpened()
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  self:LogWindowAction(EBIType.UIActionType.Open, {
    bUserClick and EBIReferType.UserClick or EBIReferType.AutoPopup
  })
end

function SurpriseChestBaseWindow:_OnStateChanged()
  self:Close()
end
