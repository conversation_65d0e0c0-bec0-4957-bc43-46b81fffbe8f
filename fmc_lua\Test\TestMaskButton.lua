TestMaskButton = {}
TestMaskButton.__index = TestMaskButton
local ETimeScale = {
  1,
  2,
  3,
  5,
  10,
  0.1,
  0.5
}

function TestMaskButton:Awake()
  function self.m_eventTrigger.OnLuaPointerDown(eventData)
    self:_OnPointerDown(eventData)
  end
  
  function self.m_eventTrigger.OnLuaDrag(eventData)
    self:_OnDrag(eventData)
  end
  
  self.lockColor = CSColor(1, 0.19607843137254902, 0.19607843137254902, 0.7843137254901961)
  self.normalColor = CSColor(0.19607843137254902, 0.19607843137254902, 0.19607843137254902, 0.7843137254901961)
  self.m_errorNotifierGo:SetActive(false)
  local reporterGo = GameObject.Find("Reporter")
  if reporterGo ~= nil then
    local receiver = reporterGo:GetComponent(typeof(CS.ReporterMessageReceiver))
    if receiver ~= nil then
      receiver.errorNotifier = self.m_errorNotifierGo
    end
  end
  local idx = Table.GetKey(ETimeScale, CS.UnityEngine.Time.timeScale) or 1
  self.m_timeScaleText.text = ETimeScale[idx] .. "x"
  self.m_TimeScaleIdx = idx
end

function TestMaskButton:Update()
  local isEventLock = GM.UIManager:IsEventLock()
  self.m_image.color = isEventLock and self.lockColor or self.normalColor
end

function TestMaskButton:OnClick()
  if self.m_bMoved then
    self.m_bMoved = false
    return
  end
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.TestWindow) then
    return
  end
  GM.UIManager:OpenView(UIPrefabConfigName.TestWindow)
end

function TestMaskButton:_OnPointerDown(eventData)
  self.m_bMoved = false
  self.m_offset = GM.UIManager.camera:ScreenToWorldPoint(Vector3(eventData.position.x, eventData.position.y, GM.UIManager.camera.farClipPlane)) - self.gameObject.transform.position
end

function TestMaskButton:_OnDrag(eventData)
  if not self.m_offset then
    return
  end
  self.m_bMoved = true
  self.gameObject.transform.position = GM.UIManager.camera:ScreenToWorldPoint(Vector3(eventData.position.x, eventData.position.y, GM.UIManager.camera.farClipPlane)) - self.m_offset
end

function TestMaskButton:OnMergeAllClicked()
  local boardModel = BoardModelHelper.GetActiveModel()
  if boardModel ~= nil then
    boardModel:MergeAll()
  end
end

function TestMaskButton:OnChangeTimeScale()
  if self.m_TimeScaleIdx == #ETimeScale then
    self.m_TimeScaleIdx = 1
  else
    self.m_TimeScaleIdx = self.m_TimeScaleIdx + 1
  end
  local scale = ETimeScale[self.m_TimeScaleIdx]
  self.m_timeScaleText.text = scale .. "x"
  CS.UnityEngine.Time.timeScale = scale
end
