AlbumRecyleFlyBox = {}
AlbumRecyleFlyBox.__index = AlbumRecyleFlyBox

function AlbumRecyleFlyBox:Init(spriteImg, index, sortingOrder)
  self.m_maskImg.sprite = spriteImg
  self.m_spriteImage.sprite = spriteImg
  SpriteUtil.SetNativeSize(self.m_maskImg)
  SpriteUtil.SetNativeSize(self.m_spriteImage)
  self.m_Album_madaFlyBoxCanv.sortingOrder = sortingOrder
  UIUtil.SetActive(self.m_box1Light.gameObject, false)
  UIUtil.SetActive(self.m_box2Light.gameObject, false)
  UIUtil.SetActive(self.m_box3Light.gameObject, false)
  UIUtil.SetActive(self["m_box" .. index .. "Light"].gameObject, true)
end

function AlbumRecyleFlyBox:PlayOpenBoxAnim()
  self.m_BoxAnim:SetTrigger("Open")
end

AlbumActivityRecyleRewardWindow = setmetatable({showWindowMask = false}, RewardWindow)
AlbumActivityRecyleRewardWindow.__index = AlbumActivityRecyleRewardWindow

function AlbumActivityRecyleRewardWindow:ShowWindowOnLoadFinished()
  if self.m_strOpenWindowEffectName and self.m_strOpenWindowEffectName ~= "" then
    GM.AudioModel:PlayEffect(self.m_strOpenWindowEffectName)
  end
  if self.disableWindowMaskOnOpenView ~= nil then
    self:DisableWindowMaskTouchTemporarily(self.disableWindowMaskOnOpenView)
  end
end

function AlbumActivityRecyleRewardWindow:BeforeOpenCheck(ActivityType, flyObj, SucCallback, rewards)
  if flyObj.gameObject:IsNull() then
    return false
  end
  return true
end

function AlbumActivityRecyleRewardWindow:Init(ActivityType, flyObj, SucCallback, rewards)
  self.m_model = GM.ActivityManager:GetModel(ActivityType)
  self.m_activityDefinition = AlbumActivityDefinition[ActivityType]
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self.Close)
  self.m_rewards = rewards
  self.m_sucCallback = SucCallback
  self.m_flyObj = flyObj
  local canvas = flyObj.gameObject:GetComponent(typeof(CS.UnityEngine.Canvas))
  if canvas ~= nil then
    canvas.sortingOrder = self:GetSortingOrder() + 1
  end
  self.m_flyObj.transform.parent = self.m_BoxNodeRectTrans
  GM.UIManager:SetEventLock(true, self)
  local sequence = DOTween.Sequence()
  sequence:Append(0, self.m_maskImage:DOFade(0.8, 0.5))
  DelayExecuteFuncInView(function()
    self:OpenBox()
  end, 0.5, self)
  DelayExecuteFuncInView(function()
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxSaveCoconutCele)
    EventDispatcher.DispatchEvent(EEventType.VibrationLight)
  end, 0.6, self)
end

function AlbumActivityRecyleRewardWindow:OpenBox()
  self.m_flyObj:PlayOpenBoxAnim()
  DelayExecuteFuncInView(function()
    GM.UIManager:SetEventLock(false, self)
    self:OnCloseWindow()
  end, 2.0, self)
end

function AlbumActivityRecyleRewardWindow:OnDestroy()
  GM.UIManager:RemoveAllEventLocks(self)
  AlbumActivityBaseWindow.OnDestroy(self)
end

function AlbumActivityRecyleRewardWindow:OnCloseWindow()
  self.m_winAnimator:SetTrigger("Show")
  UIUtil.SetActive(self.m_contentRectTrans.gameObject, true)
  self.m_bInit = true
  RewardWindow.Init(self, self.m_rewards, "rewards_window_title_shop", false)
end

function AlbumActivityRecyleRewardWindow:Close()
  if self.m_sucCallback then
    self.m_sucCallback()
  end
  if self.m_bInit then
    RewardWindow.Close(self)
  end
end
