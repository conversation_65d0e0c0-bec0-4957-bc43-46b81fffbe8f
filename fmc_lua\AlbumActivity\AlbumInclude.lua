require("AlbumActivity.AlbumActivityDefinition")
require("AlbumActivity.Model.AlbumActivityModel")
require("AlbumActivity.View.AlbumEntry")
require("AlbumActivity.View.AlbumPopupHelper")
require("AlbumActivity.View.AlbumActivityWindow")
require("AlbumActivity.View.AlbumActivityMainWindow")
require("AlbumActivity.View.AlbumActivityPackWindow")
require("AlbumActivity.View.AlbumActivityShowCardWindow")
require("AlbumActivity.View.AlbumActivityCard")
require("AlbumActivity.View.AlbumActivityRewardWindow")
require("AlbumActivity.View.AlbumActivityGachaWindow")
require("AlbumActivity.View.AlbumActivityNormalWindow")
require("AlbumActivity.View.AlbumActivityHelpWindow")
require("AlbumActivity.View.AlbumSetReward")
require("AlbumActivity.View.AlbumActivityRecyleWindow")
require("AlbumActivity.View.AlbumActivityRecyleRewardWindow")
require("AlbumActivity.View.AlbumGetNewJokerWindow")
require("AlbumActivity.View.AlbumRemindUseJokerWindow")
require("AlbumActivity.View.AlbumJokerExchangeWindow")
require("AlbumActivity.View.JokerExchangeCardSetsCell")
require("AlbumActivity.View.JokerExchangeCardCell")
require("AlbumActivity.View.AlbumJokerExchangeConfirmWindow")
require("AlbumActivity.View.AlbumJokerExchangeCompleteWindow")
require("AlbumActivity.View.AlbumUpgradeConfirmWindow")
require("AlbumActivity.View.AlbumUpgradeRewardWindow")
require("AlbumActivity.View.AlbumUpgradeTipWindow")
require("AlbumActivity.View.AlbumRewardBubble")
