DigActivityPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
DigActivityPopupHelper.__index = DigActivityPopupHelper

function DigActivityPopupHelper.Create()
  local helper = setmetatable({}, DigActivityPopupHelper)
  helper:Init()
  return helper
end

function DigActivityPopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(DigActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnSceneChange)
end

function DigActivityPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function DigActivityPopupHelper:_OnSceneChange()
  local canPop = GM.SceneManager:GetGameMode() == EGameMode.Board or GM.SceneManager:GetGameMode() == EGameMode.Main
  self:SetNeedCheckPopup(canPop)
end

function DigActivityPopupHelper:CheckPopup()
  for activityType, activityDefinition in pairs(DigActivityDefinition) do
    local args = {activityType}
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Ended and not model:HasWindowOpenedOnce(ActivityState.Ended) and model:HasWindowOpenedOnce(ActivityState.Started) and not model:HasFinishedAllRound() then
      return activityDefinition.EndWindowPrefabName, args
    end
  end
  for activityType, activityDefinition in pairs(DigActivityDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Started and not model:HasWindowOpenedOnce(ActivityState.Started) then
      return activityDefinition.StartWindowPrefabName, {activityType}
    end
  end
end
