TestLocalBackupWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestLocalBackupWindow.__index = TestLocalBackupWindow

function TestLocalBackupWindow:Init()
  self.m_cells = {}
  local backupList = GM.TestModel:GetBackupList()
  for i = #backupList, 1, -1 do
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.TestBackupCell), self.m_contentRectTrans, Vector3.zero, function(go)
      local cell = go:GetLuaTable()
      cell:Init(backupList[i])
      table.insert(self.m_cells, cell)
    end)
  end
end

function TestLocalBackupWindow:OnClickSave()
  local name = self.m_nameInput.text
  if self.m_nameInput.text == "" then
    name = os.date("%Y/%m/%d-%H:%M:%S", GM.GameModel:GetServerTime())
  end
  local info = GM.TestModel:AddNewBackup(name)
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.TestBackupCell), self.m_contentRectTrans, Vector3.zero, function(go)
    go.transform:SetSiblingIndex(0)
    local cell = go:GetLuaTable()
    cell:Init(info)
    table.insert(self.m_cells, 1, cell)
  end)
end

function TestLocalBackupWindow:OnClickRefresh()
end

TestLocalBackupCell = {}
TestLocalBackupCell.__index = TestLocalBackupCell

function TestLocalBackupCell:Init(info)
  self.m_id = info.id
  self.m_dataText.text = info.id .. " - 【" .. info.name .. "】\n" .. info.version .. (info.server and " - ser-" .. string.sub(info.server, 13) or "") .. " - " .. os.date("%Y/%m/%d-%H:%M:%S", info.time)
end

function TestLocalBackupCell:OnClickApply()
  GM.TestModel:UseBackup(self.m_id)
end

function TestLocalBackupCell:OnClickClean()
  local success = GM.TestModel:DeleteBackup(self.m_id)
  if success then
    self.gameObject:RemoveSelf()
  end
end
