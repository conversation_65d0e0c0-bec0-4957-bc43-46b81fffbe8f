local Serialization = require("Model.Network.Serialization")()
Serialization.ProtocolMd5 = {
  1,
  245,
  231,
  44,
  1,
  225,
  217,
  178,
  29,
  172,
  46,
  199,
  205,
  243,
  157,
  114
}
Serialization.RankInfo = {}
Serialization.RankInfo.__index = Serialization.RankInfo

function Serialization.RankInfo.Serialize(writer, value)
  local bRet
  assert(value.userid ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userid)
  if not bRet then
    return false
  end
  assert(value.name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.name)
  if not bRet then
    return false
  end
  assert(value.icon ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon)
  if not bRet then
    return false
  end
  assert(value.icon_frame ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon_frame)
  if not bRet then
    return false
  end
  assert(value.score ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.score)
  if not bRet then
    return false
  end
  assert(value.rank ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rank)
  if not bRet then
    return false
  end
  return true
end

function Serialization.RankInfo.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.RankInfo)
  bRet, value.userid = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon_frame = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.score = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.rank = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.GetBakeOutRankReq = {}
Serialization.GetBakeOutRankReq.__index = Serialization.GetBakeOutRankReq

function Serialization.GetBakeOutRankReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.config_key ~= nil)
  bRet = Serialization.String.Serialize(writer, value.config_key)
  if not bRet then
    return false
  end
  assert(value.config_id ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.config_id)
  if not bRet then
    return false
  end
  assert(value.name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.name)
  if not bRet then
    return false
  end
  assert(value.icon ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon)
  if not bRet then
    return false
  end
  assert(value.icon_frame ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon_frame)
  if not bRet then
    return false
  end
  assert(value.token ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.token)
  if not bRet then
    return false
  end
  return true
end

function Serialization.GetBakeOutRankReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.GetBakeOutRankReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.config_key = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.config_id = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon_frame = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.token = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.GetBakeOutRankResp = {}
Serialization.GetBakeOutRankResp.__index = Serialization.GetBakeOutRankResp

function Serialization.GetBakeOutRankResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.is_finished ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.is_finished)
  if not bRet then
    return false
  end
  assert(value.group_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.group_id)
  if not bRet then
    return false
  end
  assert(value.ranks ~= nil)
  bRet = Serialization.Array(Serialization.RankInfo).Serialize(writer, value.ranks)
  if not bRet then
    return false
  end
  return true
end

function Serialization.GetBakeOutRankResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.GetBakeOutRankResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.is_finished = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.group_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ranks = Serialization.Array(Serialization.RankInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.QueryBakeOutRankReq = {}
Serialization.QueryBakeOutRankReq.__index = Serialization.QueryBakeOutRankReq

function Serialization.QueryBakeOutRankReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.group_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.group_id)
  if not bRet then
    return false
  end
  return true
end

function Serialization.QueryBakeOutRankReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.QueryBakeOutRankReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.group_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.QueryBakeOutRankResp = {}
Serialization.QueryBakeOutRankResp.__index = Serialization.QueryBakeOutRankResp

function Serialization.QueryBakeOutRankResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.ranks ~= nil)
  bRet = Serialization.Array(Serialization.RankInfo).Serialize(writer, value.ranks)
  if not bRet then
    return false
  end
  return true
end

function Serialization.QueryBakeOutRankResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.QueryBakeOutRankResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ranks = Serialization.Array(Serialization.RankInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.HeartBeatReq = {}
Serialization.HeartBeatReq.__index = Serialization.HeartBeatReq

function Serialization.HeartBeatReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.session ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.session)
  if not bRet then
    return false
  end
  assert(value.bakeoutToken ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.bakeoutToken)
  if not bRet then
    return false
  end
  return true
end

function Serialization.HeartBeatReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.HeartBeatReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.session = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.bakeoutToken = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.HeartBeatResp = {}
Serialization.HeartBeatResp.__index = Serialization.HeartBeatResp

function Serialization.HeartBeatResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.bakeoutRank ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.bakeoutRank)
  if not bRet then
    return false
  end
  return true
end

function Serialization.HeartBeatResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.HeartBeatResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.bakeoutRank = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.reqNameMap = {
  RSGetBakeOutRank = Serialization.GetBakeOutRankReq,
  RSQueryBakeOutRank = Serialization.QueryBakeOutRankReq,
  RSHeartBeat = Serialization.HeartBeatReq
}
Serialization.respNameMap = {
  RSGetBakeOutRank = Serialization.GetBakeOutRankResp,
  RSQueryBakeOutRank = Serialization.QueryBakeOutRankResp,
  RSHeartBeat = Serialization.HeartBeatResp
}
return Serialization
