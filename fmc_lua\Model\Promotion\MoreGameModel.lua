MoreGameModel = {}
MoreGameModel.__index = MoreGameModel
local EMoreGameWinOpenStatus = {NotOpen = "notopen", Opened = "open"}
local MoreGameIDs = "more_game_ids"
local MoreNameNextID = 99

function MoreGameModel:Init()
  self.m_bOpenMoreGame = false
end

function MoreGameModel:LoginMsg(data)
  self.m_bOpenMoreGame = false
  if Table.IsEmpty(data) then
    return
  end
  self.m_arrHistoryIds = {}
  local ids = PlayerPrefs.GetString(MoreGameIDs, "")
  if ids ~= "" then
    self.m_arrHistoryIds = StringUtil.Split(ids, ",")
  end
  local arrGame = data
  local bIsiOS = DeviceInfo.IsSystemIOS()
  self.m_arrGames = {}
  for i = 1, #data do
    local game = {}
    game.gameId = arrGame[i].promoted_games_id
    game.targetGameToken = arrGame[i].target_game_token or ""
    game.packageName = arrGame[i].package_name
    game.scheme = arrGame[i].scheme
    game.m_judgeInstallKey = bIsiOS and game.scheme or game.packageName
    game.link = arrGame[i].link
    game.picture = arrGame[i].picture
    game.pictureName = self:ParseFileName(game.picture)
    game.order = arrGame[i].order
    self.m_arrGames[#self.m_arrGames + 1] = game
    if not Table.Contain(self.m_arrHistoryIds, tostring(game.gameId)) then
      self.m_arrHistoryIds[#self.m_arrHistoryIds + 1] = tostring(game.gameId)
      PlayerPrefs.SetString(EPlayerPrefKey.MoreGameWindowOpened, EMoreGameWinOpenStatus.NotOpen)
    end
  end
  PlayerPrefs.SetString(MoreGameIDs, table.concat(self.m_arrHistoryIds, ","))
  self.m_arrGames[#self.m_arrGames + 1] = {
    gameId = MoreNameNextID,
    pictureName = "crosspromotion_next",
    order = 99
  }
  table.sort(self.m_arrGames, function(a, b)
    return a.order < b.order
  end)
  self.m_bOpenMoreGame = true
end

function MoreGameModel:GetGames()
  return self.m_arrGames
end

function MoreGameModel:ParseFileName(name)
  local ts = string.reverse(name)
  local _, i = string.find(ts, "/")
  if i == nil then
    return name
  end
  local m = string.len(ts) - i + 1
  return string.sub(name, m + 1, -1)
end

function MoreGameModel:Go2NewGame(gameId)
  local tbGame
  for i = 1, #self.m_arrGames do
    if self.m_arrGames[i].gameId == gameId then
      tbGame = self.m_arrGames[i]
      break
    end
  end
  if tbGame ~= nil then
    local param = "?campaign=" .. GameConfig.GetGameToken() .. "&adgroup=" .. gameId .. "&creative=" .. GM.UserModel:GetUserId()
    if DeviceInfo.IsSystemIOS() then
      param = param .. "&idfa=" .. DeviceInfo.GetAdvertisingIdentifier()
    else
      param = param .. "&gps_adid=" .. DeviceInfo.GetAdvertisingIdentifier()
    end
    local urlPath
    if PlatformInterface.IsAppInstalled(tbGame.m_judgeInstallKey) then
      urlPath = tbGame.scheme .. "://" .. EOperActionPath.MoreGame .. param
      GM.BIManager:LogAction(EBIType.MoreGamePlayAction.PullUp, urlPath .. "&targetGameToken=" .. tbGame.targetGameToken)
    else
      urlPath = GM.CrossPromotionModel:ReplaceURLParam(tbGame.link, gameId)
      GM.BIManager:LogAction(EBIType.MoreGamePlayAction.Down, urlPath .. "&targetGameToken=" .. tbGame.targetGameToken)
    end
    CSPlatform:OpenURL(urlPath)
  end
end

function MoreGameModel:IsMoreNextCell(id)
  return id == MoreNameNextID
end

function MoreGameModel:IsMoreGame()
  return self.m_bOpenMoreGame
end

function MoreGameModel:SetMGWinOpended()
  PlayerPrefs.SetString(EPlayerPrefKey.MoreGameWindowOpened, EMoreGameWinOpenStatus.Opened)
end

function MoreGameModel:IsStrongTip()
  return self.m_bOpenMoreGame and PlayerPrefs.GetString(EPlayerPrefKey.MoreGameWindowOpened, EMoreGameWinOpenStatus.Opened) == EMoreGameWinOpenStatus.NotOpen
end
