local Step = {PutBack = "1"}
local EStep2TextKey = {
  [Step.PutBack] = "tutorial_equipmentReturn_1"
}
local EStep2TextAnchorPercent = {
  [Step.PutBack] = 67
}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.LevelUp, self, self._OnLevelUp)
  EventDispatcher.AddListener(EEventType.ItemCookMaterialClicked, self, self._OnItemCookMaterialClicked)
end

function Executer:_OnLevelUp()
  if self.m_strOngoingDatas == Step.PutBack and self.m_gesture then
    return
  end
  if GM.LevelModel:GetCurrentLevel() >= 7 then
    self:Finish()
  end
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  if (StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) or self.m_strOngoingDatas == Step.PutBack) and self:_TryExecuteStep1() then
    return true
  end
end

function Executer:_OnItemCookMaterialClicked()
  if self.m_strOngoingDatas == Step.PutBack and self.m_gesture then
    TutorialHelper.DehighlightBoardInfoCookMaterials()
    self:Finish(self.m_gesture)
  end
end

function Executer:_TryExecuteStep1()
  local items = GM.MainBoardModel:FilterItems(function(itemModel)
    return itemModel:GetComponent(ItemCook) ~= nil and (itemModel:GetComponent(ItemCook):GetState() == EItemCookState.CanCook or itemModel:GetComponent(ItemCook):GetState() == EItemCookState.Prepare)
  end)
  for _, item in ipairs(items) do
    local itemCook = item:GetComponent(ItemCook)
    if self:_CanTutorialTakeOut(itemCook:GetCurMaterialsArray()) then
      local boardView = MainBoardView.GetInstance()
      if boardView ~= nil then
        boardView:UpdateSelectedItem(item)
      end
      self:_ExecuteStep1()
      return true
    end
  end
end

function Executer:_CanTutorialTakeOut(curMaterials)
  if #curMaterials == 0 then
    return false
  end
  local dishes, _ = GM.MainBoardModel:GetOrderRequirements(false)
  for dish, _ in pairs(dishes) do
    local needMaterials = GM.ItemDataModel:GetMaterials(dish, false, false)
    if Table.IsSubArray(needMaterials, curMaterials) then
      return false
    end
  end
  return true
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.PutBack
  self:_SaveOngoingDatas()
  TutorialHelper.WholeMask()
  TutorialHelper.HighlightBoardInfoCookMaterials()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  self.m_gesture = TutorialHelper.TapOnBoardInfoCookFirstMaterial()
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
