ExtraBoardActivityLevelProgressCell = {}
ExtraBoardActivityLevelProgressCell.__index = ExtraBoardActivityLevelProgressCell

function ExtraBoardActivityLevelProgressCell:Init(level, model, window, bEnd)
  self.m_index = level + 1
  self.m_model = model
  self.m_window = window
  self.m_bEnd = bEnd
  self.m_iconArea:SetParent(window, self.m_index)
  local itemType = self.m_model:GetItemTypeByIndex(self.m_index)
  local spriteName = GM.ItemDataModel:GetSpriteName(itemType)
  SpriteUtil.SetImage(self.m_iconImage, spriteName, false)
  SpriteUtil.SetImage(self.m_shadowImage, spriteName, false)
end

function ExtraBoardActivityLevelProgressCell:UpdateLevelContent(currentLevel, needAnimation)
  if needAnimation and not self.m_iconImage.gameObject.activeSelf and currentLevel >= self.m_index then
    local originalScale = self.m_iconImage.transform.localScale
    local sequence = DOTween.Sequence()
    sequence:Append(self.m_shadowImage.transform:DOScale(originalScale * 0.5, 0.13))
    sequence:AppendCallback(function()
      self.m_iconImage.transform.localScale = originalScale * 0.5
      self.m_iconImage.gameObject:SetActive(true)
      self.m_shadowImage.gameObject:SetActive(false)
    end)
    sequence:Append(self.m_iconImage.transform:DOScale(originalScale * 1.2, 0.1))
    sequence:Append(self.m_iconImage.transform:DOScale(originalScale, 0.67))
  else
    UIUtil.SetActive(self.m_iconImage.gameObject, currentLevel >= self.m_index)
    UIUtil.SetActive(self.m_shadowImage.gameObject, currentLevel < self.m_index)
  end
end

function ExtraBoardActivityLevelProgressCell:GetIconArea()
  return self.m_iconArea
end

ExtraBoardActivityLevelProgressCellButton = setmetatable({}, HudBaseButton)
ExtraBoardActivityLevelProgressCellButton.__index = ExtraBoardActivityLevelProgressCellButton

function ExtraBoardActivityLevelProgressCellButton:SetParent(window, index)
  self.m_window = window
  self.m_index = index
end

function ExtraBoardActivityLevelProgressCellButton:IconScaleAnimation(needEffect)
  HudBaseButton.IconScaleAnimation(self, needEffect)
  if self.m_window and self.m_window.gameObject and not self.m_window.gameObject:IsNull() then
    self.m_window:OnUnlockAnimationFinish(self.m_index)
  end
end
