DecoratedTitle = {}
DecoratedTitle.__index = DecoratedTitle
local gapWidth = 42

function DecoratedTitle:Start()
  local totalWidth = self.m_rootRectTrans.sizeDelta.x
  local textWidth = self.m_text.preferredWidth
  local decorationWidth = self.m_leftRectTrans.sizeDelta.x + self.m_rightRectTrans.sizeDelta.x + gapWidth * 2
  local maxTextWidth = totalWidth - decorationWidth
  textWidth = math.min(textWidth, maxTextWidth)
  totalWidth = textWidth + decorationWidth
  self.m_textRectTrans.sizeDelta = Vector2(textWidth, self.m_textRectTrans.sizeDelta.y)
  self.m_rootRectTrans.sizeDelta = Vector2(totalWidth, self.m_rootRectTrans.sizeDelta.y)
end
