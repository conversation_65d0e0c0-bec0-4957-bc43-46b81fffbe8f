return {
  {
    ChapterId = "BBQ",
    Id = 1,
    Cost = 122,
    Rewards = {
      {Currency = "exp", Amount = 50},
      {Currency = "energy", Amount = 20}
    },
    SlotState = {
      {Slot = "kitFloor", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 2,
    StartConditions = {1},
    Cost = 122,
    Rewards = {
      {Currency = "exp", Amount = 50},
      {Currency = "energy", Amount = 20}
    },
    SlotState = {
      {Slot = "kitWall", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 3,
    StartConditions = {2},
    Cost = 130,
    Rewards = {
      {Currency = "exp", Amount = 50},
      {Currency = "energy", Amount = 20}
    },
    SlotState = {
      {Slot = "kitStove", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 4,
    StartConditions = {3},
    Cost = 138,
    Rewards = {
      {Currency = "exp", Amount = 50},
      {Currency = "energy", Amount = 8}
    },
    SlotState = {
      {Slot = "kitSink", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 5,
    StartConditions = {4},
    Cost = 138,
    Rewards = {
      {Currency = "exp", Amount = 50},
      {Currency = "energy", Amount = 8}
    },
    SlotState = {
      {Slot = "kitWood", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 6,
    StartConditions = {5},
    Cost = 130,
    Rewards = {
      {Currency = "exp", Amount = 50},
      {Currency = "energy", Amount = 8}
    },
    SlotState = {
      {Slot = "kitHood", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 7,
    StartConditions = {6},
    Cost = 117,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 8}
    },
    SlotState = {
      {
        Slot = "kitCookware",
        State = 9
      }
    }
  },
  {
    ChapterId = "BBQ",
    Id = 8,
    StartConditions = {7},
    Cost = 117,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "pd_4_1", Amount = 1},
      {Currency = "energy", Amount = 8}
    },
    SlotState = {
      {Slot = "kitPlate", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 9,
    StartConditions = {8},
    Cost = 164,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 8}
    },
    SlotState = {
      {
        Slot = "bigFloorTrash",
        State = 100
      },
      {
        Slot = "bigFloorOld",
        State = 100
      },
      {Slot = "bigFloor", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 10,
    StartConditions = {9},
    Cost = 164,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 8}
    },
    SlotState = {
      {Slot = "fence", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 11,
    StartConditions = {10},
    Cost = 145,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 8}
    },
    SlotState = {
      {Slot = "tableLeft", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 12,
    StartConditions = {11},
    Cost = 117,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 8}
    },
    SlotState = {
      {
        Slot = "utensilLeft",
        State = 9
      }
    }
  },
  {
    ChapterId = "BBQ",
    Id = 13,
    StartConditions = {12},
    Cost = 155,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 8}
    },
    SlotState = {
      {Slot = "bonsai", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 14,
    StartConditions = {13},
    Cost = 189,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barFloor", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 15,
    StartConditions = {14},
    Cost = 189,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barWall", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 16,
    StartConditions = {15},
    Cost = 142,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barRoof", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 17,
    StartConditions = {16},
    Cost = 111,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barLed", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 18,
    StartConditions = {17},
    Cost = 96,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barLight", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 19,
    StartConditions = {18},
    Cost = 174,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barTable", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 20,
    StartConditions = {19},
    Cost = 158,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barItems", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 21,
    StartConditions = {20},
    Cost = 220,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "glassWall", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 22,
    StartConditions = {21},
    Cost = 226,
    Rewards = {
      {Currency = "exp", Amount = 75},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "glassDec", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 23,
    StartConditions = {22},
    Cost = 226,
    Rewards = {
      {Currency = "exp", Amount = 108},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "tableRight", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 24,
    StartConditions = {23},
    Cost = 173,
    Rewards = {
      {Currency = "exp", Amount = 108},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "utensilRight",
        State = 9
      }
    }
  },
  {
    ChapterId = "BBQ",
    Id = 25,
    StartConditions = {24},
    Cost = 208,
    Rewards = {
      {Currency = "exp", Amount = 108},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "decFront", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 26,
    StartConditions = {25},
    Cost = 102,
    Rewards = {
      {Currency = "exp", Amount = 108},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "plantRight", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 27,
    StartConditions = {26},
    Cost = 102,
    Rewards = {
      {Currency = "exp", Amount = 108},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "door", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 28,
    StartConditions = {27},
    Cost = 155,
    Rewards = {
      {Currency = "exp", Amount = 108},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "bushRight", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 29,
    StartConditions = {28},
    Cost = 155,
    Rewards = {
      {Currency = "exp", Amount = 108},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "bushLeft", State = 9}
    }
  },
  {
    ChapterId = "BBQ",
    Id = 30,
    StartConditions = {29},
    Cost = 173,
    Rewards = {
      {Currency = "exp", Amount = 108},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "bushBack", State = 9}
    }
  }
}
