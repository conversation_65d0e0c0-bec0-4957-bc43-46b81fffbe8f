SyncModel = {}
SyncModel.__index = SyncModel
ESyncState = {
  Idle = 1,
  Uploading = 2,
  Downloading = 3,
  VersionError = 5
}
local CHECK_UPLOAD_DELAY_NORMAL = 300
local CHECK_UPLOAD_DELAY_OFTEN = 30
local CHECK_UPLOAD_DELAY_OFTEN_STEP = 30
local arrDiscardSyncKey = {}

function SyncModel:Init()
  self:RegisterModels()
  
  function self.m_funcAutoCheckUpload()
    self:CheckUpload()
  end
  
  self:ChangeSyncState(ESyncState.Idle)
  self:_ScheduleNextUpload(CHECK_UPLOAD_DELAY_OFTEN)
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self.OnLoginFinished)
end

function SyncModel:Destroy()
  self:UnregisterModels()
  EventDispatcher.RemoveTarget(self)
  Scheduler.UnscheduleTarget(self)
end

function SyncModel:ApplicationWillEnterForeground()
  self:_ScheduleNextUpload(CHECK_UPLOAD_DELAY_OFTEN)
end

function SyncModel:SyncDataWhenLoading(callbackFunc)
  if self:ShouldDownload() then
    self:DownloadData(callbackFunc, true)
  elseif self:ShouldUpload() then
    callbackFunc(true)
    self:UploadData(nil, nil, false)
  else
    callbackFunc(true)
  end
end

function SyncModel:OnSceneViewLoaded()
  if self.m_downloadedServerData ~= nil then
    self:_OpenConflictWindow(self.m_downloadedServerData)
    self.m_downloadedServerData = nil
  end
end

function SyncModel:RegisterModel(model, serverFieldName, bCanTriggerUpload)
  Log.Assert(model and serverFieldName, "SyncModel:RegisterModel 1")
  Log.Assert(model.GetData and model.FromSyncData, "SyncModel:RegisterModel 2")
  bCanTriggerUpload = bCanTriggerUpload == nil and true or bCanTriggerUpload
  self.m_mapRegisteredModels[model] = {name = serverFieldName, bCanTriggerUpload = bCanTriggerUpload}
end

function SyncModel:RegisterModels()
  self.m_mapRegisteredModels = {}
  self:RegisterModel(GM.EnergyModel, "energy", false)
  self:RegisterModel(GM.ItemDataModel, "itemUnlock")
  self:RegisterModel(GM.ShopModel, "shop")
  self:RegisterModel(GM.TutorialModel, "tutorial")
  self:RegisterModel(GM.MiscModel, "misc")
  self:RegisterModel(GM.UserProfileModel, "profile")
  self:RegisterModel(GM.ReturnUserModel, "return_user")
  self:RegisterModel(GM.RewardModel, "local_rewards")
  self:RegisterModel(GM.BIManager, "bi_sync", false)
  self:RegisterModel(GM.NoticeModel, "notice")
end

function SyncModel:UnregisterModels()
  self.m_mapRegisteredModels = {}
end

function SyncModel:ChangeSyncState(eSyncState)
  self.eSyncState = eSyncState
  EventDispatcher.DispatchEvent(EEventType.SyncStateChanged)
end

function SyncModel:_UnScheduleNextUpload()
  if self.m_funcAutoCheckUpload then
    Scheduler.Unschedule(self.m_funcAutoCheckUpload, self)
    Log.Info("SyncModel unschedule next upload")
  end
end

function SyncModel:_ScheduleNextUpload(delay)
  self:_UnScheduleNextUpload()
  self.m_checkUploadDelay = delay
  Scheduler.Schedule(self.m_funcAutoCheckUpload, self, delay, 1)
  Log.Info("SyncModel schedule next upload, delay:" .. delay)
end

function SyncModel:_UpdateCheckUploadDelay(bUploadSuccess)
  if not GM.DBTableManager:HealthCheck() then
    self:_ScheduleNextUpload(CHECK_UPLOAD_DELAY_OFTEN)
  elseif bUploadSuccess then
    self:_ScheduleNextUpload(CHECK_UPLOAD_DELAY_NORMAL)
    self.m_bUploadSucceed = true
  elseif self.m_checkUploadDelay == CHECK_UPLOAD_DELAY_NORMAL and self.m_bUploadSucceed == true then
    self:_ScheduleNextUpload(CHECK_UPLOAD_DELAY_OFTEN)
    self.m_bUploadSucceed = false
  elseif self.m_checkUploadDelay >= CHECK_UPLOAD_DELAY_OFTEN and self.m_checkUploadDelay < CHECK_UPLOAD_DELAY_NORMAL then
    self:_ScheduleNextUpload(self.m_checkUploadDelay + CHECK_UPLOAD_DELAY_OFTEN_STEP)
  else
    self:_ScheduleNextUpload(CHECK_UPLOAD_DELAY_NORMAL)
  end
end

function SyncModel:OnLoginFinished(msg)
  if not msg.bSuccess then
    return
  end
  local tbResp = msg.tbResp
  local serverSyncTime = tonumber(tbResp.last_sync_time) or 0
  if serverSyncTime ~= 0 then
    GM.UserModel.newUser = false
  end
  if tbResp and tbResp.force_sync == 1 then
    GM.BIManager:LogProject(EBIType.SyncModel, EBIType.SyncModelAction.LoginForceSync)
    self:DownloadData()
    return
  end
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.DataConflictWindow) then
    GM.BIManager:LogProject(EBIType.SyncModel, EBIType.SyncModelAction.LoginConflictExist)
    return
  end
  if serverSyncTime == 0 then
    self:_ClearDataCrackedTag()
    self:SetDataInconsistent(false)
    self:ClearDataUploadRecord()
    self:UploadData()
    return
  end
  if GM.SceneManager:GetGameMode() == EGameMode.Loading and self:_CheckDataCracked() then
    self:DownloadData()
    GM.BIManager:LogErrorInfo(EBIType.DataCracked, "cracked on login")
    return
  end
  local localSyncTime = self:GetLastSyncTime()
  if serverSyncTime ~= localSyncTime then
    GM.BIManager:LogProject(EBIType.SyncModel, tostring(EBIType.SyncModelAction.LoginSyncTimeDiffer) .. " local: " .. tostring(localSyncTime) .. " server: " .. tostring(serverSyncTime))
    local serverDeviceId = tbResp.installation_id
    local localDeviceId = GM.UserModel:GetInstallUuid()
    local serverDeviceModel = tbResp.update_device_model
    local localDeviceModel = DeviceInfo.GetDeviceModel()
    if serverDeviceId ~= localDeviceId or serverDeviceModel ~= "" and serverDeviceModel ~= localDeviceModel then
      if serverDeviceId ~= localDeviceId then
        GM.BIManager:LogProject(EBIType.SyncModel, tostring(EBIType.SyncModelAction.LoginDeviceIdDiffer) .. " local: " .. tostring(localDeviceId) .. " server: " .. tostring(serverDeviceId))
      else
        GM.BIManager:LogProject(EBIType.SyncModel, tostring(EBIType.SyncModelAction.LoginDeviceModelDiffer) .. " local: " .. tostring(localDeviceModel) .. " server: " .. tostring(serverDeviceModel))
      end
      self:DownloadData()
    else
      self:UploadData(nil, math.max(serverSyncTime, localSyncTime))
    end
  else
    Log.Info("SyncModel OnLoginFinished same syncTime")
    self:SetDataInconsistent(false)
    self:CheckUpload()
  end
end

function SyncModel:_GetUploadDataTableArray(checkingIfShouldUpload)
  local arrDataTable = {
    GM.UserModel:GetData(),
    GM.MainBoardModel:GetItemData(),
    GM.MainBoardModel:GetItemLayerData(),
    GM.MainBoardModel:GetItemCacheData(),
    GM.MainBoardModel:GetItemStoreData(),
    GM.MainBoardModel:GetOrderMetaData(),
    GM.MainBoardModel:GetOrderData(),
    GM.ShopModel:GetShopItemDB(),
    GM.ActivityManager:GetGeneralData(),
    GM.BundleManager:GetBundleDataTable(),
    GM.BundleManager:GetBundleMetaDataTable(),
    GM.TaskManager:GetMetaData(),
    GM.TaskManager:GetFinishedTaskData()
  }
  for model, params in pairs(self.m_mapRegisteredModels) do
    if not checkingIfShouldUpload or params.bCanTriggerUpload then
      table.insert(arrDataTable, model:GetData())
    end
  end
  return arrDataTable
end

function SyncModel:ShouldUploadData(dbTable)
  if not dbTable then
    Log.Assert(false, "SyncModel:ShouldUploadData")
    return false
  end
  return dbTable:IsModified()
end

function SyncModel:_GetDataToUpload(dbTable)
  if self:ShouldUploadData(dbTable) then
    dbTable:SetUploading()
    return dbTable:ToUploadArr()
  else
    return {}
  end
end

function SyncModel:CheckUpload(canBeSkipped, callback)
  Log.Info("SyncModel checkUpload")
  if self:ShouldUpload() then
    self:UploadData(callback, nil, canBeSkipped)
    return true
  else
    self:_UpdateCheckUploadDelay(true)
  end
end

function SyncModel:ShouldUpload()
  if self.m_uploadSkippedInLoadingLayer then
    return true
  end
  GM.DBTableManager:FlushTables()
  if GM:IsNewUser() then
    return false
  end
  local arrDataTable = self:_GetUploadDataTableArray(true)
  for i = 1, #arrDataTable do
    if self:ShouldUploadData(arrDataTable[i]) then
      Log.Info("SyncModel ShouldUpload because of " .. tostring(arrDataTable[i].tableName))
      return true
    end
  end
  return false
end

function SyncModel:CanUpload()
  if self:IsDataInconsistent() then
    Log.Info("SyncModel DataInconsistent")
    return false
  end
  if self:_IsDataCracked() then
    Log.Info("SyncModel data cracked")
    return false
  end
  if GM.destroying then
    Log.Info("SyncModel destroying")
    return false
  end
  if self.eSyncState == ESyncState.Uploading then
    Log.Info("SyncModel already uploading")
    return false
  end
  if self.eSyncState == ESyncState.Downloading then
    Log.Info("SyncModel can not upload when downloading")
    return false
  end
  if self.eSyncState == ESyncState.VersionError then
    Log.Info("SyncModel can not upload when version error")
    return false
  end
  if self:ShouldDownload() then
    Log.Info("SyncModel no local data")
    return false
  end
  if GM:IsNewUser() then
    Log.Info("SyncModel can not upload in new-user state")
    return false
  end
  return true
end

function SyncModel:UploadData(callbackFunc, syncTime, canBeSkipped)
  canBeSkipped = canBeSkipped ~= false
  if GM.SceneManager:GetGameMode() == EGameMode.Loading and canBeSkipped then
    if not self:ShouldDownload() then
      self.m_uploadSkippedInLoadingLayer = true
      self.m_syncTimeWhenSkipUpload = syncTime
    end
    self:_UpdateCheckUploadDelay(false)
    return
  end
  if self.m_uploadSkippedInLoadingLayer then
    if syncTime == nil then
      syncTime = self.m_syncTimeWhenSkipUpload
    end
    self.m_uploadSkippedInLoadingLayer = false
    self.m_syncTimeWhenSkipUpload = nil
  end
  callbackFunc = callbackFunc or function()
  end
  if not syncTime and not self:CanUpload() then
    callbackFunc(false)
    self:_UpdateCheckUploadDelay(false)
    return
  end
  GM.DBTableManager:TrySaveAll()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local orderCId, orderGId = orderModel:GetOrderGroupInfo()
  local tbReq = {
    userId = GM.UserModel:GetUserId(),
    version = GameConfig.GetCurrentVersion(),
    syncTime = syncTime or self:GetLastSyncTime(),
    currentTask = GM.TaskManager:GetOngoingTasksString(),
    currentChapter = GM.TaskManager:GetOngoingChapterId(),
    installation_id = GM.UserModel:GetInstallUuid(),
    device_model = DeviceInfo.GetDeviceModel(),
    orderChapterId = orderCId,
    orderGroupId = orderGId
  }
  local syncList = {}
  syncList[#syncList + 1] = "user"
  tbReq.user = {
    {
      gold = GM.PropertyDataManager:PropertyToSyncData(EPropertyType.Gold),
      gem = GM.PropertyDataManager:PropertyToSyncData(EPropertyType.Gem),
      skipprop = GM.PropertyDataManager:PropertyToSyncData(EPropertyType.SkipProp),
      level = tostring(GM.LevelModel:GetCurrentLevel()),
      exp = GM.PropertyDataManager:PropertyToSyncData(EPropertyType.Experience),
      progress = GM.TaskManager:GetTaskProgress(),
      energy = GM.PropertyDataManager:PropertyToSyncData(EPropertyType.Energy),
      day = orderModel:GetCurOrderDay()
    }
  }
  local userTable = GM.UserModel:GetData()
  userTable:SetUploading()
  self:_PackReq(syncList, tbReq, "item", GM.MainBoardModel:GetItemData())
  self:_PackReq(syncList, tbReq, "board", GM.MainBoardModel:GetItemLayerData())
  self:_PackReq(syncList, tbReq, "cacheItem", GM.MainBoardModel:GetItemCacheData())
  self:_PackReq(syncList, tbReq, "inventory", GM.MainBoardModel:GetItemStoreData())
  self:_PackReq(syncList, tbReq, "orderMeta", GM.MainBoardModel:GetOrderMetaData())
  self:_PackReq(syncList, tbReq, "orders", GM.MainBoardModel:GetOrderData())
  self:_PackReq(syncList, tbReq, "shopItem", GM.ShopModel:GetShopItemDB())
  tbReq.slot = ""
  tbReq.ad_status = ""
  self:_PackReq(syncList, tbReq, "mainTaskMeta", GM.TaskManager:GetMetaData())
  self:_PackReq(syncList, tbReq, "mainTask", GM.TaskManager:GetFinishedTaskData())
  self:_PackReq(syncList, tbReq, "activity_data", GM.ActivityManager:GetGeneralData())
  self:_PackReq(syncList, tbReq, "bundle", GM.BundleManager:GetBundleDataTable())
  self:_PackReq(syncList, tbReq, "bundle_meta", GM.BundleManager:GetBundleMetaDataTable())
  for model, params in pairs(self.m_mapRegisteredModels) do
    self:_PackReq(syncList, tbReq, params.name, model:GetData())
  end
  for _, discardKey in ipairs(arrDiscardSyncKey) do
    if self:_NeedResetSyncKey(discardKey) then
      syncList[#syncList + 1] = discardKey
      self:_ClearResetSyncKey(discardKey)
    end
    tbReq[discardKey] = Table.Empty
  end
  tbReq.syncList = syncList
  tbReq.icon = GM.UserProfileModel:GetIcon()
  tbReq.name = GM.UserProfileModel:GetName()
  self:_UnScheduleNextUpload()
  self:ChangeSyncState(ESyncState.Uploading)
  local startTime = NetTimeStamp.Create(EBIType.NetworkCheckAction.StartSyncUploadData)
  GM.BIManager:LogNet(EBIType.NetworkCheckAction.StartSyncUploadData)
  local callback = function(result, response, responseCtx)
    local timeInterval = startTime:EndAndGetDur()
    if not result then
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.SyncUploadDataFailed, response, responseCtx, timeInterval)
    else
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.SyncUploadDataSuccess, nil, responseCtx, timeInterval)
    end
    self:_OnUploadFinished(result, response, responseCtx, callbackFunc)
    self.m_bIsSendingUploadRequest = false
  end
  self.m_bIsSendingUploadRequest = true
  ApiMessage.UploadData(tbReq, callback)
end

function SyncModel:IsSendingUploadRequest()
  return self.m_bIsSendingUploadRequest
end

function SyncModel:_PackReq(syncList, tbReq, name, data)
  if self:ShouldUploadData(data) then
    syncList[#syncList + 1] = name
  end
  tbReq[name] = self:_GetDataToUpload(data)
end

function SyncModel:_OnUploadFinished(result, response, responseCtx, callbackFunc)
  self:ChangeSyncState(ESyncState.Idle)
  if not result then
    callbackFunc(false)
    self:_UpdateCheckUploadDelay(false)
    return
  end
  local rcode = response.rcode
  Log.Info("SyncModel _OnUploadFinished " .. rcode or -1)
  if rcode == 0 then
    self:SetLastSyncTime(response.newSyncTime)
    self:SetDataInconsistent(false)
    self:_MarkUploaded()
    self:_UpdateCheckUploadDelay(true)
    callbackFunc(true)
  elseif rcode == 1 then
    self:ChangeSyncState(ESyncState.VersionError)
    callbackFunc(false)
  elseif rcode == 2 then
    self:_UpdateCheckUploadDelay(false)
    callbackFunc(false)
  elseif rcode == 3 then
    self:_UpdateCheckUploadDelay(true)
    self:DownloadData(callbackFunc)
  elseif rcode == 4 then
    GM.HeartBeatManager:HandleConflictSession()
    callbackFunc(false)
  elseif rcode == 5 then
    self:SetDataInconsistent(true)
    callbackFunc(false)
  elseif rcode == 6 then
    self:_UpdateCheckUploadDelay(false)
    callbackFunc(false)
  elseif rcode == 7 then
    callbackFunc(false)
    GM.GameModel:OnCheat()
  else
    GM.BIManager:LogProject(EBIType.SyncUploadError, rcode or -1)
    self:_UpdateCheckUploadDelay(false)
    callbackFunc(false)
  end
end

function SyncModel:ShouldDownload()
  if self.m_downloadSkippedInLoadingLayer then
    self.m_downloadSkippedInLoadingLayer = false
    return true
  end
  if self.eSyncState == ESyncState.Downloading or self.eSyncState == ESyncState.VersionError then
    return false
  end
  return GM:IsNewUser()
end

function SyncModel:ClearDataUploadRecord()
  local dataTables = self:_GetUploadDataTableArray()
  for _, dataTable in ipairs(dataTables) do
    dataTable:SetModified()
  end
end

function SyncModel:_MarkUploaded(forceSynced)
  local dataTables = self:_GetUploadDataTableArray()
  for _, dataTable in ipairs(dataTables) do
    dataTable:OnUploadedFinished(forceSynced)
  end
end

function SyncModel:GetLastSyncTime()
  return GM.UserModel:GetInNumber(EUserLocalDataKey.LastSyncTime)
end

function SyncModel:SetLastSyncTime(lastSyncTime)
  GM.BIManager:LogProject(EBIType.SyncModel, tostring(lastSyncTime))
  GM.UserModel:Set(EUserLocalDataKey.LastSyncTime, tostring(lastSyncTime))
end

function SyncModel:SetDataInconsistent(bIsInconsistent)
  local preValue = self:IsDataInconsistent()
  if preValue ~= bIsInconsistent then
    local action = bIsInconsistent and EBIType.SyncModelAction.TurnInconsistent or EBIType.SyncModelAction.TurnConsistent
    GM.BIManager:LogProject(EBIType.SyncModel, action)
  end
  local value = bIsInconsistent and "true" or "false"
  GM.UserModel:Set(EUserLocalDataKey.DataInconsistent, value)
end

function SyncModel:IsDataInconsistent()
  return GM.UserModel:Get(EUserLocalDataKey.DataInconsistent) == "true"
end

function SyncModel:_SetDataCracked()
  self.m_bDataCracked = true
end

function SyncModel:_ClearDataCrackedTag()
  self.m_bDataCracked = nil
end

function SyncModel:_IsDataCracked()
  return self.m_bDataCracked or false
end

function SyncModel:_CheckDataCracked()
  if self:_IsDataCracked() then
    return true
  end
  if GM.DBTableManager:IsCracked() then
    self:_SetDataCracked()
    return true
  end
  local arrDataTable = self:_GetUploadDataTableArray()
  for i = 1, #arrDataTable do
    if arrDataTable[i]:IsCracked() then
      self:_SetDataCracked()
      return true
    end
  end
end

function SyncModel:StoreDataCracked(cracked)
  PlayerPrefs.SetInt(EPlayerPrefKey.UserDataCracked, cracked and 1 or 0)
end

function SyncModel:DownloadData(callbackFunc, isLoading)
  if GM.SceneManager:GetGameMode() == EGameMode.Loading and not callbackFunc then
    self.m_downloadSkippedInLoadingLayer = true
    return
  end
  callbackFunc = callbackFunc or function()
  end
  local startTime = NetTimeStamp.Create(EBIType.NetworkCheckAction.StartSyncDownloadData)
  GM.BIManager:LogNet(EBIType.NetworkCheckAction.StartSyncDownloadData, nil, nil, nil, isLoading)
  local callback = function(result, response, responseCtx)
    local timeInterval = startTime:EndAndGetDur()
    self:ChangeSyncState(ESyncState.Idle)
    if not result then
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.SyncDownloadDataFailed, response, responseCtx, timeInterval, isLoading)
      GM.BIManager:LogProject(EBIType.SyncModel, EBIType.SyncModelAction.DownloadFail)
      callbackFunc(false, response)
      return
    end
    GM.BIManager:LogNet(EBIType.NetworkCheckAction.SyncDownloadDataSuccess, nil, responseCtx, timeInterval, isLoading)
    local rcode = response.rcode
    if rcode == 0 then
      self:OnDownloadDataSuccess(response)
    elseif rcode == 1 then
    elseif rcode == 2 then
      self:ChangeSyncState(ESyncState.VersionError)
    elseif rcode == 3 then
      callbackFunc(false, {hideNetError = true})
      GM.GameModel:OnCheat()
      return
    else
      GM.BIManager:LogProject(EBIType.SyncDownloadError, rcode)
      callbackFunc(false)
      return
    end
    callbackFunc(true)
  end
  self:ChangeSyncState(ESyncState.Downloading)
  ApiMessage.DownloadData(callback, isLoading)
end

function SyncModel:OnDownloadDataSuccess(response)
  GM.BIManager:LogProject(EBIType.SyncModel, EBIType.SyncModelAction.DownloadSuccess)
  if response.force_sync == 1 or self:_IsDataCracked() or GM:IsNewUser() or not self:ShouldUpload() then
    self:_UseServerData(response)
  elseif GM.SceneManager:GetGameMode() == EGameMode.Loading then
    self.m_downloadedServerData = response
  else
    self:_OpenConflictWindow(response)
  end
end

function SyncModel:_OpenConflictWindow(response)
  local selectLocalCallback = function()
    GM.BIManager:LogAction(EBIType.SceneDataConflict, EBIType.SceneDataConflictAction.SameAccountUseLocal)
    self:_UseLocalData(response)
  end
  local selectNewCallback = function()
    GM.BIManager:LogAction(EBIType.SceneDataConflict, EBIType.SceneDataConflictAction.SameAccountUseServer)
    self:_UseServerData(response)
  end
  local gems = GM.PropertyDataManager:GetPropertyNumFromSyncData(response.user[1].gem)
  local coins = GM.PropertyDataManager:GetPropertyNumFromSyncData(response.user[1].gold)
  local level = tonumber(response.user[1].level)
  local day = MainOrderDataModel.GetOrderDayByOrderGroup(response.orderChapterId, response.orderGroupId)
  local currentChapterId = response.currentChapter
  local currentTasks = response.currentTask
  local timestamp = GM.GameModel:GetServerTime() - response.syncTime
  local displayData = DataConflictWindow.CreateDisplayData(gems, coins, level, day, currentChapterId, currentTasks, timestamp)
  GM.UIManager:OpenView(UIPrefabConfigName.DataConflictWindow, "progressConflict_cloud_desc", displayData, selectLocalCallback, selectNewCallback, nil)
end

function SyncModel:_UseLocalData(response)
  self:ClearDataUploadRecord()
  GM.UIManager:ShowMask()
  local callback = function(result)
    GM.UIManager:HideMask()
    if result then
      local loginCallback
      
      function loginCallback(bSuccess, tbLoginResp, reqCtx)
        GM.UIManager:HideMask()
        if not bSuccess then
          GM.UIManager:OpenView(UIPrefabConfigName.NetworkErrorWindow, function()
            GM.UIManager:ShowMask()
            GM.GameModel:Login(loginCallback)
          end)
        end
      end
      
      GM.UIManager:ShowMask()
      GM.GameModel:Login(loginCallback)
      return
    end
    if self.eSyncState == ESyncState.VersionError then
      local title = GM.GameTextModel:GetText("sync_ver_error_title")
      local description = GM.GameTextModel:GetText("sync_ver_error_desc")
      local buttonText = GM.GameTextModel:GetText("sync_yes_button")
      GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, title, description, buttonText, nil, nil, false)
      return
    end
    local title = GM.GameTextModel:GetText("sync_error_title")
    local description = GM.GameTextModel:GetText("sync_error_desc")
    local buttonText = GM.GameTextModel:GetText("sync_yes_button")
    GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, title, description, buttonText, function()
      self:_UseLocalData(response)
    end, nil, false)
  end
  self:UploadData(callback, response.syncTime)
end

function SyncModel:_UseServerData(response)
  GM.BIManager:LogProject(EBIType.SyncModel, EBIType.SyncModelAction.UseServerData)
  GM.PropertyDataManager:PropertyFromSyncData(EPropertyType.Gold, response.user[1].gold)
  GM.PropertyDataManager:PropertyFromSyncData(EPropertyType.Gem, response.user[1].gem)
  GM.PropertyDataManager:PropertyFromSyncData(EPropertyType.SkipProp, response.user[1].skipprop)
  GM.PropertyDataManager:PropertyFromSyncData(EPropertyType.Experience, response.user[1].exp)
  GM.LevelModel:SyncLevel(tonumber(response.user[1].level))
  GM.UserProfileModel:FromAvatarAndName(response.name, response.icon)
  GM.MainBoardModel:SyncItemData(response.item)
  GM.MainBoardModel:SyncItemLayerData(response.board)
  GM.MainBoardModel:SyncItemCacheData(response.cacheItem)
  GM.MainBoardModel:SyncItemStoreData(response.inventory)
  GM.MainBoardModel:SyncOrderMetaData(response.orderMeta)
  GM.MainBoardModel:SyncOrderData(response.orders)
  GM.ShopModel:SyncShopItem(response.shopItem)
  GM.TaskManager:FromSyncData(response.mainTaskMeta, response.mainTask)
  GM.ActivityManager:SyncGeneralData(response.activity_data)
  GM.BundleManager:SyncBundleDataTable(response.bundle)
  GM.BundleManager:SyncBundleMetaDataTable(response.bundle_meta)
  for _, discardSyncKey in ipairs(arrDiscardSyncKey) do
    if not Table.IsEmpty(response[discardSyncKey]) then
      self:_SetSyncKey(discardSyncKey)
    end
  end
  for model, params in pairs(self.m_mapRegisteredModels) do
    model:FromSyncData(response[params.name])
  end
  self:SetDataInconsistent(false)
  self:SetLastSyncTime(response.syncTime)
  self:_ClearDataCrackedTag()
  GM.DBTableManager:TrySaveAll()
  self:_MarkUploaded(true)
  if GM.SceneManager:GetGameMode() ~= EGameMode.Loading then
    GM:RestartGame(nil, EBIProjectType.RestartGameAction.UseServerData)
  end
end

function SyncModel:ClearAllData()
  GM.BIManager:LogProject(EBIType.SyncModel, EBIType.SyncModelAction.Clear)
  GM.SsoManager:ThrowPendingRequests()
  GM.HttpManager:ThrowSavedRequests()
  local uuid = GM.UserModel:GetInstallUuid()
  GM.DBTableManager:Clear()
  local selectedValue = NetworkConfig.GetSelectedServer()
  local selectedSchema = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema, "")
  local installReferrerFlag = PlayerPrefs.GetInt(EPlayerPrefKey.GetInstallReferrer, 0)
  local useRemoteCodeFlag = PlayerPrefs.GetInt("use_remote_code", 0)
  PlayerPrefs.DeleteAll()
  GM.SimplePrefs:Clear()
  GM.UserModel:SetInstallUuid(uuid)
  NetworkConfig.SetSelectedServer(selectedValue)
  PlayerPrefs.SetString(EPlayerPrefKey.TestServerSchema, selectedSchema)
  PlayerPrefs.SetInt(EPlayerPrefKey.GetInstallReferrer, installReferrerFlag)
  PlayerPrefs.SetInt("use_remote_code", useRemoteCodeFlag)
  local language = LocalizationModel:GetCurLanguageInString()
  LocalizationModel:ChangeLanguageWithString(language)
  GM.DBTableManager:TrySaveAll()
end

function SyncModel:_NeedResetSyncKey(syncKey)
  return PlayerPrefs.GetInt(self:_GetResetSyncKeyPlayerPrefsKey(syncKey), 0) == 1
end

function SyncModel:_SetSyncKey(syncKey)
  PlayerPrefs.SetInt(self:_GetResetSyncKeyPlayerPrefsKey(syncKey), 1)
end

function SyncModel:_ClearResetSyncKey(syncKey)
  PlayerPrefs.DeleteKey(self:_GetResetSyncKeyPlayerPrefsKey(syncKey))
end

function SyncModel:_GetResetSyncKeyPlayerPrefsKey(syncKey)
  return "ResetSyncKey" .. tostring(syncKey)
end
