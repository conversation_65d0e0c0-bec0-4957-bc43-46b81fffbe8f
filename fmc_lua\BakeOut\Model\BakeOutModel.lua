BakeOutEventType = {
  StateChanged = 1,
  EnList = 2,
  AcquireToken = 3,
  UpdateRank = 4,
  NeedOpenWindow = 5,
  ModeChanged = 6
}
local BakeOutConfigKey = {
  RankReward = "bakeout_rank_reward",
  CoinExchange = "bakeout_rank_exchange",
  Misc = "bakeout_rank_parameter"
}
local BakeOutMiscKey = {
  DelayTime = "delayTime",
  UploadTime = "uploadTime",
  NoRegisterTime = "noRegisterTime",
  SettlementTime = "settlementTime",
  MaxNumInGroup = "maxNum",
  RetainTime = "retainTime"
}
local DBKey = {
  Rank = "rank",
  LastRank = "lastRank",
  Token = "token",
  EnList = "enList",
  ExchangeTimes = "exchangeTimes",
  CachedData = "cachedData",
  CachedDataMD5 = "cachedDataMd5",
  DelayedGetRewardTime = "delayedGetRewardTime",
  RewardAcquired = "rewardAcquired",
  FinishActivity = "FinishActivity",
  LastIncremNum = "LastIncremNum",
  GroupID = "groupId"
}
local HeartBeatKey = {
  GroupId = "bakeoutGroupId",
  Token = "bakeoutToken",
  Rank = "bakeoutRank"
}
local DBColumnValue = "value"
BakeOutModel = setmetatable({}, BaseActivityModel)
BakeOutModel.__index = BakeOutModel

function BakeOutModel:Init(virtualDBTable)
  self.m_arrWaitingOpenWindow = {}
  self:_Reset()
  local cachedConfig = virtualDBTable:GetValue(DBKey.CachedData, DBColumnValue)
  if cachedConfig ~= nil then
    self.m_cachedConfig = json.decode(cachedConfig)
  end
  BaseActivityModel.Init(self, ActivityType.BakeOut, virtualDBTable)
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self.OnMainTaskFinished)
  EventDispatcher.AddListener(EEventType.OnClaimedOrderGroupReward, self, self.OnMainTaskFinished)
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self.OnLoginFinished)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnChangeGameMode)
  EventDispatcher.AddListener(EEventType.NewChapterUnlocked, self, self.UpdatePerSecond)
  EventDispatcher.AddListener(EEventType.NewReleasedOrderGroupUnlocked, self, self.UpdatePerSecond)
end

function BakeOutModel:LateInit()
  self.m_bLateInit = true
end

function BakeOutModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function BakeOutModel:_LoadOtherServerConfig(config)
  self:_LoadCoinExchangeConfig(config[BakeOutConfigKey.CoinExchange])
  self:_LoadRankRewardConfig(config[BakeOutConfigKey.RankReward])
  self:_LoadMiscConfig(config[BakeOutConfigKey.Misc])
end

function BakeOutModel:GetBoardEntryShowConfig()
  return {
    statusChangeEvent = EEventType.BakeOutModeChanged,
    eEntryRootKey = EEntryRootKey.BakeOut,
    entryPrefabName = UIPrefabConfigName.BakeOutBubble,
    checkFun = function()
      return self:IsBakeOutModeOn()
    end
  }
end

function BakeOutModel:GetMapEntryShowConfig()
  return {
    statusChangeEvent = EEventType.BakeOutModeChanged,
    hudKey = ESceneViewHudButtonKey.BakeOut,
    eEntryRootKey = EEntryRootKey.BakeOut,
    entryPrefabName = UIPrefabConfigName.BakeOutEntry,
    checkFun = function()
      return self:IsBakeOutModeOn()
    end
  }
end

function BakeOutModel:_OnStateChanged()
  if not self.m_bLateInit then
    return
  end
  if self:IsStateReliable() and not self:CanAcquireToken() then
    local mainOrderModel = GM.MainBoardModel:GetOrderModel()
    mainOrderModel:RemoveBakeOutOrders()
  end
  self:UpdatePerSecond()
  self.event:Call(BakeOutEventType.StateChanged)
  EventDispatcher.DispatchEvent(EEventType.BakeOutStateChanged)
end

function BakeOutModel:FromHeartBeat(tbData)
  if tbData[HeartBeatKey.Rank] and tbData[HeartBeatKey.Rank] > 0 then
    self:UpdateCurRank(tbData[HeartBeatKey.Rank], true)
  end
end

function BakeOutModel:ToHeartBeat(tbData)
  if self:CanHeartBeat() then
    tbData[HeartBeatKey.Token] = self:GetToken() or 0
  else
    tbData[HeartBeatKey.Token] = 0
  end
end

function BakeOutModel:IsSendingRankRequest()
  return self.m_bIsSendingRequest
end

function BakeOutModel:CanHeartBeat()
  if not self:IsBakeOutModeOn() then
    return false
  end
  return self:IsEnlist() and (self:GetState() == ActivityState.Started or self:GetState() == ActivityState.Ended and GM.GameModel:GetServerTime() < self:_GetUploadTokenFinishedTime())
end

function BakeOutModel:OnMainTaskFinished()
  if self:IsBakeOutModeOn() then
    self.event:Call(BakeOutEventType.StateChanged)
    GM.HeartBeatManager:TrySchedule()
  end
end

function BakeOutModel:OnLoginFinished(msg)
  if self.m_arrFinalRankInfos == nil and msg.bSuccess then
    local serverTime = GM.GameModel:GetServerTime()
    if self:IsEnlist() and self.m_config ~= nil and serverTime > self.m_config.rTime then
      if serverTime <= self:_GetRetainFinishedTime() and self:_GetGroupId() ~= "" then
        self:OnActivityFinished()
      else
        if not self:HasAcquiredReward() then
          GM.BIManager:LogErrorInfo(EBIType.BakeOutGetRewardsError, "can't get bakeout info after rTime at id " .. self.m_config.id)
        end
        self:_ClearActivity(true)
      end
    end
  end
end

function BakeOutModel:OnChangeGameMode()
  if GM.SceneManager:GetGameMode() == EGameMode.Board and self:CanHeartBeat() then
    self:_HeartBeatOnNextFrame()
  end
end

function BakeOutModel:_Reset()
  self.m_arrFinalRankInfos = nil
  self.m_cachedConfig = nil
  self.m_bIsSendingRequest = nil
  self.m_arrGetBakeOutRankCallback = {}
  self.m_config = nil
  self.m_bInActivityFinishedRequest = nil
  self.m_bIsSendingRequest = nil
  self.m_arrLastRankDatas = nil
  self.m_bExchangeCoinFree = nil
  self.m_bAddedResultWindowToChain = nil
  self.m_bActivityFinishRequested = nil
end

function BakeOutModel:_ClearActivity(reloadConfig)
  self:_Reset()
  self:_DropData()
  if reloadConfig then
    self:LoadServerConfig()
  end
  self.event:Call(BakeOutEventType.StateChanged)
end

function BakeOutModel:IsBakeOutModeOn()
  if not (self.m_config ~= nil and self.m_bLateInit) or self:HasActivityFinished() or self:GetState() == ActivityState.Preparing or not self:IsStateReliable() then
    return false
  end
  if not self:IsEnlist() and not self:CanEnlist() then
    return false
  end
  if self:IsEnlist() and self:GetState() == ActivityState.Started and not self:IsAllFinished() then
    return false
  end
  if self:GetState(false) == ActivityState.Released and GM.GameModel:GetServerTime() > self:_GetRetainFinishedTime() then
    return false
  end
  return true
end

function BakeOutModel:IsAllFinished()
  return GM.MainBoardModel:GetOrderModel():IsAllCanUnlockGroupFinished() and not GM.TaskManager:CanFinishOngoingTask()
end

function BakeOutModel:CanAcquireToken()
  local modeOn = self:IsBakeOutModeOn()
  return modeOn and (self:IsEnlist() and self:GetState() == ActivityState.Started or self:CanEnlist()), modeOn
end

function BakeOutModel:CanAcquireReward()
  return self:IsEnlist() and self:GetToken() > 0 and self:GetState(false) == ActivityState.Released and self:GetFinalRankInfos() ~= nil
end

function BakeOutModel:FinishActivity()
  self.m_dbTable:Set(DBKey.FinishActivity, DBColumnValue, 1)
end

function BakeOutModel:HasActivityFinished()
  return self.m_dbTable:GetValue(DBKey.FinishActivity, DBColumnValue) == 1
end

function BakeOutModel:SetRewardAcquired()
  self.m_dbTable:Set(DBKey.RewardAcquired, DBColumnValue, 1)
end

function BakeOutModel:HasAcquiredReward()
  return self.m_dbTable:GetValue(DBKey.RewardAcquired, DBColumnValue) == 1
end

function BakeOutModel:SetLastIncremNum(num)
  self.m_dbTable:Set(DBKey.LastIncremNum, DBColumnValue, num)
end

function BakeOutModel:GetLastIncremNum()
  return self.m_dbTable:GetValue(DBKey.LastIncremNum, DBColumnValue) or 0
end

function BakeOutModel:OnCheckResourcesFinished()
  if self:CanAcquireToken() then
    self:GetBakeOutRanks(nil, false)
  end
end

function BakeOutModel:UpdatePerSecond()
  if not GM.CheckResourcesStageFinished and GameConfig.IsTestMode() then
    Log.Error("BakeOutModel:UpdatePerSecond before CheckResourcesStageFinished !!!")
  end
  self:_UpdateState()
  if self:GetState(false) == ActivityState.Released and GM.GameModel:GetServerTime() <= self:_GetRetainFinishedTime() and self:GetFinalRankInfos() then
    self:OnActivityFinished()
  end
  local canAcquireToken, curModeOn = self:CanAcquireToken()
  if self.m_lastCanAcquireToken ~= canAcquireToken then
    if not canAcquireToken and self:IsStateReliable() then
      local mainOrderModel = GM.MainBoardModel:GetOrderModel()
      mainOrderModel:RemoveBakeOutOrders()
    end
    self.m_lastCanAcquireToken = canAcquireToken
    Log.Info("BakeOut CanAcquireToken changed:" .. tostring(canAcquireToken))
  end
  if self.m_lastModeOn ~= curModeOn then
    self.event:Call(BakeOutEventType.ModeChanged)
    EventDispatcher.DispatchEvent(EEventType.BakeOutModeChanged)
    self.m_lastModeOn = curModeOn
    Log.Info("BakeOut ModeOn changed:" .. tostring(curModeOn))
  end
end

function BakeOutModel:LoadServerConfig()
  local config = GM.ConfigModel:GetServerConfig(self.m_type)
  if config == nil then
    if self.m_cachedConfig ~= nil then
      self:_LoadConfig(self.m_cachedConfig)
    else
      self:_LoadConfig()
    end
  elseif self.m_cachedConfig == nil then
    self:_LoadConfig(config)
  elseif config.id ~= self.m_cachedConfig.id then
    self:_LoadConfig(self.m_cachedConfig)
  else
    self:_LoadConfig(config)
  end
end

function BakeOutModel:_LoadConfig(config)
  if config == nil then
    self.m_config = nil
    return
  end
  if self.m_cachedConfig ~= config then
    local configMD5 = GM.ConfigModel:GetServerConfigMD5(ActivityType.BakeOut)
    if configMD5 ~= "" and (self.m_cachedConfig == nil or self.m_dbTable:GetValue(DBKey.CachedDataMD5, DBColumnValue) ~= configMD5) then
      self.m_dbTable:Set(DBKey.CachedData, DBColumnValue, json.encode(config))
      self.m_dbTable:Set(DBKey.CachedDataMD5, DBColumnValue, configMD5)
      if self.m_cachedConfig == nil or config[BakeOutConfigKey.Misc][1][BakeOutMiscKey.DelayTime] ~= self.m_cachedConfig[BakeOutConfigKey.Misc][1][BakeOutMiscKey.DelayTime] then
        local curDelayTime = self.m_dbTable:GetValue(DBKey.DelayedGetRewardTime, DBColumnValue)
        if curDelayTime == nil or curDelayTime > config[BakeOutConfigKey.Misc][1][BakeOutMiscKey.DelayTime] then
          local randomDelayTime = math.random(config[BakeOutConfigKey.Misc][1][BakeOutMiscKey.DelayTime])
          self.m_dbTable:Set(DBKey.DelayedGetRewardTime, DBColumnValue, randomDelayTime)
          GM.BIManager:LogAction(EBIType.BakeOutRandomDelayTime, {cur = curDelayTime, rand = randomDelayTime})
        end
      end
      self.m_cachedConfig = config
    end
  end
  self.m_config = {}
  self.m_config.id = config.id
  self.m_config.sTime = config.sTime
  self.m_config.eTime = config.eTime
  self.m_config.rTime = config.rTime
  self:_LoadOtherServerConfig(config)
  self:_CheckId()
  self:_UpdateState()
end

function BakeOutModel:_UpdateState()
  BaseActivityModel._UpdateState(self)
  if self.m_config ~= nil and self:IsStateReliable() then
    if self:IsEnlist() and self:GetToken() > 0 then
      if self:GetState(false) == ActivityState.Ended and not self:HasWindowOpenedOnce(self.m_state) then
        self:_OnSettlement()
      elseif self:GetState(false) == ActivityState.Released then
        if GM.GameModel:GetServerTime() > self:_GetRetainFinishedTime() then
          local config = GM.ConfigModel:GetServerConfig(self.m_type)
          self:_ClearActivity(config ~= nil and config.id ~= self.m_config.id)
        elseif not self:HasWindowOpenedOnce(self:GetState(false)) then
          self:OnActivityFinished()
        end
      end
    elseif self:GetState(false) == ActivityState.Started then
      if not self:HasWindowOpenedOnce(self.m_state) then
        self:OnReadyStart()
      end
    elseif self:GetState(false) == ActivityState.Released or self:GetState(false) == ActivityState.Ended then
      if not self:HasActivityFinished() then
        self:SetRewardAcquired()
        self:FinishActivity()
        if not self:HasWindowOpenedOnce(self:GetState(false)) and GM.SceneManager:GetGameMode() ~= EGameMode.Loading then
          self:SetWindowOpened()
        end
        self:TryUpdateHint()
      end
      if GM.GameModel:GetServerTime() > self.m_config.rTime then
        local config = GM.ConfigModel:GetServerConfig(self.m_type)
        self:_ClearActivity(config ~= nil and config.id ~= self.m_config.id)
      end
    end
  end
end

function BakeOutModel:NeedCheckResource()
  return self.m_config ~= nil and self.m_bLateInit and (self:IsEnlist() or self:CanEnlist())
end

function BakeOutModel:_CalculateState()
  if self.m_config == nil then
    return ActivityState.Released, nil
  end
  local serverTime = GM.GameModel:GetServerTime()
  if serverTime < self.m_config.sTime then
    return ActivityState.Preparing, self.m_config.sTime
  elseif serverTime < self.m_config.eTime then
    return ActivityState.Started, self.m_config.eTime
  elseif serverTime < self:_GetSettlementFinishedTime() then
    return ActivityState.Ended, self:_GetSettlementFinishedTime()
  else
    return ActivityState.Released, nil
  end
end

function BakeOutModel:_GetSettlementFinishedTime()
  if self.m_config == nil then
    return 0
  end
  return math.min(self.m_config.rTime, self.m_config.eTime + self:_GetMiscConfig(BakeOutMiscKey.UploadTime) + self:_GetMiscConfig(BakeOutMiscKey.SettlementTime) + (self.m_dbTable:GetValue(DBKey.DelayedGetRewardTime, DBColumnValue) or 0))
end

function BakeOutModel:_GetUploadTokenFinishedTime()
  return math.min(self.m_config.rTime, self.m_config.eTime + self:_GetMiscConfig(BakeOutMiscKey.UploadTime))
end

function BakeOutModel:_GetRetainFinishedTime()
  if self.m_config == nil then
    return 0
  end
  return self.m_config.rTime + self:_GetMiscConfig(BakeOutMiscKey.RetainTime)
end

function BakeOutModel:OnReadyStart()
  if not (not self:IsEnlist() and self:IsBakeOutModeOn()) or self:HasWindowOpenedOnce(ActivityState.Started) then
    return
  end
  if self:GetState() == ActivityState.Started then
    self:SetWindowOpened()
  end
  self.m_arrWaitingOpenWindow[#self.m_arrWaitingOpenWindow + 1] = {
    name = UIPrefabConfigName.BakeOutReadyWindow
  }
  self.event:Call(BakeOutEventType.NeedOpenWindow)
end

function BakeOutModel:_OnSettlement()
  if GM.SceneManager:GetGameMode() == EGameMode.Loading then
    return
  end
  if GM.GameModel:GetServerTime() <= self.m_config.eTime + 30 then
    DelayExecuteFunc(function()
      if self:GetState(false) == ActivityState.Ended then
        GM.GameModel:Login()
        if self:CanHeartBeat() then
          self:GetBakeOutRanks(nil, false)
        end
      end
    end, GM.UserModel:GetUserId() % 30)
  else
    GM.GameModel:Login()
    if self:CanHeartBeat() then
      self:GetBakeOutRanks(nil, false)
    end
  end
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    self:SetWindowOpened()
    return
  end
  self.event:Call(BakeOutEventType.StateChanged)
  self:SetWindowOpened()
  self.m_arrWaitingOpenWindow[#self.m_arrWaitingOpenWindow + 1] = {
    name = UIPrefabConfigName.BakeOutInSettlementWindow
  }
  self.event:Call(BakeOutEventType.NeedOpenWindow)
end

function BakeOutModel:OnActivityFinished(userClick)
  if self.m_bInActivityFinishedRequest then
    return
  end
  if GM.SceneManager:GetGameMode() == EGameMode.Loading then
    return
  end
  if self.m_arrFinalRankInfos ~= nil then
    if not self:HasWindowOpenedOnce(self.m_state) and self.m_bAddedResultWindowToChain ~= true then
      self.m_bAddedResultWindowToChain = true
      self.m_arrWaitingOpenWindow[#self.m_arrWaitingOpenWindow + 1] = {
        name = UIPrefabConfigName.BakeOutResultWindow
      }
      if GM.SceneManager:GetGameMode() == EGameMode.Main or userClick then
        self.event:Call(BakeOutEventType.NeedOpenWindow)
      end
    end
    return
  end
  if not userClick and self.m_bActivityFinishRequested then
    return
  end
  self.m_bActivityFinishRequested = true
  local startTime = NetTimeStamp.Create(EBIType.NetworkCheckAction.StartBakeOut)
  GM.BIManager:LogNet(EBIType.NetworkCheckAction.StartBakeOut)
  self.m_bInActivityFinishedRequest = true
  GM.UIManager:ShowTestPrompt("bakeout 发送结算请求")
  RedisMessage.QueryBakeOutRank(self:_GetGroupId(), function(bSuccess, tbResp, reqCtx)
    self.m_bInActivityFinishedRequest = false
    local timeInterval = startTime:EndAndGetDur()
    if bSuccess and tbResp.rcode == 0 and 0 < #tbResp.ranks then
      self:_SetFinalRankInfos(tbResp.ranks)
      self:OnActivityFinished(userClick)
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.BakeOutSuccess, nil, reqCtx, timeInterval)
    else
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.BakeOutFailed, nil, reqCtx, timeInterval)
      if userClick then
        GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("bakeout_offline_title"), GM.GameTextModel:GetText("bakeout_offline_tip"), GM.GameTextModel:GetText("bakeout_offline_button"), nil, nil, true)
      end
    end
  end)
end

function BakeOutModel:AcquireReward(callback)
  if not self:CanAcquireReward() then
    if callback ~= nil then
      callback()
    end
    return
  end
  local rank = self:GetCurRank()
  local rewards = self:GetRankReward(rank)
  if not rewards or rewards.rewards == nil then
    Log.Error("Bakeout rank:" .. tostring(rank) .. " 没有配置奖励")
    rewards = {}
    rewards.rewards = {
      {
        [PROPERTY_TYPE] = EPropertyType.Gem,
        [PROPERTY_COUNT] = 1,
        [PROPERTY_CRYPT] = Crypt.CryptCurrency(1)
      }
    }
  end
  if rewards ~= nil and rewards.rewards ~= nil then
    if not self:HasAcquiredReward() then
      RewardApi.AcquireRewardsLogic(rewards.rewards, EPropertySource.Give, EBIType.BakeOutGetRewards, EGameMode.Board, CacheItemType.Stack)
      self:SetRewardAcquired()
    end
    GM.DBTableManager:TrySaveAll()
    GM.UIManager:ShowMask()
    GM.SyncModel:UploadData(function(bSuccess)
      GM.UIManager:HideMask()
      if bSuccess then
        local finalRewards = self:GetFinalRankInfos()
        GM.UIManager:OpenViewWhenIdle(UIPrefabConfigName.BakeOutTopThreeWindow, finalRewards, self:GetId())
        self:FinishActivity()
        self:_ClearActivity(true)
        GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, rewards.rewards, "rewards_window_title_normal", true)
      else
        GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("bakeout_offline_title"), GM.GameTextModel:GetText("bakeout_collectfail_tip"), GM.GameTextModel:GetText("bakeout_offline_button"), function()
          self:AcquireReward()
        end, nil, true)
      end
      if callback ~= nil then
        callback()
      end
    end)
  elseif callback ~= nil then
    callback()
  end
end

function BakeOutModel:TryUpdateHint()
  if GM.SceneManager:GetGameMode() == EGameMode.Loading then
    return
  end
  local config = GM.ConfigModel:GetServerConfig(self.m_type)
  if config ~= nil and config.id ~= self.m_config.id then
    return
  end
  if not self:IsAllFinished() then
    return
  end
  if not GM.UpdateHintModel:NeedOpenWindow(true) then
    return
  end
  GM.UpdateHintModel:SetHasPopupWindow(false)
  GM.UpdateHintModel:OpenUpdateHintWindow(true)
end

function BakeOutModel:GetFinalRankInfos()
  return self.m_arrFinalRankInfos
end

function BakeOutModel:_SetFinalRankInfos(arrRankInfos)
  self.m_arrFinalRankInfos = arrRankInfos
  self:_SequenceRankDatas(arrRankInfos)
  local rankData = self:GetOwnDataFromRanksData(arrRankInfos)
  if rankData ~= nil then
    self:UpdateCurRank(rankData.rank)
  end
end

function BakeOutModel:_GetGroupId()
  return self.m_dbTable:GetValue(DBKey.GroupID, DBColumnValue) or ""
end

function BakeOutModel:_SetGroupId(groupId)
  self.m_dbTable:Set(DBKey.GroupID, DBColumnValue, groupId)
end

function BakeOutModel:_LoadMiscConfig(config)
  if config == nil then
    GM.BIManager:LogErrorInfo(EBIType.BakeOutConfigError, "empty config in parameters")
  end
  self.m_miscConfig = config and config[1] or Table.Empty
end

function BakeOutModel:_GetMiscConfig(miscConfigKey)
  return self.m_miscConfig and self.m_miscConfig[miscConfigKey] or 0
end

function BakeOutModel:GetMaxNumInGroup()
  return self:_GetMiscConfig(BakeOutMiscKey.MaxNumInGroup)
end

function BakeOutModel:GetWaitingOpenWindowDatas()
  return self.m_arrWaitingOpenWindow
end

local bakeOutOnLabels = {
  AddressableLabel.BakeOut
}

function BakeOutModel:GetResourceLabels()
  return bakeOutOnLabels
end

function BakeOutModel:IsEnlist()
  return self.m_dbTable:GetValue(DBKey.EnList, DBColumnValue) == 1
end

function BakeOutModel:CanEnlist()
  return self.m_config ~= nil and GM.GameModel:GetServerTime() + self:_GetMiscConfig(BakeOutMiscKey.NoRegisterTime) < (self.m_config and self.m_config.eTime or 0) and self:IsAllFinished()
end

function BakeOutModel:OnEnlist(fromReadyWindow)
  if self:IsEnlist() then
    return false
  end
  if not self:CanEnlist() then
    return false
  end
  self:GetBakeOutRanks(function(bSuccess, tbResp)
    if bSuccess and tbResp.rcode == 0 then
      self.m_dbTable:Set(DBKey.EnList, DBColumnValue, 1)
      self.event:Call(BakeOutEventType.EnList)
      if fromReadyWindow then
        if GM.SceneManager:GetGameMode() == EGameMode.Board then
          GM.SceneManager:ChangeGameMode(EGameMode.Main)
        end
        if GM.SceneManager:GetGameMode() == EGameMode.Main and not GM.TimelineManager:IsPlayingTimeline() then
          GM.TimelineManager:PlayTimeline(self:_GetEnlistTimeline(), nil, function()
            GM.UIManager:OpenView(UIPrefabConfigName.BakeOutMainWindow)
          end)
        end
      else
        GM.UIManager:OpenView(UIPrefabConfigName.BakeOutMainWindow, nil, true)
      end
      GM.HeartBeatManager:TrySchedule()
    else
      if GameConfig.IsTestMode() then
        GM.UIManager:ShowTestPrompt("Enlist error " .. (bSuccess and "rcode " .. tostring(tbResp.rcode) or tbResp))
      end
      GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("bakeout_offline_title"), GM.GameTextModel:GetText("bakeout_offline_tip"), GM.GameTextModel:GetText("bakeout_offline_button"), function()
        self:OnEnlist(fromReadyWindow)
      end, nil, true)
    end
  end, true)
end

function BakeOutModel:_GetEnlistTimeline()
  return "bakeout_intro"
end

function BakeOutModel:GetToken()
  return self.m_dbTable:GetValue(DBKey.Token, DBColumnValue) or 0
end

function BakeOutModel:AcquireToken(tokenNum)
  self.m_dbTable:Set(DBKey.Token, DBColumnValue, self:GetToken() + tokenNum)
  Scheduler.Unschedule(self._HeartBeatOnNextFrame, self)
  DelayExecuteFunc(self._HeartBeatOnNextFrame, self:_GetAcquireTokenUploadDelay(), self)
  self:_Try2UpdateDisplayRank()
  self.event:Call(BakeOutEventType.AcquireToken)
  EventDispatcher.DispatchEvent(EEventType.CollectGold, tokenNum)
end

function BakeOutModel:_HeartBeatOnNextFrame()
  GM.HeartBeatManager:HeartBeatOnNextFrame(false)
end

function BakeOutModel:_GetAcquireTokenUploadDelay()
  local removeItemDt = BaseSceneBoardView.finishOrderRemoveItemDt
  local flyDt = MAX_BAKEOUT_TOKEN_FLY_TIME
  return math.min(removeItemDt + flyDt, self:_GetMiscConfig(BakeOutMiscKey.UploadTime) / 2)
end

function BakeOutModel:_GetExchangeTimes()
  return self.m_dbTable:GetValue(DBKey.ExchangeTimes, DBColumnValue) or 0
end

function BakeOutModel:_AddExchangeTimes()
  self.m_dbTable:Set(DBKey.ExchangeTimes, DBColumnValue, self:_GetExchangeTimes() + 1)
end

function BakeOutModel:GetExchangeCoinCostNum()
  return self.m_bExchangeCoinFree and 0 or self.m_arrCoinExchangeCost[math.min(self:_GetExchangeTimes() + 1, #self.m_arrCoinExchangeCost)]
end

function BakeOutModel:GetExchangeTokenGainNum()
  return 10
end

function BakeOutModel:CanExchangeCoin()
  return self:IsBakeOutModeOn() and GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gold) >= self:GetExchangeCoinCostNum()
end

function BakeOutModel:ExchangeCoin2Token()
  if not self:CanExchangeCoin() then
    return false
  end
  local coinCost = self:GetExchangeCoinCostNum()
  GM.PropertyDataManager:Consume(EPropertyType.Gold, coinCost, EBIType.BakeOutExchangeCoin, "bakeOutExchange")
  local leftCleanCoin = GM.TaskManager:GetCleanGoldCount()
  GM.TaskManager:ConsumeTaskCleanGold(math.min(leftCleanCoin, coinCost))
  local count = self:GetExchangeTokenGainNum()
  RewardApi.AcquireRewardsLogic({
    {
      [PROPERTY_TYPE] = EPropertyType.BakeOutToken,
      [PROPERTY_COUNT] = count,
      [PROPERTY_CRYPT] = Crypt.CryptCurrency(count)
    }
  }, EPropertySource.Give, EBIType.BakeOutExchangeCoin)
  if not self.m_bExchangeCoinFree then
    self:_AddExchangeTimes()
  end
  self.m_bExchangeCoinFree = false
  EventDispatcher.DispatchEvent(EEventType.BakeOutCoinExchanged)
  return true
end

function BakeOutModel:_LoadCoinExchangeConfig(config)
  self.m_arrCoinExchangeCost = {}
  if config == nil then
    GM.BIManager:LogErrorInfo(EBIType.BakeOutConfigError, "empty config in coin exchange")
    self.m_arrCoinExchangeCost[1] = self:GetExchangeTokenGainNum()
    return
  end
  for _, data in ipairs(config) do
    self.m_arrCoinExchangeCost[data.time] = data.cost
  end
end

function BakeOutModel:GetCurRank()
  return self.m_dbTable:GetValue(DBKey.Rank, DBColumnValue)
end

function BakeOutModel:GetDisplayCurRank()
  return self.m_displayCurRank or self:GetCurRank()
end

function BakeOutModel:SetCurRank(rank)
  self.m_dbTable:Set(DBKey.Rank, DBColumnValue, rank)
end

function BakeOutModel:UpdateCurRank(curRank, animated)
  self:SetCurRank(curRank)
  self.m_displayCurRank = nil
  self.event:Call(BakeOutEventType.UpdateRank, animated)
end

function BakeOutModel:TryUpdateDisplayRank()
  if self.m_displayCurRank then
    self.event:Call(BakeOutEventType.UpdateRank, true)
  end
end

function BakeOutModel:UpdateLastRank(lastRank)
  self.m_dbTable:Set(DBKey.LastRank, DBColumnValue, lastRank)
end

function BakeOutModel:GetLastRank()
  return self.m_dbTable:GetValue(DBKey.LastRank, DBColumnValue)
end

function BakeOutModel:_Try2UpdateDisplayRank()
  if self.m_arrLastRankDatas == nil then
    self.m_displayCurRank = nil
    return
  end
  local ownCellData = self:GetOwnDataFromRanksData(self.m_arrLastRankDatas)
  if ownCellData == nil then
    self.m_displayCurRank = nil
    return
  end
  self.m_displayCurRank = ownCellData.rank
  local curToken = self:GetToken()
  if ownCellData.rank > 1 then
    local index = ownCellData.rank
    while 1 < index and curToken >= self.m_arrLastRankDatas[index - 1].score do
      index = index - 1
    end
    self.m_displayCurRank = math.max(1, index)
  end
end

function BakeOutModel:GetRankReward(rank)
  return self:_GetRankRewardDatas()[rank]
end

function BakeOutModel:GetMaxRankWithRankReward()
  return #self:_GetRankRewardDatas()
end

function BakeOutModel:_GetRankRewardDatas()
  return self.m_arrRankReward
end

function BakeOutModel:_LoadRankRewardConfig(config)
  self.m_arrRankReward = {}
  if config == nil then
    GM.BIManager:LogErrorInfo(EBIType.BakeOutConfigError, "empty config in rank reward")
    return
  end
  local rankReward
  for _, data in ipairs(config) do
    local arrRewards = data.rewards
    if arrRewards and IsString(arrRewards[1]) then
      arrRewards = ConfigUtil.GetCurrencyFromArrStr(data.rewards)
    else
      RewardApi.CryptRewards(arrRewards, true)
    end
    rankReward = {
      rewards = arrRewards,
      rewardsRank = data.start_rank
    }
    for i = data.start_rank, data.end_rank do
      self.m_arrRankReward[i] = rankReward
    end
  end
end

function BakeOutModel:GetBakeOutRanks(callback, bShowMask)
  if self.m_arrGetBakeOutRankCallback == nil then
    self.m_arrGetBakeOutRankCallback = {}
  end
  if self:IsSendingRankRequest() then
    self.m_arrGetBakeOutRankCallback[#self.m_arrGetBakeOutRankCallback + 1] = callback
    return
  end
  self.m_getBakeOutRankStartTime = NetTimeStamp.Create(EBIType.NetworkCheckAction.StartBakeOut)
  GM.BIManager:LogNet(EBIType.NetworkCheckAction.StartBakeOut)
  self.m_arrGetBakeOutRankCallback[#self.m_arrGetBakeOutRankCallback + 1] = callback
  if self.m_getBakeOutRankcallbackFunc == nil then
    function self.m_getBakeOutRankcallbackFunc(bSuccess, tbResp, reqCtx)
      self.m_bIsSendingRequest = false
      
      local timeInterval = self.m_getBakeOutRankStartTime:EndAndGetDur()
      if bSuccess and tbResp.rcode == 0 then
        self:_UpdateRankInfo(tbResp)
        self:_SequenceRankDatas(tbResp.ranks)
        self.m_arrLastRankDatas = tbResp.ranks
        GM.BIManager:LogNet(EBIType.NetworkCheckAction.BakeOutSuccess, nil, reqCtx, timeInterval)
      else
        GM.BIManager:LogNet(EBIType.NetworkCheckAction.BakeOutFailed, nil, reqCtx, timeInterval)
      end
      for i = #self.m_arrGetBakeOutRankCallback, 1, -1 do
        self.m_arrGetBakeOutRankCallback[i](bSuccess, tbResp)
        table.remove(self.m_arrGetBakeOutRankCallback, i)
      end
    end
  end
  self.m_bIsSendingRequest = true
  GM.BIManager:LogAction(EBIType.BakeOutGetBakeOutRanks, {
    st = self.m_config and self.m_config.sTime,
    et = self.m_config and self.m_config.eTime,
    rt = self:_GetSettlementFinishedTime(),
    dt = self.m_dbTable:GetValue(DBKey.DelayedGetRewardTime, DBColumnValue),
    ct = GM.GameModel:GetServerTime()
  })
  RedisMessage.GetBakeOutRank(self.m_type, self:GetId(), self:GetToken(), self.m_getBakeOutRankcallbackFunc, bShowMask)
  GM.UIManager:ShowTestPrompt("发送排行榜请求")
end

function BakeOutModel:GetOwnDataFromRanksData(arrRanks)
  local userid = GM.UserModel:GetUserId()
  for i = 1, #arrRanks do
    if arrRanks[i].userid == userid then
      return arrRanks[i], i
    end
  end
  return nil
end

function BakeOutModel:_UpdateRankInfo(tbResp)
  self:_SetGroupId(tbResp.group_id)
  local userId = GM.UserModel:GetUserId()
  local rankData = self:GetOwnDataFromRanksData(tbResp.ranks)
  if rankData ~= nil then
    self:UpdateCurRank(rankData.rank)
  end
end

function BakeOutModel:_SequenceRankDatas(arrRanks)
  self:_SortRankArr(arrRanks)
  self:_UpdateSelfProfile(self:GetOwnDataFromRanksData(arrRanks))
end

function BakeOutModel:_SortRankArr(arrRanks)
  local userid = GM.UserModel:GetUserId()
  table.sort(arrRanks, function(rankInfoA, rankInfoB)
    if rankInfoA.rank == rankInfoB.rank then
      if rankInfoA.userid ~= userid and rankInfoB.userid ~= userid then
        return rankInfoA.userid < rankInfoB.userid
      elseif rankInfoA.userid == userid and rankInfoB.userid ~= userid then
        return true
      elseif rankInfoA.userid ~= userid and rankInfoB.userid == userid then
        return false
      else
        return false
      end
    else
      return rankInfoA.rank < rankInfoB.rank
    end
  end)
end

function BakeOutModel:_UpdateSelfProfile(myRankData)
  if myRankData and myRankData.userid == GM.UserModel:GetUserId() then
    myRankData.name = GM.UserProfileModel:GetName() or myRankData.name
    myRankData.icon = GM.UserProfileModel:GetIcon() or myRankData.icon
  end
end

function BakeOutModel:SetExchangeCoinFree()
  self.m_bExchangeCoinFree = true
end

function BakeOutModel:GetDisplayName(name)
  if not name or type(name) ~= "string" or #name <= 0 then
    return ""
  end
  local curByte
  local i = 1
  local byteCount
  local limitCount = 0
  while true do
    curByte = string.byte(name, i)
    if 239 < curByte then
      byteCount = 4
      limitCount = limitCount + 2
    elseif 223 < curByte then
      byteCount = 3
      limitCount = limitCount + 2
    elseif 128 < curByte then
      byteCount = 2
      limitCount = limitCount + 2
    else
      byteCount = 1
      limitCount = limitCount + 1
    end
    if 8 < limitCount then
      break
    end
    i = i + byteCount
    if i > #name then
      break
    end
  end
  if 8 < limitCount then
    return string.sub(name, 0, i - 1) .. "..."
  end
  return name
end

function BakeOutModel.GetButtonTarget()
  if GM.SceneManager:GetGameMode() == EGameMode.Board then
    local boardView = MainBoardView.GetInstance()
    if boardView ~= nil then
      local orderArea = boardView:GetOrderArea()
      if orderArea ~= nil then
        return orderArea:GetIconAreaByActivityType(ActivityType.BakeOut)
      end
    end
  elseif GM.SceneManager:GetGameMode() == EGameMode.Main then
    return TutorialHelper.GetHudButton(ESceneViewHudButtonKey.BakeOut)
  end
  return nil
end
