BlindChestBoardEntry = {}
BlindChestBoardEntry.__index = BlindChestBoardEntry

function BlindChestBoardEntry:Awake()
  self.m_bAwaked = true
  if self.m_bWaitAddListener then
    self:_AddListeners()
  end
end

function BlindChestBoardEntry:Init(model, orderArea)
  self.m_activityType = model:GetType()
  self.m_model = model
  self.m_activityDefinition = BlindChestDefinition[self.m_activityType]
  self.m_orderArea = orderArea
  self.m_iconArea:Init(self.m_activityDefinition.ActivityTokenPropertyType)
  self.m_iconArea:SetInBoardView()
  if self.m_bAwaked then
    self:_AddListeners()
  else
    self.m_bWaitAddListener = true
  end
  self:UpdatePerSecond()
  self:_UpdateRedDot()
  self:_OnTurnChanged()
end

function BlindChestBoardEntry:_AddListeners()
  EventDispatcher.AddListener(self.m_activityDefinition.KeyChangedEvent, self, self._OnKeyChanged)
  EventDispatcher.AddListener(self.m_activityDefinition.TurnChangedEvent, self, self._OnTurnChanged)
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._UpdateRedDot)
end

function BlindChestBoardEntry:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BlindChestBoardEntry:UpdatePerSecond()
  local nextStateTime = self.m_model and self.m_model:GetNextStateTime()
  if nextStateTime ~= nil then
    local delta = math.max(0, nextStateTime - GM.GameModel:GetServerTime())
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function BlindChestBoardEntry:_OnStateChanged()
  if not self.m_model or self.m_model:GetState() == ActivityState.Released then
    return
  end
  self:_UpdateRedDot()
  self:_OnTurnChanged()
end

function BlindChestBoardEntry:_OnKeyChanged(delta)
  if delta ~= nil and delta < 0 then
    self:_UpdateRedDot()
  end
end

function BlindChestBoardEntry:_OnTurnChanged()
  local turn = self.m_model:GetTurn() - 1
  local maxTurn = self.m_model:GetCurRoundTurnNum()
  self.m_slider.value = maxTurn ~= 0 and turn / maxTurn or 0
  self.m_progressText.text = turn .. "/" .. maxTurn
end

function BlindChestBoardEntry:_UpdateRedDot()
  self.m_keyCount = self.m_model:GetKeyCount()
  if self.m_keyCount == 0 then
    self.m_exclamationGo:SetActive(false)
  else
    self.m_exclamationGo:SetActive(true)
    self.m_countText.text = self.m_keyCount
  end
end

function BlindChestBoardEntry:OnMaskClicked()
  self.m_model:TryOpenView()
end

function BlindChestBoardEntry:GetIconArea()
  return self.m_iconArea
end

BlindChestHudButton = setmetatable({}, HudPropertyButton)
BlindChestHudButton.__index = BlindChestHudButton

function BlindChestHudButton:UpdateValueText()
  local num = math.floor(self.m_value + 0.5)
  if self.m_valueText then
    self.m_valueText.text = num
  end
  if 0 < num and self.m_exclamationGo ~= nil then
    UIUtil.SetActive(self.m_exclamationGo, true)
  end
end
