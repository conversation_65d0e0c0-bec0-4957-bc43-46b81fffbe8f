TaskStarter = {}
TaskStarter.__index = TaskStarter

function TaskStarter.Create(taskData)
  local starter = setmetatable({}, TaskStarter)
  starter:_Init(taskData)
  return starter
end

function TaskStarter:_Init(taskData)
  self.m_taskData = taskData
  self.m_condition = nil
  if taskData.StartConditions then
    self.m_condition = TaskStartCondition.Create(taskData, taskData.StartConditions)
  end
end

function TaskStarter:GetTaskId()
  return self.m_taskData.Id
end

function TaskStarter:GetCondition()
  return self.m_condition
end

function TaskStarter:IsAllConditionSatisfied()
  return not self.m_condition or self.m_condition:IsSatisfied()
end
