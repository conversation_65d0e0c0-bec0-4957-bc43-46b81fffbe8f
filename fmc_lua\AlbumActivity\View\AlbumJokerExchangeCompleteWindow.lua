AlbumJokerExchangeCompleteWindow = setmetatable({}, AlbumActivityBaseWindow)
AlbumJokerExchangeCompleteWindow.__index = AlbumJokerExchangeCompleteWindow

function AlbumJokerExchangeCompleteWindow:Init(activityType, cardId)
  AlbumActivityBaseWindow.Init(self, activityType)
  self.m_cardId = cardId
  self.m_cardInfo = self.m_model:GetCardInfo(cardId)
  self.m_num = self.m_model:GetCardCount(cardId, true)
  local bRed = self.m_num == 1
  self.m_cardLuaTable:Init(cardId, self.m_model, bRed, false, nil)
end

function AlbumJokerExchangeCompleteWindow:OnCloseBtnClick()
  if self.m_bClosed then
    return
  end
  self:Close()
  DelayExecuteFuncInView(function()
    local listGroup = self.m_model:GetCardSets()
    local groupIndex = 0
    for i, v in ipairs(listGroup) do
      if v.setId == self.m_cardInfo.setId then
        groupIndex = i
      end
    end
    self.m_groupIndex = groupIndex
    local mainWindow = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
    local cell = mainWindow and mainWindow:GetSetCell(groupIndex)
    if mainWindow == nil or cell == nil then
      return
    end
    mainWindow:FlyCard2SetCell(self.m_cardRect.gameObject, self.m_cardId, self.m_groupIndex)
    UIUtil.SetActive(self.m_cardRect.gameObject, false)
  end, 0.04, self)
end
