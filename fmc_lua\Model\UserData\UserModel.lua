UserModel = {}
UserModel.__index = UserModel

function UserModel:Init()
  self.m_localDBTable = GM.DBTableManager:GetTable(EDBTableConfigs.Local)
  local uuid = self:GetInstallUuid()
  if StringUtil.IsNilOrEmpty(uuid) then
    uuid = PlatformInterface.GenerateUUID()
    self:SetInstallUuid(uuid)
  end
  self.m_syncDBTable = GM.DBTableManager:GetTable(EDBTableConfigs.User)
  local userid = self:GetUserId()
  if userid ~= 0 then
    self:_SyncUserId2NeedModel(userid)
  else
    self.newInstallUser = true
    if self.m_syncDBTable:IsEmpty() then
      self.newUser = true
    end
  end
end

function UserModel:IsNewUser()
  return self.newUser
end

function UserModel:CreateNewUser()
  self.newUser = false
  GM.PropertyDataManager:CreateNewUser()
end

function UserModel:GetData()
  return self.m_syncDBTable
end

function UserModel:Get(userDataKey)
  Log.Assert(userDataKey ~= EPlayerPrefKey.InstallUuid, "必须使用UserModel.GetInstallUuid访问uuid")
  Log.Assert(userDataKey ~= EUserLocalDataKey.UserId, "必须使用UserModel.GetUserId访问userid")
  return self:_GetDBTableByUserDataKey(userDataKey):GetValue(userDataKey, "value")
end

function UserModel:GetInNumber(userDataKey)
  return tonumber(self:Get(userDataKey)) or 0
end

function UserModel:Set(userDataKey, value)
  Log.Assert(userDataKey ~= EPlayerPrefKey.InstallUuid, "必须使用UserModel.SetInstallUuid访问uuid")
  Log.Assert(userDataKey ~= EUserLocalDataKey.UserId, "必须使用UserModel.GetUserId设置userid")
  self:_GetDBTableByUserDataKey(userDataKey):Set(userDataKey, "value", tostring(value))
end

function UserModel:BatchSet(mapContent)
  if mapContent == nil then
    return
  end
  local mapLocalContent = {}
  local mapSyncContent = {}
  for k, v in pairs(mapContent) do
    if self:_GetDBTableByUserDataKey(k) == self.m_localDBTable then
      mapLocalContent[k] = v
    else
      mapSyncContent[k] = v
    end
  end
  self.m_localDBTable:BatchSet(mapLocalContent)
  self.m_syncDBTable:BatchSet(mapSyncContent)
end

function UserModel:ChangeNumber(key, delta)
  if delta == 0 then
    return true
  end
  local curNum = self:GetInNumber(key)
  local newNum = curNum + delta
  if newNum < 0 then
    Log.Error("user property change to less than zero")
    return false
  end
  self:Set(key, newNum)
  return true
end

function UserModel:_GetDBTableByUserDataKey(userDataKey)
  if self.m_mapUserLocalDataKey == nil then
    self.m_mapUserLocalDataKey = {}
    for _, v in pairs(EUserLocalDataKey) do
      self.m_mapUserLocalDataKey[v] = true
    end
  end
  return self.m_mapUserLocalDataKey[userDataKey] and self.m_localDBTable or self.m_syncDBTable
end

function UserModel:GetUserId()
  return tonumber(self.m_localDBTable:GetValue(EUserLocalDataKey.UserId, "value")) or 0
end

function UserModel:SetUserId(userId)
  self.m_localDBTable:Set(EUserLocalDataKey.UserId, "value", tostring(userId))
  self:_SyncUserId2NeedModel(userId)
  Log.Info("UserModel UserId changed to " .. userId)
end

function UserModel:_SyncUserId2NeedModel(userId)
  CSFirebaseManager:SetUserId(tostring(userId))
  CSAppsFlyerManager:SetCuid(tostring(userId))
  PlayerPrefs.SetInt(EPlayerPrefKey.UserIdForCSharp, userId)
  CSErrorMonitor.userId = userId
end

function UserModel:EncryptUserId(iUserId)
  local strEncrypted = CS.Base32.Encode(iUserId)
  return strEncrypted
end

function UserModel:DecryptUserId(strUserId)
  local iDecrypted = CS.Base32.Decode(strUserId)
  return iDecrypted
end

function UserModel:GetDisplayUserId()
  local iUserId = self:GetUserId()
  return self:EncryptUserId(iUserId)
end

function UserModel:GetInstallUuid()
  return GM.SimplePrefs:Get(EPlayerPrefKey.InstallUuid)
end

function UserModel:SetInstallUuid(uuid)
  GM.SimplePrefs:Set(EPlayerPrefKey.InstallUuid, uuid)
end
