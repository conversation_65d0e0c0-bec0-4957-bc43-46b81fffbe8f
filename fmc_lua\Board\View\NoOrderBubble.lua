NoOrderBubble = {}
NoOrderBubble.__index = NoOrderBubble
local TimePerFrame = 0.016666666666666666

function NoOrderBubble:Awake()
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self._CheckShow)
  EventDispatcher.AddListener(EEventType.NewTasksUnlocked, self, self._CheckShow)
  EventDispatcher.AddListener(EEventType.OrderAnimationFinished, self, self._CheckShow)
  EventDispatcher.AddListener(EEventType.EnterMainScene, self, self._CheckShow)
  EventDispatcher.AddListener(EEventType.UpdateOrderEmpty, self, self._CheckShow)
  EventDispatcher.AddListener(EEventType.BakeOutModeChanged, self, self._CheckShow)
  EventDispatcher.AddListener(EEventType.BakeOutStateChanged, self, self._CheckShow)
  self.m_orderArea = self.m_orderAreaGo:GetLuaTable()
  self.gameObject:SetActive(true)
  self:_CheckShow()
end

function NoOrderBubble:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function NoOrderBubble:_CheckShow()
  if self.m_orderArea:IsPlayingOrderAnimation() then
    return
  end
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local hasOrder = orderModel:HasOrders()
  local visible = orderModel:IsAllCanUnlockGroupFinished() and not GM.TaskManager:CanFinishOngoingTask() and not hasOrder
  if visible then
    local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
    visible = not bakeOutModel:CanAcquireToken()
  end
  self.transform:SetLocalScale(visible and 1 or 0)
  if self.m_visible ~= visible then
    self.m_visible = visible
    if self.m_orderArea ~= nil then
      self.m_orderArea:ForceRebuildLayout()
    end
  end
end
