LogPushTokenKey = {
  Android = "oem_token",
  IOS = "device_token"
}
PushTokenModel = {}
PushTokenModel.__index = PushTokenModel

function PushTokenModel:Init()
  self.m_pushToken = PlayerPrefs.GetString(EPlayerPrefKey.PushToken, "")
  self.m_pushTokenUserId = PlayerPrefs.GetInt(EPlayerPrefKey.PushTokenUserId, 0)
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self._TryUpdatePushToken)
end

function PushTokenModel:Destroy()
  EventDispatcher.RemoveTarget(self)
  Scheduler.UnscheduleTarget(self)
end

function PushTokenModel:_TryUpdatePushToken()
  if DeviceInfo.IsUnityEditor() then
    return
  end
  if DeviceInfo.IsSystemIOS() then
    self:_TryUpdatePushTokenIOS()
  else
    self:_TryUpdatePushTokenAndroid()
  end
end

function PushTokenModel:UpdatePushToken(token)
  local userId = GM.UserModel:GetUserId()
  if self.m_pushToken ~= token or self.m_pushTokenUserId ~= userId then
    self.m_pushToken = token
    PlayerPrefs.SetString(EPlayerPrefKey.PushToken, token)
    self.m_pushTokenUserId = userId
    PlayerPrefs.SetInt(EPlayerPrefKey.PushTokenUserId, userId)
    local tokenKey = DeviceInfo.IsSystemIOS() and LogPushTokenKey.IOS or LogPushTokenKey.Android
    GM.BIManager:LogPushToken(tokenKey, token)
    Log.Info("Notification PushToken[New]: token " .. token .. " userId " .. userId)
  else
    Log.Info("Notification PushToken[Same]: token " .. token .. " userId " .. userId)
  end
end

function PushTokenModel:_TryUpdatePushTokenIOS()
  if GM.NotificationModel:IsSystemNotiRequsted() or PlatformInterface.IsNotificationsEnabled() then
    GM.NotificationModel:RequestAuthorization()
  end
end

function PushTokenModel:_TryUpdatePushTokenAndroid()
  local func = function()
    local pushToken = DeviceInfo.GetAndroidPushToken()
    if pushToken ~= "" then
      self:UpdatePushToken(pushToken)
      Scheduler.UnscheduleTarget(self)
    end
  end
  Scheduler.Schedule(func, self, 1, 10)
end
