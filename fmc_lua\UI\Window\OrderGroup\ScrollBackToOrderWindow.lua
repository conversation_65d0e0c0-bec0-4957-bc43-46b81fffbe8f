ScrollBackToOrderWindow = setmetatable({}, TransparentBaseWindow)
ScrollBackToOrderWindow.__index = ScrollBackToOrderWindow

function ScrollBackToOrderWindow:Init()
  local orderArea = MainBoardView:GetInstance():GetOrderArea()
  local dt = 0
  if orderArea:GetOrderGroupButton():IsVisible() then
    dt = orderArea:ScrollToOrderGroup(true, false)
  elseif GM.ActivityManager:GetModel(ActivityType.BakeOut):CanAcquireToken() then
    dt = orderArea:ScrollToActivityEntry(ActivityType.BakeOut, true, true)
  end
  DelayExecuteFuncInView(function()
    self:Close()
  end, dt, self, false)
end

function ScrollBackToOrderWindow:_OnSkipClick()
  if not GM.ConfigModel:CanNewOrderRewardAnimationSkip() then
    return
  end
  Scheduler.FireAndUnscheduleTarget(self)
end
