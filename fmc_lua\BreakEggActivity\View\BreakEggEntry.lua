BreakEggEntry = setmetatable({}, HudCountDownButton)
BreakEggEntry.__index = BreakEggEntry

function BreakEggEntry:Awake()
  HudCountDownButton.Awake(self)
  self.m_model = GM.ActivityManager:GetModel(ActivityType.BreakEgg)
  self:_UpdateRefreshRedTip()
  self:UpdatePerSecond()
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._UpdateRefreshRedTip)
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self._UpdateRefreshRedTip)
  AddHandlerAndRecordMap(self.m_model.event, BreakEggEventType.RefreshFreeTime, {
    obj = self,
    method = self._UpdateRefreshRedTip
  })
end

function BreakEggEntry:OnDestroy()
  HudCountDownButton.OnDestroy(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
end

function BreakEggEntry:OnBtnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.BreakEggMainWindow, false)
end

function BreakEggEntry:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  elseif self.gameObject.activeSelf then
    self.gameObject:SetActive(false)
  end
end

function BreakEggEntry:_UpdateRefreshRedTip()
end
