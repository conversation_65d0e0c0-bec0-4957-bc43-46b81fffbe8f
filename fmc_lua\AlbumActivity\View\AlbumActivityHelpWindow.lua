AlbumActivityHelpWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, AlbumActivityBaseWindow)
AlbumActivityHelpWindow.__index = AlbumActivityHelpWindow

function AlbumActivityHelpWindow:Init(activityType, pageIndex)
  AlbumActivityBaseWindow.Init(self, activityType)
end

AlbumPackInfo = {}
AlbumPackInfo.__index = AlbumPackInfo

function AlbumPackInfo:Init(packId, model)
  local cardInfo = model:GetCardTipInfo(packId)
  if cardInfo == nil then
    return
  end
  self.m_numText.text = "X" .. cardInfo.count
  for i = 1, 5 do
    if self["m_starGo" .. i] then
      self["m_starGo" .. i]:SetActive(i <= cardInfo.star)
    end
  end
end
