TutorialConfigData = {}
TutorialConfigData.__index = TutorialConfigData

function TutorialConfigData.Create(tbData)
  local data = setmetatable({}, TutorialConfigData)
  data:_Init(tbData)
  return data
end

function TutorialConfigData:_Init(tbData)
  self.m_data = tbData
  self.m_arrConditions = {}
  for i, condition in ipairs(self.m_data.condition) do
    self.m_arrConditions[i] = TutorialStartCondition.Create(condition)
  end
end

function TutorialConfigData:IsSatisfied()
  for _, condition in ipairs(self.m_arrConditions) do
    if not condition:IsSatisfied() then
      return false
    end
  end
  return true
end

function TutorialConfigData:GetTutorialId()
  return self.m_data.id
end

function TutorialConfigData:GetArgs()
  return self.m_data.args
end

function TutorialConfigData:GetExecuterName()
  return self.m_data.executer
end

function TutorialConfigData:GetMapSpecialEventListeners()
  local result, eventTypes
  for _, condition in ipairs(self.m_arrConditions) do
    eventTypes = condition:GetSpecialEventListeners()
    if eventTypes ~= nil then
      if result == nil then
        result = {}
      end
      for _, type in pairs(eventTypes) do
        result[type] = true
      end
    end
  end
  return result
end

TutorialStartCondition = {}
TutorialStartCondition.__index = TutorialStartCondition

function TutorialStartCondition.Create(strCondition)
  local condition = setmetatable({}, TutorialStartCondition)
  condition:_Init(strCondition)
  return condition
end

function TutorialStartCondition:_Init(strCondition)
  self.m_type = strCondition.type
  self.m_arrArgs = strCondition.args
end

function TutorialStartCondition:GetConditionType()
  return self.m_type
end

function TutorialStartCondition:IsSatisfied()
  if self.m_type == ETutorialStartCondition.TutorialFinished then
    local tutorialModel = GM.TutorialModel
    for _, tutorialName in ipairs(self.m_arrArgs) do
      if not tutorialModel:IsTutorialFinished(tutorialName) then
        return false
      end
    end
    return true
  elseif self.m_type == ETutorialStartCondition.FunctionEnabled then
    local OpenFunctionModel = GM.OpenFunctionModel
    for _, funcType in ipairs(self.m_arrArgs) do
      if not OpenFunctionModel:IsFunctionOpen(funcType) then
        return false
      end
    end
    return true
  elseif self.m_type == ETutorialStartCondition.CachedItemPushed then
    if self.m_arrArgs == nil then
      return GM.MainBoardModel:GetCachedItemCount() ~= 0
    else
      for i = 1, GM.MainBoardModel:GetCachedItemCount() do
        if Table.ListContain(self.m_arrArgs, GM.MainBoardModel:GetCachedItem(i)) then
          return true
        end
      end
      return false
    end
  elseif self.m_type == ETutorialStartCondition.MainLevel then
    return GM.LevelModel:GetCurrentLevel() >= self.m_arrArgs[1]
  elseif self.m_type == ETutorialStartCondition.TaskFinished then
    local taskModel = GM.TaskManager
    for _, taskConditionData in ipairs(self.m_arrArgs) do
      if not taskModel:IsTaskFinished(taskConditionData.chapterId, taskConditionData.taskId) then
        return false
      end
    end
    return true
  elseif self.m_type == ETutorialStartCondition.CashDashStart then
    local cashDashModel = GM.ActivityManager:GetModel(ActivityType.CashDash)
    return cashDashModel ~= nil and cashDashModel:GetState() ~= ActivityState.Released
  elseif self.m_type == ETutorialStartCondition.BakeOutStart then
    local bakeoutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
    return bakeoutModel ~= nil and bakeoutModel:IsBakeOutModeOn()
  elseif self.m_type == ETutorialStartCondition.OrderGroupFinished then
    local orderModel = GM.MainBoardModel:GetOrderModel()
    return orderModel:IsOrderGroupFinished(self.m_arrArgs[1], self.m_arrArgs[2])
  elseif self.m_type == ETutorialStartCondition.ItemAdded then
    return 0 < #TutorialHelper.GetItems(self.m_arrArgs[1])
  elseif self.m_type == ETutorialStartCondition.HasOrder then
    local orders = GM.MainBoardModel:GetOrders()
    for _, order in pairs(orders) do
      if order:GetType() == OrderType.Fixed and order:GetId() == self.m_arrArgs[1] then
        return true
      end
    end
    return false
  end
  for _, activityDef in pairs(ActivityDefinitions) do
    for activityType, def in pairs(activityDef) do
      if self.m_type == def.TutorialStartCondition then
        local model = GM.ActivityManager:GetModel(activityType)
        if model ~= nil and model:GetState() == ActivityState.Started then
          return true
        end
      end
    end
  end
  return false
end

function TutorialStartCondition:GetSpecialEventListeners()
  if self.m_type == ETutorialStartCondition.FunctionEnabled then
    return {
      EEventType.FunctionOpen
    }
  elseif self.m_type == ETutorialStartCondition.CachedItemPushed then
    return {
      EEventType.CacheItems
    }
  elseif self.m_type == ETutorialStartCondition.MainLevel then
    return {
      EEventType.LevelUp
    }
  elseif self.m_type == ETutorialStartCondition.TaskFinished then
    return {
      EEventType.MainTaskFinished
    }
  elseif self.m_type == ETutorialStartCondition.CashDashStart then
    return {
      EEventType.CashDashStateChanged
    }
  elseif self.m_type == ETutorialStartCondition.BakeOutStart then
    return {
      EEventType.BakeOutModeChanged
    }
  elseif self.m_type == ETutorialStartCondition.OrderGroupFinished then
    return {
      EEventType.OrderGroupRefreshed
    }
  elseif self.m_type == ETutorialStartCondition.ItemAdded then
    return {
      EEventType.ItemViewAdded
    }
  elseif self.m_type == ETutorialStartCondition.HasOrder then
    return {
      EEventType.OrderAnimationFinished
    }
  end
  local arrEvent
  for _, activityDef in pairs(ActivityDefinitions) do
    arrEvent = {}
    for _, def in pairs(activityDef) do
      if self.m_type == def.TutorialStartCondition then
        table.insert(arrEvent, def.StateChangedEvent)
      end
    end
    if not Table.IsEmpty(arrEvent) then
      return arrEvent
    end
  end
  return nil
end
