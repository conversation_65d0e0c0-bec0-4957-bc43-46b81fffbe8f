local Step = {Click = "1"}
local EStep2TextKey = {
  [Step.Click] = "tutorial_order_group_1"
}
local EStep2TextAnchorPercent = {
  [Step.Click] = 40
}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  self:_ExecuteStep1()
  return true
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.Click
  self:_SaveOngoingDatas()
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  local orderGroup = TutorialHelper.HighlightOrderGroup()
  if not orderGroup then
    self:Finish()
    return
  end
  self.m_gesture = TutorialHelper.TapCustomPos(orderGroup.transform.position)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_OnOpenView(msg)
  if msg and msg.name == UIPrefabConfigName.OrderDayWindow and self.m_strOngoingDatas == Step.Click and self.m_gesture then
    TutorialHelper.DehighlightOrderGroup()
    self:Finish(self.m_gesture)
  end
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
