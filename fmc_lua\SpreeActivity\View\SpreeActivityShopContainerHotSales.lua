SpreeActivityShopContainerHotSales = setmetatable({}, BaseShopListContainer)
SpreeActivityShopContainerHotSales.__index = SpreeActivityShopContainerHotSales

function SpreeActivityShopContainerHotSales:Init(activityType)
  self.m_shopModel = GM.ActivityManager:GetModel(activityType):GetShopModel()
  local activityDefinition = SpreeActivityDefinition[activityType]
  BaseShopListContainer.Init(self, activityDefinition.HotSalesShopType, false, 3)
end

function SpreeActivityShopContainerHotSales:_GetCellData()
  return self.m_shopModel:GetHotSaleData()
end

function SpreeActivityShopContainerHotSales:_OnCellClicked(cell)
  local data = cell:GetData()
  local onSuccess = function()
    self:_UpdateContent()
    GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, {
      {
        [PROPERTY_TYPE] = data.Code,
        [PROPERTY_COUNT] = 1
      }
    }, "rewards_window_title_shop", false)
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxShopBuy)
  end
  local onOutOfGem = function(gemCount)
    GM.ShopModel:OnLackOfGem(gemCount)
  end
  self.m_shopModel:BuyItem(data.Slot, data.Code, data.Price, onSuccess, onOutOfGem)
end

function SpreeActivityShopContainerHotSales:_GetRefreshTime()
  return self.m_shopModel:GetHotSalesRefreshTime()
end
