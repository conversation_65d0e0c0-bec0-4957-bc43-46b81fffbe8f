UpdateHintWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.UpdateHintWindow,
  canCloseByChangeGameMode = false
}, BaseWindow)
UpdateHintWindow.__index = UpdateHintWindow

function UpdateHintWindow:Init(permitClose)
  self.m_bPermitClose = permitClose
  self.m_closeBtnGo:SetActive(self.m_bPermitClose)
  GM.UpdateHintModel:AddPopupWindowCount()
  if not self.m_bPermitClose and not GM.BIManager:IsNetworkCheckEnd() then
    GM.BIManager:LogNet(EBIType.NetworkCheckAction.UpdateVersionFromWindow, nil, nil, nil, true)
    GM.BIManager:SetNetworkCheckEnd()
  end
end

function UpdateHintWindow:OnConfirmBtnClicked()
  GM.UpdateHintModel:TryGoToStorePage()
  if self.m_bPermitClose and not GM.BIManager:IsNetworkCheckEnd() then
    GM.BIManager:LogNet(EBIType.NetworkCheckAction.UpdateVersionFromWindow, nil, nil, nil, true)
    GM.BIManager:SetNetworkCheckEnd()
  end
end

function UpdateHintWindow:OnCloseBtnClick()
  if not self.m_bPermitClose then
    return
  end
  BaseWindow.OnCloseBtnClick(self)
end
