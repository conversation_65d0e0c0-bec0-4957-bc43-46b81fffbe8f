NoticeWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
NoticeWindow.__index = NoticeWindow

function NoticeWindow:Init()
  self.m_cells = {}
  GM.NoticeModel:ResetHasNewNotice()
  local notices = Table.ShallowCopy(GM.NoticeModel:GetNotices())
  local i = 1
  local serverTime = GM.GameModel:GetServerTime()
  while i <= #notices do
    local notice = notices[i]
    if serverTime > notice.DisappearTime then
      table.remove(notices, i)
    else
      i = i + 1
    end
  end
  if #notices == 0 then
    self.m_emptyGo:SetActive(true)
    self.m_mailListGo:SetActive(false)
    return
  end
  self.m_emptyGo:SetActive(false)
  self.m_mailListGo:SetActive(true)
  table.sort(notices, NoticeWindow._NoticeComparer)
  for _, notice in ipairs(notices) do
    local cell = Object.Instantiate(self.m_noticeCellPrefab, self.m_noticeContent):GetLuaTable()
    cell:Init(notice)
    table.insert(self.m_cells, cell)
  end
  EventDispatcher.AddListener(EEventType.CloseView, self, self._OnContentWindowClose)
end

function NoticeWindow:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function NoticeWindow:_OnContentWindowClose(msg)
  if not self.m_cells or msg.name ~= UIPrefabConfigName.NoticeContentWindow then
    return
  end
  for _, cell in ipairs(self.m_cells) do
    cell:UpdateState()
  end
  if not GM.NoticeModel:HasUnreadSystemNotice() then
    self:Close()
  end
end

function NoticeWindow._NoticeComparer(a, b)
  local isARead = GM.NoticeModel:GetNoticeState(a) == NoticeState.Read
  local isBRead = GM.NoticeModel:GetNoticeState(b) == NoticeState.Read
  if isARead ~= isBRead then
    return not isARead
  end
  if a.OrderNumber ~= b.OrderNumber then
    return a.OrderNumber > b.OrderNumber
  end
  return a.ReleaseTime < b.ReleaseTime
end
