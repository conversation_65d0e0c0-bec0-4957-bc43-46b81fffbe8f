ItemManager = {}
ItemManager.__index = ItemManager
local ItemRowTable = {}
local ItemColumnTable = {}

function ItemManager.Create(dbTable, boardModel)
  local itemManager = setmetatable({}, ItemManager)
  itemManager:Init(dbTable, boardModel)
  return itemManager
end

function ItemManager:Init(dbTable, boardModel)
  self.m_dbTable = dbTable
  self.m_boardModel = boardModel
  self.m_idGenerator = DBIdGenerator.Create()
  self.m_itemTable = {}
end

function ItemManager:Destroy()
  if not Table.IsEmpty(self.m_itemTable) then
    for _, itemModel in pairs(self.m_itemTable) do
      itemModel:Destroy()
    end
  end
end

function ItemManager:OnSyncDataFinished()
  for id, data in pairs(self.m_dbTable:GetValues()) do
    data.id = id
    local item = ItemModelFactory.CreateWithData(self.m_boardModel, data)
    local createOk = false
    if item then
      local itemBubble = item:GetComponent(ItemBubble)
      if not itemBubble or GM.ItemDataModel:GetModelConfig(itemBubble:GetInnerItemCode(), true) then
        self.m_itemTable[id] = item
        createOk = true
      end
    end
    if not createOk then
      local removed = GM.ItemTypeDeleteModel:OnItemRemoved(data.codeStr)
      if not removed then
        GM.BIManager:LogProject(EBIType.ItemNotFound, "IM itemCode:" .. (IsString(data.codeStr) and data.codeStr or "nil"))
      end
      self.m_dbTable:Remove(id)
    end
  end
end

function ItemManager:ResetVar()
  self.m_itemTable = {}
end

function ItemManager:GetItem(id)
  return self.m_itemTable[id]
end

function ItemManager:SetItem(item)
  local id = item:GetId()
  if id == nil then
    id = self.m_idGenerator:Generate()
    while self.m_itemTable[id] ~= nil do
      id = self.m_idGenerator:Generate()
    end
    item:SetId(id)
  end
  if self.m_itemTable[id] == nil then
    self.m_itemTable[id] = item
  end
  self:_FillDBTable(item, ItemColumnTable)
  ItemRowTable[id] = ItemColumnTable
  self.m_dbTable:BatchSet(ItemRowTable)
  ItemRowTable[id] = nil
  for k, _ in pairs(ItemColumnTable) do
    ItemColumnTable[k] = nil
  end
end

function ItemManager:RemoveItem(id)
  self.m_itemTable[id] = nil
  self.m_dbTable:Remove(id)
end

function ItemManager:GetIdGenerator()
  return self.m_idGenerator
end

function ItemManager:_FillDBTable(item, DBTable)
  item:ToSerialization(DBTable)
end

function ItemManager:IsEmpty()
  return Table.IsEmpty(self.m_itemTable)
end

function ItemManager:GetAllItems()
  return Table.GetValueList(self.m_itemTable)
end
