local DEFAULT_ANCHOR_PERCENT = 60
local V3Zero = V3Zero
local V3One = V3One
local ETutorialGestureType = {
  None = 0,
  Tap = 1,
  Drag = 2
}
TutorialLayer = {
  CameraOffset = Vector3(-BOARD_CAM_POS_X, 0, 0)
}
TutorialLayer.__index = TutorialLayer

function TutorialLayer:Init(sceneView)
  self.gameObject.transform.sizeDelta = ScreenFitter.GetScreenAdjustSize()
  self.m_sceneView = sceneView
  if self.m_sceneView ~= nil then
    self.m_baseSortingOrder = ESpecialViewSortingOrder.TutorialMask
    self.m_canvas.sortingOrder = self.m_baseSortingOrder
  end
  self.m_mapHighlightTransform = {}
  self.m_mapHighlightForBoardTransform = {}
  self.m_mask:Hide(true)
  self.m_mapCachedGestures = {}
  for _, v in pairs(ETutorialGestureType) do
    self.m_mapCachedGestures[v] = {}
  end
  self.m_mapGestures = {}
  self.m_gestureTapGo:SetActive(false)
  self.m_gestureDragGo:SetActive(false)
  self.m_dialog:Hide(true)
  self.m_mapArrows = {}
  self.m_arrCachedArrows = {}
  self.m_arrowGo:SetActive(false)
end

function TutorialLayer:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function TutorialLayer:UpdateMask(center, size, callback, clipRectClickable)
  self.m_mask:UpdateMask(center, size, callback, clipRectClickable)
end

function TutorialLayer:UpdateMaskOnBoard(fromBoardPosition, toBoardPosition, callback, clipRectClickable)
  local center, size = self:_GetMaskAreaData(fromBoardPosition, toBoardPosition)
  self:UpdateMask(center, size, callback, clipRectClickable)
end

function TutorialLayer:UpdateMaskOnGivenBoard(boardView, fromBoardPosition, toBoardPosition, callback, clipRectClickable)
  local center, size = self:_GetMaskAreaData(fromBoardPosition, toBoardPosition, boardView)
  self:UpdateMask(center, size, callback, clipRectClickable)
end

function TutorialLayer:SetMaskAlphaOnce(alpha)
  self.m_mask:SetMaskAlphaOnce(alpha)
end

function TutorialLayer:ShowText(text, anchorPercent, speakerName, flip)
  self.m_dialog:UpdateText(text, anchorPercent or DEFAULT_ANCHOR_PERCENT, speakerName, flip)
end

function TutorialLayer:UpdateTextAnchorPercent(anchorPercent)
  self.m_dialog:UpdateAnchorPercent(anchorPercent)
end

function TutorialLayer:ShowTextWithBoardMaskArea(text, boardPos1, boardPos2, speakerName, flip)
  local center, size = self:_GetMaskAreaData(boardPos1, boardPos2)
  self.m_dialog:UpdateTextWithMaskArea(text, center, size, speakerName, flip)
end

function TutorialLayer:ShowTextWithGivenBoardMaskArea(boardView, text, boardPos1, boardPos2, speakerName, filp, interval)
  local center, size = self:_GetMaskAreaData(boardPos1, boardPos2, boardView)
  self.m_dialog:UpdateTextWithMaskArea(text, center, size, speakerName, filp, interval)
end

function TutorialLayer:ShowDialogContinueText()
  self.m_dialog:ShowContinueText()
end

function TutorialLayer:UpdateDialogSortingOrder(sortingOrder)
  self.m_dialog:UpdateSortingOrder(sortingOrder)
end

function TutorialLayer:PlayTapAnimation(position, scale)
  local gesture = self:_GetGesture(ETutorialGestureType.Tap)
  self.m_mapGestures[gesture] = true
  gesture:PlayTapAnimation(position, scale)
  return gesture
end

function TutorialLayer:PlayTapOnBoardAnimation(boardPosition)
  return self:PlayTapAnimation(self:_ConvertBoardPosition(boardPosition))
end

function TutorialLayer:PlayTapOnGivenBoardAnimation(boardView, boardPosition)
  return self:PlayTapAnimation(self:_ConvertBoardPosition(boardPosition, boardView))
end

function TutorialLayer:PlayDragAnimation(fromPosition, toPosition, spriteImg)
  local gesture = self:_GetGesture(ETutorialGestureType.Drag)
  self.m_mapGestures[gesture] = true
  gesture:PlayDragAnimation(fromPosition, toPosition, spriteImg)
  return gesture
end

function TutorialLayer:PlayDragOnBoardAnimation(fromBoardPosition, toBoardPosition, spriteImg, boardView)
  local gesture = self:_GetGesture(ETutorialGestureType.Drag)
  self.m_mapGestures[gesture] = true
  gesture:PlayDragAnimation(self:_ConvertBoardPosition(fromBoardPosition, boardView), self:_ConvertBoardPosition(toBoardPosition, boardView), spriteImg)
  return gesture
end

local canvasType = typeof(CS.UnityEngine.Canvas)
local raycasterType = typeof(CS.UnityEngine.UI.GraphicRaycaster)

function TutorialLayer:HighlightForUI(transform, disableInteraction)
  local context = self.m_mapHighlightTransform[transform]
  if context then
    context.count = context.count + 1
    return
  else
    context = {}
    context.count = 1
  end
  local luaTb = transform.gameObject:GetLuaTable()
  if luaTb ~= nil and luaTb.OnTutorialHighlight ~= nil then
    luaTb:OnTutorialHighlight(true)
  end
  local canvas = transform:GetComponent(canvasType)
  if canvas == nil or canvas:IsNull() then
    canvas = transform.gameObject:AddComponent(canvasType)
    context.newCanvas = canvas
  else
    context.canvas = canvas
    context.overrideSorting = canvas.overrideSorting
    context.sortingOrder = canvas.sortingOrder
    context.additionalShaderChannels = canvas.additionalShaderChannels
  end
  canvas.overrideSorting = true
  canvas.sortingOrder = ESpecialViewSortingOrder.TutorialHighlight
  UIUtil.UpdateDeltaSortingOrder(transform.gameObject, ESpecialViewSortingOrder.TutorialHighlight)
  canvas.additionalShaderChannels = 27
  local raycaster = transform:GetComponent(raycasterType)
  local hasRaycaster = raycaster and not raycaster:IsNull()
  if not hasRaycaster and not disableInteraction then
    raycaster = transform.gameObject:AddComponent(raycasterType)
    context.newGraphicRaycaster = raycaster
  elseif hasRaycaster and raycaster.enabled and disableInteraction then
    raycaster.enabled = false
    context.disabledRaycaster = raycaster
  end
  self.m_mapHighlightTransform[transform] = context
end

function TutorialLayer:DehighlightForUI(transform)
  local context = self.m_mapHighlightTransform[transform]
  if context == nil then
    return
  elseif context.count >= 1 then
    context.count = context.count - 1
    if context.count >= 1 then
      return
    end
  end
  local luaTb = transform.gameObject:GetLuaTable()
  if luaTb ~= nil and luaTb.OnTutorialHighlight ~= nil then
    luaTb:OnTutorialHighlight(false)
  end
  if context.newGraphicRaycaster ~= nil and not context.newGraphicRaycaster:IsNull() then
    GameObject.DestroyImmediate(context.newGraphicRaycaster)
  end
  if context.disabledRaycaster and not context.disabledRaycaster:IsNull() then
    context.disabledRaycaster.enabled = true
  end
  if context.newCanvas ~= nil and not context.newCanvas:IsNull() then
    GameObject.DestroyImmediate(context.newCanvas)
  elseif context.canvas ~= nil and not context.canvas:IsNull() then
    context.canvas.overrideSorting = context.overrideSorting
    context.canvas.sortingOrder = context.sortingOrder
    context.canvas.additionalShaderChannels = context.additionalShaderChannels
  end
  UIUtil.UpdateDeltaSortingOrder(transform.gameObject, -ESpecialViewSortingOrder.TutorialHighlight)
  self.m_mapHighlightTransform[transform] = nil
end

function TutorialLayer:ToggleBoardHighlightRaycast(enable)
  self.m_highlightForBoardRaycast.enabled = enable
end

function TutorialLayer:HighlightForBoard(transform, isInfoBar, placeHolderScale)
  if self.m_mapHighlightForBoardTransform[transform] ~= nil then
    return
  end
  local newPlaceHolder = GameObject.Instantiate(self.m_placeHolderGo, transform.parent).transform
  newPlaceHolder.localPosition = transform.localPosition
  newPlaceHolder.sizeDelta = transform.sizeDelta
  newPlaceHolder.gameObject:SetActive(transform.gameObject.activeSelf)
  newPlaceHolder:SetParent(transform.parent, true)
  newPlaceHolder:SetSiblingIndex(transform:GetSiblingIndex())
  if placeHolderScale ~= nil then
    newPlaceHolder.localScale = placeHolderScale
  end
  self.m_mapHighlightForBoardTransform[transform] = newPlaceHolder
  transform:SetParent(isInfoBar and self.m_highlightForBoardInfoBarRectTrans or self.m_highlightForBoardRectTrans, true)
  transform.localPosition = transform.localPosition + TutorialLayer.CameraOffset
  if isInfoBar then
    local canvas = self.m_highlightForBoardInfoBarRectTrans:GetComponent(canvasType)
    local inventoryWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.InventoryWindow)
    if canvas and not canvas:IsNull() and inventoryWindow then
      canvas.sortingOrder = inventoryWindow:GetSortingOrder() + GM.UIManager:GetDeltaSortingOrder()
    end
  end
  UIUtil.UpdateSortingOrder(transform.gameObject, ESpecialViewSortingOrder.TutorialHighlight)
end

function TutorialLayer:UpdateBoardHighlightPos(boardView)
  if not self.m_mapHighlightForBoardTransform or not boardView then
    return
  end
  for key, value in pairs(self.m_mapHighlightForBoardTransform) do
    if not key:IsNull() then
      local worldPos = value.position
      local screenPos = boardView:ConvertWorldPositionToScreenPosition(worldPos)
      local uiWorldPos = PositionUtil.UICameraScreen2World(screenPos)
      local localPos = key.parent:InverseTransformPoint(uiWorldPos)
      localPos.z = 0
      key.localPosition = localPos
    end
  end
end

function TutorialLayer:DehighlightForBoard(transform)
  local placeHolderTransform = self.m_mapHighlightForBoardTransform[transform]
  if self.m_mapHighlightForBoardTransform[transform] == nil then
    return
  end
  if transform:IsNull() then
    self.m_mapHighlightForBoardTransform[transform].gameObject:RemoveSelf()
    self.m_mapHighlightForBoardTransform[transform] = nil
    return
  end
  if placeHolderTransform:IsNull() then
    Object.Destroy(transform.gameObject)
  else
    local localPosition = self.m_mapHighlightForBoardTransform[transform].localPosition
    transform:SetParent(self.m_mapHighlightForBoardTransform[transform].parent, true)
    transform:SetSiblingIndex(self.m_mapHighlightForBoardTransform[transform]:GetSiblingIndex())
    self.m_mapHighlightForBoardTransform[transform].gameObject:SetActive(false)
    self.m_mapHighlightForBoardTransform[transform].gameObject:RemoveSelf()
    self.m_mapHighlightForBoardTransform[transform] = nil
    transform.localPosition = localPosition
    UIUtil.UpdateSortingOrder(transform.gameObject, 0)
  end
end

function TutorialLayer:AddArrow(pos, rotateZ, dis)
  local arrow = self:_GetArrow()
  self.m_mapArrows[arrow] = true
  arrow:UpdateArrow(pos, rotateZ, dis)
  return arrow
end

function TutorialLayer:HighlightItems(itemDatasArr)
  self.m_mask:HighlightItems(itemDatasArr)
end

function TutorialLayer:UnHighlightItems()
  self.m_mask:UnHighlightItems()
end

function TutorialLayer:Hide(gesture, arrow)
  self:HideMask()
  self:HideDialog()
  self:HideGesture(gesture)
  self:HideArrow(arrow)
end

function TutorialLayer:Finish(gesture, arrow)
  self:Hide(gesture, arrow)
  self.m_dialog:Finish()
  self.m_mask:Finish()
end

function TutorialLayer:HideGesture(gesture)
  if gesture and self.m_mapGestures[gesture] ~= nil then
    self:RecycleGesture(gesture)
  end
end

function TutorialLayer:HideDialog()
  self.m_dialog:Hide()
end

function TutorialLayer:HideMask()
  self.m_mask:Hide()
end

function TutorialLayer:HideArrow(arrow)
  if arrow and self.m_mapArrows[arrow] ~= nil then
    self:RecycleArrow(arrow)
  end
end

function TutorialLayer:HideAll()
  self:HideMask()
  self:HideDialog()
  for gesture, _ in pairs(self.m_mapGestures) do
    self:RecycleGesture(gesture)
  end
  for transform, _ in pairs(self.m_mapHighlightTransform) do
    self:DehighlightForUI(transform)
  end
  for transform, _ in pairs(self.m_mapHighlightForBoardTransform) do
    self:DehighlightForBoard(transform)
  end
  for arrow, _ in pairs(self.m_mapArrows) do
    self:RecycleArrow(arrow)
  end
end

function TutorialLayer:_ConvertBoardPosition(boardPosition, boardView)
  local boardView = boardView or MainBoardView.GetInstance()
  if boardView == nil then
    return V3Zero
  end
  local uiWorldPosition = PositionUtil.UICameraScreen2World(boardView:ConvertBoardPositionToScreenPosition(boardPosition))
  uiWorldPosition.z = 0
  return uiWorldPosition
end

function TutorialLayer:_GetMaskAreaData(from, to, boardView)
  local fromWorldPos, toWorldPos = self:_ConvertBoardPosition(from, boardView), self:_ConvertBoardPosition(to, boardView)
  local unitSize = self:GetBoardUnitSize(boardView)
  return (fromWorldPos + toWorldPos) / 2, Vector2(unitSize * (math.abs(from:GetX() - to:GetX()) + 1.25), unitSize * (math.abs(from:GetY() - to:GetY()) + 1.25))
end

function TutorialLayer:GetBoardUnitSize(boardView)
  if self.m_boardUnitSize == nil then
    boardView = boardView or MainBoardView.GetInstance()
    local boardModel = boardView:GetModel()
    local f, t = self:_ConvertBoardPosition(boardModel:CreatePosition(1, 1), boardView), self:_ConvertBoardPosition(boardModel:CreatePosition(2, 2), boardView)
    self.m_boardUnitSize = t.x - f.x
  end
  return self.m_boardUnitSize
end

function TutorialLayer:_GetGesture(eGestureType)
  if #self.m_mapCachedGestures[eGestureType] > 0 then
    return table.remove(self.m_mapCachedGestures[eGestureType])
  end
  local newObj
  if eGestureType == ETutorialGestureType.Tap then
    newObj = GameObject.Instantiate(self.m_gestureTapGo, self.m_gestureTapGo.transform.parent)
  elseif eGestureType == ETutorialGestureType.Drag then
    newObj = GameObject.Instantiate(self.m_gestureDragGo, self.m_gestureDragGo.transform.parent)
  end
  local gesture = newObj:GetLuaTable()
  gesture:UpdateDefaultSortingOrder(self.m_baseSortingOrder + 1)
  return gesture
end

function TutorialLayer:RecycleGesture(gesture)
  gesture:Hide()
  self.m_mapGestures[gesture] = nil
  local type = gesture:GetType()
  self.m_mapCachedGestures[type][#self.m_mapCachedGestures[type] + 1] = gesture
end

function TutorialLayer:_GetArrow()
  if #self.m_arrCachedArrows > 0 then
    return table.remove(self.m_arrCachedArrows)
  end
  local newObj = GameObject.Instantiate(self.m_arrowGo, self.m_arrowGo.transform.parent)
  local arrow = newObj:GetLuaTable()
  arrow:UpdateDefaultSortingOrder(self.m_baseSortingOrder + 1)
  return arrow
end

function TutorialLayer:RecycleArrow(arrow)
  arrow:Hide()
  self.m_mapArrows[arrow] = nil
  self.m_arrCachedArrows[#self.m_arrCachedArrows + 1] = arrow
end

local RectTransformUtility = RectTransformUtility
local MASK_ALPHA = 0.39215686274509803
TutorialMask = {}
TutorialMask.__index = TutorialMask

function TutorialMask:Awake()
  function self.m_raycastFilter.OnLuaIsRaycastLocationValid(screenPoint, eventCamera)
    return self:_IsRaycastLocationValid(screenPoint, eventCamera)
  end
  
  function self.m_eventTrigger.OnLuaPointerClick(eventData)
    self:_OnMaskClicked(eventData)
  end
  
  self.m_arrHighlightItems = {}
end

function TutorialMask:UpdateMask(center, size, clickCallback, clipRectClickable)
  self.m_bFinish = nil
  local targetAlpha = self.m_alphaOnce or MASK_ALPHA
  if self.gameObject.activeSelf then
    if self.m_curCenter ~= center or self.m_curSize ~= size or self.m_bHiding or self.m_maskImg.color.a ~= targetAlpha then
      self:_ClearTween()
      self.gameObject:SetActive(true)
      GM.UIManager:SetEventLock(true)
      local dt = 0.3
      local seq = DOTween.Sequence()
      seq:Append(self.m_maskImg:DOFade(targetAlpha, dt))
      if self.m_curSize == V2Zero then
        self.m_clipRectTrans.localPosition = center
      elseif size == V2Zero then
      else
        seq:Join(self.m_clipRectTrans:DOLocalMove(center, dt))
      end
      seq:Join(self.m_clipRectTrans:DOSizeDelta(size, dt))
      seq:OnComplete(function()
        GM.UIManager:SetEventLock(false)
        UIUtil.SetAlpha(self.m_maskImg, targetAlpha)
        self.m_alphaOnce = nil
        self.m_tween = nil
      end)
      self.m_clickCallback = clickCallback
      self.m_bClipRectClickable = clipRectClickable
      self.m_curCenter = center
      self.m_curSize = size
      self.m_tween = seq
    else
      self.m_alphaOnce = nil
      self.m_clickCallback = clickCallback
    end
  else
    self.m_curCenter = center
    self.m_curSize = size
    self.m_clipRectTrans.localPosition = self.gameObject.transform:InverseTransformPoint(center)
    UIUtil.SetLocalPosition(self.m_clipRectTrans, nil, nil, 0)
    self.m_clipRectTrans.sizeDelta = size
    self.m_clickCallback = clickCallback
    self.m_bClipRectClickable = clipRectClickable
    self.gameObject:SetActive(true)
    GM.UIManager:SetEventLock(true)
    UIUtil.SetAlpha(self.m_maskImg, 0)
    if self.m_tweenCompleteCallback == nil then
      function self.m_tweenCompleteCallback()
        GM.UIManager:SetEventLock(false)
        
        UIUtil.SetAlpha(self.m_maskImg, self.m_alphaOnce or MASK_ALPHA)
        self.m_alphaOnce = nil
        self.m_tween = nil
      end
    end
    self.m_tween = self.m_maskImg:DOFade(targetAlpha, 0.3):OnComplete(self.m_tweenCompleteCallback)
  end
end

function TutorialMask:Hide(ignoreAnim)
  if self.gameObject.activeSelf then
    if ignoreAnim then
      self.gameObject:SetActive(false)
    else
      if self.m_bHiding then
        return
      end
      self:_ClearTween()
      GM.UIManager:SetEventLock(true)
      self.m_bHiding = true
      if self.m_hideCompleteCallback == nil then
        function self.m_hideCompleteCallback()
          self.gameObject:SetActive(false)
          
          self.m_bHiding = false
          GM.UIManager:SetEventLock(false)
          self.m_bFinish = nil
          self.m_tween = nil
        end
      end
      self.m_tween = self.m_maskImg:DOFade(0, self.m_maskImg.color.a * 0.8):OnComplete(self.m_hideCompleteCallback)
    end
  end
  self:UnHighlightItems()
end

function TutorialMask:Finish()
  self.m_bFinish = true
end

function TutorialMask:SetMaskAlphaOnce(alpha)
  self.m_alphaOnce = alpha
end

function TutorialMask:_IsRaycastLocationValid(screenPoint, eventCamera)
  if self.m_bClipRectClickable == false then
    return true
  end
  return not RectTransformUtility.RectangleContainsScreenPoint(self.m_clipRectTrans, screenPoint, eventCamera)
end

function TutorialMask:_OnMaskClicked(eventData)
  if self.m_clickCallback ~= nil then
    self.m_clickCallback()
  end
end

function TutorialMask:_ClearTween()
  if self.m_tween ~= nil then
    self.m_tween:Kill(true)
    self.m_tween = nil
  end
end

function TutorialMask:HighlightItems(itemDatasArr)
  self:UnHighlightItems()
  local tileWidth = BaseBoardModel.TileSize
  for _, item in ipairs(itemDatasArr) do
    local eleClip = Object.Instantiate(self.m_clipRectTrans.gameObject, self.transform)
    local ScreenPos = MainBoardView.GetInstance():ConvertBoardPositionToScreenPosition(item:GetPosition())
    local UICameraWorldPos = PositionUtil.UICameraScreen2World(ScreenPos)
    eleClip.transform.localPosition = self.gameObject.transform:InverseTransformPoint(UICameraWorldPos)
    eleClip.transform.sizeDelta = Vector2(tileWidth, tileWidth)
    table.insert(self.m_arrHighlightItems, eleClip)
  end
end

function TutorialMask:UnHighlightItems()
  for _, v in ipairs(self.m_arrHighlightItems) do
    v:RemoveSelf()
  end
  self.m_arrHighlightItems = {}
end

TutorialDialog = {}
TutorialDialog.__index = TutorialDialog
local avatarImagePos = {
  [ImageFileConfigName.guide_default_avatar] = Vector2(-12.3, 22.7),
  [ImageFileConfigName.guide_shop_avatar] = Vector2(-11, 14)
}

function TutorialDialog:Awake()
  self.m_screenHeight = ScreenFitter.GetScreenAdjustSize().y
  self.m_contentHeight = self.gameObject.transform.sizeDelta.y
  self.m_boardBaseHeight = self.m_boardRectTrans.sizeDelta.y
  UIUtil.SetActive(self.m_continueTextGo, false)
end

function TutorialDialog:UpdateText(text, anchorPercent, speakerName, flip)
  self.m_bFinish = nil
  self:UpdateSortingOrder()
  if self.gameObject.activeSelf then
    if self.m_nextText ~= nil or self.m_bHiding then
      self.m_nextText = text
      self.m_nextAnchorPercent = anchorPercent
      self.m_nextSpeakerName = speakerName
      self.m_nextFlip = flip
    else
      self:_ClearTween()
      self.m_nextText = text
      self.m_nextAnchorPercent = anchorPercent
      self.m_nextSpeakerName = speakerName
      self.m_nextFlip = flip
      self:Hide()
    end
  else
    speakerName = speakerName or "default"
    self.m_avatarImg.enabled = false
    local img = ImageFileConfigName["guide_" .. string.lower(speakerName) .. "_avatar"]
    SpriteUtil.SetImage(self.m_avatarImg, img, true)
    self.m_avatarImg.transform.anchoredPosition = avatarImagePos[img] or V2Zero
    self.gameObject:SetActive(true)
    UIUtil.SetActive(self.m_continueTextGo, self.m_bShowContinueText)
    self.m_descText.text = text
    self:UpdateAnchorPercent(anchorPercent)
    self.m_boardCanvasGroup.alpha = 0
    self.m_boardRectTrans.localScale = V3Zero
    UIUtil.SetLocalScale(self.transform, flip and -1 or 1)
    UIUtil.SetLocalScale(self.m_descText.transform, flip and -1 or 1)
    GM.UIManager:SetEventLock(true)
    local sequence = DOTween.Sequence()
    sequence:Insert(0.13, self.m_boardCanvasGroup:DOFade(1, 0.2))
    sequence:Insert(0.13, self.m_boardRectTrans:DORotate(V3Zero, 0.2, CS.DG.Tweening.RotateMode.Fast))
    sequence:Insert(0.13, self.m_boardRectTrans:DOScale(1.1, 0.2))
    sequence:Insert(0.33, self.m_boardRectTrans:DOScale(1, 0.1))
    if self.m_updateCompleteCallback == nil then
      function self.m_updateCompleteCallback()
        self.m_boardCanvasGroup.alpha = 1
        
        self.m_boardRectTrans.localScale = V3One
        GM.UIManager:SetEventLock(false)
        self.m_tween = nil
      end
    end
    sequence:OnComplete(self.m_updateCompleteCallback)
    self.m_tween = sequence
  end
end

function TutorialDialog:UpdateAnchorPercent(anchorPercent)
  UIUtil.SetAnchoredPosition(self.gameObject.transform, nil, -self.m_screenHeight * anchorPercent / 100)
end

local DEFAULT_DIALOG_MASK_INTERVAL = 50

function TutorialDialog:UpdateTextWithMaskArea(text, center, size, speakerName, flip, interval)
  local dialogInterval = interval or DEFAULT_DIALOG_MASK_INTERVAL
  local defaultPosY = center.y - size.y / 2 - self.m_contentHeight / 2 - dialogInterval
  if defaultPosY - self.m_contentHeight / 2 - 100 < -self.m_screenHeight / 2 * 0.9 then
    defaultPosY = center.y * 2 - defaultPosY + dialogInterval + 100
  end
  local targetAnchorPercent = math.floor((self.m_screenHeight / 2 - defaultPosY) * 100 / self.m_screenHeight)
  self:UpdateText(text, targetAnchorPercent, speakerName, flip)
end

function TutorialDialog:Hide(ignoreAnim)
  if self.gameObject.activeSelf then
    if ignoreAnim then
      self.gameObject:SetActive(false)
    else
      if self.m_bHiding then
        return
      end
      self:_ClearTween()
      GM.UIManager:SetEventLock(true)
      self.m_bHiding = true
      self.m_boardCanvasGroup.alpha = 1
      self.m_boardRectTrans.localScale = V3One
      local sequence = DOTween.Sequence()
      sequence:Insert(0, self.m_boardRectTrans:DOScale(1.1, 0.2))
      sequence:Insert(0.2, self.m_boardRectTrans:DOScale(0, 0.2))
      sequence:Insert(0.3, self.m_boardCanvasGroup:DOFade(0, 0.2))
      if self.m_nextCompleteCallback == nil then
        function self.m_nextCompleteCallback()
          self.m_bHiding = false
          
          self.gameObject:SetActive(false)
          self.m_boardCanvasGroup.alpha = 0
          self.m_boardRectTrans.localScale = V3Zero
          GM.UIManager:SetEventLock(false)
          local text, anchorPercent, speakerName, flip, finish = self.m_nextText, self.m_nextAnchorPercent, self.m_nextSpeakerName, self.m_nextFlip, self.m_bFinish
          self.m_nextText = nil
          self.m_nextAnchorPercent = nil
          self.m_nextSpeakerName = nil
          self.m_bFinish = nil
          self.m_nextFlip = nil
          self.m_tween = nil
          if text ~= nil and not finish then
            self:UpdateText(text, anchorPercent, speakerName, flip)
          end
        end
      end
      sequence:OnComplete(self.m_nextCompleteCallback)
    end
  end
end

function TutorialDialog:ShowContinueText()
  self.m_bShowContinueText = true
end

function TutorialDialog:Finish()
  self.m_bShowContinueText = false
  self.m_bFinish = true
end

function TutorialDialog:_ClearTween()
  if self.m_tween ~= nil then
    self.m_tween:Kill(true)
    self.m_tween = nil
  end
end

function TutorialDialog:UpdateSortingOrder(sortingOrder)
  if sortingOrder ~= nil then
    self.m_canvas.overrideSorting = true
    self.m_canvas.sortingOrder = sortingOrder
  else
    self.m_canvas.overrideSorting = false
  end
end

TutorialGesture = {
  Type = ETutorialGestureType.None
}
TutorialGesture.__index = TutorialGesture

function TutorialGesture:UpdateDefaultSortingOrder(baseOrder)
  self.m_defaultOrder = baseOrder
  self:UpdateSortingOrder(self.m_defaultOrder)
end

function TutorialGesture:UpdateSortingOrder(baseOrder)
  self.m_curOrder = baseOrder
  UIUtil.UpdateSortingOrder(self.gameObject, self.m_curOrder)
  self.m_canvas.sortingOrder = self.m_curOrder
end

function TutorialGesture:Show()
  if not self.gameObject:IsNull() and not self.gameObject.activeSelf then
    self.gameObject:SetActive(true)
  end
end

function TutorialGesture:GetType()
  return self.Type
end

function TutorialGesture:Hide(ignoreClearTween)
  if self.gameObject.activeSelf then
    UIUtil.SetLocalScale(self.gameObject.transform, 1, 1, 1)
    self.gameObject:SetActive(false)
    if self.m_curOrder ~= self.m_defaultOrder then
      self:UpdateSortingOrder(self.m_defaultOrder)
    end
  end
  if not ignoreClearTween then
    self:_ClearTween()
  elseif self.m_curTween ~= nil then
    self.m_curTween:Pause()
  end
end

function TutorialGesture:OnDestroy()
  self:_ClearTween()
end

function TutorialGesture:_ClearTween()
  if self.m_curTween ~= nil then
    self.m_curTween:Kill()
    self.m_curTween = nil
  end
end

function TutorialGesture:Mirror()
  UIUtil.SetLocalScale(self.gameObject.transform, -1, 1, 1)
end

TutorialGestureTap = setmetatable({
  Type = ETutorialGestureType.Tap
}, TutorialGesture)
TutorialGestureTap.__index = TutorialGestureTap

function TutorialGestureTap:PlayTapAnimation(uiPos, scale)
  uiPos.z = 0
  scale = scale or 1
  self:_ClearTween()
  if self.m_showCallback == nil then
    function self.m_showCallback()
      self:Show()
    end
  end
  self.m_curTween = DOVirtual.DelayedCall(0.5, self.m_showCallback)
  self.transform.localPosition = uiPos
  self.transform:SetLocalScaleXY(scale)
end

TutorialGestureDrag = setmetatable({
  Type = ETutorialGestureType.Drag
}, TutorialGesture)
TutorialGestureDrag.__index = TutorialGestureDrag

function TutorialGestureDrag:PlayDragAnimation(from, to, spriteImg)
  from.z = 0
  to.z = 0
  self:_ClearTween()
  self:Show()
  self.gameObject.transform.localPosition = from
  if IsString(spriteImg) then
    SpriteUtil.SetImage(self.m_img, spriteImg, true)
  elseif spriteImg then
    self.m_img.sprite = spriteImg
    self.m_img:SetNativeSize()
  else
    self.m_img.enabled = false
  end
  self.m_img.transform.localScale = V3Zero
  if self.m_dragInCallback == nil then
    function self.m_dragInCallback()
      if not self.m_animator:IsNull() then
        SafeCall(function()
          self.m_animator:Play("am_shouzhi_huadong_in")
        end)
      end
    end
  end
  if self.m_dragOutCallback == nil then
    function self.m_dragOutCallback()
      if not self.m_animator:IsNull() then
        SafeCall(function()
          self.m_animator:Play("am_shouzhi_huadong_out")
        end)
      end
    end
  end
  local sequence = DOTween.Sequence():SetLoops(-1)
  sequence:AppendInterval(1)
  sequence:AppendCallback(self.m_dragInCallback)
  sequence:AppendInterval(0.6)
  sequence:AppendCallback(function()
    self.m_img.transform.localScale = V3One
  end)
  sequence:Append(self.gameObject.transform:DOLocalMove(to, Vector3.Distance(from, to) / 430))
  sequence:AppendCallback(function()
    self.m_img.transform.localScale = V3Zero
  end)
  sequence:AppendInterval(0.2)
  sequence:AppendCallback(self.m_dragOutCallback)
  sequence:AppendInterval(0.4)
  self.m_curTween = sequence
end

TutorialArrow = {}
TutorialArrow.__index = TutorialArrow

function TutorialArrow:UpdateArrow(pos, rotateZ, dis)
  rotateZ = rotateZ or 0
  dis = dis or 0
  if not self.gameObject.activeSelf then
    self.gameObject:SetActive(true)
  end
  self.gameObject.transform.position = pos
  UIUtil.SetLocalPosition(self.gameObject.transform, nil, nil, 0)
  local radian = -rotateZ * math.pi / 180
  UIUtil.AddLocalPosition(self.gameObject.transform, dis * math.sin(radian), dis * math.cos(radian))
  self.m_pivotRectTrans:SetLocalRotation(0, 0, rotateZ)
end

function TutorialArrow:Hide()
  if self.gameObject.activeSelf then
    self.gameObject:SetActive(false)
  end
end

function TutorialArrow:UpdateDefaultSortingOrder(baseOrder)
  self.m_defaultOrder = baseOrder
  self:UpdateSortingOrder(self.m_defaultOrder)
end

function TutorialArrow:UpdateSortingOrder(baseOrder)
  self.m_curOrder = baseOrder
  self.m_canvas.sortingOrder = baseOrder
end
