Matrix = {}
Matrix.__index = Matrix

function Matrix:Init(boardModel)
  self.m_boardModel = boardModel
  self.m_data = {}
  for y = 1, boardModel:GetVerticalTiles() do
    self.m_data[y] = {}
  end
end

function Matrix:FillWith(value)
  for x = 1, self.m_boardModel:GetHorizontalTiles() do
    for y = 1, self.m_boardModel:GetVerticalTiles() do
      self:Set(x, y, value)
    end
  end
end

function Matrix:Get(x, y)
  return self.m_data[y][x]
end

function Matrix:GetValueOnPosition(position)
  return self:Get(position:GetX(), position:GetY())
end

function Matrix:Set(x, y, value)
  self.m_data[y][x] = value
end

function Matrix:SetValueOnPosition(position, value)
  self:Set(position:GetX(), position:GetY(), value)
end
