TestOrderEnergyDiffWindow = setmetatable({}, BaseWindow)
TestOrderEnergyDiffWindow.__index = TestOrderEnergyDiffWindow

function TestOrderEnergyDiffWindow:Init()
  local info = ""
  local orders = GM.MainBoardModel:GetOrders()
  local diff, log
  for _, order in pairs(orders) do
    diff, log = GM.MainBoardModel:GetOrderFillEnergyDiff(order)
    info = info .. log .. [[


]]
  end
  self.m_text.text = info
end
