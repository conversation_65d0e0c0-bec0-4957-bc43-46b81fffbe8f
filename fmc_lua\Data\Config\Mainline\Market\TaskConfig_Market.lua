return {
  {
    ChapterId = "Market",
    Id = 1,
    Cost = 25,
    Rewards = {
      {Currency = "exp", Amount = 10}
    },
    SlotState = {
      {Slot = "main", State = 9}
    }
  },
  {
    ChapterId = "Market",
    Id = 2,
    StartConditions = {1},
    Cost = 54,
    Rewards = {
      {Currency = "exp", Amount = 20},
      {Currency = "energy", Amount = 10}
    },
    SlotState = {
      {Slot = "floorOld", State = 100},
      {Slot = "trash", State = 100},
      {Slot = "floor", State = 9},
      {Slot = "car", State = 9}
    }
  },
  {
    ChapterId = "Market",
    Id = 3,
    StartConditions = {2},
    Cost = 48,
    Rewards = {
      {Currency = "exp", Amount = 25},
      {Currency = "pd_1_3", Amount = 1}
    },
    SlotState = {
      {Slot = "chair", State = 9}
    }
  },
  {
    ChapterId = "Market",
    Id = 4,
    StartConditions = {3},
    Cost = 33,
    Rewards = {
      {Currency = "exp", Amount = 25},
      {Currency = "pd_2_1", Amount = 1},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "house", State = 9}
    }
  },
  {
    ChapterId = "Market",
    Id = 5,
    StartConditions = {4},
    Cost = 86,
    Rewards = {
      {Currency = "exp", Amount = 40},
      {Currency = "pd_1_3", Amount = 1}
    },
    SlotState = {
      {Slot = "vegetable", State = 9}
    }
  },
  {
    ChapterId = "Market",
    Id = 6,
    StartConditions = {5},
    Cost = 81,
    Rewards = {
      {Currency = "exp", Amount = 40},
      {Currency = "eq_1_3", Amount = 1}
    },
    SlotState = {
      {Slot = "board", State = 9}
    }
  },
  {
    ChapterId = "Market",
    Id = 7,
    StartConditions = {6},
    Cost = 50,
    Rewards = {
      {Currency = "exp", Amount = 40},
      {Currency = "pd_1_3", Amount = 1},
      {Currency = "energy", Amount = 10}
    },
    SlotState = {
      {Slot = "umbrella", State = 9}
    }
  },
  {
    ChapterId = "Market",
    Id = 8,
    StartConditions = {7},
    Cost = 68,
    Rewards = {
      {Currency = "exp", Amount = 40},
      {Currency = "energy", Amount = 20}
    },
    SlotState = {
      {Slot = "bushLeft", State = 9}
    }
  },
  {
    ChapterId = "Market",
    Id = 9,
    StartConditions = {8},
    Cost = 38,
    Rewards = {
      {Currency = "exp", Amount = 40},
      {Currency = "energy", Amount = 30}
    },
    SlotState = {
      {Slot = "bushRight", State = 9}
    }
  },
  {
    ChapterId = "Market",
    Id = 10,
    StartConditions = {9},
    Cost = 47,
    Rewards = {
      {Currency = "exp", Amount = 50},
      {Currency = "energy", Amount = 40}
    },
    SlotState = {
      {Slot = "statue", State = 9}
    }
  }
}
