local SPEED_HEIGHT_BASE = 1840
local TOUCH_MOVE_SPEED_FACTOR = 20.0
local SCROLL_SPEED = 50
local INERTIA_DECELERATION_RATE = 0.9
local INIT_CAMERA_SIZE = 900
local MIN_CAMERA_SIZE = 500
local MAX_CAMERA_SIZE = 1500
local MAX_INERTIA_SPEED = 20000
RoomInput = {
  m_fMovedThreshold = 15,
  m_fClickMaxTime = 0.2,
  m_borders = {}
}
RoomInput.__index = RoomInput

function RoomInput:Awake()
  self.m_nMapWidth = 0
  self.m_nMapHeight = 0
  self:_FitScreen()
  self:SetInitPos()
  self:_AddEventListener()
  self.m_bUpdateEnable = true
end

function RoomInput:RefreshBorder(width, height)
  self.m_nMapWidth = width or 0
  self.m_nMapHeight = height or 0
  self:_UpdateBorders(self.m_camera.orthographicSize)
end

function RoomInput:OnDestroy()
  self:_RemoveEventListener()
end

function RoomInput:SetInitPos()
  local targetPos = Vector3.zero
  self.m_camera.orthographicSize = INIT_CAMERA_SIZE
  self:_UpdateBorders(self.m_camera.orthographicSize)
  self.m_camTrans.localPosition = self:_ClampPos(targetPos)
  self.m_cameraTargetPos = self.m_camTrans.position
end

function RoomInput:SetCameraToPos(position, tween)
  self:_SetCameraTo(position, not tween)
end

function RoomInput:SetUpdateEnable(enable)
  self.m_bUpdateEnable = enable
end

function RoomInput:GetUpdateEnable()
  return self.m_bUpdateEnable
end

function RoomInput:GetCamPos()
  return self.m_camTrans.position
end

function RoomInput:_AddEventListener()
  function self.m_eventTrigger.OnLuaPointerDown(eventData)
    self:_OnPointerDown(eventData)
  end
  
  function self.m_eventTrigger.OnLuaPointerUp(eventData)
    self:_OnPointerUp(eventData)
  end
  
  function self.m_eventTrigger.OnLuaScroll(eventData)
    self:_OnScroll(eventData)
  end
  
  function self.m_eventTrigger.OnLuaDrag(eventData)
    self:_OnDrag(eventData)
  end
end

function RoomInput:_RemoveEventListener()
  self.m_eventTrigger.OnLuaPointerDown = nil
  self.m_eventTrigger.OnLuaPointerUp = nil
  self.m_eventTrigger.OnLuaScroll = nil
  self.m_eventTrigger.OnLuaDrag = nil
end

function RoomInput:_FitScreen()
  local height = Screen.height
  local width = Screen.width
  self.m_distanceFactor = SPEED_HEIGHT_BASE / height
  self.m_ratio = width / height
end

function RoomInput:_UpdateBorders(height)
  local width = self.m_ratio * height
  self.m_borders.left = width - self.m_nMapWidth * 0.5
  self.m_borders.right = self.m_nMapWidth * 0.5 - width
  self.m_borders.bottom = height - self.m_nMapHeight * 0.5
  self.m_borders.top = self.m_nMapHeight * 0.5 - height
end

function RoomInput:ClampPos(pos, size)
  local width = self.m_ratio * size
  pos.x = math.min(pos.x, self.m_nMapWidth * 0.5 - width)
  pos.x = math.max(pos.x, width - self.m_nMapWidth * 0.5)
  pos.y = math.min(pos.y, self.m_nMapHeight * 0.5 - size)
  pos.y = math.max(pos.y, size - self.m_nMapHeight * 0.5)
  return pos
end

function RoomInput:_ClampPos(pos)
  pos.x = math.min(pos.x, self.m_borders.right)
  pos.x = math.max(pos.x, self.m_borders.left)
  pos.y = math.min(pos.y, self.m_borders.top)
  pos.y = math.max(pos.y, self.m_borders.bottom)
  return pos
end

function RoomInput:_OnPointerDown(eventData)
  self.m_fHoldTime = 0
  self.m_bMoved = false
  self.m_startScreenPos = eventData.position
  local cameraPos = self.m_camTrans.position
  self.m_startCameraPos = Vector2(cameraPos.x, cameraPos.y)
  self.m_inertiaSpeed = nil
  self.m_inertiaSpeedMag = 0
  self.m_bHasZoomed = nil
  local roomBubble = self:_GetTouchedBubble(eventData.position)
  if roomBubble then
    roomBubble:OnPointerDown()
  end
end

function RoomInput:_GetTouchedBubble(position)
  local worldPoint = self:_GetScreenWorldPos(position)
  local hitCollider = Physics2D.OverlapPoint(worldPoint.x, worldPoint.y)
  if hitCollider then
    local roomView = GM.ChapterManager:GetActiveRoomView()
    if not roomView then
      return nil
    end
    local roomBubble = roomView:GetRoomBubble(hitCollider)
    return roomBubble
  end
  return nil
end

function RoomInput:_OnPointerUp(eventData)
  EventDispatcher.DispatchEvent(EEventType.RoomTouchesUp)
  if not self.m_startScreenPos then
    return
  end
  local screenOffset = (eventData.position - self.m_startScreenPos) * self.m_distanceFactor * (self.m_camera.orthographicSize / SPEED_HEIGHT_BASE * 2)
  if screenOffset.magnitude > self.m_fMovedThreshold then
    self.m_bMoved = true
  end
  if self.m_startCameraPos and not self.m_bHasZoomed then
    local offset = -screenOffset
    self:_SetCameraTo(self.m_startCameraPos + offset, true)
    self:_Inertia(offset)
  end
  local isClick = not self.m_bMoved and self.m_fHoldTime < self.m_fClickMaxTime
  if isClick then
    local roomBubble = self:_GetTouchedBubble(eventData.position)
    if roomBubble then
      roomBubble:OnClicked()
    end
  end
  self.m_fHoldTime = nil
  self.m_startScreenPos = nil
  self.m_startCameraPos = nil
  self.m_bIsZooming = false
end

local INERTIA_ACCELERATE = 50

function RoomInput:_Inertia()
  if self.m_fHoldTime > 0 then
    self.m_inertiaSpeed = (self.m_cameraTargetPos - self.m_camTrans.position) * TOUCH_MOVE_SPEED_FACTOR * self.m_distanceFactor
    local magnitude = self.m_inertiaSpeed.magnitude
    if magnitude > MAX_INERTIA_SPEED then
      local rate = MAX_INERTIA_SPEED / magnitude
      self.m_inertiaSpeed = self.m_inertiaSpeed * rate
    end
    self.m_inertiaSpeedMag = self.m_inertiaSpeed.magnitude
  end
end

function RoomInput:_OnScroll(eventData)
  local scaleCenter = self:_GetScreenWorldPos(eventData.position)
  local oldSize = self.m_camera.orthographicSize
  local newSize = oldSize - eventData.scrollDelta.y * SCROLL_SPEED * self.m_distanceFactor
  newSize = math.max(MIN_CAMERA_SIZE, math.min(self:GetMaxCameraSize(), newSize))
  self:SetCameraSize(newSize)
  local scaleFactor = newSize / oldSize
  local cameraPos = Vector2(self.m_camTrans.position.x, self.m_camTrans.position.y)
  local newCameraPos = (cameraPos - scaleCenter) * scaleFactor + scaleCenter
  self:_SetCameraTo(newCameraPos, false)
end

function RoomInput:_OnDrag(eventData)
  if not self.m_startScreenPos then
    return
  end
  if self.m_bIsZooming then
    return
  end
  local screenOffset = (eventData.position - self.m_startScreenPos) * self.m_distanceFactor * (self.m_camera.orthographicSize / SPEED_HEIGHT_BASE * 2)
  if screenOffset.magnitude > self.m_fMovedThreshold then
    self.m_bMoved = true
  end
  if self.m_startCameraPos then
    local offset = -screenOffset
    self:_SetCameraTo(self.m_startCameraPos + offset, true)
  end
end

function RoomInput:_SetCameraTo(pos, interpolation)
  self.m_inertiaSpeed = nil
  self.m_inertiaSpeedMag = 0
  pos = self:_ClampPos(pos)
  self.m_cameraTargetPos = Vector3(pos.x, pos.y, self.m_camTrans.position.z)
  if not interpolation then
    self.m_camTrans.position = self.m_cameraTargetPos
  end
end

function RoomInput:Update(dt)
  if not self.m_bUpdateEnable then
    self.m_inertiaSpeed = nil
    self.m_inertiaSpeedMag = 0
    return
  end
  if not (GM.UIManager ~= nil and not GM.UIManager:IsEventLock() and GM.UIManager.allWindowClosed) or GM.TutorialModel:HasAnyStrongTutorialOngoing() then
    self.m_inertiaSpeed = nil
    self.m_inertiaSpeedMag = 0
    return
  end
  if self.m_fHoldTime ~= nil then
    self.m_fHoldTime = self.m_fHoldTime + dt
  end
  if Input.touchCount == 2 then
    local touch1 = Input.GetTouch(0)
    local touch2 = Input.GetTouch(1)
    if touch1.phase == TouchPhase.Moved or touch2.phase == TouchPhase.Moved then
      self.m_bHasZoomed = true
      self.m_fDoubleTouchCurDis = Vector2.Distance(touch1.position, touch2.position)
      if not self.m_bIsZooming then
        self.m_fDoubleTouchStartDis = self.m_fDoubleTouchCurDis
        self.m_bIsZooming = true
        self.m_startCameraSize = self.m_camera.orthographicSize
        local cameraPos = self.m_camTrans.position
        self.m_startCameraPos = Vector2(cameraPos.x, cameraPos.y)
        local screenMiddlePos = (touch1.position + touch2.position) * 0.5
        self.m_startScaleMiddlePos = self:_GetScreenWorldPos(screenMiddlePos)
      end
      local scaleFactor = self.m_fDoubleTouchStartDis / self.m_fDoubleTouchCurDis
      local newSize = self.m_startCameraSize * scaleFactor
      newSize = math.max(MIN_CAMERA_SIZE, math.min(self:GetMaxCameraSize(), newSize))
      scaleFactor = newSize / self.m_startCameraSize
      self.m_camera.orthographicSize = newSize
      self:SetCameraSize(newSize)
      local cameraPos = (self.m_startCameraPos - self.m_startScaleMiddlePos) * scaleFactor + self.m_startScaleMiddlePos
      self:_SetCameraTo(cameraPos, false)
    elseif touch1.phase == TouchPhase.Ended or touch2.phase == TouchPhase.Ended then
      local endTouch, leftTouch
      if touch1.phase == TouchPhase.Ended then
        endTouch = touch1
        leftTouch = touch2
      else
        endTouch = touch2
        leftTouch = touch1
      end
      self.m_fHoldTime = 0
      self.m_bMoved = false
      self.m_bIsZooming = false
      self.m_startScreenPos = nil
      self.m_startCameraPos = nil
    end
  elseif self.m_bIsZooming then
    self.m_bIsZooming = false
    if Input.touchCount == 1 then
      self.m_fHoldTime = 0
      self.m_bMoved = false
      self.m_startCameraPos = nil
      self.m_startScreenPos = nil
    end
  end
  self:_UpdateCameraSize(dt)
  self:_UpdateCameraPos(dt)
end

function RoomInput:_UpdateCameraPos(dt)
  if self.m_inertiaSpeed then
    self.m_camTrans.position = self:_ClampPos(self.m_camTrans.position + self.m_inertiaSpeed * dt)
    self.m_cameraTargetPos = self.m_camTrans.position
    self.m_inertiaSpeed = self.m_inertiaSpeed * INERTIA_DECELERATION_RATE
    self.m_inertiaSpeedMag = self.m_inertiaSpeed.magnitude
    if self.m_inertiaSpeedMag < 0.1 then
      self.m_inertiaSpeed = nil
      self.m_inertiaSpeedMag = 0
    end
  else
    local cameraDistance = (self.m_cameraTargetPos - self.m_camTrans.position).magnitude
    if 0.1 < cameraDistance then
      self.m_camTrans.position = Vector3.MoveTowards(self.m_camTrans.position, self.m_cameraTargetPos, dt * cameraDistance * TOUCH_MOVE_SPEED_FACTOR * self.m_distanceFactor)
    end
  end
end

function RoomInput:_UpdateCameraSize(dt)
  if not self.m_fCameraTargetSize then
    return
  end
  local fSizeDelta = math.abs(self.m_fCameraTargetSize - self.m_camera.orthographicSize)
  if fSizeDelta < 1.0E-4 then
    self.m_fCameraTargetSize = nil
  else
    local fNewSize = Mathf.MoveTowards(self.m_camera.orthographicSize, self.m_fCameraTargetSize, dt * fSizeDelta * 5)
    self:SetCameraSize(fNewSize)
  end
end

function RoomInput:GetMaxCameraSize()
  local mapMaxSize = self.m_nMapHeight * 0.5 - 100
  local mapMaxSizeByWidth = self.m_nMapWidth / self.m_ratio * 0.5
  mapMaxSize = math.min(mapMaxSize, mapMaxSizeByWidth)
  return math.min(mapMaxSize, MAX_CAMERA_SIZE)
end

function RoomInput:CalculateCameraSize(scale)
  local size = INIT_CAMERA_SIZE
  if 0 < scale then
    size = size / scale
  end
  size = math.max(size, MIN_CAMERA_SIZE)
  size = math.min(size, self:GetMaxCameraSize())
  return size
end

function RoomInput:GetBubbleScale()
  local scale = self.m_camera.orthographicSize / INIT_CAMERA_SIZE
  return scale
end

function RoomInput:_GetScreenWorldPos(pos)
  local pos3D = self.m_camera:ScreenToWorldPoint(Vector3(pos.x, pos.y, 0.0))
  return Vector2(pos3D.x, pos3D.y)
end

function RoomInput:SetCameraSize(fSize, bInterpolation)
  if bInterpolation then
    self.m_fCameraTargetSize = fSize
  else
    self.m_camera.orthographicSize = fSize
    self:_UpdateBorders(fSize)
  end
end
