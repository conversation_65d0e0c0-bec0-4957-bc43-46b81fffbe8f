HttpManager = {SendSavedRequestPeriod = 10.0, NetLibLogPeriod = 10.0}
HttpManager.__index = HttpManager
local Base64 = require("Model.Network.Base64")
EHttpReqStatus = {Normal = 1, SsoPooled = 2}
local DBColumnValue = "value"

function HttpManager:Init()
  self.m_fTickCount = 0.0
  self.m_fLastSendTime = 0.0
  self.m_fLastLogTime = 0.0
  self.m_tbFlyingRequests = {}
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.CachedRequests)
end

function HttpManager:Update(dt)
  self.m_fTickCount = self.m_fTickCount + dt
  local notInLoading = GM.SceneManager:GetGameMode() ~= EGameMode.Loading
  local notUploading = GM.SyncModel.eSyncState ~= ESyncState.Uploading
  if notInLoading and notUploading then
    local hasNetwork = Application.internetReachability ~= NetworkReachability.NotReachable
    if self.m_fTickCount - self.m_fLastSendTime > HttpManager.SendSavedRequestPeriod and hasNetwork then
      self.m_fLastSendTime = self.m_fTickCount
      if not self.m_dbTable:IsEmpty() then
        local count = 0
        local tbContent = self.m_dbTable:GetAllInTable()
        local uMsgId, canContinue, markFlyingRequests
        for key, value in pairs(tbContent) do
          uMsgId = tonumber(key)
          if self.m_tbFlyingRequests[uMsgId] == nil then
            canContinue, markFlyingRequests = self:SendSavedRequests(uMsgId, json.decode(value[DBColumnValue]), true)
            if not canContinue then
              break
            end
            if markFlyingRequests ~= false then
              self.m_tbFlyingRequests[uMsgId] = true
              count = count + 1
              if count == 4 then
                break
              end
            end
          end
        end
      end
    end
    if self.m_fTickCount - self.m_fLastLogTime > HttpManager.NetLibLogPeriod then
      self.m_fLastLogTime = self.m_fTickCount
      local strData = CSNetLibManager:GetRecords()
      if strData ~= "[]" then
        local arrData = json.decode(strData)
        if arrData ~= nil then
          for index, value in ipairs(arrData) do
            if string.lower(type(value)) == "table" then
              value.event = "netlib"
              value.userid = GM.UserModel:GetUserId()
              value.cs_time = GM.GameModel:HasServerTime() and GM.GameModel:GetServerTime() or 0
              value.client_time = TimeUtil.GetTimeInSecond()
              value.network = GM.BIManager:GetNetworkReachability()
              value.country = DeviceInfo.GetCountry()
              if not self.m_strOSName then
                self.m_strOSName = DeviceInfo.GetOsName()
              end
              value.os_name = self.m_strOSName
              value.app_version = GameConfig.GetCurrentVersion()
              if not self.m_channelId then
                self.m_channelId = string.format("%u", GameConfig.CURRENT_PLATFORM)
              end
              value.channel_id = self.m_channelId
              GM.BIManager:QueueWithoutFlush(value)
            end
          end
        end
      end
    end
  end
end

function HttpManager:GetNetworkStatus()
  if Application.internetReachability == NetworkReachability.NotReachable then
    return 0
  elseif Application.internetReachability == NetworkReachability.ReachableViaCarrierDataNetwork then
    return 1
  elseif Application.internetReachability == NetworkReachability.ReachableViaLocalAreaNetwork then
    return 2
  end
  return 3
end

function HttpManager:SaveHttpRequest(reqCtx, op)
  if GM.TestModel and GM.TestModel:IsClearing() then
    return
  end
  local uMsgId = reqCtx.MessageId
  local tbReqBasic = {
    url = reqCtx.Url,
    method = reqCtx.Method,
    headers = {},
    body = Base64.encode(string.sub(reqCtx.Body, 1, reqCtx.ContentLength)),
    opName = op
  }
  for key, value in pairs(reqCtx.Headers) do
    tbReqBasic.headers[key] = value
  end
  self.m_dbTable:Set(tostring(uMsgId), DBColumnValue, json.encode(tbReqBasic))
  self.m_tbFlyingRequests[uMsgId] = true
end

function HttpManager:OnFinishSavedHttpRequest(uMsgId, rcode)
  self.m_tbFlyingRequests[uMsgId] = nil
  if rcode == ResultCode.Succeeded then
    self.m_dbTable:Remove(tostring(uMsgId))
  end
end

function HttpManager:SendSavedRequests(uMsgId, tbReqBasic, bHasCallback)
  if tbReqBasic == nil or string.lower(type(tbReqBasic)) ~= "table" or tbReqBasic.headers == nil then
    self.m_dbTable:Remove(tostring(uMsgId))
    return true, false
  end
  local token = tbReqBasic.headers[NetworkConfig.TokenHeaderKey]
  if token ~= nil then
    token = GM.SsoManager:GetToken()
    if token == "" then
      return false
    end
  end
  local strUrl = tbReqBasic.url
  local strOpname = tbReqBasic.opName
  if strOpname ~= nil then
    strUrl = NetworkConfig.GetHttpServerUrl(strOpname)
  end
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(uMsgId, strUrl, tbReqBasic.method, 8000, 0)
  for key, value in pairs(tbReqBasic.headers) do
    if key == NetworkConfig.ClientHeaderKey then
      reqCtx:SetHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader(1))
    elseif key == NetworkConfig.TokenHeaderKey then
      reqCtx:SetHeader(NetworkConfig.TokenHeaderKey, token)
    else
      reqCtx:SetHeader(key, value)
    end
  end
  reqCtx:AppendBody(Base64.decode(tbReqBasic.body))
  if bHasCallback then
    reqCtx:SetCallback(function()
      if GM ~= nil then
        if reqCtx.Rcode == ResultCode.Succeeded then
          local headers = GM.HttpManager:ConvertHeaders(reqCtx.ResponseHeaders)
          local strToken = headers.token
          local strNeedReplace = headers.needreplace
          if strNeedReplace ~= nil then
            GM.SsoManager:OnTokenExpired(token)
          elseif not StringUtil.IsNilOrEmpty(strToken) then
            GM.SsoManager:SetToken(strToken)
          end
        end
        self:OnFinishSavedHttpRequest(reqCtx.MessageId, reqCtx.Rcode)
      end
    end)
  end
  reqCtx:Send()
  return true
end

function HttpManager:ThrowSavedRequests()
  local contents = self.m_dbTable:GetAllInTable()
  local uMsgId, tbReqBasic
  for k, v in pairs(contents) do
    uMsgId = tonumber(k)
    if self.m_tbFlyingRequests[uMsgId] == nil then
      tbReqBasic = json.decode(v[DBColumnValue])
      self:SendSavedRequests(uMsgId, tbReqBasic, false)
    end
  end
  self.m_tbFlyingRequests = {}
  self.m_dbTable:Clear()
end

function HttpManager:ConvertHeaders(tbHeader)
  local headers = {}
  if tbHeader ~= nil then
    for key, value in pairs(tbHeader) do
      headers[string.lower(key)] = value
    end
  end
  return headers
end

function HttpManager:GetServerTime()
  return GM.GameModel:HasServerTime() and GM.GameModel:GetServerTime() or TimeUtil:GetTimeInSecond()
end

function HttpManager:TryFallbackRequest(reqCtx, strIp, strChannel)
  if HttpManager._CanFallback(reqCtx) then
    reqCtx:SetConnectIp(strIp, strChannel)
    reqCtx:Retain()
    reqCtx:Send()
    return true
  end
  return false
end

function HttpManager._CanFallback(reqCtx)
  if not StringUtil.IsNilOrEmpty(reqCtx.Ip) then
    return false
  end
  if reqCtx.Rcode == ResultCode.Error then
    return true
  end
  if reqCtx.Rcode ~= ResultCode.Except then
    return false
  end
  if reqCtx.ErrorMsg == "Couldn't resolve host name" then
    return true
  end
  if reqCtx.ErrorMsg == "SSL connect error" then
    return true
  end
  return false
end
