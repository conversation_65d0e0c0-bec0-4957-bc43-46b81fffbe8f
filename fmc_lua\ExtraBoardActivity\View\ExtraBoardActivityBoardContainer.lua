ExtraBoardActivityBoardContainer = setmetatable({}, BaseUIBoardContainer)
ExtraBoardActivityBoardContainer.__index = ExtraBoardActivityBoardContainer

function ExtraBoardActivityBoardContainer.GetInstance()
  return ExtraBoardActivityBoardContainer.s_instance
end

function ExtraBoardActivityBoardContainer:Init()
  local boardView = GM.ModeViewController:GetExtraBoardActivityBoardView()
  local boardCamera = GM.ModeViewController:GetExtraBoardActivityBoardCamera()
  BaseUIBoardContainer.Init(self, boardView, boardCamera)
  ExtraBoardActivityBoardContainer.s_instance = self
end

function ExtraBoardActivityBoardContainer:GetTargetTrans()
  return self.m_targetTrans
end

function ExtraBoardActivityBoardContainer:OnDestroy()
  ExtraBoardActivityBoardContainer.s_instance = nil
end
