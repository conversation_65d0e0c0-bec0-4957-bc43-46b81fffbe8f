CoinRaceBoardEntry = {}
CoinRaceBoardEntry.__index = CoinRaceBoardEntry

function CoinRaceBoardEntry:Awake()
  self.m_bAwaked = true
  if self.m_bWaitAddListener then
    self:_AddListeners()
  end
end

function CoinRaceBoardEntry:Init(model, orderArea)
  self.m_model = model
  self.m_activityType = self.m_model:GetType()
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  self.m_orderArea = orderArea
  self.m_lastUpdateTime = 0
  self.m_iconArea:Init(self.m_activityType)
  self.m_iconArea:SetInBoardView()
  if self.m_bAwaked then
    self:_AddListeners()
  else
    self.m_bWaitAddListener = true
  end
  self:UpdateContent()
  self:UpdatePerSecond()
end

function CoinRaceBoardEntry:_AddListeners()
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self.UpdateContent)
  EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self.UpdateContent)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnGameModeChanged)
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.OpenMainWindow, {
    obj = self,
    method = self.UpdateContent
  })
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.ScoreChanged, {
    obj = self,
    method = self.OnScoreChanged
  })
end

function CoinRaceBoardEntry:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
  Scheduler.UnscheduleTarget(self)
end

function CoinRaceBoardEntry:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  elseif self.gameObject.activeSelf then
    self.gameObject:SetActive(false)
  end
  local serTime = GM.GameModel:GetServerTime()
  if serTime - self.m_lastUpdateTime > 120 then
    self.m_lastUpdateTime = serTime
    self:UpdateContent()
  end
end

function CoinRaceBoardEntry:UpdateContent()
  if self.m_model == nil then
    return
  end
  if self.m_model:HasNetwork() then
    local rank = self.m_model:GetMyRank(true)
    local bChanged = self.m_rank ~= rank and self.m_rank ~= nil
    local bRankUp = self.m_rank ~= nil and rank < self.m_rank
    if self.m_rank ~= rank then
      self.m_rank = self.m_model:GetMyRank(true)
      if self.m_rank > 0 and self.m_rank <= 5 then
        SpriteUtil.SetImage(self.m_iconImg, self.m_activityDefinition.CoinRaceBoardEntryRankImgPrefix .. self.m_rank)
      end
      if bChanged then
        self.m_rankAnimator:SetTrigger("play")
        if bRankUp then
          self.m_rankEffect:Play()
        end
      end
    end
  end
  UIUtil.SetActive(self.m_bgEffectGo, self.m_model:HasNetwork() and self.m_model:CanClaimReward())
end

function CoinRaceBoardEntry:OnScoreChanged(bChanged)
  self:UpdateContent()
end

function CoinRaceBoardEntry:OnBtnClicked()
  local state = self.m_model:GetState()
  if state == ActivityState.Preparing then
    GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, false, false)
  elseif state == ActivityState.Started then
    if self.m_model:IsInRace() then
      self.m_model:TryOpenMainWindow()
    else
      GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, false, true)
    end
  end
end

function CoinRaceBoardEntry:_OnGameModeChanged()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Main or self.m_model == nil or not self.m_model:IsInRace() then
    return
  end
  local score = self.m_model:GetMyScore()
  local targetScore = self.m_model:GetTargetScore()
  if targetScore == 0 or self.m_avatar == nil then
    return
  end
end

function CoinRaceBoardEntry:GetIconArea()
  return self.m_iconArea
end
