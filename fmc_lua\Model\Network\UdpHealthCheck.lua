UdpHealthCheck = {}
UdpHealthCheck.__index = UdpHealthCheck
UdpHealthCheck.Threshold = 3.0

function UdpHealthCheck.Create(strUrl)
  local check = setmetatable({}, UdpHealthCheck)
  check:_Init(strUrl)
  return check
end

function UdpHealthCheck:_Init(strUrl)
  self.m_strUrl = strUrl
  self.m_fLastCheck = 0.0
  self.m_bHealth = true
  self.m_bEnable = not MINIGAME
  self.m_healthCount = 0
end

function UdpHealthCheck:Update(fTickCount)
  if fTickCount - self.m_fLastCheck >= 10.0 then
    self.m_fLastCheck = fTickCount
    if self:Enable() and not self:Available() then
      self:HealthCheck()
    end
  end
end

function UdpHealthCheck:UpdateUdpStatus(bRet)
  if not (not bRet or self:Available()) or not bRet and self:Available() then
    self.m_healthCount = self.m_healthCount + 1
  else
    self.m_healthCount = 0
  end
  if self.m_healthCount >= UdpHealthCheck.Threshold then
    self.m_bHealth = not self.m_bHealth
    self.m_healthCount = 0
  end
end

function UdpHealthCheck:Available()
  return self.m_bHealth
end

function UdpHealthCheck:Enable()
  return self.m_bEnable
end

function UdpHealthCheck:GetUrl()
  return self.m_strUrl
end

function UdpHealthCheck:HealthCheck()
  local reqCtx = CSNetLibManager:CreateGeneralUdpRequest(self.m_strUrl, 3000, 0)
  local writer = CSNetLibManager:CreateBufferBlockWriter(reqCtx.ReqDatagram)
  UdpMessage.SerializeMessageId(writer, reqCtx.MessageId)
  reqCtx:SetCallback(function()
    if GM ~= nil then
      self:UpdateUdpStatus(reqCtx.Rcode == ResultCode.Succeeded)
    end
  end)
  CSNetLibManager:ReleaseBufferBlockWriter(writer)
  reqCtx:Send()
end
