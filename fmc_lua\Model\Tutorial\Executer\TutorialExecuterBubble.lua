local Step = {TapBubble = "1", BreakBubble = "2"}
local EStep2TextKey = {
  [Step.TapBubble] = "tutorial_bubble_1",
  [Step.BreakBubble] = "tutorial_bubble_2"
}
local EStep2TextAnchorPercent = {
  [Step.BreakBubble] = 77
}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.BoardPointerUp, self, self._OnBoardPointerUp)
  EventDispatcher.AddListener(EEventType.BubbleDisposed, self, self._OnBubbleDisposed)
end

function Executer:TryStartTutorial()
  if self:_CanExecuteStep1() then
    self:_ExecuteStep1()
    return true
  end
end

function Executer:_OnBoardPointerUp()
  if self.m_strOngoingDatas == Step.TapBubble and self.m_gesture and self.m_bubbleItem == TutorialHelper.GetSelectedItem() then
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
    self:_ExecuteStep2()
  end
end

function Executer:_OnBubbleDisposed()
  if self.m_strOngoingDatas == Step.BreakBubble and self.m_gesture then
    TutorialHelper.DehighlightBoardInfoUnlockBtnGo()
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
  end
end

function Executer:_CanExecuteStep1()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return false
  end
  local filter = function(itemModel)
    local itemBubble = itemModel:GetComponent(ItemBubble)
    return itemBubble ~= nil
  end
  local curBubbles = GM.MainBoardModel:FilterItems(filter)
  if 1 <= #curBubbles then
    self.m_bubbleItem = curBubbles[1]
    return true
  end
end

function Executer:_ExecuteStep1()
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.TapBubble
  self:_SaveOngoingDatas()
  local tapPos = self.m_bubbleItem:GetPosition()
  local itemBubble = self.m_bubbleItem:GetComponent(ItemBubble)
  if itemBubble then
    itemBubble:SetTutorialCD()
  else
    self:Finish()
    return
  end
  TutorialHelper.MaskOnItemBoard(tapPos, tapPos)
  TutorialHelper.ShowDialogWithBoardMaskArea(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), tapPos, tapPos)
  self.m_gesture = TutorialHelper.TapOnBoard(tapPos, false)
  if not self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish()
    return
  end
  GM.TutorialModel:SetForceSourceBoardPosition(tapPos)
  GM.TutorialModel:SetForceTargetBoardPosition(tapPos)
end

function Executer:_ExecuteStep2()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.BreakBubble
  self:_SaveOngoingDatas()
  TutorialHelper.WholeMask()
  local btn = TutorialHelper.HighlightBoardInfoUnlockBtnGo()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  self.m_gesture = TutorialHelper.TapOnBoardInfoBarButton(btn)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
