local Step = {
  Dialogue = "1",
  HighlightFinalRewards = "2",
  HighlightProgress = "3",
  ClickProgress = "4"
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._OnCloseView)
  for _, activityDefinition in pairs(ProgressActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChange)
  end
end

function Executer:_OnOpenView(msg)
  for activityType, activityDefinition in pairs(ProgressActivityDefinition) do
    if msg.name == activityDefinition.MainWindowPrefabName then
      if StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
        self.m_windowName = msg.name
        self:_ExecuteStep1()
      else
        self:Finish(self.m_gesture)
      end
      return
    elseif msg.name == activityDefinition.RewardProgressWindowPrefabName then
      self:Finish(self.m_gesture)
    end
  end
end

function Executer:_OnCloseView(msg)
  for _, activityDefinition in pairs(ProgressActivityDefinition) do
    if msg.name == activityDefinition.MainWindowPrefabName then
      self:Finish(self.m_gesture)
      return
    end
  end
end

function Executer:_OnStateChange(msg)
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    self:Finish(self.m_gesture)
  end
end

function Executer:_GetWindow()
  local window = GM.UIManager:GetOpenedViewByName(self.m_windowName)
  if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
    self:Finish(self.m_gesture)
    return
  end
  return window
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.Dialogue
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    TutorialHelper.WholeMask(function()
      self:_ExecuteStep2()
    end)
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText("tutorial_progress_1"), 60)
  end, 0.5)
end

function Executer:_ExecuteStep2()
  self.m_strOngoingDatas = Step.HighlightFinalRewards
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local window = self:_GetWindow()
  if not window then
    return
  end
  local transf = window:GetFinalPrizeTrans()
  TutorialHelper.UpdateMask(transf.position, transf.sizeDelta + Vector2(50, 60), function()
    self:_ExecuteStep3()
  end, false)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText("tutorial_progress_2_new"), 60)
end

function Executer:_ExecuteStep3()
  self.m_strOngoingDatas = Step.HighlightProgress
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local window = self:_GetWindow()
  if not window then
    return
  end
  local transf = window:GetSliderTrans()
  TutorialHelper.UpdateMask(transf.position, transf.sizeDelta + Vector2(0, 50), function()
    self:_ExecuteStep4()
  end, false)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText("tutorial_progress_3"), 35)
end

function Executer:_ExecuteStep4()
  self.m_strOngoingDatas = Step.ClickProgress
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local window = self:_GetWindow()
  if not window then
    return
  end
  local transf = window:GetSliderBgTrans()
  TutorialHelper.UpdateMask(transf.position + Vector3(0, 2, 0), transf.rect.size + Vector2(-12, -4), nil, true)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText("tutorial_progress_4"), 35)
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(transf)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
