TestCodeEditWindow = setmetatable({
  canClickWindowMask = false,
  canCloseByAndroidBack = false,
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestCodeEditWindow.__index = TestCodeEditWindow

function TestCodeEditWindow:Init()
  if not GameConfig.IsTestMode() then
    return
  end
  if TestCodeEditWindow.s_strCacheText then
    self.m_InputField.text = TestCodeEditWindow.s_strCacheText
  end
end

function TestCodeEditWindow:_OnOkButtonClick()
  if not GameConfig.IsTestMode() then
    return
  end
  local testCode = self.m_InputField.text
  if StringUtil.IsNilOrEmpty(testCode) then
    Log.Info("代码为空")
    return
  end
  TestCodeEditWindow.s_strCacheText = testCode
  local path = FileUtils.WritablePath .. "TestCodeExecuter.lua"
  if not string.find(package.path, FileUtils.WritablePath) then
    package.path = package.path .. ";" .. FileUtils.WritablePath .. "?.lua"
    Log.Info("将WritablePath添加到lua的搜索路径")
  end
  local fs = CS.System.IO.FileStream(path, CS.System.IO.FileMode.Create, CS.System.IO.FileAccess.ReadWrite)
  local sw = CS.System.IO.StreamWriter(fs)
  fs:SetLength(0)
  sw:Write(testCode)
  sw:Close()
  fs:Close()
  sw:Dispose()
  fs:Dispose()
  xpcall(function()
    require("TestCodeExecuter")
  end, function(err)
    Log.Info("代码有问题，请检查代码  ErrorMsg : " .. err, nil)
  end)
  package.loaded.TestCodeExecuter = nil
end
