BlindChestBaseWindow = setmetatable({}, BaseWindow)
BlindChestBaseWindow.__index = BlindChestBaseWindow

function BlindChestBaseWindow:Init(model, state, bAutoPopup)
  self.m_model = model
  self.m_definition = BlindChestDefinition[self.m_model:GetType()]
  self.m_state = state
  self.m_model:SetWindowOpened(state)
  EventDispatcher.AddListener(self.m_definition.StateChangedEvent, self, self._CheckState)
  self.m_id = model:GetId()
  self:LogWindowAction(EBIType.UIActionType.Open, {
    bAutoPopup and EBIReferType.AutoPopup or EBIReferType.UserClick
  })
end

function BlindChestBaseWindow:_CheckState()
  if self.m_model:GetState() ~= self.m_state or self.m_model:GetState() == ActivityState.Released or self.m_id ~= self.m_model:GetId() then
    self:Close()
  end
end

function BlindChestBaseWindow:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

BlindChestHelpWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BlindChestBaseWindow)
BlindChestHelpWindow.__index = BlindChestHelpWindow

function BlindChestHelpWindow:Init(model, state)
  BlindChestBaseWindow.Init(self, model, state)
  UIUtil.SetActive(self.m_probUrlGo, true)
end

function BlindChestHelpWindow:_OnUrlClicked()
  CSPlatform:OpenURL(NetworkConfig.GetProbabilityDisclosureLink())
end

BlindChestTip = {}
BlindChestTip.__index = BlindChestTip

function BlindChestTip:Update()
  if CS.UnityEngine.EventSystems.EventSystem.current == nil then
    return
  end
  if self.m_currentSelected ~= CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject and CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject ~= self.gameObject then
    self:Hide()
    return
  end
end

function BlindChestTip:Show(textKey)
  self.m_currentSelected = CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject
  self.gameObject:SetActive(true)
  self.gameObject.transform:SetLocalScaleXY(0)
  self.gameObject.transform:DOScale(1, 0.2)
  self.m_bShow = true
  if textKey ~= nil then
    self.m_text.text = GM.GameTextModel:GetText(textKey)
  end
end

function BlindChestTip:Hide()
  if self.m_bShow == false then
    return
  end
  self.m_bShow = false
  self.gameObject.transform:SetLocalScaleXY(1)
  self.gameObject.transform:DOScale(0, 0.2):OnComplete(function()
    if self and not self.gameObject:IsNull() then
      self.gameObject:SetActive(false)
    end
  end)
end
