TestAllItemContent = setmetatable({}, TestAllItemContent)
TestAllItemContent.__index = TestAllItemContent

function TestAllItemContent.GetInstance()
  return TestAllItemContent.m_instance
end

function TestAllItemContent:Awake()
  self:UpdateContent()
  
  function self.m_eventTrigger.OnLuaPointerDown(eventData)
    self:_OnPointerDown(eventData)
  end
  
  function self.m_eventTrigger.OnLuaDrag(eventData)
    self:_OnDrag(eventData)
  end
  
  function self.m_eventTrigger.OnLuaPointerUp(eventData)
    self:_OnPointerUp(eventData)
  end
  
  if TestFavoriteItemContent.GetInstance() ~= nil then
    TestFavoriteItemContent.GetInstance():OnCloseButtonClicked()
  end
  TestAllItemContent.m_instance = self
end

function TestAllItemContent:Init()
  local posStr = PlayerPrefs.GetString(EPlayerPrefKey.FavoriteContentOpenPos, "")
  if posStr ~= "" then
    local arrPos = StringUtil.SplitToNum(posStr, ",")
    UIUtil.SetLocalPosition(self.gameObject.transform, arrPos[1], arrPos[2])
  end
end

function TestAllItemContent:UpdateContent()
  self.m_arrItemTypes = GM.TestModel:GetItemTypesInSequence()
  self.m_index = 1
  
  function self.m_scheduler()
    for i = 1, 6 do
      if self.m_index > #self.m_arrItemTypes then
        self.m_scheduler = nil
        Scheduler.Unschedule(self.m_scheduler, self)
        return
      end
      local obj = GameObject.Instantiate(self.m_originGo, self.m_contentRectTrans)
      obj:SetActive(true)
      local cell = obj:GetLuaTable()
      cell:Init(self.m_arrItemTypes[self.m_index])
      self.m_index = self.m_index + 1
    end
  end
  
  Scheduler.Schedule(self.m_scheduler, self)
end

function TestAllItemContent:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  TestAllItemContent.m_instance = nil
  if self.m_scheduler ~= nil then
    Scheduler.Unschedule(self.m_scheduler, self)
  end
end

function TestAllItemContent:_OnPointerDown(eventData)
  self.m_bDragging = false
  self.m_startPos = PositionUtil.UICameraScreen2World(eventData.position)
  self.m_localPos = self.gameObject.transform.localPosition
end

function TestAllItemContent:_OnDrag(eventData)
  self.m_bDragging = true
  local pos = PositionUtil.UICameraScreen2World(eventData.position)
  self.gameObject.transform.localPosition = self.m_localPos + pos - self.m_startPos
end

function TestAllItemContent:_OnPointerUp(eventData)
  local lastPos = self.gameObject.transform.localPosition
  PlayerPrefs.SetString(EPlayerPrefKey.FavoriteContentOpenPos, lastPos.x .. "," .. lastPos.y)
end

function TestAllItemContent:OnCloseButtonClicked()
  self.gameObject:RemoveSelf()
end

function TestAllItemContent:AddOrderItems()
  GM.TestModel:AddOrderItems()
end

function TestAllItemContent:AddNextOrderItems()
  GM.TestModel:AddNextOrderItems()
end
