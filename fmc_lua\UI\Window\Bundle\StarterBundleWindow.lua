StarterBundleWindow = setmetatable({}, BundleNormalWindow)
StarterBundleWindow.__index = StarterBundleWindow

function StarterBundleWindow:Init(bundleType, dataGroup, bUserClick, eTriggerType)
  BundleNormalWindow.Init(self, bundleType, dataGroup, bUserClick, eTriggerType)
  self.m_spineRoleTb:Init()
  self.m_spineRoleTb.gameObject:SetActive(false)
  DelayExecuteFuncInView(function()
    self.m_spineRoleTb.gameObject:SetActive(true)
    self.m_spineRoleTb:PlayAnimation("appear", function()
      self.m_spineRoleTb:PlayAnimation("idle", nil, true, false)
    end, false, true)
  end, 0.15, self, false)
end
