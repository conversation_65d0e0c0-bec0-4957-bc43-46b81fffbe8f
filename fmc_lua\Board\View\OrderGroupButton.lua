OrderGroupButton = {}
OrderGroupButton.__index = OrderGroupButton

function OrderGroupButton:Init(boardView)
  self.m_boardView = boardView
  self.m_orderModel = GM.MainBoardModel:GetOrderModel()
  self.m_slider.value = 0
  self.gameObject:SetActive(true)
  self:UpdateContent()
  EventDispatcher.AddListener(EEventType.UpdateOrderGroupButton, self, self.UpdateContent)
  EventDispatcher.AddListener(EEventType.ShowItemTestInfoChanged, self, self._OnShowItemTestInfoChanged)
  EventDispatcher.AddListener(EEventType.FlambeTimeChanged, self, self._OnFlambeTimeChanged)
  if GM.ConfigModel:ButtonShowDay() then
    UIUtil.SetAnchoredPosition(self.m_boxContainerTrans, 64, -61)
    UIUtil.SetLocalScale(self.m_boxContainerTrans, 0.4, 0.4)
    self.m_orderDayCalender.gameObject:SetActive(true)
  else
    UIUtil.SetAnchoredPosition(self.m_boxContainerTrans, 0, 54)
    UIUtil.SetLocalScale(self.m_boxContainerTrans, 1, 1)
    self.m_orderDayCalender.gameObject:SetActive(false)
  end
end

function OrderGroupButton:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function OrderGroupButton:UpdateContent(animated)
  if self.m_orderModel == nil then
    return
  end
  local visible = self.m_orderModel:IsOrderGroupButtonVisible()
  if self.m_visible == nil or self.m_visible ~= visible then
    self.transform:SetLocalScale(visible and 1 or 0)
  end
  self.m_visible = visible
  if visible then
    if GM.ConfigModel:ButtonShowDay() then
      self.m_orderDayCalender:UpdateContent()
    end
    self.m_flambeTimeBoard:UpdateFiredCount()
    self:_OnFlambeTimeChanged()
    local finishedCount, totalCount = self.m_orderModel:GetGroupProgress()
    self.m_progressText.text = finishedCount .. "/" .. totalCount
    local dt = 0.2
    local value = finishedCount / totalCount
    if animated then
      self.m_slider:DOValue(value, dt)
    else
      self.m_slider.value = value
    end
    self:_OnShowItemTestInfoChanged()
  end
end

function OrderGroupButton:IsVisible()
  return self.m_visible
end

function OrderGroupButton:_OnFlambeTimeChanged()
  local showFire = GM.FlambeTimeModel:IsFlambeTime()
  if self.m_flambeFire.activeSelf and not showFire then
    self.m_flambeFire:SetActive(false)
  elseif not self.m_flambeFire.activeSelf and showFire then
    self.m_flambeFire:SetActive(true)
    local isLink = GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link
    self.m_linkFireGo:SetActive(isLink)
    self.m_modeFireGo:SetActive(not isLink)
    self.m_flambeFire.transform.localScale = V3Zero
    self.m_flambeFire.transform:DOScale(1, 0.2)
  end
end

function OrderGroupButton:GetBoxPosAndScale()
  local boxTrans = self.m_normalBoxTrans
  local scale = 0.4 * (GM.ConfigModel:ButtonShowDay() and 0.4 or 1)
  local boxType = EBoxRewardType.Red
  return boxTrans.position + TutorialLayer.CameraOffset, scale, boxType
end

function OrderGroupButton:OnClick()
  if self.m_orderModel:CanClaimGroupReward() then
    GM.UIManager:OpenView(UIPrefabConfigName.OrderGroupFinishWindow)
  else
    GM.UIManager:OpenView(UIPrefabConfigName.OrderDayWindow)
  end
end

function OrderGroupButton:FlyProgressFromPos(fromPosition, callback)
  callback = callback or function()
  end
  if GM.TutorialModel:IsTutorialOnGoing(ETutorialId.OrderGroup) then
    callback()
    return
  end
  if not self.m_visible then
    callback()
    return
  end
  local finishedCount, totalCount = self.m_orderModel:GetGroupProgress()
  if self.m_orderModel:GetTotalFinishedGroupCount() == 0 and finishedCount == 0 then
    callback()
    return
  end
  local toPosition = self.m_iconTrans.position
  local s = DOTween.Sequence()
  local flyEffectGo = Object.Instantiate(self.m_flyEffectPrefab, GM.UIManager:GetCanvasRoot())
  flyEffectGo.transform.position = fromPosition
  s:Append(flyEffectGo.transform:DOMove(toPosition, 0.7))
  s:AppendCallback(function()
    flyEffectGo:RemoveSelf()
    self:UpdateContent(true)
    callback()
  end)
end

function OrderGroupButton:_OnShowItemTestInfoChanged()
  if not GM.UIManager:CanShowTestUI() then
    self.m_testText.gameObject:SetActive(false)
    return
  end
  self.m_testText.gameObject:SetActive(true)
  local chapterId, groupId = self.m_orderModel:GetOrderGroupInfo()
  self.m_testText.text = chapterId .. "@" .. groupId
end

function OrderGroupButton:TestFinishCurrentOrders()
  GM.TestModel.bFinishingOrders = true
  local orders = Table.ShallowCopy(GM.MainBoardModel:GetOrders())
  for _, order in pairs(orders) do
    GM.MainBoardModel:FinishOrder(order, false)
  end
  GM.TestModel.bFinishingOrders = false
end
