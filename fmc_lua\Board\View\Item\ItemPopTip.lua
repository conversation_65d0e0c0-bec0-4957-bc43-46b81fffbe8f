ItemPopTip = {}
ItemPopTip.__index = ItemPopTip
EItemPopTipType = {
  InstrPop = 1,
  MatPopWithInstruLack = 2,
  MatPopWithInstruBusy = 3
}

function ItemPopTip:PopTip(popItemCode, tipType)
  EventDispatcher.AddListener(EEventType.OrderItemCookPopTip, self, self._OnOrderItemCookPopTip)
  self.m_bgInstruGo:SetActive(tipType == EItemPopTipType.InstrPop)
  self.m_bgMatGo:SetActive(tipType ~= EItemPopTipType.InstrPop)
  self.m_noticeGo:SetActive(tipType == EItemPopTipType.MatPopWithInstruBusy)
  if tipType == EItemPopTipType.MatPopWithInstruLack then
    UIUtil.SetAlpha(self.m_popIconImage, 0.5)
  end
  self.m_popTrans.localScale = V3Zero
  local imageName = GM.ItemDataModel:GetSpriteName(popItemCode)
  SpriteUtil.SetSpriteRenderer(self.m_popIconImage, imageName)
  local showTween = DOTween.Sequence()
  showTween:AppendInterval(0.5)
  showTween:Append(self.m_popTrans:DOScale(1.03, 0.15))
  showTween:Append(self.m_popTrans:DOScale(1, 0.07))
  showTween:AppendInterval(3)
  showTween:Append(self.m_popTrans:DOScale(0, 0.15))
  showTween:OnComplete(function()
    self:_StopPopTween()
  end)
end

function ItemPopTip:_OnOrderItemCookPopTip(message)
  if not message.tip then
    self:_StopPopTween()
  end
end

function ItemPopTip:_StopPopTween()
  self.gameObject:RemoveSelf()
end

function ItemPopTip:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end
