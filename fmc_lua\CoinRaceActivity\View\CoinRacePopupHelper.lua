CoinRacePopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
CoinRacePopupHelper.__index = CoinRacePopupHelper

function CoinRacePopupHelper.Create()
  local helper = setmetatable({}, CoinRacePopupHelper)
  helper:Init()
  return helper
end

function CoinRacePopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(CoinRaceActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
  EventDispatcher.AddListener(EEventType.ActivityRaceCompleted, self, self._OnStateChanged)
end

function CoinRacePopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function CoinRacePopupHelper:CheckPopup()
  for activityType, activityDefinition in pairs(CoinRaceActivityDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Preparing then
      local curGameMode = GM.SceneManager:GetGameMode()
      if not model:HasWindowOpenedOnce(ActivityState.Preparing) and curGameMode == EGameMode.Main then
        return activityDefinition.NoticeWindowPrefabName, {
          activityType,
          true,
          false
        }
      end
    elseif state == ActivityState.Started then
      if self:_CanPopupDailyPopup(model) then
        return activityDefinition.NoticeWindowPrefabName, {
          activityType,
          true,
          true
        }
      elseif model:HasNetwork() and model:CanClaimReward() then
        return activityDefinition.MainWindowPrefabName, {activityType, true}
      end
    elseif state == ActivityState.Ended and not model:HasWindowOpenedOnce(ActivityState.Ended) and model:HasWindowOpenedOnce(ActivityState.Started) then
      if model:HasNetwork() and model:CanClaimReward() then
        return activityDefinition.MainWindowPrefabName, {activityType, true}
      end
      if not model:CanClaimReward() then
        return activityDefinition.CompleteWindowPrefabName, {activityType}
      end
    end
  end
  return nil
end

function CoinRacePopupHelper:_CanPopupDailyPopup(model)
  if model:IsDeadline() then
    return false
  end
  if model:IsInRace() then
    return false
  end
  local serverTime = GM.GameModel:GetServerTime()
  local openTime = PlayerPrefs.GetInt(EPlayerPrefKey.CoinRaceDailyOpenTime, 0)
  if serverTime // Sec2Day > openTime // Sec2Day then
    return true
  end
  return false
end
