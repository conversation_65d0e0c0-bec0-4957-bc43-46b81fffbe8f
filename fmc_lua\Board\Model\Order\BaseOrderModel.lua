BaseOrderModel = {}
BaseOrderModel.__index = BaseOrderModel

function BaseOrderModel:Init(dbMetaTable, dbTable, boardModel)
  self.m_boardModel = boardModel
  self.m_orderIdGenerator = DBIdGenerator.Create()
  self.m_dbMetaTable = dbMetaTable
  self.m_dbTable = dbTable
end

function BaseOrderModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function BaseOrderModel:GenerateOrderId()
  return self.m_orderIdGenerator:Generate()
end

function BaseOrderModel:LoadFileConfig()
  Log.Assert(false, "LoadFileConfig()是抽象接口")
end

function BaseOrderModel:OnSyncDataFinished()
  self.m_orders = self:_DBGetOrders()
end

function BaseOrderModel:LateInit()
  self.m_bLateInited = true
  self:_TryFillOrders()
end

function BaseOrderModel:_DBGetOrders()
  Log.Assert(false, "_DBGetOrders()是抽象接口")
end

function BaseOrderModel:_DBAddOrder(order)
  Log.Assert(false, "_DBAddOrder()是抽象接口")
end

function BaseOrderModel:_DBRemoveOrder()
  Log.Assert(false, "_DBRemoveOrder()是抽象接口")
end

function BaseOrderModel:_TryFillOrders()
  Log.Assert(false, "_TryFillOrders()是抽象接口")
end

function BaseOrderModel:FinishOrder(order, removeItemInfo)
  if not self:ContainsOrder(order) then
    return false
  end
  order:SetFinished()
  local costInfo, curDayEnergy, pastDayEnergy = self:_FormatItemCost(removeItemInfo)
  self:_LogOrderAction(order, EBIOrderAction.OrderFinish, costInfo)
  if order:GetType() == OrderType.Fixed then
    self:_AddOrderGroupCostEnergy(curDayEnergy, pastDayEnergy)
  end
  self:_TryFillOrders()
  EventDispatcher.DispatchEvent(EEventType.OrderFinished, {order = order})
  return true
end

function BaseOrderModel:GetMetaData()
  return self.m_dbMetaTable
end

function BaseOrderModel:GetData()
  return self.m_dbTable
end

function BaseOrderModel:ContainsOrder(order)
  return Table.Contain(self.m_orders, order)
end

function BaseOrderModel:UpdateState(codeCountMap, itemCookCmp)
  for _, order in pairs(self.m_orders) do
    order:UpdateState(self.m_boardModel, codeCountMap, itemCookCmp)
  end
end

function BaseOrderModel:_LogOrderAction(order, action, costInfo, extInfo)
  local type = ""
  if order:GetType() == OrderType.Fixed then
    type = "fixed"
  elseif order:GetType() == OrderType.BakeOut then
    type = "bakeout"
  else
    type = "random"
  end
  local activityModel
  extInfo = extInfo or ""
  for activityType, _ in pairs(SurpriseChestActivityDefinition) do
    activityModel = GM.ActivityManager:GetModel(activityType)
    if activityModel ~= nil and activityModel:CanAcquireChestRewards(order:GetId()) then
      extInfo = extInfo == "" and "" or extInfo .. ";"
      extInfo = extInfo .. "surprise_chest:1"
      break
    end
  end
  if GM.FlambeTimeModel:IsOrderShowFire(order:GetId()) then
    extInfo = extInfo == "" and "" or extInfo .. ";"
    extInfo = extInfo .. "flambe:" .. GM.FlambeTimeModel:GetFlambeTimeType()
  end
  local readyinfo, other
  if action == EBIOrderAction.OrderUnlock then
    readyinfo = {}
    other = {}
    local codeCountMap = self.m_boardModel:GetCodeCountMap(true, false, true)
    for _, requirement in ipairs(order:GetRequirements()) do
      readyinfo[requirement] = codeCountMap[requirement]
    end
    for _, v in pairs(self.m_orders) do
      if v ~= order then
        other[#other + 1] = v:GetBIRequirements()
      end
    end
  end
  local orderScore = order:GetOrderScore()
  GM.BIManager:LogOrder(order:GetId(), type, order:GetBIRequirements(), action, orderScore, order:IsTimelimitOrder(), order:GetCreateTime(), GM.GameModel:GetServerTime(), order:GetRewards(), costInfo, extInfo, readyinfo, other)
end

function BaseOrderModel:GetOrders()
  return self.m_orders
end

function BaseOrderModel:GetOrdersCount()
  return Table.GetMapSize(self.m_orders)
end

function BaseOrderModel:_FormatItemCost(removeItemInfo)
  local curDayEnergy = 0
  local pastDayEnergy = 0
  local list = {}
  for _, items in pairs(removeItemInfo) do
    for _, item in pairs(items) do
      if IsTable(item) then
        list[#list + 1] = ItemModelHelper.FormatCost2String(item)
        curDayEnergy = curDayEnergy + (item.costEnergyCurDay or 0)
        pastDayEnergy = pastDayEnergy + (item.costEnergy or 0)
      end
    end
  end
  return table.concat(list, ";"), curDayEnergy, pastDayEnergy
end

function BaseOrderModel:_AddOrderGroupCostEnergy(curDayCount, pastDayCount)
  Log.Assert(false, "_AddOrderGroupCostEnergy()是抽象接口")
end
