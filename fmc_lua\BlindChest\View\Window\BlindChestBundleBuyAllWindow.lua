BlindChestBundleButton = {}
BlindChestBundleButton.__index = BlindChestBundleButton

function BlindChestBundleButton:Init(bundleType, groupData, actModel)
  self.m_bundleType = bundleType
  self.m_model = GM.BundleManager:GetModel(bundleType)
  self.m_actModel = actModel
  self.m_groupData = groupData
  self.m_viewName = BundleUIType[self.m_groupData:GetBundleUIType()].window
  self:UpdatePerSecond()
  self:_UpdateRedPoint()
  self:_UpdateChainRedPoint()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnViewOpen)
  EventDispatcher.AddListener(EEventType.BundleChainActivityContentUpdateEvent, self, self._UpdateChainRedPoint)
end

function BlindChestBundleButton:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BlindChestBundleButton:_OnViewOpen(msg)
  if msg and msg.name == self.m_viewName then
    self:_UpdateRedPoint()
  end
end

function BlindChestBundleButton:UpdatePerSecond()
  local restDuration = self.m_model:GetRestDuration(self.m_groupData)
  if 0 < restDuration then
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(restDuration)
  else
    self.gameObject:SetActive(false)
  end
end

function BlindChestBundleButton:OnClicked()
  self.m_redPointGo:SetActive(false)
  self.m_actModel:SetBundleWindowShown(self.m_groupData:GetGroupId())
  self.m_viewName = self.m_model:OpenBundleView(self.m_groupData, true, true)
end

function BlindChestBundleButton:_UpdateRedPoint()
  if self.m_bundleType == EBundleType.NewChain then
    UIUtil.SetActive(self.m_redPointGo, false)
    return
  end
  self.m_redPointGo:SetActive(not self.m_actModel:HasBundleWindowShown(self.m_groupData:GetGroupId()))
end

function BlindChestBundleButton:_UpdateChainRedPoint()
  if self.m_bundleType ~= EBundleType.NewChain then
    UIUtil.SetActive(self.m_redPointChainGo, false)
    return
  end
  local freeNum = self.m_model:GetFreeRewardNum(self.m_groupData)
  if 0 < freeNum then
    self.m_redPointChainGo:SetActive(true)
    self.m_redPointChainText.text = freeNum
    return
  end
  self.m_redPointChainGo:SetActive(false)
end

BlindChestBundleBuyAllWindow = setmetatable({}, BundleBuyAllWindow)
BlindChestBundleBuyAllWindow.__index = BlindChestBundleBuyAllWindow

function BlindChestBundleBuyAllWindow:Init(dataGroup, bUserClick, eTriggerType)
  if dataGroup == nil then
    self:Close()
    return
  end
  local actModel = GM.ActivityManager:GetOneActivityModel(ActivityModelType.BlindChest)
  if not actModel then
    self:Close()
    return
  end
  actModel:SetBundleWindowShown(dataGroup:GetGroupId())
  self.m_dataGroup = dataGroup
  self.m_list = {
    self.m_bundleCell1,
    self.m_bundleCell2,
    self.m_bundleCell3
  }
  self.m_model = GM.BundleManager:GetModel(EBundleType.BuyAll)
  self.m_expireTime = self.m_model:GetBundleTriggerEndTime(self.m_dataGroup) or 0
  BundleNormalWindow.Init(self, bUserClick, eTriggerType)
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = false,
    hudKey = ESceneViewHudButtonKey.Gem
  })
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = false,
    hudKey = ESceneViewHudButtonKey.Coin
  })
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = false,
    hudKey = ESceneViewHudButtonKey.Energy
  })
  self:UpdatePerSecond()
  self.m_titleSkeleton:Initialize(false)
  self.m_titleSkeleton.AnimationState:SetAnimation(0, "appear", false)
  self.m_titleSkeleton.AnimationState:AddAnimation(0, "idle", true, 0)
end
