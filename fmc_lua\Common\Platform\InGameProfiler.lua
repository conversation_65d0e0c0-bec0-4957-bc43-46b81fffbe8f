InGameProfiler = {}
InGameProfiler.__index = InGameProfiler

function InGameProfiler:Init()
  self.m_gameTime = {}
  self.m_biTimer = 0
  self.m_mapLayerMode = nil
  self.m_biTime = 300
  EventDispatcher.AddListener(EEventType.EnterMainScene, self, self.OnEnterMainScene)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnGameModeChanged)
end

function InGameProfiler:UpdatePerSecond()
  if self.m_mapLayerMode then
    if self.m_mapLayerMode then
      self.m_gameTime[self.m_mapLayerMode] = (self.m_gameTime[self.m_mapLayerMode] or 0) + 1
    end
    self.m_biTimer = self.m_biTimer + 1
    if self.m_biTimer >= self.m_biTime then
      self.m_biTimer = self.m_biTimer - self.m_biTime
      GM.BIManager:LogProfiling(self.m_gameTime)
      MicrofunProfiler.Instance:ResetFrameTime("Default")
    end
  end
end

function InGameProfiler:OnEnterMainScene()
  if not self.m_bLogDeviceSpec then
    GM.BIManager:LogDeviceSpec()
    GM.BIManager:LogFileStorage()
    self.m_bLogDeviceSpec = true
    local maxHiccupCheck = 50
    local hiccupSent = 0
    
    function self.m_hiccupCallback(hiccups, maxframetime)
      if hiccupSent < maxHiccupCheck then
        GM.BIManager:LogProfilingHiccup(hiccups, maxframetime)
        hiccupSent = hiccupSent + 1
      end
    end
    
    MicrofunProfiler.Instance:AddHiccupCallback(self.m_hiccupCallback)
  end
  self.m_mapLayerMode = EGameMode.Main
end

function InGameProfiler:Destroy()
  EventDispatcher.RemoveTarget(self)
  if self.m_hiccupCallback and MicrofunProfiler.Instance and not MicrofunProfiler.Instance:IsNull() then
    MicrofunProfiler.Instance:RemoveHiccupCallback(self.m_hiccupCallback)
    self.m_hiccupCallback = nil
  end
end

function InGameProfiler:OnGameModeChanged()
  local curlayerMode = GM.SceneManager:GetGameMode()
  if self.m_mapLayerMode ~= curlayerMode then
    self.m_mapLayerMode = curlayerMode
  end
end

function InGameProfiler.Crash()
  CS.InGameProfiler.Crash()
end

function InGameProfiler.StartProfile()
  if not InGameProfiler._luaProfiler then
    InGameProfiler._luaProfiler = require("perf.profiler")
  end
  if InGameProfiler._luaProfiler then
    Log.Info("Start lua profiling...")
    InGameProfiler._luaProfiler.start()
    return true
  end
end

function InGameProfiler.StopProfile()
  if InGameProfiler._luaProfiler then
    Log.Info("Stop lua profiling")
    InGameProfiler._luaProfilingResult = InGameProfiler._luaProfiler.report("TOTAL")
    InGameProfiler._luaProfiler.stop()
  end
  return InGameProfiler._luaProfilingResult
end

function InGameProfiler.GetLastProfilingReport()
  return InGameProfiler._luaProfilingResult
end
