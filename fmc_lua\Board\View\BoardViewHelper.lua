BoardViewHelper = {}

function BoardViewHelper.GetActiveView()
  local currentMode = GM.SceneManager:GetGameMode()
  for _, activityDefinition in pairs(SpreeActivityDefinition) do
    if currentMode == activityDefinition.GameMode then
      return SpreeActivityBoardView.GetInstance()
    end
  end
  if currentMode == EGameMode.Board then
    return MainBoardView.GetInstance()
  else
    return nil
  end
end

function BoardViewHelper.PlaySellItemAnimation(effectRoot, effectPos, uiWorldPosition, itemModel)
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.effect_daoju_shoumai_1), effectRoot, effectPos, function(go)
  end)
  local cost = itemModel:GetSellPrice()
  if cost[PROPERTY_TYPE] ~= DELETE_TAG then
    local properties = {cost}
    GM.PropertyDataManager:PlayCollectAnimation(properties, uiWorldPosition)
  end
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxSellItem)
end
