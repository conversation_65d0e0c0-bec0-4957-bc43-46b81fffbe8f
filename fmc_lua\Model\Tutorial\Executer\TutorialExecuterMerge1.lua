local Step = {
  Merge1 = "1",
  Merge2 = "2",
  Merge3 = "3",
  Click1 = "4",
  Click2 = "5",
  Merge4 = "6"
}
local arrStepList = {}
for k, v in pairs(Step) do
  arrStepList[tonumber(v)] = k
end
local nextStep = function(step)
  local nextStepKey = arrStepList[tonumber(step) + 1]
  if nextStepKey ~= nil then
    return Step[nextStepKey]
  end
  return nil
end
local EStep2TextKey = {
  [Step.Merge1] = "tutorial_merge_basic_1",
  [Step.Merge2] = "tutorial_merge_basic_2",
  [Step.Merge3] = "tutorial_merge_basic_3",
  [Step.Click1] = "tutorial_spawn_basic_1",
  [Step.Click2] = "tutorial_spawn_basic_2",
  [Step.Merge4] = "tutorial_merge_after_spawn"
}
local EStep2TextAnchorPercent = {
  [Step.Merge1] = 32,
  [Step.Merge2] = 32,
  [Step.Merge3] = 32,
  [Step.Click1] = 32,
  [Step.Click2] = 32,
  [Step.Merge4] = 32
}
local EStep2BoardGesturePos = {
  [Step.Merge1] = {
    from = Vector2(3, 5),
    to = Vector2(4, 5)
  },
  [Step.Merge2] = {
    from = Vector2(4, 5),
    to = Vector2(5, 5)
  },
  [Step.Merge3] = {
    from = Vector2(5, 5),
    to = Vector2(5, 4)
  },
  [Step.Click1] = {
    from = Vector2(5, 4),
    to = Vector2(5, 4)
  },
  [Step.Click2] = {
    from = Vector2(5, 4),
    to = Vector2(5, 4)
  },
  [Step.Merge4] = {
    from = Vector2(4, 5),
    to = Vector2(5, 5)
  }
}
local EStep2BoardMaskPos = EStep2BoardGesturePos
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.ItemMerged, self, self.OnItemMerged)
  EventDispatcher.AddListener(EEventType.ItemSpread, self, self.OnItemSpread)
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  self:SetStrongTutorial(true)
  if StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    self.m_strOngoingDatas = nextStep(0)
  end
  self:_SaveOngoingDatas()
  self:_ExecuteDrag()
  return true
end

function Executer:OnItemMerged()
  self:_NextStep()
end

function Executer:OnItemSpread()
  self:_NextStep()
end

function Executer:_NextStep()
  local oldStep = self.m_strOngoingDatas
  self.m_strOngoingDatas = nextStep(self.m_strOngoingDatas)
  if self.m_strOngoingDatas ~= nil then
    self:LogTutorialStepFinish(oldStep)
    self:_SaveOngoingDatas()
    self:_ExecuteDrag()
  else
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
  end
end

function Executer:_ExecuteDrag()
  if self.m_gesture ~= nil then
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
  end
  local from = EStep2BoardGesturePos[self.m_strOngoingDatas].from
  local to = EStep2BoardGesturePos[self.m_strOngoingDatas].to
  local fromBoardPos = GM.MainBoardModel:CreatePosition(from.x, from.y)
  local toBoardPos = GM.MainBoardModel:CreatePosition(to.x, to.y)
  GM.TutorialModel:SetForceSourceBoardPosition(fromBoardPos)
  GM.TutorialModel:SetForceTargetBoardPosition(toBoardPos)
  if self.m_strOngoingDatas == Step.Click1 or self.m_strOngoingDatas == Step.Click2 then
    self.m_gesture = TutorialHelper.TapOnBoard(fromBoardPos)
  else
    self.m_gesture = TutorialHelper.DragOnItems(fromBoardPos, toBoardPos)
  end
  if not self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
    return
  end
  TutorialHelper.MaskOnItemBoard(fromBoardPos, toBoardPos)
  if EStep2TextKey[self.m_strOngoingDatas] ~= nil then
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  else
    TutorialHelper.HideDialog()
  end
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
