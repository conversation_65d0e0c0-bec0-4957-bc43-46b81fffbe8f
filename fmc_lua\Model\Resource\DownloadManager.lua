DownloadManager = {}
DownloadManager.__index = DownloadManager

function DownloadManager:Init()
  self.m_downloadedLabelSet = {}
  EventDispatcher.AddListener(EEventType.NewChapterUnlocked, self, self.TryMuteDownload)
end

function DownloadManager:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function DownloadManager:Update()
  if self.m_muteDownloadCoroutine ~= nil then
    coroutine.resume(self.m_muteDownloadCoroutine)
  end
end

function DownloadManager:IsLabelDownloaded(label)
  return self.m_downloadedLabelSet[label] == true
end

function DownloadManager:IsAllLabelDownloaded(labelList)
  for _, label in ipairs(labelList) do
    if not self:IsLabelDownloaded(label) then
      return false
    end
  end
  return true
end

function DownloadManager:SetSkipConfirmWindow()
  self.m_skipConfirmWindow = true
end

function DownloadManager:IsSkipConfirmWindow()
  return self.m_skipConfirmWindow == true
end

function DownloadManager:GetChapterLabel(chapterName)
  local labelName = AddressableLabel["Chapter" .. chapterName]
  Log.Assert(labelName ~= nil, "AddressableLabel 未包含 Chapter" .. chapterName)
  return labelName
end

function DownloadManager:GetBoardLabel(chapterName)
  local key = "Board" .. chapterName
  if not AddressableLabel.HasLabel(key) then
    return nil
  end
  return AddressableLabel[key]
end

function DownloadManager:GetForceDownloadLabels()
  local labelSet = {}
  local ongoingChapterId = GM.TaskManager:GetOngoingChapterId()
  local chapterName = GM.ChapterDataModel:GetChapterNameById(ongoingChapterId)
  local labelName = self:GetChapterLabel(chapterName)
  labelSet[labelName] = true
  local forceDownloadChapterId = self:GetForceDownloadChapterId()
  if 0 < forceDownloadChapterId and ongoingChapterId > forceDownloadChapterId then
    local forceChapterName = GM.ChapterDataModel:GetChapterNameById(forceDownloadChapterId)
    labelSet[self:GetChapterLabel(forceChapterName)] = true
  end
  for i = 1, ongoingChapterId do
    local chapterName = GM.ChapterDataModel:GetChapterNameById(i)
    local labelName = self:GetBoardLabel(chapterName)
    if labelName then
      labelSet[labelName] = true
    end
  end
  local activityLabels = GM.ActivityManager:GetResourceLabels()
  for _, label in ipairs(activityLabels) do
    labelSet[label] = true
  end
  local labels = {}
  for label, _ in pairs(labelSet) do
    table.insert(labels, label)
  end
  return labels
end

function DownloadManager:GetDownloadSizeSync(labels)
  return AddressableManager.Instance:GetDownloadSizeSync(labels)
end

function DownloadManager:GetDownloadSizeAsync(labels, callback)
  local callbackWrapper = function(size)
    if GM == nil then
      return
    end
    if size == 0 then
      for _, label in ipairs(labels) do
        self.m_downloadedLabelSet[label] = true
      end
    end
    callback(size)
  end
  AddressableManager.Instance:GetDownloadSizeAsync(labels, callbackWrapper)
end

function DownloadManager:GetFormattedSize(size)
  return AddressableManager.GetFormattedDownloadSize(size)
end

function DownloadManager:TryMuteDownload()
  if self.m_muteDownloadCoroutine ~= nil then
    return
  end
  self.m_muteDownloadCoroutine = coroutine.create(function()
    self:_MuteDownload()
    self.m_muteDownloadCoroutine = nil
    Log.Info("停止下载", "静默下载")
  end)
  coroutine.resume(self.m_muteDownloadCoroutine)
end

function DownloadManager:_MuteDownload()
  local alwaysDownloadPredidate = function()
    return true
  end
  local alwaysDownloadLabels = {}
  for _, activityDefinition in pairs(DashActivityDefinition) do
    table.insert(alwaysDownloadLabels, activityDefinition.ResourceLabel)
  end
  self:_MuteDownloadLabels(alwaysDownloadLabels, alwaysDownloadPredidate, false)
  local taskPredidateMain = function()
    return GM.TaskManager:GetOngoingChapterId() >= 2
  end
  local chapterLabels = {}
  for _, chapterName in ipairs(GM.ChapterDataModel:GetChapterSequence()) do
    local chapterLabelName = self:GetChapterLabel(chapterName)
    chapterLabels[#chapterLabels + 1] = chapterLabelName
    local boardLabelName = self:GetBoardLabel(chapterName)
    if boardLabelName then
      chapterLabels[#chapterLabels + 1] = boardLabelName
    end
  end
  self:_MuteDownloadLabels(chapterLabels, taskPredidateMain, true)
  local mainActivityLabels = {
    AddressableLabel.BPCommon
  }
  for activityType, activityDefinition in pairs(PassActivityDefinition) do
    mainActivityLabels[#mainActivityLabels + 1] = AddressableLabel[activityDefinition.Prefix]
  end
  self:_MuteDownloadLabels(mainActivityLabels, taskPredidateMain, false)
  local taskPredidateBakeOut = function()
    return GM.TaskManager:GetOngoingChapterId() >= GM.ChapterDataModel:GetChapterCount()
  end
  local bakeOutLabels = {
    AddressableLabel.BakeOut
  }
  self:_MuteDownloadLabels(bakeOutLabels, taskPredidateBakeOut, false)
  local filter = function(label)
    return label ~= AddressableLabel.Default and label ~= AddressableLabel.Lua and not self.m_downloadedLabelSet[label]
  end
  local wifiOnlyLabels = Table.Select(AddressableLabel, filter)
  self:_MuteDownloadLabels(wifiOnlyLabels, taskPredidateMain, true)
end

function DownloadManager:_MuteDownloadLabels(labels, taskPredidate, wifiOnly)
  local cnt = 0
  for _, _ in pairs(labels) do
    cnt = cnt + 1
  end
  if cnt ~= #labels then
    Log.Assert(false, "当前静默下载标签列表有nil值，请检查")
  end
  local muteDownloadEnabled = not GameConfig.IsTestMode() or PlayerPrefs.GetInt(EPlayerPrefKey.MuteDownload, 0) ~= 0
  for _, label in ipairs(labels) do
    local sizeFlag
    AddressableManager.Instance:GetDownloadSizeAsync({label}, function(size)
      sizeFlag = size
    end)
    while sizeFlag == nil do
      coroutine.yield()
    end
    if sizeFlag == 0 then
      self.m_downloadedLabelSet[label] = true
    elseif not self.m_nonMuteDownloading and muteDownloadEnabled and taskPredidate() and (not wifiOnly or Application.internetReachability == NetworkReachability.ReachableViaLocalAreaNetwork) then
      Log.Info("开始下载" .. label, "静默下载")
      for retryTimes = 1, 3 do
        local resultFlag
        AddressableManager.Instance:MuteDownload(label, function(result)
          resultFlag = result
        end)
        while resultFlag == nil do
          coroutine.yield()
        end
        if resultFlag then
          Log.Info("内容" .. label .. "下载完毕", "静默下载")
          self.m_downloadedLabelSet[label] = true
          break
        end
      end
    end
  end
end

function DownloadManager:SetForceDownloadChapterId(chapterId)
  PlayerPrefs.SetInt(EPlayerPrefKey.ForceDownloadChapterId, chapterId)
end

function DownloadManager:GetForceDownloadChapterId()
  return PlayerPrefs.GetInt(EPlayerPrefKey.ForceDownloadChapterId, 0)
end

function DownloadManager:TryDownload(labels, progressCallback, resultCallback)
  self.m_nonMuteDownloading = true
  local resultCallbackWrap = function(result)
    if result then
      self.m_skipConfirmWindow = false
      for _, label in ipairs(labels) do
        self.m_downloadedLabelSet[label] = true
      end
      self:SetForceDownloadChapterId(0)
    end
    self.m_nonMuteDownloading = false
    resultCallback(result)
  end
  if next(labels) and GameConfig.IsTestMode() then
    for _, label in ipairs(labels) do
      local size = self:GetDownloadSizeSync({label}) / 1048576
      Log.Debug("[DownloadSize]" .. label .. ":" .. size .. "Mb")
    end
  end
  AddressableManager.Instance:TryDownloadAsync(labels, resultCallbackWrap, progressCallback)
end
