DatabaseModel = {
  LocalFileName = "db.sqlite3",
  BackupFileName = "backup.sqlite3"
}
DatabaseModel.__index = DatabaseModel

function DatabaseModel:Init()
  self.m_db = SQLiteDatabase.Create(DatabaseModel.LocalFileName)
  self.hasDB = self.m_db:IsExists()
  if self.hasDB then
    self.m_db:Open()
  end
end

function DatabaseModel:GetFileSize()
  if self.m_db then
    return self.m_db.fileSize
  end
end

function DatabaseModel:CreateColumn(name, type, primary)
  return ColumnInfo(name, type, primary)
end

function DatabaseModel:CheckDatabase()
  return self.m_db:IsOpen()
end

function DatabaseModel:OpenDatabase()
  return self.m_db:Open()
end

function DatabaseModel:CloseDatabase()
  self.m_db:Close()
end

function DatabaseModel:DeleteDatabase()
  self.m_db:Close()
  self.m_db:Delete()
end

function DatabaseModel:CreateDatabaseTable(strName, arrCols)
  return self.m_db:SyncTable(strName, arrCols)
end

function DatabaseModel:CreateDatabaseCommand(sql)
  return self.m_db:CreateCommand(sql)
end

function DatabaseModel:QueueDatabaseCommand(cmd)
  self.m_db:QueueCommand(cmd)
end

function DatabaseModel:DatabaseExecute(strCmd)
  return self.m_db:Execute(strCmd)
end

function DatabaseModel:DatabaseQuery(strCmd)
  return self.m_db:Query(strCmd)
end

function DatabaseModel:DatabaseTransaction()
  if not self.m_db:NeedTransaction() then
    return 0
  end
  return self.m_db:Transaction()
end

function DatabaseModel:GetMaxVariable()
  return self.m_db:GetMaxVariable()
end

function DatabaseModel:Destroy()
  if self.m_db:IsOpen() then
    self.m_db:Close()
  end
end
