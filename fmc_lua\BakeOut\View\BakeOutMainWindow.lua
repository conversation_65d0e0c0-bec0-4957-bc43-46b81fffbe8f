BakeOutMainWindow = setmetatable({}, BaseWindow)
BakeOutMainWindow.__index = BakeOutMainWindow

function BakeOutMainWindow:Init(arrRanks, userClick, bakeoutId)
  self.m_model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  self.m_modelId = self.m_model:GetId()
  self.m_arrPodiums = {
    self.m_podium1,
    self.m_podium2,
    self.m_podium3
  }
  if arrRanks ~= nil then
    self.m_bShowResultMode = true
    self.m_goBtnGo:SetActive(false)
    self.m_exchangeBtnGo:SetActive(false)
    self.m_detailBtnGo:SetActive(false)
    self:_ShowRank(arrRanks)
  else
    self.m_loadingContentGo:SetActive(true)
    self.m_bSendingRequest = true
    self.m_model:GetBakeOutRanks(function(bSuccess, tbResp)
      if self.gameObject:IsNull() then
        return
      end
      self.m_bSendingRequest = false
      self.m_loadingContentGo:SetActive(false)
      if bSuccess and tbResp.rcode == 0 then
        self:_ShowRank(tbResp.ranks)
      else
        self.m_networkContentGo:SetActive(true)
      end
    end, false)
  end
  self:LogWindowAction(EBIType.UIActionType.Open, userClick and EBIReferType.UserClick or EBIReferType.AutoPopup, bakeoutId or self.m_model:GetId())
  self:UpdatePerSecond()
  local offsetY = 102
  local ratio = Screen.height / Screen.width
  if 1.7777777777777777 < ratio then
    offsetY = -ScreenFitter.GetFitScale(102, 15, 2.3, 1.7777777777777777)
  else
    offsetY = ScreenFitter.GetFitScale(102, 15, 1.7777777777777777, 1.3333333333333333) - 117
  end
  self.m_jarTrans:SetAnchoredPosY(offsetY)
  UIUtil.SetActive(self.m_rewardTip.gameObject, false)
end

function BakeOutMainWindow:_ShowRank(arrRankDatas)
  self.m_arrRankDatas = arrRankDatas
  for i, podium in ipairs(self.m_arrPodiums) do
    podium:UpdateContent(arrRankDatas[i])
  end
  if #self.m_arrRankDatas <= 0 and not self.m_bShowResultMode then
    self.m_emptyContentGo:SetActive(true)
  end
  self.m_rankTable:Init(arrRankDatas, not GM.TutorialModel:HasAnyStrongTutorialOngoing() and self.m_model:GetLastRank() or nil, self)
  if not self.m_bShowResultMode then
    self.m_model:UpdateLastRank(self.m_model:GetCurRank())
  end
end

function BakeOutMainWindow:OnCoinExchangeBtnClicked()
  self:HideRewardTip(not self.m_bShowResultMode)
  if self.m_bShowResultMode then
    return
  end
  self:Close()
  GM.UIManager:OpenView(UIPrefabConfigName.BakeOutCoinExchangeWindow, self.m_arrRankDatas)
end

function BakeOutMainWindow:OnHelpBtnClicked()
  self:HideRewardTip(true)
  self:Close()
  GM.UIManager:OpenView(UIPrefabConfigName.BakeOutDetailWindow)
end

function BakeOutMainWindow:OnGoBtnClicked()
  self:HideRewardTip(true)
  self:Close()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    GM.SceneManager:ChangeGameMode(EGameMode.Board)
  end
end

function BakeOutMainWindow:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  if not (self.m_model:GetState() == ActivityState.Started or self.m_bShowResultMode) or self.m_model:GetId() ~= self.m_modelId then
    self.m_model = nil
    self:Close()
    return
  end
  if not self.m_bShowResultMode and self.m_model:IsBakeOutModeOn() then
    if self.m_model:GetNextStateTime() ~= nil then
      local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
      self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
    end
  else
    self.m_countdownContentGo:SetActive(false)
  end
end

function BakeOutMainWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  if self.m_bShowResultMode then
    self.m_model:LoadServerConfig()
  end
end

function BakeOutMainWindow:OnMaskClicked()
  self:HideRewardTip()
end

function BakeOutMainWindow:OnScrollRectPointerDown()
  self:HideRewardTip()
end

function BakeOutMainWindow:TryHideRewardTip()
  if self.m_skipHideRewardTipOnce then
    self.m_skipHideRewardTipOnce = nil
    return
  end
  self:HideRewardTip()
end

function BakeOutMainWindow:ShowRewardTip(rewards, rectTrans, offsetX, offsetY)
  local scrollRect = self.m_rankScrollRect
  if scrollRect.verticalNormalizedPosition < 0 then
    self.m_skipHideRewardTipOnce = true
    scrollRect.verticalNormalizedPosition = 0
  elseif scrollRect.verticalNormalizedPosition > 1 then
    self.m_skipHideRewardTipOnce = true
    scrollRect.verticalNormalizedPosition = 1
  end
  scrollRect:StopMovement()
  self.m_rewardTip:Show(rewards, rectTrans, offsetX, offsetY)
end

function BakeOutMainWindow:HideRewardTip(bIgnoreAnim)
  self.m_rewardTip:Hide(bIgnoreAnim)
end

function BakeOutMainWindow:GetOwnCell()
  if self.m_bSendingRequest or self.m_networkContentGo.activeSelf then
    return nil
  end
  return self.m_model:GetCurRank() <= #self.m_arrPodiums and self.m_arrPodiums[self.m_model:GetCurRank()] or self.m_rankTable:GetCell(self.m_model:GetCurRank())
end

function BakeOutMainWindow:GetGoBtnGo()
  return self.m_goBtnGo
end

function BakeOutMainWindow:GetExchangeBtnGo()
  return self.m_exchangeBtnGo
end

function BakeOutMainWindow:GetExchangeBtnTapTransform()
  return self.m_exchangeBtnRectTrans
end
