BakeOutCoinExchangeWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BaseWindow)
BakeOutCoinExchangeWindow.__index = BakeOutCoinExchangeWindow

function BakeOutCoinExchangeWindow:Init()
  local adjustSizeOffset = ScreenFitter.GetSceneViewSizeOffset()
  UIUtil.AddSizeDelta(self.m_fullContentRectTrans, nil, -adjustSizeOffset.y)
  UIUtil.AddLocalPosition(self.m_fullContentRectTrans, nil, -adjustSizeOffset.y / 2)
  self.m_model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  self.m_modelId = self.m_model:GetId()
  self.m_ownCellData = {
    rank = self.m_model:GetDisplayCurRank(),
    score = self.m_model:GetToken(),
    name = GM.UserProfileModel:GetName(),
    icon = GM.UserProfileModel:GetIcon()
  }
  self:UpdateOwnCellData()
  self.m_jarSpine:Init()
  self:UpdateContent()
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = true,
    hudKey = ESceneViewHudButtonKey.Coin
  })
  UIUtil.SetActive(self.m_rewardTip.gameObject, false)
end

function BakeOutCoinExchangeWindow:UpdateContent()
  local costNum = self.m_model:GetExchangeCoinCostNum()
  self.m_fillButton:Init(costNum == 0 and GM.GameTextModel:GetText("shop_free_button") or costNum, nil)
  local a = self.m_model:CanExchangeCoin()
  self.m_fillButton:SetEnabled(self.m_model:CanExchangeCoin())
  self.m_gainTokenNum = self.m_model:GetExchangeTokenGainNum()
  self.m_gainText.text = "x" .. self.m_gainTokenNum
end

function BakeOutCoinExchangeWindow:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  if self.m_model:GetState() ~= ActivityState.Started or self.m_model:GetId() ~= self.m_modelId then
    self:Close()
  end
end

function BakeOutCoinExchangeWindow:OnExchangeBtnClicked()
  self:HideRewardTip()
  if not self.m_bPlayingExchangeAnimation then
    self.m_model:ExchangeCoin2Token()
    self:UpdateContent()
    self.m_bPlayingExchangeAnimation = true
    local openDt = 0.3
    local flyDt = 1.4
    local closeDt = 0.8
    self.m_jarSpine:SetAnimation("0_remove_1", false)
    DelayExecuteFuncInView(function()
      self:PlayTokenFlyAnimation()
      DelayExecuteFuncInView(function()
        self.m_jarSpine:SetAnimation("1_appear_0", false)
        DelayExecuteFuncInView(function()
          self.m_bPlayingExchangeAnimation = false
        end, closeDt, self, true)
      end, flyDt, self, true)
    end, openDt, self, true)
  end
end

function BakeOutCoinExchangeWindow:UpdateOwnCellData()
  if self.m_ownCellData ~= nil then
    self.m_ownCellData.rank = self.m_model:GetDisplayCurRank()
    self.m_ownCellData.score = self.m_model:GetToken()
    self.m_ownRankCell:UpdateContent(self.m_ownCellData, true, self)
  end
end

function BakeOutCoinExchangeWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  if self.m_model:CanAcquireToken() then
    GM.UIManager:OpenView(UIPrefabConfigName.BakeOutMainWindow)
  end
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = false,
    hudKey = ESceneViewHudButtonKey.Coin
  })
end

function BakeOutCoinExchangeWindow:GetExchangeBtnGo()
  return self.m_exchangeBtnGo
end

local countX = 3
local countY = 3
local starCount = countX * countY
local intervalX = 80
local intervalY = 80
local scaleDelay = 0.01
local scaleDuration = 0.05
local scale = 2.5
local maxScaleDelay = 0.6
local moveDuration = 0.4
local moveDelay = 0.03

function BakeOutCoinExchangeWindow:PlayTokenFlyAnimation()
  local transform = self.transform
  local startPositionBase = transform:InverseTransformPoint(self.m_bottleRectTrans.position)
  startPositionBase.z = 0
  local targetPosition = transform:InverseTransformPoint(self.m_starRectTrans.position)
  targetPosition.z = 0
  for i = 1, starCount do
    local offsetX = -150 + math.random(300)
    local offsetY = -150 + math.random(300)
    local offset = Vector3(offsetX, offsetY, 0)
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.effect_UI_xingxing_1), transform, startPositionBase + offset, function(go)
      if go:IsNull() then
        return
      end
      local transform = go.transform
      UIUtil.UpdateSortingOrder(go, self:GetSortingOrder() + 4)
      local sequence = DOTween.Sequence()
      sequence:AppendInterval((i - 1) * scaleDelay)
      sequence:Append(transform:DOScale(scale, scaleDuration))
      sequence:AppendInterval(maxScaleDelay - scaleDuration - (i - 1) * scaleDelay)
      sequence:Append(transform:DOLocalMove(targetPosition, moveDuration + (i - 1) * moveDelay))
      sequence:Join(transform:DOScale(1, moveDuration + (i - 1) * moveDelay))
      sequence:AppendCallback(function()
        if not go:IsNull() then
          go:SetActive(false)
          go:RemoveSelf()
        end
        if i == 1 and not self.gameObject:IsNull() then
          self:UpdateOwnCellData()
          sequence:Append(self.m_starRectTrans:DOScale(1.2, 0.15):SetLoops(2, LoopType.Yoyo))
        end
      end)
    end)
  end
end

function BakeOutCoinExchangeWindow:ShowRewardTip(rewards, rectTrans, offsetX, offsetY)
  self.m_rewardTip:Show(rewards, rectTrans, offsetX, offsetY)
end

function BakeOutCoinExchangeWindow:HideRewardTip(bIgnoreAnim)
  self.m_rewardTip:Hide(bIgnoreAnim)
end

function BakeOutCoinExchangeWindow:OnMaskClicked()
  self:HideRewardTip()
end
