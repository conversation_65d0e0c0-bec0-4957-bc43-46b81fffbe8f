InAppPurchaseModel = {}
InAppPurchaseModel.__index = InAppPurchaseModel
InAppPurchaseModel.UsdToGemRatio = 40
local IAP_QUERY_LIMIT = 3
local DBColumnOrderStatus = "status"

function InAppPurchaseModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.IAPOrder)
  self.m_mapProducts = IAPProduct.GetProducts()
  self.m_totalPay = 0
end

function InAppPurchaseModel:InitPurchase()
  GM.SDKHelper:PurchaseInit(self:_GeneratePurchaseInitParameters())
end

function InAppPurchaseModel:_GeneratePurchaseInitParameters()
  local arrProductIds = {}
  for k, v in pairs(self.m_mapProducts) do
    arrProductIds[#arrProductIds + 1] = v.productId
  end
  return table.concat(arrProductIds, ",")
end

function InAppPurchaseModel:UpdateInAppPurchaseData(strInfo)
  local infoTable = json.decode(strInfo)
  if not infoTable then
    return
  end
  local isIos = DeviceInfo.IsSystemIOS()
  local info, eIAPType
  for i = 1, #infoTable do
    info = infoTable[i]
    if isIos then
      eIAPType = self:GetIAPTypeFromProductId(info.product)
      if eIAPType ~= nil and info.price ~= nil and tonumber(info.price) ~= nil and info.symbol ~= nil and info.locale ~= nil then
        self.m_mapProducts[eIAPType]:UpdateInfo(info.symbol .. " " .. info.price, tonumber(info.price), info.locale)
      end
    else
      info = json.decode(info)
      if info then
        eIAPType = self:GetIAPTypeFromProductId(info.productId)
        if eIAPType ~= nil and info.price ~= nil then
          if GameConfig.CURRENT_PLATFORM == Channels.AMAZON:GetValue() then
            self.m_mapProducts[eIAPType]:UpdateInfo(info.price, 0, "USD")
          elseif info.price_amount_micros ~= nil and info.price_currency_code ~= nil then
            self.m_mapProducts[eIAPType]:UpdateInfo(info.price, info.price_amount_micros / 1000000, info.price_currency_code)
          end
        end
      end
    end
  end
end

function InAppPurchaseModel:StartPurchase(eIAPType, funcSuccessCallback, scene)
  Log.Assert(type(scene) == "string", "InAppPurchaseModel:StartPurchase need scene")
  if GameConfig.IsTestMode() and DeviceInfo.IsUnityEditor() then
    if PlayerPrefs.GetInt(EPlayerPrefKey.TestUnityIAPFail, 0) == 1 then
      GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, "Tip", "模拟支付失败：" .. tostring(eIAPType), "拷贝支付项", function()
        PlatformInterface.SetString2Clipboard(eIAPType)
      end)
    elseif funcSuccessCallback then
      funcSuccessCallback()
    end
    return
  end
  local productData = self.m_mapProducts[eIAPType]
  if productData == nil then
    Log.Error("InAppPurchaseModel:StartPurchase no product info")
    return
  end
  GM:OnEnteringThirdPartyApp(false)
  GM.UIManager:ShowMask()
  local extraInfo = {}
  extraInfo.eIAPType = eIAPType
  extraInfo.productId = productData.productId
  extraInfo.funcSuccessCallback = funcSuccessCallback
  extraInfo.strScene = scene
  self:_LogPayAction(EBIType.StartPurchase, extraInfo.eIAPType)
  self.m_startTime = NetTimeStamp.Create("pay")
  GM.OperBIManager:TrackEvent(EOperBIEventType.start_purchase)
  Log.Info("start purchase", LogTag.Purchase)
  if GM.ShopModel:IsEnergyBuyInDiscount() then
    GM.BIManager:LogAction(EBIType.StartIAPWhenEnergyDiscount, {
      t = eIAPType,
      fm = productData.fixedMoney,
      rm = productData.realMoney,
      c = productData.currency
    })
  end
  if GameConfig.CURRENT_PLATFORM == 60 then
    PlayerPrefs.SetInt(EPlayerPrefKey.LastTimePurchaseTime, GM.GameModel:GetServerTime())
  end
  GM.SDKHelper:Purchase(extraInfo.productId, function(eStatus, productId, message, data, signature, orderId)
    GM:OnBackFromThirdPartyApp(false)
    if GameConfig.CURRENT_PLATFORM == 60 and eStatus ~= EPurchaseResultStatus.Success then
      self:_QueryPayServer4Info(eStatus, productId, message, data, signature, orderId, extraInfo, 1)
    else
      self:_JNIPurchaseComplete(eStatus, productId, message, data, signature, orderId, extraInfo)
    end
  end)
end

function InAppPurchaseModel:RestorePurchase(isUserTriggered)
  if isUserTriggered then
    self:_LogPayAction(EBIType.RestorePurchase, "user")
    self.m_startTime = NetTimeStamp.Create("pay")
    GM:OnEnteringThirdPartyApp(false)
    GM.UIManager:ShowMask()
  end
  GM.SDKHelper:PurchaseRestore(function(eStatus, productId, message, data, signature, orderId, tmpIsUserTriggered)
    local extraInfo = {}
    extraInfo.userTriggeredRestore = tmpIsUserTriggered
    if tmpIsUserTriggered then
      GM:OnBackFromThirdPartyApp(false)
    end
    if GameConfig.CURRENT_PLATFORM == 60 and eStatus ~= EPurchaseResultStatus.Success then
      local lastPT = PlayerPrefs.GetInt(EPlayerPrefKey.LastTimePurchaseTime, 0)
      local now = GM.GameModel:GetServerTime()
      if 0 < lastPT and now - lastPT < 259200 then
        self:_QueryPayServer4Info(eStatus, productId, message, data, signature, orderId, extraInfo, 1)
        return
      end
    end
    self:_JNIPurchaseComplete(eStatus, productId, message, data, signature, orderId, extraInfo)
  end, isUserTriggered)
end

function InAppPurchaseModel:GetIAPTypeFromProductId(productId)
  for k, v in pairs(self.m_mapProducts) do
    if v.productId == productId then
      return k
    end
  end
  Log.Error("InAppPurchaseModel:GetIAPTypeFromProductId no matching iap type " .. productId)
end

function InAppPurchaseModel:GetTotalRecharge()
  return self.m_totalPay
end

function InAppPurchaseModel:SetTotalRecharge(pay)
  self.m_totalPay = pay
end

function InAppPurchaseModel:GetLocalizedPrice(eIAPType)
  local productData = self.m_mapProducts[eIAPType]
  if productData == nil then
    Log.Error("InAppPurchaseModel:GetLocalizedPrice no product info, ErrType: " .. eIAPType)
    return nil
  end
  return productData.localizedPrice
end

function InAppPurchaseModel:GetProductData(eIAPType)
  return self.m_mapProducts[eIAPType]
end

function InAppPurchaseModel:GetLocalizedOriginPrice(EIAPType, originPrice)
  local localPriceText = self:GetLocalizedPrice(EIAPType)
  local productData = self:GetProductData(EIAPType)
  local fixedMoney = tonumber(productData.fixedMoney)
  local realMoney = tonumber(productData.realMoney)
  local originFixedMoney = tonumber(originPrice)
  local originPriceText = ""
  if originFixedMoney and fixedMoney and realMoney then
    local originRealMoney = originFixedMoney
    if fixedMoney ~= realMoney then
      originRealMoney = originFixedMoney * realMoney / fixedMoney
      if 100 <= realMoney and realMoney * 100 // 100 == realMoney then
        originRealMoney = math.ceil(originRealMoney / 100) * 100 - 1
      else
        originRealMoney = math.ceil(originRealMoney) - 0.01
      end
    end
    if string.find(localPriceText, "%.%d+") then
      originRealMoney = string.format("%.2f", originRealMoney)
    end
    local result, replaceTime = string.gsub(localPriceText, "%d+[,%d]*%.?%d+", originRealMoney)
    if replaceTime == 1 then
      originPriceText = result
    end
  end
  GM.BIManager:LogProject("glop", {
    i = EIAPType,
    t = originPriceText,
    l = localPriceText,
    f = fixedMoney,
    r = realMoney,
    o = originFixedMoney
  })
  return originPriceText
end

function InAppPurchaseModel:_QueryPayServer4Info(eStatus, productId, message, data, signature, orderId, purchaseInfo, retry)
  local delay = 1 * retry * retry
  Log.Info("_QueryPayServer4Info: retry=" .. tostring(retry) .. ", delay=" .. tostring(delay), LogTag.Purchase)
  DelayExecuteFunc(function()
    Log.Info("query payserver:" .. tostring(retry), LogTag.Purchase)
    self:_LogPayAction(EBIType.QueryPurchase, "q_" .. tostring(retry), nil, nil, true)
    retry = retry + 1
    PayMessage.Query(purchaseInfo, function(result, tbData, reqCtx)
      if result and tbData.rcode == 0 then
        local pid = tbData.product_id
        local oid = tbData.order_id
        self:_LogPayAction(EBIType.QueryPurchaseS, purchaseInfo.eIAPType or "r", oid, json.encode(tbData), true)
        self:_JNIPurchaseComplete(EPurchaseResultStatus.Success, pid, message, data, signature, oid, purchaseInfo, true)
        return
      end
      if retry > IAP_QUERY_LIMIT then
        self:_LogPayAction(EBIType.FP_QueryFaild, purchaseInfo.eIAPType or "r", nil, nil, true)
        self:_JNIPurchaseComplete(eStatus, productId, message, data, signature, orderId, purchaseInfo)
      else
        self:_QueryPayServer4Info(eStatus, productId, message, data, signature, orderId, purchaseInfo, retry)
      end
    end)
  end, delay)
end

function InAppPurchaseModel:_JNIPurchaseComplete(eStatus, productId, message, data, signature, orderId, purchaseInfo, skipVerify)
  purchaseInfo.isRestore = purchaseInfo.eIAPType == nil or productId ~= purchaseInfo.productId and not StringUtil.IsNilOrEmpty(productId) or purchaseInfo.userTriggeredRestore
  if StringUtil.IsNilOrEmpty(productId) and StringUtil.IsNilOrEmpty(purchaseInfo.productId) then
    self:_ShowFailTip(purchaseInfo)
    if not purchaseInfo.isRestore or purchaseInfo.userTriggeredRestore then
      self:_LogPayAction(EBIType.FP_NoPid, purchaseInfo.eIAPType, orderId, tostring(eStatus) .. ";" .. (message or ""))
    end
    return
  end
  if StringUtil.IsNilOrEmpty(productId) then
    productId = purchaseInfo.productId
  end
  local eIAPType = self:GetIAPTypeFromProductId(productId)
  if eIAPType == nil then
    self:_ShowFailTip(purchaseInfo)
    self:_LogPayAction(EBIType.FP_InvalidPid, productId, orderId, tostring(eStatus) .. ";" .. (message or ""))
    return
  end
  local tbData = json.decode(data) or {}
  purchaseInfo.orderId = tbData.orderId or orderId or ""
  purchaseInfo.payResult = eStatus
  purchaseInfo.receiptData = data
  purchaseInfo.receiptSignature = signature or ""
  local iapData = self.m_mapProducts[eIAPType]
  purchaseInfo.realMoney = iapData.realMoney
  purchaseInfo.currency = iapData.currency
  purchaseInfo.payChannelId = DeviceInfo.IsSystemIOS() and 100 or 7
  purchaseInfo.purchaseToken = tbData.purchaseToken or ""
  if GameConfig.CURRENT_PLATFORM == 60 and eStatus == EPurchaseResultStatus.Success and StringUtil.IsNilOrEmpty(purchaseInfo.orderId) then
    purchaseInfo.orderId = MD5.Encode(tbData.purchaseToken)
  end
  if GameConfig.CURRENT_PLATFORM == Channels.AMAZON:GetValue() then
    purchaseInfo.payChannelId = 66
    purchaseInfo.orderId = signature or ""
    purchaseInfo.receiptSignature = message or purchaseInfo.orderId
  end
  if purchaseInfo.isRestore then
    purchaseInfo.eIAPType = eIAPType
    purchaseInfo.productId = productId
    purchaseInfo.strScene = purchaseInfo.userTriggeredRestore and EBIType.UserRestore or EBIType.Restore
  elseif purchaseInfo.eIAPType ~= eIAPType then
    Log.Error("InAppPurchaseModel:_JNIPurchaseComplete something unexpected happened")
    self:_ShowFailTip(purchaseInfo)
    self:_LogPayAction(EBIType.FP_UnexpectedType, purchaseInfo.eIAPType, orderId, tostring(eStatus) .. ";" .. (message or ""))
    return
  end
  if eStatus == EPurchaseResultStatus.Success then
    if skipVerify and self:_CanAwardForOrderId(purchaseInfo) then
      self:_HideMask(purchaseInfo)
      self:_OnVerifySuccess(purchaseInfo)
    else
      self:_VerifyPurchase(purchaseInfo)
    end
  elseif eStatus == EPurchaseResultStatus.Cancelled then
    self:_HideMask(purchaseInfo)
    if not purchaseInfo.isRestore or purchaseInfo.userTriggeredRestore then
      GM.UIManager:ShowPromptWithKey("purchase_cancel_tip")
    end
    self:_LogPayAction(EBIType.CancelPurchase, purchaseInfo.eIAPType, orderId)
    Log.Info("cancel purchase", LogTag.Purchase)
  else
    self:_ShowFailTip(purchaseInfo)
    self:_LogPayAction(EBIType.FP_CallbackFailed, purchaseInfo.eIAPType, orderId, tostring(eStatus) .. ";" .. (message or ""))
  end
end

function InAppPurchaseModel:_ShowFailTip(purchaseInfo, msgKey)
  self:_HideMask(purchaseInfo)
  if not purchaseInfo.isRestore then
    GM.UIManager:OpenView(UIPrefabConfigName.PurchaseFailWindow, msgKey)
  elseif purchaseInfo.userTriggeredRestore then
    local gameTextModel = GM.GameTextModel
    GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, gameTextModel:GetText("no_purchase_need_restore_title"), gameTextModel:GetText("no_purchase_need_restore_desc"), gameTextModel:GetText("no_purchase_need_restore_button"))
  end
  Log.Info("fail purchase", LogTag.Purchase)
end

function InAppPurchaseModel:_HideMask(purchaseInfo)
  if not purchaseInfo.isRestore or purchaseInfo.userTriggeredRestore then
    GM.UIManager:HideMask()
  end
end

function InAppPurchaseModel:_CloseTransaction(purchaseInfo)
  GM.SDKHelper:PurchaseFinish(purchaseInfo.productId or "")
end

function InAppPurchaseModel:_VerifyPurchase(purchaseInfo)
  if not self:_CanAwardForOrderId(purchaseInfo) then
    self:_ShowFailTip(purchaseInfo)
    self:_LogPayAction(EBIType.FP_RewardAlready, purchaseInfo.eIAPType, purchaseInfo.orderId)
    self:_CloseTransaction(purchaseInfo)
    return
  end
  PayMessage.PaidProof(purchaseInfo, function(result, tbData, reqCtx)
    if not self:_CanAwardForOrderId(purchaseInfo) then
      self:_ShowFailTip(purchaseInfo)
      self:_CloseTransaction(purchaseInfo)
      return
    end
    if not result then
      self:_HideMask(purchaseInfo)
      if not purchaseInfo.isRestore or purchaseInfo.userTriggeredRestore then
        GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("bad_network_window_title"), GM.GameTextModel:GetText("bad_network_window_desc"), GM.GameTextModel:GetText("common_button_ok"))
      end
      self:_LogPayAction(EBIType.FP_NoNetwork, purchaseInfo.eIAPType, purchaseInfo.orderId)
      Log.Info("fail purchase, no network", LogTag.Purchase)
      return
    end
    if tbData.rcode == 7 then
      self:_ShowFailTip(purchaseInfo, "cheat_pay_remind_desc")
      self:_LogPayAction(EBIType.FP_VerifyPending, purchaseInfo.eIAPType, purchaseInfo.orderId)
      Log.Info("pending purchase", LogTag.Purchase)
      return
    elseif tbData.rcode ~= 0 then
      self:_ShowFailTip(purchaseInfo)
      local host = reqCtx.Host or ""
      local url = reqCtx.RealUrl
      local strInfo = "rcode: " .. tbData.rcode .. ", url: " .. url .. ", host: " .. host .. ", response: " .. json.encode(tbData)
      self:_LogPayAction(EBIType.FP_VerifyFailed, purchaseInfo.eIAPType, purchaseInfo.orderId, strInfo)
      self:_CloseTransaction(purchaseInfo)
      Log.Info("fail purchase, server response code is " .. tbData.rcode, LogTag.Purchase)
      return
    end
    self:_HideMask(purchaseInfo)
    self:_OnVerifySuccess(purchaseInfo)
  end)
end

function InAppPurchaseModel:_OnVerifySuccess(purchaseInfo)
  if purchaseInfo.funcSuccessCallback then
    purchaseInfo.funcSuccessCallback()
  elseif purchaseInfo.isRestore then
    local awarded = self:_RestoreRewards(purchaseInfo.eIAPType)
    if not awarded then
      return
    end
  else
    return
  end
  GM.SyncModel:CheckUpload()
  PayMessage.IAP(purchaseInfo)
  GM.BIManager:LogPay(purchaseInfo.productId, purchaseInfo.orderId, purchaseInfo.strScene)
  self:_LogPayAction(EBIType.SuccessPurchase, purchaseInfo.eIAPType, purchaseInfo.orderId, purchaseInfo.strScene)
  GM.OperBIManager:TrackClientPurchase(tostring(purchaseInfo.realMoney), purchaseInfo.currency, purchaseInfo.orderId)
  if GM.ShopModel:IsEnergyBuyInDiscount() then
    GM.BIManager:LogAction(EBIType.FinishIAPWhenEnergyDiscount, {
      t = purchaseInfo.eIAPType,
      fm = self.m_mapProducts[purchaseInfo.eIAPType].fixedMoney,
      rm = purchaseInfo.realMoney,
      c = purchaseInfo.currency
    })
  end
  self:_SetOrderIdAwarded(purchaseInfo.orderId)
  if self.m_totalPay <= 0 then
    GM.OperBIManager:TrackEvent(EOperBIEventType.first_pay)
  end
  self:_RecordPurchase(self.m_mapProducts[purchaseInfo.eIAPType].fixedMoney)
  self:_CloseTransaction(purchaseInfo)
end

function InAppPurchaseModel:_RecordPurchase(fixedMoney)
  self.m_totalPay = self.m_totalPay + math.ceil(fixedMoney * 100)
end

function InAppPurchaseModel:_RestoreRewards(eIAPType)
  if eIAPType == EIAPType.getgem_1 or eIAPType == EIAPType.getgem_2 or eIAPType == EIAPType.getgem_3 or eIAPType == EIAPType.getgem_4 or eIAPType == EIAPType.getgem_5 or eIAPType == EIAPType.getgem_6 then
    local data = GM.ShopDataModel:GetIAPConfigData(EShopType.Diamonds, eIAPType)
    if not data then
      return false
    end
    GM.PropertyDataManager:AcquireWithCollectAnimation(data.goods, EPropertySource.Buy, nil, {
      spriteKey = ShopSpriteKeyPrefix .. (data.icon or "")
    }, EBIType.Restore)
    return true
  else
    if GM.BundleManager:OnRestoreIAPRewards(eIAPType) then
      return true
    end
    if GM.ActivityManager:RestoreIapRewards(eIAPType) then
      return true
    end
    local productData = self.m_mapProducts[eIAPType]
    if productData == nil then
      Log.Error("[InAppPurchaseModel:_RestoreRewards]: no product info with IAP type " .. eIAPType)
      return false
    end
    local formattedRewards = {
      {
        [PROPERTY_TYPE] = EPropertyType.Gem,
        [PROPERTY_COUNT] = math.ceil(productData.fixedMoney * InAppPurchaseModel.UsdToGemRatio)
      }
    }
    RewardApi.CryptRewards(formattedRewards)
    RewardApi.AcquireRewards(formattedRewards, EPropertySource.Buy, EBIType.Restore, nil, EGameMode.Board, CacheItemType.Stack)
    return true
  end
end

function InAppPurchaseModel:_CanAwardForOrderId(purchaseInfo)
  local orderId = purchaseInfo.orderId
  if StringUtil.IsNilOrEmpty(orderId) then
    return false
  end
  if not StringUtil.IsNilOrEmpty(purchaseInfo.purchaseToken) then
    local fakeOrderId = MD5.Encode(purchaseInfo.purchaseToken)
    if orderId ~= fakeOrderId and self.m_dbTable:GetValue(fakeOrderId, DBColumnOrderStatus) == "1" then
      if GM and GM.BIManager then
        GM.BIManager:LogErrorInfo(EBIType.NewGoogleIAPInfo, "fakeId: " .. fakeOrderId .. " info: " .. json.encode(purchaseInfo))
      end
      return false
    end
  end
  return self.m_dbTable:GetValue(orderId, DBColumnOrderStatus) ~= 1
end

function InAppPurchaseModel:_SetOrderIdAwarded(orderId)
  self.m_dbTable:Set(orderId, DBColumnOrderStatus, 1)
end

function InAppPurchaseModel:PopPurchaseCloseTip()
  GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("purchase_close_title"), GM.GameTextModel:GetText("purchase_close_desc"), GM.GameTextModel:GetText("common_button_ok"))
end

function InAppPurchaseModel:_LogPayAction(stage, eIAPType, orderId, info, isMid)
  local timeInterval
  if isMid ~= true and self.m_startTime ~= nil then
    timeInterval = self.m_startTime:EndAndGetDur()
    self.m_startTime = nil
  end
  GM.BIManager:LogPayAction(stage, eIAPType, orderId, info, timeInterval)
end
