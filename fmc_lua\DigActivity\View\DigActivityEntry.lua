DigActivityEntry = setmetatable({}, HudPropertyButton)
DigActivityEntry.__index = DigActivityEntry

function DigActivityEntry:Awake()
  self.m_originScale = self.transform.localScale
  self.m_bDisplay = self.gameObject.activeSelf
  HudPropertyButton.Awake(self)
  self:OnGameModeChanged()
  self:_AddListeners()
end

function DigActivityEntry:Init(model)
  self.m_model = model
  self.m_activityType = self.m_model:GetType()
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  HudPropertyButton.Init(self, self.m_activityDefinition.ActivityTokenPropertyType)
  self:UpdatePerSecond()
  self:_OnScoreChanged({UpdateScore = true})
  if self.gameObject.activeInHierarchy then
    self:_AddListeners()
  end
end

function DigActivityEntry:_AddListeners()
  if self.m_activityDefinition ~= nil then
    EventDispatcher.AddListener(self.m_activityDefinition.ScoreChangedEvent, self, self._OnScoreChanged, true)
    EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self._OnStateChanged, true)
  end
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnGameModeChanged, true)
end

function DigActivityEntry:UpdatePerSecond()
  if self.m_model == nil or self.m_model:GetNextStateTime() == nil then
    return
  end
  local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
  self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
end

function DigActivityEntry:OnBtnClicked()
  local state = self.m_model:GetState()
  if state == ActivityState.Started and not GM.UIManager:IsViewExisting(self.m_activityDefinition.MainWindowPrefabName) then
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, true)
  end
end

function DigActivityEntry:_OnScoreChanged(msg)
  if self.m_model == nil then
    return
  end
  if msg and msg.UpdateScore or GM.UIManager:IsViewOpen(self.m_activityDefinition.MainWindowPrefabName) then
    HudPropertyButton.Init(self, self.m_activityDefinition.ActivityTokenPropertyType)
  end
  local score = self.m_model:GetScore()
  UIUtil.SetActive(self.m_exclamationGo, 0 < score)
end

function DigActivityEntry:_OnStateChanged()
  if not self.m_model or self.m_model:GetState() == ActivityState.Released then
    return
  end
  self:_OnScoreChanged()
  self:SyncToModelValue()
end

function DigActivityEntry:UpdateValueText()
  if self.m_exclamationText then
    self.m_exclamationText.text = math.floor(self.m_value + 0.5)
  end
end

function DigActivityEntry:OnGameModeChanged()
  if HudGeneralButton._NeedDisplay(self) then
    if not self.m_bDisplay then
      self.m_bDisplay = true
      self.transform.localScale = self.m_originScale
    end
  elseif self.m_bDisplay then
    self.m_bDisplay = false
    self.transform.localScale = V3Zero
  end
  self:_OnScoreChanged({UpdateScore = true})
end
