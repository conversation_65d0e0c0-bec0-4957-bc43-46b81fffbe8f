BundleNormalWindow = setmetatable({misTouch = false}, BundleBaseWindow)
BundleNormalWindow.__index = BundleNormalWindow

function BundleNormalWindow:Init(bundleType, dataGroup, bUserClick, eTriggerType)
  BundleBaseWindow.Init(self, bundleType, dataGroup, bUserClick, eTriggerType)
  local arrBundleIds = self.m_dataGroup:GetBundleIds()
  self.m_bundleData = dataGroup:GetConfigData(arrBundleIds[1])
  if self.m_bundleData == nil then
    Log.Error("不存在礼包数据，请检查！！")
    self:Close()
    return
  end
  self:UpdateContent()
  if self.misTouch and not bUserClick then
    self:EnterMisTouchDuration()
  end
end

function BundleNormalWindow:GetPurchaseIds()
  if self.m_dataGroup ~= nil then
    return self.m_dataGroup:GetBundleIds()
  end
end

function BundleNormalWindow:UpdatePerSecond()
  if self.m_model ~= nil and self.m_dataGroup ~= nil then
    local restDuration = self.m_model:GetRestDuration(self.m_dataGroup)
    if 0 < restDuration then
      self.m_countdownText.text = TimeUtil.ParseTimeDescription(restDuration, 2, false, false)
    else
      UIUtil.SetActive(self.m_countdownGo, false)
    end
  end
end

function BundleNormalWindow:UpdateContent()
  if self.m_bundleData == nil then
    return
  end
  local goods = self.m_bundleData:GetGoods()
  self.m_rewardContent:Init(goods, nil, EItemDetailWindowRefer.Bundle)
  self.m_IAPText.text = GM.InAppPurchaseModel:GetLocalizedPrice(self.m_bundleData:GetPurchaseId())
  self.m_discountText.text = self.m_bundleData:GetDiscountTag()
  local boughtNum, maxNum = self.m_model:GetShowBuyLimitNum(self.m_dataGroup)
  if boughtNum and maxNum and 0 < maxNum then
    self.m_leftText.text = GM.GameTextModel:GetText("pack_available_times", boughtNum, maxNum)
    UIUtil.SetActive(self.m_leftText.gameObject, true)
  else
    UIUtil.SetActive(self.m_leftText.gameObject, false)
  end
  self:UpdatePerSecond()
  local goods = self.m_bundleData:GetGoods()
  self.m_rewardTrans.sizeDelta = Vector2(self:_GetRewardContentWidth(#goods), self.m_rewardTrans.sizeDelta.y)
  self.m_rewardLayout.spacing = self:_GetRewardSpacingSize(#goods)
  if IsAutoRun() then
    DOVirtual.DelayedCall(0.5, function()
      self:OnCloseBtnClick()
    end)
  end
end

function BundleNormalWindow:_GetRewardContentWidth(count)
  if 4 <= count then
    return 757
  elseif count == 3 then
    return 657
  else
    return 557
  end
end

function BundleNormalWindow:_GetRewardSpacingSize(count)
  if 5 <= count then
    return 0
  elseif count == 4 then
    return 20
  elseif count == 3 then
    return 35
  else
    return 40
  end
end

function BundleNormalWindow:OnBtnClicked()
  if self.m_bDisableTouch then
    return
  end
  if self.m_model ~= nil and self.m_dataGroup ~= nil then
    self.m_model:BuyBundle(self.m_dataGroup, function(rewards)
      GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, rewards, "rewards_window_title_shop", true)
      self:Close()
    end)
  else
    self:Close()
  end
end

function BundleNormalWindow:OnCloseView(bWithoutAnimation)
  BaseWindow.OnCloseView(self, bWithoutAnimation)
  if self.m_model ~= nil and self.m_dataGroup ~= nil then
    self.m_model:RefreshBundleWindowPopupTime(self.m_dataGroup)
  end
end

function BundleNormalWindow:EnterMisTouchDuration()
  local disableTouchDuration = GM.ConfigModel:GetAntiMisTouchDuration()
  if disableTouchDuration ~= nil and 0 < disableTouchDuration then
    self.m_bDisableTouch = true
    DelayExecuteFuncInView(function()
      self.m_bDisableTouch = nil
    end, disableTouchDuration, self)
  end
end
