AlbumJokerExchangeConfirmWindow = setmetatable({}, AlbumActivityBaseWindow)
AlbumJokerExchangeConfirmWindow.__index = AlbumJokerExchangeConfirmWindow

function AlbumJokerExchangeConfirmWindow:Init(activityType, cardId, bShowOnlyNone)
  AlbumActivityBaseWindow.Init(self, activityType, true)
  self.m_cardId = cardId
  self.m_bShowOnlyNone = bShowOnlyNone
  self.m_bHasCard = self.m_model:GetCardCount(cardId) > 0
  local descKey = self.m_bHasCard and "joker_card_choose_confirm_desc2" or "joker_card_choose_confirm_desc"
  self.m_descText.text = GM.GameTextModel:GetText(descKey)
  local cardInfo = self.m_model:GetCardInfo(cardId)
  local cur, max = self.m_model:GetSetCollectProgress(cardInfo.setId)
  local bOnlyOne = max - cur == 1
  self.m_exchangeCardLuaTable:Init(cardId, self.m_model, false, bOnlyOne)
end

function AlbumJokerExchangeConfirmWindow:OnCloseFinish()
  BaseWindow.OnCloseFinish(self)
  if self.m_openPrefabName == UIPrefabConfigName.AlbumJokerExchangeCompleteWindow then
    local prefab = UIPrefabConfigName.AlbumJokerExchangeCompleteWindow
    GM.UIManager:OpenView(prefab, self.m_activityType, self.m_cardId)
  elseif self.m_openPrefabName == UIPrefabConfigName.AlbumJokerExchangeWindow then
    GM.UIManager:OpenView(UIPrefabConfigName.AlbumJokerExchangeWindow, self.m_activityType, false, self.m_bShowOnlyNone)
  end
end

function AlbumJokerExchangeConfirmWindow:OnConfirmClicked()
  self.m_isGoldenJoker = self.m_model:IsGoldenJokerCardInUse()
  local bSuc = self.m_model:JokerExchange(self.m_cardId)
  self:Close()
  self.m_openPrefabName = UIPrefabConfigName.AlbumJokerExchangeCompleteWindow
end

function AlbumJokerExchangeConfirmWindow:OnCloseBtnClick()
  self:Close()
  self.m_openPrefabName = UIPrefabConfigName.AlbumJokerExchangeWindow
end
