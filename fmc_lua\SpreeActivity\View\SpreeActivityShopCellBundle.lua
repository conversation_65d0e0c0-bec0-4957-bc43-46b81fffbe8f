SpreeActivityShopCellBundle = setmetatable({}, BaseShopCell)
SpreeActivityShopCellBundle.__index = SpreeActivityShopCellBundle

function SpreeActivityShopCellBundle:Init(data, callback)
  self.m_shopModel = GM.ActivityManager:GetModel(data.ActivityType):GetShopModel()
  BaseShopCell.Init(self, data, callback)
end

function SpreeActivityShopCellBundle:UpdatePerSecond()
  local remainTime = self.m_shopModel:GetActiveBundleRemainTime()
  self.m_timeText.text = TimeUtil.ParseTimeDescription(remainTime, 2, false, false)
end

function SpreeActivityShopCellBundle:UpdateContent(data)
  BaseShopCell.UpdateContent(self, data)
  if self.m_items == nil then
    self.m_items = {
      self.m_itemGo:GetLuaTable()
    }
  end
  for i = 1, #data.Rewards do
    if self.m_items[i] == nil then
      self.m_items[i] = Object.Instantiate(self.m_itemGo, self.m_itemRoot):GetLuaTable()
    end
    self.m_items[i].gameObject:SetActive(true)
    self.m_items[i]:Init(data.Rewards[i])
  end
  for i = #data.Rewards + 1, #self.m_items do
    self.m_items[i].gameObject:SetActive(false)
  end
  local price = GM.InAppPurchaseModel:GetLocalizedPrice(data.PurchaseId)
  self.m_IAPButton:Init(price, self.m_callback)
  self:UpdatePerSecond()
end
