BakeOutPodiumCell = {}
BakeOutPodiumCell.__index = BakeOutPodiumCell

function BakeOutPodiumCell:Awake()
  EventDispatcher.AddListener(EEventType.UpdateProfile, self, self._OnChangeProfile)
end

function BakeOutPodiumCell:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BakeOutPodiumCell:_OnChangeProfile()
  if self.m_userId == GM.UserModel:GetUserId() then
    local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
    self.m_nameText.text = GM.UserProfileModel:GetName()
    self.m_userAvatar:SetAvatar(EAvatarFrame.Normal, GM.UserProfileModel:GetIcon())
  end
end

function BakeOutPodiumCell:UpdateContent(rankData)
  self.m_userId = rankData and rankData.userid or -1
  if rankData == nil then
    self:_Reset()
    return
  end
  self.m_tokenGo:SetActive(true)
  self.m_userAvatar.gameObject:SetActive(true)
  self.m_userAvatar:SetAvatar(rankData.rank == 1 and EAvatarFrame.Highlight or EAvatarFrame.Normal, rankData.icon)
  if self.m_nameText ~= nil then
    self.m_nameText.text = rankData.name
  end
  self.m_tokenText.text = rankData.score
  self:_UpdateRankIcon(rankData.rank)
end

function BakeOutPodiumCell:GetUserAvatar()
  return self.m_userAvatar
end

function BakeOutPodiumCell:_Reset()
  self.m_userAvatar.gameObject:SetActive(false)
  if self.m_nameText ~= nil then
    self.m_nameText.text = ""
  end
  self.m_tokenText.text = ""
  self.m_tokenGo:SetActive(false)
end

local mapMedalImageName = {
  [1] = ImageFileConfigName.medal_bg1,
  [2] = ImageFileConfigName.medal_bg2,
  [3] = ImageFileConfigName.medal_bg3
}

function BakeOutPodiumCell:_UpdateRankIcon(rank)
  if self.m_rankText ~= nil then
    self.m_rankText.text = rank
  elseif mapMedalImageName[rank] ~= nil then
    SpriteUtil.SetImage(self.m_medalImg, mapMedalImageName[rank], true)
  end
end
