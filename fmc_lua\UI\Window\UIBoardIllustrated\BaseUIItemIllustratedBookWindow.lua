BaseUIItemIllustratedBookWindow = setmetatable({}, BaseWindow)
BaseUIItemIllustratedBookWindow.__index = BaseUIItemIllustratedBookWindow
local getChainHeight = function(itemCount)
  return 146 + 190 * ((itemCount + 3) // 4)
end
local lastClickId
local MOVE_CONTAINER_TWEEN_DURATION = 0.4

function BaseUIItemIllustratedBookWindow:Init(activityType, itemBookModel, bUserClick)
  self.m_activityType = activityType
  self.m_activityModel = GM.ActivityManager:GetModel(activityType)
  self.m_activityDefinition = self.m_activityModel:GetActivityDefinition()
  self.m_itemBookModel = itemBookModel
  self.m_finalReward = itemBookModel:GetFinalReward()
  self.m_dataList = itemBookModel:GetCacheInfo()
  self.m_listView:InitListView(#self.m_dataList, function(listView, cellIndex)
    return self:_OnListItemByIndex(listView, cellIndex)
  end)
  self.m_listView:SetListItemCount(#self.m_dataList, true)
  self.m_listView:RefreshAllShownItem()
  self:_UpdateListViewHeight()
  self.m_mapTweens = {}
  self:_TryMove()
  UIUtil.SetActive(self.m_testUnlockButtonGo, GM.UIManager:CanShowTestUI())
  local bHasFinalReward = not Table.IsEmpty(self.m_finalReward)
  UIUtil.SetActive(self.m_finalRewardGo, bHasFinalReward)
  UIUtil.SetSizeDelta(self.m_scrollRect.transform, nil, bHasFinalReward and 1008 or 1150)
  if not Table.IsEmpty(self.m_finalReward) then
    UIUtil.SetActive(self.m_rewardTip.gameObject, false)
    self:UpdateProgressReward()
    self:UpdateProgressSlider()
    RewardApi.AddFilterRewardEventListener(self, self.UpdateProgressReward)
    AddHandlerAndRecordMap(self.m_itemBookModel.event, BaseUIItemIllustratedBookModel.EventType.Refreshed, {
      obj = self,
      method = self.UpdateProgressSlider
    })
  end
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self.Close)
  lastClickId = nil
  self:LogWindowAction(EBIType.UIActionType.Open, {
    bUserClick and EBIReferType.UserClick or EBIReferType.AutoPopup
  })
end

function BaseUIItemIllustratedBookWindow:OnDestroy()
  BaseWindow.OnDestroy(self)
  if self.m_itemBookModel ~= nil then
    RemoveAllHandlers(self.m_itemBookModel.event, self)
  end
  EventDispatcher.RemoveTarget(self)
  GM.UIManager:RemoveAllEventLocks(self)
end

function BaseUIItemIllustratedBookWindow:AddEventListener()
  EventDispatcher.AddListener(EEventType.UIItemBookUpdate, self, self._TryMove)
end

function BaseUIItemIllustratedBookWindow:RemoveEventListener()
  EventDispatcher.RemoveTarget(self)
end

function BaseUIItemIllustratedBookWindow:_TryMove(msg)
  self.m_content:DOKill(false)
  self.m_scrollRect:StopMovement()
  local chainIndex, itemIndex
  local lastChainId = msg and msg.chainId
  local lastChainIndex = msg and msg.chainIndex
  local shouldMove = not lastChainId
  if not shouldMove then
    local nextCodeInSameChain, nextCodeIndex = self.m_itemBookModel:GetCanClaimRewardItemByIndex(lastChainIndex + 1)
    if not nextCodeInSameChain then
      shouldMove = true
    else
      local chainItem = self.m_listView:GetShownItemByItemIndex(lastChainIndex)
      if chainItem and not chainItem:IsNull() then
        local lastChainTb = chainItem.gameObject:GetLuaTable()
        local cellGo = lastChainTb:GetCellTb(nextCodeIndex).gameObject
        local relativePos = self.m_scrollRect.viewport:InverseTransformPoint(cellGo.transform.position)
        if relativePos.y <= -self.m_scrollRect.transform.rect.height + 100 or relativePos.y >= -100 then
          shouldMove = true
          chainIndex = lastChainIndex + 1
          itemIndex = nextCodeIndex
        end
      end
    end
  end
  if shouldMove and chainIndex == nil then
    chainIndex, itemIndex = self.m_itemBookModel:GetCanClaimRewardItem()
  end
  if chainIndex ~= nil and shouldMove then
    self:_MoveTo(chainIndex, itemIndex)
  end
end

function BaseUIItemIllustratedBookWindow:OnCloseView(bWithoutAnimation)
  for seq, _ in pairs(self.m_mapTweens) do
    seq:Kill()
  end
  self.m_mapTweens = nil
  BaseWindow.OnCloseView(self, bWithoutAnimation)
end

function BaseUIItemIllustratedBookWindow:_AddTween(tween)
  if not self.m_mapTweens then
    tween:Kill()
  else
    self.m_mapTweens[tween] = true
  end
end

function BaseUIItemIllustratedBookWindow:_OnListItemByIndex(listView, index)
  local data = self.m_dataList[index + 1]
  if not data then
    return nil
  end
  local item = listView:NewListViewItem("BaseUIItemBookChain")
  local chainTb = item.gameObject:GetLuaTable()
  chainTb:Init(self.m_itemBookModel, data, index, self)
  return item
end

function BaseUIItemIllustratedBookWindow:GetCanRewardCell()
  if not self.m_curChainIndex then
    return nil
  end
  local curChainObj = self.m_listView:GetShownItemByIndex(self.m_curChainIndex - 1)
  if curChainObj then
    local curChainTb = curChainObj.gameObject:GetLuaTable()
    if curChainTb and curChainTb.m_list then
      for _, item in ipairs(curChainTb.m_list) do
        if item:GetState() == BaseUIItemIllustratedBookModel.ItemState.UnLock then
          return item.gameObject.transform
        end
      end
    end
  end
  return nil
end

function BaseUIItemIllustratedBookWindow:_UpdateListViewHeight()
  local height, items
  local itemDataModel = GM.ItemDataModel
  for i = 1, #self.m_dataList do
    items = self.m_dataList[i].itemInfos
    height = getChainHeight(#items)
    self.m_listView:SetItemSize(i - 1, height)
  end
  self.m_listView:UpdateContentSize()
end

function BaseUIItemIllustratedBookWindow:_MoveTo(chainIndex, itemIndex)
  local finishPosY = 0
  local scrollViewHeight = self.m_content.rect.height
  local endPos = scrollViewHeight - self.m_content.parent.rect.height
  if 0 < endPos then
    finishPosY = self.m_listView:GetItemPos(chainIndex - 1)
    if itemIndex then
      finishPosY = finishPosY + (itemIndex - 1) // 4 * 189
    end
    finishPosY = math.min(finishPosY, endPos)
  end
  self.m_scrollRect.velocity = V2Zero
  local tween = self.m_content:DOLocalMoveY(finishPosY, MOVE_CONTAINER_TWEEN_DURATION):SetEase(Ease.InCubic)
  self:_AddTween(tween)
end

function BaseUIItemIllustratedBookWindow:MoveChainRewardItemVisible(chainIndex)
  local go = self.m_listView:GetShownItemByIndex(chainIndex)
  if go == nil then
    return
  end
  local transform = go.transform
  local scrollViewHeight = self.m_scrollRect.transform.rect.height
  local itemHeight = 100
  local height = self.m_content.rect.height - scrollViewHeight
  if height <= 0 then
    return
  end
  local contentPosY = self.m_content.anchoredPosition.y
  local cellPosY = -go.transform.anchoredPosition.y
  local topY = cellPosY - contentPosY
  local bottomY = topY + itemHeight
  if 0 <= topY and scrollViewHeight >= bottomY then
    return
  end
  self:_MoveTo(chainIndex + 1, 1)
end

function BaseUIItemIllustratedBookWindow:OnTestUnlockClick()
  if GM.UIManager:CanShowTestUI() then
    self.m_itemBookModel:OnTestUnlockAllItem()
    self:Close()
  end
end

function BaseUIItemIllustratedBookWindow:RefreshAllShownItem()
  self.m_listView:RefreshAllShownItem()
end

function BaseUIItemIllustratedBookWindow:UpdateProgressSlider()
  local curProgress, maxProgress = self.m_itemBookModel:GetProgress()
  self.m_sliderText.text = curProgress .. "/" .. maxProgress
  if maxProgress ~= 0 then
    self.m_slider.value = curProgress / maxProgress
  end
end

function BaseUIItemIllustratedBookWindow:UpdateProgressReward()
  local validRewards = RewardApi.FilterRewards(self.m_finalReward)
  self.m_validRewards = validRewards
  if Table.IsEmpty(self.m_validRewards) then
    return
  end
  local bOnce = #self.m_validRewards == 1
  UIUtil.SetActive(self.m_rewardContent.gameObject, bOnce)
  UIUtil.SetActive(self.m_rewardBoxGo, not bOnce)
  local curProgress, maxProgress = self.m_itemBookModel:GetProgress()
  local bComplete = curProgress == maxProgress
  if bOnce then
    self.m_rewardContent:Init(validRewards)
    UIUtil.SetActive(self.m_rewardCheckGo, bComplete)
  else
    UIUtil.SetActive(self.m_boxCheckGo, bComplete)
  end
end

function BaseUIItemIllustratedBookWindow:OnRewardBoxClicked()
  self.m_rewardTip:Show(self.m_validRewards, self.m_rewardBoxGo.transform, 0, 50, nil, true)
  GM.BIManager:LogAction(EBIType.ShowBookFinalReward)
end

function BaseUIItemIllustratedBookWindow:OnCloseBtnClick()
  self.m_rewardTip:Hide()
  BaseWindow.OnCloseBtnClick(self)
end

function BaseUIItemIllustratedBookWindow:PlayFinalRewardAnim(finalReward)
  local validRewards = RewardApi.FilterRewards(finalReward)
  if Table.IsEmpty(validRewards) then
    return
  end
  self:UpdateProgressReward()
  local bOnce = #validRewards == 1
  if bOnce then
    local rewardItem = self.m_rewardContent:GetRewardItem(1)
    local startPos = rewardItem and rewardItem.transform.position or V3Zero
    GM.UIManager:OpenView(UIPrefabConfigName.BaseUIItemBookRewardWindow, validRewards, nil, startPos)
  else
    GM.UIManager:OpenView(UIPrefabConfigName.BoxRewardWindow, self.m_rewardBoxGo.transform.position, 0.4, ImageFileConfigName.dig_chest_3, validRewards, nil, true)
  end
end

BaseUIItemBookChain = {}
BaseUIItemBookChain.__index = BaseUIItemBookChain

function BaseUIItemBookChain:Init(itemBookModel, cacheInfo, index, bookWindow)
  self.m_itemBookModel = itemBookModel
  self.m_bookWindow = bookWindow
  if not self.m_list then
    self.m_list = {}
    self.m_transform = self.gameObject.transform
  end
  local chainId = cacheInfo.config.chainId
  self.m_chain = chainId
  self.m_name.text = GM.GameTextModel:GetText("chain_" .. chainId .. "_name")
  local rewards = cacheInfo.config.reward
  rewards = RewardApi.FilterRewards(rewards)
  if not Table.IsEmpty(rewards) then
    UIUtil.SetActive(self.m_rewardContent.gameObject, true)
    self.m_rewardContent:Init(rewards)
    local bAcquireAll = self.m_itemBookModel:HasAcquireAllChainItemReward(cacheInfo.index)
    self.m_rewardCanvasGroup.alpha = bAcquireAll and 0.5 or 1
    local rewardItem, checkTrans
    for index, _ in ipairs(rewards) do
      rewardItem = self.m_rewardContent:GetRewardItem(index)
      checkTrans = rewardItem.transform:Find("check")
      if checkTrans ~= nil then
        UIUtil.SetActive(checkTrans.gameObject, bAcquireAll)
      end
    end
  else
    UIUtil.SetActive(self.m_rewardContent.gameObject, false)
  end
  local items = GM.ItemDataModel:GetChain(chainId)
  local count = #items
  for i = 1, count do
    if not self.m_list[i] then
      local go = GameObject.Instantiate(self.m_item, self.m_content)
      self.m_list[i] = go:GetLuaTable()
    end
    self.m_list[i].gameObject:SetActive(true)
    self.m_list[i]:Init(self.m_itemBookModel, self.m_chain, index, items[i], i % 4 ~= 0 and i ~= count, self, self.m_bookWindow)
  end
  for i = count + 1, #self.m_list do
    self.m_list[i].gameObject:SetActive(false)
  end
  self.m_transform.sizeDelta = Vector2(self.m_transform.sizeDelta.x, getChainHeight(count))
end

function BaseUIItemBookChain:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BaseUIItemBookChain:GetPosY()
  return -self.m_transform.localPosition.y
end

function BaseUIItemBookChain:GetHeight()
  return self.m_transform.rect.height
end

function BaseUIItemBookChain:GetChain()
  return self.m_chain
end

function BaseUIItemBookChain:GetCellTb(index)
  return self.m_list[index]
end

function BaseUIItemBookChain:PlayChainRewardAnim()
  self.m_rewardContent:PlayRewardAnimation()
  self.m_bookWindow:RefreshAllShownItem()
end

BaseUIItemBookCell = {}
BaseUIItemBookCell.__index = BaseUIItemBookCell
local white = CSColor.white
local transparent = CSColor(1, 1, 1, 0.4)

function BaseUIItemBookCell:Init(itemBookModel, chainId, chainIndex, itemType, showArrow, chainCell, bookWindow)
  self.m_itemBookModel = itemBookModel
  self.m_itemInfo = self.m_itemBookModel:GetItemInfo(itemType)
  if self.m_itemInfo == nil then
    return
  end
  self.m_chainCell = chainCell
  self.m_bookWindow = bookWindow
  self.m_chainId = chainId
  self.m_chainIndex = chainIndex
  self.m_type = itemType
  local state = self.m_itemInfo.state
  self.m_boxGo:SetActive(false)
  self.m_icon.gameObject:SetActive(true)
  self.m_icon.color = white
  self:StopBoxAnimation()
  self.m_icon.enabled = false
  self.m_detailBtn.enabled = false
  if state == BaseUIItemIllustratedBookModel.ItemState.Lock then
    SpriteUtil.SetImage(self.m_icon, ImageFileConfigName.detail_future, true)
  else
    if state == BaseUIItemIllustratedBookModel.ItemState.UnLock then
      self.m_button.interactable = true
      self.m_boxGo:SetActive(true)
      self.m_icon.gameObject:SetActive(false)
      self:_TryCreateBoxAnimation()
    else
      self.m_detailBtn.enabled = true
    end
    SpriteUtil.SetImage(self.m_icon, GM.ItemDataModel:GetSpriteName(itemType), true)
  end
  self.m_showArrow = showArrow
  self.m_arrow:SetActive(showArrow)
end

function BaseUIItemBookCell:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  if self.m_boxTween then
    self.m_boxTween:Kill()
    self.m_boxTween = nil
  end
  if self.m_rewardSeq ~= nil then
    self.m_rewardSeq:Kill()
    self.m_rewardSeq = nil
  end
end

function BaseUIItemBookCell:_TryCreateBoxAnimation()
  if self.m_boxTween then
    self.m_boxTween:Restart()
    return
  end
  local boxTransform = self.m_boxGo.transform
  local s = DOTween.Sequence()
  s:Append(boxTransform:DOScaleY(0.8, 0.05)):SetEase(Ease.InSine)
  s:Join(boxTransform:DOScaleX(1.2, 0.05)):SetEase(Ease.InSine)
  s:Append(boxTransform:DOScaleY(1.1, 0.2)):SetEase(Ease.OutSine)
  s:Join(boxTransform:DOScaleX(0.9, 0.2)):SetEase(Ease.OutSine)
  s:Append(boxTransform:DOScale(1, 0.2)):SetEase(Ease.InSine)
  s:AppendInterval(1)
  self.m_boxTween = s:SetLoops(-1)
end

function BaseUIItemBookCell:StopBoxAnimation()
  if self.m_boxTween then
    self.m_boxTween:Pause()
  end
  local boxTransform = self.m_boxGo.transform
  boxTransform:SetLocalPosY(0)
  boxTransform:SetLocalScaleXY(1)
end

function BaseUIItemBookCell:OnClickDetail()
  ItemDetailWindow.Open(self.m_type, ItemDetailWindowMode.Normal, EItemDetailWindowRefer.Discovery)
end

function BaseUIItemBookCell:OnClickBox()
  lastClickId = self.m_type
  self.m_button.interactable = false
  local rewards, setReward, finalReward = self.m_itemBookModel:AcquireCacheItemInfo(self.m_itemInfo.itemCode)
  local msg = {
    chainId = self.m_chainId,
    chainIndex = self.m_chainIndex
  }
  if rewards ~= nil then
    RewardApi.AcquireRewardsInView(rewards, {
      arrWorldPos = {
        self.m_icon.transform.position
      },
      noDelayTime = true
    })
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxUnlockGift)
    self.m_boxGo:SetActive(false)
    self.m_icon.gameObject:SetActive(true)
    self.m_detailBtn.enabled = true
    self:StopBoxAnimation()
    if setReward == nil and finalReward == nil then
      EventDispatcher.DispatchEvent(EEventType.UIItemBookUpdate, msg)
    end
  end
  local seq = DOTween.Sequence()
  if setReward ~= nil then
    GM.UIManager:SetEventLock(true, self)
    self.m_bookWindow:MoveChainRewardItemVisible(self.m_chainIndex)
    seq:AppendInterval(0.5)
    seq:AppendCallback(function()
      self.m_chainCell:PlayChainRewardAnim()
      GM.UIManager:SetEventLock(false, self)
    end)
  end
  if finalReward ~= nil then
    GM.UIManager:SetEventLock(true, self)
    seq:AppendInterval(1)
    seq:AppendCallback(function()
      self.m_bookWindow:PlayFinalRewardAnim(finalReward)
      GM.UIManager:SetEventLock(false, self)
    end)
  else
    seq:AppendInterval(0.5)
    seq:AppendCallback(function()
      if lastClickId == self.m_type then
        EventDispatcher.DispatchEvent(EEventType.UIItemBookUpdate, msg)
      end
    end)
  end
  seq:AppendCallback(function()
    self.m_rewardSeq = nil
  end)
  self.m_rewardSeq = seq
end

function BaseUIItemBookCell:GetState()
  return self.m_itemInfo.state
end
