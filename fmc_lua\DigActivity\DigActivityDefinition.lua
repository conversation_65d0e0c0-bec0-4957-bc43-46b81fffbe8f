DigActivityDefinition = {
  [ActivityType.TreasureDig] = {
    EntryButtonKey = ESceneViewHudButtonKey.TreasureDig,
    eEntryRootKey = EEntryRootKey.DigActivity,
    ActivityDataTableName = VirtualDBTableName.TreasureDig,
    StateChangedEvent = EEventType.TreasureDigStateChanged,
    ScoreChangedEvent = EEventType.TreasureDigScoreChanged,
    DigItemStateChangedEvent = EEventType.TreasureDigItemStateChanged,
    GridChangedEvent = EEventType.TreasureDigGridChanged,
    ActivityTokenPropertyType = EPropertyType.TreasureDigToken,
    GetRewardsBIType = EBIType.TreasureDigGetRewards,
    AcquireScoreBIType = EBIType.TreasureDigGetScore,
    AcquireTutorialScoreBIType = EBIType.TreasureDigGetTutorialScore,
    UseScoreBIType = EBIType.TreasureDigUseScore,
    DigActionBIType = EBIType.TreasureDigGridAction,
    RandomSelectLevelBIType = EBIType.TreasureDigRandomSelectLevel,
    EntryPrefabName = UIPrefabConfigName.TreasureDigEntry,
    BoardEntryPrefabName = UIPrefabConfigName.TreasureDigBoardEntry,
    MainWindowPrefabName = UIPrefabConfigName.TreasureDigMainWindow,
    EndWindowPrefabName = UIPrefabConfigName.TreasureDigEndWindow,
    StartWindowPrefabName = UIPrefabConfigName.TreasureDigStartWindow,
    SuccessWindowPrefabName = UIPrefabConfigName.TreasureDigSuccessWindow,
    HelpWindowPrefabName = UIPrefabConfigName.TreasureDigHelpWindow,
    TwoButtonWindowPrefabName = UIPrefabConfigName.TreasureDigTwoButtonWindow,
    TokenImageName = ImageFileConfigName.treasuredig_token_icon,
    BigTokenImageName = ImageFileConfigName.treasuredig_token_icon_big,
    TileDarkImageName = ImageFileConfigName.treasuredig_tile_bg1,
    TileLightImageName = ImageFileConfigName.treasuredig_tile_bg2,
    TutorialStartCondition = ETutorialStartCondition.DigActivityStart,
    LackScoreTextKey = "treasure_dig_not_tap",
    ResourceLabels = {
      AddressableLabel.DigActivityCommon,
      AddressableLabel.TreasureDig,
      AddressableLabel.DigItems1
    },
    GridSize = 130,
    SpacingOffsetX = 0,
    SpacingOffsetY = 0,
    ItemGrooveMaxNum = 6,
    BoardBottomMargin = 240
  }
}
for activityType, activityDefinition in pairs(DigActivityDefinition) do
  EPropertySprite[activityDefinition.ActivityTokenPropertyType] = activityDefinition.TokenImageName
  EPropertySpriteBig[activityDefinition.ActivityTokenPropertyType] = activityDefinition.BigTokenImageName
end
