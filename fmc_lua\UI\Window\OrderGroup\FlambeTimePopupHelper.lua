FlambeTimePopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true
  },
  canIgnorePopup = false
}, BasePopupHelper)
FlambeTimePopupHelper.__index = FlambeTimePopupHelper

function FlambeTimePopupHelper.Create()
  local helper = setmetatable({}, FlambeTimePopupHelper)
  helper:Init()
  return helper
end

function FlambeTimePopupHelper:Init()
  BasePopupHelper.Init(self)
  EventDispatcher.AddListener(EEventType.FlambeTimePopup, self, self._OnFlambeTimePopup)
  EventDispatcher.AddListener(EEventType.FlambeTimeChanged, self, self._OnFlambeTimeChanged)
end

function FlambeTimePopupHelper:_OnFlambeTimePopup(params)
  self:SetNeedCheckPopup(true)
  self.m_bCanPop = true
  self.m_params = params
end

function FlambeTimePopupHelper:_OnFlambeTimeChanged()
  local bIsFlambeTime = GM.FlambeTimeModel:IsFlambeTime()
  if not bIsFlambeTime then
    self.m_bCanPop = nil
  end
end

function FlambeTimePopupHelper:CheckPopup()
  if self.m_bCanPop then
    self.m_bCanPop = nil
    return UIPrefabConfigName.FlambeTimeWindow, self.m_params
  end
end
