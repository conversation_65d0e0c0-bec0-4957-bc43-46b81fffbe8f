PinataAvatar = {}
PinataAvatar.__index = PinataAvatar

function PinataAvatar:Init()
  self:Hide()
end

function PinataAvatar:Hide()
  UIUtil.SetActive(self.m_avatarGo, false)
end

function PinataAvatar:PlayEnterAnimation()
  UIUtil.SetActive(self.m_avatarGo, true)
  self.m_animator:SetTrigger("drop")
end

local hitTrigger = {
  "hit_1",
  "hit_2",
  "hit_3",
  "hit_4"
}

function PinataAvatar:PlayHitAnimation(hitLevel)
  UIUtil.SetActive(self.m_avatarGo, true)
  self.m_animator:SetTrigger(hitTrigger[hitLevel] or hitTrigger[#hitTrigger])
  if self.m_effect ~= nil then
    self.m_effect:Play()
  end
  GM.AudioModel:PlayEffect(AudioFileConfigName.sfxPinataHit)
end

PinataAnimation = {}
PinataAnimation.__index = PinataAnimation

function PinataAnimation:PlayIdleAnimation()
  UIUtil.SetActive(self.m_spine.gameObject, true)
  self.m_spine.AnimationState:SetAnimation(0, "pinata_idle", true)
end

function PinataAnimation:PlayDropAnimation()
  UIUtil.SetActive(self.m_spine.gameObject, true)
  self.m_spine.AnimationState:SetAnimation(0, "pinata_drop", false)
  self.m_spine.AnimationState:AddAnimation(0, "pinata_idle", true, 0)
end

function PinataAnimation:PlayBreakAnimation()
  self.m_spine.AnimationState:SetAnimation(0, "pinata_break", false)
  GM.AudioModel:PlayEffect(AudioFileConfigName.sfxPinataBreak)
end
