RangeGenerator = {}
RangeGenerator.__index = RangeGenerator
local random = math.random

function RangeGenerator.Create(dataTb)
  local instance = setmetatable({}, RangeGenerator)
  instance:_Init(dataTb)
  return instance
end

function RangeGenerator:_Init(arrData)
  arrData = arrData or {}
  self.min = arrData[1] or 0
  self.max = arrData[2] or 0
  if self.max < self.min then
    Log.Assert(false, "RangeGenerator:_Init")
    self.max = self.min
  end
end

function RangeGenerator:Generate()
  return random(self.min, self.max)
end

function RangeGenerator:IsAlive()
  return self.max > 0
end
