ItemSplitView = setmetatable({}, BaseItemViewComponent)
ItemSplitView.__index = ItemSplitView

function ItemSplitView:Init(model)
  self.m_model = model
  AddHandlerAndRecordMap(self.m_model.event, ItemSplitEventType.StateChanged, {
    obj = self,
    method = self._OnStateChanged
  })
  self:_OnStateChanged()
end

function ItemSplitView:OnDestroy()
  RemoveAllHandlers(self.m_model.event, self)
  EventDispatcher.RemoveTarget(self)
end

function ItemSplitView:_OnStateChanged()
  local count = self.m_model:GetSplitUseCount()
  self.m_number.text = 999 < count and 999 or count
end

function ItemSplitView:SetFlying(flying)
  self.m_numberGo:SetActive(not flying)
end

function ItemSplitView:OnDragBegin()
  self.m_numberGo:SetActive(false)
end

function ItemSplitView:OnDragEnd()
  self.m_numberGo:SetActive(true)
end
