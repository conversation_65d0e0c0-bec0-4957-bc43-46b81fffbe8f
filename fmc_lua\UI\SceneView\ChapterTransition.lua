ChapterTransition = setmetatable({
  canCloseByAndroidBack = false,
  canClickWindowMask = false,
  showWindowMask = false,
  needBlurEffect = false,
  canCloseByChangeGameMode = false,
  sortingOrder = ESpecialViewSortingOrder.Transition
}, BaseWindow)
ChapterTransition.__index = ChapterTransition

function ChapterTransition:Init(nextChapterName, enterCallback, canExitCheckFunc, loadFinishCallback, finishCallback)
  local nextChapterId = GM.ChapterDataModel:GetChapterIdByName(nextChapterName)
  local nextImageName = GM.ChapterDataModel:GetChapterImageKey(nextChapterName)
  SpriteUtil.SetImage(self.m_nextImg, nextImageName, true)
  self.m_nextName.text = GM.ChapterDataModel:GetChapterNameText(nextChapterName)
  self:_PlayCurrentAnimation()
  GM.UIManager:SetEventLock(true)
  EventDispatcher.DispatchEvent(EEventType.ChapterWillChange)
  EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {
    Show = false,
    AnchorTypes = self:GetHudAnchorTypes()
  })
  self.m_canExitCheckFunc = canExitCheckFunc
  self.m_checkingCanExit = false
  self.m_enterCallback = enterCallback
  self.m_loadFinishCallback = loadFinishCallback
  self.m_finishCallback = finishCallback
end

function ChapterTransition:OnShowFinish()
  if self.m_enterCallback then
    self.m_enterCallback()
    self.m_enterCallback = nil
  end
  if GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    GM.SceneManager:ChangeGameMode(EGameMode.Main, function()
      self.m_checkingCanExit = true
    end)
  else
    self.m_checkingCanExit = true
  end
end

function ChapterTransition:Update()
  if self.m_checkingCanExit and self.m_animationFinished and self.m_canExitCheckFunc() then
    self.m_checkingCanExit = false
    if self.m_loadFinishCallback then
      self.m_loadFinishCallback()
      self.m_loadFinishCallback = nil
    end
    EventDispatcher.DispatchEvent(EEventType.ChapterChangeWillFinish)
    self:_PlayNextAnimation()
  end
end

function ChapterTransition:_PlayCurrentAnimation()
  self.m_animationFinished = false
  local canvasSize = GM.UIManager:GetCanvasSize()
  local canvasWidth = canvasSize.x
  local canvasHeight = canvasSize.y
  UIUtil.SetSizeDelta(self.m_bgTrans, canvasWidth, canvasHeight)
  local bgScale = canvasHeight / 2048
  self.m_bgTrans:SetLocalScaleXY(1 < bgScale and bgScale or 1)
  self.m_maskScale = canvasHeight / self.m_maskWindowTrans.rect.size.y * 1.5
  self.m_maskWindowTrans:SetLocalScaleXY(self.m_maskScale)
  local s = DOTween:Sequence()
  s:Append(self.m_maskWindowTrans:DOScale(0, 0.5):SetEase(Ease.OutSine))
  self.m_nextWindowTrans:SetLocalScaleXY(0, 0)
  self.m_nextName.transform:SetLocalScaleXY(0, 0)
  s:Append(self.m_nextWindowTrans:DOScale(1, 0.5):SetEase(Ease.OutBack))
  s:Join(self.m_nextName.transform:DOScale(1, 0.5):SetEase(Ease.OutBack))
  s:AppendCallback(function()
    self:OnShowFinish()
  end)
  self.m_slider.value = 0
  self.m_nextTrans:SetLocalPosX(0)
  self.m_slider.transform:SetLocalScaleXY(0, 0)
  UIUtil.SetSizeDelta(self.m_nextTrans, canvasWidth)
  s:AppendCallback(function()
    self.m_animationFinished = true
  end)
end

function ChapterTransition:_PlayNextAnimation()
  local s = DOTween:Sequence()
  s:Append(self.m_slider.transform:DOScale(1, 0.3):SetEase(Ease.OutBack))
  s:Append(self.m_slider:DOValue(1, 0.5):SetEase(Ease.InOutSine))
  s:Join(self.m_sliderText:DOCounter(0, 100, 0.5):SetEase(Ease.InOutSine))
  s:AppendCallback(function()
    self.m_slider.gameObject:SetActive(false)
  end)
  s:Append(self.m_nextWindowTrans:DOScale(0, 0.5):SetEase(Ease.InBack))
  s:Join(self.m_nextName.transform:DOScale(0, 0.5):SetEase(Ease.InBack))
  Log.Assert(self.m_maskScale, "房间切换动画逻辑错误")
  s:Append(self.m_maskWindowTrans:DOScale(self.m_maskScale, 0.5):SetEase(Ease.InSine))
  s:AppendCallback(function()
    self:Close()
    GM.UIManager:SetEventLock(false)
    local onFinish = self.m_finishCallback
    EventDispatcher.DispatchEvent(EEventType.ChapterChangeFinished)
    EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {
      Show = true,
      AnchorTypes = self:GetHudAnchorTypes()
    })
    GM.UIManager:SetEventLock(true)
    DelayExecuteFunc(function()
      GM.UIManager:SetEventLock(false)
    end, SceneViewHud.Duration)
    if onFinish then
      onFinish()
    end
  end)
end

function ChapterTransition:IsInReviewRoom()
  if GM.TaskManager:IsAutoChangingChapter() then
    GM.TaskManager:SetAutoChangingChapter(false)
    return false
  end
  return GM.ChapterManager.curActiveChapterName ~= GM.TaskManager:GetOngoingChapterName()
end

function ChapterTransition:GetHudAnchorTypes()
  return self:IsInReviewRoom() and {
    EHudAnchorType.BottomLeft
  } or nil
end
