OrderDayPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true,
    [EPopupScene.Main] = true
  },
  canIgnorePopup = false
}, BasePopupHelper)
OrderDayPopupHelper.__index = OrderDayPopupHelper

function OrderDayPopupHelper.Create()
  local helper = setmetatable({}, OrderDayPopupHelper)
  helper:Init()
  return helper
end

function OrderDayPopupHelper:Init()
  BasePopupHelper.Init(self)
  EventDispatcher.AddListener(EEventType.NewReleasedOrderGroupUnlocked, self, self._UpdateState)
end

function OrderDayPopupHelper:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function OrderDayPopupHelper:_UpdateState()
  self.m_canPopDayWindow = true
end

function OrderDayPopupHelper:NeedCheckPopup()
  return self.m_canPopDayWindow
end

function OrderDayPopupHelper:CheckPopup()
  if self.m_canPopDayWindow then
    self.m_canPopDayWindow = false
    return UIPrefabConfigName.OrderDayWindow
  end
end
