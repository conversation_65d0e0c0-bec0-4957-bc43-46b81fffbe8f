MainOrderDataModel = {}
MainOrderDataModel.__index = MainOrderDataModel

function MainOrderDataModel.Create()
  local tb = setmetatable({}, MainOrderDataModel)
  tb:Init()
  return tb
end

function MainOrderDataModel:Init()
end

function MainOrderDataModel:LoadFileConfig()
  local arrCleanOrderConfig = require("Data.Config.CleanOrderConfig")
  self.m_mapCleanOrderConfig = {}
  for _, config in ipairs(arrCleanOrderConfig) do
    RewardApi.CryptRewards(config.Reward)
    if self.m_mapCleanOrderConfig[config.ItemId] then
      Log.Error("OrderRandomConfig 中有重复的棋子：" .. (config.ItemId or "nil"))
    else
      self.m_mapCleanOrderConfig[config.ItemId] = config
    end
  end
end

function MainOrderDataModel:LoadChapterConfig(chapterId)
  self.m_arrGroupConfig = {}
  self.m_arrOrderConfig = {}
  self.m_mapOrderConfig = {}
  self.m_mapRandomDishes = {}
  if chapterId > GM.ChapterDataModel:GetChapterCount() then
    return
  end
  local orderGroupConfigs = require("Data.Config.OrderGroupConfig_" .. chapterId)
  local groupCount = 0
  for _, groupConfig in pairs(orderGroupConfigs) do
    groupCount = groupCount + 1
    Log.Assert(#groupConfig.Rewards <= 6, "订单组奖励最多6种")
    RewardApi.CryptRewards(groupConfig.Rewards, true)
    self.m_arrGroupConfig[groupConfig.GroupId] = groupConfig
  end
  Log.Assert(#self.m_arrGroupConfig == groupCount, "订单组配置错误！")
  local itemDataModel = GM.ItemDataModel
  local isLastChapter = chapterId == GM.ChapterDataModel:GetChapterCount()
  local orderConfigs = require("Data.Config.OrderFixedConfig_" .. chapterId)
  for _, orderConfig in ipairs(orderConfigs) do
    local groupId = orderConfig.GroupId or 0
    local arrOrders = self.m_arrOrderConfig[groupId]
    if not arrOrders then
      arrOrders = {}
      self.m_arrOrderConfig[groupId] = arrOrders
      Log.Assert(groupId == 0 or self.m_arrGroupConfig[groupId] ~= nil, "配置了不存在的订单组：" .. tostring(groupId))
    end
    arrOrders[#arrOrders + 1] = orderConfig
    self.m_mapOrderConfig[orderConfig.Id] = orderConfig
    if isLastChapter then
      if orderConfig.Requirement_1 then
        self:_CheckRamdomDish(itemDataModel, orderConfig.Requirement_1.Type)
      end
      if orderConfig.Requirement_2 then
        self:_CheckRamdomDish(itemDataModel, orderConfig.Requirement_2.Type)
      end
    end
  end
  if isLastChapter and next(self.m_mapRandomDishes) == nil then
    Log.Error("没有配置随机菜品！")
  end
end

function MainOrderDataModel:_CheckRamdomDish(itemDataModel, itemType)
  local isDish, inRandom = itemDataModel:IsDishes(itemType)
  if isDish and inRandom then
    self.m_mapRandomDishes[itemType] = true
  end
end

function MainOrderDataModel:GetOrderConfig(orderId)
  local orderConfig = self.m_mapOrderConfig[orderId]
  Log.Assert(orderConfig, "orderConfig 为空，orderId:" .. tostring(orderId))
  return orderConfig or {}
end

function MainOrderDataModel:GetGroupedOrderConfig(groupId)
  local arrOrderConfigs = self.m_arrOrderConfig[groupId]
  return arrOrderConfigs
end

function MainOrderDataModel:GetGroupConfig(groupId)
  local groupConfig = self.m_arrGroupConfig[groupId]
  return groupConfig
end

function MainOrderDataModel:GetGroupCount()
  return #self.m_arrGroupConfig
end

function MainOrderDataModel:GetRandomDishMap()
  return self.m_mapRandomDishes
end

function MainOrderDataModel:GetCleanOrderConfigMap()
  return self.m_mapCleanOrderConfig
end

function MainOrderDataModel.GetOrderDayByOrderGroup(orderChapterId, orderGroupId)
  if not (orderChapterId and orderGroupId) or orderChapterId <= 0 then
    return 1
  end
  if orderChapterId > GM.ChapterDataModel:GetChapterCount() then
    orderChapterId = GM.ChapterDataModel:GetChapterCount()
    orderGroupId = 99
  end
  if orderGroupId <= 0 then
    orderGroupId = 1
  end
  local orderGroupConfigs = require("Data.Config.OrderGroupConfig_" .. orderChapterId)
  if not orderGroupConfigs then
    return 1
  end
  local mapConfigs = {}
  for _, v in ipairs(orderGroupConfigs) do
    mapConfigs[v.GroupId] = v.Day
  end
  for i = orderGroupId, 1, -1 do
    if mapConfigs[i] ~= nil then
      return mapConfigs[i]
    end
  end
  return 1
end

function MainOrderDataModel.GetMaxOrderDayOfChapter(chapterId)
  local minDay, maxDay = MainOrderDataModel.GetMinMaxOrderDayOfChapter(chapterId)
  return maxDay
end

MainOrderDataModel.ChapterMinMaxDay = {}

function MainOrderDataModel.GetMinMaxOrderDayOfChapter(chapterId)
  if MainOrderDataModel.ChapterMinMaxDay[chapterId] ~= nil then
    local minDay = MainOrderDataModel.ChapterMinMaxDay[chapterId].minDay
    local maxDay = MainOrderDataModel.ChapterMinMaxDay[chapterId].maxDay
    if minDay and maxDay then
      return minDay, maxDay
    end
  end
  local orderGroupConfigs = require("Data.Config.OrderGroupConfig_" .. chapterId)
  local minDay, maxDay
  for _, v in ipairs(orderGroupConfigs or {}) do
    if minDay == nil then
      minDay = v.Day
    else
      minDay = math.min(minDay, v.Day)
    end
    if maxDay == nil then
      maxDay = v.Day
    else
      maxDay = math.max(maxDay, v.Day)
    end
  end
  MainOrderDataModel.ChapterMinMaxDay[chapterId] = {minDay = minDay, maxDay = maxDay}
  return minDay, maxDay
end
