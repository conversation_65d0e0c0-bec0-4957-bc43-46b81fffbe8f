return {
  {
    Id = "160010",
    GroupId = 1,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "160020",
    GroupId = 1,
    ChapterId = 16,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6rice_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160030",
    GroupId = 1,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e1fs_3",
      Count = 1
    }
  },
  {
    Id = "160040",
    GroupId = 1,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    }
  },
  {
    Id = "160050",
    GroupId = 1,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160060",
    GroupId = 1,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "160070",
    GroupId = 1,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "160080",
    GroupId = 2,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "160090",
    GroupId = 2,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e6taji_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160100",
    GroupId = 2,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "160110",
    GroupId = 2,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_friedve_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "160120",
    GroupId = 2,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_16e5fd_44",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160130",
    GroupId = 2,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4assort_12",
      Count = 1
    }
  },
  {
    Id = "160140",
    GroupId = 2,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "160150",
    GroupId = 3,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160160",
    GroupId = 3,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e6sala_15",
      Count = 1
    }
  },
  {
    Id = "160170",
    GroupId = 3,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_9",
      Count = 1
    }
  },
  {
    Id = "160180",
    GroupId = 3,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "160190",
    GroupId = 3,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e1cockt_32",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160200",
    GroupId = 3,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_4",
      Count = 1
    }
  },
  {
    Id = "160210",
    GroupId = 3,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "160220",
    GroupId = 4,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "160230",
    GroupId = 4,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1icytre_1",
      Count = 1
    }
  },
  {
    Id = "160240",
    GroupId = 4,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e1cockt_31",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160250",
    GroupId = 4,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    }
  },
  {
    Id = "160260",
    GroupId = 4,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_14e4curry_24",
      Count = 1
    }
  },
  {
    Id = "160270",
    GroupId = 4,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e4sala_11",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160280",
    GroupId = 4,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "160290",
    GroupId = 5,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "160300",
    GroupId = 5,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1dst_21",
      Count = 1
    }
  },
  {
    Id = "160310",
    GroupId = 5,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_14e6nibble_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e4sala_18",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160320",
    GroupId = 5,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_14e5semi_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5fd_27",
      Count = 1
    }
  },
  {
    Id = "160330",
    GroupId = 5,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e6taji_8",
      Count = 1
    }
  },
  {
    Id = "160340",
    GroupId = 5,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160350",
    GroupId = 5,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_6e2flb_5",
      Count = 1
    }
  },
  {
    Id = "160360",
    GroupId = 6,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_16e5fd_44",
      Count = 1
    }
  },
  {
    Id = "160370",
    GroupId = 6,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "160380",
    GroupId = 6,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e1chopfv_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160390",
    GroupId = 6,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "160400",
    GroupId = 6,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "160410",
    GroupId = 6,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160420",
    GroupId = 6,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_1_2", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "160430",
    GroupId = 7,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "160440",
    GroupId = 7,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "160450",
    GroupId = 7,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_12e1nutt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e1sala_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160460",
    GroupId = 7,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e5mt_12",
      Count = 1
    }
  },
  {
    Id = "160470",
    GroupId = 7,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "160480",
    GroupId = 7,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_14e3icytre_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160490",
    GroupId = 7,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    }
  },
  {
    Id = "160500",
    GroupId = 8,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "160510",
    GroupId = 8,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5maca_2",
      Count = 1
    }
  },
  {
    Id = "160520",
    GroupId = 8,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e4sala_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160530",
    GroupId = 8,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "160540",
    GroupId = 8,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160550",
    GroupId = 8,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "160560",
    GroupId = 8,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e1semi_25",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "160570",
    GroupId = 9,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e5fd_44",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "160580",
    GroupId = 9,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "160590",
    GroupId = 9,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e1cockt_33",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160600",
    GroupId = 9,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "160610",
    GroupId = 9,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "160620",
    GroupId = 9,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e6taji_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160630",
    GroupId = 9,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "160640",
    GroupId = 10,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    }
  },
  {
    Id = "160650",
    GroupId = 10,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_10",
      Count = 1
    }
  },
  {
    Id = "160660",
    GroupId = 10,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_16e5sala_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160670",
    GroupId = 10,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_1",
      Count = 1
    }
  },
  {
    Id = "160680",
    GroupId = 10,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160690",
    GroupId = 10,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e4friedmt_19",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    }
  },
  {
    Id = "160700",
    GroupId = 10,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "160710",
    GroupId = 11,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e6rice_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "160720",
    GroupId = 11,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_11e4tato_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160730",
    GroupId = 11,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e1dst_26",
      Count = 1
    }
  },
  {
    Id = "160740",
    GroupId = 11,
    ChapterId = 16,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_13e5mt_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160750",
    GroupId = 11,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e2mt_16",
      Count = 1
    }
  },
  {
    Id = "160760",
    GroupId = 11,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "160770",
    GroupId = 11,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    }
  },
  {
    Id = "160780",
    GroupId = 12,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "160790",
    GroupId = 12,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160800",
    GroupId = 12,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "160810",
    GroupId = 12,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "160820",
    GroupId = 12,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e6bec_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6sala_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160830",
    GroupId = 12,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "160840",
    GroupId = 12,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_16e5fd_44",
      Count = 1
    }
  },
  {
    Id = "160850",
    GroupId = 13,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "160860",
    GroupId = 13,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e6taji_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160870",
    GroupId = 13,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "160880",
    GroupId = 13,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_5",
      Count = 1
    }
  },
  {
    Id = "160890",
    GroupId = 13,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e3semi_20",
      Count = 1
    }
  },
  {
    Id = "160900",
    GroupId = 13,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_15e5maca_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160910",
    GroupId = 13,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "160920",
    GroupId = 14,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_11e6nibble_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "160930",
    GroupId = 14,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e4appe_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160940",
    GroupId = 14,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e4sala_18",
      Count = 1
    }
  },
  {
    Id = "160950",
    GroupId = 14,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "160960",
    GroupId = 14,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e1sala_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "160970",
    GroupId = 14,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    }
  },
  {
    Id = "160980",
    GroupId = 14,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "160990",
    GroupId = 15,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "161000",
    GroupId = 15,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161010",
    GroupId = 15,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161020",
    GroupId = 15,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "161030",
    GroupId = 15,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_5",
      Count = 1
    }
  },
  {
    Id = "161040",
    GroupId = 15,
    ChapterId = 16,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161050",
    GroupId = 15,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "161060",
    GroupId = 16,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161070",
    GroupId = 16,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    }
  },
  {
    Id = "161080",
    GroupId = 16,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e2veg_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161090",
    GroupId = 16,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "161100",
    GroupId = 16,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "161110",
    GroupId = 16,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e4friedmt_23",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161120",
    GroupId = 16,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "161130",
    GroupId = 17,
    ChapterId = 16,
    Requirement_1 = {Type = "ds_sal_1", Count = 1}
  },
  {
    Id = "161140",
    GroupId = 17,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161150",
    GroupId = 17,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "161160",
    GroupId = 17,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    }
  },
  {
    Id = "161170",
    GroupId = 17,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e1sala_19",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161180",
    GroupId = 17,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "161190",
    GroupId = 17,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_12e1nutt_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161200",
    GroupId = 18,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e6rice_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161210",
    GroupId = 18,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_3",
      Count = 1
    }
  },
  {
    Id = "161220",
    GroupId = 18,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e6soup_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161230",
    GroupId = 18,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "161240",
    GroupId = 18,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "161250",
    GroupId = 18,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e1cockt_32",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161260",
    GroupId = 18,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "161270",
    GroupId = 19,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    }
  },
  {
    Id = "161280",
    GroupId = 19,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "161290",
    GroupId = 19,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161300",
    GroupId = 19,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161310",
    GroupId = 19,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_14e6soup_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161320",
    GroupId = 19,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_friedve_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "161330",
    GroupId = 19,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "161340",
    GroupId = 20,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e5fd_44",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    }
  },
  {
    Id = "161350",
    GroupId = 20,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e6taji_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161360",
    GroupId = 20,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "161370",
    GroupId = 20,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6nibble_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161380",
    GroupId = 20,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_11e4tato_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161390",
    GroupId = 20,
    ChapterId = 16,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "161400",
    GroupId = 20,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_8",
      Count = 1
    }
  },
  {
    Id = "161410",
    GroupId = 21,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "161420",
    GroupId = 21,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e6taji_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161430",
    GroupId = 21,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "161440",
    GroupId = 21,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_5",
      Count = 1
    }
  },
  {
    Id = "161450",
    GroupId = 21,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e4friedmt_25",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161460",
    GroupId = 21,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "161470",
    GroupId = 21,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    }
  },
  {
    Id = "161480",
    GroupId = 22,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "161490",
    GroupId = 22,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_16e1sala_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161500",
    GroupId = 22,
    ChapterId = 16,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "161510",
    GroupId = 22,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "161520",
    GroupId = 22,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161530",
    GroupId = 22,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_14e4sf_30",
      Count = 1
    }
  },
  {
    Id = "161540",
    GroupId = 22,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e6nibble_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e5semi_16",
      Count = 1
    }
  },
  {
    Id = "161550",
    GroupId = 23,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "161560",
    GroupId = 23,
    ChapterId = 16,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_16e6taji_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161570",
    GroupId = 23,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161580",
    GroupId = 23,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5maca_4",
      Count = 1
    }
  },
  {
    Id = "161590",
    GroupId = 23,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161600",
    GroupId = 23,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "161610",
    GroupId = 23,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    }
  },
  {
    Id = "161620",
    GroupId = 24,
    ChapterId = 16,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "161630",
    GroupId = 24,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e1sala_20",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161640",
    GroupId = 24,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "161650",
    GroupId = 24,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161660",
    GroupId = 24,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_16e2sf_32",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161670",
    GroupId = 24,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e4friedmt_19",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "161680",
    GroupId = 24,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "161690",
    GroupId = 25,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_2",
      Count = 1
    }
  },
  {
    Id = "161700",
    GroupId = 25,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e4friedmt_24",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161710",
    GroupId = 25,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_16e5fd_44",
      Count = 1
    }
  },
  {
    Id = "161720",
    GroupId = 25,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_sal_1", Count = 1}
  },
  {
    Id = "161730",
    GroupId = 25,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6nibble_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161740",
    GroupId = 25,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "161750",
    GroupId = 25,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6porr_1",
      Count = 1
    }
  },
  {
    Id = "161760",
    GroupId = 26,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e4friedmt_19",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161770",
    GroupId = 26,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e1cockt_34",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e5saus_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161780",
    GroupId = 26,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_1",
      Count = 1
    }
  },
  {
    Id = "161790",
    GroupId = 26,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_10e6rice_8",
      Count = 1
    }
  },
  {
    Id = "161800",
    GroupId = 26,
    ChapterId = 16,
    Requirement_1 = {
      Type = "it_a16_1_6_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e4sala_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161810",
    GroupId = 26,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5fd_37",
      Count = 1
    }
  },
  {
    Id = "161820",
    GroupId = 26,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "161830",
    GroupId = 27,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "161840",
    GroupId = 27,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161850",
    GroupId = 27,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    }
  },
  {
    Id = "161860",
    GroupId = 27,
    ChapterId = 16,
    Requirement_1 = {
      Type = "it_a16_1_6_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_sal_1", Count = 1}
  },
  {
    Id = "161870",
    GroupId = 27,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e4sf_31",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161880",
    GroupId = 27,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161890",
    GroupId = 27,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6rice_13",
      Count = 1
    }
  },
  {
    Id = "161900",
    GroupId = 28,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "161910",
    GroupId = 28,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e1cockt_30",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161920",
    GroupId = 28,
    ChapterId = 16,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_5",
      Count = 1
    }
  },
  {
    Id = "161930",
    GroupId = 28,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "161940",
    GroupId = 28,
    ChapterId = 16,
    Requirement_1 = {
      Type = "it_a16_1_6_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161950",
    GroupId = 28,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e5mt_12",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161960",
    GroupId = 28,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "161970",
    GroupId = 29,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e5fd_27",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "161980",
    GroupId = 29,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e6taji_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "161990",
    GroupId = 29,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    }
  },
  {
    Id = "162000",
    GroupId = 29,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_13e6stewmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    }
  },
  {
    Id = "162010",
    GroupId = 29,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_16e2sf_33",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "162020",
    GroupId = 29,
    ChapterId = 16,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "162030",
    GroupId = 29,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "162040",
    GroupId = 30,
    ChapterId = 16,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "it_a16_1_6_1",
      Count = 1
    }
  },
  {
    Id = "162050",
    GroupId = 30,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_16e6taji_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "162060",
    GroupId = 30,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    }
  },
  {
    Id = "162070",
    GroupId = 30,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_11e4tato_5",
      Count = 1
    }
  },
  {
    Id = "162080",
    GroupId = 30,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6assort_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "162090",
    GroupId = 30,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "162100",
    GroupId = 30,
    ChapterId = 16,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  }
}
