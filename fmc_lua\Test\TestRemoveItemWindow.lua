TestRemoveItemWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestRemoveItemWindow.__index = TestRemoveItemWindow

function TestRemoveItemWindow:Init()
  self.m_bMultiTouchEnabled = Input.multiTouchEnabled
  Input.multiTouchEnabled = false
  local boardModel = BoardModelHelper.GetActiveModel()
  self.m_boardModel = boardModel
  self.m_cellMatrix = boardModel:CreateMatrix()
  self.m_cellSize = BaseBoardModel.TileSize
  UIUtil.SetSizeDelta(self.m_boardRectTrans, BaseBoardModel.TileSize * boardModel:GetHorizontalTiles(), BaseBoardModel.TileSize * boardModel:GetVerticalTiles())
  self.m_contentLayoutGroup.cellSize = Vector2(BaseBoardModel.TileSize, BaseBoardModel.TileSize)
  local itemModel, cellObj, cell, position
  for y = 1, boardModel:GetVerticalTiles() do
    for x = 1, boardModel:GetHorizontalTiles() do
      position = boardModel:CreatePosition(x, y)
      cellObj = GameObject.Instantiate(self.m_originGo, self.m_contentLayoutGroup.gameObject.transform)
      cellObj:SetActive(true)
      cell = cellObj:GetLuaTable()
      self.m_cellMatrix:SetValueOnPosition(position, cell)
      itemModel = boardModel:GetItem(position)
      if itemModel ~= nil then
        local itemCobweb = itemModel:GetComponent(ItemCobweb)
        if itemCobweb ~= nil then
          cell:Init(itemCobweb:GetInnerItemCode(), true)
        else
          cell:Init(itemModel:GetType())
        end
      else
        cell:Init()
      end
    end
  end
  
  function self.m_eventTrigger.OnLuaPointerDown(eventData)
    self:_OnPointerDown(eventData)
  end
  
  function self.m_eventTrigger.OnLuaDrag(eventData)
    self:_OnDrag(eventData)
  end
  
  self.m_bInRemoveMode = true
  self:_UpdateSwitchText()
end

function TestRemoveItemWindow:OnDestroy()
  Input.multiTouchEnabled = self.m_bMultiTouchEnabled
end

function TestRemoveItemWindow:_OnPointerDown(eventData)
  local boardPosition = self:_GetBoardPosition(eventData.position)
  self.m_lastTouchPos = boardPosition
  self:_SetCell(self.m_lastTouchPos)
end

function TestRemoveItemWindow:_OnDrag(eventData)
  local boardPosition = self:_GetBoardPosition(eventData.position)
  if boardPosition ~= self.m_lastTouchPos then
    self.m_lastTouchPos = boardPosition
    self:_SetCell(self.m_lastTouchPos)
  end
end

function TestRemoveItemWindow:_SetCell(boardPosition)
  if boardPosition:IsValid() then
    local cell = self.m_cellMatrix:GetValueOnPosition(boardPosition)
    cell:UpdateRemoveState(self.m_bInRemoveMode)
  end
end

function TestRemoveItemWindow:_GetBoardPosition(screenPosition)
  local worldPosition = PositionUtil.UICameraScreen2World(screenPosition)
  local localPosition = self.m_anchorRectTrans:InverseTransformPoint(worldPosition)
  return self.m_boardModel:CreatePositionFromLocalPosition(localPosition.x, localPosition.y)
end

function TestRemoveItemWindow:OnConfirmBtnClicked()
  local testModel = GM.TestModel
  local cell
  local boardModel = self.m_boardModel
  local position
  for y = 1, boardModel:GetVerticalTiles() do
    for x = 1, boardModel:GetHorizontalTiles() do
      position = boardModel:CreatePosition(x, y)
      cell = self.m_cellMatrix:GetValueOnPosition(position)
      if cell ~= nil and cell:IsRemoved() then
        testModel:RemoveItem(position)
      end
    end
  end
  GM.UIManager:ShowPrompt("移除成功")
  self:Close()
end

function TestRemoveItemWindow:OnReverseBtnClicked()
  local cell
  local boardModel = self.m_boardModel
  local position
  for y = 1, boardModel:GetVerticalTiles() do
    for x = 1, boardModel:GetHorizontalTiles() do
      position = boardModel:CreatePosition(x, y)
      cell = self.m_cellMatrix:GetValueOnPosition(position)
      if cell ~= nil then
        cell:UpdateRemoveState(not cell:IsRemoved())
      end
    end
  end
end

function TestRemoveItemWindow:OnSwitchBtnClicked()
  self.m_bInRemoveMode = not self.m_bInRemoveMode
  self:_UpdateSwitchText()
end

function TestRemoveItemWindow:_UpdateSwitchText()
  self.m_switchText.text = self.m_bInRemoveMode and "Mode:Delete" or "Mode:Restore"
end

TestRemoveItemCell = {}
TestRemoveItemCell.__index = TestRemoveItemCell

function TestRemoveItemCell:Init(type, cobweb)
  self.m_cobwebGo:SetActive(cobweb)
  if type == nil then
    self.m_iconImg.gameObject:SetActive(false)
  else
    SpriteUtil.SetImage(self.m_iconImg, GM.ItemDataModel:GetSpriteName(type), true)
    self.m_type = type
  end
  self.m_bSelected = false
end

function TestRemoveItemCell:UpdateRemoveState(removed)
  if not self:CanRemove() then
    return
  end
  if self.m_bSelected ~= removed then
    self.m_bSelected = removed
    self:_UpdateIconImage()
  end
end

function TestRemoveItemCell:CanRemove()
  return self.m_type ~= nil
end

function TestRemoveItemCell:IsRemoved()
  return self.m_bSelected
end

function TestRemoveItemCell:_UpdateIconImage()
  self.m_canvasGroup.alpha = self.m_bSelected and 0.2 or 1
end
