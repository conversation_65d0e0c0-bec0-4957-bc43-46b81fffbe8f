SpreeActivityShopWindow = setmetatable({}, BaseWindow)
SpreeActivityShopWindow.__index = SpreeActivityShopWindow

function SpreeActivityShopWindow:Init(activityType)
  self.m_activityType = activityType
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_shopModel = self.m_model:GetShopModel()
  self.m_shopModel:RefreshBundleData()
  self:_AdjustSize()
  self.m_bundleEventTrigger = self.m_bundleSectionGo:GetComponent(typeof(CS.LuaEventTrigger))
  
  function self.m_bundleEventTrigger.OnLuaBeginDrag(eventData)
    self:_OnBeginDragBundle(eventData)
  end
  
  self.m_bundleContainerWidth = self.m_containerBundlePrefab.transform.sizeDelta.x
  self.m_bundleContainers = {}
  local activeBundles = self.m_shopModel:GetActiveBundleData()
  self.m_bundleCount = #activeBundles
  self.m_currentBundleIndex = 1
  if self.m_bundleCount ~= 0 then
    for _, bundleData in ipairs(activeBundles) do
      local containerGo = Object.Instantiate(self.m_containerBundlePrefab, self.m_bundleContent)
      local container = containerGo:GetLuaTable()
      container:Init(activityType, bundleData)
      table.insert(self.m_bundleContainers, container)
    end
    if self.m_bundleCount == 1 then
      self.m_dots.gameObject:SetActive(false)
    else
      self.m_dots.gameObject:SetActive(true)
      for i = 1, self.m_bundleCount - 1 do
        Object.Instantiate(self.m_whiteDotPrefab, self.m_dots)
      end
      self:_SwitchToBundle(self.m_bundleCount, false)
      DOVirtual.DelayedCall(1, function()
        self:_SwitchToBundle(1, true)
      end)
      self.m_switchBundleTween = DOTween.Sequence()
      self.m_switchBundleTween:InsertCallback(5, function()
        self:_AutoSwitchBundle()
      end)
      self.m_switchBundleTween:SetLoops(-1)
    end
  else
    self.m_bundleSectionGo:SetActive(false)
  end
  self.m_shopContainerHotSales:Init(activityType)
  EventDispatcher.AddListener(EEventType.PropertyAcquired, self, self._OnPropertyChanged)
  EventDispatcher.AddListener(EEventType.PropertyConsumed, self, self._OnPropertyChanged)
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self._OnShopRefreshed)
  AddHandlerAndRecordMap(self.m_model.event, SpreeActivityEventType.StateChanged, {
    obj = self,
    method = self.Close
  })
end

function SpreeActivityShopWindow:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  RemoveAllHandlers(self.m_model.event, self)
  self.m_bundleEventTrigger.OnLuaBeginDrag = nil
end

function SpreeActivityShopWindow:_OnBeginDragBundle(eventData)
  if self.m_switchBundleTween ~= nil then
    self.m_switchBundleTween:Restart()
  end
  if eventData.delta.x < 0 then
    if self.m_currentBundleIndex < self.m_bundleCount then
      self:_SwitchToBundle(self.m_currentBundleIndex + 1, true)
    end
  elseif self.m_currentBundleIndex > 1 then
    self:_SwitchToBundle(self.m_currentBundleIndex - 1, true)
  end
end

function SpreeActivityShopWindow:_OnPropertyChanged()
  self.m_gemHudButton:SyncToModelValue()
end

function SpreeActivityShopWindow:_OnShopRefreshed(message)
  local activityDefinition = SpreeActivityDefinition[self.m_activityType]
  if message.shopType ~= activityDefinition.BundleShopType then
    return
  end
  local lastBundleCount = self.m_bundleCount
  local activeBundles = self.m_shopModel:GetActiveBundleData()
  self.m_bundleCount = #activeBundles
  if self.m_bundleCount == 0 then
    self.m_bundleSectionGo:SetActive(false)
  else
    self:_SwitchToBundle(1, false)
    local i = 1
    while i <= #self.m_bundleContainers do
      if i <= self.m_bundleCount then
        self.m_bundleContainers[i]:SetData(activeBundles[i])
        i = i + 1
      else
        Object.Destroy(self.m_bundleContainers[i].gameObject)
        table.remove(self.m_bundleContainers, i)
      end
    end
    for i = lastBundleCount, self.m_bundleCount + 1, -1 do
      Object.Destroy(self.m_dots:GetChild(i - 1).gameObject)
    end
    if self.m_bundleCount == 1 then
      self.m_dots.gameObject:SetActive(false)
      self.m_switchBundleTween:Kill()
      self.m_switchBundleTween = nil
    end
  end
end

function SpreeActivityShopWindow:_AdjustSize()
  local adjustSize = ScreenFitter.GetScreenAdjustSize()
  local scale = adjustSize.x / ScreenFitter.GetStandardWidth()
  if ScreenFitter.NeedNotch() then
    local notchHeight = ScreenFitter.GetNotchHeight()
    local titlePosition = self.m_title.anchoredPosition
    titlePosition.y = titlePosition.y - notchHeight
    self.m_title.anchoredPosition = titlePosition
  end
  self.m_title.localScale = V3One * scale
  local contentHeight = adjustSize.y + self.m_title.anchoredPosition.y - self.m_title.sizeDelta.y * scale / 2
  local backgroundSizeDelta = self.m_background.sizeDelta
  backgroundSizeDelta.x = backgroundSizeDelta.x * scale
  backgroundSizeDelta.y = contentHeight - self.m_background.anchoredPosition.y
  self.m_background.sizeDelta = backgroundSizeDelta
  local itemContentScale = (scale + 1) / 2
  self.m_itemContent.localScale = V3One * itemContentScale
  local itemContentSizeDelta = self.m_itemContent.sizeDelta
  itemContentSizeDelta.y = contentHeight / itemContentScale
  self.m_itemContent.sizeDelta = itemContentSizeDelta
end

function SpreeActivityShopWindow:_AutoSwitchBundle()
  if self.m_currentBundleIndex < self.m_bundleCount then
    self:_SwitchToBundle(self.m_currentBundleIndex + 1, true)
  else
    self:_SwitchToBundle(1, true)
  end
end

function SpreeActivityShopWindow:_SwitchToBundle(index, withAnimation)
  self.m_currentBundleIndex = index
  self.m_redDot:SetSiblingIndex(index - 1)
  local targetPositionX = -(index - 1) * self.m_bundleContainerWidth
  if withAnimation then
    self.m_bundleContent:DOAnchorPosX(targetPositionX, 0.6)
  else
    local bundlePosition = self.m_bundleContent.anchoredPosition
    bundlePosition.x = targetPositionX
    self.m_bundleContent.anchoredPosition = bundlePosition
  end
end

SpreeShopCellItem = setmetatable({}, ShopCellItem)
SpreeShopCellItem.__index = SpreeShopCellItem
