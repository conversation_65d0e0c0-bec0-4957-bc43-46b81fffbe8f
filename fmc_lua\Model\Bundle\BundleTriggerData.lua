BundleTriggerData = {}
BundleTriggerData.__index = BundleTriggerData

function BundleTriggerData.Create(triggerData, dataGroup)
  local tri = setmetatable(triggerData, BundleTriggerData)
  tri:Init(dataGroup)
  return tri
end

function BundleTriggerData:Init(dataGroup)
  self.m_dataGroup = dataGroup
end

function BundleTriggerData:CanTrigger(eTriggerType, triggerArg, bundleModel)
  if eTriggerType == self.trigger then
    if eTriggerType == EBundleTriggerType.LackGem then
      if IsNumber(triggerArg) and bundleModel and bundleModel.GetCurBundleGemByDataGroup and triggerArg <= bundleModel:GetCurBundleGemByDataGroup(self.m_dataGroup) then
        return true
      end
      return false
    elseif eTriggerType == EBundleTriggerType.LackEnergy then
      return GM.EnergyModel:GetEnergy() < 50
    elseif eTriggerType == EBundleTriggerType.PdCDNumber then
      local configCount = self.m_dataGroup:GetGeneralConfig("pd_cd_number", EConfigParamType.Int) or 4
      return configCount <= (triggerArg or 0)
    end
    return true
  end
  return false
end

function BundleTriggerData:CanPopup(eTriggerType, hasTriggerNum)
  if eTriggerType == self.trigger and (self.dailyShowNum == nil or hasTriggerNum < self.dailyShowNum) then
    return true
  end
  return false
end
