ItemSpreadView = setmetatable({}, BaseItemViewComponent)
ItemSpreadView.__index = ItemSpreadView

function ItemSpreadView:Awake()
  self:_AddListeners()
end

function ItemSpreadView:Init(itemSpreadModel)
  self.m_model = itemSpreadModel
  AddHandlerAndRecordMap(self.m_model.event, ItemSpreadEventType.StateChanged, {
    obj = self,
    method = self._OnStateChanged
  })
  AddHandlerAndRecordMap(self.m_model.event, ItemSpreadEventType.UpdateBoostHint, {
    obj = self,
    method = self._UpdateHintEffect
  })
  AddHandlerAndRecordMap(self.m_model.event, ItemSpreadEventType.AddItemBoost, {
    obj = self,
    method = self._OnAddItemBoost
  })
  AddHandlerAndRecordMap(self.m_model.event, ItemSpreadEventType.PlaySkipTimeAnim, {
    obj = self,
    method = self._OnPlaySkipTimeAnim
  })
  AddHandlerAndRecordMap(self.m_model.event, ItemSpreadEventType.UpdateFire, {
    obj = self,
    method = self._UpdateFlambeFire
  })
  AddHandlerAndRecordMap(self.m_model.event, ItemSpreadEventType.HideFire, {
    obj = self,
    method = self._HideFire
  })
  if self.gameObject.activeInHierarchy then
    self:_AddListeners()
  end
  self:_UpdateView(false)
  self:_UpdateFlambeFire()
end

function ItemSpreadView:_AddListeners()
  EventDispatcher.AddListener(EEventType.ShowItemTestInfoChanged, self, self._UpdateView, true)
  EventDispatcher.AddListener(EEventType.EnergyBoostModeChanged, self, self._UpdateHintEffect, true)
end

function ItemSpreadView:OnDestroy()
  RemoveAllHandlers(self.m_model.event, self)
  EventDispatcher.RemoveTarget(self)
end

function ItemSpreadView:SetItemView(itemView)
  BaseItemViewComponent.SetItemView(self, itemView)
  self:_UpdateItemSprite()
end

function ItemSpreadView:_UpdateItemSprite()
  local state = self.m_model:GetState()
  local itemType = self.m_model:GetItemModel():GetType()
  local spriteName = GM.ItemDataModel:GetSpriteName(itemType) .. "_open"
  if state == ItemSpreadState.Opened and ImageFileConfigName.HasConfig(spriteName) then
    SpriteUtil.SetSpriteRenderer(self.m_itemView:GetSpriteRenderer(), spriteName)
  end
  if self.m_model:ShowCountDown() then
    self.m_itemView:ChangeSpriteMaterial2Gray()
  else
    self.m_itemView:RestoreSpriteMaterial()
  end
end

function ItemSpreadView:_OnStateChanged()
  if self.m_model.m_bSpeeding then
    return
  end
  if self.m_model:IsFlambeTime() ~= self.m_bIsFlamebTime then
    return
  end
  self:_UpdateView(true)
  self:_UpdateItemSprite()
end

function ItemSpreadView:_UpdateFlambeFire()
  local isFlamebTime = self.m_model:IsFlambeTime()
  if self.m_fireGo.activeSelf and not isFlamebTime then
    self.m_fireGo:SetActive(false)
  elseif not self.m_fireGo.activeSelf and isFlamebTime then
    self.m_fireGo:SetActive(true)
    local isLink = GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link
    self.m_linkFireGo:SetActive(isLink)
    self.m_modeFireGo:SetActive(not isLink)
    self.m_fireGo.transform.localScale = V3Zero
    self.m_fireGo.transform:DOScale(1, 0.2)
  end
  local oldValue = self.m_bIsFlamebTime
  if self.m_bIsFlamebTime ~= isFlamebTime then
    self.m_bIsFlamebTime = isFlamebTime
    if oldValue ~= nil then
      self:_OnStateChanged()
    end
  end
end

function ItemSpreadView:_HideFire()
  self.m_fireGo:SetActive(false)
end

function ItemSpreadView:_OnAddItemBoost()
  self:_UpdateView(false)
end

function ItemSpreadView:SetFlying(flying)
  self.m_flying = flying
  self:_UpdateCountdown(false)
end

function ItemSpreadView:_UpdateView(withAnimation)
  self:_UpdateHintEffect()
  self:_UpdateCountdown(withAnimation)
  self:_UpdateTestInventoryDisplay()
  self:_UpdateAddItemCount()
end

function ItemSpreadView:_UpdateAddItemCount()
  local addItemCount = self.m_model:GetAddItemCount()
  self.m_boostNumber.text = 999 < addItemCount and 999 or addItemCount
  self.m_boostGo:SetActive(0 < addItemCount)
end

function ItemSpreadView:_UpdateHintEffect()
  if self.m_model:GetStorageRestNumber() == 0 or self.m_model:GetState() ~= ItemSpreadState.Opened then
    if self.m_hintEffectGo ~= nil then
      self.m_hintEffectGo:SetActive(false)
    end
    return
  end
  local effectPrefab = self:_GetHintEffectPrefab()
  if effectPrefab ~= self.m_hintEffectPrefab then
    if self.m_mapCachedEffectGo == nil then
      self.m_mapCachedEffectGo = {}
    end
    if self.m_hintEffectGo ~= nil then
      self.m_mapCachedEffectGo[self.m_hintEffectPrefab] = self.m_hintEffectGo
      UIUtil.SetActive(self.m_mapCachedEffectGo[self.m_hintEffectPrefab], false)
      self.m_hintEffectGo = nil
    end
    if self.m_mapCachedEffectGo[effectPrefab] ~= nil then
      self.m_hintEffectGo = self.m_mapCachedEffectGo[effectPrefab]
    else
      self.m_hintEffectGo = Object.Instantiate(effectPrefab, self.transform)
    end
    self.m_hintEffectPrefab = effectPrefab
  end
  UIUtil.SetActive(self.m_hintEffectGo, true)
end

function ItemSpreadView:_GetHintEffectPrefab()
  if self.m_model:CostEnergy() then
    if GM.EnergyBoostModel:CanEnergyBoost(self.m_model:GetItemModel():GetType()) then
      if GM.EnergyBoostModel:GetUserBoostType() == EEnergyBoostType.Quad then
        return self.m_energyBoostQuadEffectPrefab
      else
        return self.m_energyBoostEffectPrefab
      end
    else
      return self.m_energyEffectPrefab
    end
  else
    return self.m_energyFreeEffectPrefab
  end
end

function ItemSpreadView:_UpdateCountdown(withAnimation)
  if self.m_model:ShowCountDown() then
    self.m_countDown.gameObject:SetActive(not self.m_flying)
    if self.m_fillAmountTween ~= nil then
      self.m_fillAmountTween:Kill()
    end
    self.m_countDown:SetPercentage(self.m_model:GetTimerAmount())
    if withAnimation then
      self.m_countDown:GrowPercentageTo(self.m_model:GetNextTimerAmount())
    end
    if self.m_model:ShowAcceleratedCountDownAnim() then
      self.m_countDown:StartAnimation()
    else
      self.m_countDown:StopAnimation()
    end
    self:SetSpineAni(true)
  else
    if withAnimation and self.m_countDown.gameObject.activeInHierarchy then
      self:_OnRechargeFinish()
    end
    if self.m_countDown.gameObject.activeSelf then
      self.m_countDown.gameObject:SetActive(false)
      self.m_countDown:StopAnimation()
    end
    self:SetSpineAni(false)
  end
end

function ItemSpreadView:_UpdateTestInventoryDisplay()
  if not GM.UIManager:CanShowTestUI() then
    self.m_testRootGo:SetActive(false)
    return
  end
  self.m_testRootGo:SetActive(true)
  self.m_testRechargeCountText.text = self.m_model:IsFull() and "" or self.m_model:GetRechargeCount()
  self.m_testRechargeTimeText.text = self.m_model:IsFull() and "" or self.m_model:GetRechargeTime()
  self.m_testStorageRestNumberText.text = self.m_model:GetStorageRestNumber() - self.m_model:GetAddItemCount()
  self.m_testSpreadCount.text = self.m_model:GetSpreadCount()
end

function ItemSpreadView:_OnRechargeFinish()
  if self.m_hintEffectGo ~= nil then
    self.m_hintEffectGo:SetActive(false)
  end
  Object.Instantiate(self.m_rechargeFinishEffectPrefab, self.transform)
  DOVirtual.DelayedCall(0.2, function()
    self.m_itemView:PlayTapAnimation(0.2)
    self:_UpdateHintEffect()
  end)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxMergeCd)
end

function ItemSpreadView:SetItemSpriteShow(isShow)
  if not self.m_itemView then
    return
  end
  local rawSprite = self.m_itemView:GetSpriteRenderer()
  if rawSprite and not rawSprite:IsNull() and rawSprite.gameObject.activeSelf ~= isShow then
    rawSprite.gameObject:SetActive(isShow)
  end
end

function ItemSpreadView:SetSpineAni(isShow)
  do return end
  if self.m_spine and self.m_spine.go then
    if self.m_spine.go.activeSelf ~= isShow then
      self.m_spine.go:SetActive(isShow)
      self:SetItemSpriteShow(not isShow)
    end
    return
  end
  if self.m_spine and self.m_spine.loading then
    self.m_spine.isShow = isShow
    return
  end
  self.m_spine = {isShow = isShow, loading = true}
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(prefab), self.transform, Vector3.zero, function(go)
    if go:IsNull() then
      self.m_spine = nil
      return
    end
    self.m_spine.loading = false
    self.m_spine.go = go
    self.m_spine.go:SetActive(self.m_spine.isShow)
    if self.m_itemView then
      self:SetItemSpriteShow(not self.m_spine.isShow)
    else
      DelayExecuteFuncInView(function()
        self:SetItemSpriteShow(not self.m_spine.isShow)
      end, 0.1, self)
    end
  end)
end

function ItemSpreadView:_OnPlaySkipTimeAnim()
  local trans = self.m_countDown.transform
  if not self.m_countDownOriginalPos then
    self.m_countDownOriginalPos = trans.localPosition
    self.m_countDownOriginalScale = trans.localScale
  end
  local sequence = DOTween.Sequence()
  sequence:Append(trans:DOLocalMove(V3Zero, 0.2))
  sequence:Join(trans:DOScale(1.8, 0.2))
  sequence:AppendCallback(function()
    GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.effect_shizhong_guang_3), self.transform, Vector3.zero, function(go)
    end)
  end)
  sequence:AppendInterval(1)
  if not self.m_model:ShowCountDown(true) then
    sequence:Append(trans:DOScale(2.7, 0.2):SetEase(Ease.InSine))
    sequence:Append(trans:DOScale(0, 0.2):SetEase(Ease.OutSine))
  end
  sequence:AppendCallback(function()
    self:_OnStateChanged()
  end)
  sequence:Append(trans:DOLocalMove(self.m_countDownOriginalPos, 0.2))
  sequence:Join(trans:DOScale(self.m_countDownOriginalScale, 0.2))
end
