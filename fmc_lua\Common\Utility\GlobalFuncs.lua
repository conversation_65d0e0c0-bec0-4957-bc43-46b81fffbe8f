function IsTable(o)
  return type(o) == "table"
end

function IsString(o)
  return type(o) == "string"
end

function IsNumber(o)
  return type(o) == "number"
end

function IsDecimal(o)
  return type(o) == "number" and o > math.floor(o)
end

function IsBool(o)
  return type(o) == "boolean"
end

function IsFunction(o)
  return type(o) == "function"
end

function IsUserData(o)
  return type(o) == "userdata"
end

function CSharpCallLuaMethod(scriptName, funcName, params)
  if not _ENV[scriptName] then
    return nil
  end
  if _ENV[scriptName][funcName] then
    local argv = {}
    if params then
      for i = 0, params.Length - 1 do
        argv[i + 1] = params[i]
      end
    end
    _ENV[scriptName][funcName](_ENV[scriptName], table.unpack(argv))
  end
end

function IsNil(tb)
  return tb == nil or tb == json.null
end

function DelayExecuteFunc(func, dt, target)
  Log.Assert(target == nil or target.gameObject == nil, "use DelayExecuteFuncInView when target is a view!")
  if not dt or dt == 0 then
    func(target)
    return
  end
  Scheduler.Schedule(func, target, nil, 1, dt)
end

function DelayExecuteFuncInView(func, dt, view, bLockEvent)
  Log.Assert(view and view.gameObject, "view should not be nil!")
  if not dt or dt == 0 then
    func()
    return
  end
  if bLockEvent then
    GM.UIManager:SetEventLock(true, view)
  end
  local traceback = debug.traceback()
  Scheduler.Schedule(function()
    if bLockEvent then
      GM.UIManager:SetEventLock(false, view)
    end
    if view and view.gameObject and not view.gameObject:IsNull() then
      func()
    elseif GameConfig.IsTestMode() then
      Log.Error("view is null! Unschedule before destroy.\n" .. traceback)
    end
  end, view, nil, 1, dt, function()
    if bLockEvent then
      GM.UIManager:SetEventLock(false)
    end
  end)
end

function Recycle(gameObject)
  if not GameObjectPool or GameObjectPool:IsNull() then
    gameObject:RemoveSelf()
  else
    GameObjectPool:Recycle(gameObject)
  end
end

function GetServerTimeForCS()
  return GM.GameModel:GetServerTime()
end

local XpCallErrorHandler = function(error)
  Debug.LogError("[xpcall] " .. (error or "nil") .. "\n" .. debug.traceback())
end

function SafeCall(func, handler, ...)
  if handler then
    local innerHandler = function(err)
      XpCallErrorHandler(err)
      handler(err)
    end
    return xpcall(func, innerHandler, ...)
  else
    return xpcall(func, XpCallErrorHandler, ...)
  end
end

function IsAutoRun()
  return GameConfig.IsTestMode() and GM.TestAutoRunModel and GM.TestAutoRunModel.autoRun
end
