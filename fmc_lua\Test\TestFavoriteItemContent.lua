TestFavoriteItemContent = {}
TestFavoriteItemContent.__index = TestFavoriteItemContent

function TestFavoriteItemContent.GetInstance()
  return TestFavoriteItemContent.m_instance
end

function TestFavoriteItemContent:Awake()
  self:UpdateContent()
  
  function self.m_eventTrigger.OnLuaPointerDown(eventData)
    self:_OnPointerDown(eventData)
  end
  
  function self.m_eventTrigger.OnLuaDrag(eventData)
    self:_OnDrag(eventData)
  end
  
  function self.m_eventTrigger.OnLuaPointerUp(eventData)
    self:_OnPointerUp(eventData)
  end
  
  EventDispatcher.AddListener(EEventType.FavoriteItemChanged, self, self.OnFavoriteItemChanged)
  if TestAllItemContent.GetInstance() ~= nil then
    TestAllItemContent.GetInstance():OnCloseButtonClicked()
  end
  TestFavoriteItemContent.m_instance = self
end

function TestFavoriteItemContent:Init()
  local posStr = PlayerPrefs.GetString(EPlayerPrefKey.FavoriteContentOpenPos, "")
  if posStr ~= "" then
    local arrPos = StringUtil.SplitToNum(posStr, ",")
    UIUtil.SetLocalPosition(self.gameObject.transform, arrPos[1], arrPos[2])
  end
end

function TestFavoriteItemContent:UpdateContent()
  if self.m_arrCells == nil then
    self.m_arrCells = {}
  end
  local arrItems = GM.TestModel:GetFavoriteItems()
  local cellObj, cell
  for i, type in ipairs(arrItems) do
    if self.m_arrCells[i] ~= nil then
      self.m_arrCells[i].gameObject:SetActive(true)
      if self.m_arrCells[i]:GetType() ~= type then
        self.m_arrCells[i]:Init(type)
      end
    else
      cellObj = GameObject.Instantiate(self.m_originGo, self.m_contentRectTrans)
      cellObj:SetActive(true)
      cell = cellObj:GetLuaTable()
      cell:Init(type)
      self.m_arrCells[i] = cell
    end
  end
  for i = #arrItems + 1, #self.m_arrCells do
    self.m_arrCells[i].gameObject:SetActive(false)
  end
end

function TestFavoriteItemContent:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  TestFavoriteItemContent.m_instance = nil
end

function TestFavoriteItemContent:OnFavoriteItemChanged()
  self:UpdateContent()
end

function TestFavoriteItemContent:_OnPointerDown(eventData)
  self.m_bDragging = false
  self.m_startPos = PositionUtil.UICameraScreen2World(eventData.position)
  self.m_localPos = self.gameObject.transform.localPosition
end

function TestFavoriteItemContent:_OnDrag(eventData)
  self.m_bDragging = true
  local pos = PositionUtil.UICameraScreen2World(eventData.position)
  self.gameObject.transform.localPosition = self.m_localPos + pos - self.m_startPos
end

function TestFavoriteItemContent:_OnPointerUp(eventData)
  local lastPos = self.gameObject.transform.localPosition
  PlayerPrefs.SetString(EPlayerPrefKey.FavoriteContentOpenPos, lastPos.x .. "," .. lastPos.y)
end

function TestFavoriteItemContent:OnCloseButtonClicked()
  self.gameObject:RemoveSelf()
  PlayerPrefs.SetInt(EPlayerPrefKey.TestFavoriteItemContent, 0)
end

function TestFavoriteItemContent:AddOrderItems()
  GM.TestModel:AddOrderItems()
end
