TabButton = {}
TabButton.__index = TabButton

function TabButton:Init(tabView)
  self.m_tabView = tabView
  self:SetTipVisible(false)
end

function TabButton:OnButtonClicked()
  self.m_tabView:OnTabButtonClicked(self.m_strKey)
end

function TabButton:GetTabKey()
  return self.m_strKey
end

function TabButton:SetHighlightActive(active)
  self.m_normalGo:SetActive(not active)
  self.m_highlightGo:SetActive(active)
end

function TabButton:SetTipVisible(tipVisible)
  if self.m_tipGo ~= nil then
    self.m_tipGo:SetActive(tipVisible)
  end
end
