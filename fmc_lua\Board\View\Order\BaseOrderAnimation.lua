BaseOrderAnimation = {}
BaseOrderAnimation.__index = BaseOrderAnimation

function BaseOrderAnimation:Init(name)
  self.m_name = name
  self.m_state = nil
  self.m_animation:Initialize(false)
  self.m_spine:Init()
end

function BaseOrderAnimation:SetTransparent(transparent)
  if transparent then
    UIUtil.SetAlpha(self.m_animation, 0)
  else
    UIUtil.SetAlpha(self.m_animation, 1)
  end
end

function BaseOrderAnimation:ClearTracks()
  self.m_animation.AnimationState:ClearTracks()
  self.m_animation.Skeleton:SetToSetupPose()
end

function BaseOrderAnimation:SetAnimation(shortName, backStates)
  if self.m_state == nil then
    self:_SetAnimation(shortName, false)
    self:_AddRepeatAnimation(shortName)
    return
  end
  for _, state in ipairs(backStates) do
    if self.m_state == state then
      self:_SetAnimation(shortName, false)
      self:_AddRepeatAnimation(shortName)
      return
    end
  end
  self:_SetAnimation(shortName, true)
end

function BaseOrderAnimation:_SetAnimation(shortName, isRepeat)
  UIUtil.SetAlpha(self.m_animation, 1)
  local animationName = self:_GetAnimationName(shortName, isRepeat)
  self.m_spine:SetAnimation(animationName, isRepeat)
end

function BaseOrderAnimation:_AddRepeatAnimation(shortName)
  local animationName = self:_GetAnimationName(shortName, true)
  self.m_spine:AddAnimation(animationName, true)
end

function BaseOrderAnimation:_GetAnimationName(shortName, isRepeat)
  local postfix = isRepeat and "_loop" or ""
  return self.m_name .. "_" .. shortName .. postfix
end
