AlbumPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true,
    [EPopupScene.Main] = true
  }
}, BasePopupHelper)
AlbumPopupHelper.__index = AlbumPopupHelper

function AlbumPopupHelper.Create()
  local helper = setmetatable({}, AlbumPopupHelper)
  helper:Init()
  return helper
end

function AlbumPopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(AlbumActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
  EventDispatcher.AddListener(EEventType.AlbumPopStateChanged, self, self._OnStateChanged)
end

function AlbumPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function AlbumPopupHelper:NeedCheckPopup()
  local albumModel = AlbumActivityModel.GetActiveModel()
  local bCheckPopup = albumModel ~= nil and albumModel:HasExpiredJokerCard()
  return BasePopupHelper.NeedCheckPopup(self) or bCheckPopup
end

function AlbumPopupHelper:CheckPopup()
  for activityType, activityDefinition in pairs(AlbumActivityDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Started then
      if not model:HasWindowOpenedOnce(ActivityState.Started) then
        return activityDefinition.StartWindowPrefabName, {activityType}
      elseif model:CanPopNewJokerWindow() then
        local windowName = model:GetNewJokerWindowName()
        model:ClearNewJokerState()
        return windowName, {activityType}
      elseif model:HasExpiredJokerCard() then
        return UIPrefabConfigName.AlbumRemindUseJokerWindow, {activityType}
      end
    elseif state == ActivityState.Ended and model:HasWindowOpenedOnce(ActivityState.Started) and not model:HasWindowOpenedOnce(ActivityState.Ended) then
      return activityDefinition.EndWindowPrefabName, {activityType}
    end
  end
end
