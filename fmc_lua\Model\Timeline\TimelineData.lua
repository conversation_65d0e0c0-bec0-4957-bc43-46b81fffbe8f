TimelineData = {}
TimelineData.__index = TimelineData

function TimelineData.Create(id, chapterName)
  local data = setmetatable({
    id = id,
    chapterName = chapterName,
    stepList = {}
  }, TimelineData)
  return data
end

function TimelineData:Init()
  self:Reset()
  if self.chapterName and self.id ~= REVIEW_TIMELINE_ID and self.id ~= CHAPTER_START_TIMELINE_ID then
    local arrSlotChanges = GM.TaskManager:GetTaskSlotsChanges(self.id, self.chapterName)
    local mapSlotChanges = {}
    local state, slotId
    for _, changeData in ipairs(arrSlotChanges) do
      slotId = changeData.Slot
      state = changeData.State
      Log.Assert(mapSlotChanges[slotId] == nil, tostring(self.id) .. " 配置了重复的 slotId：" .. tostring(slotId))
      mapSlotChanges[slotId] = state
    end
    self.mapSlotChanges = mapSlotChanges
  end
  local count = 1
  for step, v in pairs(self.stepList) do
    for _, data in pairs(v) do
      data:Init()
      local slotId = data.slotId
      if data.type ~= ETimelineAction.Cover and self.mapSlotChanges and slotId and self.mapSlotChanges[slotId] == nil then
        Log.Error("时间线 " .. self.id .. " 中的 slot 没有在任务中找到对应的 state。 slotId:" .. tostring(slotId))
      end
    end
  end
  self.stepCount = #self.stepList
end

function TimelineData:Reset()
end

function TimelineData:AddStep(config)
  local order = config.Order
  local stepsForOneOrder = self.stepList[order]
  if not stepsForOneOrder then
    stepsForOneOrder = {}
    self.stepList[order] = stepsForOneOrder
  end
  stepsForOneOrder[#stepsForOneOrder + 1] = TimelineStep.Create(config)
end

TimelineStep = {}
TimelineStep.__index = TimelineStep

function TimelineStep.Create(tbData)
  local data = setmetatable({data = tbData}, TimelineStep)
  return data
end

function TimelineStep:Init()
  local data = self.data
  self.type = data.Type
  if self.type == ETimelineAction.Plot then
    local arrPlotData = StringUtil.Split(data.Content or "", "#")
    self.storyId = arrPlotData[1] or ""
    Log.Assert(not StringUtil.IsNilOrEmpty(self.storyId), "时间线 plot content 配置错误")
    self.delay = tonumber(arrPlotData[2]) or 0
  elseif self.type == ETimelineAction.SlotActions then
    local arrSlotActions = {}
    local arrSlotActionSplit = StringUtil.Split(data.Content or "", ";")
    local actionCount = #arrSlotActionSplit
    self.changeDelay = tonumber(arrSlotActionSplit[actionCount])
    if self.changeDelay then
      actionCount = actionCount - 1
    else
      self.changeDelay = 0
    end
    for i = 1, actionCount do
      local splits = StringUtil.Split(arrSlotActionSplit[i], "#")
      Log.Assert(2 <= #splits, "slotAction 时间线配置错误")
      local eSlotAnimationType = tostring(splits[1])
      local animationConfig = tostring(splits[2])
      local slotAction = {
        eSlotAnimationType = eSlotAnimationType,
        animationConfig = animationConfig,
        delay = tonumber(splits[3]) or 0
      }
      arrSlotActions[i] = slotAction
    end
    self.arrSlotActions = arrSlotActions
    self.slotId = data.Slot
    self.reviewState = data.ReviewState
  elseif self.type == ETimelineAction.SlotAnimator then
    self.delay = tonumber(data.Content) or 0
    self.slotId = data.Slot
    self.reviewState = data.ReviewState
  elseif self.type == ETimelineAction.SlotSpines then
    local arrSlotActions = {}
    local arrSlotActionSplit = StringUtil.Split(data.Content or "", ";")
    for i = 1, #arrSlotActionSplit do
      local splits = StringUtil.Split(arrSlotActionSplit[i], "-")
      local animationConfig = splits[2] or splits[1]
      local configSplits = StringUtil.Split(animationConfig, "#")
      local slotAction = {
        state = tonumber(splits[1]),
        animationName = configSplits[1],
        delay = tonumber(configSplits[2]) or 0
      }
      arrSlotActions[i] = slotAction
    end
    self.arrSlotActions = arrSlotActions
    self.slotId = data.Slot
    self.reviewState = data.ReviewState
  elseif self.type == ETimelineAction.CameraMove then
    local innerList = StringUtil.Split(data.Content, ",")
    Log.Assert(2 <= #innerList, "CameraMove格式错误")
    self.pos = TimelineStep.GetPositionList(innerList[1])[1]
    self.scale = self.pos[3]
    self.pos[3] = nil
    self.duration = tonumber(innerList[2])
    self.delayTime = tonumber(innerList[3]) or 0
  elseif self.type == ETimelineAction.RoleAction then
    local innerList = StringUtil.Split(data.Content, ",")
    Log.Assert(3 <= #innerList, "RoleAction格式错误")
    self.animationName = innerList[1]
    self.pos = TimelineStep.GetPositionList(innerList[2])[1]
    self.bReverse = innerList[3] == "true"
    self.waitTime = tonumber(innerList[4]) or 0
    self.delayTime = tonumber(innerList[5]) or 0
  elseif self.type == ETimelineAction.ConstructionAudio then
    local arrAudioDataStr = StringUtil.Split(data.Content, ";")
    self.arrAudioData = {}
    for _, asplit in ipairs(arrAudioDataStr) do
      local splits = StringUtil.Split(asplit, ",")
      Log.Assert(#splits == 2, "audio格式错误")
      local audioName = splits[1]
      if AudioFileConfigName.HasConfig(audioName) then
        local delayTime = tonumber(splits[2]) or 0
        self.arrAudioData[#self.arrAudioData + 1] = {audioName = audioName, delayTime = delayTime}
      else
        Log.Error("audioFile 不存在:" .. tostring(audioName))
      end
    end
  elseif self.type == ETimelineAction.Cover then
    self.slotId = data.Slot
    Log.Assert(self.slotId, "Cover slotId 为空")
    self.delayTime = tonumber(data.content) or 0
  else
    self.content = data.Content
  end
end

local split = "/"
local patten = "%([^()]+%)"

function TimelineStep.GetPositionList(str)
  local pos = {}
  for s in string.gmatch(str, patten) do
    pos[#pos + 1] = StringUtil.SplitToNum(string.sub(s, 2, #s - 1), split)
  end
  return pos
end

function TimelineStep:GetFinalState(timelineData)
  local state = self.reviewState
  if not state then
    local taskState = timelineData.mapSlotChanges[self.slotId]
    Log.Assert(taskState, "时间线 slot 没有在任务中找到对应的 state。 slotId:" .. tostring(self.slotId))
    state = taskState
  end
  return state
end
