ModeViewController = {}
ModeViewController.__index = ModeViewController
local BOARD_VIEW_POS_NORMAL = Vector3(-455, -760, 100)
BOARD_CAM_POS_X = 10000

function ModeViewController:Awake()
  GM:Add<PERSON><PERSON><PERSON>("ModeViewController", self)
  EventDispatcher.AddListener(EEventType.InitVideoFinished, self, self._OnInitVideoFinished)
  EventDispatcher.AddListener(EEventType.TimelineStart, self, self._OnTimelineStart)
  EventDispatcher.AddListener(EEventType.TimelineComplete, self, self._OnTimelineComplete)
  EventDispatcher.AddListener(EEventType.EnterMainScene, self, self.OnEnterScene)
  for activityType, activityDefinition in pairs(SpreeActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, function()
      self:_OnSpreeActivityStateChanged(activityType)
    end)
    self:_OnSpreeActivityStateChanged(activityType)
  end
  self.m_canvasWidth = GM.UIManager:GetCanvasSize().x
end

function ModeViewController:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function ModeViewController:OnEnterScene()
  self.m_chapterInput.gameObject:SetActive(true)
  self.m_chapterCam.enabled = true
  self.m_boardCam.enabled = true
  GM.ChapterManager.roomModel.view:OnEnterScene()
  self.bHasEntered = true
end

function ModeViewController:GetChapterCam()
  return self.m_chapterCam, self.m_chapterInput
end

function ModeViewController:GetBoardInfo()
  return self.m_boardCam
end

function ModeViewController:LoadView()
  self:ReloadRoomView()
  self:_LoadBoardView(EGameMode.Board, ScenePrefabConfigName.MainBoardView)
end

function ModeViewController:ReloadRoomView()
  self:_LoadRoomView()
end

function ModeViewController:_LoadRoomView()
  local roomGoName = EGameMode.Main .. "Go"
  self:_TryUnloadGameObject(roomGoName)
  self.isLoading = true
  local callback = function()
    self.isLoading = false
  end
  GM.UIManager:SetEventLock(true)
  local objName = "room_" .. GM.ChapterManager.curActiveChapterName
  self:_LoadView(ScenePrefabConfigName[objName], self.m_chapterRootTrans, roomGoName, Vector3.zero, 1, objName, function(go)
    GM.UIManager:SetEventLock(false)
    local luaTable = go:GetLuaTable()
    local roomModel = GM.ChapterManager.roomModel
    luaTable:Init(roomModel, callback)
    roomModel.view = luaTable
  end)
end

function ModeViewController:_LoadView(viewName, rootTrans, goName, pos, scale, objName, callback)
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(viewName), rootTrans, pos, function(go)
    self[goName] = go
    if objName then
      go.name = objName
    end
    go.transform.localScale = Vector3(scale, scale, 1)
    if callback then
      callback(go)
    end
  end)
end

function ModeViewController:_TryUnloadView(gameMode)
  local goName = gameMode .. "Go"
  self:_TryUnloadGameObject(goName)
end

function ModeViewController:_TryUnloadGameObject(goName)
  if self[goName] ~= nil then
    Object.Destroy(self[goName])
    self[goName] = nil
  end
end

function ModeViewController:UnloadTutorialBoardView()
  if self.m_tutorialBoardViewGo ~= nil then
    Object.Destroy(self.m_tutorialBoardViewGo)
    self.m_tutorialBoardViewGo = nil
    if not self.m_tutorialBoardRootTrans:IsNull() then
      self.m_tutorialBoardRootTrans.gameObject:SetActive(false)
    end
  end
end

function ModeViewController:_LoadBoardView(gameMode, viewName)
  local scale = ScreenFitter.GetBoardScale()
  local x = BOARD_VIEW_POS_NORMAL.x * scale
  local y = BOARD_VIEW_POS_NORMAL.y * scale + ScreenFitter.GetBoardOffsetY()
  local z = BOARD_VIEW_POS_NORMAL.z
  local pos = Vector3(x, y, z)
  self:_LoadView(viewName, self.m_boardRootTrans, gameMode .. "Go", pos, scale)
end

function ModeViewController:_LoadActivityBoardView(activityType, gameMode, viewName)
  local config = GM.DataResource.ScenePrefabConfig:GetConfig(viewName)
  local callback = function(gameObject)
    gameObject:SetActive(false)
    gameObject:GetLuaTable():Init(activityType)
    self[gameMode .. "Go"] = gameObject
  end
  GM.ResourceLoader:LoadPrefab(config, self.m_boardRootTrans, BOARD_VIEW_POS_NORMAL, callback)
end

function ModeViewController:LoadTutorialBoardView(boardModel)
  local config = GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.TutorialBoardView)
  local callback = function(gameObject)
    gameObject:GetLuaTable():Init(boardModel)
    self.m_tutorialBoardViewGo = gameObject
    self.m_tutorialBoardRootTrans.gameObject:SetActive(true)
  end
  GM.ResourceLoader:LoadPrefab(config, self.m_tutorialBoardRootTrans, Vector3(-290, -284, 100), callback)
end

function ModeViewController:LoadExtraBoardActivityBoardView(boardModel, rawImage, itemDeleteButton, itemTipButton, callback)
  local config = GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.ExtraBoardActivityBoardView)
  local fitScale = ScreenFitter.GetBoardScale()
  local callback = function(gameObject)
    if not UIUtil.IsEmptyComponent(self.m_extraBoardActivityBoardView) then
      Object.Destroy(gameObject)
      return
    end
    local sw = CS.System.Diagnostics.Stopwatch()
    sw:Start()
    self.m_extraBoardActivityBoardCamera.gameObject:SetActive(true)
    self.m_extraBoardActivityBoardView = gameObject:GetLuaTable()
    self.m_extraBoardActivityBoardView:Init(boardModel, itemDeleteButton, itemTipButton)
    self.m_extraBoardActivityBoardView.transform.localScale = Vector3(fitScale, fitScale, 1)
    if callback ~= nil then
      callback(self.m_extraBoardActivityBoardView)
    end
    sw:Stop()
    Debug.Log("[ExtraBoard] create board view:" .. sw.ElapsedMilliseconds)
    if not CS.GraphicHelper.Instance:CreateRenderTexture(self.m_extraBoardActivityBoardCamera, rawImage) then
      local str = self.m_extraBoardActivityBoardCamera:IsNull() and "cam" or ""
      str = (rawImage:IsNull() and "img" or "") .. str
      GM.BIManager:LogProject("rt_c_f", str)
      self:UnloadExtraBoardActivityBoardView()
    else
      EventDispatcher.DispatchEvent(EEventType.ExtraBoardViewCreated, boardModel)
    end
  end
  local x = -(boardModel:GetHorizontalTiles() * BaseBoardModel.TileSize) / 2 * fitScale
  local y = -(boardModel:GetVerticalTiles() * BaseBoardModel.TileSize) / 2 * fitScale
  GM.ResourceLoader:LoadPrefab(config, self.m_extraBoardActivityBoardCamera.transform, Vector3(x, y, 100), callback)
  GM.SceneManager:EnterActivityMode()
end

function ModeViewController:UnloadExtraBoardActivityBoardView()
  if not UIUtil.IsEmptyComponent(self.m_extraBoardActivityBoardView) then
    Object.Destroy(self.m_extraBoardActivityBoardView.gameObject)
    self.m_extraBoardActivityBoardView = nil
    if not UIUtil.IsEmptyComponent(self.m_extraBoardActivityBoardCamera) then
      CS.GraphicHelper.Instance:UnloadRenderTexture(self.m_extraBoardActivityBoardCamera)
      self.m_extraBoardActivityBoardCamera.gameObject:SetActive(false)
    end
  end
  GM.SceneManager:QuitActivityMode(EGameMode.ExtraBoard)
end

function ModeViewController:GetExtraBoardActivityBoardView()
  return self.m_extraBoardActivityBoardView
end

function ModeViewController:GetExtraBoardActivityBoardCamera()
  return self.m_extraBoardActivityBoardCamera
end

function ModeViewController:UpdateGameView(callback)
  local gameMode = GM.SceneManager:GetGameMode()
  local isMainMode = gameMode == EGameMode.Main
  Input.multiTouchEnabled = isMainMode
  self.m_chapterRaycaster.enabled = isMainMode
  self.m_boardCam.gameObject:SetActive(not isMainMode)
  self.m_chapterCam.gameObject:SetActive(isMainMode)
  for _, v in pairs(EGameMode) do
    if self[v .. "Go"] ~= nil then
      self[v .. "Go"]:SetActive(GM.SceneManager:GetGameMode() == v)
    end
  end
  if callback then
    callback()
  end
end

function ModeViewController:_OnInitVideoFinished()
  GM.AudioModel:PlayBGM(AudioFileConfigName.bgmMakeover)
  GM.SceneManager:PlayLoopEffect()
end

function ModeViewController:_OnTimelineStart()
  self.m_chapterRaycaster.enabled = false
end

function ModeViewController:_OnTimelineComplete()
  self.m_chapterRaycaster.enabled = true
end

function ModeViewController:_OnSpreeActivityStateChanged(activityType)
  local activityDefinition = SpreeActivityDefinition[activityType]
  local model = GM.ActivityManager:GetModel(activityType)
  if model:GetState() == ActivityState.Started then
    self:_LoadActivityBoardView(activityType, activityDefinition.GameMode, activityDefinition.BoardViewPrefabName)
  else
    if GM.SceneManager:GetGameMode() == activityDefinition.GameMode then
      GM.SceneManager:ChangeGameMode(EGameMode.Main)
    end
    self:_TryUnloadView(activityDefinition.GameMode)
  end
end

function ModeViewController:GetChapterRootTrans()
  return self.m_chapterRootTrans
end

function ModeViewController:ShowBoomEffect(pos, dur)
  self.m_boomEffectController:EnableBoom(pos, dur)
end
