# 通知权限请求弹窗触发规则

## 概述

游戏中的通知权限请求弹窗主要通过 `NotificationModel:TryOpenNotificationWindow()` 函数触发，用于在特定场景下引导玩家开启系统通知权限。

## 核心触发函数

### TryOpenNotificationWindow(eNotiSceneDescKey)

**函数位置**: `fmc_lua\Model\Notification\NotificationModel.lua`

## 触发前置条件

弹窗触发需要同时满足以下**所有条件**：

### 1. 基础环境检查
- ✅ **非自动运行模式**: `not IsAutoRun()`
- ✅ **系统通知未开启**: `not PlatformInterface.IsNotificationsEnabled()`
- ✅ **游戏内通知开关开启**: `PlayerPrefs.GetInt(EPlayerPrefKey.OpenNotification, 1) == 1`

### 2. 冷却时间限制
- ✅ **距离上次弹窗超过3天**: `serTime - lastTime >= 259200` (259200秒 = 3天)
- ✅ **弹窗次数未超限**: `popTimes < 3` (最多弹出3次)

### 3. 场景特定条件

#### 体力不足场景 (ENotiSceneDescKey.EnergyEmpty)
- ✅ **当前体力不足**: `energy < ItemSpread.GetCostEnergyNum()`

#### 其他场景 (CashDash, ItemCoolDown)
- ✅ **无额外条件检查**

### 4. 窗口状态检查
- ✅ **通知弹窗未打开**: `not GM.UIManager:IsViewExisting(UIPrefabConfigName.NotificationOpenWindow)`

## 具体触发场景

### 1. 体力购买窗口关闭时

**触发位置**: `fmc_lua\UI\Window\Shop\BuyEnergyWindow.lua:111`

```lua
function BuyEnergyWindow:OnCloseFinish()
  -- 当购买主体力失败或取消时
  if self.Type == EnergyType.Main then
    GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.EnergyEmpty)
  end
end
```

**触发条件**:
- 玩家打开体力购买窗口
- 购买失败或取消购买
- 购买的是主体力 (EnergyType.Main)

### 2. 母棋子点击时体力不足

**触发位置**: `fmc_lua\Board\Model\Item\ItemSpread.lua:278`

```lua
function ItemSpread:OnTap()
  if self:GetStorageRestNumber() == 0 then
    local restDuration = self:GetTimerDuration() + self:GetStartTimer() - GM.GameModel:GetServerTime()
    if 1200 <= restDuration then -- 剩余冷却时间 >= 20分钟
      GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.ItemCoolDown)
    end
  end
end
```

**触发条件**:
- 玩家点击母棋子
- 母棋子存储为0且在冷却中
- 剩余冷却时间 >= 20分钟 (1200秒)
- 已完成相关教程 (PDCD1, PDCD2)

### 3. 活动通知窗口关闭时

#### Dash活动
**触发位置**: `fmc_lua\DashActivity\Common\View\DashActivityWindow.lua:60`

```lua
function DashActivityNoticeWindow:OnCloseFinish()
  GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.CashDash)
end
```

#### Coconut活动
**触发位置**: `fmc_lua\DashActivity\Coconut\View\CoconutWindowTemplate1.lua:43`

```lua
function CoconutNoticeWindow:OnCloseFinish()
  GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.CashDash)
end
```

**触发条件**:
- 玩家关闭活动通知窗口
- 系统通知权限未开启

## 弹窗类型和描述

### 场景描述键值对应

```lua
ENotiSceneDescKey = {
  EnergyEmpty = "notification_desc_energy",      -- 体力不足
  CashDash = "notification_desc_cash_dash",      -- 活动相关
  ItemCoolDown = "notification_desc_item_cooldown" -- 物品冷却
}
```

### 弹窗内容
- **标题**: 固定的通知权限请求标题
- **描述**: 根据场景显示不同的描述文本
- **按钮**: "开启通知" 和 "稍后再说"

## 弹窗行为逻辑

### 打开时行为
1. **记录弹窗时间**: 更新 `LastNotifiWindowPopTime`
2. **增加弹窗次数**: `NotifiWindowPopTimes + 1`
3. **显示对应描述**: 根据 `descKey` 显示相应文本
4. **监听前台事件**: 应用回到前台时检查权限状态

### 用户操作响应

#### 点击"开启通知"
```lua
function NotificationOpenWindow:OnRequestAuthorization()
  GM.NotificationModel:TryOpenNotification(true)
end
```

#### 点击"稍后再说"
```lua
function NotificationOpenWindow:OnLateButtonClicked()
  self:Close() -- 直接关闭弹窗
end
```

#### 应用回到前台
```lua
function NotificationOpenWindow:ApplicationWillEnterForeground()
  if PlatformInterface.IsNotificationsEnabled() then
    self:Close() -- 如果权限已开启，自动关闭弹窗
  end
end
```

## 限制机制

### 时间限制
- **冷却期**: 3天 (259200秒)
- **目的**: 避免频繁打扰用户

### 次数限制
- **最大次数**: 3次
- **重置机制**: 通过服务器配置 `notify_remind_time` 可重置计数

### 服务器控制
```lua
function NotificationModel:_UpdateAuthRequestTimesInfo()
  local nServerTimesKey = GM.ConfigModel:GetGeneralConfByType(EGeneralConfType.NotifyRemindTimes)
  if nServerTimesKey ~= nil and nServerTimesKey ~= nLocalTimesKey then
    PlayerPrefs.SetInt(EPlayerPrefKey.NotifiWindowPopTimes, 0) -- 重置次数
  end
end
```

## 相关配置

### PlayerPrefs 键值
- `OpenNotification`: 游戏内通知开关 (默认: 1)
- `SystemNotiRequsted`: 是否已请求过系统权限 (默认: 0)
- `NotifiWindowPopTimes`: 弹窗次数计数
- `LastNotifiWindowPopTime`: 上次弹窗时间戳
- `NotificationClearTimesKey`: 服务器重置标识

### 服务器配置
- `notify_remind_time`: 控制弹窗次数重置的服务器配置

## 总结

通知权限请求弹窗是一个精心设计的用户引导机制，通过在关键时刻（体力不足、物品冷却、活动结束）提醒用户开启通知权限，同时通过时间和次数限制避免过度打扰，确保良好的用户体验。
