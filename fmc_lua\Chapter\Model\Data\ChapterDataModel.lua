ChapterDataModel = {}
ChapterDataModel.__index = ChapterDataModel
local DEFAULT_ENABLE_COUNT = 4

function ChapterDataModel:Init()
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self._OnLoginFinished)
end

function ChapterDataModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function ChapterDataModel:_OnLoginFinished(msg)
  if not msg.bSuccess then
    return
  end
  local configs = GM.ConfigModel:GetServerConfig(ServerConfigKey.ChapterUpdate)
  if not configs or #configs < 1 then
    return
  end
  self.m_mapServerConfig = {}
  self.m_maxConfigedChapter = 0
  local config, minDay, maxDay
  for i = 1, #configs do
    config = configs[i]
    if config.enable == 1 then
      minDay, maxDay = MainOrderDataModel.GetMinMaxOrderDayOfChapter(config.chapter)
      if not config.dayLevel or minDay > config.dayLevel or maxDay < config.dayLevel then
        config.dayLevel = maxDay
      end
      if self.m_mapServerConfig[config.chapter] == nil then
        self.m_mapServerConfig[config.chapter] = {}
      end
      table.insert(self.m_mapServerConfig[config.chapter], config)
      self.m_maxConfigedChapter = math.max(self.m_maxConfigedChapter, config.chapter)
    end
  end
  local sortFunc = function(a, b)
    return a.dayLevel < b.dayLevel
  end
  for chapter, list in pairs(self.m_mapServerConfig) do
    table.sort(list, sortFunc)
  end
  self:UpdatePerSecond()
end

function ChapterDataModel:UpdatePerSecond()
  if not self.m_arrOriginChapterSequence then
    return
  end
  local newChapterSequence = {}
  local newDayCount = 0
  local serverTime = GM.GameModel:GetServerTime()
  for id, name in ipairs(self.m_arrOriginChapterSequence) do
    if self.m_mapServerConfig and self.m_mapServerConfig[id] == nil and id < self.m_maxConfigedChapter then
      newChapterSequence[id] = name
      newDayCount = math.max(newDayCount, MainOrderDataModel.GetMaxOrderDayOfChapter(id))
    elseif self.m_mapServerConfig and self.m_mapServerConfig[id] ~= nil then
      for _, config in ipairs(self.m_mapServerConfig[id]) do
        if serverTime > config.openDay then
          newChapterSequence[id] = name
          newDayCount = math.max(newDayCount, config.dayLevel)
        end
      end
    end
    if newChapterSequence[id] == nil and self.m_userChapterId ~= nil and id <= self.m_userChapterId then
      newChapterSequence[id] = name
      local minDay, maxDay = MainOrderDataModel.GetMinMaxOrderDayOfChapter(id)
      newDayCount = math.max(newDayCount, minDay)
    end
  end
  if self.m_userOrderDay ~= nil and newDayCount < self.m_userOrderDay then
    newDayCount = self.m_userOrderDay
  end
  local oldChapterCount = #self.m_arrEffectChapterSequence
  self.m_arrEffectChapterSequence = newChapterSequence
  local newChapterCount = #self.m_arrEffectChapterSequence
  local oldDayCount = self.m_maxCanUnlockDay
  self.m_maxCanUnlockDay = newDayCount
  if oldChapterCount < newChapterCount or newDayCount > oldDayCount then
    EventDispatcher.DispatchEvent(EEventType.NewContentReleased)
  end
end

function ChapterDataModel:ConsiderTaskChapter(chapterId)
  self.m_userChapterId = chapterId
  self:UpdatePerSecond()
end

function ChapterDataModel:ConsiderOrderDay(day)
  self.m_userOrderDay = day
  self:UpdatePerSecond()
end

function ChapterDataModel:GetMaxCanUnlockDay()
  if not self.m_maxCanUnlockDay then
    Log.Error("too early to get this func")
    return 0
  end
  return self.m_maxCanUnlockDay
end

function ChapterDataModel:GetPreTipTime()
  if not self.m_mapServerConfig then
    return
  end
  local count = self:GetChapterCount()
  local nextConfig
  for _, config in ipairs(self.m_mapServerConfig[count] or {}) do
    if config.dayLevel > self.m_maxCanUnlockDay then
      nextConfig = config
      break
    end
  end
  if not nextConfig and self.m_mapServerConfig[count + 1] ~= nil then
    nextConfig = self.m_mapServerConfig[count + 1][1]
  end
  if not (nextConfig and nextConfig.preDay) or GM.GameModel:GetServerTime() < nextConfig.preDay then
    return
  end
  return TimeUtil.ToDate(nextConfig.openDay, ETimeFormat.YMDHMS)
end

function ChapterDataModel:LoadConfigs()
  self.m_mapChapterConfig = {}
  self.m_arrOriginChapterSequence = {}
  self.m_arrEffectChapterSequence = {}
  local arrConfigs = require("Data.Config.ChapterConfig")
  if not arrConfigs then
    Log.Error("ChapterConfig 配置缺失。")
    return {}
  end
  local maxChapterId = 0
  for _, config in ipairs(arrConfigs) do
    RewardApi.CryptRewards(config.UnlockRewards)
    self.m_mapChapterConfig[config.Id] = config
    self.m_arrOriginChapterSequence[config.Id] = config.Name
    if config.Id <= DEFAULT_ENABLE_COUNT then
      self.m_arrEffectChapterSequence[config.Id] = config.Name
    end
    maxChapterId = math.max(maxChapterId, config.Id)
  end
  if #self.m_arrOriginChapterSequence ~= maxChapterId then
    Log.Error("章节配置错误，Id不连续")
  end
  self.m_maxCanUnlockDay = MainOrderDataModel.GetMaxOrderDayOfChapter(DEFAULT_ENABLE_COUNT)
  self:UpdatePerSecond()
end

function ChapterDataModel:_GetChapterConfig(chapterId)
  local chapterConfig = self.m_mapChapterConfig[chapterId]
  if not chapterConfig then
    Log.Error("章节配置缺失：" .. chapterId)
  end
  return chapterConfig
end

function ChapterDataModel:GetChapterSequence()
  return self.m_arrEffectChapterSequence
end

function ChapterDataModel:GetChapterCount()
  return #self.m_arrEffectChapterSequence
end

function ChapterDataModel:GetChapterNameById(chapterId)
  local chapterName = self.m_arrEffectChapterSequence[chapterId]
  Log.Assert(chapterName ~= nil, "不存在的章节顺序索引：" .. tostring(chapterId))
  return chapterName
end

function ChapterDataModel:GetChapterIdByName(chapterName)
  local index = Table.GetIndex(self.m_arrEffectChapterSequence, chapterName)
  if index < 1 then
    return nil
  end
  return index
end

function ChapterDataModel:GetNextChapterNameById(chapterId)
  if chapterId == self:GetChapterCount() then
    return nil
  end
  return self:GetChapterNameById(chapterId + 1)
end

function ChapterDataModel:GetChapterUnlockRewards(chapterId)
  local chapterConfig = self:_GetChapterConfig(chapterId)
  return chapterConfig.UnlockRewards
end

function ChapterDataModel:GetChapterImageKey(chapterName)
  local imageName = "room_" .. chapterName
  return imageName
end

function ChapterDataModel:GetChapterNameText(chapterName)
  return GM.GameTextModel:GetText(chapterName .. "_name")
end

function ChapterDataModel:GetChapterRemainGold(chapterId)
  local chapterConfig = self:_GetChapterConfig(chapterId)
  return chapterConfig.RemainGold or 0
end
