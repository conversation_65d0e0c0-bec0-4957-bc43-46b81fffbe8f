HeartBeatManager = {}
HeartBeatManager.__index = HeartBeatManager
local HEART_BEAT_DEFAULT_MAX_INTERVAL = 60

function HeartBeatManager:Init()
  self.m_maxInterval = HEART_BEAT_DEFAULT_MAX_INTERVAL
  self.m_iPendingLimit = self.m_maxInterval
  EventDispatcher.AddListener(EEventType.EnterMainScene, self, self._OnEnterMainScene)
end

function HeartBeatManager:Update()
  if self.m_frameSchedulerArg == nil then
    return
  end
  if self.m_scheduled and not self:IsKickedOut() then
    self:_HeartBeatScheduler(self.m_frameSchedulerArg)
  end
  self.m_frameSchedulerArg = nil
end

function HeartBeatManager:UpdatePerSecond()
  if self.m_scheduled and not self:IsKickedOut() then
    self.m_schedulerInterval = self.m_schedulerInterval + 1
    if self.m_schedulerInterval > self.m_iPendingLimit then
      self:_HeartBeatScheduler(false)
    end
  end
end

function HeartBeatManager:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function HeartBeatManager:OnSceneViewLoaded()
  if self:IsKickedOut() and not self.m_bOpendWindow then
    self:KickedOut()
  end
end

function HeartBeatManager:_OnEnterMainScene()
  self:TrySchedule()
end

function HeartBeatManager:TrySchedule(triggeredByLogin)
  if not self:_CheckSceneCondition() or not GM.ActivityManager:CanHeartBeat() then
    self:Unschedule()
    return
  end
  if not self.m_scheduled then
    self.m_scheduled = true
    Log.Info("心跳开始")
    if GameConfig.IsTestMode() then
      GM.UIManager:ShowTestPrompt("心跳触发开始")
    end
    self:_HeartBeatScheduler(false)
  elseif triggeredByLogin then
    self:_HeartBeatScheduler(false)
  end
end

function HeartBeatManager:_HeartBeatScheduler(useHttp)
  if not self:GetSession() then
    Log.Info("[HeartBeatManager] session为nil")
    return
  end
  if not self.m_bIsSendingRequest and not GM.ActivityManager:IsSendingRankRequest() and GM.SyncModel:CanUpload() then
    self:_TrySend(useHttp)
  else
    self:_ResetSchedulerInterval()
  end
end

function HeartBeatManager:_ResetSchedulerInterval()
  self.m_iPendingLimit = self.m_iPendingLimit - self.m_schedulerInterval
  self.m_schedulerInterval = 0
end

function HeartBeatManager:_ClearSchedulerInterval()
  self.m_iPendingLimit = self.m_maxInterval
  self.m_schedulerInterval = 0
end

function HeartBeatManager:Unschedule()
  self.m_scheduled = false
  self:_ClearSchedulerInterval()
end

function HeartBeatManager:HeartBeatOnNextFrame(useHttp)
  self.m_frameSchedulerArg = useHttp
end

function HeartBeatManager:_TrySend(useHttp)
  local callback = function(bSuccess, response)
    if bSuccess then
      if response.rcode == 0 then
        GM.ActivityManager:FromHeartBeat(response)
      elseif response.rcode == 1 then
        self:HandleConflictSession()
        return
      end
    elseif GameConfig.IsTestMode() then
      GM.UIManager:ShowTestPrompt("心跳失败")
    end
    self.m_bIsSendingRequest = false
    self:_ClearSchedulerInterval()
  end
  local request = GM.ActivityManager:ToHeartBeat()
  request.userId = GM.UserModel:GetUserId()
  request.session = self:GetSession()
  request.user_level = GM.LevelModel:GetCurrentLevel()
  self.m_bIsSendingRequest = true
  RedisMessage.HeartBeat(request, callback, useHttp)
  if self.m_bIsSendingRequest and GameConfig.IsTestMode() then
    GM.UIManager:ShowTestPrompt("发送心跳")
  end
end

function HeartBeatManager:HandleHeartBeatResponse(tbResp)
  if tbResp.session ~= 0 and tbResp.session ~= self:GetSession() then
    self:HandleConflictSession()
    return
  end
  GM.ActivityManager:FromHeartBeat(tbResp)
end

function HeartBeatManager:HandleConflictSession()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Loading then
    self:KickedOut()
  else
    self:KickedOut(true)
  end
end

function HeartBeatManager:KickedOut(bHideWindow)
  bHideWindow = bHideWindow or false
  self.m_bOpendWindow = false
  self.m_bKickedOut = true
  GM.SyncModel:SetDataInconsistent(true)
  if not bHideWindow then
    self.m_bOpendWindow = true
    GM.UIManager:OpenView(UIPrefabConfigName.SystemGeneralMsgWindow, GM.GameTextModel:GetText("device_conflict_window_title"), GM.GameTextModel:GetText("device_conflict_window_desc"), GM.GameTextModel:GetText("common_button_ok"), function()
      PlatformInterface.ExitGame()
    end)
  end
end

function HeartBeatManager:IsKickedOut()
  return self.m_bKickedOut
end

function HeartBeatManager:SetSession(session)
  self.m_session = session
end

function HeartBeatManager:GetSession()
  return self.m_session
end

function HeartBeatManager:SetInterval(maxInterval)
  self.m_maxInterval = maxInterval
  self:TrySchedule(true)
end

function HeartBeatManager:_CanUseUdp()
  do return false end
  local strToken = GM.SsoManager:GetToken()
  if string.len(strToken) ~= 52 then
    return false
  end
  return true
end

function HeartBeatManager:_CheckSceneCondition()
  return GM and GM.SceneManager and GM.SceneManager:GetGameMode() ~= EGameMode.Loading
end
