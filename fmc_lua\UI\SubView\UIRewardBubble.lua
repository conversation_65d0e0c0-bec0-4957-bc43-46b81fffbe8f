UIRewardBubble = {}
UIRewardBubble.__index = UIRewardBubble

function UIRewardBubble:Init(iconRect)
  self.m_iconRect = iconRect
  self:InitTweens()
  self:UpdateSortingOrder()
  self:FitScreen()
end

function UIRewardBubble:InitTweens()
  if self.m_scaleTween then
    self.m_scaleTween:Kill()
    self.m_scaleTween = nil
  end
  local originScale = Vector3(1 / self.transform.parent.lossyScale.x, 1 / self.transform.parent.lossyScale.y, 1)
  self.m_scaleTween = self.transform:DOScale(originScale, 0.2)
  self.transform.localScale = Vector3.zero
  self.m_originScale = originScale
  UIUtil.SetActive(self.gameObject, true)
end

function UIRewardBubble:UpdateSortingOrder()
  local canvas = self.gameObject.transform.parent:GetComponentInParent(typeof(CS.UnityEngine.Canvas))
  local uiOrder = canvas and canvas.sortingOrder + 5 or 0
  if uiOrder ~= nil and IsNumber(uiOrder) then
    self.m_canvas.overrideSorting = true
    self.m_canvas.sortingOrder = uiOrder
  else
    self.m_canvas.overrideSorting = false
  end
end

function UIRewardBubble:FitScreen()
  local height = (self.m_bgRect.sizeDelta.y + 50) * self.m_originScale.y
  local width = self.m_bgRect.sizeDelta.x * self.m_originScale.x
  if self.m_widthDelta ~= nil and tonumber(self.m_widthDelta) ~= nil then
    width = width + self.m_widthDelta
  end
  local arrowMaxOffsetX = math.max((self.m_bgRect.sizeDelta.x - 100) * self.m_originScale.x / 2, 0)
  local camera = self.m_canvas.rootCanvas.worldCamera
  local _, screenLeftDownLocalPos = RectTransformUtility.ScreenPointToLocalPointInRectangle(self.transform.parent, Vector2(0, 0), camera)
  local _, screenRightUpLocalPos = RectTransformUtility.ScreenPointToLocalPointInRectangle(self.transform.parent, Vector2(Screen.width, Screen.height), camera)
  local targetLocalPos = self.m_iconRect.localPosition
  local targetHeight = self.m_iconRect.sizeDelta.y * self.m_iconRect.localScale.y
  local bUp = targetLocalPos.y + targetHeight / 2 + height <= screenRightUpLocalPos.y
  local ratio = bUp and 1 or -1
  local posY = targetLocalPos.y + ratio * (targetHeight / 2 + height / 2)
  UIUtil.SetActive(self.m_topArrowGo, not bUp)
  UIUtil.SetActive(self.m_bottomArrowGo, bUp)
  UIUtil.SetAnchoredPosition(self.m_rootRect, nil, height / 2 / self.m_originScale.y * ratio)
  posY = posY - ratio * height / 2
  local arrowRect = bUp and self.m_bottomArrowGo.transform or self.m_topArrowGo.transform
  local offsetX = 0
  local leftDis = screenLeftDownLocalPos.x - (targetLocalPos.x - width / 2)
  local rightDis = screenRightUpLocalPos.x - (targetLocalPos.x + width / 2)
  if 0 < leftDis then
    offsetX = math.min(leftDis, arrowMaxOffsetX)
  elseif rightDis < 0 then
    offsetX = math.max(rightDis, -arrowMaxOffsetX)
  end
  local posX = offsetX + targetLocalPos.x
  UIUtil.SetAnchoredPosition(self.m_rootRect, offsetX / self.m_originScale.x)
  posX = posX - offsetX
  UIUtil.SetAnchoredPosition(arrowRect, -offsetX / self.m_originScale.x)
  UIUtil.SetLocalPosition(self.transform, posX, posY)
end

function UIRewardBubble:GetMapInteractableObjs()
  return nil
end

function UIRewardBubble:Update()
  local mapInteractableObj = self:GetMapInteractableObjs()
  if CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject ~= nil and mapInteractableObj ~= nil and mapInteractableObj[CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject] then
    return
  end
  if Input.GetMouseButtonDown(0) then
    self:_DoRecyle()
  end
  if 0 < Input.touchCount then
    for i = 0, Input.touchCount - 1 do
      if Input.GetTouch(i).phase == TouchPhase.Began then
        self:_DoRecyle()
      end
    end
  end
end

function UIRewardBubble:_DoRecyle()
  self:OnDestroy()
  Recycle(self.gameObject)
end

function UIRewardBubble:OnDestroy()
  if self.m_scaleTween then
    self.m_scaleTween:Kill()
    self.m_scaleTween = nil
  end
  Scheduler.UnscheduleTarget(self)
  UIUtil.SetActive(self.gameObject, false)
end
