CoinRaceMainWindow = setmetatable({disableEffectWhenCloseView = true}, BaseWindow)
CoinRaceMainWindow.__index = CoinRaceMainWindow

function CoinRaceMainWindow:Init(activityType, bAuto, bFromSignUp)
  self.m_activityType = activityType
  local model = GM.ActivityManager:GetModel(activityType)
  self.m_activityDefinition = model:GetActivityDefinition()
  self.m_activityModel = model
  self.m_activityModel:UpdatePlayerRank()
  local bAcquired, rewards = self.m_activityModel:TryClaimReward()
  self.m_bAcquired = bAcquired
  self.m_acquiredRewards = rewards
  self.m_bFromSignUp = bFromSignUp
  local targetScore = self.m_activityModel:GetTargetScore()
  self.m_finishNumText.text = targetScore
  local titleInfo
  local canShowRound = self.m_activityModel:CanShowRound()
  if canShowRound then
    titleInfo = "(" .. self.m_activityModel:GetCurrentRound() .. "/" .. self.m_activityModel:GetRoundCount() .. ")"
  else
    titleInfo = self.m_activityModel:GetCurrentRoundInRoman()
  end
  self.m_titleText.text = GM.GameTextModel:GetText("coin_race_main_title", titleInfo)
  local descParam = "<sprite=\"coinRace_token_icon\" name=\"coinRace_token_icon\">"
  local color = canShowRound and "#8fff28" or "#39910e"
  local scoreStr = "<color=" .. color .. ">x" .. targetScore .. "</color>"
  self.m_descText.text = GM.GameTextModel:GetText("coin_race_main_desc", descParam .. scoreStr)
  self.m_historyGo:SetActive(canShowRound)
  self.m_noHistoryGo:SetActive(not canShowRound)
  if canShowRound then
    self:InitHistory()
  end
  local playerDatas = self.m_activityModel:GetAllPlayerData()
  Log.Assert(#playerDatas == COIN_RACE_MAX_PLAYER_COUNT, "Coin race player data error")
  self.m_cells = {}
  for i = 1, #playerDatas do
    local idx = playerDatas[i]:GetTrack()
    self.m_cells[idx] = self.m_cellsRectTrans:GetChild(idx - 1).gameObject:GetLuaTable()
    self.m_cells[idx]:Init(playerDatas, i, idx, self, targetScore, model)
  end
  self.m_bEventLock = true
  
  function self.m_eventLockCallback()
    self:TrySpeedUpAnimation()
  end
  
  GM.UIManager:SetEventLock(true, self, self.m_eventLockCallback)
  self.m_startAniEventLock = true
  for i = 1, COIN_RACE_MAX_PLAYER_COUNT do
    if self.m_cells[i] then
      self.m_cells[i]:TryPlayAnimation()
    end
  end
  self.m_activityModel:UpdateLastScore()
  self:UpdatePerSecond()
  self:LogWindowAction(EBIType.UIActionType.Open, {
    bAuto and EBIReferType.AutoPopup or EBIReferType.UserClick
  })
  if self.m_activityModel:GetState() == ActivityState.Ended and bAuto then
    local baseSceneView = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BaseSceneView)
    baseSceneView:GetPopupChain():GetHelper(EPopupHelper.CoinRace):SetNeedCheckPopup(true)
  end
  self.m_activityModel:OpenMainWindowEventCall()
  AddHandlerAndRecordMap(self.m_activityModel.event, RaceEventType.StateChanged, {
    obj = self,
    method = self.TryClose
  })
  GM.AudioModel:PlayEffectLoop(AudioFileConfigName.SfxCoinRaceActivityClap, self)
end

function CoinRaceMainWindow:OnDestroy()
  if self.m_activityModel ~= nil then
    RemoveAllHandlers(self.m_activityModel.event, self)
  end
  Scheduler.UnscheduleTarget(self)
  GM.UIManager:RemoveAllEventLocks(self)
  GM.AudioModel:StopEffectLoop(self)
end

function CoinRaceMainWindow:TryClose()
  if self.m_bEventLock then
    self.m_isGoingToClose = true
    return
  end
  self:Close()
end

function CoinRaceMainWindow:OnCloseView(bWithoutAnimation)
  BaseWindow.OnCloseView(self, bWithoutAnimation)
  self.m_RewardTipLuaTable:Hide(true)
end

function CoinRaceMainWindow:UpdatePerSecond()
  if self.m_tipCooldown ~= nil then
    self.m_tipCooldown = self.m_tipCooldown - 1
    if self.m_tipCooldown <= 0 then
      self.m_RewardTipLuaTable:Hide(false)
      self.m_tipCooldown = nil
    end
  end
  if self.m_activityModel == nil then
    return
  end
  if self.m_activityModel:GetState() == ActivityState.Ended then
    UIUtil.SetActive(self.m_countdownGo, false)
    return
  end
  local nextTime = self.m_activityModel:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_activityModel:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  else
    self:Close()
  end
end

function CoinRaceMainWindow:BeforeOpenCheck()
  for activityType, activityDefinition in pairs(CoinRaceActivityDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    if not model then
      return false
    end
    return true
  end
end

function CoinRaceMainWindow:InitHistory()
  self.m_descText2.text = self.m_descText.text
  local roundCnt = self.m_activityModel:GetRoundCount()
  local activityType = self.m_activityModel:GetType()
  local prefix = CoinRaceActivityDefinition[activityType].CoinRaceBoardEntryRankImgPrefix
  for i = 1, roundCnt do
    local rank = self.m_activityModel:GetRoundRanks(i)
    local medalGo = GameObject.Instantiate(self.m_historyMedalTemplateGo, self.m_historyScrollRect.content)
    medalGo:SetActive(true)
    local img = medalGo.transform:GetComponent(typeof(Image))
    if img then
      local hasRank = rank and rank <= 5
      local suffix = hasRank and rank or "empty"
      SpriteUtil.SetImage(img, prefix .. suffix, false)
    end
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_historyScrollRect.content)
  local currentRound = self.m_activityModel:GetCurrentRound()
  local offsetX = 0
  offsetX = 85 * (currentRound - 9 + 1)
  local length = self.m_historyScrollRect.content.rect.width - self.m_historyScrollRect.transform.rect.width
  local normalizedPos = math.max(0, math.min(offsetX / length, 1))
  self.m_historyScrollRect.horizontalNormalizedPosition = normalizedPos
end

function CoinRaceMainWindow:GetRewardTip()
  return self.m_RewardTipLuaTable
end

function CoinRaceMainWindow:ShowRewardTip(reward, trans, offsetX, offsetY, bDir)
  self.m_RewardTipLuaTable:Show(reward, trans, offsetX, offsetY, bDir)
  self.m_tipCooldown = 3
end

function CoinRaceMainWindow:HideRewardTip()
  self.m_tipCooldown = nil
  self.m_RewardTipLuaTable:Hide(false)
end

function CoinRaceMainWindow:IsFromSignUp()
  return self.m_bFromSignUp
end

function CoinRaceMainWindow:TrySpeedUpAnimation()
  for i = 1, COIN_RACE_MAX_PLAYER_COUNT do
    if not self.m_cells[i] then
      break
    end
    self.m_cells[i]:TrySpeedUpAnimation()
  end
end

function CoinRaceMainWindow:OnCoinAniFinish()
  local needAni = false
  for i = 1, COIN_RACE_MAX_PLAYER_COUNT do
    if not self.m_cells[i] then
      break
    end
    if self.m_cells[i].playingAni == nil or self.m_cells[i].playingAni then
      return
    end
    if self.m_cells[i].realNeedAni then
      needAni = true
    end
  end
  if needAni then
    DelayExecuteFuncInView(function()
      GM.AudioModel:PlayEffect(AudioFileConfigName.SfxCoinRaceActivityBell)
    end, 0.2, self)
  end
  for i = 1, COIN_RACE_MAX_PLAYER_COUNT do
    if not self.m_cells[i] then
      break
    end
    self.m_cells[i]:UpdateContent(needAni)
  end
  if self.m_bEventLock then
    self.m_bEventLock = false
    GM.UIManager:SetEventLock(false, self, self.m_eventLockCallback)
    self.m_startAniEventLock = nil
  elseif self.m_startAniEventLock then
    GM.BIManager:LogErrorInfo("coinr_el_u", "not ui lock")
  else
    GM.BIManager:LogErrorInfo("coinr_el_i", "not init")
  end
  if not self.m_bAcquired then
    if self.m_isGoingToClose then
      self:Close()
    end
    return
  end
  local rank = self.m_activityModel:GetMyRank()
  local rewards = self.m_acquiredRewards
  DelayExecuteFuncInView(function()
    if self and self.gameObject and not self.gameObject:IsNull() then
      self:Close()
    end
    if not Table.IsEmpty(rewards) then
      GM.UIManager:OpenViewWhenIdle(self.m_activityDefinition.RewardWindowPrefabName, self.m_activityType, rank, rewards)
    else
      local model = self.m_activityModel
      if model:GetState() == ActivityState.Started and not model:HasFinishedAllRounds() then
        GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, true, true)
      end
    end
  end, 2, self, true)
end

function CoinRaceMainWindow:UpdateCurHighScore(score, cell)
  if not self.m_highScore then
    self.m_highScore = 0
  end
  if score > self.m_highScore or score == self.m_highScore and self.m_highScoreCell ~= nil and cell:GetShowRank() < self.m_highScoreCell:GetShowRank() then
    self.m_highScore = score
    if self.m_highScoreCell == cell then
      return
    end
    self.m_highScoreCell = cell
    cell:SetHighScore(true)
    for i = 1, COIN_RACE_MAX_PLAYER_COUNT do
      if self.m_cells[i] and self.m_cells[i] ~= cell then
        self.m_cells[i]:SetHighScore(false)
      end
    end
  elseif score ~= self.m_highScore or cell ~= self.m_highScoreCell then
    cell:SetHighScore(false)
  end
end

function CoinRaceMainWindow:GetCurHighScore()
  return self.m_highScore or 0
end

function CoinRaceMainWindow:OnBtnClicked()
  self:Close()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    GM.SceneManager:ChangeGameMode(EGameMode.Board)
  end
end

function CoinRaceMainWindow:OnHelpBtnClick()
  GM.UIManager:OpenView(self.m_activityDefinition.HelpWindowPrefabName, self.m_activityType)
  self:HideRewardTip()
end

function CoinRaceMainWindow:OnBgClick()
  self:HideRewardTip()
end

function CoinRaceMainWindow:GetTrackTransf()
  return self.m_cellsRectTrans
end

function CoinRaceMainWindow:GetEndLineTransf()
  return self.m_endLineRectTrans
end

function CoinRaceMainWindow:GetAvatarAreaTransf()
  return self.m_avatarAreaTrans
end

function CoinRaceMainWindow:GetHelpTransf()
  return self.m_helpRectTrans
end

function CoinRaceMainWindow:Close()
  BaseWindow.Close(self)
end

CoinRaceCell = {}
CoinRaceCell.__index = CoinRaceCell
local MaxAnimationDuration = 2
local MinAnimationDuration = 1

function CoinRaceCell:Init(playerDatas, myIndex, trackIndex, parent, targetScore, activityModel)
  self.m_playerData = playerDatas[myIndex]
  self.m_AllPlayerData = playerDatas
  self.m_index = trackIndex
  self.m_parent = parent
  Log.Assert(0 < targetScore, "targetScore should be greater than 0")
  self.m_targetScore = targetScore
  self.m_activityModel = activityModel
  self.m_userNameText.text = self.m_playerData:GetUserName()
  self.m_oldScore = self.m_playerData:GetLastScore()
  self.m_curScore = self.m_playerData:GetCurScore(activityModel:GetGapTime())
  self.m_rankBgGo:SetActive(false)
  self.m_rankImageImg.gameObject:SetActive(false)
  self.m_medalRank = 0
  if self.m_parent:IsFromSignUp() then
    self.m_userRectTrans:SetLocalScaleXY(0)
    self.m_userRectTrans:DOScale(1, 0.2):SetDelay(0.5)
  end
  local frame = self.m_playerData:IsMySelf() and EAvatarFrame.Highlight or EAvatarFrame.Normal
  self.m_userAvatar:SetAvatar(frame, self.m_playerData:GetIcon())
  local baseOrder = self.m_parent:GetSortingOrder()
  UIUtil.UpdateSortingOrder(self.m_effectRootTrans, baseOrder + 1)
  UIUtil.UpdateSortingOrder(self.m_runEffectGo.transform, baseOrder + 1)
  self.m_userCanvas.sortingOrder = baseOrder + 2
end

function CoinRaceCell:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  if self.m_rankSeq ~= nil then
    self.m_rankSeq:Kill()
    self.m_rankSeq = nil
  end
  if self.m_bgSeq ~= nil then
    self.m_bgSeq:Kill()
    self.m_bgSeq = nil
  end
end

function CoinRaceCell:UpdateContent(needAni)
  self:SetScore(self.m_curScore)
  self:UpdateRankIcon(needAni)
  self:SetHighScore(self:GetShowRank() == 1)
end

function CoinRaceCell:OnRewardClick()
  if self.m_medalRank == nil or self.m_medalRank == 0 or self.m_medalRank > 3 then
    return
  end
  local rewards = self.m_activityModel:GetCurrentRoundReward()
  Log.Assert(#rewards == 3, "[CoinRace]奖励配置数量有问题")
  local reward = rewards[self.m_medalRank]
  self.m_parent:ShowRewardTip(reward, self.m_rankImageImg.transform, 0, 60, true)
end

function CoinRaceCell:SetScore(score)
  self:UpdateScoreText(score)
  local percent = math.max(0, math.min(1, score / self.m_targetScore))
  local posY, heightY = self:GetTargetPointInfo(score)
  self.m_userRectTrans:SetAnchoredPosY(posY)
  UIUtil.SetSizeDelta(self.m_footRect, nil, heightY)
end

function CoinRaceCell:UpdateScoreText(score)
  self.m_resultText.text = score
  self.m_parent:UpdateCurHighScore(score, self)
end

function CoinRaceCell:GetTargetPointInfo(score)
  local percent = math.max(0, math.min(1, score / self.m_targetScore))
  return percent * 660, percent * 660 + 20
end

function CoinRaceCell:TryPlayAnimation()
  local from = self.m_oldScore
  local to = self.m_curScore
  self:SetScore(from)
  self.realNeedAni = false
  self.playingAni = true
  if from >= to then
    self:OnAniFinish()
    return
  end
  local myRank = self.m_playerData:GetRank()
  local delay = 0.5
  if 1 < myRank then
    local upRankData = self:GetPlayerDataByRank(myRank - 1)
    while upRankData ~= nil do
      local oldScore = upRankData:GetLastScore()
      local curScore = upRankData:GetCurScore(self.m_activityModel:GetGapTime())
      if oldScore == curScore then
        break
      end
      delay = delay + self:GetAnimationDuration(oldScore, self.m_oldScore, self.m_targetScore)
      upRankData = self:GetPlayerDataByRank(upRankData:GetRank() - 1)
    end
  end
  self.realNeedAni = true
  if 0 < delay then
    DelayExecuteFuncInView(function()
      self:PlayAnimation()
    end, delay, self)
  else
    self:PlayAnimation()
  end
end

function CoinRaceCell:GetAnimationDuration(from, to, target)
  return MinAnimationDuration
end

function CoinRaceCell:GetPlayerDataByRank(rank)
  for i = 1, #self.m_AllPlayerData do
    if self.m_AllPlayerData[i]:GetRank() == rank then
      return self.m_AllPlayerData[i]
    end
  end
  return nil
end

function CoinRaceCell:PlayAnimation()
  local from = self.m_oldScore
  local to = self.m_curScore
  local target = self.m_targetScore
  local duration = self:GetAnimationDuration(from, to, target)
  if self.m_scoreTween then
    self.m_scoreTween:Kill()
  end
  if self.m_sequence then
    self.m_sequence:Kill()
  end
  self.m_avatarAnimator:SetBool("run", true)
  UIUtil.SetActive(self.m_runEffectGo, true)
  local posY, heightY = self:GetTargetPointInfo(to)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxCoinRaceActivityRun)
  self.m_sequence = DOTween.Sequence()
  self.m_sequence:Append(self.m_userRectTrans:DOAnchorPosY(posY, duration))
  self.m_sequence:Join(self.m_footRect:DOSizeDelta(Vector2(self.m_footRect.sizeDelta.x, heightY), duration))
  self.m_sequence:OnComplete(function()
    self.m_sequence = nil
  end)
  self.m_scoreTween = DOVirtual.Float(from, to, duration, function(x)
    self:UpdateScoreText(math.ceil(x))
  end):OnComplete(function()
    self:OnAniFinish()
    self.m_scoreTween = nil
  end)
end

function CoinRaceCell:TrySpeedUpAnimation()
  Scheduler.FireAndUnscheduleTarget(self)
  if self.m_scoreTween then
    self.m_scoreTween:Complete(true)
  end
  if self.m_sequence then
    self.m_sequence:Complete(true)
  end
end

function CoinRaceCell:OnAniFinish()
  self.playingAni = false
  self:SetScore(self.m_curScore)
  self.m_parent:OnCoinAniFinish()
  self.m_avatarAnimator:SetBool("run", false)
  UIUtil.SetActive(self.m_runEffectGo, false)
end

function CoinRaceCell:UpdateRankIcon(needAni)
  local rankIcon, rank = self:GetRankIcon()
  self.m_medalRank = rank
  if rankIcon then
    SpriteUtil.SetImage(self.m_rankImageImg, rankIcon, true, function()
      self.m_rankImageImg.gameObject:SetActive(true)
      if self.m_medalRank == 1 then
        self.m_rankBgGo:SetActive(true)
      end
      local rank = self:GetShowRank()
      if self.m_parent:IsFromSignUp() and rank == 1 then
        DOVirtual.DelayedCall(0.7, function()
          if self and self.gameObject and not self.gameObject:IsNull() then
            self:OnRewardClick()
          end
        end)
      end
      if needAni then
        local orgScale = 0.37
        local bgDuration = self.m_medalRank == 1 and 0.3 or 0
        local delayTime = 0.2 * (4 - rank)
        if self.m_medalRank == 1 then
          local bgRect = self.m_rankBgGo.transform
          UIUtil.SetAnchoredPosition(bgRect, nil, 200)
          self.m_bgSeq = bgRect:DOAnchorPosY(0, bgDuration):SetEase(Ease.OutCubic):SetDelay(delayTime)
        end
        self.m_rankImageImg.transform.localScale = Vector3.zero
        local seq = DOTween.Sequence()
        seq:AppendInterval(delayTime + bgDuration)
        seq:Append(self.m_rankImageImg.transform:DOScale(orgScale + 0.1, 0.2))
        seq:AppendCallback(function()
          if not self.realNeedAni then
            return
          end
          self:PlayRankBoxEffect()
          self.m_rankSeq = nil
        end)
        seq:Append(self.m_rankImageImg.transform:DOScale(orgScale, 0.2))
        self.m_rankSeq = seq
      end
    end)
  end
end

function CoinRaceCell:GetShowRank()
  local rank = self.m_playerData:GetRank()
  if rank == 0 then
    if self.m_index == 3 then
      return 1
    elseif self.m_index == 2 then
      return 2
    elseif self.m_index == 4 then
      return 3
    end
    return
  end
  return rank
end

function CoinRaceCell:GetRankIcon()
  local rank = self:GetShowRank()
  if 3 < rank then
    return
  end
  local prefix = CoinRaceActivityDefinition[self.m_activityModel:GetType()].CoinRaceBoxRankImgPrefix
  return ImageFileConfigName[prefix .. rank], rank
end

local highScoreColor = CSColor.white
local unhighScoreColor = UIUtil.ConvertHexColor2CSColor("57577b")

function CoinRaceCell:SetHighScore(isHigh)
  if self.m_isCurHighScore == isHigh then
    return
  end
  self.m_isCurHighScore = isHigh
end

function CoinRaceCell:PlayRankBoxEffect()
  self.m_boxParticleSystem:Play()
end
