SpreeActivityShopButton = setmetatable({}, HudGeneralButton)
SpreeActivityShopButton.__index = SpreeActivityShopButton

function SpreeActivityShopButton:Init(activityType)
  self.m_activityType = activityType
  self.m_activityDefinition = SpreeActivityDefinition[activityType]
  self.m_shopModel = GM.ActivityManager:GetModel(activityType):GetShopModel()
  for key, value in pairs(EGameMode) do
    if value == self.m_activityDefinition.GameMode then
      self.m_strDisplayGameMode = key
      break
    end
  end
  self.m_exclamationGo:SetActive(not self.m_shopModel:IsWindowOpened())
end

function SpreeActivityShopButton:OnClicked()
  self.m_shopModel:SetWindowOpened()
  self.m_exclamationGo:SetActive(false)
  GM.UIManager:OpenView(self.m_activityDefinition.ShopWindowPrefabName, self.m_activityType)
end
