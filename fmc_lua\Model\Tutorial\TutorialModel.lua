TutorialModel = {}
TutorialModel.__index = TutorialModel
local DBColumnState = "state"
local DBColumnOngoingDatas = "ongoingDatas"

function TutorialModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.Tutorial)
  self.m_mapAutoPopupExecuters = {}
  self.m_mapOngoingTutorials = {}
end

function TutorialModel:IsNewUser()
  return not self:IsTutorialFinished(ETutorialId.CG) and not self:IsTutorialFinished(ETutorialId.Timeline) and not self:IsTutorialOnGoing(ETutorialId.Timeline)
end

function TutorialModel:OnCheckResourcesFinished()
  self:UpdateTutorialDatas()
  if GameConfig.IsTestMode() and PlayerPrefs.GetInt(EPlayerPrefKey.TestIgnoreTutorial, 0) == 1 then
    self:TestFinishAllTutorials()
  end
  self:TryToStartTutorials()
end

function TutorialModel:Destroy()
  EventDispatcher.RemoveTarget(self)
  for _, executer in pairs(self.m_mapOngoingTutorials) do
    executer:OnRemove()
  end
end

function TutorialModel:UpdateTutorialDatas()
  if self.m_mapOngoingTutorials ~= nil then
    for _, executer in pairs(self.m_mapOngoingTutorials) do
      executer:OnRemove()
    end
  end
  if self.m_mapTutorialListenEventType ~= nil then
    for type, _ in pairs(self.m_mapTutorialListenEventType) do
      EventDispatcher.RemoveListener(type, self)
    end
  end
  local arrTutorialConfigs = require("Data.Tutorial.TutorialConfig")
  self.m_mapTutorialConfigDatas = {}
  self.m_mapOngoingTutorials = {}
  self.m_mapNotStartedTutorials = {}
  self.m_mapTutorialListenEventType = {}
  local state, data, executer, mapEventType
  for i, tbData in ipairs(arrTutorialConfigs) do
    data = TutorialConfigData.Create(tbData)
    self.m_mapTutorialConfigDatas[data:GetTutorialId()] = data
    state = self.m_dbTable:GetValue(data:GetTutorialId(), DBColumnState)
    if state == nil or state == ETutorialState.NotStarted then
      self.m_mapNotStartedTutorials[data:GetTutorialId()] = data
      mapEventType = data:GetMapSpecialEventListeners()
      if mapEventType ~= nil then
        for eventType, _ in pairs(mapEventType) do
          self.m_mapTutorialListenEventType[eventType] = true
        end
      end
    elseif state == ETutorialState.Ongoing then
      self:_StartTutorial(data:GetTutorialId(), data:GetArgs())
    end
  end
  for eventType, _ in pairs(self.m_mapTutorialListenEventType) do
    EventDispatcher.AddListener(eventType, self, self.TryToStartTutorials)
  end
end

function TutorialModel:IsTutorialFinished(tutorialId)
  return self:_GetState(tutorialId) == ETutorialState.Finished
end

function TutorialModel:IsTutorialOnGoing(tutorialId)
  return self:_GetState(tutorialId) == ETutorialState.Ongoing
end

function TutorialModel:HasAnyStrongTutorialOngoing()
  if self.m_mapOngoingTutorials == nil then
    return false
  end
  for _, executer in pairs(self.m_mapOngoingTutorials) do
    if executer:IsInStrongTutorial() then
      return true, executer:GetTutorialId()
    end
  end
  return false
end

function TutorialModel:GetOngoindDatas(tutorialId)
  if not self:IsTutorialOnGoing(tutorialId) then
    return nil
  end
  return self:_GetOngoingDatas(tutorialId)
end

function TutorialModel:_GetState(tutorialId)
  return self.m_dbTable:GetValue(tutorialId, DBColumnState)
end

function TutorialModel:_GetOngoingDatas(tutorialId)
  return self.m_dbTable:GetValue(tutorialId, DBColumnOngoingDatas)
end

function TutorialModel:UpdateOngoingDatas(tutorialId, strOngoingDatas)
  self.m_dbTable:Set(tutorialId, DBColumnOngoingDatas, strOngoingDatas)
end

function TutorialModel:SetTutorialOngoing(tutorialId)
  self.m_dbTable:Set(tutorialId, DBColumnState, ETutorialState.Ongoing)
  self.m_dbTable:Set(tutorialId, DBColumnOngoingDatas, "")
end

function TutorialModel:SetTutorialFinished(tutorialId)
  self.m_dbTable:Set(tutorialId, DBColumnState, ETutorialState.Finished)
  self.m_dbTable:Set(tutorialId, DBColumnOngoingDatas, "")
end

function TutorialModel:GetData()
  return self.m_dbTable
end

function TutorialModel:FromSyncData(dataArr)
  self.m_dbTable:FromArr(dataArr)
end

function TutorialModel:_GetExecuterName(tutorialId)
  return self.m_mapTutorialConfigDatas[tutorialId] and self.m_mapTutorialConfigDatas[tutorialId]:GetExecuterName()
end

function TutorialModel:_GetExecuterTemplatePath(executerName)
  return "Model.Tutorial.Executer.TutorialExecuter" .. executerName
end

function TutorialModel:_CreateExecuter(tutorialId, args)
  local executerName = self:_GetExecuterName(tutorialId)
  if executerName ~= nil then
    local creator = require(self:_GetExecuterTemplatePath(executerName))
    return creator(tutorialId, self:_GetOngoingDatas(tutorialId), args)
  end
  return nil
end

function TutorialModel:TryToStartTutorials()
  if GameConfig.IsTestMode() and PlayerPrefs.GetInt(EPlayerPrefKey.TestIgnoreTutorial, 0) == 1 then
    return
  end
  for tutorialId, configData in pairs(self.m_mapNotStartedTutorials) do
    if configData:IsSatisfied() then
      self.m_mapNotStartedTutorials[tutorialId] = nil
      self:_StartTutorial(configData:GetTutorialId(), configData:GetArgs())
    end
  end
end

function TutorialModel:_StartTutorial(tutorialId, args)
  local executer = self:_CreateExecuter(tutorialId, args)
  if executer ~= nil then
    self.m_mapOngoingTutorials[tutorialId] = executer
    if not self:IsTutorialOnGoing(tutorialId) then
      self:SetTutorialOngoing(tutorialId)
    end
    executer:OnStart()
    return true
  end
  return false
end

function TutorialModel:FinishTutorial(tutorialId)
  local executer = self.m_mapOngoingTutorials and self.m_mapOngoingTutorials[tutorialId]
  if executer then
    executer:LogTutorialStepFinish(TutorialExecuter.StepEnd)
    executer:OnRemove()
  end
  self.m_mapNotStartedTutorials[tutorialId] = nil
  self.m_mapOngoingTutorials[tutorialId] = nil
  self:SetTutorialFinished(tutorialId)
  self:TryToStartTutorials()
  EventDispatcher.DispatchEvent(EEventType.TutorialFinished, {id = tutorialId})
end

function TutorialModel:ClearTempDatas()
  self:SetForceSourceBoardPosition()
  self:SetForceTargetBoardPosition()
  self:SetForceSpreadItemCode()
end

function TutorialModel:SetForceSourceBoardPosition(boardPos)
  self.m_sourceBoardPos = boardPos
end

function TutorialModel:GetForceSourceBoardPosition()
  return self.m_sourceBoardPos
end

function TutorialModel:SetForceTargetBoardPosition(boardPos)
  self.m_targetBoardPos = boardPos
end

function TutorialModel:GetForceTargetBoardPosition()
  return self.m_targetBoardPos
end

function TutorialModel:SetForceSpreadItemCode(code)
  self.m_spreadItemCode = code
end

function TutorialModel:GetForceSpreadItemCode()
  return self.m_spreadItemCode
end

function TutorialModel:AddAutoPopup(executer)
  self.m_mapAutoPopupExecuters[executer] = true
end

function TutorialModel:RemoveAutoPopup(executer)
  self.m_mapAutoPopupExecuters[executer] = nil
end

function TutorialModel:ShouldAutoPopup()
  return next(self.m_mapAutoPopupExecuters) ~= nil
end

function TutorialModel:TryAutoPopup()
  Log.Assert(self:ShouldAutoPopup(), "引导自动弹出逻辑错误")
  local mapOngoingTutorials = Table.ShallowCopy(self.m_mapOngoingTutorials)
  for k, executer in pairs(mapOngoingTutorials) do
    if self.m_mapAutoPopupExecuters[executer] and executer:TryStartTutorial() then
      executer:LogTutorialStepFinish(TutorialExecuter.StepStart)
      self:RemoveAutoPopup(executer)
      return true
    end
  end
end

function TutorialModel:TryPopupTargetTutorial(tutorialId)
  local executer = self.m_mapOngoingTutorials[tutorialId]
  if executer == nil then
    return false
  end
  if executer:TryStartTutorial() then
    executer:LogTutorialStepFinish(TutorialExecuter.StepStart)
    return true
  end
end

function TutorialModel:TestFinishTutorial(tutorialId)
  if not GameConfig.IsTestMode() then
    return false
  end
  for k, data in pairs(self.m_mapNotStartedTutorials) do
    if tutorialId == data:GetTutorialId() then
      self:SetTutorialFinished(data:GetTutorialId())
      self.m_mapNotStartedTutorials[k] = nil
      if tutorialId == "tutorial_2" then
        GM.MiscModel:SetIntroTimelineFinished(true)
      end
      return true
    end
  end
  for k, executer in pairs(self.m_mapOngoingTutorials) do
    if tutorialId == executer:GetTutorialId() then
      self:SetTutorialFinished(executer:GetTutorialId())
      executer:OnRemove()
      self.m_mapOngoingTutorials[k] = nil
      if tutorialId == "tutorial_2" then
        GM.MiscModel:SetIntroTimelineFinished(true)
      end
      GM.TutorialModel:ClearTempDatas()
      return true
    end
  end
  return false
end

function TutorialModel:TestUnFinishTutorial(tutorialId)
  if not GameConfig.IsTestMode() then
    return false
  end
  self.m_mapNotStartedTutorials[tutorialId] = self.m_mapTutorialConfigDatas[tutorialId]
  self.m_dbTable:Set(tutorialId, DBColumnState, ETutorialState.NotStarted)
  self.m_dbTable:Set(tutorialId, DBColumnOngoingDatas, "")
  PlayerPrefs.SetInt(EPlayerPrefKey.TestIgnoreTutorial, 0)
  self:UpdateTutorialDatas()
  self:TryToStartTutorials()
  return true
end

function TutorialModel:TestFinishAllTutorials()
  if not GameConfig.IsTestMode() then
    return false
  end
  local arrTutorialConfigs = require("Data.Tutorial.TutorialConfig")
  for _, config in ipairs(arrTutorialConfigs) do
    self:SetTutorialFinished(config.id)
  end
  GM.MiscModel:SetIntroTimelineFinished(true)
  self.m_mapNotStartedTutorials = {}
  for _, executer in pairs(self.m_mapOngoingTutorials) do
    executer:OnRemove()
  end
  self.m_mapOngoingTutorials = {}
end
