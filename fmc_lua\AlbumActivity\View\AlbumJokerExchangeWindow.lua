AlbumJokerExchangeWindow = setmetatable({bAutoBI = false}, AlbumActivityBaseWindow)
AlbumJokerExchangeWindow.__index = AlbumJokerExchangeWindow

function AlbumJokerExchangeWindow:Init(activityType, bUserClick, bShowOnlyNone, jokerEndTime)
  AlbumActivityBaseWindow.Init(self, activityType, bUserClick)
  local curProgress, maxProgress = self.m_model:GetAlbumCollectProgress()
  self.m_bCompleteAlbum = maxProgress <= curProgress
  self.m_bShowOnlyNone = not self.m_bCompleteAlbum
  self.m_bInitState = self.m_bCompleteAlbum
  local bGoldJoker = self.m_model:IsGoldenJokerCardInUse()
  if not self.m_bCompleteAlbum and not bGoldJoker and self.m_model:IsAllGoldCardUncollect() then
    self.m_bShowOnlyNone = false
    self.m_bInitState = true
  end
  if bShowOnlyNone ~= nil then
    self.m_bShowOnlyNone = bShowOnlyNone
  end
  self.m_toggle.isOn = self.m_bShowOnlyNone
  self.m_arrCardSet = Table.ShallowCopy(self.m_model:GetCardSets())
  table.sort(self.m_arrCardSet, function(a, b)
    local collectNumA = self.m_model:GetSetCollectProgress(a.setId)
    local collectNumB = self.m_model:GetSetCollectProgress(b.setId)
    if collectNumA ~= collectNumB then
      return collectNumA > collectNumB
    end
    return a.order < b.order
  end)
  self.m_nextStateTime = self.m_model:GetNextStateTime()
  self.m_jokerEndTime = jokerEndTime
  if self.m_jokerEndTime == nil then
    local jokerGetTime
    if AlbumActivityModel.UseNewJokerCard then
      jokerGetTime = self.m_model:GetMaxJokerCardTime()
    else
      jokerGetTime = self.m_model:GetMinJokerCardTime()
    end
    local nextStateTime = self.m_model:GetNextStateTime()
    self.m_jokerEndTime = jokerGetTime + Sec2Day
    self.m_jokerEndTime = math.min(self.m_jokerEndTime, nextStateTime)
  end
  self:UpdatePerSecond()
  self.m_exchangeBtnLuaTable:SetUIEnabled(false)
  self:_UpdateCurArrSet()
  local initParam = ListViewParam.CopyDefaultInitParam()
  initParam.mItemDefaultWithPaddingSize = 0
  self.m_loopListView:InitListView(#self.m_curArrSets, function(listView, cellIndex)
    return self:GetListItemByIndex(listView, cellIndex)
  end, initParam)
  self.m_toggleText.text = GM.GameTextModel:GetText("joker_card_show_missing")
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_toggleText.transform.parent)
  self:LogWindowAction(EBIType.UIActionType.Open, EBIReferType.UserClick, bGoldJoker and "golden" or "normal")
end

function AlbumJokerExchangeWindow:GetListItemByIndex(listView, cellIndex)
  if cellIndex < 0 then
    return
  end
  local item = listView:NewListViewItem("JokerExchangeCardSetsCell")
  local luaTable = item.gameObject:GetLuaTable()
  luaTable:Init(self, self.m_curArrSets[cellIndex + 1], self.m_model, self.m_bShowOnlyNone, function(selectCardId)
    self.m_selectCardId = selectCardId
    self.m_exchangeBtnLuaTable:SetUIEnabled(self.m_selectCardId ~= nil)
    EventDispatcher.DispatchEvent(EEventType.JokerUpdateExchangeSelect, {cardId = selectCardId})
  end)
  return item
end

function AlbumJokerExchangeWindow:UpdatePerSecond()
  if self.m_jokerEndTime ~= nil then
    local delta = math.max(0, self.m_jokerEndTime - GM.GameModel:GetServerTime())
    if delta == 0 then
      if self.m_closeGo.activeSelf then
        self.m_countdownText.text = GM.GameTextModel:GetText("joker_card_time_expired")
        self.m_closeGo:SetActive(false)
        self.canCloseByAndroidBack = false
      end
    else
      local str = TimeUtil.ParseTimeDescription(delta, 2, false, false)
      self.m_countdownText.text = str
    end
  end
end

function AlbumJokerExchangeWindow:IsSelectCard(cardId)
  return self.m_selectCardId == cardId
end

function AlbumJokerExchangeWindow:OnToggled()
  local bShow = self:IsShowNoneCards()
  if self.m_bShowOnlyNone == bShow then
    return
  end
  GM.UIManager:SetEventLock(true)
  self.m_bShowOnlyNone = bShow
  self.m_contentCanv:DOFade(0, 0.2)
  DelayExecuteFuncInView(function()
    self:_UpdateCurArrSet()
    self.m_loopListView:SetListItemCount(#self.m_curArrSets, false)
    self.m_loopListView:RefreshAllShownItem()
  end, 0.2, self)
  DelayExecuteFuncInView(function()
    self.m_contentCanv:DOFade(1, 0.2)
    GM.UIManager:SetEventLock(false)
  end, 0.4, self)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxButtonClick)
end

function AlbumJokerExchangeWindow:_UpdateCurArrSet()
  self.m_curArrSets = {}
  if self.m_bShowOnlyNone then
    for i, v in ipairs(self.m_arrCardSet) do
      local cur, max = self.m_model:GetSetCollectProgress(v.setId)
      if cur < max then
        self.m_curArrSets[#self.m_curArrSets + 1] = v
      end
    end
  else
    self.m_curArrSets = self.m_arrCardSet
  end
end

function AlbumJokerExchangeWindow:IsShowOnlyNone()
  return self.m_bShowOnlyNone
end

function AlbumJokerExchangeWindow:OnHelpBtnClick()
  GM.UIManager:OpenView(UIPrefabConfigName.AlbumJokerHelpWindow, self.m_model:GetType(), 2)
end

function AlbumJokerExchangeWindow:IsShowNoneCards()
  return self.m_toggle.isOn
end

function AlbumJokerExchangeWindow:OnCloseFinish()
  if self.m_openConfirmWindow and not self.m_bInitState then
    GM.UIManager:OpenView(UIPrefabConfigName.AlbumJokerExchangeConfirmWindow, self.m_activityType, self.m_selectCardId, self.m_bShowOnlyNone)
  elseif self.m_openConfirmWindow and self.m_bInitState then
    local isGoldenJoker = self.m_model:IsGoldenJokerCardInUse()
    self.m_model:JokerExchange(self.m_selectCardId)
    local prefab = isGoldenJoker and UIPrefabConfigName.AlbumGoldJokerExchangeCompleteWindow or UIPrefabConfigName.AlbumJokerExchangeCompleteWindow
    GM.UIManager:OpenView(prefab, self.m_activityType, self.m_selectCardId)
  end
  BaseWindow.OnCloseFinish(self)
end

function AlbumJokerExchangeWindow:OnExchangeClicked()
  if IsNil(self.m_selectCardId) then
    GM.UIManager:ShowPromptWithKey("joker_card_choose_btn_tip")
    return
  end
  self.m_openConfirmWindow = true
  self:Close()
end
