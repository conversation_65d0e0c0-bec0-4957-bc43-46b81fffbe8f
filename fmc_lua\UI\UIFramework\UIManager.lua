EViewState = {
  Invalid = -1,
  Cached = 0,
  WaitingOpen = 1,
  Loading = 2,
  Opened = 3,
  Closing = 4
}
local arrValidStateInSequence = {}
for _, state in pairs(EViewState) do
  if state ~= EViewState.Invalid then
    arrValidStateInSequence[#arrValidStateInSequence + 1] = state
  end
end
table.sort(arrValidStateInSequence)
local DEFAULT_WINDOW_MASK_ALPHA = 0.6078431372549019
local PropertyAnimationManager = PropertyAnimationManager
UIManager = {}
UIManager.__index = UIManager

function UIManager:Awake()
  GM:AddSingleton("UIManager", self)
  GameObject.DontDestroyOnLoad(self.gameObject)
  self:_ResetViewData()
  self.m_lockCountWithoutTarget = 0
  self.m_lockTargetCountMap = {}
  self.m_mapEventLockCallback = {}
  self.m_promptManager = PromptManager.Create()
  UIUtil.SetActive(self.m_maskLayer.gameObject, true)
  self:HideMask()
  self.m_bWindowMaskOn = false
  self.m_iClickIndex = 0
  
  function self.m_windowMaskEventTrigger.OnLuaPointerClick(eventData)
    local topWindow = self:GetOpenedTopViewByType(EViewType.Window)
    if topWindow and topWindow.canClickWindowMask then
      topWindow:OnWindowMaskClicked()
      return
    end
    local rectTrans = self.m_windowMaskImg.rectTransform
    local halfWidth = rectTrans.rect.width / 2
    local halfHeight = rectTrans.rect.height / 2
    local screenPos = PositionUtil.UICameraScreen2World(eventData.position)
    local isCorner = math.abs(math.abs(screenPos.x) - halfWidth) < 150 and 150 > math.abs(math.abs(screenPos.y) - halfHeight)
    if not isCorner then
      self.m_iClickIndex = 0
      return
    end
    if (self.m_iClickIndex == 0 or self.m_iClickIndex == 1) and screenPos.x < 0 and screenPos.y < 0 then
      self.m_iClickIndex = 1
    elseif self.m_iClickIndex == 1 and screenPos.x < 0 and screenPos.y > 0 then
      self.m_iClickIndex = 2
    elseif self.m_iClickIndex == 2 and screenPos.x > 0 and screenPos.y < 0 then
      self.m_iClickIndex = 3
    elseif self.m_iClickIndex == 3 and screenPos.x < 0 and screenPos.y < 0 then
      self.m_iClickIndex = 4
      self:OpenView(UIPrefabConfigName.GeneralMsgWindow, "+", CS.Crypt.CryptStringWithKey("yZ[D]_MX", "43862986"), "OK")
    else
      self.m_iClickIndex = 0
    end
  end
  
  if GameConfig.IsTestMode() then
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.TestMaskButton), GM.UIManager:GetCanvasRoot(), Vector3.zero, function(go)
      go.transform.anchoredPosition3D = V3Zero
      self.m_testMaskBtnGo = go
    end)
  end
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnGameModeChanged)
end

function UIManager:Start()
  self.m_canvasSize = self.m_scaler.referenceResolution
end

function UIManager:Destroy()
  self.gameObject:RemoveSelf()
  EventDispatcher.RemoveTarget(self)
end

function UIManager:_OnGameModeChanged()
  local arrOpenedViews = Table.ShallowCopy(self.m_mapViewDataList[EViewState.Opened])
  for _, viewData in ipairs(arrOpenedViews) do
    if viewData.view.OnGameModeChangeInBaseWindow then
      viewData.view:OnGameModeChangeInBaseWindow()
    end
  end
end

function UIManager:Update()
  if self.m_bOpenWaitingWindow and PropertyAnimationManager.uiLockFlyingCount == 0 then
    local viewData = self:_PollWaitingOpenViewData()
    self:OpenView(viewData.viewName, table.unpack(viewData.args, 1, viewData.args.n))
  end
  if self.shouldClearResources then
    ResourceLoader.ClearUnusedAssets()
    self.shouldClearResources = false
  end
end

function UIManager:OnBack()
  if self.m_isMaskVisible then
    return
  end
  if self:IsEventLock() then
    return
  end
  if GM.TutorialModel:HasAnyStrongTutorialOngoing() then
    return
  end
  local topView = self:GetOpenedTopView()
  if topView and topView.OnBack and topView.canCloseByAndroidBack then
    topView:OnBack()
  elseif self.allWindowClosed and not GM.TimelineManager:IsPlayingTimeline() then
    local eGameMode = GM.SceneManager:GetGameMode()
    if eGameMode ~= EGameMode.Loading then
      if GM.SceneManager:GetGameMode() == EGameMode.Main then
        self:OpenView(UIPrefabConfigName.TwoButtonWindow, "quit_window_title", "quit_window_desc", "quit_window_button_yes", "quit_window_button_no", function()
          PlatformInterface.ExitGame()
        end, nil, true)
      else
        GM.SceneManager:ChangeGameMode(EGameMode.Main)
      end
    end
  end
end

function UIManager:_ResetViewData()
  if self.m_mapViewDataList ~= nil then
    for state, viewDataList in pairs(self.m_mapViewDataList) do
      for _, viewData in ipairs(viewDataList) do
        if viewData.view ~= nil then
          viewData.view:Destroy()
        end
      end
    end
  end
  self.m_mapViewDataList = {}
  for _, state in ipairs(arrValidStateInSequence) do
    self.m_mapViewDataList[state] = {}
  end
  self:_UpdateViewData()
end

function UIManager:_UpdateViewData()
  self.m_mapViewName2ViewState = {}
  for _, state in ipairs(arrValidStateInSequence) do
    for i, viewData in ipairs(self.m_mapViewDataList[state]) do
      self.m_mapViewName2ViewState[viewData.viewName] = state
    end
  end
  local openedList = self.m_mapViewDataList[EViewState.Opened]
  table.sort(openedList, function(a, b)
    return a.view:GetSortingOrder() < b.view:GetSortingOrder()
  end)
  self.m_mapTopView = {}
  for i, viewData in ipairs(openedList) do
    if viewData.view == nil or viewData.view.eViewType == nil then
      local info = "UIManager: missing viewType in view: " .. (viewData.viewName or "nil")
      Log.Error(info)
    else
      self.m_mapTopView[viewData.view.eViewType] = viewData.view
    end
  end
  local hasActiveWindow = self.m_mapTopView[EViewType.Window] ~= nil or #self.m_mapViewDataList[EViewState.Loading] > 0 or 0 < #self.m_mapViewDataList[EViewState.Closing]
  self.m_bOpenWaitingWindow = not hasActiveWindow and 0 < #self.m_mapViewDataList[EViewState.WaitingOpen]
  self.allWindowClosed = not hasActiveWindow and #self.m_mapViewDataList[EViewState.WaitingOpen] == 0
  self.shouldClearResources = self.allWindowClosed
end

function UIManager:_AddLoadingViewData(viewName)
  local loadingDataList = self.m_mapViewDataList[EViewState.Loading]
  loadingDataList[#loadingDataList + 1] = {viewName = viewName}
  self:_UpdateViewData()
end

function UIManager:_PollWaitingOpenViewData()
  Log.Assert(#self.m_mapViewDataList[EViewState.WaitingOpen] > 0, "[UIManager]Poll View Data When List Is Empty")
  local viewData = table.remove(self.m_mapViewDataList[EViewState.WaitingOpen], 1)
  self:_UpdateViewData()
  return viewData
end

function UIManager:_RemoveClosingViewData(view)
  for i, data in ipairs(self.m_mapViewDataList[EViewState.Closing]) do
    if data.view == view then
      table.remove(self.m_mapViewDataList[EViewState.Closing], i)
      self:_UpdateViewData()
      return true
    end
  end
  return false
end

function UIManager:_ShiftViewData(viewTarget, sourceState, targetState, loadedView)
  local index = 0
  for i, data in ipairs(self.m_mapViewDataList[sourceState]) do
    if type(viewTarget) == "string" and data.viewName == viewTarget or type(viewTarget) == "table" and data.view == viewTarget then
      index = i
      if sourceState == EViewState.WaitingOpen then
        break
      end
    end
  end
  if 0 < index then
    local data = self.m_mapViewDataList[sourceState][index]
    if loadedView then
      data.view = loadedView
    end
    table.remove(self.m_mapViewDataList[sourceState], index)
    self.m_mapViewDataList[targetState][#self.m_mapViewDataList[targetState] + 1] = data
    self:_UpdateViewData()
  end
end

function UIManager:OpenViewWhenIdle(viewName, ...)
  Log.Info("UIManager OpenView " .. tostring(viewName))
  table.insert(self.m_mapViewDataList[EViewState.WaitingOpen], {
    viewName = viewName,
    args = table.pack(...)
  })
  self:_UpdateViewData()
end

function UIManager:OpenView(viewName, ...)
  Log.Info("UIManager OpenView " .. tostring(viewName))
  if not self:_CheckViewNameValid(viewName) then
    return
  end
  MicrofunProfiler.Instance:StartSession(viewName)
  if self:_GetView(viewName, EViewState.Cached) ~= nil then
    self:_ReOpenView(viewName, ...)
  else
    self:_LoadView(viewName, ...)
  end
end

function UIManager:_LoadView(viewName, ...)
  self:SetEventLock(true)
  local args = table.pack(...)
  local callback = function(go)
    self:SetEventLock(false)
    if self:_GetViewState(viewName) == EViewState.Invalid then
      go:RemoveSelf()
      return
    end
    go:SetActive(false)
    local view = go:GetLuaTable()
    if type(view) ~= "table" then
      local errInfo = "UIManager: invalid view type: " .. type(view) .. " in viewName: " .. (viewName or "nil")
      Log.Error(errInfo)
      go:RemoveSelf()
      local index
      for i, data in ipairs(self.m_mapViewDataList[EViewState.Loading]) do
        if data.viewName == viewName then
          index = i
        end
      end
      table.remove(self.m_mapViewDataList[EViewState.Loading], index)
      self:_UpdateViewData()
      return
    end
    view.name = viewName
    self:_OnViewOpen(EViewState.Loading, view, table.unpack(args, 1, args.n))
  end
  self:_AddLoadingViewData(viewName)
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(viewName), self.m_canvasRootRect, Vector3.zero, callback)
end

function UIManager:_ReOpenView(viewName, ...)
  for _, viewData in ipairs(self.m_mapViewDataList[EViewState.Cached]) do
    if viewData.viewName == viewName then
      self:_OnViewOpen(EViewState.Cached, viewData.view, ...)
    end
  end
end

function UIManager:_OnViewOpen(sourceState, view, ...)
  if not view:BeforeOpenCheck(...) then
    self:_ShiftViewData(view.name, sourceState, EViewState.Cached, view)
    if GameConfig.IsTestMode() then
      self:ShowPrompt("弹窗开启检查失败:" .. tostring(view.name))
      Log.Info("弹窗开启检查失败:" .. tostring(view.name))
    end
    return
  end
  view.gameObject:SetActive(true)
  self:_SetSortingOrder(view)
  self:_ShiftViewData(view.name, sourceState, EViewState.Opened, view)
  if view.OnOpenView == nil then
    local info = "UIManager: OnOpenView is required in view " .. (view.name or "nil")
    Log.Error(info)
    return
  end
  view:OnOpenView(...)
  if view.eViewType == EViewType.Window then
    self:_UpdateWindowMask()
  end
  EventDispatcher.DispatchEvent(EEventType.OpenView, {
    name = view.name,
    needBlurEffect = view.needBlurEffect
  })
end

function UIManager:_SetSortingOrder(view)
  if view.SetSortingOrder == nil then
    local info = "UIManager: SetSortingOrder is required in view " .. (view.name or "nil")
    Log.Error(info)
    return
  end
  if view.sortingOrder then
    view:SetSortingOrder(view.sortingOrder)
  else
    local curOrder = self:_GetTopViewSortingOrder() + self:GetDeltaSortingOrder()
    view:SetSortingOrder(curOrder)
  end
end

function UIManager:GetDeltaSortingOrder()
  return 10
end

function UIManager:CloseView(view)
  if IsString(view) then
    view = self:GetOpenedViewByName(view)
  end
  if view then
    Log.Info("UIManager CloseView " .. tostring(view.name))
  end
  if view and self:_GetViewState(view.name) == EViewState.Opened then
    GM.BIManager:LogProfilingSession(tostring(view.name))
    self:_ShiftViewData(view, EViewState.Opened, EViewState.Closing)
    EventDispatcher.DispatchEvent(EEventType.OnViewWillClose, {
      name = view.name
    })
    view:OnCloseView()
    if view.eViewType == EViewType.Window then
      self:_UpdateWindowMask()
    end
  end
end

function UIManager:OnViewCloseFinish(view)
  if view.eCloseType == EViewCloseType.Destroy then
    self:_RemoveClosingViewData(view)
  elseif view.eCloseType == EViewCloseType.Hide then
    self:_ShiftViewData(view, EViewState.Closing, EViewState.Cached)
  end
  EventDispatcher.DispatchEvent(EEventType.CloseView, {
    name = view.name
  })
end

function UIManager:CloseAllOpenedWindow()
  local i = #self.m_mapViewDataList[EViewState.Opened]
  while 0 < i do
    if self.m_mapViewDataList[EViewState.Opened][i].view.eViewType == EViewType.Window then
      self:CloseView(self.m_mapViewDataList[EViewState.Opened][i].view)
    end
    i = i - 1
  end
end

function UIManager:GetOpenedTopView()
  local viewData = self.m_mapViewDataList[EViewState.Opened][#self.m_mapViewDataList[EViewState.Opened]]
  return self:_GetViewGoFromViewData(viewData)
end

function UIManager:_GetViewGoFromViewData(viewData)
  if viewData and viewData.view then
    local go = viewData.view.gameObject
    if go:IsNull() then
      Log.Error("UIManager:GetOpenedTopView " .. viewData.viewName)
      return nil
    end
  end
  return viewData and viewData.view or nil
end

function UIManager:_GetTopViewSortingOrder()
  local openedViews = self.m_mapViewDataList[EViewState.Opened]
  for i = #openedViews, 1, -1 do
    local view = self:_GetViewGoFromViewData(openedViews[i])
    if view and view.sortingOrder == nil then
      return view:GetSortingOrder()
    end
  end
  return 0
end

function UIManager:GetOpenedTopViewByType(viewType)
  return self.m_mapTopView and self.m_mapTopView[viewType] or nil
end

function UIManager:GetOpenedViewByName(viewName)
  return self:_GetView(viewName, EViewState.Opened)
end

function UIManager:_GetView(viewName, state)
  for _, viewData in ipairs(self.m_mapViewDataList[state]) do
    if viewData.viewName == viewName then
      return viewData.view
    end
  end
  return nil
end

function UIManager:IsViewExisting(viewName)
  return self:_GetViewState(viewName) >= EViewState.WaitingOpen
end

function UIManager:IsViewOpen(viewName)
  local state = self:_GetViewState(viewName)
  return state >= EViewState.WaitingOpen and state ~= EViewState.Closing
end

function UIManager:GetOpenedViewCountByType(viewType)
  local count = 0
  for _, viewData in ipairs(self.m_mapViewDataList[EViewState.Opened]) do
    if viewData.view.eViewType == viewType then
      count = count + 1
    end
  end
  return count
end

function UIManager:GetCanvasSize()
  return self.m_canvasSize
end

function UIManager:GetCanvasRoot()
  return self.m_canvasRootRect
end

function UIManager:GetCanvas()
  return self.m_canvas
end

function UIManager:OnSceneChange()
  self:_ResetViewData()
  self:_UpdateWindowMask(true)
end

function UIManager:OnGameModeChange()
  self:_UpdateWindowMask(true)
end

function UIManager:LoadComponent(name, view, callback, ...)
  if not self:_CheckViewNameValid(name) then
    return
  end
  local args = table.pack(...)
  local loadCallback = function(go)
    local component = go:GetLuaTable()
    component.name = name
    component:Open(table.unpack(args, 1, args.n))
    callback(view, component)
  end
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(name), view.gameObject.transform, Vector3.zero, loadCallback)
end

function UIManager:_CheckViewNameValid(strViewName)
  if not GM.DataResource.UIPrefabConfig:GetConfig(strViewName) then
    Log.Error("no prefab for " .. strViewName .. ", please config in UIPrefabConfig")
    return false
  end
  return true
end

function UIManager:SetEventLock(locked, target, callback)
  local delta = locked and 1 or -1
  if target == nil then
    self.m_lockCountWithoutTarget = self.m_lockCountWithoutTarget + delta
    Log.Assert(self.m_lockCountWithoutTarget >= 0, "lockCount不应为负数")
  else
    local lockCount = self.m_lockTargetCountMap[target]
    if lockCount == nil and not locked then
      return
    end
    lockCount = (lockCount or 0) + delta
    Log.Assert(0 <= lockCount, "lockCount不应为负数")
    self.m_lockTargetCountMap[target] = lockCount
    if callback ~= nil then
      if locked then
        self:_AddEventLockCallback(target, callback)
      else
        self:_RemoveEventLockCallback(target, callback)
      end
    end
  end
  self:UpdateEventLockRect()
end

function UIManager:UpdateEventLockRect()
  local bLock = self:IsEventLock()
  if not self.m_eventLockRect:IsNull() then
    self.m_eventLockRect.localScale = bLock and V3One or Vector3.zero
  end
  if not bLock then
    EventDispatcher.DispatchEvent(EEventType.EventLockClear)
  end
end

function UIManager:_AddEventLockCallback(target, callback)
  if self.m_mapEventLockCallback[target] ~= nil then
    Log.Error("[UIManager:_AddEventLockCallback] target有已经绑定的callback")
  end
  self.m_mapEventLockCallback[target] = callback
  EventDispatcher.AddListener(EEventType.OnEventLockMaskClicked, target, callback)
end

function UIManager:_RemoveEventLockCallback(target, callback)
  if self.m_mapEventLockCallback[target] ~= callback then
    Log.Error("[UIManager:_RemoveEventLockCallback] target没有绑定对应callback")
  end
  self.m_mapEventLockCallback[target] = nil
  EventDispatcher.RemoveListener(EEventType.OnEventLockMaskClicked, target)
end

function UIManager:OnEventLockMaskClick()
  Log.Debug("EventLockClick")
  if not Table.IsEmpty(self.m_mapEventLockCallback) then
    EventDispatcher.DispatchEvent(EEventType.OnEventLockMaskClicked)
  end
end

function UIManager:RemoveAllEventLocks(target)
  self.m_lockTargetCountMap[target] = nil
  if self.m_mapEventLockCallback[target] then
    self.m_mapEventLockCallback[target] = nil
    if not next(self.m_mapEventLockCallback) then
      EventDispatcher.RemoveListener(EEventType.OnEventLockMaskClicked, target)
    end
  end
  self:UpdateEventLockRect()
end

function UIManager:IsEventLock(ignorePopupLock)
  if self.m_lockCountWithoutTarget > 0 then
    return true
  end
  if self.m_bEventLockUntilNextPopup and not ignorePopupLock then
    return true
  end
  for _, count in pairs(self.m_lockTargetCountMap) do
    if 0 < count then
      return true
    end
  end
  return false
end

function UIManager:SetEventLockUntilNextPopup()
  self.m_bEventLockUntilNextPopup = true
  self:UpdateEventLockRect()
end

function UIManager:CancelEventLockUntilNextPopup()
  self.m_bEventLockUntilNextPopup = false
  self:UpdateEventLockRect()
end

function UIManager:IsEventLockUntilNextPopup()
  return self.m_bEventLockUntilNextPopup
end

function UIManager:OnChainPopuped()
  self.m_bEventLockUntilNextPopup = false
  self:UpdateEventLockRect()
end

function UIManager:_GetViewState(strViewName)
  return self.m_mapViewName2ViewState[strViewName] or EViewState.Invalid
end

function UIManager:_ShouldShowWindowMask()
  local hasMask = false
  local maskAlpha = 0
  for _, viewData in ipairs(self.m_mapViewDataList[EViewState.Opened]) do
    if viewData.view.eViewType == EViewType.Window and viewData.view.showWindowMask then
      hasMask = true
      local alpha = viewData.view.windowMaskAlpha or EWindowMaskAlpha.Default
      if maskAlpha < alpha then
        maskAlpha = alpha
      end
    end
  end
  return hasMask, maskAlpha
end

function UIManager:_UpdateWindowMask(changeScene)
  local topWindow = self:GetOpenedTopViewByType(EViewType.Window)
  local hasMask, maskAlpha = self:_ShouldShowWindowMask()
  if hasMask then
    if not self.m_bWindowMaskOn then
      self.m_windowMaskCanvas.gameObject:SetActive(true)
      self.m_bWindowMaskOn = true
      if self.m_windowMaskTween ~= nil then
        self.m_windowMaskTween:Kill()
      end
      self.m_windowMaskTween = self.m_windowMaskImg:DOFade(maskAlpha, 0.16666666666666666)
      self.m_windowMaskTween:OnComplete(function()
        self.m_windowMaskTween = nil
      end)
    elseif self.m_windowMaskImg.color.a ~= maskAlpha then
      if self.m_windowMaskTween ~= nil then
        self.m_windowMaskTween:Kill()
      end
      self.m_windowMaskImg.color = CSColor(0, 0, 0, maskAlpha)
    end
    self.m_windowMaskCanvas.sortingOrder = topWindow:GetSortingOrder() - 2
    self.m_iClickIndex = 0
  elseif self.m_bWindowMaskOn or changeScene then
    self.m_bWindowMaskOn = false
    self.m_iClickIndex = 0
    if self.m_windowMaskTween ~= nil then
      self.m_windowMaskTween:Kill()
    end
    if changeScene then
      if self.m_windowMaskCanvas and not self.m_windowMaskCanvas:IsNull() then
        self.m_windowMaskCanvas.gameObject:SetActive(false)
        self.m_windowMaskImg.color = CSColor(0, 0, 0, 0)
      end
    else
      self.m_windowMaskTween = self.m_windowMaskImg:DOFade(0, 0.16666666666666666)
      self.m_windowMaskTween:OnComplete(function()
        self.m_windowMaskTween = nil
        self.m_windowMaskCanvas.gameObject:SetActive(false)
      end)
    end
  end
end

function UIManager:ShowPromptWithKey(textKey, screenPos, stayDuration, bTMP, forTest, baseSortingOrder, specialPrefab)
  self:ShowPrompt(GM.GameTextModel:GetText(textKey), screenPos, stayDuration, bTMP, forTest, baseSortingOrder, specialPrefab)
end

function UIManager:ShowPrompt(text, screenPos, stayDuration, bTMP, forTest, baseSortingOrder, specialPrefab)
  screenPos = screenPos and PositionUtil.UICameraScreen2World(screenPos)
  self.m_promptManager:Show(text, screenPos, stayDuration, bTMP, forTest, baseSortingOrder, specialPrefab)
end

function UIManager:ShowTestPrompt(text)
  if not GM.UIManager:CanShowTestUI() then
    return
  end
  self:ShowPrompt(text, nil, nil, nil, true)
end

function UIManager:GetCurrentPromptId()
  return self.m_promptManager:GetCurrentId()
end

function UIManager:ShowMask()
  if self.m_isMaskVisible then
    Log.Warning("MaskLayer already visible")
    return
  end
  self.m_isMaskVisible = true
  self.m_maskLayer:Show()
end

function UIManager:HideMask()
  self.m_isMaskVisible = false
  self.m_maskLayer:Hide()
end

function UIManager:IsMaskVisible()
  return self.m_isMaskVisible == true
end

function UIManager:HideTestInfo()
  if self.m_performanceInfoGo then
    self.m_performanceInfoGo:SetActive(false)
  end
  if self.m_testMaskBtnGo then
    self.m_testMaskBtnGo:SetActive(false)
  end
  self.bHideTest = true
end

function UIManager:CanShowTestUI()
  return GameConfig.IsTestMode() and PlayerPrefs.GetInt(EPlayerPrefKey.ShowItemTestInfo, 0) ~= 0
end
