AlbumUpgradeTipWindow = setmetatable({}, AlbumActivityBaseWindow)
AlbumUpgradeTipWindow.__index = AlbumUpgradeTipWindow

function AlbumUpgradeTipWindow:Init(activityType, redButtonCallback, greenButtonCallback)
  AlbumActivityBaseWindow.Init(self, activityType)
  self.m_redButtonCallback = redButtonCallback
  self.m_greenButtonCallback = greenButtonCallback
  local surplusStars = self.m_model:GetSurplusCardStar()
  self.m_descText.text = GM.GameTextModel:GetText("album_upgrade_notice_desc", surplusStars)
end

function AlbumUpgradeTipWindow:OnRedClick()
  if self.m_redButtonCallback then
    self.m_redButtonCallback(self)
  else
    self:Close()
  end
end

function AlbumUpgradeTipWindow:OnGreenClick()
  if self.m_greenButtonCallback then
    self.m_greenButtonCallback(self)
  else
    self:Close()
  end
end
