PinataRewardProgress = {}
PinataRewardProgress.__index = PinataRewardProgress
local SLIDER_SIDE_MARGIN = 120

function PinataRewardProgress:Init()
  local model = GM.ActivityManager:GetModel(ActivityType.Pinata)
  if model == nil or model:GetLevelConfigs() == nil then
    return
  end
  if self.m_arrCells == nil then
    self.m_arrCells = {
      self.m_cellOrigin:GetLuaTable()
    }
  end
  local currentLevel = model:GetLevel()
  local maxLevel = model:GetMaxRewardLevel()
  local index = 1
  for level = 1, maxLevel do
    if self.m_arrCells[index] == nil then
      self.m_arrCells[index] = GameObject.Instantiate(self.m_cellOrigin, self.m_cellGroupRectTrans):GetLuaTable()
    else
      UIUtil.SetActive(self.m_arrCells[index].gameObject, true)
    end
    self.m_arrCells[index]:Init(level)
    index = index + 1
  end
  local extraReward = model:GetExtraReward()
  if extraReward ~= nil then
    if self.m_arrCells[index] == nil then
      self.m_arrCells[index] = GameObject.Instantiate(self.m_cellOrigin, self.m_cellGroupRectTrans):GetLuaTable()
    else
      UIUtil.SetActive(self.m_arrCells[index].gameObject, true)
    end
    self.m_arrCells[index]:Init(PinataModel.ExtraRewardLevel)
    index = index + 1
  end
  for i = index, #self.m_arrCells do
    UIUtil.SetActive(self.m_arrCells[i], false)
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_cellGroupRectTrans)
  local preferredWitdh = self.m_cellGroupLayoutGroup.preferredWidth
  UIUtil.SetSizeDelta(self.m_contentRectTrans, preferredWitdh)
  self.m_slider.value = currentLevel > maxLevel and 1 or (currentLevel - 1) / (maxLevel - (extraReward ~= nil and 0 or 1))
  local halfScrollRectWidth = self.m_scrollRect.transform.sizeDelta.x / 2
  local localPosX = self.m_arrCells[math.min(currentLevel, maxLevel)].transform.localPosition.x
  self.m_scrollRect.horizontalNormalizedPosition = math.min(1, math.max(0, localPosX - halfScrollRectWidth) / (preferredWitdh - halfScrollRectWidth * 2))
end

local Level2IconName = {}
PinataRewardProgressCell = {}
PinataRewardProgressCell.__index = PinataRewardProgressCell

function PinataRewardProgressCell:Init(index)
  local model = GM.ActivityManager:GetModel(ActivityType.Pinata)
  if model == nil then
    return
  end
  local currentLevel = model:GetLevel()
  local currentFinishedLevel = model:GetFinishLevel()
  local extraReward = index == PinataModel.ExtraRewardLevel
  local acquired, canAcquire
  if extraReward then
    local maxRewardLevel = model:GetMaxRewardLevel()
    acquired = currentLevel > maxRewardLevel
    canAcquire = currentFinishedLevel > maxRewardLevel
  else
    acquired = index < currentLevel
    canAcquire = index <= currentFinishedLevel
  end
  self.m_avatarBgLeftImg.sprite = extraReward and self.m_avatarBgSprite3 or canAcquire and self.m_avatarBgSprite1 or self.m_avatarBgSprite2
  self.m_avatarBgRightImg.sprite = extraReward and self.m_avatarBgSprite3 or canAcquire and self.m_avatarBgSprite1 or self.m_avatarBgSprite2
  self.m_rewardBgImg.sprite = extraReward and self.m_rewardBgSprite3 or canAcquire and self.m_rewardBgSprite1 or self.m_rewardBgSprite2
  UIUtil.SetActive(self.m_finalRewardTextGo, extraReward)
  UIUtil.SetActive(self.m_checkIconGo, acquired)
  UIUtil.SetActive(self.m_avatarBgFrameGo, currentLevel == index)
  UIUtil.SetActive(self.m_rewardBgFrameGo, currentLevel == index)
  if not extraReward then
    SpriteUtil.SetImage(self.m_avatarImg, Level2IconName[index] or Level2IconName[#Level2IconName], true)
  else
    UIUtil.SetActive(self.m_avatarImg.gameObject, false)
  end
  if not acquired then
    local rewards = (model:GetLevelConfigs() or Table.Empty)[index]
    if rewards == nil or rewards.rewards == nil then
      return
    end
    rewards = rewards.rewards
    if self.m_items == nil then
      self.m_items = {
        self.m_item1,
        self.m_item2,
        self.m_item3
      }
    end
    for i = 1, 3 do
      if rewards[i] ~= nil then
        UIUtil.SetActive(self.m_items[i].gameObject, true)
        self.m_items[i]:Init(rewards[i])
      else
        UIUtil.SetActive(self.m_items[i].gameObject, false)
      end
    end
  end
end
