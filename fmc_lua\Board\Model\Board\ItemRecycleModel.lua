ItemRecycleModel = {}
ItemRecycleModel.__index = ItemRecycleModel
EItemRecycleTag = {
  InBoard = 1,
  InInventory = 2,
  InCache = 3,
  InInstrument = 4
}

function ItemRecycleModel:Init()
end

function ItemRecycleModel:LateInit()
  self.m_isOn, self.m_nDivisor, self.m_nMinEnergyCount = GM.ConfigModel:UseItemAutoRecycle()
end

function ItemRecycleModel:GetCurMaxSeries()
  local lastDay = GM.MainBoardModel:GetCurOrderDay() - 1
  local producerType = GM.MainBoardModel:GetProducerTypeByOpenDay(lastDay)
  if not producerType then
    return nil
  end
  local nMaxSeries = GM.ItemDataModel:GetItemSeries(producerType)
  return nMaxSeries, producerType
end

function ItemRecycleModel:ShouldPopRecycleWindow(lastDay)
  if not self.m_isOn then
    return false
  end
  local nMaxSeries, producerType = self:GetCurMaxSeries()
  if not nMaxSeries then
    return false
  end
  if GM.MainBoardModel:GetOneBoardItemByType(producerType) ~= nil then
    return true
  end
  local arrDishes, arrInstrus, arrOthers, mapInInstrumentItems = GM.ItemRecycleModel:GetRecycleData()
  for _, tb in ipairs({
    arrDishes,
    arrInstrus,
    arrOthers,
    mapInInstrumentItems
  }) do
    if next(tb) ~= nil then
      return true
    end
  end
  return false
end

function ItemRecycleModel:GetRecycleData()
  local nMaxSeries = self:GetCurMaxSeries()
  if not nMaxSeries then
    Log.Error("不符合棋子回收要求")
    return nil
  end
  local arrDishes = {}
  local arrInstrus = {}
  local arrOthers = {}
  local itemType, nSeries
  local itemDataModel = GM.ItemDataModel
  local arrAllCookCmp = GM.MainBoardModel:GetAllItemCookCmp(true, true)
  local mapInInstrumentItems = {}
  for _, cookCmp in ipairs(arrAllCookCmp) do
    if cookCmp:GetState() <= EItemCookState.CanCook then
      for _, materialItem in ipairs(cookCmp:GetMaterialItems()) do
        itemType = materialItem:GetType()
        nSeries = itemDataModel:GetItemSeries(itemType)
        if nSeries and nMaxSeries >= nSeries then
          mapInInstrumentItems[materialItem] = cookCmp
          materialItem.eRecycleTag = EItemRecycleTag.InInstrument
        end
      end
    end
  end
  local funcCategorize = function(itemModel, eTag)
    itemType = itemModel:GetType()
    if itemDataModel:IsPd(itemType) then
      return
    end
    nSeries = itemDataModel:GetItemSeries(itemType)
    if nSeries and nSeries <= nMaxSeries then
      if itemDataModel:IsDishes(itemType) then
        arrDishes[#arrDishes + 1] = itemModel
      elseif itemDataModel:IsInstrument(itemType) then
        if itemModel.GetComponent then
          local cookCmp = itemModel:GetComponent(ItemCook)
          local arrMaterialCodes = cookCmp:GetCurMaterialsArray()
          local s
          for _, materialCode in ipairs(arrMaterialCodes) do
            s = itemDataModel:GetItemSeries(materialCode)
            if not s or s > nMaxSeries then
              return
            end
          end
        end
        arrInstrus[#arrInstrus + 1] = itemModel
      else
        arrOthers[#arrOthers + 1] = itemModel
      end
      itemModel.eRecycleTag = eTag
    end
  end
  local boardModel = GM.MainBoardModel
  local mapBoardItems = boardModel:GetAllBoardItems()
  for itemModel, _ in pairs(mapBoardItems) do
    funcCategorize(itemModel, EItemRecycleTag.InBoard)
  end
  local itemModel
  for i = 1, boardModel:GetStoredItemCount() do
    itemModel = boardModel:GetStoredItem(i)
    funcCategorize(itemModel, EItemRecycleTag.InInventory)
  end
  for i = 1, boardModel:GetCachedItemCount() do
    itemType = boardModel:GetCachedItem(i)
    funcCategorize(RecycleFakeItem.Create(itemType), EItemRecycleTag.InCache)
  end
  return arrDishes, arrInstrus, arrOthers, mapInInstrumentItems
end

function ItemRecycleModel:CalculateEnergyCount(arrDishes, arrInstrus, arrOthers, mapInInstrumentItems)
  local totalScore = 0
  local itemType
  local itemDataModel = GM.ItemDataModel
  for _, arr in ipairs({
    arrDishes,
    arrInstrus,
    arrOthers
  }) do
    for _, itemModel in ipairs(arr) do
      itemType = itemModel:GetType()
      totalScore = totalScore + itemDataModel:GetItemScore(itemType)
    end
  end
  for itemModel, cookCmp in pairs(mapInInstrumentItems) do
    itemType = itemModel:GetType()
    totalScore = totalScore + itemDataModel:GetItemScore(itemType)
  end
  local energyCount = math.ceil(totalScore / self.m_nDivisor)
  energyCount = math.max(energyCount, self.m_nMinEnergyCount)
  return energyCount
end

function ItemRecycleModel:_GenerateItemTypeCount(arrDishes, arrInstrus, arrOthers, mapInInstrumentItems)
  local map = {}
  local itemType
  for _, arr in ipairs({
    arrDishes,
    arrInstrus,
    arrOthers
  }) do
    for _, itemModel in ipairs(arr) do
      itemType = itemModel:GetType()
      map[itemType] = (map[itemType] or 0) + 1
    end
  end
  for itemModel, cookCmp in pairs(mapInInstrumentItems) do
    itemType = itemModel:GetType()
    map[itemType] = (map[itemType] or 0) + 1
  end
  return map
end

function ItemRecycleModel:ConfirmRecycle(arrDishes, arrInstrus, arrOthers, mapInInstrumentItems)
  local mapItemTypeCount = self:_GenerateItemTypeCount(arrDishes, arrInstrus, arrOthers, mapInInstrumentItems)
  local energyCount = self:CalculateEnergyCount(arrDishes, arrInstrus, arrOthers, mapInInstrumentItems)
  for itemModel, cookCmp in pairs(mapInInstrumentItems) do
    cookCmp:RemoveMaterial(itemModel)
  end
  local boardModel = GM.MainBoardModel
  local itemStoreModel = boardModel:GetStoreModel()
  for _, arr in ipairs({
    arrDishes,
    arrInstrus,
    arrOthers
  }) do
    for _, itemModel in ipairs(arr) do
      if itemModel.GetComponent then
        local cookCmp = itemModel:GetComponent(ItemCook)
        if cookCmp and cookCmp:GetState() ~= EItemCookState.Empty then
          Log.Error("棋子回收逻辑错误")
          return false
        end
      end
      if itemModel.eRecycleTag == EItemRecycleTag.InBoard then
        boardModel:RemoveItem(itemModel)
      elseif itemModel.eRecycleTag == EItemRecycleTag.InInventory then
        itemStoreModel:RemoveItemByItemModel(itemModel)
        boardModel:RemoveItem(itemModel)
      elseif itemModel.eRecycleTag == EItemRecycleTag.InCache then
        boardModel:RemoveCachedItemByCode(itemModel:GetType())
      end
    end
  end
  local rewards = {
    {
      [PROPERTY_TYPE] = EPropertyType.Energy,
      [PROPERTY_COUNT] = energyCount
    }
  }
  RewardApi.CryptRewards(rewards)
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.ItemRecycle)
  local info = {
    items = GM.BIManager:TableToString(mapItemTypeCount),
    rewards = GM.BIManager:TableToString({
      [EPropertyType.Energy] = energyCount
    })
  }
  GM.BIManager:LogAction(EBIType.ItemRecycle, info)
  local mapBoardItems = boardModel:GetAllBoardItems()
  local itemDataModel = GM.ItemDataModel
  local nMaxSeries = self:GetCurMaxSeries()
  local itemType, nSeries
  for itemModel, _ in pairs(mapBoardItems) do
    itemType = itemModel:GetType()
    if itemType == ItemType.Bubble then
      local itemBubble = itemModel:GetComponent(ItemBubble)
      if itemBubble then
        local innerType = itemBubble:GetInnerItemCode()
        nSeries = itemDataModel:GetItemSeries(innerType)
        if nSeries and nMaxSeries >= nSeries then
          itemBubble:OnSpeedUp()
        end
      end
    end
  end
  return true
end

RecycleFakeItem = {}
RecycleFakeItem.__index = RecycleFakeItem

function RecycleFakeItem.Create(type)
  return setmetatable({type = type}, RecycleFakeItem)
end

function RecycleFakeItem:GetType()
  return self.type
end

function RecycleFakeItem:GetId()
  return 0
end
