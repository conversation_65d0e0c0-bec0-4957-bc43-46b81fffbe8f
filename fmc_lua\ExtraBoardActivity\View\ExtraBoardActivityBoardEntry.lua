ExtraBoardActivityBoardEntry = {}
ExtraBoardActivityBoardEntry.__index = ExtraBoardActivityBoardEntry

function ExtraBoardActivityBoardEntry:Awake()
  self:_AddListeners()
end

function ExtraBoardActivityBoardEntry:Init(model, orderArea)
  self.m_activityType = model:GetType()
  self.m_model = model
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  self.m_orderArea = orderArea
  self.m_iconArea:Init(self.m_activityType)
  self.m_iconArea:SetInBoardView()
  self:UpdatePerSecond()
  if self.gameObject.activeInHierarchy then
    self:_AddListeners()
  end
end

function ExtraBoardActivityBoardEntry:_AddListeners()
  if not self.m_activityDefinition then
    return
  end
end

function ExtraBoardActivityBoardEntry:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function ExtraBoardActivityBoardEntry:UpdatePerSecond()
  local nextStateTime = self.m_model and self.m_model:GetNextStateTime()
  if nextStateTime ~= nil then
    local delta = math.max(0, nextStateTime - GM.GameModel:GetServerTime())
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function ExtraBoardActivityBoardEntry:OnMaskClicked()
  if not self.m_model:HasWindowOpenedOnce(ActivityState.Started) then
    GM.UIManager:OpenView(self.m_activityDefinition.ReadyWindowPrefabName, self.m_activityType, true)
  else
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, true)
  end
end

function ExtraBoardActivityBoardEntry:GetIconArea()
  return self.m_iconArea
end
