require("Model.Activity.ActivityDefinition")
SceneManager = {}
SceneManager.__index = SceneManager
EGameMode = {
  Loading = "loading",
  Main = "main",
  Board = "board",
  ExtraBoard = "extraBoard"
}
setmetatable(EGameMode, {
  __index = function(_, key)
    Log.Error("EGameMode try to index a nil key: " .. tostring(key))
    return nil
  end
})
local GameRoot, LoadingRoot
local FindGameRootAndLoadingRootObject = function()
  GameRoot = Root:GetChild(0).gameObject
  LoadingRoot = Root:GetChild(1).gameObject
end

function SceneManager:Init()
  self.m_eGameMode = EGameMode.Loading
  self:_StartSceneTick()
  FindGameRootAndLoadingRootObject()
end

function SceneManager:SetLoggingFlag(flag)
  self._enableLoggingWhenLoadingScene = flag and flag ~= "0"
  if self._enableLoggingWhenLoadingScene then
    Debug.Log("SceneManager: enable logging with flag " .. tostring(flag))
  end
end

function SceneManager:StartLoading()
  LoadingRoot:SetActive(true)
  ApplicationManager.Instance:ClosePreInitView()
end

function SceneManager:UnloadLoading()
  if self.m_tmpScene then
    local childs = self.m_tmpScene.Scene:GetRootGameObjects()
    for i = 0, childs.Length - 1 do
      if childs[i].name == "EventSystem" then
        childs[i]:SetActive(true)
      end
    end
    self.m_tmpScene = nil
  end
  Recycle(LoadingRoot)
  LoadingRoot = nil
  GameRoot = nil
  Debug.Log("Unloading Loading")
  self:ChangeGameMode(EGameMode.Main)
  GameObjectPool.recycleEnabled = true
  AddressableLoader.DisableLogging()
  self:EnterGame()
  GM.UIManager:OpenView(UIPrefabConfigName.BaseSceneView)
end

function SceneManager:ActivateGameRoot()
  GameRoot:SetActive(true)
  GameRoot = nil
end

function SceneManager:EnterGame()
  ApplicationManager.Instance:EnterGame()
end

function SceneManager:GetGameMode()
  return self.m_eGameMode
end

function SceneManager:GetOldMode()
  return self.m_oldMode
end

function SceneManager:InSpreeGameMode()
  return StringUtil.StartWith(self.m_eGameMode, "spree")
end

function SceneManager:ChangeGameMode(targetMode, callback)
  self:_EndTickAndLog()
  self:_StartSceneTick()
  local oldMode = self.m_eGameMode
  self.m_oldMode = oldMode
  self.m_eGameMode = targetMode
  if oldMode == EGameMode.Loading then
    EventDispatcher.DispatchEvent(EEventType.EnterMainScene)
  end
  self:_PlayBGM()
  if targetMode ~= EGameMode.Loading and not GM.TutorialModel:IsNewUser() then
    self:PlayLoopEffect()
  else
    GM.AudioModel:StopLoopBgEffect()
  end
  EventDispatcher.DispatchEvent(EEventType.ChangeGameMode, self.m_eGameMode)
  ResourceLoader.ClearUnusedAssets()
  if GM.UIManager then
    GM.UIManager:OnGameModeChange()
  end
  self.isChanging = true
  GM.ModeViewController:UpdateGameView(function()
    self.isChanging = false
    EventDispatcher.DispatchEvent(EEventType.ChangeGameModeFinished, self.m_eGameMode)
    if callback then
      callback()
    end
  end)
  GM.DBTableManager:SaveAllAndCheck()
end

function SceneManager:ChangeChapter(chapterName)
  if GM.TimelineManager:IsPlayingTimeline() then
    Log.Error("时间线中不允许切换房间")
    return
  end
  GM.GameTextModel:LoadChapterText(chapterName)
  GM.ChapterManager:ChangeChapter(chapterName)
  GM.ModeViewController:ReloadRoomView()
  EventDispatcher.DispatchEvent(EEventType.LogicChangeChapter)
  ResourceLoader.ClearUnusedAssets()
end

function SceneManager:ChangeChapterWithTransition(chapterName, enterCallback, loadFinishCallback, finishCallback, canCancelWhenLackOfResource)
  local chapterId = GM.ChapterDataModel:GetChapterIdByName(chapterName)
  local forReview = chapterName ~= GM.TaskManager:GetOngoingChapterName()
  local info = {
    char_index = chapterId,
    char_id = chapterName,
    replay = forReview and 1 or 0
  }
  GM.BIManager:LogAction(EBIType.SwitchRoom, info)
  local label = GM.DownloadManager:GetChapterLabel(chapterName)
  local callback = function(size)
    GM.UIManager:SetEventLock(false)
    if 0 < size then
      local exitCallback = function()
        if not canCancelWhenLackOfResource then
          PlatformInterface.ExitGame()
        end
      end
      local downloadCallback = function()
        if forReview then
          GM.DownloadManager:SetForceDownloadChapterId(chapterId)
        end
        GM:RestartGame(nil, EBIProjectType.RestartGameAction.DownloadRequiredResource)
      end
      local exitKey = canCancelWhenLackOfResource and "resource_download_confirm_later" or "resource_download_confirm_quit"
      GM.UIManager:OpenView(UIPrefabConfigName.DownloadConfirmWindow, size, exitKey, exitCallback, downloadCallback)
    else
      self:_DoChangeChapterWithTransition(chapterName, enterCallback, loadFinishCallback, finishCallback)
    end
  end
  if label then
    GM.UIManager:SetEventLock(true)
    GM.DownloadManager:GetDownloadSizeAsync({label}, callback)
  else
    self:_DoChangeChapterWithTransition(chapterName, enterCallback, loadFinishCallback, finishCallback)
  end
end

function SceneManager:_DoChangeChapterWithTransition(chapterName, enterCallback, loadFinishCallback, finishCallback)
  local innerEnterCallback = function()
    self:ChangeChapter(chapterName)
    if enterCallback then
      enterCallback()
    end
  end
  local chapterId = GM.ChapterDataModel:GetChapterIdByName(chapterName)
  local canExitCheckFunc = function()
    if not AddressableLoader.isUnloading and GM.ResourceLoader:IsAllResourceLoaded() then
      return true
    end
    return false
  end
  local innerFinishCallback = function()
    if finishCallback then
      finishCallback()
    end
    self:PlayLoopEffect()
  end
  local transitionPrefab = UIPrefabConfigName.ChapterTransition
  GM.UIManager:OpenView(transitionPrefab, chapterName, innerEnterCallback, canExitCheckFunc, loadFinishCallback, innerFinishCallback)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxScenesSwitch)
  GM.AudioModel:StopLoopBgEffect()
end

function SceneManager:_PlayBGM()
  if self.m_eGameMode == EGameMode.Board then
    GM.AudioModel:PlayBGM(AudioFileConfigName.bgmBoard)
  elseif not GM.TutorialModel:IsNewUser() then
    GM.AudioModel:PlayBGM(AudioFileConfigName.bgmMakeover)
  end
end

function SceneManager:PlayLoopEffect()
  if GM.ChapterManager.roomModel.chapterId == 3 then
    GM.AudioModel:PlayLoopBgEffect(AudioFileConfigName.SfxOcean)
  else
    GM.AudioModel:PlayLoopBgEffect(AudioFileConfigName.SfxGeneral)
  end
end

function SceneManager:EnterActivityMode()
  self:_EndTickAndLog()
  self:_StartSceneTick()
end

function SceneManager:QuitActivityMode(gameMode)
  self:_EndTickAndLog(gameMode)
  self:_StartSceneTick()
end

function SceneManager:ApplicationDidEnterBackground()
  self:_EndTickAndLog()
end

function SceneManager:Destroy()
  self:_EndTickAndLog()
end

function SceneManager:ApplicationWillEnterForeground()
  self:_StartSceneTick()
end

function SceneManager:_StartSceneTick()
  self.m_sceneTick = 0
end

function SceneManager:UpdatePerSecond()
  self.m_gameTick = (self.m_gameTick or 0) + 1
  if self.m_sceneTick then
    self.m_sceneTick = self.m_sceneTick + 1
  end
  if self.m_privacyTick then
    self.m_privacyTick = self.m_privacyTick + 1
  end
end

function SceneManager:_EndTickAndLog(mode)
  self:StoreGameDuration()
  if not self.m_sceneTick then
    return
  end
  if self.m_privacyTick then
    self.m_privacyDuration = self.m_privacyTick
    self.m_privacyTick = 0
  end
  local action = {
    scene = mode or self.m_eGameMode,
    duration = self.m_sceneTick,
    p_dur = self.m_privacyDuration,
    d_size = self.m_downloadSize,
    event_id = BoardModelHelper.GetActiveActivityModelEventId()
  }
  local actionString = GM.BIManager:TableToString(action)
  GM.BIManager:LogAction(EBIType.SceneDuration, actionString)
  self.m_sceneTick = nil
  self.m_privacyDuration = nil
  self.m_downloadSize = nil
end

function SceneManager:StoreGameDuration()
  if self.m_gameTick and self.m_gameTick > 0 then
    GM.BIManager:ChangeNumber(EBISyncKey.GameDuration, self.m_gameTick)
    self.m_gameTick = 0
  end
end

function SceneManager:SetDownloadSize(size)
  self.m_downloadSize = size
end

function SceneManager:StartPrivacyTick()
  self.m_privacyTick = 0
end

function SceneManager:EndPrivacyTick()
  self.m_privacyDuration = self.m_privacyTick
  self.m_privacyTick = nil
end
