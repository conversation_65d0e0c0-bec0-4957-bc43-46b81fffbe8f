PassActivityMainWindow = setmetatable({bWithoutCloseAnim = false}, PassActivityBaseWindow)
PassActivityMainWindow.__index = PassActivityMainWindow
PassActivityMainWindow.DesignWidth = 960
PassActivityMainWindow.TaskAreaHeight = 480

function PassActivityMainWindow:Init(activityType, tokenNumber)
  PassActivityBaseWindow.Init(self, activityType, false)
  self.m_bUseGroup = self.m_model:IsTaskGroupOpen()
  EventDispatcher.DispatchEvent(EEventType.PassActivityMainWindowInited)
  EventDispatcher.AddListener(PassActivityDefinition[activityType].RefreshTimelimitTasksEvent, self, self._UpdateState)
  local baseSortingOrder = self:GetSortingOrder()
  self.m_taskCanvas.sortingOrder = baseSortingOrder + 2
  self.m_taskSpriteMask.frontSortingOrder = baseSortingOrder + 3
  self.m_taskSpriteMask.backSortingOrder = baseSortingOrder
  self.m_scale = self.m_canvas.transform.rect.width / PassActivityMainWindow.DesignWidth
  self.m_scale = math.min(self.m_scale, 1)
  self.m_contentNode.localScale = Vector3(self.m_scale, self.m_scale, 1)
  local screenHeight = self.m_canvas.transform.rect.height
  if ScreenFitter.NeedNotch() then
    local notchHeight = ScreenFitter.GetNotchHeight()
    screenHeight = screenHeight - notchHeight
    UIUtil.SetAnchoredPosition(self.m_contentNode, nil, -notchHeight)
  end
  local contentHeight = screenHeight / self.m_scale
  UIUtil.SetSizeDelta(self.m_contentNode, nil, contentHeight)
  local levelConfigs = self.m_model:GetLevelConfigs() or {}
  local currentTokenLevel = self.m_model:GetCurrentLevel() or 0
  local progressHeight = contentHeight - self.m_titleNode.rect.height - self.m_taskNode.rect.height
  local hasTasksLeft = currentTokenLevel < #levelConfigs or self.m_model:HasExtraLevels()
  self.m_taskCoverGo:SetActive(not hasTasksLeft)
  self.m_taskCountdonwText.gameObject:SetActive(self.m_bUseGroup and hasTasksLeft)
  UIUtil.SetSizeDelta(self.m_progressNode, nil, progressHeight - 170)
  self.RewardGroupTopPadding = self.m_activityDefinition.RewardGroupTopPadding
  self.RewardCellHeight = self.m_activityDefinition.RewardCellHeight
  local levelConfigs = self.m_model:GetLevelConfigs()
  UIUtil.SetActive(self.m_goldenTicketGo, true)
  UIUtil.SetActive(self.m_superTicketGo, false)
  self.m_rewardCells = {}
  for level, config in ipairs(levelConfigs) do
    local normalCellObject = Object.Instantiate(self.m_normalRewardCellPrefab, self.m_normalRewards)
    local normalCell = normalCellObject:GetLuaTable()
    normalCell:Init(activityType, level, false, config.rewards)
    table.insert(self.m_rewardCells, normalCell)
    local vipCellObject = Object.Instantiate(self.m_vipRewardCellPrefab, self.m_vipRewards)
    local vipCell = vipCellObject:GetLuaTable()
    vipCell:Init(activityType, level, true, config.vipRewards)
    table.insert(self.m_rewardCells, vipCell)
  end
  self.m_sliderLayoutElement.preferredHeight = self:_GetProgressSliderHeight(#levelConfigs)
  self.m_progressSlider.anchoredPosition = Vector2(0, self.RewardGroupTopPadding)
  self.m_progressCells = {}
  for level = 1, #levelConfigs do
    local progressCellObject = Object.Instantiate(self.m_progressCellPrefab, self.m_progressSlider)
    local progressCell = progressCellObject:GetLuaTable()
    progressCell:Init(activityType, level)
    progressCell.transform.anchoredPosition = Vector2(0, -self:_GetProgressSliderHeight(level))
    table.insert(self.m_progressCells, progressCell)
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_progressContent)
  local nextLevelCellObject = Object.Instantiate(self.m_progressCellPrefab, self.m_levelCellNode)
  self.m_nextLevelCell = nextLevelCellObject:GetLuaTable()
  nextLevelCellObject.transform:SetLocalScaleXY(0.8)
  if self.m_avatar then
    self.m_avatar:Init(AvatarScene.Order)
  end
  if self.m_roleBubble then
    self.m_roleBubble:UpdateText("bp_help_hestia_" .. math.random(1, 5))
  end
  self.m_mapTaskType2Cells = {}
  for _, taskType in pairs(EPassActivityTaskType) do
    self.m_mapTaskType2Cells[taskType] = {}
  end
  self:UpdateBPPlusContent()
  self:_UpdateState(nil, tokenNumber)
  self:_UpdateLevel(tokenNumber)
  self:UpdatePerSecond()
  if tokenNumber == nil then
    PassActivityViewHelper.SetViewChain(true)
    PassActivityViewHelper.ContinueViewChain(self.m_activityType)
  end
  if self.m_testClaimAllBtnGo then
    self.m_testClaimAllBtnGo:SetActive(GM.UIManager:CanShowTestUI())
  end
end

function PassActivityMainWindow:UpdateBPPlusContent()
  UIUtil.SetActive(self.m_extraRewardBoxGo, false)
  self.m_rewardBubble:ForceHideBubble()
  if not self.m_model:HasExtraLevels() or not self.m_model:CanBuyMaxTicket() then
    return
  end
  UIUtil.SetActive(self.m_extraRewardBoxGo, true)
  local padding = 500
  local initOffset = self.m_progressContentLayout.padding
  local targetOffset = CS.UnityEngine.RectOffset(initOffset.left, initOffset.right, initOffset.top, initOffset.bottom + padding)
  self.m_progressContentLayout.padding = targetOffset
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_progressContent)
  local rewards = self.m_model:GetAllExtraRewards() or {}
  self.m_rewardBubble:Init(rewards, self:GetSortingOrder() + 7)
  self:UpdateExtraBoxContent()
end

function PassActivityMainWindow:OnBonusBoxClicked()
  if not self.m_model:HasExtraLevels() then
    return
  end
  self:TryShowRewardBubble({
    bRight = true,
    pos = self.m_extraRewardBoxRect.position,
    offset = {x = 160, y = 67}
  })
end

function PassActivityMainWindow:UpdateExtraBoxContent()
  local bMaxTicket = self.m_model:HasMaxTicket()
  local bMaxLevel = self.m_model:GetCurrentLevel() >= self.m_model:GetMaxLevel()
  UIUtil.SetActive(self.m_extraBoxLockGo, not bMaxTicket or not bMaxLevel)
  if bMaxLevel then
    UIUtil.SetActive(self.m_extraSliderRect.gameObject, true)
    local sliderRect = self.m_levelGo.transform
    sliderRect:SetParent(self.m_extraSliderRect)
    UIUtil.SetAnchoredPosition(sliderRect, 11, 0)
    local canvas = sliderRect:GetComponent(typeof(CS.UnityEngine.Canvas))
    if canvas ~= nil then
      canvas.overrideSorting = false
    end
    UIUtil.SetAnchoredPosition(self.m_extraRewardBoxRect, nil, 5)
  else
    UIUtil.SetActive(self.m_extraSliderRect.gameObject, false)
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_extraSliderRect.transform.parent)
  local rewardNum = self.m_model:GetCanTakeExtraRewardNum()
  local bPlayAnim = false
  if not bMaxTicket and self.m_extraRewardNum ~= nil and rewardNum ~= self.m_extraRewardNum then
    bPlayAnim = true
  end
  self.m_extraRewardNum = rewardNum
  local textKey, param
  if not bMaxLevel and not bMaxTicket then
    textKey = "ultimate_reward_desc_1"
  elseif bMaxLevel and not bMaxTicket then
    local rewardNum = self.m_model:GetCanTakeExtraRewardNum()
    if rewardNum == 0 then
      textKey = "ultimate_reward_desc_2"
    else
      textKey = "ultimate_reward_desc_3"
      param = rewardNum
    end
  else
    textKey = "ultimate_reward_desc_4"
  end
  self.m_extraBoxDescText.text = GM.GameTextModel:GetText(textKey, param)
  if bPlayAnim then
    local rect = self.m_extraRewardBoxRect
    local seq = DOTween.Sequence()
    seq:Append(rect:DORotate(Vector3(0, 0, 6), 0.06))
    seq:Append(rect:DORotate(Vector3(0, 0, -4), 0.08))
    seq:Append(rect:DORotate(Vector3(0, 0, 2), 0.1))
    seq:Append(rect:DORotate(Vector3(0, 0, 0), 0.12))
  end
end

function PassActivityMainWindow:HideRewardBubble()
  if self.m_skipHideRewardBubbleOnce then
    self.m_skipHideRewardBubbleOnce = nil
    return
  end
  self:ForceHideRewardBubble()
end

function PassActivityMainWindow:ForceHideRewardBubble()
  self.m_rewardBubble:HideBubble()
end

function PassActivityMainWindow:TryShowRewardBubble(msg)
  local scrollRect = self.m_progressNode:GetComponent(typeof(ScrollRect))
  if scrollRect.verticalNormalizedPosition < 0 then
    self.m_skipHideRewardBubbleOnce = true
    scrollRect.verticalNormalizedPosition = 0
  elseif scrollRect.verticalNormalizedPosition > 1 then
    self.m_skipHideRewardBubbleOnce = true
    scrollRect.verticalNormalizedPosition = 1
  else
    scrollRect:StopMovement()
  end
  scrollRect = self.m_taskScrollRect
  if 0 > scrollRect.horizontalNormalizedPosition then
    self.m_skipHideRewardBubbleOnce = true
    scrollRect.horizontalNormalizedPosition = 0
  elseif 1 < scrollRect.horizontalNormalizedPosition then
    self.m_skipHideRewardBubbleOnce = true
    scrollRect.horizontalNormalizedPosition = 1
  else
    scrollRect:StopMovement()
  end
  self.m_rewardBubble:ShowBubble(msg, true)
end

function PassActivityMainWindow:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  Scheduler.UnscheduleTarget(self)
  GM.UIManager:RemoveAllEventLocks(self)
end

function PassActivityMainWindow:UpdatePerSecond()
  if self.m_model ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
  if self.m_bUseGroup and self.m_taskCountdonwText ~= nil and self.m_model ~= nil then
    local delta = math.max(self.m_model:GetTaskRefreshTime() - GM.GameModel:GetServerTime(), 0)
    self.m_taskCountdonwText.text = GM.GameTextModel:GetText("battlepass_taskCountdown_desc", TimeUtil.ToMSOrHMS(delta))
  end
end

function PassActivityMainWindow:OnGetFocus()
  self:_UpdateState()
  self:UpdateExtraBoxContent()
  if self.m_focusedOnce then
    self:_TryPlayCellAnimation()
  else
    DOVirtual.DelayedCall(0.6, function()
      self:_TryPlayCellAnimation()
    end)
    self.m_focusedOnce = true
  end
end

function PassActivityMainWindow:_UpdateState(bForced, tokenNumber)
  local textKey
  if self.m_model:CanBuyMaxTicket() then
    if self.m_model:GetTicketState() == EBPTicketState.None then
      textKey = "battlepass_activateButton"
    elseif self.m_model:GetTicketState() == EBPTicketState.Upgrade then
      textKey = "battlepass_upgradeButton"
    else
      textKey = "go_play_btn"
    end
  else
    local hasTicket = self.m_model:HasTicket()
    textKey = hasTicket and "go_play_btn" or "battlepass_activateButton"
  end
  UIUtil.SetActive(self.m_goldenTicketGo, not self.m_model:HasMaxTicket())
  UIUtil.SetActive(self.m_superTicketGo, self.m_model:HasMaxTicket())
  self.m_ticketButtonGo:SetActive(true)
  self.m_ticketButtonGo:GetLuaTable():SetText(GM.GameTextModel:GetText(textKey))
  local levelConfigs = self.m_model:GetLevelConfigs()
  local currentTokenLevel = self.m_model:GetCurrentLevel()
  if tokenNumber ~= nil then
    currentTokenLevel = self.m_model:GetLevel(tokenNumber, true)
  end
  for _, cell in ipairs(self.m_rewardCells) do
    cell:UpdateState(currentTokenLevel)
  end
  UIUtil.SetSizeDelta(self.m_progressSliderFill, nil, self:_GetProgressSliderHeight(currentTokenLevel))
  for _, cell in ipairs(self.m_progressCells) do
    cell:UpdateState(currentTokenLevel)
  end
  local scrollRect = self.m_progressNode:GetComponent(typeof(ScrollRect))
  scrollRect.verticalNormalizedPosition = self:_GetProgressMiddleVerticalNormalizedPosition(currentTokenLevel)
  if self.m_bUseGroup then
    self:_UpdateGroupTask(bForced)
  else
    self:_UpdateTask(bForced)
  end
  self.m_goldenTicketButton.enabled = self:CanClickTicketButton()
end

function PassActivityMainWindow:_UpdateTask(bForced)
  local levelConfigs = self.m_model:GetLevelConfigs()
  local currentTokenLevel = self.m_model:GetCurrentLevel()
  local activeTasks, finishedTasks = self.m_model:GetCurDayAllTasks()
  if not self.m_model:HasExtraLevels() and currentTokenLevel >= #levelConfigs then
    activeTasks = {}
    self.m_taskCoverGo:SetActive(true)
  else
    table.sort(activeTasks, function(task1, task2)
      return self:_SortTasksNew(task1, task2)
    end)
    table.sort(finishedTasks, function(task1, task2)
      return self:_SortTasksNew(task1, task2)
    end)
  end
  local refreshTask = false
  if self.m_activeTasks == nil or #self.m_activeTasks ~= #activeTasks then
    refreshTask = true
  else
    for index, task in ipairs(self.m_activeTasks) do
      if task ~= activeTasks[index] then
        refreshTask = true
        break
      end
    end
  end
  self.m_activeTasks = activeTasks
  if refreshTask or bForced then
    for i = 1, self.m_taskContent.childCount do
      Object.Destroy(self.m_taskContent:GetChild(i - 1).gameObject)
    end
    local cellObject
    for _, task in ipairs(activeTasks) do
      if task.Type == EPassActivityTaskType.Timelimit then
        cellObject = Object.Instantiate(self.m_timelimitTaskCellPrefab, self.m_taskContent)
      elseif task.Type == EPassActivityTaskType.Bonus then
        cellObject = Object.Instantiate(self.m_bonusTaskCellPrefab, self.m_taskContent)
      elseif task.Type == EPassActivityTaskType.VIP then
        cellObject = Object.Instantiate(self.m_vipTaskCellPrefab, self.m_taskContent)
      else
        cellObject = Object.Instantiate(self.m_cycleTaskCellPrefab, self.m_taskContent)
      end
      cellObject:GetLuaTable():Init(self.m_activityType, task, false, self)
    end
    for _, task in ipairs(finishedTasks) do
      if task.Type == EPassActivityTaskType.Timelimit then
        cellObject = Object.Instantiate(self.m_timelimitTaskCellPrefab, self.m_taskContent)
      elseif task.Type == EPassActivityTaskType.Bonus then
        cellObject = Object.Instantiate(self.m_bonusTaskCellPrefab, self.m_taskContent)
      elseif task.Type == EPassActivityTaskType.VIP then
        cellObject = Object.Instantiate(self.m_vipTaskCellPrefab, self.m_taskContent)
      else
        cellObject = Object.Instantiate(self.m_cycleTaskCellPrefab, self.m_taskContent)
      end
      cellObject:GetLuaTable():Init(self.m_activityType, task, true, self)
    end
  else
    for i = 1, self.m_taskContent.childCount do
      local taskCell = self.m_taskContent:GetChild(i - 1).gameObject:GetLuaTable()
      if taskCell then
        taskCell:UpdateProgress()
      end
    end
  end
end

function PassActivityMainWindow:_UpdateGroupTask(bForced)
  local levelConfigs = self.m_model:GetLevelConfigs()
  local currentTokenLevel = self.m_model:GetCurrentLevel()
  local activeTasks = self.m_model:GetShowingTaskByGroup()
  if not self.m_model:HasExtraLevels() and currentTokenLevel >= #levelConfigs then
    activeTasks = {}
    self.m_taskCoverGo:SetActive(true)
  else
    table.sort(activeTasks, function(task1, task2)
      return self:_SortTasksNew(task1, task2)
    end)
  end
  local refreshTask = false
  if self.m_activeTasks == nil or #self.m_activeTasks ~= #activeTasks then
    refreshTask = true
  else
    for index, task in ipairs(self.m_activeTasks) do
      if task ~= activeTasks[index] then
        refreshTask = true
        break
      end
    end
  end
  self.m_activeTasks = activeTasks
  local arrSeq = {}
  if refreshTask or bForced then
    for taskType, cells in pairs(self.m_mapTaskType2Cells) do
      for _, cell in ipairs(cells) do
        if taskType ~= EPassActivityTaskType.Timelimit then
          Object.Destroy(cell.gameObject)
        else
          UIUtil.SetActive(cell.gameObject, false)
        end
      end
      if taskType ~= EPassActivityTaskType.Timelimit then
        self.m_mapTaskType2Cells[taskType] = {}
      end
    end
    local cellObject, bRecycle
    for _, task in ipairs(activeTasks) do
      bRecycle = false
      cellObject = nil
      if task.Type == EPassActivityTaskType.Timelimit then
        for _, cell in ipairs(self.m_mapTaskType2Cells[EPassActivityTaskType.Timelimit]) do
          if cell:GetTask() ~= nil and cell:GetTask().Group == task.Group then
            cellObject = cell.gameObject
            bRecycle = true
            UIUtil.SetActive(cell.gameObject, true)
            if task.Index ~= cell:GetTask().Index then
              table.insert(arrSeq, cell:PlayGroupChangedAnim(task))
            end
            break
          end
        end
        if cellObject == nil then
          cellObject = Object.Instantiate(self.m_timelimitTaskCellPrefab, self.m_taskContent)
        end
      elseif task.Type == EPassActivityTaskType.Bonus then
        cellObject = Object.Instantiate(self.m_bonusTaskCellPrefab, self.m_taskContent)
      elseif task.Type == EPassActivityTaskType.VIP then
        cellObject = Object.Instantiate(self.m_vipTaskCellPrefab, self.m_taskContent)
      else
        cellObject = Object.Instantiate(self.m_cycleTaskCellPrefab, self.m_taskContent)
      end
      if not bRecycle then
        cellObject:GetLuaTable():Init(self.m_activityType, task, false, self)
        table.insert(self.m_mapTaskType2Cells[task.Type], cellObject:GetLuaTable())
      end
    end
  else
    for _, cell in ipairs(self.m_mapTaskType2Cells[EPassActivityTaskType.Cycle]) do
      cell:UpdateProgress()
    end
  end
  return arrSeq
end

function PassActivityMainWindow:_UpdateLevelOnFilterRewardChange()
  self:_UpdateLevel()
end

function PassActivityMainWindow:_UpdateLevel(num)
  local levelConfigs = self.m_model:GetLevelConfigs()
  local currentTokenLevel, currentTokenNumber = self.m_model:GetCurrentLevel(true)
  if IsNumber(num) then
    currentTokenLevel, currentTokenNumber = self.m_model:GetLevel(num, true)
  end
  self.m_tokenLevel = currentTokenLevel
  self.m_tokenNumber = self.m_model:GetTokenNumber()
  if currentTokenLevel >= #levelConfigs then
    if not self.m_model:HasExtraLevels() then
      self.m_levelSlider.value = 1
      self.m_levelText.text = currentTokenNumber .. "/MAX"
      self.m_nextLevelCell:Init(self.m_activityType, #levelConfigs)
    else
      self:UpdateExtraBoxContent()
      self.m_nextLevelCell.gameObject:SetActive(false)
      local levelConfig = self.m_model:GetLevelConfigIncludeExtra(currentTokenLevel + 1)
      if levelConfig ~= nil then
        self.m_levelSlider.value = currentTokenNumber / levelConfig.require
        self.m_levelText.text = currentTokenNumber .. "/" .. levelConfig.require
      else
        Log.Error("This should not happen.")
      end
    end
  else
    local nextLevelConfig = levelConfigs[currentTokenLevel + 1]
    self.m_levelSlider.value = currentTokenNumber / nextLevelConfig.require
    self.m_levelText.text = currentTokenNumber .. "/" .. nextLevelConfig.require
    self.m_nextLevelCell:Init(self.m_activityType, currentTokenLevel + 1)
  end
end

function PassActivityMainWindow:_TryPlayCellAnimation()
  for _, cell in ipairs(self.m_rewardCells) do
    cell:TryPlayAnimation()
  end
end

function PassActivityMainWindow:_SortTasks(task1, task2)
  if task1.Type ~= task2.Type then
    if task1.Type == EPassActivityTaskType.Timelimit then
      return true
    elseif task2.Type == EPassActivityTaskType.Timelimit then
      return false
    else
      return task1.Type == EPassActivityTaskType.Bonus
    end
  end
  local progress1 = self.m_model:GetFinishedCount(task1) / task1:GetFinalCount()
  local progress2 = self.m_model:GetFinishedCount(task2) / task2:GetFinalCount()
  if progress1 ~= progress2 then
    return progress1 > progress2
  end
  return task1.Reward > task2.Reward
end

function PassActivityMainWindow:_SortTasksNew(task1, task2)
  local order1 = EPassActivityTaskOrder[task1.Type] + task1.Index
  local order2 = EPassActivityTaskOrder[task2.Type] + task2.Index
  return order1 < order2
end

function PassActivityMainWindow:_GetProgressSliderHeight(level)
  return self.RewardCellHeight * (level - 1)
end

function PassActivityMainWindow:_GetProgressMiddleVerticalNormalizedPosition(level)
  local targetSliderHeight = self:_GetProgressSliderHeight(level) + self.RewardCellHeight + self.RewardGroupTopPadding
  if level == self.m_model:GetMaxLevel() and self.m_extraRewardBoxGo.activeSelf then
    return 0
  end
  local progressNodeHeight = self.m_progressNode.rect.height
  if targetSliderHeight > progressNodeHeight / 2 then
    local progressContentHeight = self.m_progressContent.rect.height
    local normalized = (progressContentHeight - targetSliderHeight - progressNodeHeight / 2) / (progressContentHeight - progressNodeHeight)
    return math.max(normalized, 0)
  else
    return 1
  end
end

function PassActivityMainWindow:OnTicketButtonClicked()
  local bBuyMaxTicket = self.m_model:CanBuyMaxTicket()
  if not bBuyMaxTicket and self.m_model:HasTicket() or bBuyMaxTicket and self.m_model:GetTicketState() == EBPTicketState.Purchase then
    self:Close()
    if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
      GM.SceneManager:ChangeGameMode(EGameMode.Board)
    else
      local mainShopWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.ShopWindow)
      if mainShopWindow then
        mainShopWindow:Close()
      end
    end
  else
    PassActivityViewHelper.OpenBuyTicketWindow(self.m_activityType)
  end
end

function PassActivityMainWindow:OnTicketClicked()
  if self:CanClickTicketButton() then
    PassActivityViewHelper.OpenBuyTicketWindow(self.m_activityType)
  end
end

function PassActivityMainWindow:CanClickTicketButton()
  if self.m_model:CanBuyMaxTicket() then
    return not self.m_model:HasMaxTicket()
  else
    return not self.m_model:HasTicket()
  end
end

function PassActivityMainWindow:TryShowRewardTip(level, isVip, targetCell)
  self:ForceHideRewardTip()
  local descriptionKey
  if self.m_model:IsRewardTaken(level, isVip) then
    descriptionKey = "battlepass_redesc_claimed"
  elseif isVip and not self.m_model:HasTicket() then
    descriptionKey = self.m_activityDefinition.LockedUnpayTextKey
  elseif level > self.m_model:GetCurrentLevel() then
    descriptionKey = "battlepass_redesc_locked"
  end
  if descriptionKey == nil then
    return
  end
  local scrollRect = self.m_progressNode:GetComponent(typeof(ScrollRect))
  if scrollRect.verticalNormalizedPosition < 0 then
    self.m_skipHideRewardTipOnce = true
    scrollRect.verticalNormalizedPosition = 0
  elseif scrollRect.verticalNormalizedPosition > 1 then
    self.m_skipHideRewardTipOnce = true
    scrollRect.verticalNormalizedPosition = 1
  end
  local position = targetCell.transform:TransformPoint(Vector3.zero)
  local localPosition = self.m_viewPortNode:InverseTransformPoint(position)
  localPosition.z = 0
  if isVip then
    self.m_rewardTipObject = Object.Instantiate(self.m_vipRewardTipPrefab, self.m_viewPortNode)
    localPosition = localPosition + Vector3(-25, 175, 0)
  else
    self.m_rewardTipObject = Object.Instantiate(self.m_normalRewardTipPrefab, self.m_viewPortNode)
    localPosition = localPosition + Vector3(25, 175, 0)
  end
  self.m_rewardTipObject:GetLuaTable():Init(self.m_activityType, descriptionKey)
  local transform = self.m_rewardTipObject.transform
  local ratioX = isVip and -1 or 1
  transform.localPosition = localPosition
  transform.localScale = Vector3(ratioX * 0.95, 0.95, 1)
  transform:DOScale(Vector3(ratioX * 1, 1, 1), 0.4):SetEase(Ease.OutBack)
end

function PassActivityMainWindow:HideRewardTip()
  if self.m_skipHideRewardTipOnce then
    self.m_skipHideRewardTipOnce = nil
    return
  end
  self:ForceHideRewardTip()
end

function PassActivityMainWindow:ForceHideRewardTip()
  if self.m_rewardTipObject ~= nil then
    self.m_rewardTipObject.transform:DOKill()
    Object.Destroy(self.m_rewardTipObject)
    self.m_rewardTipObject = nil
  end
end

function PassActivityMainWindow:PlayLevelProgressAnimation(startPosition)
  local properties = {
    {
      [PROPERTY_TYPE] = "pass_activity_token",
      [PROPERTY_COUNT] = 6
    }
  }
  local customData = {
    {
      targetButton = self.m_token,
      spriteKey = self.m_activityDefinition.TokenIconName,
      spriteScale = 0.54
    }
  }
  GM.PropertyDataManager:PlayCollectAnimation(properties, startPosition, customData)
  local currentTokenLevel, currentTokenNumber = self.m_model:GetCurrentLevel(true)
  local arrSeq
  if self.m_bUseGroup then
    arrSeq = self:_UpdateGroupTask()
  end
  local sequence = DOTween.Sequence()
  sequence:Append(DOTween.To(function()
    return self.m_tokenNumber
  end, function(x)
    local num = math.ceil(x)
    local curLevel, curNumber = self.m_model:GetLevel(num, true)
    local targetConfig = self.m_model:GetLevelConfigIncludeExtra(curLevel + 1)
    local targetNumber = targetConfig and targetConfig.require
    if targetNumber ~= nil and 0 < targetNumber then
      self.m_levelText.text = curNumber .. "/" .. targetNumber
      self.m_levelSlider.value = curNumber / targetNumber
    end
    self.m_nextLevelCell:UpdateLevel(curLevel + 1)
  end, self.m_model:GetTokenNumber(), 1.5))
  if self.m_tokenLevel ~= currentTokenLevel and self.m_tokenLevel < self.m_model:GetMaxLevel() then
    local captureLevel = self.m_tokenLevel
    local levelFunc = function()
      self:_UpdateLevel()
    end
    sequence:AppendCallback(levelFunc)
    local targetLevel = math.min(currentTokenLevel, self.m_model:GetMaxLevel())
    local sizeDelta = self.m_progressSliderFill.sizeDelta
    sizeDelta.y = self:_GetProgressSliderHeight(targetLevel)
    sequence:Append(self.m_progressSliderFill:DOSizeDelta(sizeDelta, 2))
    local scrollRect = self.m_progressNode:GetComponent(typeof(ScrollRect))
    local verticalNormalizedPosition = self:_GetProgressMiddleVerticalNormalizedPosition(targetLevel)
    sequence:Insert(1.5, scrollRect:DOVerticalNormalizedPos(verticalNormalizedPosition, 2))
  end
  local eventLockCallback = function()
    sequence:Complete(true)
    if not Table.IsEmpty(arrSeq) then
      for _, seq in ipairs(arrSeq) do
        seq:Complete(true)
      end
    end
  end
  GM.UIManager:SetEventLock(true, self, eventLockCallback)
  local finishFunc = function()
    GM.UIManager:SetEventLock(false, self, eventLockCallback)
    PassActivityViewHelper.ContinueViewChain(self.m_activityType)
    for _, cell in ipairs(self.m_progressCells) do
      cell:TryPlayAnimation()
    end
  end
  sequence:AppendCallback(finishFunc)
end

function PassActivityMainWindow:GetSecondRewardArea()
  local height = self:_GetProgressSliderHeight(2)
  local center = self.m_progressSlider.transform:TransformPoint(Vector3(0, -height, 0))
  local down = self.m_progressSlider.transform:TransformPoint(Vector3(0, -height - self.RewardCellHeight / 2, 0))
  local up = self.m_progressSlider.transform:TransformPoint(Vector3(0, -height + self.RewardCellHeight / 2, 0))
  local size = Vector2(PassActivityMainWindow.DesignWidth * self.m_scale, up.y - down.y)
  return center, size
end

function PassActivityMainWindow:GetTaskArea()
  local center = self.m_taskNode:TransformPoint(Vector3(0, PassActivityMainWindow.TaskAreaHeight / 2, 0))
  local up = self.m_taskNode:TransformPoint(Vector3(0, PassActivityMainWindow.TaskAreaHeight, 0))
  local down = self.m_taskNode:TransformPoint(Vector3.zero)
  local size = Vector2((PassActivityMainWindow.DesignWidth - 50) * self.m_scale, up.y - down.y)
  return center, size
end

function PassActivityMainWindow:GetTicketArea()
  local leftDown = self.m_ticketButtonGo.transform:TransformPoint(Vector3(-246, -50, 0))
  local rightUp = self.m_ticketButtonGo.transform:TransformPoint(Vector3(200, 220, 0))
  local center = (leftDown + rightUp) / 2
  local size = Vector2(rightUp.x - leftDown.x, rightUp.y - leftDown.y)
  return center, size
end

function PassActivityMainWindow:GetVipTicketTransform()
  return self.m_vipTicketRectTrans
end

function PassActivityMainWindow:ScrollToFront()
  local scrollRect = self.m_progressNode:GetComponent(typeof(ScrollRect))
  scrollRect.verticalNormalizedPosition = 1
end

function PassActivityMainWindow:GetTicketButtonTransform()
  return self.m_ticketButtonGo.transform
end

function PassActivityMainWindow:TryClaimExtraRewards()
  local canTakeExtraRewards = self.m_model:GetCanTakeExtraRewards()
  if #canTakeExtraRewards <= 0 then
    if self.m_tokenLevel >= self.m_model:GetMaxLevel() then
      EventDispatcher.DispatchEvent(EEventType.PassActivityReachMaxLevel, self.m_levelGo.transform)
    end
    return
  end
  local rewards = {}
  for _, canTakeReward in ipairs(canTakeExtraRewards) do
    if canTakeReward.IsExtra then
      self.m_model:TakeExtraReward(canTakeReward.Index, canTakeReward.Rewards)
      Table.ListAppend(rewards, canTakeReward.Rewards)
    end
  end
  rewards = RewardApi.GetMergedRewards(rewards)
  self:_UpdateLevel()
  GM.UIManager:OpenView(UIPrefabConfigName.BoxRewardWindow, self.m_extraRewardBoxRect.position, 1, ImageFileConfigName.bp_super_box, rewards, "rewards_window_title_normal", true, nil, function()
    PassActivityViewHelper.ContinueViewChain(self.m_activityType)
    if self.gameObject and not self.gameObject:IsNull() then
      self:_UpdateState(true)
    end
  end, false)
end

function PassActivityMainWindow:OnTestClaimAll()
  local canTakeRewards = self.m_model:GetCanTakeRewards()
  for _, canTakeReward in ipairs(canTakeRewards) do
    if canTakeReward.IsExtra then
      self.m_model:TakeExtraReward(canTakeReward.Index, canTakeReward.Rewards)
    else
      self.m_model:TakeReward(canTakeReward.Level, canTakeReward.IsVip, canTakeReward.Rewards)
    end
  end
  GM.UIManager:ShowTestPrompt("操作成功，请重新打开活动窗口")
  self:Close()
end

function PassActivityMainWindow:SetScrollLock(blocked)
  self.m_taskScrollRect.enabled = not blocked
  self.m_progressScrollRect.enabled = not blocked
end
