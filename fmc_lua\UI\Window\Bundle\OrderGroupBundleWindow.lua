OrderGroupBundleWindow = setmetatable({}, BundleNormalWindow)
OrderGroupBundleWindow.__index = OrderGroupBundleWindow

function OrderGroupBundleWindow:Init(bundleType, dataGroup, bUserClick, eTriggerType)
  BundleNormalWindow.Init(self, bundleType, dataGroup, bUserClick, eTriggerType)
  self.m_spineAnim:Initialize(false)
  self.m_titleSpineAnim:Initialize(false)
  self.m_spineAnim.AnimationState:SetAnimation(0, "init", true)
  self.m_titleSpineAnim.AnimationState:SetAnimation(0, "init", true)
  self.m_titleCanvasGroup.alpha = 0
  self.m_openTextCanvasGroup.alpha = 0
  DelayExecuteFuncInView(function()
    self.m_titleCanvasGroup.alpha = 1
    self.m_spineAnim.AnimationState:SetAnimation(0, "appear", false)
    self.m_spineAnim.AnimationState:AddAnimation(0, "idle", true, 0)
    self.m_titleSpineAnim.AnimationState:SetAnimation(0, "appear", false)
    self.m_titleSpineAnim.AnimationState:AddAnimation(0, "idle", true, 0)
    self.m_openTextCanvasGroup:DOFade(1, 0.2):SetDelay(0.3)
  end, 0.15, self, false)
  self.m_countdownCanvasGroup.alpha = 0
  UIUtil.SetActive(self.m_titleEffectGo, false)
  DelayExecuteFuncInView(function()
    UIUtil.SetActive(self.m_titleEffectGo, true)
    self.m_countdownCanvasGroup:DOFade(1, 0.2)
  end, 0.4, self)
end
