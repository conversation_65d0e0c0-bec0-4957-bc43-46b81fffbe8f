BreakEggMainWindow = setmetatable({}, BreakEggBaseWindow)
BreakEggMainWindow.__index = BreakEggMainWindow
BreakEggMainWindow.DesignWidth = 1560
BreakEggMainWindow.DesignHeight = 2400

function BreakEggMainWindow:Init(autoOpen)
  BreakEggBaseWindow.Init(self)
  local scale = self.m_canvas.transform.rect.height / BreakEggMainWindow.DesignHeight
  self.m_contentRect.localScale = Vector3(scale, scale, 1)
  self.m_scoreBoard:Init()
  self.m_middleStartArea:Init()
  self.m_bottomStartArea:Init()
  self.m_middleSelectArea:Init()
  self.m_bottomSelectArea:Init()
  if autoOpen then
    self:LogWindowAction(EBIType.UIActionType.Open, {
      EBIReferType.AutoPopup
    })
  else
    self:LogWindowAction(EBIType.UIActionType.Open, {
      EBIReferType.UserClick
    })
  end
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.UserClick
  })
  self:UpdateContent()
  self:UpdatePerSecond()
  self.m_activityModel:SetWindowOpened()
end

function BreakEggMainWindow:UpdatePerSecond()
  if self.m_activityModel ~= nil then
    local delta = self.m_activityModel:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function BreakEggMainWindow:OnDestroy()
  BreakEggBaseWindow.OnDestroy(self)
  if self.m_animationTween ~= nil then
    self.m_animationTween:Kill()
  end
  if self.m_avatarIdleTween ~= nil then
    self.m_avatarIdleTween:Kill()
  end
end

function BreakEggMainWindow:UpdateContent()
  self.m_scoreBoard:UpdateContent()
  self.m_middleStartArea:UpdateContent()
  self.m_bottomStartArea:UpdateContent()
  self.m_middleSelectArea:UpdateContent()
  self.m_bottomSelectArea:UpdateContent()
  self.m_avatar.AnimationState:SetAnimation(0, "Norman_idle", true)
  self.m_bubbleNode.localScale = Vector3.zero
  local breakEggState = self.m_activityModel:GetBreakEggState()
  if breakEggState == BreakEggState.NoStart then
    self:_playAvatar()
  elseif breakEggState == BreakEggState.WaitRevive then
    GM.UIManager:OpenView(UIPrefabConfigName.BreakEggReviveWindow)
  end
end

function BreakEggMainWindow:_playAvatar()
  self.m_avatarIdleTween = DOTween.Sequence()
  self.m_avatarIdleTween:AppendCallback(function()
    self:_ShowBubble("break_egg_norman_begin", 2)
  end)
  self.m_avatarIdleTween:AppendInterval(10)
  self.m_avatarIdleTween:SetLoops(-1)
end

function BreakEggMainWindow:OnInformationButtonClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.BreakEggProbWindow)
end

function BreakEggMainWindow:OnHelpButtonClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.BreakEggHelpWindow)
end

function BreakEggMainWindow:OnStartButtonClicked()
  local success, lackNumber = self.m_activityModel:StartBreakEgg()
  if success then
    self:_DisplayFirstStep()
  else
    GM.ShopModel:OnLackOfGem(lackNumber)
  end
end

function BreakEggMainWindow:OnCollectButtonClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.BreakEggQuitWindow)
end

function BreakEggMainWindow:OnBreakEgg(index)
  local results = self.m_activityModel:BreakEgg(index)
  self.m_middleSelectArea:DisplayResults(results, index)
  GM.UIManager:SetEventLock(true, self)
  local breakEggSuccess = self.m_activityModel:GetBreakEggState() ~= BreakEggState.WaitRevive
  self.m_animationTween = DOTween.Sequence()
  self.m_animationTween:InsertCallback(0.4, function()
    if breakEggSuccess then
      self.m_avatar.AnimationState:SetAnimation(0, "Norman_happy_begin", false)
      self.m_avatar.AnimationState:AddAnimation(0, "Norman_happy_loop", true, 0)
      self:_ShowBubble("break_egg_norman_happy", 5)
    else
      self.m_avatar.AnimationState:SetAnimation(0, "Norman_fear_begin", false)
      self.m_avatar.AnimationState:AddAnimation(0, "Norman_fear_loop", true, 0)
      self:_ShowBubble("break_egg_norman_shock", 2)
    end
  end)
  local delayTime = breakEggSuccess and 3 or 1.5
  self.m_animationTween:InsertCallback(delayTime, function()
    if breakEggSuccess then
      self:_CollectReward(index, results[index])
    else
      GM.UIManager:SetEventLock(false, self)
      GM.UIManager:OpenView(UIPrefabConfigName.BreakEggReviveWindow)
    end
  end)
end

function BreakEggMainWindow:OnReviveSuccess()
  GM.UIManager:SetEventLock(true, self)
  self.m_avatar.AnimationState:SetAnimation(0, "Norman_idle", true)
  self.m_bubbleNode.localScale = Vector3.zero
  self.m_middleSelectArea:OnReviveSuccess()
  self.m_animationTween = DOVirtual.DelayedCall(0.5, function()
    GM.UIManager:SetEventLock(false, self)
  end)
end

function BreakEggMainWindow:_DisplayFirstStep()
  self.m_avatarIdleTween:Kill()
  self.m_avatarIdleTween = nil
  GM.UIManager:SetEventLock(true, self)
  self.m_scoreBoard:PlayFocusAnimation(1.6)
  self.m_middleStartArea:FadeOut()
  self.m_bottomStartArea:FadeOut()
  self.m_animationTween = DOTween.Sequence()
  self.m_animationTween:InsertCallback(0.3, function()
    self.m_middleSelectArea:DisplayFirstStep()
    self.m_bottomSelectArea:DisplayFirstStep()
  end)
  self.m_animationTween:InsertCallback(2, function()
    GM.UIManager:SetEventLock(false, self)
  end)
end

function BreakEggMainWindow:DisPlayStart()
  GM.UIManager:SetEventLock(true, self)
  self.m_scoreBoard:PlayFocusAnimation(1.6, self.m_activityModel:GetStepTotal())
  self.m_avatar.AnimationState:SetAnimation(0, "Norman_idle", true)
  self.m_bubbleNode.localScale = Vector3.zero
  self:_playAvatar()
  self.m_bottomStartArea:UpdateChild()
  self.m_animationTween = DOTween.Sequence()
  self.m_animationTween:InsertCallback(0.5, function()
    self.m_middleSelectArea:DisplayStart()
    self.m_bottomSelectArea:DisplayStart()
  end)
  self.m_animationTween:InsertCallback(1, function()
    GM.UIManager:SetEventLock(false, self)
    self.m_middleStartArea:FadeIn()
    self.m_bottomStartArea:FadeIn()
  end)
end

function BreakEggMainWindow:_CollectReward(selectedIndex, rewardData)
  local rewardObject = self.m_middleSelectArea:CloneRewardObject(selectedIndex)
  self.m_middleSelectArea:CollectReward(selectedIndex)
  local image = rewardObject:GetComponent(typeof(Image))
  local text = rewardObject.transform:GetChild(0):GetComponent(typeof(CS.TextFit))
  local targetPosition = self.m_bottomSelectArea:GetRewardTargetPosition()
  self.m_animationTween = DOTween.Sequence()
  self.m_animationTween:Insert(0, rewardObject.transform:DOMove(targetPosition, 0.7))
  self.m_animationTween:Insert(0.4, rewardObject.transform:DOScale(1, 0.3))
  self.m_animationTween:Insert(0.4, image:DOFade(0, 0.3))
  self.m_animationTween:Insert(0.4, text:DOFade(0, 0.3))
  self.m_animationTween:InsertCallback(0.7, function()
    Object.Destroy(rewardObject)
    self.m_bottomSelectArea:DisplayNewStep(rewardData)
    if self.m_activityModel:GetBreakEggState() == BreakEggState.NoStart then
      GM.UIManager:SetEventLock(false, self)
      GM.UIManager:OpenView(UIPrefabConfigName.BreakEggSuccessWindow)
    else
      self:_DisplayNewStep()
    end
  end)
end

function BreakEggMainWindow:_DisplayNewStep()
  self.m_avatar.AnimationState:SetAnimation(0, "Norman_idle", true)
  self.m_scoreBoard:PlayFocusAnimation(0.5)
  self.m_middleSelectArea:DisplayNewStep()
  self.m_animationTween = DOVirtual.DelayedCall(0.6, function()
    GM.UIManager:SetEventLock(false, self)
  end)
end

function BreakEggMainWindow:_ShowBubble(textKeyPrefix, number)
  if self.m_bubbleTween ~= nil then
    self.m_bubbleTween:Kill()
  end
  local index = math.random(1, number)
  self.m_bubbleText.text = GM.GameTextModel:GetText(textKeyPrefix .. index)
  self.m_bubbleTween = DOTween.Sequence()
  self.m_bubbleTween:Append(self.m_bubbleNode:DOScale(1, 0.3))
  self.m_bubbleTween:AppendInterval(4.4)
  self.m_bubbleTween:Append(self.m_bubbleNode:DOScale(0, 0.3))
end
