SurpriseChestActivityModel = setmetatable({}, BaseActivityModel)
SurpriseChestActivityModel.__index = SurpriseChestActivityModel
local DBKey = {OpeningSurpriseChest = "op_s_c", TriggerEndTime = "t_e_t"}
SurpriseChestActivityModel.SurpriseChestDurationKey = "duration"

function SurpriseChestActivityModel:Init(activityType, dbTable)
  self.m_activityDefinition = SurpriseChestActivityDefinition[activityType]
  BaseActivityModel.Init(self, activityType, dbTable)
  self:_LoadOpeningSurpriseChestData()
  EventDispatcher.AddListener(EEventType.OrderFinished, self, self._OnOrderFinished)
  EventDispatcher.AddListener(EEventType.OrderStateChanged, self, self._OnOrderStateChanged)
end

function SurpriseChestActivityModel:_LoadOtherServerConfig(serverConfig)
  local chestConfig = serverConfig.order_token_control or Table.Empty
  self.m_arrChestData = {}
  for _, config in ipairs(chestConfig) do
    table.insert(self.m_arrChestData, SurpriseChestData.Create(config))
  end
  self.m_duration = self:GetGeneralConfig(SurpriseChestActivityModel.SurpriseChestDurationKey, EConfigParamType.Int) or 0
  Log.Assert(self.m_duration > 0, "问号宝箱活动的buff时间为0！")
end

function SurpriseChestActivityModel:GetLockDurationInSeconds()
  return self.m_duration
end

function SurpriseChestActivityModel:GetResourceLabels()
  return self.m_activityDefinition.ResourceLabels
end

function SurpriseChestActivityModel:_OnStateChanged()
  BaseActivityModel._OnStateChanged(self)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
end

function SurpriseChestActivityModel:IsActivityOpen()
  return self:GetState() == ActivityState.Started and (self:IsInOpeningPeriod() or not self:HasTriggeredSurpriseChest())
end

function SurpriseChestActivityModel:GetAllStateChangedEvent()
  return {
    self.m_activityDefinition.StateChangedEvent
  }
end

function SurpriseChestActivityModel:_CalculateState()
  if self.m_config == nil then
    return ActivityState.Released, -1
  end
  local serverTime = GM.GameModel:GetServerTime()
  if serverTime < self.m_config.sTime then
    return ActivityState.Preparing, self.m_config.sTime
  elseif serverTime < self.m_config.eTime then
    if self:IsInLockTime() and not self:HasWindowOpenedOnce(ActivityState.Started) then
      return ActivityState.Ended, self.m_config.rTime
    end
    return ActivityState.Started, self.m_config.eTime
  elseif self.m_config.rTime and serverTime < self.m_config.rTime then
    return ActivityState.Ended, self.m_config.rTime
  else
    return ActivityState.Released, -1
  end
end

function SurpriseChestActivityModel:CanTriggerSurpriseChest()
  return self:GetState() == ActivityState.Started and not self:IsInLockTime() and self.m_duration > 0
end

function SurpriseChestActivityModel:_GetValidSurpriseData(order)
  if order == nil then
    return
  end
  local orderScore = order:GetOrderScore() or -1
  for _, chestData in ipairs(self.m_arrChestData) do
    if chestData:CheckOrderScoreRange(orderScore) then
      return chestData
    end
  end
end

function SurpriseChestActivityModel:TriggerSurpriseChest()
  self.m_dbTable:Set(DBKey.TriggerEndTime, "value", tostring(GM.GameModel:GetServerTime() + self.m_duration))
  self:_ClearSurpriseChestData()
  self:_UpdateSurpriseChestData(true)
  GM.BIManager:LogAcquire(self.m_type, self.m_duration, EBIType.TriggerSurpriseChest, true)
end

function SurpriseChestActivityModel:_UpdateSurpriseChestData(bTriggered)
  local orders = GM.MainBoardModel:GetOrders()
  local chestData
  for _, order in pairs(orders) do
    if not self:_HasSurpriseChestData(order:GetId()) then
      chestData = self:_GetValidSurpriseData(order)
      if chestData ~= nil then
        self:_AddSurpriseChestData(order:GetId(), chestData:ToSerilization(order:GetOrderScore()))
      end
    end
  end
  EventDispatcher.DispatchEvent(self.m_activityDefinition.ChestDataStateChanged, bTriggered)
end

function SurpriseChestActivityModel:_OnOrderStateChanged()
  if self:IsInOpeningPeriod() then
    self:_UpdateSurpriseChestData()
  end
end

function SurpriseChestActivityModel:IsInOpeningPeriod()
  return GM.GameModel:GetServerTime() <= self:GetTriggerEndTime()
end

function SurpriseChestActivityModel:HasTriggeredSurpriseChest()
  return self:GetTriggerEndTime() ~= 0
end

function SurpriseChestActivityModel:CanAcquireChestRewards(orderId)
  return self:IsInOpeningPeriod() and self.m_mapOrderId2ChestData[orderId] ~= nil
end

function SurpriseChestActivityModel:TryAcquireChestRewards(orderId)
  if not self:CanAcquireChestRewards(orderId) then
    return
  end
  local chestData = self.m_mapOrderId2ChestData[orderId]
  local rewards = chestData:RandomSelectRewards()
  if not Table.IsEmpty(rewards) then
    local goldCount
    for _, v in pairs(rewards) do
      if v[PROPERTY_TYPE] == EPropertyType.Gold then
        goldCount = v[PROPERTY_COUNT]
        EventDispatcher.DispatchEvent(EEventType.CollectGold, {count = goldCount})
      end
    end
    RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.AcquireChestRewards, nil, CacheItemType.Stack)
  end
  return rewards
end

function SurpriseChestActivityModel:_OnOrderFinished(msg)
  if not self:IsInOpeningPeriod() then
    return
  end
  local order = msg and msg.order
  local rewards
  if order ~= nil then
    local orderId = order:GetId()
    rewards = self:TryAcquireChestRewards(orderId)
    self:_RemoveSurpriseChestData(orderId)
    self.m_mapOrderId2AcquiredRewards = self.m_mapOrderId2AcquiredRewards or {}
    self.m_mapOrderId2AcquiredRewards[order:GetId()] = rewards
  end
end

function SurpriseChestActivityModel:_LoadOpeningSurpriseChestData()
  self.m_mapOrderId2ChestData = {}
  local strData = self.m_dbTable:GetValue(DBKey.OpeningSurpriseChest, "value")
  if not StringUtil.IsNilOrEmpty(strData) then
    local cacheTb = json.decode(StringUtil.Replace(strData, "@", ","))
    for orderId, data in pairs(cacheTb) do
      self.m_mapOrderId2ChestData[orderId] = SurpriseChestData.Create(data)
    end
  end
end

function SurpriseChestActivityModel:_SaveOpeningSurpriseChestData()
  if Table.IsEmpty(self.m_mapOrderId2ChestData) then
    self.m_dbTable:Remove(DBKey.OpeningSurpriseChest)
  else
    local cacheTb = {}
    for orderId, chestData in pairs(self.m_mapOrderId2ChestData) do
      cacheTb[orderId] = chestData:ToSerilization()
    end
    self.m_dbTable:Set(DBKey.OpeningSurpriseChest, "value", StringUtil.Replace(json.encode(cacheTb), ",", "@"))
  end
end

function SurpriseChestActivityModel:_HasSurpriseChestData(orderId)
  return self.m_mapOrderId2ChestData[orderId] ~= nil
end

function SurpriseChestActivityModel:_AddSurpriseChestData(orderId, data)
  self.m_mapOrderId2ChestData[orderId] = SurpriseChestData.Create(data)
  self:_SaveOpeningSurpriseChestData()
end

function SurpriseChestActivityModel:_RemoveSurpriseChestData(orderId)
  self.m_mapOrderId2ChestData[orderId] = nil
  self:_SaveOpeningSurpriseChestData()
end

function SurpriseChestActivityModel:_ClearSurpriseChestData()
  self.m_mapOrderId2ChestData = {}
  self:_SaveOpeningSurpriseChestData()
end

function SurpriseChestActivityModel:GetTriggerEndTime()
  return math.min(tonumber(self.m_dbTable:GetValue(DBKey.TriggerEndTime, "value") or "0"), self.m_config and self.m_config.eTime or 0)
end

function SurpriseChestActivityModel:GetChestDataByOrderId(orderId)
  return self.m_mapOrderId2ChestData[orderId]
end

function SurpriseChestActivityModel:GetAllChestData()
  return self.m_mapOrderId2ChestData or {}
end

function SurpriseChestActivityModel:GetAcquiredRewardsByOrderId(orderId)
  return self.m_mapOrderId2AcquiredRewards and self.m_mapOrderId2AcquiredRewards[orderId]
end

function SurpriseChestActivityModel:GetCountDownText()
  return TimeUtil.ParseTimeDescription(self.m_duration, 2, false, true)
end
