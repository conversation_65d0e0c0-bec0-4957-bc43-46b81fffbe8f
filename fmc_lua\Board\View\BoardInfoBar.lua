BoardInfoBar = {}
BoardInfoBar.__index = BoardInfoBar

function BoardInfoBar:Init(boardModel)
  self.m_infoContent:Init(self, boardModel)
  self.m_removeContent:Init(self, boardModel)
  self:_ShowContent(false)
end

function BoardInfoBar:OnItemSold(itemModel)
  self:_ShowContent(false, itemModel, nil, self.inBoard)
end

function BoardInfoBar:UpdateInfoBar(itemModel, showCookStartTip, inBoard, cookTip)
  self:_ShowContent(itemModel ~= nil, itemModel, showCookStartTip, inBoard, cookTip)
end

function BoardInfoBar:_ShowContent(showInfo, itemModel, showCookStartTip, inBoard, cookTip)
  self.inBoard = inBoard
  local disableContent = showInfo and self.m_removeContent or self.m_infoContent
  local enableContent = showInfo and self.m_infoContent or self.m_removeContent
  disableContent.gameObject:SetActive(false)
  disableContent:OnDisable()
  enableContent.gameObject:SetActive(true)
  enableContent:UpdateContent(itemModel, showCookStartTip, inBoard, cookTip)
end

function BoardInfoBar:GetInfoContent()
  return self.m_infoContent
end

function BoardInfoBar:GetOpenBtnGo()
  return self.m_infoContent.m_openGo
end

function BoardInfoBar:GetSkipBtnGo()
  return self.m_infoContent.m_skipGo
end

function BoardInfoBar:GetCookBtnGo()
  return self.m_infoContent.m_boardInfoCookContent.m_startCookBtn.gameObject
end

function BoardInfoBar:GetUnlockBtnGo()
  return self.m_infoContent.m_unlockGo
end

function BoardInfoBar:GetInfoBtnGo()
  return self.m_infoContent.m_itemTipButton.gameObject
end

function BoardInfoBar:GetActivateBtnGo()
  return self.m_infoContent.m_activateGo
end

function BoardInfoBar:GetCookMaterialsGo()
  return self.m_infoContent.m_boardInfoCookContent.m_materialRootGo
end

function BoardInfoBar:GetCookFirstMaterialTrans()
  return self.m_infoContent.m_boardInfoCookContent.m_showMaterials[1].transform
end

function BoardInfoBar:AdjustDescription()
  self.m_infoContent:_AdjustDescription()
end

BoardInfoContent = {}
BoardInfoContent.__index = BoardInfoContent
BoardInfoContent.SpriteType = {Normal = 1, EnergyBoost = 2}

function BoardInfoContent:Awake()
  self:_AddListeners()
  self:OnShowItemTestInfoChanged()
end

function BoardInfoContent:Init(infoBar, boardModel)
  if BoardInfoContent.SpriteGroupOutlineColor == nil then
    BoardInfoContent.SpriteGroupOutlineColor = {
      [BoardInfoContent.SpriteType.Normal] = UIUtil.ConvertHexColor2CSColor("58597E"),
      [BoardInfoContent.SpriteType.EnergyBoost] = UIUtil.ConvertHexColor2CSColor("8536A2")
    }
  end
  self.m_infoBar = infoBar
  self.m_boardModel = boardModel
  self.m_spriteType = BoardInfoContent.SpriteType.Normal
  self.m_descriptionOriginalSizeDelta = self.m_descriptionText.transform.sizeDelta
  if LocalizationModel:GetCurLanguageInString() == "KO" then
    self.m_descriptionText.lineSpacing = 1.2
  end
  self:OnShowItemTestInfoChanged()
end

function BoardInfoContent:_AddListeners()
  EventDispatcher.AddListener(EEventType.ItemOpened, self, self.OnItemChanged)
  EventDispatcher.AddListener(EEventType.ItemCookStarted, self, self.OnItemChanged)
  EventDispatcher.AddListener(EEventType.ItemTutorial, self, self.OnItemChanged)
  EventDispatcher.AddListener(EEventType.UpdateStorage, self, self.OnItemChanged)
  EventDispatcher.AddListener(EEventType.ShowItemTestInfoChanged, self, self.OnShowItemTestInfoChanged)
  EventDispatcher.AddListener(EEventType.EnergyBoostModeChanged, self, self._UpdateText)
end

function BoardInfoContent:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BoardInfoContent:OnItemChanged(message)
  if self.m_itemModel == message.item then
    self:UpdateContent(self.m_itemModel, self.m_bShowCookStartTip)
  end
end

function BoardInfoContent:OnShowItemTestInfoChanged()
  if not GM.UIManager:CanShowTestUI() then
    self.m_testText.gameObject:SetActive(false)
    return
  end
  self.m_testText.gameObject:SetActive(true)
  self:UpdatePerSecond()
end

function BoardInfoContent:_TryUnlockBubbleBreak()
  local sourceBubble = self.m_itemModel and self.m_itemModel:GetComponent(ItemBubble)
  if sourceBubble ~= nil then
    sourceBubble:SetLockBreak(false)
  end
end

function BoardInfoContent:OnDisable()
  self:_TryUnlockBubbleBreak()
end

function BoardInfoContent:UpdateContent(itemModel, showCookStartTip, inBoard, cookTip)
  self:SetHandEffectActive(false)
  if inBoard == nil then
    inBoard = true
  end
  self.m_bShowCookStartTip = showCookStartTip
  if self.m_itemModel ~= itemModel then
    self:_ResetButtonEffect()
    self:_TryUnlockBubbleBreak()
    local targetBubble = itemModel and itemModel:GetComponent(ItemBubble)
    if targetBubble ~= nil then
      targetBubble:SetLockBreak(true)
    end
  end
  self.m_itemModel = itemModel
  local itemCook = itemModel:GetComponent(ItemCook)
  if itemCook ~= nil and itemCook:GetState() ~= EItemCookState.Empty then
    self.m_boardInfoCookContent:UpdateContent(itemCook, showCookStartTip, inBoard, cookTip)
    self.m_boardInfoCookContent.gameObject:SetActive(true)
    self.m_descriptionText.gameObject:SetActive(false)
    if itemCook:GetState() == EItemCookState.Cooked or not inBoard and itemCook:GetState() == EItemCookState.Cooking then
      SpriteUtil.SetImage(self.m_infoBgImg, ImageFileConfigName.game_desc_bg)
      self:_SetTitleGoVisible(true)
    else
      SpriteUtil.SetImage(self.m_infoBgImg, ImageFileConfigName.game_cook_bg)
      self:_SetTitleGoVisible(false)
    end
  else
    SpriteUtil.SetImage(self.m_infoBgImg, ImageFileConfigName.game_desc_bg)
    self.m_boardInfoCookContent.gameObject:SetActive(false)
    self.m_descriptionText.gameObject:SetActive(true)
    self:_SetTitleGoVisible(true)
  end
  local itemBubble, itemCobweb, itemRewardBubble
  if itemModel ~= nil and inBoard then
    self.m_itemTipButton.gameObject:SetActive(true)
    itemBubble = itemModel:GetComponent(ItemBubble)
    itemCobweb = itemModel:GetComponent(ItemCobweb)
    itemRewardBubble = itemModel:GetComponent(ItemRewardBubble)
    local itemCode = itemModel:GetType()
    if itemBubble ~= nil then
      itemCode = itemBubble:GetInnerItemCode()
    elseif itemCobweb ~= nil then
      itemCode = itemCobweb:GetInnerItemCode()
    elseif itemRewardBubble ~= nil then
      itemCode = itemRewardBubble:GetInnerItemCode()
    end
    local refer = EItemDetailWindowRefer.BoardInfoBar
    if GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.InventoryWindow) ~= nil then
      refer = EItemDetailWindowRefer.Inventory
    end
    self.m_itemTipButton:UpdateItemType(itemCode, nil, refer)
  else
    self.m_itemTipButton.gameObject:SetActive(false)
  end
  if itemModel ~= nil and self.m_boardModel:CanItemSell(itemModel) and inBoard then
    local sellPrice = itemModel:GetSellPrice()
    if sellPrice[PROPERTY_TYPE] ~= DELETE_TAG then
      self.m_sellGo:SetActive(true)
      self.m_removeGo:SetActive(false)
      self.m_sellCostText.text = "+" .. sellPrice[PROPERTY_COUNT]
      SpriteUtil.SetImage(self.m_sellIcon, EPropertySprite[sellPrice[PROPERTY_TYPE]])
    else
      self.m_sellGo:SetActive(false)
      self.m_removeGo:SetActive(true)
    end
  else
    self.m_sellGo:SetActive(false)
    self.m_removeGo:SetActive(false)
  end
  local itemSpread = itemModel and itemModel:GetComponent(ItemSpread)
  if itemSpread == nil or itemSpread:GetState() ~= ItemSpreadState.Closed then
    self.m_openGo:SetActive(false)
  elseif not self.m_boardModel:HasOpeningItem() then
    self.m_openGo:SetActive(true)
    self.m_openCostText.text = TimeUtil.ParseTimeDescription(itemSpread:GetOpenDuration(), 2, false, true)
  end
  local showSkip, speedUpCost, isSkipProp = ItemModelHelper.GetSkipInfo(self.m_itemModel, inBoard)
  EventDispatcher.DispatchEvent(EEventType.ToggleSkipPropHud, isSkipProp)
  self.m_showSkip = showSkip
  self.m_skipGo:SetActive(showSkip)
  if showSkip then
    if itemSpread and itemSpread:GetState() == ItemSpreadState.Opening then
      self.m_skipClockGo:SetActive(true)
      UIUtil.SetAnchoredPosition(self.m_skipButtonTrans, 0, -15)
      UIUtil.SetLocalScale(self.m_skipButtonTrans.parent, 0.8, 0.8, 1)
    else
      self.m_skipClockGo:SetActive(false)
      UIUtil.SetAnchoredPosition(self.m_skipButtonTrans, 0, -27)
      UIUtil.SetLocalScale(self.m_skipButtonTrans.parent, 1, 1, 1)
    end
  end
  self.m_showUnlock = itemBubble ~= nil or itemCobweb ~= nil
  self.m_unlockGo:SetActive(self.m_showUnlock)
  if self.m_showUnlock then
    local breakCost = itemBubble ~= nil and itemBubble:GetBreakCost() or itemCobweb:GetBreakCost()
    UIUtil.SetActive(self.m_unlockFreeGo, breakCost <= 0)
    UIUtil.SetActive(self.m_unlockCostText.gameObject, 0 < breakCost)
    self.m_unlockCostText.text = breakCost
    self.m_unlockTitleText.text = GM.GameTextModel:GetText(itemBubble ~= nil and "hint_button_desc_bubblePop" or "btn_unlock")
  end
  self.m_showBubbleRemove = itemBubble ~= nil and not GM.TutorialModel:HasAnyStrongTutorialOngoing()
  self.m_bubbleRemoveGo:SetActive(self.m_showBubbleRemove)
  local itemAccelerate = itemModel and itemModel:GetComponent(ItemAccelerate)
  self.m_activateGo:SetActive(false)
  if itemAccelerate ~= nil and not itemAccelerate:IsActivated() then
    self.m_activateGo:SetActive(true)
  end
  self.m_accelerateInfoGo:SetActive(false)
  if itemAccelerate ~= nil and itemAccelerate:IsActivated() then
    self.m_accelerateInfoGo:SetActive(true)
  end
  local itemSplit = itemModel and itemModel:GetComponent(ItemSplit)
  UIUtil.SetActive(self.m_splitCountContentGo, itemSplit ~= nil)
  if itemSplit ~= nil then
    self.m_splitCountText.text = itemSplit:GetSplitUseCount()
  end
  self:UpdatePerSecond()
  self:_AdjustDescription()
end

function BoardInfoContent:_SetTitleGoVisible(visible)
  self.m_titleGo:SetActive(visible)
  UIUtil.SetAnchoredPosition(self.m_tipBtnTrans, visible and 423.4 or 24)
end

function BoardInfoContent:_GetSpriteType(itemModel)
  if itemModel == nil then
    return BoardInfoContent.SpriteType.Normal
  end
  if GM.EnergyBoostModel:CanEnergyBoost(itemModel:GetType()) then
    return BoardInfoContent.SpriteType.EnergyBoost
  end
  return BoardInfoContent.SpriteType.Normal
end

function BoardInfoContent:UpdatePerSecond()
  if self.m_itemModel == nil then
    return
  end
  if self.m_showSkip then
    local needSkip, speedUpCost, isSkipProp, skipPropsFinishCost = ItemModelHelper.GetSkipInfo(self.m_itemModel)
    if needSkip then
      UIUtil.SetActive(self.m_skipFreeGo, speedUpCost <= 0)
      UIUtil.SetActive(self.m_skipCostText.gameObject, 0 < speedUpCost)
      self.m_skipCostText.text = speedUpCost
      local itemSpread = self.m_itemModel:GetComponent(ItemSpread)
      local itemTransform = self.m_itemModel:GetComponent(ItemTransform)
      local restDuration
      if itemSpread ~= nil then
        restDuration = itemSpread:GetTimerDuration() + itemSpread:GetStartTimer() - GM.GameModel:GetServerTime()
      elseif itemTransform ~= nil then
        restDuration = itemTransform:GetDuration() + itemTransform:GetStartTimer() - GM.GameModel:GetServerTime()
      end
      if restDuration ~= nil then
        self.m_skipTimeText.text = TimeUtil.ParseTimeDescription(restDuration, 2, false, false)
        self.m_skipTimeText.transform.sizeDelta = Vector2(math.min(120, self.m_skipTimeText.preferredWidth), 40)
      end
      local showEffect = GM.ConfigModel:IsServerControlOpen(EGeneralConfType.SkipHintEffect) and isSkipProp and skipPropsFinishCost ~= nil and skipPropsFinishCost <= speedUpCost
      self.m_skipLoopAnimator.enabled = showEffect
      self.m_skipEffectGo:SetActive(showEffect)
      if isSkipProp then
        SpriteUtil.SetImage(self.m_skipIcon, ImageFileConfigName.icon_skipprop)
      else
        SpriteUtil.SetImage(self.m_skipIcon, ImageFileConfigName.icon_gem)
      end
    else
      self:UpdateContent(self.m_itemModel)
    end
  end
  if self.m_accelerateInfoGo.activeSelf then
    local itemAccelerate = self.m_itemModel:GetComponent(ItemAccelerate)
    local restDuration = itemAccelerate:GetTimerDuration() + itemAccelerate:GetStartTimer() - GM.GameModel:GetServerTime()
    self.m_accelerateInfoText.text = TimeUtil.ParseTimeDescription(restDuration, 2, false, false)
  end
  if GameConfig.IsTestMode() and self.m_testText.gameObject.activeSelf then
    self.m_testText.text = ItemModelHelper.FormatCost2String(self.m_itemModel) .. "sc:" .. GM.ItemDataModel:GetItemScore(self.m_itemModel:GetType())
  end
end

function BoardInfoContent:_GetTitle(itemType)
  if itemType == nil then
    return ""
  end
  local levelSuffix
  if self:_IsItemInChain(itemType) then
    local level = GM.ItemDataModel:GetChainLevel(itemType)
    levelSuffix = GM.GameTextModel:GetText("hint_title_level", level)
  end
  local title = GM.GameTextModel:GetText(ItemNameDefinition.GetName(itemType))
  if levelSuffix ~= nil then
    title = title .. " " .. levelSuffix
  end
  return title
end

function BoardInfoContent:_IsItemInChain(itemType)
  local chainId = GM.ItemDataModel:GetChainId(itemType)
  return GM.ItemDataModel:GetChainMaxLevel(chainId) ~= 1
end

function BoardInfoContent:_GetDescription(itemModel)
  local gameTextModel = GM.GameTextModel
  if itemModel == nil then
    return gameTextModel:GetText("hint_desc_blank")
  end
  local itemCobweb = itemModel:GetComponent(ItemCobweb)
  if itemCobweb ~= nil then
    local tempItem = ItemModelFactory.CreateWithCode(nil, nil, itemCobweb:GetInnerItemCode(), false)
    local desc = self:_GetDescription(tempItem)
    tempItem:Destroy()
    return desc
  end
  local itemSpread = itemModel:GetComponent(ItemSpread)
  if itemSpread ~= nil and itemSpread:GetState() == ItemSpreadState.Initializing then
    local beforeKey = "item_" .. itemModel:GetType() .. "_desc_before"
    if gameTextModel:HasText(beforeKey) then
      return gameTextModel:GetText(beforeKey)
    end
  end
  local itemAccelerate = itemModel:GetComponent(ItemAccelerate)
  if itemAccelerate ~= nil and itemAccelerate:IsActivated() then
    local key = "item_" .. itemModel:GetType() .. "_desc_activated"
    return gameTextModel:GetText(key)
  end
  local specialKey = "item_" .. itemModel:GetType() .. "_desc"
  if gameTextModel:HasText(specialKey) or ItemDetailWindow.ShowDescInfo(itemModel:GetType()) then
    return gameTextModel:GetText(specialKey)
  end
  if GM.ItemDataModel:IsDishes(itemModel:GetType()) then
    return gameTextModel:GetText("dishItem_generalDesc")
  end
  if itemModel:GetComponent(ItemBubble) ~= nil then
    return gameTextModel:GetText("hint_desc_in_bubble_new")
  end
  local itemDesc
  local needLevelDesc = true
  if itemSpread ~= nil then
    if itemSpread:GetOpenDuration() ~= nil then
      local state = itemSpread:GetState()
      if state == ItemSpreadState.Closed then
        if self.m_boardModel:HasOpeningItem() then
          itemDesc = gameTextModel:GetText("hint_desc_cannot_open")
        else
          itemDesc = gameTextModel:GetText("hint_desc_open")
        end
      elseif state == ItemSpreadState.Opening then
        itemDesc = gameTextModel:GetText("hint_desc_opening")
      elseif itemSpread:IsChestUsedOnce() then
        needLevelDesc = false
      end
    end
    if itemDesc == nil then
      local genItems = {}
      local genItemChains = {}
      local modelConfig = GM.ItemDataModel:GetModelConfig(itemModel:GetCode())
      local generatedItems = modelConfig.GeneratedItems or {}
      for _, generatedItem in ipairs(generatedItems) do
        genItems[generatedItem.Code] = true
        local chainId = GM.ItemDataModel:GetChainId(generatedItem.Code)
        genItemChains[chainId] = true
      end
      generatedItems = modelConfig.Transform or {}
      for _, generatedItem in ipairs(generatedItems) do
        genItems[generatedItem.Currency] = true
        local chainId = GM.ItemDataModel:GetChainId(generatedItem.Currency)
        genItemChains[chainId] = true
      end
      local genItemsDesc = ""
      if modelConfig.DropOnSpot == 1 and modelConfig.DropsTotal == 1 then
        genItems = Table.GetKeys(genItems)
        table.sort(genItems)
        local count = #genItems
        for index, genItem in ipairs(genItems) do
          genItemsDesc = genItemsDesc .. gameTextModel:GetText("item_" .. genItem .. "_name")
          if index ~= count then
            genItemsDesc = genItemsDesc .. ", "
          end
        end
      else
        genItemChains = Table.GetKeys(genItemChains)
        table.sort(genItemChains)
        local count = #genItemChains
        for index, genItem in ipairs(genItemChains) do
          genItemsDesc = genItemsDesc .. gameTextModel:GetText("chain_" .. genItem .. "_name")
          if index ~= count then
            genItemsDesc = genItemsDesc .. ", "
          end
        end
      end
      if itemSpread:IsAutoSpread() then
        itemDesc = gameTextModel:GetText("hint_desc_auto_spawn", genItemsDesc)
      elseif modelConfig.DropsTotal == 1 then
        itemDesc = gameTextModel:GetText("hint_desc_tap_transform", genItemsDesc)
      else
        itemDesc = gameTextModel:GetText("hint_desc_tap_spawn", genItemsDesc)
      end
    end
  end
  local itemCook = itemModel:GetComponent(ItemCook)
  if itemCook ~= nil and itemCook:GetState() == EItemCookState.Empty then
    itemDesc = gameTextModel:GetText("hint_desc_equipment")
  end
  local desc
  if needLevelDesc then
    if itemModel:GetMergedType() == nil then
      desc = gameTextModel:GetText("hint_desc_max_level")
    else
      desc = gameTextModel:GetText("hint_desc_merge", gameTextModel:GetText(ItemNameDefinition.GetName(itemModel:GetMergedType())))
    end
    if itemDesc ~= nil then
      desc = itemDesc .. " " .. desc
    end
  else
    desc = itemDesc and itemDesc or ""
  end
  return desc
end

function BoardInfoContent:_UpdateText(itemModel)
  itemModel = itemModel or self.m_itemModel
  local itemType = itemModel and itemModel:GetType() or nil
  if itemType == ItemType.Cobweb then
    itemType = itemModel:GetComponent(ItemCobweb):GetInnerItemCode()
  elseif itemType == ItemType.Bubble then
    itemType = itemModel:GetComponent(ItemBubble):GetInnerItemCode()
  end
  self.m_titleText.text = self:_GetTitle(itemType)
  local strText = self:_GetDescription(itemModel)
  strText = strText .. self:_GetEnergyBoostDescription(itemModel)
  self.m_descriptionText.text = strText
end

function BoardInfoContent:_GetEnergyBoostDescription(itemModel)
  if itemModel == nil then
    return ""
  end
  if GM.EnergyBoostModel:CanEnergyBoost(itemModel:GetCode()) then
    return " " .. GM.GameTextModel:GetText("double_energy_info_tip1")
  end
  return ""
end

function BoardInfoContent:_AdjustDescription()
  local scale
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_buttonGroup.transform)
  local adjustSize = Vector2(self.m_descriptionOriginalSizeDelta.x - self.m_buttonGroup.preferredWidth, self.m_descriptionOriginalSizeDelta.y)
  self.m_descriptionText.transform.sizeDelta = adjustSize
  self:_UpdateText()
end

function BoardInfoContent:OnSellButtonClicked()
  if self.m_disableSellButton then
    return
  end
  if false then
    local itemType = self.m_itemModel:GetType()
    GM.UIManager:OpenView(UIPrefabConfigName.DeleteItemConfirmWindow, self.m_itemModel, function()
      if self.m_itemModel == nil then
        Log.Assert(false, "[BoardInfoContent]itemModel不能为空,type:" .. tostring(itemType))
        return
      end
      itemType = self.m_itemModel:GetType()
      self.m_boardModel:SellItem(self.m_itemModel)
      self.m_infoBar:OnItemSold(self.m_itemModel)
      GM.BIManager:LogUI(EBIType.DeleteItemConfirmOkClick, itemType, EBIReferType.UserClick)
    end)
  else
    self.m_boardModel:SellItem(self.m_itemModel)
    self.m_infoBar:OnItemSold(self.m_itemModel)
  end
end

function BoardInfoContent:OnOpenButtonClicked()
  self.m_boardModel:OpenItem(self.m_itemModel)
  self:UpdateContent(self.m_itemModel)
end

function BoardInfoContent:OnSkipButtonClicked()
  self.m_boardModel:SpeedUpItem(self.m_itemModel)
  self:UpdateContent(self.m_itemModel)
end

function BoardInfoContent:OnRemoveBubbleButtonClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.RemoveBubbleConfirmWindow, self.m_itemModel, function(model)
    if self.m_itemModel == model then
      self:OnSkipButtonClicked()
      GM.BIManager:LogUI(EBIType.RemoveBubbleConfirmClick, model:GetComponent(ItemBubble):GetInnerItemCode(), EBIReferType.UserClick)
    end
  end)
end

function BoardInfoContent:OnUnlockButtonClicked()
  self.m_boardModel:BreakItem(self.m_itemModel)
end

function BoardInfoContent:OnActivateButtonClicked()
  self.m_boardModel:ActivateItem(self.m_itemModel)
  self:UpdateContent(self.m_itemModel)
end

function BoardInfoContent:SetHandEffectActive(active)
  self.m_handEffectGo:SetActive(active)
end

function BoardInfoContent:SetDisableSellButton(disabled)
  self.m_disableSellButton = disabled
end

function BoardInfoContent:PlaySkipButtonEffect()
  if self.m_skipAnimator:GetCurrentAnimatorStateInfo(0):IsName("Init") then
    self.m_skipAnimator:SetTrigger("Effect")
  end
end

function BoardInfoContent:_ResetButtonEffect()
  if self.m_skipAnimator.gameObject.activeInHierarchy then
    self.m_skipAnimator:Update(10)
  end
  self.m_skipButtonEffectGo:SetActive(false)
end

function BoardInfoContent:OnUndoButtonClicked()
  self.m_boardModel:UndoSellItem(self.m_itemModel)
end

BoardRemoveContent = {}
BoardRemoveContent.__index = BoardRemoveContent

function BoardRemoveContent:Init(infoBar, boardModel)
  self.m_infoBar = infoBar
  self.m_boardModel = boardModel
end

function BoardRemoveContent:OnDisable()
  if self.m_itemModel then
    GM.BIManager:LogAction(EBIType.ItemSellDone, ItemModelHelper.FormatCost2String(self.m_itemModel))
    self.m_itemModel = nil
  end
end

function BoardRemoveContent:UpdateContent(itemModel)
  self.m_itemModel = itemModel
  EventDispatcher.DispatchEvent(EEventType.ToggleSkipPropHud, false)
  if itemModel == nil then
    self.m_removeContentGo:SetActive(false)
    self.m_blankTextGo:SetActive(true)
  else
    self.m_removeContentGo:SetActive(true)
    self.m_blankTextGo:SetActive(false)
    local spriteName = GM.ItemDataModel:GetSpriteName(itemModel:GetType())
    SpriteUtil.SetImage(self.m_iconImg, spriteName, true)
    local sellPrice = itemModel:GetSellPrice()
    local isSell = sellPrice[PROPERTY_TYPE] ~= DELETE_TAG
    self.m_descriptionText.text = GM.GameTextModel:GetText(isSell and "hint_sold" or "hint_removed")
    if isSell then
      self.m_sellPriceGo:SetActive(true)
      self.m_sellPriceText.text = "-" .. sellPrice[PROPERTY_COUNT]
      SpriteUtil.SetImage(self.m_sellIcon, EPropertySprite[sellPrice[PROPERTY_TYPE]])
    else
      self.m_sellPriceGo:SetActive(false)
    end
  end
end

function BoardRemoveContent:OnUndoButtonClicked()
  self.m_boardModel:UndoSellItem(self.m_itemModel)
end
