local Step = {
  HighlightAvatar = "1",
  HighlightTrack = "2",
  HighlightEndLine = "3",
  HighlightHelp = "4"
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._OnCloseView)
  for _, activityDefinition in pairs(CoinRaceActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnCoinRaceStateChange)
  end
end

function Executer:_OnOpenView(msg)
  for activityType, activityDefinition in pairs(CoinRaceActivityDefinition) do
    if msg.name == activityDefinition.MainWindowPrefabName and activityDefinition.TutorialFirstRoundId == self:GetTutorialId() then
      local model = GM.ActivityManager:GetModel(activityType)
      if model and model:GetCurrentRound() == 1 then
        self.m_activityDefinition = activityDefinition
        self.m_mainWindow = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
        self:_ExecuteStep1()
      end
    elseif not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) and msg.name == activityDefinition.HelpWindowPrefabName then
      self:Finish(self.m_gesture)
    end
  end
end

function Executer:_OnCloseView(msg)
  for _, activityDefinition in pairs(CoinRaceActivityDefinition) do
    if msg.name == activityDefinition.MainWindowPrefabName and self.m_strOngoingDatas == Step.HighlightHelp then
      self:Finish(self.m_gesture)
    end
  end
end

function Executer:_OnCoinRaceStateChange(msg)
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) and (not msg or not msg.isEnterCompetition) then
    self:Finish(self.m_gesture)
  end
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.HighlightAvatar
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local window = self.m_mainWindow
    if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
      self:Finish()
      return
    end
    local transf = window:GetAvatarAreaTransf()
    TutorialHelper.UpdateMask(transf.position, transf.sizeDelta, function()
      self:_ExecuteStep2()
    end, false)
    local percent = 50
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText("coin_race_main_tutorial_1"), percent)
  end, 0.5)
end

function Executer:_ExecuteStep2()
  self.m_strOngoingDatas = Step.HighlightTrack
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local window = self.m_mainWindow
  if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
    self:Finish()
    return
  end
  local transf = window:GetTrackTransf()
  TutorialHelper.UpdateMask(transf.position, transf.sizeDelta, function()
    self:_ExecuteStep3()
  end, false)
  local percent = 10
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText("coin_race_main_tutorial_2"), percent)
end

function Executer:_ExecuteStep3()
  self.m_strOngoingDatas = Step.HighlightEndLine
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local window = self.m_mainWindow
  if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
    self:Finish()
    return
  end
  local transf = window:GetEndLineTransf()
  TutorialHelper.UpdateMask(transf.position, transf.sizeDelta, function()
    self:_ExecuteStep4()
  end, false)
  local percent = 50
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText("coin_race_main_tutorial_3"), percent)
end

function Executer:_ExecuteStep4()
  self.m_strOngoingDatas = Step.HighlightHelp
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local window = self.m_mainWindow
  if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
    self:Finish()
    return
  end
  self:SetStrongTutorial(false)
  local transf = window:GetHelpTransf()
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(transf)
  self.m_gesture:UpdateSortingOrder(window:GetSortingOrder() + 1)
  TutorialHelper.HideMask()
  TutorialHelper.HideDialog()
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
