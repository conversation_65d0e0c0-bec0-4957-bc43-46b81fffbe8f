BreakEggState = {
  NoStart = 1,
  WaitBreakEgg4 = 2,
  WaitRevive = 3,
  WaitBreakEgg3 = 4
}
local DBKey = {
  BreakEggFree = "breakEggFree",
  BreakEggState = "breakEggState",
  BreakEggRound = "breakEggRound",
  CurrentStep = "currentStep",
  CollectRewards = "collectRewards",
  ReviveCount = "reviveCount",
  CompleteRId = "completeRId",
  BreakEggIndex = "breakEggIndex"
}
local DBColumnValue = "value"
local BreakEggConfigKey = {
  RevivePrice = "break_egg_revive",
  Jackpot = "break_egg_jackpot",
  Rating = "break_egg_rating"
}
BreakEggEventType = {RefreshFreeTime = 1}
BreakEggModel = setmetatable({}, BaseActivityModel)
BreakEggModel.__index = BreakEggModel

function BreakEggModel:Init(virtualDBTable)
  BaseActivityModel.Init(self, ActivityType.BreakEgg, virtualDBTable)
  self:_UpdateEqualGem()
end

function BreakEggModel:ResetData()
  self:LogActivity(EBIType.ActivityRankUp, self:GetCurrentStep() - 1)
  self.m_dbTable:Set(DBKey.CurrentStep, DBColumnValue, 1)
  self.m_dbTable:Set(DBKey.ReviveCount, DBColumnValue, 0)
  self.m_dbTable:Set(DBKey.BreakEggIndex, DBColumnValue, 0)
  self.m_dbTable:Remove(DBKey.CollectRewards)
  self:SetBreakEggState(BreakEggState.NoStart)
  self.m_equalRewardGem = 0
end

function BreakEggModel:IsActivityOpen()
  return self:GetState() == ActivityState.Started
end

function BreakEggModel:_LoadOtherServerConfig(config)
  local configRevive = Table.DeepCopy(config[BreakEggConfigKey.RevivePrice], true)
  self.m_arrRevive = {}
  for _, value in ipairs(configRevive) do
    if value.revive_time == 0 then
      self.m_startBreakEggPrice = value.price
    else
      self.m_arrRevive[#self.m_arrRevive + 1] = value
    end
  end
  Log.Assert(self.m_startBreakEggPrice ~= nil and self.m_startBreakEggPrice ~= 0, "start break egg consum gem config error!")
  table.sort(self.m_arrRevive, function(a, b)
    return a.revive_time < b.revive_time
  end)
  self.m_arrJockpot = Table.DeepCopy(config[BreakEggConfigKey.Jackpot], true)
  table.sort(self.m_arrJockpot, function(a, b)
    return a.ID < b.ID
  end)
  self.m_arrRating = {}
  local configRating = Table.DeepCopy(config[BreakEggConfigKey.Rating])
  for _, value in ipairs(configRating) do
    self.m_arrRating[#self.m_arrRating + 1] = BreakEggRating.Create(value)
  end
  table.sort(self.m_arrRating, function(a, b)
    return a:GetStep() < b:GetStep()
  end)
end

function BreakEggModel:_OnStateChanged()
  EventDispatcher.DispatchEvent(EEventType.BreakEggStateChanged)
end

function BreakEggModel:GetStartBreakEggPrice()
  return self.m_startBreakEggPrice
end

function BreakEggModel:BreakEgg(eggIdx)
  local step = self:GetCurrentStep()
  local maxStep = self:GetStepTotal()
  if step > maxStep then
    return
  end
  local curEggIdx = self:GetBreakEggIndex()
  Log.Assert(1 <= eggIdx and eggIdx <= 4 and curEggIdx ~= eggIdx, "eggIdx param is error!")
  local round = self:GetBreakEggRound()
  local reward = self.m_arrRating[step]:GetBreakEggReward()
  local arrShowReward = {}
  local hasGem = reward ~= nil and reward[PROPERTY_TYPE] == EPropertyType.Gem
  local excludeReward = reward ~= nil and {reward} or nil
  local count = 3
  if (curEggIdx ~= 0 or reward ~= nil) and step ~= 1 then
    count = 2
  end
  local showReward = self.m_arrRating[step]:GetShowReward(count, excludeReward, not hasGem)
  if curEggIdx == 0 and reward ~= nil and step ~= 1 then
    showReward[#showReward + 1] = "nil"
  end
  showReward = Table.ListAlwaysRandomSelectN(showReward, #showReward)
  local sfxAudio = reward ~= nil and AudioFileConfigName.sfxBreakEggSuc or AudioFileConfigName.sfxBreakEggFail
  GM.AudioModel:PlayEffect(sfxAudio)
  if reward == nil then
    self:SetBreakEggState(BreakEggState.WaitRevive)
    self:SetBreakEggIndex(eggIdx)
    self:LogActivity(EBIType.BreakEggStep, round .. "," .. step .. ",0")
  else
    self:AddCurrentStep()
    self:SetBreakEggState(step == maxStep and BreakEggState.NoStart or BreakEggState.WaitBreakEgg4)
    self:SetBreakEggIndex(0)
    self:SaveCollectRewards(reward)
    if step == maxStep then
      local arrReward = self:GetCollectRewards()
      local completeReward = self:GetCompleteReward()
      arrReward[#arrReward + 1] = completeReward
      RewardApi.AcquireRewardsLogic(arrReward, EPropertySource.Give, EBIType.BreakEggReward, EGameMode.Board, CacheItemType.Stack)
      self:AddCompleteRewardId()
      self.m_lastCompleteReward = completeReward
      self:LogReward(arrReward, "1")
    end
    self:LogActivity(EBIType.BreakEggStep, round .. "," .. step .. "," .. reward[PROPERTY_TYPE])
  end
  local k = 1
  for i = 1, 4 do
    if i == eggIdx then
      arrShowReward[i] = reward
    elseif i == curEggIdx then
      arrShowReward[i] = nil
    else
      arrShowReward[i] = showReward[k]
      if arrShowReward[i] == "nil" then
        arrShowReward[i] = nil
      end
      k = k + 1
    end
  end
  return arrShowReward
end

function BreakEggModel:GetCollectRewards()
  local str = self.m_dbTable:GetValue(DBKey.CollectRewards, DBColumnValue)
  if StringUtil.IsNilOrEmpty(str) then
    return {}
  end
  local arr = json.decode(str)
  table.sort(arr, function(a, b)
    local t1, t2 = 10, 10
    if a[PROPERTY_TYPE] == EPropertyType.Gem then
      t1 = 1
    elseif a[PROPERTY_TYPE] == EPropertyType.Energy or a[PROPERTY_TYPE] == EPropertyType.EventEnergy then
      t1 = 2
    elseif a[PROPERTY_TYPE] == EPropertyType.Gold then
      t1 = 3
    end
    if b[PROPERTY_TYPE] == EPropertyType.Gem then
      t2 = 1
    elseif b[PROPERTY_TYPE] == EPropertyType.Energy or b[PROPERTY_TYPE] == EPropertyType.EventEnergy then
      t2 = 2
    elseif b[PROPERTY_TYPE] == EPropertyType.Gold then
      t2 = 3
    end
    return t1 < t2
  end)
  return arr
end

function BreakEggModel:SaveCollectRewards(rewards)
  local arr = self:GetCollectRewards()
  if Table.IsEmpty(arr) then
    arr = {}
  end
  local bMerge = false
  for _, value in ipairs(arr) do
    if value[PROPERTY_TYPE] == rewards[PROPERTY_TYPE] then
      value[PROPERTY_COUNT] = value[PROPERTY_COUNT] + rewards[PROPERTY_COUNT]
      bMerge = true
      break
    end
  end
  if not bMerge then
    arr[#arr + 1] = rewards
  end
  self.m_dbTable:Set(DBKey.CollectRewards, DBColumnValue, json.encode(arr))
  self:_UpdateEqualGem()
end

function BreakEggModel:GetStepTotal()
  if not self.m_arrRating then
    return 0
  end
  return #self.m_arrRating
end

function BreakEggModel:GetLastCompleteReward()
  return self.m_lastCompleteReward
end

function BreakEggModel:GetCompleteReward()
  local rid = self:GetCompleteRewardId()
  rid = math.min(rid, #self.m_arrJockpot)
  return ConfigUtil.GetCurrencyFromStr(self.m_arrJockpot[rid].reward[1])
end

function BreakEggModel:GetCurrentStep()
  return self.m_dbTable:GetValue(DBKey.CurrentStep, DBColumnValue) or 1
end

function BreakEggModel:AddCurrentStep()
  local step = self:GetCurrentStep()
  self.m_dbTable:Set(DBKey.CurrentStep, DBColumnValue, step + 1)
end

function BreakEggModel:IsFree()
  local free = self.m_dbTable:GetValue(DBKey.BreakEggFree, DBColumnValue) or 1
  return free ~= 0
end

function BreakEggModel:ConsumFree()
  self.m_dbTable:Set(DBKey.BreakEggFree, DBColumnValue, 0)
  self.event:Call(BreakEggEventType.RefreshFreeTime)
end

function BreakEggModel:GetReviveCount()
  return self.m_dbTable:GetValue(DBKey.ReviveCount, DBColumnValue) or 0
end

function BreakEggModel:AddReviveCount()
  local ct = self:GetReviveCount()
  self.m_dbTable:Set(DBKey.ReviveCount, DBColumnValue, ct + 1)
end

function BreakEggModel:GetBreakEggIndex()
  return self.m_dbTable:GetValue(DBKey.BreakEggIndex, DBColumnValue) or 0
end

function BreakEggModel:SetBreakEggIndex(idx)
  self.m_dbTable:Set(DBKey.BreakEggIndex, DBColumnValue, idx)
end

function BreakEggModel:GetBreakEggRound()
  return self.m_dbTable:GetValue(DBKey.BreakEggRound, DBColumnValue) or 0
end

function BreakEggModel:AddBreakEggRound()
  local curRound = self:GetBreakEggRound()
  self.m_dbTable:Set(DBKey.BreakEggRound, DBColumnValue, curRound + 1)
end

function BreakEggModel:GetCompleteRewardId()
  return self.m_dbTable:GetValue(DBKey.CompleteRId, DBColumnValue) or 1
end

function BreakEggModel:AddCompleteRewardId()
  local rid = self:GetCompleteRewardId()
  self.m_dbTable:Set(DBKey.CompleteRId, DBColumnValue, rid + 1)
end

function BreakEggModel:GetRevivePrice()
  local reviveCount = self:GetReviveCount()
  local index = math.min(reviveCount + 1, #self.m_arrRevive)
  return self.m_arrRevive[index].price
end

function BreakEggModel:ReviveContinue()
  self:SetBreakEggState(BreakEggState.WaitBreakEgg3)
  self:AddReviveCount()
end

function BreakEggModel:GetShowProbability()
  local curStep = self:GetCurrentStep()
  if curStep > self:GetStepTotal() then
    return {}
  end
  return self.m_arrRating[curStep]:GetShowRewardProb()
end

function BreakEggModel:GetBreakEggState()
  return self.m_dbTable:GetValue(DBKey.BreakEggState, DBColumnValue) or BreakEggState.NoStart
end

function BreakEggModel:SetBreakEggState(state)
  self.m_dbTable:Set(DBKey.BreakEggState, DBColumnValue, state)
end

function BreakEggModel:StartBreakEgg()
  local sucFun = function(freeCode, gCost)
    self:AddBreakEggRound()
    self:SetBreakEggState(BreakEggState.WaitBreakEgg4)
    local act = self:GetBreakEggRound() .. "," .. freeCode .. "," .. gCost
    self:LogActivity(EBIType.BreakEggStart, act)
  end
  if self:IsFree() then
    self:ConsumFree()
    sucFun(1, 0)
    return true
  end
  local gemCost = self:GetStartBreakEggPrice()
  if not GM.PropertyDataManager:Consume(EPropertyType.Gem, gemCost, EBIType.BreakEggStart, false, EBIConsumerType.BreakEgg) then
    return false, gemCost - GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
  end
  sucFun(0, gemCost)
  return true
end

function BreakEggModel:LogActivity(scene, action)
  GM.BIManager:LogActivity(self.m_type, self:GetId(), scene, action)
end

function BreakEggModel:LogReward(rewards, lSt)
  local strRewards = "["
  for i = 1, #rewards do
    strRewards = strRewards .. rewards[i][PROPERTY_TYPE] .. "-" .. rewards[i][PROPERTY_COUNT]
    if i ~= #rewards then
      strRewards = strRewards .. ","
    end
  end
  strRewards = strRewards .. "]"
  local round = self:GetBreakEggRound()
  local step = self:GetCurrentStep() - 1
  lSt = lSt or "0"
  self:LogActivity(EBIType.BreakEggReward, round .. "," .. step .. "," .. lSt .. "," .. strRewards)
end

function BreakEggModel:HasReward()
  local curStep = self:GetCurrentStep()
  local maxStep = self:GetStepTotal()
  local state = self:GetBreakEggState()
  local collectReward = self:GetCollectRewards()
  return not Table.IsEmpty(collectReward) and 1 < curStep and curStep <= maxStep and state ~= BreakEggState.WaitRevive
end

function BreakEggModel:GetRewardWhenActivityEnd()
  local collectReward = Table.DeepCopy(self:GetCollectRewards())
  RewardApi.AcquireRewardsLogic(collectReward, EPropertySource.Give, EBIType.BreakEggReward, EGameMode.Board, CacheItemType.Stack)
  self:LogReward(collectReward, "0")
  GM.UIManager:OpenView(UIPrefabConfigName.BreakEggRewardWIndow, collectReward, "break_egg_reward_title", false)
  self:ResetData()
end

function BreakEggModel:GetResourceLabels()
  return {
    AddressableLabel.BreakEgg
  }
end

function BreakEggModel:GetEqualGem()
  local n = self.m_equalRewardGem or 0
  return math.ceil(n)
end

function BreakEggModel:_UpdateEqualGem()
  self.m_equalRewardGem = 0
  local arrRewards = self:GetCollectRewards()
  local curStep = self:GetCurrentStep()
  local maxStep = self:GetStepTotal()
  if 0 < maxStep and curStep > maxStep then
    local completeReward = self:GetCompleteReward()
    arrRewards[#arrRewards + 1] = completeReward
  end
  for i = 1, #arrRewards do
    local price = 0
    if arrRewards[i][PROPERTY_TYPE] == EPropertyType.Gem then
      price = 1
    elseif arrRewards[i][PROPERTY_TYPE] == EPropertyType.Energy or arrRewards[i][PROPERTY_TYPE] == EPropertyType.EventEnergy then
      price = 0.4
    elseif arrRewards[i][PROPERTY_TYPE] == EPropertyType.Gold then
      price = 0.5
    else
      local tp = self:_GetItemPrice(arrRewards[i][PROPERTY_TYPE])
      price = tp or 10
    end
    self.m_equalRewardGem = self.m_equalRewardGem + price * arrRewards[i][PROPERTY_COUNT]
  end
end

function BreakEggModel:_GetItemPrice(type)
  local config = GM.ItemDataModel:GetPropertyConfig(type)
  if config then
    return config.Price
  end
  if type == "3001" then
    return 3
  elseif type == "scissors_1" then
    return 20
  elseif type == "time_skip_1" then
    return 25
  elseif type == "4101" or type == "3101" then
    return 30
  elseif type == "infi_ene_1" or type == "level_down_piece_2" then
    return 40
  elseif type == "time_skip_2" or type == "4001" then
    return 50
  end
end

local HighImg = {}

function BreakEggModel:GetHighImg(protype)
  return HighImg[protype]
end

BreakEggRating = {}
BreakEggRating.__index = BreakEggRating

function BreakEggRating.Create(data)
  local tb = setmetatable({}, BreakEggRating)
  tb:_Init(data)
  return tb
end

function BreakEggRating:_Init(data)
  self.step = data.step
  self.failRate = data.fail_rate
  self.checkPoint = data.check_point
  self.checkFailRate = data.fail_check_rate
  self.m_rewardInfo = {}
  for _, value in ipairs(data.reward) do
    local tmpRwd = {}
    local arr = StringUtil.Split(value, ",")
    tmpRwd.reward = ConfigUtil.GetCurrencyFromStr(arr[1])
    tmpRwd.weight = tonumber(arr[2])
    Log.Assert(tmpRwd.reward[PROPERTY_TYPE] ~= nil and tmpRwd.reward[PROPERTY_COUNT] ~= nil and tmpRwd.weight ~= nil, "break egg rating config is error, step:" .. self.step)
    self.m_rewardInfo[#self.m_rewardInfo + 1] = tmpRwd
  end
  self.m_activityModel = GM.ActivityManager:GetModel(ActivityType.BreakEgg)
end

function BreakEggRating:GetBreakEggReward()
  local state = self.m_activityModel:GetBreakEggState()
  if state ~= BreakEggState.WaitBreakEgg3 and state ~= BreakEggState.WaitBreakEgg4 then
    Log.Assert(false, "break egg state error")
    return
  end
  local step = self.m_activityModel:GetCurrentStep()
  if step == 1 or state == BreakEggState.WaitBreakEgg3 then
    return self:RandomReward()
  end
  local reviveCount = self.m_activityModel:GetReviveCount()
  local failProb = reviveCount >= self.checkPoint and self.failRate or self.checkFailRate
  local rand = math.random(1, 100)
  if failProb < rand then
    return self:RandomReward()
  end
end

function BreakEggRating:GetStep()
  return self.step
end

function BreakEggRating:RandomReward()
  local rewadInfo = Table.ListWeightSelectOne(self.m_rewardInfo, "weight")
  return rewadInfo.reward
end

function BreakEggRating:GetShowReward(count, excludeReward, hasGem)
  local gemReward
  local arrRewards = {}
  local resultRwd = {}
  for _, value in ipairs(self.m_rewardInfo) do
    if value.reward[PROPERTY_TYPE] == EPropertyType.Gem then
      gemReward = value.reward
    else
      local bCan = true
      if not Table.IsEmpty(excludeReward) then
        for _, reward in ipairs(excludeReward) do
          if reward[PROPERTY_TYPE] == value.reward[PROPERTY_TYPE] and reward[PROPERTY_COUNT] == value.reward[PROPERTY_COUNT] then
            bCan = false
            break
          end
        end
      end
      if bCan then
        arrRewards[#arrRewards + 1] = value
      end
    end
  end
  if hasGem then
    resultRwd[#resultRwd + 1] = gemReward
    count = count - 1
  end
  if 1 <= count then
    for k = 1, count do
      local rewardInfo = Table.ListWeightSelectOne(arrRewards, "weight")
      if rewardInfo == nil then
        break
      end
      resultRwd[#resultRwd + 1] = rewardInfo.reward
      for i = 1, #arrRewards do
        if arrRewards[i].reward[PROPERTY_TYPE] == rewardInfo.reward[PROPERTY_TYPE] and arrRewards[i].reward[PROPERTY_COUNT] == rewardInfo.reward[PROPERTY_COUNT] then
          table.remove(arrRewards, i)
          break
        end
      end
    end
  end
  return resultRwd
end

function BreakEggRating:GetShowRewardProb()
  local curStep = self.m_activityModel:GetCurrentStep()
  local arrProb = {}
  local arrReward = {}
  local totalWeight = 0
  local bombProb = curStep == 1 and "0%" or "25%"
  local totalProb = curStep == 1 and 100 or 75
  local bomb = {
    icon = ImageFileConfigName.icon_bomb,
    num = 1,
    prob = bombProb
  }
  for _, value in ipairs(self.m_rewardInfo) do
    arrReward[#arrReward + 1] = value
    totalWeight = totalWeight + value.weight
  end
  totalWeight = math.max(totalWeight, 1)
  arrProb[#arrProb + 1] = bomb
  local bPropertyType
  for i = 1, #arrReward do
    local tmpReward = {}
    bPropertyType = GM.PropertyDataManager:IsPropertyType(arrReward[i].reward[PROPERTY_TYPE])
    tmpReward.icon = bPropertyType and EPropertySpriteBig[arrReward[i].reward[PROPERTY_TYPE]] or GM.ItemDataModel:GetSpriteName(arrReward[i].reward[PROPERTY_TYPE])
    tmpReward.num = arrReward[i].reward[PROPERTY_COUNT]
    local prob = math.floor(arrReward[i].weight * totalProb / totalWeight + 0.5)
    tmpReward.prob = prob .. "%"
    if arrReward[i].reward[PROPERTY_TYPE] == EPropertyType.Gem then
      table.insert(arrProb, 1, tmpReward)
    else
      table.insert(arrProb, tmpReward)
    end
  end
  return arrProb
end
