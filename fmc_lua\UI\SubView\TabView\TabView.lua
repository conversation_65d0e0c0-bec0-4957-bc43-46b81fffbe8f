TabView = {}
TabView.__index = TabView

function TabView:Init(tabButtonClickCallback, initTabKey, tipCallback)
  self.m_callback = tabButtonClickCallback
  self.m_tipCallback = tipCallback
  self.m_arrTabButtons = {}
  self.m_tabSize = tonumber(self.m_strTabSize)
  local tabButton
  for index = 1, self.m_tabSize do
    tabButton = self["m_tab" .. index]:GetLuaTable()
    tabButton:Init(self)
    self.m_arrTabButtons[#self.m_arrTabButtons + 1] = tabButton
  end
  self:OnTabButtonClicked(initTabKey)
  if self.m_tipCallback then
    self:UpdateExclaimation()
  end
end

function TabView:OnTabButtonClicked(tabKey)
  self.m_lastClickedTabkey = tabKey
  if not self.m_callback(tabKey) then
    return
  end
  self:UpdateHighlightState()
end

function TabView:UpdateHighlightState()
  for _, button in ipairs(self.m_arrTabButtons) do
    button:SetHighlightActive(button:GetTabKey() == self.m_lastClickedTabkey)
  end
end

function TabView:GetTabSize()
  return self.m_tabSize
end

function TabView:GetTabButton(index)
  return self["m_tab" .. index]:GetLuaTable()
end

function TabView:UpdateExclaimation(tabKey)
  if not self.m_tipCallback then
    Log.Error("没有注册红点刷新函数")
    return
  end
  for index = 1, self.m_tabSize do
    local tabBtn = self:GetTabButton(index)
    if not tabKey or tabBtn:GetTabKey() == tabKey then
      local tipVisible = self.m_tipCallback(tabBtn:GetTabKey())
      tabBtn:SetTipVisible(tipVisible)
    end
  end
end
