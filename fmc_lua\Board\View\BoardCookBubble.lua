BoardCookBubble = {}
BoardCookBubble.__index = BoardCookBubble

function BoardCookBubble:Init()
  self.gameObject:SetActive(true)
  self.bShowing = true
  self:Hide()
end

function BoardCookBubble:OnDestroy()
  self.itemModel = nil
  self:_TryKillTween()
end

function BoardCookBubble:_TryKillTween()
  self.m_animRoot:SetLocalScale(1)
  if self.m_tween then
    self.m_tween:Kill()
    self.m_tween = nil
  end
end

function BoardCookBubble:Show(itemModel, animated, forTutorial)
  self.m_bToShow = true
  self.itemModel = itemModel
  self.m_bAnimated = animated
  self.m_bForTutorial = forTutorial
  if animated == false then
    self:LateUpdate()
  end
end

function BoardCookBubble:Hide(forTutorial)
  if self.m_bForTutorial and not forTutorial then
    return
  end
  self.m_bToShow = false
  self.itemModel = nil
end

function BoardCookBubble:LateUpdate()
  if not GM.UIManager.allWindowClosed then
    return
  end
  if self.m_bToShow == nil then
    return
  end
  local toShow = self.m_bToShow
  self.m_bToShow = nil
  if toShow and not self.bShowing then
    self.bShowing = true
    if not self.itemModel then
      return
    end
    local itemCook = self.itemModel:GetComponent(ItemCook)
    if not itemCook then
      return
    end
    local recipe = itemCook:GetRecipe()
    if not recipe then
      return
    end
    local sceneBoardView = BoardViewHelper.GetActiveView()
    if not sceneBoardView then
      return
    end
    local itemView = sceneBoardView:GetItemView(self.itemModel)
    if not itemView then
      return
    end
    local boardPosition = self.itemModel:GetPosition()
    local v3Position = itemView:GetLocalPosition(boardPosition)
    local worldPosition = sceneBoardView.transform:TransformPoint(v3Position)
    local canvas = sceneBoardView:GetCanvas()
    local localPosition = canvas.transform:InverseTransformPoint(worldPosition)
    self.transform:SetLocalPosXY(localPosition.x, localPosition.y + 50)
    SpriteUtil.SetImage(self.m_itemImg, GM.ItemDataModel:GetSpriteName(recipe), false)
    if self.m_bAnimated ~= false then
      local s = DOTween.Sequence()
      s:Append(self.transform:DOScale(1, 0.3):SetEase(Ease.OutBack))
      s:AppendCallback(function()
        self.m_animator.enabled = true
        self.m_animator:SetTrigger("Play")
      end)
      s:AppendInterval(0.5)
      s:AppendCallback(function()
        self.m_gestureGo:SetActive(true)
      end)
      self.m_tween = s:OnComplete(function()
        self.m_tween = nil
      end)
    else
      self.transform:SetLocalScale(1)
      self.m_animator.enabled = true
      self.m_animator:SetTrigger("Play")
      self.m_gestureGo:SetActive(true)
    end
  elseif not toShow and self.bShowing then
    self.bShowing = false
    self:_TryKillTween()
    self.transform:SetLocalScale(0)
    self.m_animator.enabled = false
    self.m_gestureGo:SetActive(false)
    if self.m_bForTutorial then
      self.m_bForTutorial = false
    end
  end
end

function BoardCookBubble:OnClickCook()
  if not self.itemModel then
    return
  end
  local itemCook = self.itemModel:GetComponent(ItemCook)
  if not itemCook then
    return
  end
  itemCook:StartCook()
end
