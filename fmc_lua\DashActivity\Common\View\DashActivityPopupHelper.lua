DashActivityEndedPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
DashActivityEndedPopupHelper.__index = DashActivityEndedPopupHelper

function DashActivityEndedPopupHelper.Create()
  local helper = setmetatable({}, DashActivityEndedPopupHelper)
  helper:Init()
  return helper
end

function DashActivityEndedPopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(DashActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
end

function DashActivityEndedPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function DashActivityEndedPopupHelper:CheckPopup()
  for activityType, activityDefinition in pairs(DashActivityDefinition) do
    local args = {activityType}
    local model = GM.ActivityManager:GetModel(activityType)
    if model:GetState() == ActivityState.Ended and not model:HasWindowOpenedOnce(ActivityState.Ended) and model:HasWindowOpenedOnce(ActivityState.Started) then
      local level = model:GetLevel()
      local levelConfig = model:GetLevelConfigs()[level]
      if levelConfig ~= nil then
        if activityType == ActivityType.Coconut then
          return UIPrefabConfigName.CoconutMainWindow, {true}
        else
          local score = model:GetScore()
          if score >= levelConfig.score then
            return activityDefinition.RewardRecoverWindowPrefabName, args
          else
            return activityDefinition.FailWindowPrefabName, args
          end
        end
      end
    end
  end
end

DashActivityOtherPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
DashActivityOtherPopupHelper.__index = DashActivityOtherPopupHelper

function DashActivityOtherPopupHelper.Create()
  local helper = setmetatable({}, DashActivityOtherPopupHelper)
  helper:Init()
  return helper
end

function DashActivityOtherPopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(DashActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
    EventDispatcher.AddListener(activityDefinition.UpgradedEvent, self, self._OnStateChanged)
  end
end

function DashActivityOtherPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function DashActivityOtherPopupHelper:CheckPopup()
  for activityType, activityDefinition in pairs(DashActivityDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Preparing then
      if not model:HasWindowOpenedOnce(ActivityState.Preparing) then
        return activityDefinition.NoticeWindowPrefabName, {activityType, true}
      end
    elseif state == ActivityState.Started and not model:HasWindowOpenedOnce(ActivityState.Started) then
      return activityDefinition.ReadyWindowPrefabName, {activityType}
    end
  end
end
