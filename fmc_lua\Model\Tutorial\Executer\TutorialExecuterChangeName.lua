local Step = {Dialogue = "1"}
local EStep2TextKey = {
  [Step.Dialogue] = "tutorial_change_name"
}
local EStep2TextAnchorPercent = {
  [Step.Dialogue] = 40
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  if not GM.UserProfileModel:IsNowDefaultName() then
    self:Finish()
    return
  end
  EventDispatcher.AddListener(EEventType.OpenView, self, self.OnViewOpened)
end

function Executer:OnViewOpened(msg)
  if not self.m_bExecuteStep1 and GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BakeOutMainWindow) ~= nil and msg.name == UIPrefabConfigName.SettingWindow then
    self:_ExecuteStep1()
  end
end

function Executer:_ExecuteStep1()
  self.m_strOngoingDatas = Step.Dialogue
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  local settingWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.SettingWindow)
  if not settingWindow then
    self:Finish()
    return
  end
  local transf = settingWindow:GetProfileTrans()
  TutorialHelper.UpdateMask(transf.position, transf.sizeDelta, function()
    self:Finish()
  end, false)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
