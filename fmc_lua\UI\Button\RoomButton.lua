RoomButton = setmetatable({}, HudGeneralButton)
RoomButton.__index = RoomButton

function RoomButton:Awake()
  HudGeneralButton.Awake(self)
  EventDispatcher.AddListener(EEventType.ChapterChangeFinished, self, self.OnChapterChangeFinished)
end

function RoomButton:OnChapterChangeFinished()
  local curActiveChapterName = GM.ChapterManager.curActiveChapterName
  local isOngoingChapter = curActiveChapterName and curActiveChapterName == GM.TaskManager:GetOngoingChapterName()
  local anchoredPosY = isOngoingChapter and 317 or 160
  self.transform:SetAnchoredPosY(anchoredPosY)
end

function RoomButton:OnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.ChapterSelectWindow)
end
