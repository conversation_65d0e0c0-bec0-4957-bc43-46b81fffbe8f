PinataModel = setmetatable({}, LollipopModel)
PinataModel.__index = PinataModel
PinataModel.TokenType = "pinata"
PinataModel.ActivityType = ActivityType.Pinata
PinataModel.TransformChain = ItemChain.Pinata
PinataModel.GetScoreType = EBIType.PinataGetScore
PinataModel.ExtraRewardLevel = 0

function PinataModel:_LoadOtherServerConfig(config)
  LollipopModel._LoadOtherServerConfig(self, config)
  self.m_config.tokenConfigs = {}
  for _, tConfig in ipairs(config.pinata_token_exchange) do
    local tokenConfigItem = {}
    tokenConfigItem.minCoin = tConfig.coin_min
    tokenConfigItem.maxCoin = tConfig.coin_max
    tokenConfigItem.token = tConfig.token
    table.insert(self.m_config.tokenConfigs, tokenConfigItem)
  end
  table.sort(self.m_config.tokenConfigs, function(a, b)
    return a.minCoin < b.minCoin
  end)
end

function PinataModel:GetExtraReward()
  if self.m_config == nil or self.m_config.levelConfigs == nil or self.m_config.levelConfigs[PinataModel.ExtraRewardLevel] == nil then
    return nil
  end
  return self.m_config.levelConfigs[PinataModel.ExtraRewardLevel].rewards
end

function PinataModel:GetMaxRewardLevel()
  return #self.m_config.levelConfigs
end

function PinataModel:GetTokenNumber(orderPrice)
  for _, tokenConfig in ipairs(self.m_config.tokenConfigs) do
    if orderPrice >= tokenConfig.minCoin and orderPrice <= tokenConfig.maxCoin then
      return tokenConfig.token
    end
  end
end

function PinataModel:Upgrade()
  DashActivityModel.Upgrade(self)
  local level = self:GetLevel()
  local levelConfig = self:GetLevelConfigs()[level]
  if levelConfig == nil then
    local extraRewards = self:GetExtraReward()
    if extraRewards ~= nil then
      RewardApi.AcquireRewardsLogic(extraRewards, EPropertySource.Give, self.m_activityDefinition.GetExtraRewardsBIType, EGameMode.Board)
      self:LogActivity(EBIType.ActivityGetRewards, PinataModel.ExtraRewardLevel)
    end
    self.m_needTransformItems = true
    self:_TryTransformItems()
  end
end
