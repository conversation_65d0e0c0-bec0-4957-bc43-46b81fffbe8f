local Step = {Highlight = "1"}
local EStep2TextKey = {
  [Step.Highlight] = "tutorial_toboard_1"
}
local EStep2TextAnchorPercent = {
  [Step.Highlight] = 65
}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnGameModeChanged)
end

function Executer:_OnGameModeChanged(mode)
  if mode == EGameMode.Board and self.m_arrow then
    TutorialHelper.DehighlightHudButton(ESceneViewHudButtonKey.MainBoard)
    self:Finish(nil, self.m_arrow)
  end
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    return
  end
  self:_ExecuteStep1()
  return true
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.Highlight
  self:_SaveOngoingDatas()
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  TutorialHelper.HighlightHudButton(ESceneViewHudButtonKey.MainBoard)
  self.m_arrow = TutorialHelper.AddArrow2HudButton(ESceneViewHudButtonKey.MainBoard, 0)
  self.m_arrow:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
