DigActivityStartWindow = setmetatable({}, DigActivityBaseWindow)
DigActivityStartWindow.__index = DigActivityStartWindow

function DigActivityStartWindow:Init(...)
  DigActivityBaseWindow.Init(self, ...)
  if self.m_spineAnim ~= nil then
    self.m_spineAnim:Initialize()
    self.m_spineAnim.AnimationState:SetAnimation(0, "appear", false)
    self.m_spineAnim.AnimationState:AddAnimation(0, "idle", true, 0)
  end
  self:UpdatePerSecond()
end

function DigActivityStartWindow:OnBtnClicked()
  self:Close()
  GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, true)
end

function DigActivityStartWindow:UpdatePerSecond()
  if self.m_model ~= nil and self.m_model:GetState() ~= ActivityState.Started then
    UIUtil.SetActive(self.m_countdownGo, false)
    return
  end
  if self.m_model ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end
