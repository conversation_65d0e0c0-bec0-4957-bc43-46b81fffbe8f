ItemCobweb = setmetatable({}, BaseItemComponent)
ItemCobweb.__index = ItemCobweb

function ItemCobweb.Create(innerItemCode)
  local itemCobweb = setmetatable({}, ItemCobweb)
  itemCobweb:Init(innerItemCode)
  return itemCobweb
end

function ItemCobweb:Init(innerItemCode)
  self.m_innerItemCode = innerItemCode
  self.m_unlockPrice = 99
  local config = GM.ItemDataModel:GetModelConfig(self.m_innerItemCode)
  if config and config.UnlockPrice then
    self.m_unlockPrice = config.UnlockPrice
  end
end

function ItemCobweb:GetInnerItemCode()
  return self.m_innerItemCode
end

function ItemCobweb:IsInnerItemMaxLevel()
  local chainId = GM.ItemDataModel:GetChainId(self.m_innerItemCode)
  local level = GM.ItemDataModel:GetChainLevel(self.m_innerItemCode)
  return level == GM.ItemDataModel:GetChainMaxLevel(chainId)
end

function ItemCobweb:GetBreakCost()
  return self.m_unlockPrice
end

function ItemCobweb:OnBreak()
  local cost = self:GetBreakCost()
  self:_Break(cost)
end

function ItemCobweb:_Break(cost)
  local boardModel = self.m_itemModel:GetBoardModel()
  local gemNumber = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
  if cost > gemNumber then
    boardModel.event:Call(BoardEventType.LackGem, {
      LackNumber = cost - gemNumber
    })
    return
  end
  GM.PropertyDataManager:Consume(EPropertyType.Gem, cost, EBIType.BreakCobweb, false, self.m_innerItemCode)
  local newItem = boardModel:ReplaceItem(self.m_itemModel, self.m_innerItemCode)
  boardModel:ShockNeighborItems(self.m_itemModel:GetPosition())
  local eventInfo = {
    Source = self.m_itemModel,
    New = newItem
  }
  boardModel.event:Call(BoardEventType.CollapseItem, eventInfo)
  GM.BIManager:LogStore(EPropertyType.Gem, cost, self.m_innerItemCode, 1, EShopType.BreakCobweb)
  EventDispatcher.DispatchEvent(EEventType.BuyCobweb, {
    source = self.m_itemModel,
    cost = cost,
    newItemCode = self.m_innerItemCode
  })
end
