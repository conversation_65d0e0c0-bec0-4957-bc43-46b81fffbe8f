SkipTimeAutoEffectPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true
  },
  canIgnorePopup = false
}, BasePopupHelper)
SkipTimeAutoEffectPopupHelper.__index = SkipTimeAutoEffectPopupHelper

function SkipTimeAutoEffectPopupHelper.Create()
  local helper = setmetatable({}, SkipTimeAutoEffectPopupHelper)
  helper:Init()
  return helper
end

function SkipTimeAutoEffectPopupHelper:SetNeedCheckPopup(needCheck)
end

function SkipTimeAutoEffectPopupHelper:CheckPopup()
  local boardModel = GM.MainBoardModel
  local itemCode = boardModel:GetCachedItem(1)
  if not itemCode then
    return
  end
  local isImmediate, config = RewardApi.IsImmediateEffectItem(itemCode)
  if not isImmediate then
    return
  end
  boardModel:RemoveCachedItem(1)
  ItemSpeeder.TakeEffect(boardModel, config.Effect, itemCode)
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
  local boardView = MainBoardView.GetInstance()
  local boardCacheRoot = boardView:GetOrderArea():GetBoardCacheRoot()
  local worldPosition = boardCacheRoot.transform.position
  local screenPosition = boardView:ConvertWorldPositionToScreenPosition(worldPosition)
  local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
  RewardApi.AcquireRewardsInView({
    {
      [PROPERTY_TYPE] = itemCode,
      [PROPERTY_COUNT] = 1
    }
  }, {
    arrWorldPos = {uiWorldPosition},
    eventLock = true,
    spriteScale = boardCacheRoot:GetCacheElementScale()
  })
  return true
end
