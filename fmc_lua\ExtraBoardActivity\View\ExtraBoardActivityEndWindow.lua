ExtraBoardActivityEndWindow = setmetatable({bIgnoreCloseByState = true}, ExtraBoardActivityBaseWindow)
ExtraBoardActivityEndWindow.__index = ExtraBoardActivityEndWindow

function ExtraBoardActivityEndWindow:BeforeOpenCheck()
  local model
  for activityType, _ in pairs(ExtraBoardActivityDefinition) do
    model = GM.ActivityManager:GetModel(activityType)
    if model and model:GetState() == ActivityState.Ended then
      return true
    end
  end
  return false
end

function ExtraBoardActivityEndWindow:Init(activityType, bUserClick)
  ExtraBoardActivityBaseWindow.Init(self, activityType, bUserClick)
  local bMaxLevel = self.m_model:IsMaxLevel()
  local prefix = string.lower(activityType)
  local victoryDesc = prefix .. "_victory_desc"
  self.m_titleText.text = GM.GameTextModel:GetText(bMaxLevel and "extraboard_victory_title" or "extraboard_fail_title")
  self.m_descText.text = GM.GameTextModel:GetText(bMaxLevel and victoryDesc or "extraboard_fail_desc")
  UIUtil.SetActive(self.m_victoryGo, bMaxLevel)
  if self.m_failGo ~= nil then
    UIUtil.SetActive(self.m_failGo, not bMaxLevel)
  end
  self:UpdateSortingOrder()
end

function ExtraBoardActivityEndWindow:UpdateSortingOrder()
  local sortingOrderLength = 3
  local baseSortingOrder = self:GetSortingOrder()
  self.m_contentCanvas.sortingOrder = baseSortingOrder + sortingOrderLength
  local arrRenderer = self.m_victoryGo:GetComponentsInChildren(typeof(Renderer), true)
  for i = 0, arrRenderer.Length - 1 do
    arrRenderer[i].sortingOrder = (arrRenderer[i].sortingOrder or 0) + baseSortingOrder
  end
end
