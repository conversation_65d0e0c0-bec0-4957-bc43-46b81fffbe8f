BundleBaseModel = {}
BundleBaseModel.__index = BundleBaseModel

function BundleBaseModel:Init(bundleType, virtualDBTable)
  self.event = PairEvent.Create(self)
  self.m_bundleType = bundleType
  self.m_dbTable = virtualDBTable
  self:LoadServerConfig()
end

function BundleBaseModel:_DropData()
  self.m_dbTable:Drop()
end

function BundleBaseModel:LoadServerConfig()
  local arrConfigs = GM.BundleManager:GetServerConfigs(self.m_bundleType)
  self:_LoadServerConfig(arrConfigs)
end

function BundleBaseModel:_LoadServerConfig(arrConfigs)
end

function BundleBaseModel:LateInit()
end

function BundleBaseModel:UpdatePerSecond()
end

function BundleBaseModel:UpdateAfterLoginFinished()
  self:UpdatePerSecond()
end

function BundleBaseModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function BundleBaseModel:OnSceneViewLoaded()
end

function BundleBaseModel:GetActiveGroupDatas()
end

function BundleBaseModel:GetAllGroupDatas()
end

function BundleBaseModel:GetBundleConfigData(eIAPType)
end

function BundleBaseModel:OnPurchaseFinished(groupId, bundleId)
end

function BundleBaseModel:GetType()
  return self.m_bundleType
end

function BundleBaseModel:GetTargetPopOrderConfig(triggerType)
end
