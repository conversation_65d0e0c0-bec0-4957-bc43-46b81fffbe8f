SpreeActivityBaseWindow = setmetatable({}, BaseWindow)
SpreeActivityBaseWindow.__index = SpreeActivityBaseWindow

function SpreeActivityBaseWindow:Init(activityType, autoOpen, stayCurMode)
  self.m_activityType = activityType
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_model:SetWindowOpened()
  if not stayCurMode and GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    GM.SceneManager:ChangeGameMode(EGameMode.Main)
  end
  AddHandlerAndRecordMap(self.m_model.event, SpreeActivityEventType.StateChanged, {
    obj = self,
    method = self.Close
  })
  if autoOpen then
    self:LogWindowAction(EBIType.UIActionType.Open, {
      EBIReferType.AutoPopup
    })
  else
    self:LogWindowAction(EBIType.UIActionType.Open, {
      EBIReferType.UserClick
    })
  end
end

function SpreeActivityBaseWindow:OnD<PERSON>roy()
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
end

function SpreeActivityBaseWindow:OnButtonClicked()
  self:Close()
end

SpreeActivityReadyWindow = setmetatable({}, SpreeActivityBaseWindow)
SpreeActivityReadyWindow.__index = SpreeActivityReadyWindow
SpreeActivityResultWindow = setmetatable({}, SpreeActivityBaseWindow)
SpreeActivityResultWindow.__index = SpreeActivityResultWindow

function SpreeActivityResultWindow:Init(activityType)
  SpreeActivityBaseWindow.Init(self, activityType, true)
  self.m_levelText.text = self.m_model:GetLevel()
end

SpreeActivityLevelWindow = setmetatable({}, SpreeActivityBaseWindow)
SpreeActivityLevelWindow.__index = SpreeActivityLevelWindow

function SpreeActivityLevelWindow:Init(activityType)
  SpreeActivityBaseWindow.Init(self, activityType, false, true)
  self.m_levelRewardArea:Init(self.m_model)
  self:_UpdateContent()
end

function SpreeActivityLevelWindow:_UpdateContent()
  local level = self.m_model:GetLevel()
  self.m_levelText.text = level
  local curExp = self.m_model:GetLevelExp()
  local config = self.m_model:GetLevelConfig()
  local targetExp = self.m_model:GetLevelUpCost()
  if level == #config then
    self.m_expText.text = GM.GameTextModel:GetText("branch_event_max_level_2")
    self.m_expSlider.value = 0
  else
    self.m_expText.text = curExp .. "/" .. targetExp
    self.m_expSlider.value = curExp / targetExp
  end
  self.m_levelRewardArea:UpdateContent()
end

function SpreeActivityLevelWindow:OnButtonClicked()
  if GM.SceneManager:GetGameMode() ~= SpreeActivityDefinition[self.m_activityType].GameMode then
    GM.SceneManager:ChangeGameMode(SpreeActivityDefinition[self.m_activityType].GameMode)
  end
  self:Close()
end

function SpreeActivityLevelWindow:GetButtonTransform()
  return self.m_buttonRectTrans
end

SpreeActivityLevelUpWindow = setmetatable({}, SpreeActivityBaseWindow)
SpreeActivityLevelUpWindow.__index = SpreeActivityLevelUpWindow

function SpreeActivityLevelUpWindow:Init(activityType)
  SpreeActivityBaseWindow.Init(self, activityType, true, true)
  self.m_level = self.m_model:GetLevel()
  self.m_levelText.text = self.m_level
  self.m_rewards = self.m_model:GetLevelRewards(self.m_level)
  self.m_rewardContent:Init(self.m_rewards)
end

function SpreeActivityLevelUpWindow:Close()
  SpreeActivityBaseWindow.Close(self)
  if self.m_rewards ~= nil then
    self:_PlayRewardAnimation()
    self.m_rewards = nil
  end
end

function SpreeActivityLevelUpWindow:_PlayRewardAnimation()
  local arrWorldPos = {}
  local arrTargetGameMode = Table.ListRep(self.m_model:GetLevelRewardsTargetGameMode(self.m_level), #self.m_rewards, false)
  for i, reward in ipairs(self.m_rewards) do
    arrWorldPos[i] = self.m_rewardContent:GetRewardItem(i):GetIconRectTrans().position
  end
  RewardApi.AcquireRewardsInView(self.m_rewards, {
    arrWorldPos = arrWorldPos,
    eventLock = true,
    noDelayTime = false,
    targetGameMode = arrTargetGameMode
  })
end
