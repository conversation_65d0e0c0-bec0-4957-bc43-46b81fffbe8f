DigActivityBoardEntry = {}
DigActivityBoardEntry.__index = DigActivityBoardEntry

function DigActivityBoardEntry:Awake()
  self:_AddListeners()
  self:_OnDigItemStateChanged()
end

function DigActivityBoardEntry:Init(model, orderArea)
  self.m_activityType = model:GetType()
  self.m_model = model
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  self.m_orderArea = orderArea
  self.m_iconArea:Init(self.m_activityType)
  self.m_iconArea:SetInBoardView()
  self:UpdatePerSecond()
  self:_OnDigItemStateChanged()
  if self.gameObject.activeInHierarchy then
    self:_AddListeners()
  end
end

function DigActivityBoardEntry:_AddListeners()
  if not self.m_activityDefinition then
    return
  end
  EventDispatcher.AddListener(self.m_activityDefinition.DigItemStateChangedEvent, self, self._OnDigItemStateChanged, true)
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self._OnDigItemStateChanged, true)
end

function DigActivityBoardEntry:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function DigActivityBoardEntry:UpdatePerSecond()
  local nextStateTime = self.m_model and self.m_model:GetNextStateTime()
  if nextStateTime ~= nil then
    local delta = math.max(0, nextStateTime - GM.GameModel:GetServerTime())
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function DigActivityBoardEntry:OnMaskClicked()
  if GM.UIManager:IsViewExisting(self.m_activityDefinition.MainWindowPrefabName) then
    return
  end
  GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, true)
end

function DigActivityBoardEntry:_OnDigItemStateChanged()
  if not self.m_model or self.m_model:GetState() == ActivityState.Released then
    return
  end
  local cur, total = self.m_model:GetItemAcquiredProgress()
  if total ~= 0 then
    self.m_progressText.text = cur .. "/" .. total
    self.m_progressSlider.value = cur / total
  end
end

function DigActivityBoardEntry:GetIconArea()
  return self.m_iconArea
end
