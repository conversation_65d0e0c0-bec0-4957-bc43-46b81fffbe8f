PkRaceNoticeWindow = setmetatable({canCloseByAndroidBack = false}, PkRaceBaseWindow)
PkRaceNoticeWindow.__index = PkRaceNoticeWindow

function PkRaceNoticeWindow:Init(activityType, bAutoOpen)
  PkRaceBaseWindow.Init(self, activityType, bAutoOpen)
  PlayerPrefs.SetInt(EPlayerPrefKey.PkRaceDailyOpenTime, GM.GameModel:GetServerTime())
  self.m_bClicked = false
  self.m_closeBtnGo:SetActive(false)
  local curRound = self.m_model:GetCurrentRound()
  local bFirstRound = curRound == 0
  local titleKey = self.m_activityDefinition.TitleTextKey
  if not bFirstRound then
    titleKey = self.m_activityDefinition.MainTitleTextKey
  end
  local round = self.m_model:GetNextRoundInRoman()
  self.m_titleText.text = GM.GameTextModel:GetText(titleKey, round)
  local descKey = bFirstRound and "pkrace_desc_start" or "pkrace_desc_moreRound"
  self.m_descText.text = GM.GameTextModel:GetText(descKey)
  self:UpdatePerSecond()
end

function PkRaceNoticeWindow:UpdatePerSecond()
  self:_UpdateCountdown()
end

function PkRaceNoticeWindow:_UpdateCountdown()
  if self.m_model == nil then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  else
    self:Close()
  end
end

function PkRaceNoticeWindow:OnBtnClicked()
  self:_OnEntry()
end

function PkRaceNoticeWindow:_OnEntry()
  if self.m_bClicked then
    return
  end
  self.m_bClicked = true
  local state = self.m_model:GetState()
  if state == ActivityState.Started then
    GM.UIManager:OpenView(UIPrefabConfigName.PkRaceMatchWindow, self.m_activityType)
  end
  self:Close()
end

function PkRaceNoticeWindow:GetButtonTransform()
  return self.m_buttonGo.transform
end
