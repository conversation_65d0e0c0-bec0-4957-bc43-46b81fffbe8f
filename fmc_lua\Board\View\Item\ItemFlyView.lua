ItemFlyView = {}
ItemFlyView.__index = ItemFlyView

function ItemFlyView:Init(itemType)
  local spriteName = GM.ItemDataModel:GetSpriteName(itemType)
  SpriteUtil.SetSpriteRenderer(self.m_itemSprite, spriteName)
end

function ItemFlyView:Fly2Target(targetPos, bEnergyBoost)
  local color = bEnergyBoost and "D760F4" or "1A5E9A"
  self.m_numOutl.effectColor = UIUtil.ConvertHexColor2CSColor(color)
  local pos = self.transform.localPosition
  local seq = DOTween.Sequence()
  seq:Insert(0.1, self.transform:DOLocalMoveY(pos.y + 230, 1):SetEase(Ease.OutCirc))
  seq:Insert(1.1, self.m_numText:DOFade(0, 0.3))
  seq:Insert(1.4, self.transform:<PERSON><PERSON>ove(targetPos, 0.5):SetEase(Ease.OutQuad))
  seq:Insert(1.7, self.transform:DOScale(0.8, 0.2))
  seq:InsertCallback(2, function()
    EventDispatcher.DispatchEvent(EEventType.ItemFlyTarget)
    if self and self.gameObject and not self.gameObject:IsNull() then
      self.gameObject:RemoveSelf()
    end
  end)
end
