ShopContainerBundleMultiTier = setmetatable({}, BaseShopContainer)
ShopContainerBundleMultiTier.__index = ShopContainerBundleMultiTier

function ShopContainerBundleMultiTier:Awake()
  self.m_shopType = EShopType.MultiTierBundle
  EventDispatcher.AddListener(EEventType.BundleDataRefreshed, self, self.OnBundleDataRefreshed)
end

function ShopContainerBundleMultiTier:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function ShopContainerBundleMultiTier:OnClose()
  UIUtil.SetActive(self.m_bestDealGo, false)
end

function ShopContainerBundleMultiTier:OnBundleDataRefreshed(msg)
  if self.m_model == nil or self.m_dataGroup == nil then
    return
  end
  if self.m_dataGroup:GetLastDuration() == 0 then
    return
  end
  local bundleState = self.m_model:GetBundleState(self.m_dataGroup)
  if bundleState ~= EBundleState.Opening then
    self.gameObject:SetActive(false)
  end
end

function ShopContainerBundleMultiTier:_GetCellData()
  return {}
end

function ShopContainerBundleMultiTier:_OnCellClicked()
end

function ShopContainerBundleMultiTier:GetContentHeight()
  return self.gameObject.activeSelf and self.transform.sizeDelta.y or 0
end

function ShopContainerBundleMultiTier:Init(bundleType, dataGroup)
  self.m_model = GM.BundleManager:GetModel(bundleType)
  self.m_dataGroup = dataGroup
  self.m_cellContainer:Init(self.m_model, self.m_model:GetCurBundleDatas(dataGroup))
end

function ShopContainerBundleMultiTier:UpdateSortingOrder(baseSortingOrder)
  self.m_behindSortingGroup.sortingOrder = baseSortingOrder - 1
  self.m_frontSortingGroup.sortingOrder = baseSortingOrder + 1
  UIUtil.UpdateSortingOrder(self.m_bestDealGo, baseSortingOrder + 1)
end

function ShopContainerBundleMultiTier:UpdatePerSecond()
  if self.m_model ~= nil and self.m_dataGroup ~= nil then
    local restDuration = self.m_model:GetRestDuration(self.m_dataGroup)
    if 0 < restDuration then
      self.m_countdownText.text = TimeUtil.ParseTimeDescription(restDuration, 2, false, false)
    else
      UIUtil.SetActive(self.m_countdownGo, false)
    end
  end
end
