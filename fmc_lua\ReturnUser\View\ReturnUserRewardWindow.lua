ReturnUserRewardWindow = setmetatable({}, BaseWindow)
ReturnUserRewardWindow.__index = ReturnUserRewardWindow

function ReturnUserRewardWindow:Init(rewards)
  if GM.SceneManager:GetGameMode() == EGameMode.Board then
    GM.SceneManager:ChangeGameMode(EGameMode.Main)
  end
  self:InitRewardContent(rewards)
  self:InitRoleAndBubble()
  self.m_rewards = rewards
  self.m_effectSort.sortingOrder = self:GetSortingOrder() + 1
  self.m_backEffectSort.sortingOrder = self:GetSortingOrder()
  local fScale = ScreenFitter.GetScreenAdjustSize().x / self.m_BackRectTrans.sizeDelta.x
  self.m_BackRectTrans.localScale = Vector3(fScale, fScale, 1)
  self:LogWindowAction(EBIType.UIActionType.Open, EBIReferType.AutoPopup, GM.ReturnUserModel:GetRewardLeaveDay())
end

function ReturnUserRewardWindow:InitRoleAndBubble()
  self.m_role.AnimationState:SetAnimation(0, "Olivia_energybox_loop3", true)
  self.m_bubbleText:UpdateText()
end

function ReturnUserRewardWindow:InitRewardContent(rewards)
  self.m_rewardContentLuaTable:InitRewardContent(rewards)
end

function ReturnUserRewardWindow:OnCloseBtnClick()
  local giftPositionArray = self.m_rewardContentLuaTable:GetRewardsPos()
  local giftViewData = {arrWorldPos = giftPositionArray, noDelayTime = true}
  RewardApi.AcquireRewardsInView(self.m_rewards, giftViewData)
  BaseWindow.OnCloseBtnClick(self)
end

ReturnUserRewardContent = {}
ReturnUserRewardContent.__index = ReturnUserRewardContent

function ReturnUserRewardContent:InitRewardContent(arrReward)
  if self.m_arrRewardItems == nil then
    self.m_arrRewardItems = {
      self.m_RewardCell:GetLuaTable()
    }
  end
  for _, item in pairs(self.m_arrRewardItems) do
    UIUtil.SetActive(item.gameObject, false)
  end
  local arrPos
  if #arrReward == 5 or #arrReward == 4 then
    self.m_rewardContentCont.enabled = false
    self.m_rewardContentLayoutGroup.enabled = false
    self.transform.sizeDelta = Vector2(630, 500)
    if #arrReward == 5 then
      arrPos = {
        Vector2(105, -125),
        Vector2(315, -125),
        Vector2(525, -125),
        Vector2(210, -375),
        Vector2(420, -375)
      }
    elseif #arrReward == 4 then
      arrPos = {
        Vector2(210, -125),
        Vector2(420, -125),
        Vector2(210, -375),
        Vector2(420, -375)
      }
    end
  end
  for k, reward in pairs(arrReward) do
    if self.m_arrRewardItems[k] == nil then
      local newRewardItem = Object.Instantiate(self.m_RewardCell, self.m_rewardTrans)
      table.insert(self.m_arrRewardItems, newRewardItem:GetLuaTable())
    end
    if arrPos ~= nil then
      self.m_arrRewardItems[k].transform.anchorMin = Vector2(0, 1)
      self.m_arrRewardItems[k].transform.anchorMax = Vector2(0, 1)
      self.m_arrRewardItems[k].transform.anchoredPosition = arrPos[k]
    end
    UIUtil.SetActive(self.m_arrRewardItems[k].gameObject, true)
    self.m_arrRewardItems[k]:Init(reward)
  end
end

function ReturnUserRewardContent:GetRewardsPos()
  local arrPos = {}
  for k, v in pairs(self.m_arrRewardItems) do
    if v.gameObject.activeSelf then
      table.insert(arrPos, v.transform.position)
    end
  end
  return arrPos
end
