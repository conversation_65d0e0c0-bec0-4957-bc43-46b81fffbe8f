ExtraBoardActivityLevelProgress = setmetatable({ProgressCellGap = 100}, LevelProgress)
ExtraBoardActivityLevelProgress.__index = ExtraBoardActivityLevelProgress

function ExtraBoardActivityLevelProgress:Init(maxLevel, model, window, bEnd)
  self.ProgressCellGap = tonumber(self.ProgressCellGap)
  self.m_model = model
  self.m_window = window
  self.m_bEnd = bEnd
  LevelProgress.Init(self, maxLevel)
  self.m_bInit = true
  self.m_maxReward = self.m_model:GetMaxLevelReward()
  UIUtil.SetActive(self.m_rewardTip.gameObject, false)
  self:UpdateMaxLevelRewardTip()
  EventDispatcher.AddListener(EEventType.ExtraBoardActivityItemUnlocked, self, self.UpdateMaxLevelRewardTip)
end

function ExtraBoardActivityLevelProgress:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function ExtraBoardActivityLevelProgress:GetProgressCell(level)
  local cellObject = Object.Instantiate(self.m_progressCellPrefab, self.m_sliderNode)
  local cell = cellObject:GetLuaTable()
  cell:Init(level, self.m_model, self.m_window, self.m_bEnd)
  return cell
end

function ExtraBoardActivityLevelProgress:GetCurrentLevel()
  return self.m_model:GetLevel()
end

function ExtraBoardActivityLevelProgress:GetProgressLevel()
  return self.m_model:GetLevel()
end

function ExtraBoardActivityLevelProgress:OnScrollValueChanged()
  if self.m_bInit then
    self:UpdateMaxLevelRewardTip()
  end
end

function ExtraBoardActivityLevelProgress:UpdateMaxLevelRewardTip()
  if Table.IsEmpty(self.m_maxReward) then
    return
  end
  local bMaxLevel = self.m_model:GetMaxLevel() == self.m_model:GetLevel()
  UIUtil.SetActive(self.m_completeGo, bMaxLevel)
  local viewportWidth = self.transform.sizeDelta.x
  local contentWidth = self.m_contentNode.sizeDelta.x
  local cellWidth = 30
  local contentX = self.m_contentNode.anchoredPosition.x
  local cell = self:GetCellByLevel(self.m_model:GetMaxLevel())
  if contentX < viewportWidth - contentWidth + cellWidth then
    if not self.m_bShowTip then
      self.m_rewardTip:Show(self.m_maxReward, cell.transform, 0, 25)
      self.m_bShowTip = true
    end
  elseif self.m_bShowTip then
    self.m_rewardTip:Hide()
    self.m_bShowTip = false
  end
  local rewardItem = self.m_rewardTip:GetRewardItem(1)
  if rewardItem ~= nil then
    local icon = rewardItem:GetIcon()
    if icon ~= nil then
      UIUtil.SetColor(icon, nil, nil, nil, bMaxLevel and 0.5 or 1)
    end
    local text = rewardItem:GetText()
    if text ~= nil then
      UIUtil.SetColor(text, nil, nil, nil, bMaxLevel and 0.5 or 1)
    end
  end
end

function ExtraBoardActivityLevelProgress:Update()
  if self.m_bShowTip then
    self.m_rewardTip:UpdatePos()
  end
end
