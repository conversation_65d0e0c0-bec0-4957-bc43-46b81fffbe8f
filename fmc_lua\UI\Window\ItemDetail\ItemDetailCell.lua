ItemDetailCell = {}
ItemDetailCell.__index = ItemDetailCell
EItemDetailCellBg = {
  Green = "bgGreen",
  Yellow = "bgYellow",
  DarkGreen = "bgDarkGreen"
}
local white = CSColor.white
local transparent = CSColor(1, 1, 1, 0.4)

function ItemDetailCell:Init(itemType, isProduceCell, hasArrow, forceShow, hasTipButton, parentWindow)
  self.m_arrowGo:SetActive(hasArrow)
  UIUtil.SetActive(self.m_hotSaleGo, false)
  if isProduceCell then
    self.m_bgImg.sprite = self.m_produceBgSprite
  end
  local unlocked = GM.ItemDataModel:IsUnlocked(itemType) or forceShow
  local locked = not unlocked and GM.ItemDataModel:IsKnown(itemType)
  self.itemType = itemType
  if itemType and (unlocked or locked) then
    local spriteName = GM.ItemDataModel:GetSpriteName(itemType)
    self.m_itemImg.enabled = false
    self.m_loadingGo:SetActive(true)
    SpriteUtil.SetImage(self.m_itemImg, spriteName, true, function()
      if self.m_loadingGo and not self.m_loadingGo:IsNull() then
        self.m_loadingGo:SetActive(false)
      end
    end, nil, true)
    self.m_itemImg.color = unlocked and white or transparent
    self.m_itemTipButton.gameObject:SetActive(hasTipButton)
    if hasTipButton then
      self.m_itemTipButton:UpdateItemType(itemType, false, parentWindow.nextRefer)
    end
    self.m_bHasTipButton = hasTipButton
  else
    self.m_itemImg.enabled = true
    self.m_itemTipButton.gameObject:SetActive(false)
    self.m_btn.enabled = false
    self.m_loadingGo:SetActive(false)
  end
  self.m_selectedGo:SetActive(false)
  local isInstru = GM.ItemDataModel:GetModelConfig(itemType).Recipes ~= nil
  self.m_cookHourglass:SetActive(isInstru and unlocked)
  self.m_parentWindow = parentWindow
  self:_TryCreateTestButton()
end

function ItemDetailCell:OnClicked()
  if self.m_bHasTipButton then
    ItemDetailWindow.Open(self.itemType, self.m_parentWindow.mode or ItemDetailWindowMode.Normal, self.m_parentWindow.nextRefer)
  else
    self.m_parentWindow:UpdateView(self.itemType)
  end
end

function ItemDetailCell:ShowAsSelected(bChangeBg)
  if bChangeBg then
    self:ChangeBg(EItemDetailCellBg.Green)
  end
  self.m_selectedGo:SetActive(true)
  self.m_btn.enabled = false
end

function ItemDetailCell:ChangeBg(bgImage)
  if self[bgImage] then
    self.m_bgImg.sprite = self[bgImage]
  end
end

function ItemDetailCell:UpdateHotSaleDisplay()
  local inHotSale, shopItemId, price = GM.ShopModel:IsItemInStock(EShopType.FlashSale, self.itemType)
  self.m_hotSaleGo:SetActive(inHotSale)
  self.shopItemId = shopItemId
  if inHotSale then
    self.m_hotSalePriceText.text = price
  end
end

function ItemDetailCell:OnBuyHotSaleClick()
  Log.Assert(self.shopItemId, "闪售快速购买逻辑错误")
  local success, costType, lackNum = GM.ShopModel:BuyItem(EShopType.FlashSale, self.shopItemId, false, self.transform.position)
  if success then
    if GM.UIManager:IsViewExisting(UIPrefabConfigName.ShopWindow) then
      EventDispatcher.DispatchEvent(EEventType.ShopRefreshed, {
        shopType = EShopType.FlashSale
      })
    else
      local boardView = BoardViewHelper.GetActiveView()
      if not boardView then
        return
      end
      local orderArea = boardView:GetOrderArea()
      orderArea:ScrollToFront(false)
      local formattedRewards = {
        {
          [PROPERTY_TYPE] = self.itemType,
          [PROPERTY_COUNT] = 1
        }
      }
      local viewData = {
        arrWorldPos = {
          self.transform.position
        },
        noDelayTime = true,
        spriteScale = self.transform.localScale.x
      }
      RewardApi.AcquireRewardsInView(formattedRewards, viewData)
    end
  end
  if not success then
    GM.ShopModel:OnLackOfGem(lackNum)
  end
end

function ItemDetailCell:_TryCreateTestButton()
  if not GM.UIManager:CanShowTestUI() or self.m_bExistTestBtn then
    return
  end
  local testButtonGo = GameObject.Instantiate(self.m_simpleButtonPrefab, self.transform)
  local localPos = Vector3(tonumber(self.m_testLocalPosX) or -50, tonumber(self.m_testLocalPosY) or 60, 0)
  testButtonGo.transform.localPosition = localPos
  testButtonGo.transform:SetLocalScaleXY(0.3)
  testButtonGo.transform:SetSizeDeltaWidth(250)
  local testButton = testButtonGo:GetLuaTable()
  testButton:Init("添加", function()
    GM.TestModel:AddItem(self.itemType)
  end)
  self.m_bExistTestBtn = true
end
