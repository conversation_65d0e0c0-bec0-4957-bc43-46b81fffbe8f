AccountManager = {}
AccountManager.__index = AccountManager
local DBColumnSocialType = "SocialType"
local DBColumnSocialId = "SocialId"
local DBColumnSocialName = "SocialName"
local DBColumnSocialPictureUrl = "SocialPictureUrl"
ACCOUNT_BIND_REWARD_COUNT = 20

function AccountManager:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.Account)
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self._CheckSocialBindings)
end

function AccountManager:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function AccountManager:OnSceneViewLoaded()
  if PlayerPrefs.GetInt(EPlayerPrefKey.AccountSelectSuccess, 0) == 1 then
    self:_OpenBindSuccessWindow()
    local curIcon = GM.UserProfileModel:GetIcon()
    local avatarData = GM.UserProfileModel:GetAvatarImagesAll()
    local curUseSocialIcon = true
    for _, icon in ipairs(avatarData) do
      if icon == curIcon then
        curUseSocialIcon = false
      end
    end
    if curUseSocialIcon then
      self.m_dbTable:Set(DBColumnSocialPictureUrl, "value", curIcon)
    end
    PlayerPrefs.SetInt(EPlayerPrefKey.AccountSelectSuccess, 0)
  end
  if self.m_delayShowDisconnectWindowType ~= nil then
    self:_OpenDisconnectWindow(self.m_delayShowDisconnectWindowType)
    self.m_delayShowDisconnectWindowType = nil
  end
end

function AccountManager:Login(socialType)
  self.eCurrentLoginSocialType = socialType
  GM:OnEnteringThirdPartyApp()
  if socialType == ESsoSocialType.Facebook then
    CSFacebookManager:Login()
  else
    CSLoginClient:Login()
  end
end

function AccountManager:Logout(withWindow)
  local accountType = self:GetAccountType()
  GM.SDKHelper:LogOut()
  self.m_dbTable:Remove(DBColumnSocialId)
  self.m_dbTable:Remove(DBColumnSocialType)
  self.m_dbTable:Remove(DBColumnSocialName)
  self.m_dbTable:Remove(DBColumnSocialPictureUrl)
  EventDispatcher.DispatchEvent(EEventType.AccountLogout)
  if withWindow then
    local descriptionKey
    if accountType == ESsoSocialType.Facebook then
      descriptionKey = "sign_out_success_desc"
    elseif accountType == ESsoSocialType.Google then
      descriptionKey = "sign_out_success_google_desc"
    else
      descriptionKey = "sign_out_success_apple_desc"
    end
    GM.UIManager:OpenView(UIPrefabConfigName.AccountStatusWindow, "sign_out_success_title", descriptionKey, "sign_out_success_btn", true, false, nil)
  end
end

function AccountManager:OnSDKLoginFinished(result, socialType, socialId, accessToken, name, email, pictureUrl, refreshToken)
  GM:OnBackFromThirdPartyApp()
  if not result then
    self:_OnBindFailed()
    return
  end
  self.m_socialUser = {
    Type = socialType,
    Id = socialId,
    AccessToken = accessToken,
    Name = name,
    Email = email,
    PictureUrl = pictureUrl,
    RefreshToken = refreshToken
  }
  local callback = function(result, request, response, responseToken)
    self:_OnUpdateBindingFinished(result, response, responseToken)
  end
  local request = self:_GenerateSsoRequest(ESsoActionType.Lookup)
  GM.SsoManager:SendRequest(request, callback)
end

function AccountManager:_OnUpdateBindingFinished(result, response, responseToken)
  if not result then
    self:_OnBindFailed()
    return
  end
  local status = response.registrationStatus
  local responseUserId = response.mainUserId
  if status == ESsoActionStatus.NewRegister then
    Log.Assert(false, "AccountManager:_OnUpdateBindingFinished")
  elseif status == ESsoActionStatus.OldUser then
    if responseUserId == GM.UserModel:GetUserId() then
      self:_OnBindSuccess(responseToken)
    else
      self:_RequestSocialUserData(responseUserId, responseToken)
    end
  elseif status == ESsoActionStatus.SocialConflict then
    local switchCallback = function(window)
      window:Close()
      self:_SwitchBinding(responseToken)
    end
    local cancelCallback = function(window)
      window:Close()
      self:_OnBindFailed()
    end
    GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "bind_switch_title", "bind_switch_desc", "bind_switch_yes_btn", "bind_switch_cancel_btn", switchCallback, cancelCallback, nil, function(window)
      window.canCloseByChangeGameMode = false
    end)
  else
    self:_OnBindFailed()
  end
end

function AccountManager:_SwitchBinding(newToken)
  local callback = function(result, request, response, responseToken)
    if result and response.registrationStatus == ESsoActionStatus.OldUser then
      self:_OnBindSuccess(responseToken)
    else
      self:_OnBindFailed()
    end
  end
  local request = self:_GenerateSsoRequest(ESsoActionType.Switch)
  GM.SsoManager:SendRequest(request, callback, newToken)
end

function AccountManager:_RequestSocialUserData(newUserId, newToken)
  local callback = function(result, response)
    if not result then
      self:_OnBindFailed()
      return
    end
    if response.rcode == 0 or response.rcode == 1 then
      local selectLocalCallback = function()
        GM.BIManager:LogAction(EBIType.SceneDataConflict, EBIType.SceneDataConflictAction.DifferentAccountUseLocal)
        self:_SwitchBinding(newToken)
      end
      local selectNewCallback = function()
        GM.BIManager:LogAction(EBIType.SceneDataConflict, EBIType.SceneDataConflictAction.DifferentAccountUseServer)
        self:_Select(newUserId, newToken)
      end
      local closeCallback = function()
        GM.BIManager:LogAction(EBIType.SceneDataConflict, EBIType.SceneDataConflictAction.DifferentAccountCancel)
        self:_OnBindFailed()
      end
      local displayData
      if response.rcode == 0 then
        local gems = GM.PropertyDataManager:GetPropertyNumFromSyncData(response.gem)
        local coins = GM.PropertyDataManager:GetPropertyNumFromSyncData(response.gold)
        local level = tonumber(response.level)
        local day = MainOrderDataModel.GetOrderDayByOrderGroup(response.orderChapterId, response.orderGroupId)
        local currentChapter = response.currentChapter
        local currentTasks = response.currentTask
        local timestamp = GM.GameModel:GetServerTime() - response.syncTime
        displayData = DataConflictWindow.CreateDisplayData(gems, coins, level, day, currentChapter, currentTasks, timestamp)
      else
        displayData = DataConflictWindow.CreateDisplayData(0, 0, 1, 1, 1, "1", 0)
      end
      GM.UIManager:OpenView(UIPrefabConfigName.DataConflictWindow, "progressConflict_desc", displayData, selectLocalCallback, selectNewCallback, closeCallback)
    elseif response.rcode == 2 then
      self:Logout()
      GM.BIManager:LogAction(EBIType.SceneBind, EBIType.SceneBindAction.BindFailedVersion)
      local title = GM.GameTextModel:GetText("sign_in_fail_title")
      local description = GM.GameTextModel:GetText("login_fail_ver_desc")
      local buttonText = GM.GameTextModel:GetText("common_button_ok")
      local callback = function()
        GM.UpdateHintModel:TryGoToStorePage()
      end
      GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, title, description, buttonText, callback, nil, true)
    else
      self:_OnBindFailed()
    end
  end
  ApiMessage.GetUserDataProfile(newUserId, callback)
end

function AccountManager:_Select(newUserId, newToken)
  local callback = function(result, request, response, responseToken)
    if result and response.registrationStatus == ESsoActionStatus.OldUser then
      GM.SyncModel:ClearAllData()
      GM.UserModel:SetUserId(response.mainUserId)
      GM.UserModel:Set(EUserLocalDataKey.RegisterVersion, response.reg_version)
      self:_OnBindSuccess(responseToken, false)
      PlayerPrefs.SetInt(EPlayerPrefKey.AccountSelectSuccess, 1)
      GM:RestartGame(nil, EBIProjectType.RestartGameAction.AccountSelect)
    else
      self:_OnBindFailed()
    end
  end
  local request = self:_GenerateSsoRequest(ESsoActionType.Select)
  request.currentUserId = newUserId
  GM.SsoManager:SendRequest(request, callback, newToken)
end

function AccountManager:_OnBindSuccess(newToken, openWindow)
  GM.SsoManager:SetToken(newToken)
  self.m_dbTable:Set(DBColumnSocialType, "value", tostring(self.m_socialUser.Type))
  self.m_dbTable:Set(DBColumnSocialId, "value", self.m_socialUser.Id)
  self.m_dbTable:Set(DBColumnSocialName, "value", self.m_socialUser.Name)
  self.m_dbTable:Set(DBColumnSocialPictureUrl, "value", self.m_socialUser.PictureUrl)
  GM.UserProfileModel:ChangeName(self.m_socialUser.Name)
  GM.UserProfileModel:ChangeIcon(self.m_socialUser.PictureUrl)
  GM.BIManager:LogAction(EBIType.SceneBind, EBIType.SceneBindAction.BindSuccess)
  EventDispatcher.DispatchEvent(EEventType.AccountBindSuccess)
  if openWindow ~= false then
    self:_OpenBindSuccessWindow()
  end
end

function AccountManager:_OnBindFailed()
  self:Logout()
  GM.BIManager:LogAction(EBIType.SceneBind, EBIType.SceneBindAction.BindFailed)
  local buttonCallback = function()
    self:Login(self.eCurrentLoginSocialType)
  end
  GM.UIManager:OpenView(UIPrefabConfigName.AccountStatusWindow, "sign_in_fail_title", "sign_in_fail_desc", "sign_in_fail_btn", false, false, buttonCallback)
end

function AccountManager:_CheckSocialBindings(message)
  if not message.bSuccess then
    return
  end
  local currentSocialType = self:GetAccountType()
  if currentSocialType == nil then
    return
  end
  local currentSocialId = self.m_dbTable:GetValue(DBColumnSocialId, "value")
  local bindingId = ""
  for _, binding in ipairs(message.tbResp.social_bindings) do
    if binding.type == currentSocialType then
      bindingId = binding.id or ""
      break
    end
  end
  if bindingId ~= currentSocialId then
    self:Logout()
    if GM.SceneManager:GetGameMode() == EGameMode.Loading then
      self.m_delayShowDisconnectWindowType = currentSocialType
    else
      self:_OpenDisconnectWindow(currentSocialType)
    end
  end
end

function AccountManager:_GenerateSsoRequest(actionType)
  Log.Assert(self.m_socialUser ~= nil, "AccountManager:_GenerateSsoRequest")
  return SsoMessage.GenerateRequest(actionType, self.m_socialUser.Type, self.m_socialUser.Id, self.m_socialUser.AccessToken, self.m_socialUser.Name, self.m_socialUser.Email, self.m_socialUser.PictureUrl, self.m_socialUser.RefreshToken)
end

function AccountManager:HasAccount()
  return not StringUtil.IsNilOrEmpty(self.m_dbTable:GetValue(DBColumnSocialId, "value"))
end

function AccountManager:GetAccountType()
  return tonumber(self.m_dbTable:GetValue(DBColumnSocialType, "value"))
end

function AccountManager:GetAccountName()
  return self.m_dbTable:GetValue(DBColumnSocialName, "value")
end

function AccountManager:GetAccountPictureUrl()
  return self.m_dbTable:GetValue(DBColumnSocialPictureUrl, "value")
end

function AccountManager:_OpenBindSuccessWindow()
  local descriptionKey
  if self:GetAccountType() == ESsoSocialType.Facebook then
    descriptionKey = "sign_in_success_desc"
  elseif self:GetAccountType() == ESsoSocialType.Google then
    descriptionKey = "sign_in_success_google_desc"
  else
    descriptionKey = "sign_in_success_apple_desc"
  end
  GM.UIManager:OpenView(UIPrefabConfigName.AccountStatusWindow, "sign_in_success_title", descriptionKey, "sign_in_success_btn", true, true, nil)
end

function AccountManager:_OpenDisconnectWindow(socialType)
  local descriptionKey
  if socialType == ESsoSocialType.Facebook then
    descriptionKey = "login_disconnect_desc"
  elseif socialType == ESsoSocialType.Google then
    descriptionKey = "login_disconnect_google_desc"
  else
    descriptionKey = "login_disconnect_apple_desc"
  end
  local title = GM.GameTextModel:GetText("login_disconnect_title")
  local description = GM.GameTextModel:GetText(descriptionKey)
  local buttonText = GM.GameTextModel:GetText("login_disconnect_btn")
  GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, title, description, buttonText, nil, nil, false)
end

function AccountManager:DeleteAccount()
  GM.UIManager:OpenView(UIPrefabConfigName.WarningTwoButtonWindow, "clearAll_window_title", "clearAll_window_desc", "clearAll_window_yes_btn", "common_button_cancel", function(tbWindow)
    tbWindow:Close()
    self:_ConfirmDeleteAccount()
  end, nil, true, function(tbWindow)
    tbWindow:SetCountDownTimer(10)
    tbWindow.canCloseByChangeGameMode = false
  end)
end

function AccountManager:_ConfirmDeleteAccount()
  local vCode = StringUtil.GetVerificationCode()
  local desc = GM.GameTextModel:GetText("clearAll_verify_desc", vCode)
  GM.UIManager:OpenView(UIPrefabConfigName.InputConfirmWindow, GM.GameTextModel:GetText("clearAll_verify_title"), desc, vCode, GM.GameTextModel:GetText("clearAll_verify_yes_btn"), GM.GameTextModel:GetText("common_button_cancel"), function(tbWindow)
    tbWindow:Close()
    self:_ConfirmDeleteAccountAgain()
  end)
end

function AccountManager:_ConfirmDeleteAccountAgain()
  GM.UIManager:OpenView(UIPrefabConfigName.WarningTwoButtonWindow, "clearAll_confirm_title", "clearAll_confirm_desc", "clearAll_confirm_yes_btn", "common_button_cancel", function(tbWindow)
    tbWindow:Close()
    local request = SsoMessage.GenerateDeleteUserRequest(ESsoDeleteUserActionType.DeleteDirectly, self.eCurrentLoginSocialType)
    GM.SsoManager:SendDeleteUserRequest(request, function(success, tbResp)
      self:_SSODeleteAccountCb(success, tbResp)
    end)
  end, nil, true, function(window)
    window.canCloseByChangeGameMode = false
  end)
end

function AccountManager:_SSODeleteAccountCb(success, tbResp)
  if success and tbResp and tbResp.rcode == ESsoDeleteUserStatus.DeleteSuccess then
    self:_MarkLogout2Server()
    GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "clearAll_success_title", "clearAll_success_desc", "clearAll_success_quit_btn", "clearAll_success_restart_btn", function()
      GM.SDKHelper:LogOut()
      GM.SyncModel:ClearAllData()
      GM.skipDBSerialize = true
      PlatformInterface.ExitGame()
    end, function()
      GM.SDKHelper:LogOut()
      GM.SyncModel:ClearAllData()
      GM:RestartGame(nil, EBIProjectType.RestartGameAction.DeleteAccountSuccess)
    end, false, function(window)
      window.canCloseByChangeGameMode = false
    end)
  elseif success and tbResp and tbResp.rcode == ESsoDeleteUserStatus.HasBeenDeleted then
    self:_MarkLogout2Server()
    local deleteUserid = GM.UserModel:GetDisplayUserId()
    Log.Info("deleteUserid:::" .. deleteUserid)
    local descText = GM.GameTextModel:GetText("clearAll_repeat_remind_desc", deleteUserid)
    GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "clearAll_remind_title", descText, "clearAll_remind_contact_btn", "clearAll_remind_ok_btn", function()
      GM.SDKHelper:OpenCustomerCenter()
    end, function(tbWindow)
      tbWindow:Close()
      GM.SDKHelper:LogOut()
      GM.SyncModel:ClearAllData()
      GM:RestartGame(nil, EBIProjectType.RestartGameAction.DeleteAccountAlready)
    end, false, function(window)
      window.canCloseByChangeGameMode = false
    end)
  else
    GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("clearAll_fail_title"), GM.GameTextModel:GetText("clearAll_fail_desc"), GM.GameTextModel:GetText("clearAll_fail_btn"), nil, nil, true)
  end
end

function AccountManager:_MarkLogout2Server()
  ApiMessage.MarkLogout(function(success, resp)
    if success then
      Log.Info("MarkLogout: callback success")
    else
      Log.Info("MarkLogout: callback fail")
    end
  end)
end

function AccountManager:GiveBindRewardIfCould()
  do return end
  if not self:IsEnabled() then
    return
  end
  if self:HasAccount() and not self:HasClaimedBindReward() and PlayerPrefs.GetInt(EPlayerPrefKey.EnteredBindEntry, 0) == 1 then
    GM.RewardModel:ReceiveReward(ERewardKey.AccountBind, function(eRewardRespStatus)
      if eRewardRespStatus == ERewardRespStatus.Success then
        local reward = {
          [PROPERTY_TYPE] = EPropertyType.Gem,
          [PROPERTY_COUNT] = ACCOUNT_BIND_REWARD_COUNT
        }
        RewardApi.AcquireRewards({reward}, EPropertySource.Give, EBIType.BindReward)
      end
    end)
  end
end

function AccountManager:HasClaimedBindReward()
  return GM.RewardModel:HasReceivedReward(ERewardKey.AccountBind)
end

function AccountManager:ShowBindReward()
  do return false end
  if not self:IsEnabled() then
    return false
  end
  return not self:HasAccount() and not self:HasClaimedBindReward()
end

function AccountManager:ShowBindStrongTip()
  return self:ShowBindReward() and GM.LevelModel:GetCurrentLevel() >= 7 and PlayerPrefs.GetInt(EPlayerPrefKey.EnteredBindEntry, 0) ~= 1
end

function AccountManager:IsEnabled()
  return GM.ConfigModel:IsServerControlOpen(EGeneralConfType.SocialBind)
end
