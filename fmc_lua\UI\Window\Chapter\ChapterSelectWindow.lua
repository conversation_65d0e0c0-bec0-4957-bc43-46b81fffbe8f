ChapterSelectWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  hideHudAnchorType = {
    EHudAnchorType.All
  }
}, BaseWindow)
ChapterSelectWindow.__index = ChapterSelectWindow
local UPCOMING_CHAPTER = "upcoming"

function ChapterSelectWindow:Init()
  self:InitCells()
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_cellRoot.transform)
  local chapterName = GM.ChapterManager.curActiveChapterName
  local chapterId = GM.ChapterDataModel:GetChapterIdByName(chapterName)
  if GM.TaskManager:IsAllFinished() then
    chapterId = chapterId + 1
  end
  local offsetY = self.m_cellRoot.transform.rect.size.y - 742 * chapterId - 750 + GM.UIManager:GetCanvasSize().y * 0.5
  self.m_cellRoot.transform:SetAnchoredPosY(offsetY)
  local isCurChapter = chapterName == GM.TaskManager:GetOngoingChapterName()
  self.m_currentRoomBtnGo:SetActive(isCurChapter)
  self.m_otherRoomBtnGo:SetActive(not isCurChapter)
  EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {Show = false})
end

function ChapterSelectWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {Show = true})
  for _, cell in ipairs(self.m_arrCells) do
    cell:OnCloseView()
  end
end

function ChapterSelectWindow:OnClickReturn()
  self:Close()
  local curActiveChapterName = GM.ChapterManager.curActiveChapterName
  local ongoingChapterName = GM.TaskManager:GetOngoingChapterName()
  local isOngoingChapter = curActiveChapterName and curActiveChapterName == ongoingChapterName
  if not isOngoingChapter then
    GM.SceneManager:ChangeChapterWithTransition(ongoingChapterName, nil, nil, nil, true)
  end
end

function ChapterSelectWindow:InitCells()
  self.m_arrCells = {}
  local arrChapterSequence = GM.ChapterDataModel:GetChapterSequence()
  for index, chapterName in ipairs(arrChapterSequence) do
    local tb = self:_CreateCell(index)
    self.m_arrCells[index] = tb
  end
  self.m_arrCells[#self.m_arrCells + 1] = self:_CreateCell(UPCOMING_CHAPTER)
end

function ChapterSelectWindow:_CreateCell(chapterId)
  local go = GameObject.Instantiate(self.m_cellTemplate, self.m_cellRoot)
  go:SetActive(true)
  local tb = go:GetLuaTable()
  tb:Init(chapterId)
  return tb
end

ChapterSelectCell = {}
ChapterSelectCell.__index = ChapterSelectCell
local DRAK_COLOR = CSColor(0.6, 0.6, 0.6, 1)

function ChapterSelectCell:Init(chapterId)
  self.chapterId = chapterId
  local ongoingChapterId = GM.TaskManager:GetOngoingChapterId()
  local isUpcoming = chapterId == UPCOMING_CHAPTER
  self.chapterName = isUpcoming and chapterId or GM.ChapterDataModel:GetChapterNameById(chapterId)
  local isDone = not isUpcoming and (chapterId < ongoingChapterId or GM.TaskManager:IsChapterFinished(chapterId))
  local isDoing = not isDone and chapterId == ongoingChapterId
  local isLocked = not isUpcoming and chapterId > ongoingChapterId
  self.m_lockGo:SetActive(isLocked)
  self.m_doneGo:SetActive(isDone)
  self.m_doingGo:SetActive(isDoing)
  self.m_upcomingGo:SetActive(isUpcoming)
  self.m_titleText.text = GM.ChapterDataModel:GetChapterNameText(self.chapterName)
  local imageName = GM.ChapterDataModel:GetChapterImageKey(self.chapterName)
  SpriteUtil.SetImage(self.m_image, imageName, false, function()
    if self.m_loadingGo and not self.m_loadingGo:IsNull() then
      self.m_loadingGo:SetActive(false)
    end
  end, nil, true)
  self.m_idText.text = "No." .. chapterId
  if isDoing then
    local finishedCount, totalCount = GM.TaskManager:GetOngoingChapterProgressCount()
    local percent = finishedCount * 100 // totalCount
    local sliderValue = finishedCount / totalCount
    self.m_slider.value = sliderValue
    self.m_sliderText.text = percent .. "%"
  elseif isUpcoming then
    self.m_idGo:SetActive(false)
    self.m_image.transform:SetLocalScaleXY(1)
  elseif isLocked then
    self.m_idLockBgGo:SetActive(true)
    self.m_image.color = DRAK_COLOR
    self.m_loadingTrans:SetLocalPosY(-170)
    self.m_loadingTrans:SetLocalScaleXY(0.5)
  elseif isDone then
    local key = chapterId == ongoingChapterId and "sceneList_btn_return" or "sceneList_btn_view"
    self.m_doneBtnText.text = GM.GameTextModel:GetText(key)
  end
  self.m_contentTrans:SetLocalScaleXY(0.6)
  self.m_tween = self.m_contentTrans:DOScale(1, 0.5):SetEase(Ease.OutBack)
end

function ChapterSelectCell:OnDestroy()
  if self.m_tween then
    self.m_tween:Kill()
    self.m_tween = nil
  end
end

function ChapterSelectCell:OnClickButton()
  GM.UIManager:CloseView(UIPrefabConfigName.ChapterSelectWindow)
  if self.chapterName ~= GM.ChapterManager.curActiveChapterName then
    GM.SceneManager:ChangeChapterWithTransition(self.chapterName, nil, nil, nil, true)
  end
end

function ChapterSelectCell:OnCloseView()
  if self.m_tween then
    self.m_tween:Kill()
    self.m_tween = nil
  end
  self.m_tween = self.m_contentTrans:DOScale(0.6, 0.3):SetEase(Ease.InBack)
end
