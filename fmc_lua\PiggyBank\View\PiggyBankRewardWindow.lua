PiggyBankRewardWindow = setmetatable({
  disableWindowMaskOnOpenView = 1,
  canCloseByAndroidBack = false,
  canClickWindowMask = true
}, BaseWindow)
PiggyBankRewardWindow.__index = PiggyBankRewardWindow

function PiggyBankRewardWindow:Init(gemCount, bFull)
  self.m_gemCount = gemCount
  self.m_bFull = bFull
  self.m_numText.text = self.m_gemCount
  self:PlayEnterAnimation()
  UIUtil.UpdateSortingOrder(self.m_effectGo, self:GetSortingOrder() + 1)
end

function PiggyBankRewardWindow:PlayEnterAnimation()
  self.m_canvasGroup.alpha = 0
  self.m_bubbleRectTrans.localScale = V3Zero
  self.m_piggybankAnimation.AnimationState:SetAnimation(0, "piggy_bank_idle_" .. (self.m_bFull and "3" or "2"), false)
  self.m_piggybankAnimation:Update(0)
  self.m_delayCall = DOVirtual.DelayedCall(0.5, function()
    if self.gameObject:IsNull() then
      return
    end
    self.m_delayCall = nil
    self.m_canvasGroup:DOFade(1, 0.3)
    self.m_bubbleRectTrans:DOScale(1, 0.15)
  end)
end

function PiggyBankRewardWindow:OnWindowMaskClicked()
  if self.m_bClosing then
    return
  end
  self.m_bClosing = true
  self.m_canvasGroup:DOFade(0, 0.2)
  self.m_bubbleRectTrans:DOScale(0, 0.15)
  self.m_piggybankAnimation.AnimationState:SetAnimation(0, "piggy_bank_break_" .. (self.m_bFull and "2" or "1"), false)
  GM.UIManager:SetEventLock(true, self)
  local rewards = Table.ListRep({
    [PROPERTY_TYPE] = EPropertyType.Gem,
    [PROPERTY_COUNT] = self.m_gemCount // 3
  }, 3, true)
  rewards[3][PROPERTY_COUNT] = self.m_gemCount - self.m_gemCount // 3 * 2
  local worldPos = {
    self.m_breakGemPosRectTrans1.position,
    self.m_breakGemPosRectTrans2.position,
    self.m_breakGemPosRectTrans3.position
  }
  local sequence = DOTween.Sequence()
  sequence:InsertCallback(0.09, function()
    self.m_effectGo:SetActive(true)
  end)
  sequence:InsertCallback(0.15, function()
    RewardApi.AcquireRewardsInView(rewards, {arrWorldPos = worldPos, noDelayTime = true})
    EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
      highlight = true,
      hudKey = ESceneViewHudButtonKey.Gem
    })
  end)
  sequence:InsertCallback(2, function()
    GM.UIManager:SetEventLock(false, self)
    EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
      highlight = false,
      hudKey = ESceneViewHudButtonKey.Gem
    })
    self.m_closeTween = nil
    self:Close()
  end)
  self.m_closeTween = sequence
end

function PiggyBankRewardWindow:OnDestroy()
  GM.UIManager:RemoveAllEventLocks(self)
  if self.m_closeTween ~= nil then
    self.m_closeTween:Kill()
    self.m_closeTween = nil
  end
end
