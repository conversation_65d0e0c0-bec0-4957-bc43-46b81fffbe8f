NoticeContentWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
NoticeContentWindow.__index = NoticeContentWindow
NoticeContentWindow.ScrollViewHeightNoReward = 1040
NoticeContentWindow.ScrollViewHeightNoButton = 1040
NoticeContentWindow.MinRewardContentWidth = 749
NoticeContentWindow.RewardDistance = 160

function NoticeContentWindow:Init(notice)
  local state = GM.NoticeModel:GetNoticeState(notice)
  if state == NoticeState.Unread then
    GM.BIManager:LogAction(EBIType.ReadNotice, tostring(notice.Id))
    GM.NoticeModel:OnNoticeOpen(notice)
  end
  self.m_notice = notice
  self.m_titleText.text = notice.Title
  if notice.Rewards ~= nil then
    for index, reward in ipairs(notice.Rewards) do
      local cell = Object.Instantiate(self.m_rewardCellPrefab, self.m_rewardContent):GetLuaTable()
      cell:Init(reward)
    end
    self.m_rewardClaimedMask:SetActive(state == NoticeState.Read)
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_rewardContent)
    if self.m_rewardLayoutGroup.preferredWidth > NoticeContentWindow.MinRewardContentWidth then
      UIUtil.SetSizeDelta(self.m_rewardContent, self.m_rewardLayoutGroup.preferredWidth, nil)
    else
      self.m_rewardScrollRect.enabled = false
    end
  else
    self.m_rewardGo:SetActive(false)
  end
  self:_UpdateButtonDisplay()
  if not self.m_button.gameObject.activeSelf then
    UIUtil.SetSizeDelta(self.m_scrollView, nil, NoticeContentWindow.ScrollViewHeightNoButton)
  elseif not self.m_rewardGo.activeSelf then
    UIUtil.SetSizeDelta(self.m_scrollView, nil, NoticeContentWindow.ScrollViewHeightNoReward)
  end
  self:_InitContent()
  self.m_contentTrans:SetAnchoredPosY(0)
end

function NoticeContentWindow:_InitContent()
  local parts = StringUtil.Split(self.m_notice.Content, "<<<img=")
  for _, part in ipairs(parts) do
    if string.find(part, ">>>") == nil then
      self:_AddContentText(part)
    else
      local subParts = StringUtil.Split(part, ">>>")
      self:_AddContentImage(subParts[1])
      self:_AddContentText(subParts[2])
    end
  end
end

function NoticeContentWindow:_AddContentText(text)
  if text == "" then
    return
  end
  local contentText = Object.Instantiate(self.m_contentTextPrefab, self.m_contentTrans):GetComponent(typeof(Text))
  contentText.text = StringUtil.Trim(text)
end

function NoticeContentWindow:_AddContentImage(url)
  local contentImage = Object.Instantiate(self.m_contentImagePrefab, self.m_contentTrans):GetComponent(typeof(Image))
  local callback = function(result, sprite)
    if result and not contentImage:IsNull() then
      contentImage.sprite = sprite
      contentImage:SetNativeSize()
    end
  end
  LoadUrlImage:LoadSprite(url, callback)
end

function NoticeContentWindow:_UpdateButtonDisplay()
  if self.m_notice.Link == nil and self.m_notice.Rewards == nil then
    self.m_button.gameObject:SetActive(false)
  elseif GM.GameModel:GetServerTime() > self.m_notice.DisappearTime then
    local buttonText = GM.GameTextModel:GetText("mail_window_timeout_reward_button")
    self.m_button:SetText(buttonText)
    self.m_button:SetEnabled(false)
  else
    local state = GM.NoticeModel:GetNoticeState(self.m_notice)
    if state == NoticeState.GetRewards then
      local buttonText = GM.GameTextModel:GetText("mail_window_get_reward_button")
      self.m_button:SetText(buttonText)
    elseif state == NoticeState.Read and self.m_notice.Rewards ~= nil then
      local buttonText = GM.GameTextModel:GetText("mail_window_no_reward_button")
      self.m_button:SetText(buttonText)
      self.m_button:SetEnabled(false)
      self.m_rewardClaimedMask:SetActive(true)
    else
      local buttonText = GM.GameTextModel:GetText("mail_window_jump_button")
      self.m_button:SetText(buttonText)
    end
  end
end

function NoticeContentWindow:_OnButtonClicked()
  local state = GM.NoticeModel:GetNoticeState(self.m_notice)
  if state == NoticeState.GetRewards then
    self:_AcquireRewards()
  else
    CSPlatform:OpenURL(self.m_notice.Link)
    GM.NoticeModel:OnNoticeFollowLink(self.m_notice)
    self:_UpdateButtonDisplay()
  end
end

function NoticeContentWindow:_AcquireRewards()
  local callback = function(result)
    if result == ERewardRespStatus.Success then
      local rewards = self.m_notice.Rewards
      local worldPositionArray = {}
      for i = 1, #rewards do
        local x = (i - #rewards / 2 - 0.5) * NoticeContentWindow.RewardDistance
        local position = self.m_contentRect:TransformPoint(Vector3(x, 60, 0))
        table.insert(worldPositionArray, position)
      end
      local viewData = {arrWorldPos = worldPositionArray}
      RewardApi.AcquireRewardsInView(rewards, viewData)
    elseif result == ERewardRespStatus.HasReceived then
      GM.UIManager:ShowPromptWithKey("mail_window_no_reward")
    elseif result == ERewardRespStatus.Expired then
      GM.UIManager:ShowPromptWithKey("mail_window_timeout_reward")
    elseif result == ERewardRespStatus.RespFail then
      GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("bad_network_window_title"), GM.GameTextModel:GetText("bad_network_window_desc"), GM.GameTextModel:GetText("common_button_ok"))
    end
    self:_UpdateButtonDisplay()
  end
  GM.NoticeModel:AcquireRewards(self.m_notice, callback)
end
