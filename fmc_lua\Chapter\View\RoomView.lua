RoomView = {}
RoomView.__index = RoomView
local MAX_ORDER_BUBBLE_COUNT = 2
local MAX_CUSTOMER_COUNT = 4

function RoomView:Init(roomModel, onInitFinished)
  GM.UIManager:SetEventLock(true, self)
  self.m_camera, self.m_input = GM.ModeViewController:GetChapterCam()
  if GM.ModeViewController and GM.ModeViewController.bHasEntered then
    self.m_input:SetInitPos()
    self.m_input:RefreshBorder(tonumber(self.width), tonumber(self.height))
  end
  self.model = roomModel
  self.m_onInitFinished = onInitFinished
  self.m_mapCoveringSlots = {}
  self.m_mapSlots = {}
  for k = 0, self.m_slotsRoot.childCount - 1 do
    self:_AddSlot(self.m_slotsRoot:GetChild(k).gameObject)
  end
  self.m_mapCustomers = {}
  self:UpdateToStateMap(roomModel.slotStateMap, function()
    self.m_slotInitFinished = true
    self:OnLoadFinished()
  end)
  self:_LoadParts(function()
    self.m_partsInitFinished = true
    self:OnLoadFinished()
  end)
end

function RoomView:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  GM.UIManager:RemoveAllEventLocks(self)
end

function RoomView:_LoadParts(callback)
  self:_LoadPartsData()
  local toLoadCount = 2 + MAX_ORDER_BUBBLE_COUNT
  for _, customerName in pairs(CustomerPrefabConfigName) do
    if IsString(customerName) then
      toLoadCount = toLoadCount + 1
    end
  end
  local funcCheck = function()
    toLoadCount = toLoadCount - 1
    if toLoadCount == 0 and callback then
      callback()
    end
  end
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.RoomTaskBubble), self.loadRoot, V3Zero, function(taskBubbleGo)
    self.m_taskBubble = taskBubbleGo:GetLuaTable()
    self.m_taskBubble:Init()
    funcCheck()
  end)
  self.m_arrOrderBubbles = {}
  for i = 1, MAX_ORDER_BUBBLE_COUNT do
    GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.RoomOrderBubble), self.loadRoot, V3Zero, function(orderBubbleGo)
      local orderBubble = orderBubbleGo:GetLuaTable()
      self.m_arrOrderBubbles[i] = orderBubble
      funcCheck()
    end)
  end
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.RoomChef), self.m_rolePosRoot, V3Zero, function(mainRole)
    self.m_mainRole = mainRole:GetLuaTable()
    self.m_mainRole:Init()
    funcCheck()
  end)
  self.m_mapCustomers = {}
  for _, customerName in pairs(CustomerPrefabConfigName) do
    if IsString(customerName) then
      GM.ResourceLoader:LoadPrefab(GM.DataResource.CustomerPrefabConfig:GetConfig(customerName), self.loadRoot, Vector3.zero, function(go)
        self.m_mapCustomers[customerName] = go:GetLuaTable()
        self.m_mapCustomers[customerName]:Init(customerName)
        funcCheck()
      end)
    end
  end
end

function RoomView:OnEnterScene()
  self.m_input:SetInitPos()
  self.m_input:RefreshBorder(tonumber(self.width), tonumber(self.height))
  self.m_input:SetCameraToPos(self.m_taskBubble.transform.position, true)
end

function RoomView:OnLoadFinished()
  if not self.m_slotInitFinished or not self.m_partsInitFinished then
    return
  end
  self:_RefreshTaskBubble()
  self:_RefreshOrderBubbles()
  self:_RefreshCharacters()
  if GM.ModeViewController and GM.ModeViewController.bHasEntered then
    self.m_input:SetCameraToPos(self.m_taskBubble.transform.position, true)
  end
  GM.UIManager:SetEventLock(false, self)
  if self.m_onInitFinished then
    self.m_onInitFinished(self)
    self.m_onInitFinished = nil
  end
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self._RefreshTaskBubble)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnChangeGameMode)
  EventDispatcher.AddListener(EEventType.TimelineStart, self, self._OnTimelineStart)
  EventDispatcher.AddListener(EEventType.TimelineComplete, self, self._OnTimelineComplete)
end

function RoomView:_LoadPartsData()
  self.m_arrTaskBubbleTrans = {}
  for k = 0, self.m_taskBubblesRoot.childCount - 1 do
    local trans = self.m_taskBubblesRoot:Find(k + 1)
    self.m_arrTaskBubbleTrans[k + 1] = trans
  end
  Log.Assert(#self.m_arrTaskBubbleTrans == self.m_taskBubblesRoot.childCount, self.model.chapterName .. " taskBubbles 配置错误！")
  self.m_arrOrderBubbleTrans = {}
  for k = 0, self.m_orderBubblesRoot.childCount - 1 do
    self.m_arrOrderBubbleTrans[k + 1] = self.m_orderBubblesRoot:GetChild(k)
  end
  self.m_arrRolePosUnlockTask = {}
  self.m_mapRolePosTrans = {}
  for k = 0, self.m_rolePosRoot.childCount - 1 do
    local trans = self.m_rolePosRoot:GetChild(k)
    local taskId = tonumber(trans.name)
    local arrPos = self.m_mapRolePosTrans[taskId]
    if not arrPos then
      arrPos = {}
      self.m_mapRolePosTrans[taskId] = arrPos
      self.m_arrRolePosUnlockTask[#self.m_arrRolePosUnlockTask + 1] = taskId
    end
    arrPos[#arrPos + 1] = trans
  end
  table.sort(self.m_arrRolePosUnlockTask)
end

function RoomView:GetRoomBubble(hitCollider)
  if hitCollider == self.m_taskBubble.collider then
    return self.m_taskBubble
  end
  for _, orderBubble in ipairs(self.m_arrOrderBubbles) do
    if hitCollider == orderBubble.collider then
      return orderBubble
    end
  end
  return nil
end

function RoomView:_OnChangeGameMode()
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    self:_RefreshOrderBubbles()
    self:_RefreshCharacters()
    if GM.TutorialModel:IsTutorialOnGoing(ETutorialId.CG) or GM.TutorialModel:IsTutorialOnGoing(ETutorialId.Timeline) then
      self.m_mainRole.transform:SetLocalPosXY(10000, 10000)
      self.m_taskBubble:PlayHideAnimation(false)
      for _, orderBubble in ipairs(self.m_arrOrderBubbles) do
        orderBubble:PlayHideAnimation(false)
      end
      self:MoveCamera({340, 151.5}, 0.5, 0)
    end
  end
end

function RoomView:_OnTimelineStart()
  self:ToggleOrderBubbles(false)
  self.m_taskBubble:PlayHideAnimation()
  self:SetInputEnable(false)
end

function RoomView:_OnTimelineComplete()
  self:ToggleOrderBubbles(true)
  self.m_taskBubble:PlayShowAnimation()
  self:SetInputEnable(true)
  self:FinishCovering()
end

function RoomView:ToggleOrderBubbles(show)
  for _, bubble in ipairs(self.m_arrOrderBubbles) do
    bubble.gameObject:SetActive(show)
  end
end

function RoomView:_RefreshOrderBubbles()
  for _, orderBubble in ipairs(self.m_arrOrderBubbles) do
    orderBubble.transform:SetParent(self.loadRoot, false)
  end
  if GM.ChapterManager.curActiveChapterName ~= GM.TaskManager:GetOngoingChapterName() then
    return
  end
  local bTestShowAllOrderBubbles = GameConfig.IsTestMode() and PlayerPrefs.GetInt(EPlayerPrefKey.TestShowAllOrderBubbles, 0) ~= 0
  local orderBubbleCount = bTestShowAllOrderBubbles and 99 or MAX_ORDER_BUBBLE_COUNT
  local mainOrderModel = GM.MainBoardModel:GetOrderModel()
  local mapOrders = mainOrderModel:GetOrders()
  orderBubbleCount = math.min(orderBubbleCount, Table.GetMapSize(mapOrders))
  local arrSelectBubbleTrans = Table.ListAlwaysRandomSelectN(self.m_arrOrderBubbleTrans, orderBubbleCount)
  local mapRequirements = {}
  for _, order in pairs(mapOrders) do
    for _, requirement in ipairs(order:GetRequirements()) do
      mapRequirements[requirement] = true
    end
  end
  local arrUniqueRequirements = Table.GetKeys(mapRequirements)
  local arrSelectRequirements = Table.ListAlwaysRandomSelectN(arrUniqueRequirements, orderBubbleCount)
  for index, bubbleTrans in ipairs(arrSelectBubbleTrans) do
    if arrSelectRequirements[index] and self.m_arrOrderBubbles[index] then
      local bubble = self.m_arrOrderBubbles[index]
      bubble:UpdateContent(arrSelectRequirements[index])
      if GM.SceneManager:GetOldMode() == EGameMode.Board then
        bubble:PlayShowAnimation()
      end
      bubble.transform:SetParent(bubbleTrans, false)
    elseif bTestShowAllOrderBubbles then
      local bubble = GameObject.Instantiate(self.m_arrOrderBubbles[1].gameObject)
      self.m_arrOrderBubbles[#self.m_arrOrderBubbles + 1] = bubble:GetLuaTable()
      bubble.transform:SetParent(bubbleTrans, false)
    end
  end
end

function RoomView:_RefreshTaskBubble()
  local currentTaskId = GM.TaskManager:GetOngoingTask()
  if currentTaskId == nil or GM.ChapterManager.curActiveChapterName ~= GM.TaskManager:GetOngoingChapterName() then
    self.m_taskBubble.transform:SetParent(self.loadRoot, false)
    return
  end
  local taskTrans = self.m_arrTaskBubbleTrans[currentTaskId]
  Log.Assert(taskTrans ~= nil, "取不到任务气泡挂载节点：" .. tostring(currentTaskId))
  self.m_taskBubble.transform:SetParent(taskTrans, false)
  self.m_taskBubble:UpdateContent()
end

function RoomView:_GetMainRolePossibleParents()
  local currentTaskId = GM.TaskManager:GetOngoingTask()
  local arrAvailableTrans = {}
  for _, unlockTask in ipairs(self.m_arrRolePosUnlockTask) do
    if not currentTaskId or unlockTask <= currentTaskId then
      Table.ListAppend(arrAvailableTrans, self.m_mapRolePosTrans[unlockTask])
    end
  end
  return arrAvailableTrans
end

function RoomView:_RefreshCharacters()
  if GM.TaskManager:GetOngoingChapterName() == GM.ChapterManager.curActiveChapterName then
    local arrAvailableTrans = self:_GetMainRolePossibleParents()
    local selectTrans = Table.ListRandomSelectOne(arrAvailableTrans)
    self.m_mainRole:SetFlipX(false)
    self.m_mainRole.transform.localPosition = selectTrans and selectTrans.localPosition or Vector3.zero
  else
    self.m_mainRole.gameObject:SetActive(false)
  end
  self:_DetachCustomers()
  local arrSeats = {}
  for slotId, slotView in pairs(self.m_mapSlots) do
    if slotView:HasSeats() then
      Table.ListAppend(arrSeats, slotView:GetSeats())
    end
  end
  local count = MAX_CUSTOMER_COUNT
  count = math.min(count, #arrSeats)
  local arrCustomers = Table.GetValueList(self.m_mapCustomers)
  count = math.min(count, #arrCustomers)
  local arrSelectSeats = Table.ListRandomSelectN(arrSeats, count)
  local arrSelectCustomers = Table.ListRandomSelectN(arrCustomers, count)
  for i = 1, count do
    local customer = arrSelectCustomers[i]
    local seat = arrSelectSeats[i]
    customer:SitOn(seat.root, seat.flip)
  end
end

function RoomView:_DetachCustomers()
  for _, customer in pairs(self.m_mapCustomers) do
    customer.transform:SetParent(self.loadRoot, false)
  end
end

function RoomView:_AddSlot(go)
  local slotId = go.name
  local slotView = go:GetLuaTable()
  slotView:Init(self.model, slotId, self)
  self.m_mapSlots[slotId] = slotView
end

function RoomView:GetSlotView(slotId)
  return self.m_mapSlots[slotId]
end

function RoomView:UpdateToStateMap(slotStateMap, callback)
  Log.Assert(slotStateMap ~= nil, "RoomView 更新数据为空。")
  self:_DetachCustomers()
  local count = 0
  for _, _ in pairs(self.m_mapSlots) do
    count = count + 1
  end
  if count == 0 then
    if callback then
      callback()
    end
    return
  end
  local func = function()
    count = count - 1
    if count <= 0 and callback then
      callback()
    end
  end
  for slotId, _ in pairs(self.m_mapSlots) do
    self:SetSlotState(slotId, slotStateMap[slotId], func, true)
  end
end

function RoomView:UpdateToLogicChoices(callback)
  self:UpdateToStateMap(self.model.slotStateMap, callback)
end

function RoomView:SetSlotState(slotId, state, callback, ignoreCheck)
  local slot = self.m_mapSlots[slotId]
  if slot == nil then
    Log.Error("槽位 " .. slotId .. " 不存在！请策划检查配置！")
  else
    slot:SetState(state, callback, ignoreCheck)
  end
end

function RoomView:PlayAnimator(slotId, state, callback, delay)
  DelayExecuteFuncInView(function()
    local slot = self.m_mapSlots[slotId]
    if slot == nil then
      Log.Error("槽位 " .. slotId .. " 不存在！请策划检查配置！")
      if callback then
        callback()
      end
    else
      slot:PlayAnimator(state, callback)
    end
  end, delay, self)
end

function RoomView:PlaySpineAnimation(slotId, state, animationName, callback, delay)
  DelayExecuteFuncInView(function()
    local slot = self.m_mapSlots[slotId]
    if slot == nil then
      Log.Error("槽位 " .. slotId .. " 不存在！请策划检查配置！")
      if callback then
        callback()
      end
    else
      slot:PlaySpineAnimation(state, animationName, callback)
    end
  end, delay, self)
end

function RoomView:PlayTransAnimation(slotId, animationConfig, callback, delay)
  DelayExecuteFuncInView(function()
    local slot = self.m_mapSlots[slotId]
    if slot == nil then
      Log.Error("槽位 " .. slotId .. " 不存在！请策划检查配置！")
      if callback then
        callback()
      end
      return
    end
    slot:PlayTransAnimation(animationConfig, callback)
  end, delay, self)
end

function RoomView:PlayEffectOnSlot(slotId, animationConfig, callback, delay)
  DelayExecuteFuncInView(function()
    local slot = self.m_mapSlots[slotId]
    if slot == nil then
      Log.Error("槽位 " .. slotId .. " 不存在！请策划检查配置！")
      if callback then
        callback()
      end
      return
    end
    slot:PlayEffect(animationConfig, callback)
  end, delay, self)
end

function RoomView:PlayPlaceAnimation(slotId, animationConfig, newState, callback, delay)
  DelayExecuteFuncInView(function()
    local slot = self.m_mapSlots[slotId]
    if slot == nil then
      Log.Error("槽位 " .. slotId .. " 不存在！请策划检查配置！")
      if callback then
        callback()
      end
      return
    end
    slot:PlayPlaceAnimation(animationConfig, newState, callback)
  end, delay, self)
end

function RoomView:ShakeCamera(strength, delay, duration, callback)
  GM.ModeViewController:GetChapterCam():DOShakePosition(duration, tonumber(strength), 18, 90, false):OnComplete(callback):SetDelay(delay or 0)
end

function RoomView:MoveCamera(pos, scale, duration, onFinish)
  local size = self.m_input:CalculateCameraSize(scale)
  pos = self.m_input:ClampPos({
    x = pos[1],
    y = pos[2]
  }, size)
  pos = Vector3(pos.x, pos.y, self.m_camera.transform.position.z)
  self.m_camera.transform:DOMove(pos, duration):SetEase(Ease.InOutSine)
  self.m_camera:DOOrthoSize(size, duration):SetEase(Ease.InOutSine)
  if not GM.TimelineManager:IsPlayingTimeline() then
    self:SetInputEnable(false)
  end
  DelayExecuteFuncInView(function()
    self.m_input:SetCameraSize(size)
    self.m_input:SetCameraToPos(pos, true)
    if not GM.TimelineManager:IsPlayingTimeline() then
      self:SetInputEnable(true)
    end
    if onFinish then
      onFinish()
    end
  end, duration, self, true)
end

function RoomView:GetBubbleScale()
  return self.m_input:GetBubbleScale()
end

function RoomView:SetInputEnable(enabled)
  self.m_input:SetUpdateEnable(enabled)
end

function RoomView:PlayRoleAnimaion(animationName, posXY, flipX, waitTime, onFinish)
  self.m_mainRole.transform:SetPosXY(posXY[1], posXY[2])
  self.m_mainRole:SetFlipX(flipX)
  self.m_mainRole:PlayAnimation(animationName)
  DelayExecuteFuncInView(function()
    if onFinish then
      onFinish()
    end
  end, waitTime, self)
end

function RoomView:CoverSlot(slotId)
  if self.m_mapCoveringSlots[slotId] then
    Log.Error("槽位 " .. tostring(slotId) .. " 已经 Covered！")
    return
  end
  local slot = self.m_mapSlots[slotId]
  if slot == nil then
    Log.Error("槽位 " .. tostring(slotId) .. " 不存在！请策划检查配置！")
  else
    slot:StartCovering()
    self.m_mapCoveringSlots[slotId] = slot
  end
end

function RoomView:FinishCovering()
  for slotId, slot in pairs(self.m_mapCoveringSlots) do
    slot:FinishCovering()
  end
  self.m_mapCoveringSlots = {}
end

function RoomView:PutMainRoleToCenter()
  if not self.m_mainRole then
    GM.UIManager:ShowTestPrompt("主角不存在！")
    return
  end
  local chapterCam = GM.ModeViewController:GetChapterCam()
  local chapterCamPos = chapterCam.transform.position
  self.m_mainRole.transform.position = chapterCamPos + Vector3(0, -170, 0)
end

function RoomView:TestShowAllPossibleChef()
  if not GameConfig.IsTestMode() then
    return
  end
  local arrAvailableTrans = self:_GetMainRolePossibleParents()
  for _, parent in ipairs(arrAvailableTrans) do
    local go = GameObject.Instantiate(self.m_mainRole.gameObject, self.m_mainRole.transform.parent)
    go:SetActive(true)
    go.transform.localPosition = parent.localPosition
  end
end
