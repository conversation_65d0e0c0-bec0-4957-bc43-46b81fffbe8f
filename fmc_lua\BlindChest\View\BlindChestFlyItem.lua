BlindChestFlyItem = {}
BlindChestFlyItem.__index = BlindChestFlyItem

function BlindChestFlyItem:PlayIconFlyAnim(image, endPos)
  SpriteUtil.SetImage(self.m_image, image, true, function()
    self:_PlayIconFlyAnim(endPos)
  end)
end

function BlindChestFlyItem:_PlayIconFlyAnim(endPos)
  local seq = DOTween.Sequence()
  local pos = PositionUtil.UICameraScreen2World({
    x = Screen.width / 2,
    y = Screen.height / 2,
    z = 0
  })
  pos.z = self.transform.position.z
  seq:Append(self.transform:DOMove(pos, 0.5))
  seq:Insert(0.4, self.transform:DOScale(3.5, 0.35))
  seq:InsertCallback(0.7, function()
    self.m_effectGo:SetActive(true)
  end)
  seq:Insert(1.3, self.transform:DOScale(4.5, 0.1))
  seq:Append(self.transform:DOScale(1, 0.2))
  seq:Append(self.transform:DOMove(endPos, 0.8))
  seq:AppendCallback(function()
    self.m_effectGo:SetActive(false)
    Recycle(self.gameObject)
  end)
end
