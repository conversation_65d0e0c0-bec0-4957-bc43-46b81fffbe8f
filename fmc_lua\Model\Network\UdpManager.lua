UdpManager = {}
UdpManager.__index = UdpManager

function UdpManager:Init()
  self.m_fTickCount = 0.0
  self.m_redisHealthCheck = UdpHealthCheck.Create(NetworkConfig.GetUdpServerUrl("RS"))
end

function UdpManager:Update(dt)
  self.m_fTickCount = (self.m_fTickCount or 0) + dt
  self.m_redisHealthCheck:Update(self.m_fTickCount)
end

function UdpManager:GetRedisHealthCheck()
  return self.m_redisHealthCheck
end
