local Serialization = require("Model.Network.Serialization")()
Serialization.ProtocolMd5 = {
  38,
  245,
  19,
  252,
  14,
  233,
  63,
  250,
  182,
  31,
  190,
  249,
  63,
  34,
  237,
  81
}
Serialization.UserInfo = {}
Serialization.UserInfo.__index = Serialization.UserInfo

function Serialization.UserInfo.Create(game_token, userid)
  local value = setmetatable({}, Serialization.UserInfo)
  value.game_token = game_token
  value.userid = userid
  return value
end

function Serialization.UserInfo.Serialize(writer, value)
  local bRet
  assert(value.game_token ~= nil)
  bRet = Serialization.String.Serialize(writer, value.game_token)
  if not bRet then
    return false
  end
  assert(value.userid ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userid)
  if not bRet then
    return false
  end
  return true
end

function Serialization.UserInfo.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.UserInfo)
  bRet, value.game_token = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.userid = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.SocialInfo = {}
Serialization.SocialInfo.__index = Serialization.SocialInfo

function Serialization.SocialInfo.Create(key, id, type, name, icon, phone, infos)
  local value = setmetatable({}, Serialization.SocialInfo)
  value.key = key
  value.id = id
  value.type = type
  value.name = name
  value.icon = icon
  value.phone = phone
  value.infos = infos
  return value
end

function Serialization.SocialInfo.Serialize(writer, value)
  local bRet
  assert(value.key ~= nil)
  bRet = Serialization.String.Serialize(writer, value.key)
  if not bRet then
    return false
  end
  assert(value.id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.id)
  if not bRet then
    return false
  end
  assert(value.type ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.type)
  if not bRet then
    return false
  end
  assert(value.name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.name)
  if not bRet then
    return false
  end
  assert(value.icon ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon)
  if not bRet then
    return false
  end
  assert(value.phone ~= nil)
  bRet = Serialization.String.Serialize(writer, value.phone)
  if not bRet then
    return false
  end
  assert(value.infos ~= nil)
  bRet = Serialization.Array(Serialization.UserInfo).Serialize(writer, value.infos)
  if not bRet then
    return false
  end
  return true
end

function Serialization.SocialInfo.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.SocialInfo)
  bRet, value.key = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.type = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.phone = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.infos = Serialization.Array(Serialization.UserInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.AccountBindReq = {}
Serialization.AccountBindReq.__index = Serialization.AccountBindReq

function Serialization.AccountBindReq.Create(game_token, deviceId, vendorId, openUDID, installationId, idfa, adid, imei, oaid, imsi, imei_meid, wifiMAC, bluetoothMAC, currentUserId, action_type, firstOpenTime, socialId, socialType, social_name, social_icon, social_appid, social_openid, social_access_token, social_refresh_token, social_email, phone_verify_code, token_access, adjust_id, ctime, extappid)
  local value = setmetatable({}, Serialization.AccountBindReq)
  value.game_token = game_token
  value.deviceId = deviceId
  value.vendorId = vendorId
  value.openUDID = openUDID
  value.installationId = installationId
  value.idfa = idfa
  value.adid = adid
  value.imei = imei
  value.oaid = oaid
  value.imsi = imsi
  value.imei_meid = imei_meid
  value.wifiMAC = wifiMAC
  value.bluetoothMAC = bluetoothMAC
  value.currentUserId = currentUserId
  value.action_type = action_type
  value.firstOpenTime = firstOpenTime
  value.socialId = socialId
  value.socialType = socialType
  value.social_name = social_name
  value.social_icon = social_icon
  value.social_appid = social_appid
  value.social_openid = social_openid
  value.social_access_token = social_access_token
  value.social_refresh_token = social_refresh_token
  value.social_email = social_email
  value.phone_verify_code = phone_verify_code
  value.token_access = token_access
  value.adjust_id = adjust_id
  value.ctime = ctime
  value.extappid = extappid
  return value
end

function Serialization.AccountBindReq.Serialize(writer, value)
  local bRet
  assert(value.game_token ~= nil)
  bRet = Serialization.String.Serialize(writer, value.game_token)
  if not bRet then
    return false
  end
  assert(value.deviceId ~= nil)
  bRet = Serialization.String.Serialize(writer, value.deviceId)
  if not bRet then
    return false
  end
  assert(value.vendorId ~= nil)
  bRet = Serialization.String.Serialize(writer, value.vendorId)
  if not bRet then
    return false
  end
  assert(value.openUDID ~= nil)
  bRet = Serialization.String.Serialize(writer, value.openUDID)
  if not bRet then
    return false
  end
  assert(value.installationId ~= nil)
  bRet = Serialization.String.Serialize(writer, value.installationId)
  if not bRet then
    return false
  end
  assert(value.idfa ~= nil)
  bRet = Serialization.String.Serialize(writer, value.idfa)
  if not bRet then
    return false
  end
  assert(value.adid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.adid)
  if not bRet then
    return false
  end
  assert(value.imei ~= nil)
  bRet = Serialization.String.Serialize(writer, value.imei)
  if not bRet then
    return false
  end
  assert(value.oaid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.oaid)
  if not bRet then
    return false
  end
  assert(value.imsi ~= nil)
  bRet = Serialization.String.Serialize(writer, value.imsi)
  if not bRet then
    return false
  end
  assert(value.imei_meid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.imei_meid)
  if not bRet then
    return false
  end
  assert(value.wifiMAC ~= nil)
  bRet = Serialization.String.Serialize(writer, value.wifiMAC)
  if not bRet then
    return false
  end
  assert(value.bluetoothMAC ~= nil)
  bRet = Serialization.String.Serialize(writer, value.bluetoothMAC)
  if not bRet then
    return false
  end
  assert(value.currentUserId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.currentUserId)
  if not bRet then
    return false
  end
  assert(value.action_type ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.action_type)
  if not bRet then
    return false
  end
  assert(value.firstOpenTime ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.firstOpenTime)
  if not bRet then
    return false
  end
  assert(value.socialId ~= nil)
  bRet = Serialization.String.Serialize(writer, value.socialId)
  if not bRet then
    return false
  end
  assert(value.socialType ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.socialType)
  if not bRet then
    return false
  end
  assert(value.social_name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.social_name)
  if not bRet then
    return false
  end
  assert(value.social_icon ~= nil)
  bRet = Serialization.String.Serialize(writer, value.social_icon)
  if not bRet then
    return false
  end
  assert(value.social_appid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.social_appid)
  if not bRet then
    return false
  end
  assert(value.social_openid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.social_openid)
  if not bRet then
    return false
  end
  assert(value.social_access_token ~= nil)
  bRet = Serialization.String.Serialize(writer, value.social_access_token)
  if not bRet then
    return false
  end
  assert(value.social_refresh_token ~= nil)
  bRet = Serialization.String.Serialize(writer, value.social_refresh_token)
  if not bRet then
    return false
  end
  assert(value.social_email ~= nil)
  bRet = Serialization.String.Serialize(writer, value.social_email)
  if not bRet then
    return false
  end
  assert(value.phone_verify_code ~= nil)
  bRet = Serialization.String.Serialize(writer, value.phone_verify_code)
  if not bRet then
    return false
  end
  assert(value.token_access ~= nil)
  bRet = Serialization.String.Serialize(writer, value.token_access)
  if not bRet then
    return false
  end
  assert(value.adjust_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.adjust_id)
  if not bRet then
    return false
  end
  assert(value.ctime ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.ctime)
  if not bRet then
    return false
  end
  assert(value.extappid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.extappid)
  if not bRet then
    return false
  end
  return true
end

function Serialization.AccountBindReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.AccountBindReq)
  bRet, value.game_token = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.deviceId = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.vendorId = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.openUDID = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.installationId = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.idfa = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.adid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.imei = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.oaid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.imsi = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.imei_meid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.wifiMAC = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.bluetoothMAC = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.currentUserId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.action_type = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.firstOpenTime = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.socialId = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.socialType = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.social_name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.social_icon = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.social_appid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.social_openid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.social_access_token = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.social_refresh_token = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.social_email = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.phone_verify_code = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.token_access = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.adjust_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ctime = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.extappid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.AccountBindResp = {}
Serialization.AccountBindResp.__index = Serialization.AccountBindResp

function Serialization.AccountBindResp.Create(registrationStatus, mainUserId, devUserId, extraUserId, first_bind, service_time, social_infos, extra_social_info, user_infos, reg_extappid, reg_version, reg_channel)
  local value = setmetatable({}, Serialization.AccountBindResp)
  value.registrationStatus = registrationStatus
  value.mainUserId = mainUserId
  value.devUserId = devUserId
  value.extraUserId = extraUserId
  value.first_bind = first_bind
  value.service_time = service_time
  value.social_infos = social_infos
  value.extra_social_info = extra_social_info
  value.user_infos = user_infos
  value.reg_extappid = reg_extappid
  value.reg_version = reg_version
  value.reg_channel = reg_channel
  return value
end

function Serialization.AccountBindResp.Serialize(writer, value)
  local bRet
  assert(value.registrationStatus ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.registrationStatus)
  if not bRet then
    return false
  end
  assert(value.mainUserId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.mainUserId)
  if not bRet then
    return false
  end
  assert(value.devUserId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.devUserId)
  if not bRet then
    return false
  end
  assert(value.extraUserId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.extraUserId)
  if not bRet then
    return false
  end
  assert(value.first_bind ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.first_bind)
  if not bRet then
    return false
  end
  assert(value.service_time ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.service_time)
  if not bRet then
    return false
  end
  assert(value.social_infos ~= nil)
  bRet = Serialization.Array(Serialization.SocialInfo).Serialize(writer, value.social_infos)
  if not bRet then
    return false
  end
  assert(value.extra_social_info ~= nil)
  bRet = Serialization.SocialInfo.Serialize(writer, value.extra_social_info)
  if not bRet then
    return false
  end
  assert(value.user_infos ~= nil)
  bRet = Serialization.Array(Serialization.UserInfo).Serialize(writer, value.user_infos)
  if not bRet then
    return false
  end
  assert(value.reg_extappid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.reg_extappid)
  if not bRet then
    return false
  end
  assert(value.reg_version ~= nil)
  bRet = Serialization.String.Serialize(writer, value.reg_version)
  if not bRet then
    return false
  end
  assert(value.reg_channel ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.reg_channel)
  if not bRet then
    return false
  end
  return true
end

function Serialization.AccountBindResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.AccountBindResp)
  bRet, value.registrationStatus = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.mainUserId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.devUserId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.extraUserId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.first_bind = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.service_time = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.social_infos = Serialization.Array(Serialization.SocialInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.extra_social_info = Serialization.SocialInfo.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.user_infos = Serialization.Array(Serialization.UserInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.reg_extappid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.reg_version = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.reg_channel = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.SODeleteUserReq = {}
Serialization.SODeleteUserReq.__index = Serialization.SODeleteUserReq

function Serialization.SODeleteUserReq.Create(userId, game_token, action_type, phone_number)
  local value = setmetatable({}, Serialization.SODeleteUserReq)
  value.userId = userId
  value.game_token = game_token
  value.action_type = action_type
  value.phone_number = phone_number
  return value
end

function Serialization.SODeleteUserReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.game_token ~= nil)
  bRet = Serialization.String.Serialize(writer, value.game_token)
  if not bRet then
    return false
  end
  assert(value.action_type ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.action_type)
  if not bRet then
    return false
  end
  assert(value.phone_number ~= nil)
  bRet = Serialization.String.Serialize(writer, value.phone_number)
  if not bRet then
    return false
  end
  return true
end

function Serialization.SODeleteUserReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.SODeleteUserReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.game_token = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.action_type = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.phone_number = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.SODeleteUserResp = {}
Serialization.SODeleteUserResp.__index = Serialization.SODeleteUserResp

function Serialization.SODeleteUserResp.Create(rcode)
  local value = setmetatable({}, Serialization.SODeleteUserResp)
  value.rcode = rcode
  return value
end

function Serialization.SODeleteUserResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  return true
end

function Serialization.SODeleteUserResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.SODeleteUserResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.VerifyCellPhoneReq = {}
Serialization.VerifyCellPhoneReq.__index = Serialization.VerifyCellPhoneReq

function Serialization.VerifyCellPhoneReq.Create(game_token, phone_number, ctime)
  local value = setmetatable({}, Serialization.VerifyCellPhoneReq)
  value.game_token = game_token
  value.phone_number = phone_number
  value.ctime = ctime
  return value
end

function Serialization.VerifyCellPhoneReq.Serialize(writer, value)
  local bRet
  assert(value.game_token ~= nil)
  bRet = Serialization.String.Serialize(writer, value.game_token)
  if not bRet then
    return false
  end
  assert(value.phone_number ~= nil)
  bRet = Serialization.String.Serialize(writer, value.phone_number)
  if not bRet then
    return false
  end
  assert(value.ctime ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.ctime)
  if not bRet then
    return false
  end
  return true
end

function Serialization.VerifyCellPhoneReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.VerifyCellPhoneReq)
  bRet, value.game_token = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.phone_number = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ctime = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.VerifyCellPhoneResp = {}
Serialization.VerifyCellPhoneResp.__index = Serialization.VerifyCellPhoneResp

function Serialization.VerifyCellPhoneResp.Create(rcode, refresh_time)
  local value = setmetatable({}, Serialization.VerifyCellPhoneResp)
  value.rcode = rcode
  value.refresh_time = refresh_time
  return value
end

function Serialization.VerifyCellPhoneResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.refresh_time ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.refresh_time)
  if not bRet then
    return false
  end
  return true
end

function Serialization.VerifyCellPhoneResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.VerifyCellPhoneResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.refresh_time = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.AuthReq = {}
Serialization.AuthReq.__index = Serialization.AuthReq

function Serialization.AuthReq.Create(game_token, deviceId, vendorId, openUDID, installationId, idfa, adid, imei, oaid, imsi, imei_meid, wifiMAC, bluetoothMAC, currentUserId, firstOpenTime, adjust_id, ctime)
  local value = setmetatable({}, Serialization.AuthReq)
  value.game_token = game_token
  value.deviceId = deviceId
  value.vendorId = vendorId
  value.openUDID = openUDID
  value.installationId = installationId
  value.idfa = idfa
  value.adid = adid
  value.imei = imei
  value.oaid = oaid
  value.imsi = imsi
  value.imei_meid = imei_meid
  value.wifiMAC = wifiMAC
  value.bluetoothMAC = bluetoothMAC
  value.currentUserId = currentUserId
  value.firstOpenTime = firstOpenTime
  value.adjust_id = adjust_id
  value.ctime = ctime
  return value
end

function Serialization.AuthReq.Serialize(writer, value)
  local bRet
  assert(value.game_token ~= nil)
  bRet = Serialization.String.Serialize(writer, value.game_token)
  if not bRet then
    return false
  end
  assert(value.deviceId ~= nil)
  bRet = Serialization.String.Serialize(writer, value.deviceId)
  if not bRet then
    return false
  end
  assert(value.vendorId ~= nil)
  bRet = Serialization.String.Serialize(writer, value.vendorId)
  if not bRet then
    return false
  end
  assert(value.openUDID ~= nil)
  bRet = Serialization.String.Serialize(writer, value.openUDID)
  if not bRet then
    return false
  end
  assert(value.installationId ~= nil)
  bRet = Serialization.String.Serialize(writer, value.installationId)
  if not bRet then
    return false
  end
  assert(value.idfa ~= nil)
  bRet = Serialization.String.Serialize(writer, value.idfa)
  if not bRet then
    return false
  end
  assert(value.adid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.adid)
  if not bRet then
    return false
  end
  assert(value.imei ~= nil)
  bRet = Serialization.String.Serialize(writer, value.imei)
  if not bRet then
    return false
  end
  assert(value.oaid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.oaid)
  if not bRet then
    return false
  end
  assert(value.imsi ~= nil)
  bRet = Serialization.String.Serialize(writer, value.imsi)
  if not bRet then
    return false
  end
  assert(value.imei_meid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.imei_meid)
  if not bRet then
    return false
  end
  assert(value.wifiMAC ~= nil)
  bRet = Serialization.String.Serialize(writer, value.wifiMAC)
  if not bRet then
    return false
  end
  assert(value.bluetoothMAC ~= nil)
  bRet = Serialization.String.Serialize(writer, value.bluetoothMAC)
  if not bRet then
    return false
  end
  assert(value.currentUserId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.currentUserId)
  if not bRet then
    return false
  end
  assert(value.firstOpenTime ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.firstOpenTime)
  if not bRet then
    return false
  end
  assert(value.adjust_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.adjust_id)
  if not bRet then
    return false
  end
  assert(value.ctime ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.ctime)
  if not bRet then
    return false
  end
  return true
end

function Serialization.AuthReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.AuthReq)
  bRet, value.game_token = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.deviceId = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.vendorId = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.openUDID = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.installationId = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.idfa = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.adid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.imei = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.oaid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.imsi = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.imei_meid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.wifiMAC = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.bluetoothMAC = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.currentUserId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.firstOpenTime = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.adjust_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ctime = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.reqNameMap = {
  SOAccountBind = Serialization.AccountBindReq,
  SODeleteUser = Serialization.SODeleteUserReq,
  SOVerifyCellPhone = Serialization.VerifyCellPhoneReq,
  BIAuth = Serialization.AuthReq
}
Serialization.respNameMap = {
  SOAccountBind = Serialization.AccountBindResp,
  SODeleteUser = Serialization.SODeleteUserResp,
  SOVerifyCellPhone = Serialization.VerifyCellPhoneResp
}
return Serialization
