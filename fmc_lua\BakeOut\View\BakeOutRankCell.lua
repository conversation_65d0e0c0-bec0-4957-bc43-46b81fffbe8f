BakeOutRankCell = {}
BakeOutRankCell.__index = BakeOutRankCell
local mapMedalImageName = {
  [1] = ImageFileConfigName.medal_bg1,
  [2] = ImageFileConfigName.medal_bg2,
  [3] = ImageFileConfigName.medal_bg3
}

function BakeOutRankCell:Awake()
  EventDispatcher.AddListener(EEventType.UpdateProfile, self, self._OnChangeProfile)
end

function BakeOutRankCell:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BakeOutRankCell:_OnChangeProfile()
  if self.m_userId == GM.UserModel:GetUserId() then
    local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
    self.m_nameText.text = bakeOutModel:GetDisplayName(GM.UserProfileModel:GetName())
    self.m_userAvatar:SetAvatar(EAvatarFrame.Normal, GM.UserProfileModel:GetIcon())
  end
end

function BakeOutRankCell:UpdateContent(rankData, force, window)
  self.m_window = window
  self.m_userId = rankData.userid
  if force or rankData.rank ~= self.m_curRank then
    self.m_curRank = rankData.rank
    self.m_rankText.text = rankData.rank
    if mapMedalImageName[rankData.rank] ~= nil then
      self.m_rankImg.gameObject:SetActive(true)
      SpriteUtil.SetImage(self.m_rankImg, mapMedalImageName[rankData.rank], false)
    else
      self.m_rankImg.gameObject:SetActive(false)
    end
    self.m_userAvatar:SetAvatar(EAvatarFrame.Normal, rankData.icon)
    local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
    self.m_nameText.text = bakeOutModel:GetDisplayName(rankData.name)
    self.m_tokenText.text = rankData.score
    if bakeOutModel then
      local reward = bakeOutModel:GetRankReward(rankData.rank)
      self.m_rewardNode:UpdateContent(reward and reward.rewards or nil, rankData.rank, window)
    end
  end
end

function BakeOutRankCell:UpdateRank(rank)
  if rank == nil then
    return
  end
  rank = math.ceil(rank)
  self.m_rankText.text = rank
  self.m_curRank = rank
end

function BakeOutRankCell:GetRank()
  return self.m_curRank
end

function BakeOutRankCell:GetPadding()
  return self.gameObject.transform.sizeDelta.y - self.m_boardRectTrans.transform.sizeDelta.y
end
