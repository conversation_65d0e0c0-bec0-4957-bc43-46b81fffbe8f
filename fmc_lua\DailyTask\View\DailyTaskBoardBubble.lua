DailyTaskBoardBubble = {}
DailyTaskBoardBubble.__index = DailyTaskBoardBubble

function DailyTaskBoardBubble:Awake()
  self.m_model = GM.ActivityManager:GetModel(ActivityType.DailyTask)
  self:UpdateContent()
  EventDispatcher.AddListener(EEventType.DailyTaskStateChanged, self, self.UpdateContent)
  EventDispatcher.AddListener(EEventType.DailyTaskFinishTask, self, self.PlayProgressAnimation)
end

function DailyTaskBoardBubble:OnDestroy()
  if self.m_buttonTween ~= nil then
    self.m_buttonTween:Kill()
    self.m_buttonGo.transform.localScale = V3One
    self.m_buttonTween = nil
  end
  EventDispatcher.RemoveTarget(self)
end

function DailyTaskBoardBubble:PlayProgressAnimation()
  self.m_slider:DOKill()
  local totalTask = self.m_model:GetTaskCount()
  local finishedTask = self.m_model:GetFinishedTaskCount()
  local curProgress = finishedTask / totalTask
  self.m_slider:DOValue(curProgress, 0.6 * totalTask * (curProgress - self.m_slider.value)):OnComplete(function()
    if self.gameObject:IsNull() then
      return
    end
    self:UpdateContent()
  end)
end

function DailyTaskBoardBubble:UpdateContent()
  if not self.m_model:IsActivityOpen() then
    return
  end
  self.m_rewardItem:Init(self.m_model:GetFinalRewards()[1])
  self.m_lastTime = self.m_model:GetCurRoundEndTime()
  local canAcquireFinalRewards = self.m_model:CanAcquireFinalRewards()
  UIUtil.SetActive(self.m_slider.gameObject, not canAcquireFinalRewards)
  UIUtil.SetActive(self.m_buttonGo, canAcquireFinalRewards)
  if not canAcquireFinalRewards then
    local totalTask = self.m_model:GetTaskCount()
    local finishedTask = self.m_model:GetFinishedTaskCount()
    self.m_slider.value = finishedTask / totalTask
    self.m_sliderText.text = finishedTask .. "/" .. totalTask
    if self.m_buttonTween ~= nil then
      self.m_buttonTween:Kill()
      self.m_buttonGo.transform.localScale = V3One
      self.m_buttonTween = nil
    end
  elseif self.m_buttonTween == nil then
    local seq = DOTween.Sequence():SetLoops(-1)
    seq:Append(self.m_buttonGo.transform:DOScale(1.1, 0.4):SetLoops(2, LoopType.Yoyo))
    seq:AppendInterval(0.2)
    self.m_buttonTween = seq
  end
  self:UpdatePerSecond()
end

function DailyTaskBoardBubble:UpdatePerSecond()
  if self.m_model ~= nil and self.m_lastTime ~= nil then
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(math.max(0, self.m_lastTime - GM.GameModel:GetServerTime()), 2, false, false)
  end
end

function DailyTaskBoardBubble:OnBtnClicked()
  if self.m_model:CanAcquireFinalRewards() then
    if self.m_model:AcquireFinalRewards() then
      GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, self.m_model:GetFinalRewards(), "daily_task_clear_title", true)
    end
  else
    GM.UIManager:OpenView(self.m_model:GetMainWindowPrefabName())
  end
end

function DailyTaskBoardBubble:GetBoard()
  return self.m_boardRectTrans
end
