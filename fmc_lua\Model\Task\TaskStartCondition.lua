TaskStartCondition = {}
TaskStartCondition.__index = TaskStartCondition

function TaskStartCondition.Create(taskData, conditionData)
  local condition = setmetatable({}, TaskStartCondition)
  condition:_Init(taskData, conditionData)
  return condition
end

function TaskStartCondition:_Init(taskData, conditionData)
  self.taskData = taskData
  self.arrPreTaskIds = conditionData or {}
end

function TaskStartCondition:IsSatisfied()
  local taskModel = GM.TaskManager
  for _, taskId in ipairs(self.arrPreTaskIds) do
    if not taskModel:IsTaskFinished(self.taskData.chapterId, taskId) then
      return false
    end
  end
  return true
end
