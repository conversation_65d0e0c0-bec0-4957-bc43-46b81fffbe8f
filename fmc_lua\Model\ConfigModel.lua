LocalConfigKey = {
  BoardPrompt = "BoardPrompt",
  Bundle = "Bundle",
  FunctionEnable = "FunctionEnable",
  InventorySlot = "InventorySlot",
  ItemModel = "ItemModel",
  ItemProperty = "ItemProperty",
  ItemView = "ItemView",
  MainTask = "MainTask",
  ShopDailyDeals = "ShopDailyDeals",
  ShopFlashSale = "ShopFlashSale",
  ShopOrder = "ShopOrder",
  ShopIAP = "ShopIAP",
  ShopSlot = "ShopSlot",
  System = "System",
  NotificationText = "NotificationText",
  TaskGroup = "TaskGroup",
  OrderAvatar = "OrderAvatar",
  BubbleConfig = "BubbleConfig",
  DigItemConfig = "DigItemConfig",
  DigLevelsConfig = "DigLevelsConfig"
}
ServerConfigKey = {
  RateUs = "rateUs",
  Notify = "notify",
  FileReplace = "fileReplace",
  GeneralConf = "general_conf",
  Bulletin = "bulletin",
  BulletinLanguage = "bulletinLanguage",
  CircleGift = "circleGift",
  EnergyGift = "energyGift",
  ChapterUpdate = "chapterUpdate",
  BundleController = "bundleController",
  RewardReplace = "reward_replace",
  FlambeTime = "flambeTime"
}
EGeneralConfType = {
  AntiMisTouch = "anti_mistouch",
  CGControl = "cg_control",
  IOSUpdateHint = "iosUpdateHint",
  BubbleBuyBonus = "bubble_buy_bonus",
  EnergyBoostTrigger = "doubleEnergy_trigger",
  EnergyBoostTriggerBonus = "doubleEnergy_trigger_bonus",
  EnergyBoostTriggerQuad = "quadEnergy",
  NotifyRemindTimes = "notify_remind_time",
  SocialBind = "social_bind",
  ButtonShowDay = "button_show_day",
  BalanceGold = "balance_gold",
  SkipPropNotEnough = "skipprop_not_enough",
  IngredientRecipeHint = "ingredient_recipe_hint",
  Cache = "cache",
  HotfixMinLevel = "hotfix_min_level",
  DataBalance = "data_balance",
  EquipmentRecipeHint = "equipment_recipe_hint",
  NewOrderRewardAnimation = "new_order_reward_anim",
  NewOrderRewardAnimationSkip = "new_order_reward_anim_skip",
  NewOrderRewardAnimationNoBack = "new_order_reward_anim_no_back",
  CachePinEqPdToTop = "cache_pin_eqpd_top",
  ItemAutoRecycle = "item_auto_recycle",
  OrderSequenceByEnergyDiff = "order_seq_energy_diff",
  ItemDelete = "item_delete_corn_husk",
  BoardCookBubble = "board_cook_bubble",
  OrderNewTag = "order_new_tag",
  SkipHintEffect = "skiphint_effect",
  FB = "facebook"
}
local DBKEY = "key"
local DBVALUE = "value"
TEST_NO_SUFFIX = "无后缀"
ConfigModel = {}
ConfigModel.__index = ConfigModel
EConfigParamType = {
  Int = "param_int",
  Float = "param_float",
  String = "param_string",
  IntArray = "param_int_array",
  FloatArray = "param_float_array",
  StringArray = "param_string_array",
  RewardArray = "param_reward",
  llETime = "eTime"
}

function ConfigModel:GetReplacesFilePath(path)
  local fileName = path
  local lastSepIndex = StringUtil.rFindChar(path, ".")
  if lastSepIndex ~= nil then
    fileName = string.sub(path, lastSepIndex + 1)
  end
  local suffix = self:GetFileReplaceConfigSuffix(fileName)
  if suffix and fileName then
    return path .. suffix
  end
  return path
end

function ConfigModel:GetFileReplaceConfigSuffix(key)
  if GM.TestModel then
    local mapTestFileReplaces = GM.TestModel:GetTestFileReplaces()
    local option = mapTestFileReplaces[key]
    if option == TEST_NO_SUFFIX then
      return nil
    elseif option then
      return "_" .. option
    end
  end
  local config = self:GetServerConfig(ServerConfigKey.FileReplace)
  if config then
    for i = 1, #config do
      if config[i].file == key then
        return config[i].suffix
      end
    end
  end
  return nil
end

function ConfigModel:LoadDynamicConfigAsync(path, callback)
  local replacedRequirePath = self:GetReplacesFilePath(path)
  local bReplaced = replacedRequirePath ~= path
  LuaManager:LoadDynamicConfigAsync(replacedRequirePath, function(tb)
    if GM == nil then
      return
    end
    if bReplaced and not tb then
      Debug.LogError("LoadDynamicConfigAsync failed:" .. (replacedRequirePath or "nil") .. "\n" .. debug.traceback())
      LuaManager:LoadDynamicConfigAsync(path, callback)
    else
      callback(tb)
    end
  end)
end

function ConfigModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.Config)
  self.m_serverConfigs = {}
  self.m_generalConfigs = {}
  local data = self.m_dbTable:GetValue(DBKEY, DBVALUE) or ""
  local cacheServerConfig = json.decode(data)
  if cacheServerConfig then
    self.m_serverConfigs = cacheServerConfig
    self:_UpdateGeneralConfigInfo()
  end
end

function ConfigModel:LoadConfigs()
  self.m_configs = {}
  local itemModelConfigs = require("Data.Config.ItemModelConfig")
  local extraBoardItemConfigs = require("Data.Config.ExtraBoardItemModelConfig")
  Table.ListAppend(itemModelConfigs, extraBoardItemConfigs)
  self.m_configs[LocalConfigKey.ItemModel] = itemModelConfigs
  self.m_configs[LocalConfigKey.BoardPrompt] = require("Data.Config.BoardPromptConfig")
  self.m_configs[LocalConfigKey.FunctionEnable] = require("Data.Config.FunctionEnableConfig")
  self.m_configs[LocalConfigKey.InventorySlot] = require("Data.Config.InventorySlotConfig")
  self.m_configs[LocalConfigKey.ItemProperty] = require("Data.Config.ItemPropertyConfig")
  self.m_configs[LocalConfigKey.ItemView] = require("Data.Config.ItemViewConfig")
  self.m_configs[LocalConfigKey.ShopDailyDeals] = require("Data.Config.ShopDailyDealsConfig")
  self.m_configs[LocalConfigKey.ShopFlashSale] = require("Data.Config.ShopFlashSaleConfig")
  self.m_configs[LocalConfigKey.ShopOrder] = require("Data.Config.ShopOrderConfig")
  self.m_configs[LocalConfigKey.ShopIAP] = require("Data.Config.ShopIAPConfig")
  self.m_configs[LocalConfigKey.System] = require("Data.Config.SystemConfig")
  self.m_configs[LocalConfigKey.NotificationText] = require("Data.Config.NotificationTextConfig")
  self.m_configs[LocalConfigKey.TaskGroup] = require("Data.Config.TaskGroupConfig")
  self.m_configs[LocalConfigKey.OrderAvatar] = require("Data.Config.OrderAvatarConfig")
  self.m_configs[LocalConfigKey.BubbleConfig] = require("Data.Config.BubbleConfig")
  self.m_configs[LocalConfigKey.DigItemConfig] = require("Data.Config.DigItemConfig")
  self.m_configs[LocalConfigKey.DigLevelsConfig] = require("Data.Config.DigLevelsConfig")
  for _, config in ipairs(self.m_configs[LocalConfigKey.BubbleConfig]) do
    RewardApi.CryptOneReward(config.Rewards)
  end
  LevelConfig = require("Data.Config.LevelConfig")
  self.m_loadConfigFinished = true
end

function ConfigModel:GetLocalConfig(key)
  if not self.m_loadConfigFinished then
    Log.Error("配置还未加载完成！")
    return nil
  end
  return self.m_configs[key]
end

function ConfigModel:GetServerConfig(key)
  local config = self.m_serverConfigs[key]
  return config and config.config
end

function ConfigModel:GetServerConfigMD5(key)
  local config = self.m_serverConfigs[key]
  return config and config.md5 or ""
end

function ConfigModel:UpdateServerConfigs(data)
  local bChanged = false
  local mapChangedKeys = {}
  data = json.decode(data) or {}
  for key, config in pairs(self.m_serverConfigs) do
    if data[key] == nil then
      self.m_serverConfigs[key] = nil
      mapChangedKeys[key] = true
      bChanged = true
    end
  end
  for key, config in pairs(data) do
    if self.m_serverConfigs[key] == nil or self.m_serverConfigs[key].md5 ~= config.md5 then
      self.m_serverConfigs[key] = config
      mapChangedKeys[key] = true
      bChanged = true
    end
  end
  if bChanged then
    self.m_dbTable:Set(DBKEY, DBVALUE, json.encode(self.m_serverConfigs))
    self:_UpdateGeneralConfigInfo()
  end
  local eGameMode = GM.SceneManager:GetGameMode()
  if mapChangedKeys[ServerConfigKey.FileReplace] and eGameMode ~= EGameMode.Loading then
    local info = json.encode(self:GetServerConfig(ServerConfigKey.FileReplace) or {})
    Log.Info("[Restart] file replace: config is " .. info)
    return true
  end
  return false
end

function ConfigModel:GetSeverConfigEntries()
  local entries = {}
  for _, type in pairs(ServerConfigKey) do
    table.insert(entries, {
      config_key = type,
      config_md5 = GM.ConfigModel:GetServerConfigMD5(type)
    })
  end
  for _, type in pairs(ActivityType) do
    table.insert(entries, {
      config_key = type,
      config_md5 = GM.ConfigModel:GetServerConfigMD5(type)
    })
  end
  return entries
end

function ConfigModel:GetAntiMisTouchDuration()
  local fParam = self:GetGeneralConfByType(EGeneralConfType.AntiMisTouch, EConfigParamType.Float)
  if not IsNil(fParam) then
    return fParam
  end
  return 0.5
end

function ConfigModel:GetCGPath()
  do return nil end
  local strParam = self:GetGeneralConfByType(EGeneralConfType.CGControl, EConfigParamType.String)
  if not StringUtil.IsNilOrEmpty(strParam) then
    return Application.streamingAssetsPath .. "/" .. strParam .. ".mp4"
  else
    return nil
  end
end

function ConfigModel:CanCGSkip()
  return self:IsServerControlOpen(EGeneralConfType.CGControl)
end

function ConfigModel:HasBubbleBuyBonus()
  return self:IsServerControlOpen(EGeneralConfType.BubbleBuyBonus)
end

function ConfigModel:IsReturnUserRewardOpen()
  local nParam = self:GetGeneralConfByType(EGeneralConfType.RecallOrder)
  if not IsNil(nParam) then
    return nParam == 1
  end
  return false
end

function ConfigModel:IsEnergyBoostDoubleTriggerConfigOn()
  local triggerCount = self:GetGeneralConfByType(EGeneralConfType.EnergyBoostTrigger, EConfigParamType.Int)
  return triggerCount and 0 < triggerCount, triggerCount
end

function ConfigModel:IsEnergyBoostQuadTriggerConfigOn()
  local triggerCount = self:GetGeneralConfByType(EGeneralConfType.EnergyBoostTriggerQuad, EConfigParamType.Int)
  return triggerCount and 0 < triggerCount, triggerCount
end

function ConfigModel:IsServerControlOpen(key)
  Log.Assert(key ~= nil, "EGeneralConfType is nil!")
  local nParam = self:GetGeneralConfByType(key)
  if not IsNil(nParam) then
    return nParam == 1
  end
  return false
end

function ConfigModel:_UpdateGeneralConfigInfo()
  self.m_generalConfigs = {}
  local serverConfig = self:GetServerConfig(ServerConfigKey.GeneralConf)
  if Table.IsEmpty(serverConfig) then
    return nil
  end
  for _, v in pairs(serverConfig) do
    self.m_generalConfigs[v.confType] = v
  end
end

function ConfigModel:GetGeneralConfByType(eGeneralConfType, eParam)
  if eParam == nil then
    eParam = EConfigParamType.Int
  end
  if self.m_generalConfigs[eGeneralConfType] ~= nil then
    return self.m_generalConfigs[eGeneralConfType][eParam]
  end
  return nil
end

function ConfigModel:IsVibrateOn()
  return PlayerPrefs.GetInt(EPlayerPrefKey.OpenVibrate, 1) == 1
end

function ConfigModel:SetVibrateOn(isOn)
  PlayerPrefs.SetInt(EPlayerPrefKey.OpenVibrate, isOn and 1 or 0)
  GM.BIManager:LogAction(EBIType.SetVibration, isOn and "open" or "close")
  if isOn then
    PlatformInterface.Vibrate(EVibrationType.Light)
  end
end

function ConfigModel:IsOrderSequenceByEnergyDiff()
  if self.m_bOrderSequenceByEnergyDiff == nil then
    self.m_bOrderSequenceByEnergyDiff = self:IsServerControlOpen(EGeneralConfType.OrderSequenceByEnergyDiff)
  end
  return self.m_bOrderSequenceByEnergyDiff
end

function ConfigModel:ButtonShowDay()
  if self.m_bButtonShowDay == nil then
    self.m_bButtonShowDay = self:IsServerControlOpen(EGeneralConfType.ButtonShowDay)
  end
  return self.m_bButtonShowDay
end

function ConfigModel:UseNewCacheLayout()
  if self.m_bUseNewCache == nil then
    self.m_bUseNewCache = self:IsServerControlOpen(EGeneralConfType.Cache)
  end
  return self.m_bUseNewCache
end

function ConfigModel:UseNewOrderRewardAnimation()
  if not self:UseNewCacheLayout() then
    return false
  end
  if self.m_bUseNewOrderRewardAnimation == nil then
    self.m_bUseNewOrderRewardAnimation = self:IsServerControlOpen(EGeneralConfType.NewOrderRewardAnimation)
  end
  return self.m_bUseNewOrderRewardAnimation
end

function ConfigModel:CanNewOrderRewardAnimationSkip()
  if not self:UseNewOrderRewardAnimation() then
    return false
  end
  if self.m_bCanNewOrderRewardAnimationSkip == nil then
    self.m_bCanNewOrderRewardAnimationSkip = self:IsServerControlOpen(EGeneralConfType.NewOrderRewardAnimationSkip)
  end
  return self.m_bCanNewOrderRewardAnimationSkip
end

function ConfigModel:CanNewOrderRewardAnimationBack()
  if not self:UseNewOrderRewardAnimation() then
    return false
  end
  if self.m_bCanNewOrderRewardAnimationBack == nil then
    self.m_bCanNewOrderRewardAnimationBack = not self:IsServerControlOpen(EGeneralConfType.NewOrderRewardAnimationNoBack)
  end
  return self.m_bCanNewOrderRewardAnimationBack
end

function ConfigModel:CachePinEqPdToTop()
  if self.m_bCachePinEqPdToTop == nil then
    self.m_bCachePinEqPdToTop = self:IsServerControlOpen(EGeneralConfType.CachePinEqPdToTop)
  end
  return self.m_bCachePinEqPdToTop
end

function ConfigModel:UseItemAutoRecycle()
  if self.m_bUseItemAutoRecycle == nil then
    local arrConfig = self:GetGeneralConfByType(EGeneralConfType.ItemAutoRecycle, EConfigParamType.IntArray) or {}
    local divisor = arrConfig[1]
    local nMinEnergyCount = arrConfig[2]
    if not (divisor and nMinEnergyCount) or divisor == 0 then
      Log.Assert(divisor ~= 0, "棋子回收除数不能配置为0")
      self.m_bUseItemAutoRecycle = false
    else
      self.m_bUseItemAutoRecycle = true
      self.m_nItemAutoRecycleDivisor = divisor or 3
      self.m_nItemAutoRecycleMinEnergyCount = nMinEnergyCount or 10
    end
  end
  return self.m_bUseItemAutoRecycle, self.m_nItemAutoRecycleDivisor, self.m_nItemAutoRecycleMinEnergyCount
end

function ConfigModel:UseBoardCookBubble()
  if self.m_bUseBoardCookBubble == nil then
    self.m_bUseBoardCookBubble = self:IsServerControlOpen(EGeneralConfType.BoardCookBubble)
  end
  return self.m_bUseBoardCookBubble
end

function ConfigModel:GetRewardReplaceConfig()
  if self.m_rewardReplaceMD5 == self:GetServerConfigMD5(ServerConfigKey.RewardReplace) then
    return self.m_mapReplacedRewards
  end
  self.m_mapReplacedRewards = {}
  local arrConfig = self:GetServerConfig(ServerConfigKey.RewardReplace) or {}
  for _, config in ipairs(arrConfig) do
    if Table.IsEmpty(config.rewards) or Table.IsEmpty(config.replace) or #config.rewards ~= 1 or #config.replace ~= 1 then
      Log.Error("[reward_replace] rewards和replace的奖励种类必须为1")
    elseif config.rewards[1][PROPERTY_COUNT] ~= 1 then
      Log.Error("[reward_replace] rewards被替换的数量必须为1")
    end
    self.m_mapReplacedRewards[config.rewards[1][PROPERTY_TYPE]] = config.replace[1]
  end
  return self.m_mapReplacedRewards
end
