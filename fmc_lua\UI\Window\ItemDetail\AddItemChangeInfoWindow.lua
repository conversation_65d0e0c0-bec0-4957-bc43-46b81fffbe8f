local itemId = "additem_1"
AddItemChangeInfoWindow = setmetatable({}, BaseWindow)
AddItemChangeInfoWindow.__index = AddItemChangeInfoWindow

function AddItemChangeInfoWindow:Init()
  local chainId = GM.ItemDataModel:GetChainId(itemId)
  local chain = GM.ItemDataModel:GetChain(chainId)
  local count = #chain
  for index, type in ipairs(chain) do
    self:_AddCell(type, false, index < count, true, false, self.m_groupRect)
  end
end

function AddItemChangeInfoWindow:_AddCell(itemType, isProduceCell, hasArrow, forceShow, hasTipButton, parent)
  local cellObj = Object.Instantiate(self.m_itemOriginGo, parent)
  cellObj:SetActive(true)
  local cell = cellObj:GetLuaTable()
  cell:Init(itemType, isProduceCell, hasArrow, forceShow, hasTip<PERSON><PERSON>on, self)
end
