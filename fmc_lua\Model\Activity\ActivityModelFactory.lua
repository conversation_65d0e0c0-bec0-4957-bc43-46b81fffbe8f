VirtualDBTableName = {
  BakeOut = "bakeOut",
  ExtraBoard1 = "extraBoard1",
  ExtraBoard1Item = "extraBoard1Item",
  ExtraBoard1ItemLayer = "extraBoard1ItemLayer",
  ExtraBoard1ItemCache = "extraBoard1ItemCache",
  ExtraBoard2 = "extraBoard2",
  ExtraBoard2Item = "extraBoard2Item",
  ExtraBoard2ItemLayer = "extraBoard2ItemLayer",
  ExtraBoard2ItemCache = "extraBoard2ItemCache",
  ExtraBoard3 = "extraBoard3",
  ExtraBoard3Item = "extraBoard3Item",
  ExtraBoard3ItemLayer = "extraBoard3ItemLayer",
  ExtraBoard3ItemCache = "extraBoard3ItemCache",
  ExtraBoard4 = "extraBoard4",
  ExtraBoard4Item = "extraBoard4Item",
  ExtraBoard4ItemLayer = "extraBoard4ItemLayer",
  ExtraBoard4ItemCache = "extraBoard4ItemCache",
  ExtraBoard5 = "extraBoard5",
  ExtraBoard5Item = "extraBoard5Item",
  ExtraBoard5ItemLayer = "extraBoard5ItemLayer",
  ExtraBoard5ItemCache = "extraBoard5ItemCache",
  ExtraBoard6 = "extraBoard6",
  ExtraBoard6Item = "extraBoard6Item",
  ExtraBoard6ItemLayer = "extraBoard6ItemLayer",
  ExtraBoard6ItemCache = "extraBoard6ItemCache",
  ExtraBoard7 = "extraBoard7",
  ExtraBoard7Item = "extraBoard7Item",
  ExtraBoard7ItemLayer = "extraBoard7ItemLayer",
  ExtraBoard7ItemCache = "extraBoard7ItemCache",
  ProgressActivity1 = "prog_1",
  BP1 = "BP1",
  BP2 = "BP2",
  BP3 = "BP3",
  BP4 = "BP4",
  BP5 = "BP5",
  BP6 = "BP6",
  BP7 = "BP7",
  BP8 = "BP8",
  CoinRace = "coinRace",
  TreasureDig = "treasureDig",
  BlindChest1 = "BlindChest1",
  SurpriseChest = "surpriseChest",
  PkRace = "pkRace",
  Album1 = "album1"
}
local ActivityModelInfo = {
  [ActivityType.BakeOut] = {
    modelName = "BakeOutModel",
    dbName = VirtualDBTableName.BakeOut
  }
}
local ActivityModelInfoWithSeries, ActivityModelInfoWithBoard
ActivityModelFactory = {}

function ActivityModelFactory._InitModelInfo()
  ActivityModelInfoWithSeries = {
    {
      def = CoinRaceActivityDefinition,
      baseTable = CoinRaceModel
    },
    {
      def = PkRaceDefinition,
      baseTable = PkRaceModel
    },
    {
      def = PassActivityDefinition,
      baseTable = PassActivityModel
    },
    {
      def = DigActivityDefinition,
      baseTable = DigActivityModel
    },
    {
      def = ProgressActivityDefinition,
      baseTable = ProgressActivityModel
    },
    {
      def = SurpriseChestActivityDefinition,
      baseTable = SurpriseChestActivityModel
    },
    {
      def = BlindChestDefinition,
      baseTable = BlindChestModel
    },
    {
      def = AlbumActivityDefinition,
      baseTable = AlbumActivityModel
    }
  }
  ActivityModelInfoWithBoard = {
    {
      def = ExtraBoardActivityDefinition,
      baseTable = ExtraBoardActivityModel
    }
  }
end

function ActivityModelFactory.AddModel(activityType)
  if ActivityModelInfoWithSeries == nil or ActivityModelInfoWithBoard == nil then
    ActivityModelFactory._InitModelInfo()
  end
  for _, info in pairs(ActivityModelInfoWithSeries) do
    Log.Assert(info.def, "ActivityModelInfoWithSeries. def should not be nil")
    Log.Assert(info.baseTable, "ActivityModelInfoWithSeries. baseTable should not be nil")
    if info.def[activityType] ~= nil then
      ActivityModelFactory._AddActivityModelWithSeries(activityType, info)
      return
    end
  end
  for _, info in pairs(ActivityModelInfoWithBoard) do
    Log.Assert(info.def, "ActivityModelInfoWithBoard. def should not be nil")
    Log.Assert(info.baseTable, "ActivityModelInfoWithBoard. baseTable should not be nil")
    if info.def[activityType] ~= nil then
      ActivityModelFactory._AddActivityModelWithBoard(activityType, info)
      return
    end
  end
  if SpreeActivityDefinition[activityType] ~= nil then
    ActivityModelFactory._AddSpreeActivityModel(activityType)
    return
  end
  if ActivityModelInfo[activityType] then
    local type = _ENV[ActivityModelInfo[activityType].modelName]
    local model = setmetatable({}, type)
    GM.ActivityManager:AddModel(activityType, model)
    local dbTable = GM.ActivityManager:GetGeneralData()
    local virtualDBTable = VirtualDBTable.Create(dbTable, ActivityModelInfo[activityType].dbName)
    model:Init(virtualDBTable)
    return
  end
end

function ActivityModelFactory._AddSpreeActivityModel(activityType)
  local spreeActivityModel = setmetatable({}, SpreeActivityModel)
  GM.ActivityManager:AddModel(activityType, spreeActivityModel)
  local activityDefinition = SpreeActivityDefinition[activityType]
  local dbTable = GM.ActivityManager:GetEventActivityData()
  local activityDataTable = VirtualDBTable.Create(dbTable, activityDefinition.ActivityDataTableName)
  local itemDataTable = VirtualDBTable.Create(dbTable, activityDefinition.ItemDataTableName)
  local itemLayerDataTable = VirtualDBTable.Create(dbTable, activityDefinition.ItemLayerDataTableName)
  local itemCacheDataTable = VirtualDBTable.Create(dbTable, activityDefinition.ItemCacheDataTableName)
  local orderMetaDataTable = VirtualDBTable.Create(dbTable, activityDefinition.OrderMetaDataTableName)
  local orderDataTable = VirtualDBTable.Create(dbTable, activityDefinition.OrderDataTableName)
  local shopDataTable = VirtualDBTable.Create(dbTable, activityDefinition.ShopDataTableName)
  spreeActivityModel:Init(activityType, activityDataTable, itemDataTable, itemLayerDataTable, itemCacheDataTable, orderMetaDataTable, orderDataTable, shopDataTable)
end

function ActivityModelFactory._AddActivityModelWithSeries(activityType, info)
  local model = setmetatable({}, info.baseTable)
  GM.ActivityManager:AddModel(activityType, model)
  local activityDefinition = info.def[activityType]
  local dbTable = GM.ActivityManager:GetGeneralData()
  local virtualDBTable = VirtualDBTable.Create(dbTable, activityDefinition.ActivityDataTableName)
  model:Init(activityType, virtualDBTable)
end

function ActivityModelFactory._AddActivityModelWithBoard(activityType, info)
  local model = setmetatable({}, info.baseTable)
  GM.ActivityManager:AddModel(activityType, model)
  local activityDefinition = info.def[activityType]
  local dbTable = GM.ActivityManager:GetGeneralData()
  local activityDataTable = VirtualDBTable.Create(dbTable, activityDefinition.ActivityDataTableName)
  local itemDataTable = VirtualDBTable.Create(dbTable, activityDefinition.ItemDataTableName)
  local itemLayerDataTable = VirtualDBTable.Create(dbTable, activityDefinition.ItemLayerDataTableName)
  local itemCacheDataTable = VirtualDBTable.Create(dbTable, activityDefinition.ItemCacheDataTableName)
  model:Init(activityType, activityDataTable, itemDataTable, itemLayerDataTable, itemCacheDataTable)
end
