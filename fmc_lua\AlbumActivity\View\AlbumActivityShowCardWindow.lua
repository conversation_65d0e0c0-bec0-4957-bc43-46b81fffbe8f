AlbumActivityShowCardWindow = setmetatable({showWindowMask = false, canCloseByAndroidBack = false}, AlbumActivityBaseWindow)
AlbumActivityShowCardWindow.__index = AlbumActivityShowCardWindow

function AlbumActivityShowCardWindow:BeforeOpenCheck(activityType, cardTrans, red, callBack, noGet)
  return not cardTrans.gameObject:IsNull()
end

function AlbumActivityShowCardWindow:Init(activityType, cardTrans, red, callBack, noGet)
  AlbumActivityBaseWindow.Init(self, activityType, true)
  self.m_bred = red
  self.cardTrans = cardTrans
  self.m_bNoGet = noGet
  local cardCanvas = self.cardTrans.gameObject:GetComponent(typeof(CS.UnityEngine.Canvas))
  if cardCanvas ~= nil then
    self.m_cardCanvas = cardCanvas
    self.m_cardCanvas.overrideSorting = true
    self.m_cardCanvas.sortingOrder = self:GetSortingOrder() + 1
  end
  self.m_Callback = callBack
  self:InitUI()
end

function AlbumActivityShowCardWindow:InitUI()
  UIUtil.SetActive(self.m_TextRectTrans.gameObject, self.m_bNoGet)
  local sequence = DOTween.Sequence()
  sequence:Append(0, self.m_MaskImg:DOFade(0.8, 0.5))
end

function AlbumActivityShowCardWindow:Close()
  if self.m_Callback then
    self.m_Callback()
  end
  AlbumActivityBaseWindow.Close(self)
end
