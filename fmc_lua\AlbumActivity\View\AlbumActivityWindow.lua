AlbumActivityBaseWindow = setmetatable({
  bCloseByStateChanged = true,
  disableEffectWhenCloseView = true,
  bAutoBI = true
}, BaseWindow)
AlbumActivityBaseWindow.__index = AlbumActivityBaseWindow

function AlbumActivityBaseWindow:Init(activityType, bUserClick)
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_activityType = activityType
  self.m_activityDefinition = AlbumActivityDefinition[activityType]
  if self.bCloseByStateChanged then
    EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self.TryCloseByStateChanged)
  end
  if self.bAutoBI then
    self:LogWindowAction(EBIType.UIActionType.Open, {
      bUserClick and EBIReferType.UserClick or EBIReferType.AutoPopup
    })
  end
end

function AlbumActivityBaseWindow:TryCloseByStateChanged()
  if StringUtil.IsNilOrEmpty(self.m_model:GetNewCardId()) then
    self:Close()
  end
end
