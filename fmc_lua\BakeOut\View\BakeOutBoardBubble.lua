BakeOutBoardBubble = {}
BakeOutBoardBubble.__index = BakeOutBoardBubble

function BakeOutBoardBubble:Awake()
  self.m_bAwaked = true
  if self.m_bWaitAddListener then
    self:_AddListeners()
  end
end

function BakeOutBoardBubble:Init(model, orderArea)
  self.m_model = model
  self.m_orderArea = orderArea
  if self.m_model == nil then
    return
  end
  self.m_iconArea:Init(EPropertyType.BakeOutToken, self)
  self.m_iconArea:SetInBoardView()
  if self.m_bAwaked then
    self:_AddListeners()
  else
    self.m_bWaitAddListener = true
  end
  self:UpdateContent()
end

function BakeOutBoardBubble:_AddListeners()
  EventDispatcher.AddListener(EEventType.BakeOutCoinExchanged, self, self.UpdateContent)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnChangeGameMode)
  AddHandlerAndRecordMap(self.m_model.event, BakeOutEventType.StateChanged, {
    obj = self,
    method = self.UpdateContent
  })
  AddHandlerAndRecordMap(self.m_model.event, BakeOutEventType.UpdateRank, {
    obj = self,
    method = self.UpdateRank
  })
  AddHandlerAndRecordMap(self.m_model.event, BakeOutEventType.ModeChanged, {
    obj = self,
    method = self.UpdateContent
  })
end

function BakeOutBoardBubble:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
end

function BakeOutBoardBubble:OnChangeGameMode()
  if GM.SceneManager:GetGameMode() == EGameMode.Board then
    self:UpdateContent()
  end
end

function BakeOutBoardBubble:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    if self.m_model:GetState() == ActivityState.Ended then
      self.m_countDownText.text = GM.GameTextModel:GetText("bakeout_settlement_title")
    else
      local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
      self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
    end
  else
    self.m_countDownText.text = GM.GameTextModel:GetText("bakeout_end_title")
  end
end

function BakeOutBoardBubble:UpdateContent()
  if self.m_model == nil then
    return
  end
  local bakeoutOn = self.m_model:IsBakeOutModeOn()
  if bakeoutOn ~= self.gameObject.activeSelf then
    self.gameObject:SetActive(bakeoutOn)
  end
  if not bakeoutOn then
    return
  end
  self:UpdateRank()
  self.m_tokenText.text = self.m_model:GetToken() or "0"
  self:UpdatePerSecond()
end

function BakeOutBoardBubble:UpdateRank(animated)
  if self.m_updateRankTween then
    self.m_updateRankTween:Kill()
    self.m_updateRankTween = nil
  end
  local rank = self.m_model:GetDisplayCurRank()
  UIUtil.SetActive(self.m_rankGo, rank ~= nil)
  if animated and tonumber(self.m_rankText.text) ~= rank then
    self.m_updateRankTween = self:_CreateRankChangeTween(rank)
  else
    self.m_rankText.text = rank or ""
  end
end

function BakeOutBoardBubble:_CreateRankChangeTween(toRank)
  return DOTween.To(function()
    return tonumber(self.m_rankText.text)
  end, function(rank)
    self.m_rankText.text = math.floor(rank)
  end, toRank, 1):OnComplete(function()
    self.m_updateRankTween = nil
  end):SetEase(Ease.InOutSine)
end

function BakeOutBoardBubble:PlayAcquireAnimation()
  self:UpdateContent()
end

function BakeOutBoardBubble:OnBtnClicked()
  if self.m_model == nil then
    return
  end
  if self.m_model:GetState() == ActivityState.Started then
    if self.m_model:IsEnlist() then
      GM.UIManager:OpenView(UIPrefabConfigName.BakeOutMainWindow, nil, true)
    else
      self.m_model:OnEnlist()
    end
  elseif self.m_model:GetState() == ActivityState.Ended then
    GM.UIManager:OpenView(UIPrefabConfigName.BakeOutInSettlementWindow, true)
  elseif self.m_model:GetState() == ActivityState.Released then
    if self.m_model:GetFinalRankInfos() ~= nil then
      GM.UIManager:OpenView(UIPrefabConfigName.BakeOutResultWindow, true)
    else
      self.m_model:SetWindowReopen()
      self.m_model:OnActivityFinished(true)
    end
  end
end

function BakeOutBoardBubble:GetStarRectTransform()
  return self.m_starRectTrans
end

function BakeOutBoardBubble:GetIconArea()
  return self.m_iconArea
end

function BakeOutBoardBubble:GetTouchContentTransform()
  return self.m_touchContentRectTrans
end

BakeOutHudButton = setmetatable({}, HudPropertyButton)
BakeOutHudButton.__index = BakeOutHudButton

function BakeOutHudButton:Init(ePropertyType, boardBubble)
  HudPropertyButton.Init(self, ePropertyType)
  self.m_boardBubble = boardBubble
end

function BakeOutHudButton:GetPropertyNum()
  local model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  if model ~= nil then
    return model:GetToken()
  end
end

function BakeOutHudButton:SyncToModelValue()
  HudPropertyButton.SyncToModelValue(self)
  if self.m_boardBubble ~= nil then
    self.m_boardBubble:UpdateContent()
  end
end
