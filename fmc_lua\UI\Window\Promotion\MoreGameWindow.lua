MoreGameWindow = setmetatable({}, BaseWindow)
MoreGameWindow.__index = MoreGameWindow

function MoreGameWindow:Init()
  local arrGames = GM.MoreGameModel:GetGames()
  for index, game in ipairs(arrGames) do
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.MoreGameCell), self.m_scrollRect.content, Vector3.zero, function(go)
      local tbCell = go:GetLuaTable()
      tbCell:Init(game)
    end)
  end
  GM.MoreGameModel:SetMGWinOpended()
  EventDispatcher.DispatchEvent(EEventType.RefreshSettingStrongTip)
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.UserClick
  })
end
