ItemSplitEventType = {StateChanged = 1}
ItemSplit = setmetatable({}, BaseItemComponent)
ItemSplit.__index = ItemSplit
local DEFAULT_COUNT = 1

function ItemSplit.Create()
  local itemSplit = setmetatable({}, ItemSplit)
  itemSplit:Init()
  return itemSplit
end

function ItemSplit:Init()
  self.event = PairEvent.Create(self)
  self.m_count = DEFAULT_COUNT
end

function ItemSplit:FromSerialization(dbTable)
  self.m_count = dbTable.boostCount
end

function ItemSplit:ToSerialization(dbTable)
  dbTable.boostCount = self.m_count
end

function ItemSplit:_CostSplitUseCount()
  self.m_count = self.m_count - 1
  self.m_itemModel:GetBoardModel():SaveItemProperty(self.m_itemModel)
  self.event:Call(ItemSplitEventType.StateChanged)
end

function ItemSplit:HasLeftSplitUseCount()
  return self.m_count > 0
end

function ItemSplit:SetSplitUseCount(count)
  self.m_count = count
  self.m_itemModel:GetBoardModel():SaveItemProperty(self.m_itemModel)
  self.event:Call(ItemSplitEventType.StateChanged)
end

function ItemSplit:GetSplitUseCount()
  return self.m_count
end

function ItemSplit:CanTakeEffect(targetItem)
  if targetItem:GetComponent(ItemSplit) ~= nil then
    return true
  end
  local boardModel = self.m_itemModel:GetBoardModel()
  if boardModel:IsBoardFull() then
    return false
  end
  local config = GM.ItemDataModel:GetModelConfig(targetItem:GetType())
  local canLevelDown = config ~= nil and config.LevelDown ~= nil and config.LevelDown == 1
  if not canLevelDown then
    return false
  end
  local cookCmp = targetItem:GetComponent(ItemCook)
  return cookCmp == nil or cookCmp:GetState() == EItemCookState.Empty
end

function ItemSplit:SplitItem(targetItem, newItemCode)
  local boardModel = self.m_itemModel:GetBoardModel()
  local targetPosition = targetItem:GetPosition()
  local newPosition = boardModel:FindEmptyPositionInSpreadOrder(targetPosition)
  if newPosition == nil then
    boardModel.event:Call(BoardEventType.SpreadFailed, {
      Item = self.m_itemModel,
      Reason = SpreadFailedReason.BoardFull
    })
    return false
  end
  self:_CostSplitUseCount()
  if not self:HasLeftSplitUseCount() then
    boardModel:RemoveItem(self.m_itemModel)
  end
  boardModel:RemoveItem(targetItem)
  local cost = ItemModelHelper.SplitCost(targetItem, 2)
  local newItem1 = boardModel:GenerateItem(targetPosition, newItemCode, cost)
  local newItem2 = boardModel:GenerateItem(newPosition, newItemCode, cost)
  local message = {
    Split = self.m_itemModel,
    Target = targetItem,
    New1 = newItem1,
    New2 = newItem2
  }
  boardModel.event:Call(BoardEventType.SplitItem, message)
  GM.BIManager:LogSplit(targetItem:GetType(), newItem1:GetType(), {scissor = 1})
  return true
end

function ItemSplit:MergeItem(targetItem)
  local boardModel = self.m_itemModel:GetBoardModel()
  local targetPosition = targetItem:GetPosition()
  local targetItemSplit = targetItem:GetComponent(ItemSplit)
  boardModel:RemoveItem(self.m_itemModel)
  boardModel:RemoveItem(targetItem)
  local newItem = boardModel:GenerateItem(targetPosition, targetItem:GetType())
  local newItemSplit = newItem:GetComponent(ItemSplit)
  local scissorCount = targetItemSplit:GetSplitUseCount() + self:GetSplitUseCount()
  newItemSplit:SetSplitUseCount(scissorCount)
  local mergeMessage = {
    Source = self.m_itemModel,
    Target = targetItem,
    New = newItem
  }
  boardModel.event:Call(BoardEventType.MergeItem, mergeMessage)
  EventDispatcher.DispatchEvent(EEventType.ItemMerged, mergeMessage)
  GM.BIManager:LogMerge(self.m_itemModel:GetType(), newItem:GetCode(), {scissor = scissorCount})
  return true
end
