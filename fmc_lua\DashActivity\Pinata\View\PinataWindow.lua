local Level2SpinePrefabName = {}
local GetPinataSpinePrefabName = function(level)
  if level == nil then
    return nil
  end
  return Level2SpinePrefabName[level]
end
PinataMainWindow = setmetatable({}, DashActivityBaseWindow)
PinataMainWindow.__index = PinataMainWindow

function PinataMainWindow:Init(activityType, afterGetRewards)
  DashActivityBaseWindow.Init(self, activityType, false)
  self.activityDefinition = DashActivityDefinition[activityType]
  if afterGetRewards then
    self.m_avatar:Init(self.activityDefinition.MainWindowAvatarAnimationHappyName)
  else
    local currentLevel = self.m_model:GetLevel()
    local currentScore = self.m_model:GetScore()
    local currentNeedScore = self.m_model:GetLevelConfigs()[currentLevel].score
    if currentScore < currentNeedScore * 0.6 then
      self.m_avatar:Init(self.activityDefinition.MainWindowAvatarAnimationWaitName)
    else
      self.m_avatar:Init(self.activityDefinition.MainWindowAvatarAnimationHappyName)
    end
  end
  self:_LoadPinata()
  self.m_rewardProgress:Init()
  self.m_scoreSlider:Init(activityType)
  self.m_scoreSlider:UpdateContent()
  self.m_nextStateTime = self.m_model:GetNextStateTime()
  self:UpdatePerSecond()
end

function PinataMainWindow:_LoadPinata()
  local currentLevel = self.m_model:GetLevel()
  local pinataPrefabName = GetPinataSpinePrefabName(currentLevel)
  if pinataPrefabName == nil then
    return
  end
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(pinataPrefabName), self.m_pinataSpineParent, Vector3.zero, function(go)
    if not go:IsNull() then
      local pinataAnimation = go:GetLuaTable()
      pinataAnimation:PlayIdleAnimation()
    end
  end)
end

function PinataMainWindow:UpdatePerSecond()
  if self.m_nextStateTime ~= nil then
    local delta = math.max(0, self.m_nextStateTime - GM.GameModel:GetServerTime())
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function PinataMainWindow:OnBtnClicked()
  self:Close()
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    GM.SceneManager:ChangeGameMode(EGameMode.Board)
  end
end

function PinataMainWindow:GetButtonTransform()
  return self.m_buttonRectTrans
end

PinataFailWindow = setmetatable({}, DashActivityFailWindow)
PinataFailWindow.__index = PinataFailWindow

function PinataFailWindow:Init(activityType)
  DashActivityFailWindow.Init(self, activityType)
  self:_LoadPinata()
end

function PinataFailWindow:_LoadPinata()
  local currentLevel = self.m_model:GetLevel()
  local pinataPrefabName = GetPinataSpinePrefabName(currentLevel)
  if pinataPrefabName == nil then
    return
  end
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(pinataPrefabName), self.m_pinataSpineParent, Vector3.zero, function(go)
    if not go:IsNull() then
      local pinataAnimation = go:GetLuaTable()
      pinataAnimation:PlayIdleAnimation()
    end
  end)
end

PinataRewardRecoverWindow = setmetatable({}, DashActivityRewardRecoverWindow)
PinataRewardRecoverWindow.__index = PinataRewardRecoverWindow

function PinataRewardRecoverWindow:_GetRewards()
  DashActivityRewardRecoverWindow._GetRewards(self)
  local level = self.m_model:GetLevel()
  local levelConfig = self.m_model:GetLevelConfigs()[level]
  if levelConfig == nil then
    local extraRewards = self.m_model:GetExtraReward()
    if extraRewards ~= nil then
      Table.ListAppend(self.m_rewards, extraRewards)
    end
  end
end

PinataRewardWindow = setmetatable({canCloseByAndroidBack = false, showWindowMask = false}, BaseWindow)
PinataRewardWindow.__index = PinataRewardWindow
local RewardWindowStep = {
  PinataDrop = 1,
  NormalReward = 2,
  ExtraReward = 3,
  Closing = 4
}

function PinataRewardWindow:BeforeOpenCheck()
  local pinataModel = GM.ActivityManager:GetModel(ActivityType.Pinata)
  return pinataModel:GetState() == ActivityState.Started
end

function PinataRewardWindow:Init()
  self.m_model = GM.ActivityManager:GetModel(ActivityType.Pinata)
  local level = self.m_model:GetMilestoneLevel()
  local levelConfig = self.m_model:GetLevelConfigs()[level]
  if levelConfig == nil then
    self:Close()
    return
  end
  UIUtil.SetActive(self.m_maskButtonGo, false)
  UIUtil.SetActive(self.m_normalContentGo, false)
  UIUtil.SetActive(self.m_extraContentGo, false)
  self:_SetEventLock(1.5)
  self.m_lineGroupAnimator:SetTrigger("Show")
  self.m_breakButtonGo:SetActive(true)
  self.m_buttonCanvasGroup.alpha = 0
  local sequence = DOTween.Sequence()
  sequence:InsertCallback(0.4, function()
    self:_LoadPinata()
  end)
  sequence:Insert(1.2, self.m_buttonCanvasGroup:DOFade(1, 0.3))
  sequence:OnComplete(function()
    if not self.gameObject:IsNull() then
      self.m_sequence = nil
    end
  end)
  self.m_sequence = sequence
  self.m_step = RewardWindowStep.PinataDrop
  AddHandlerAndRecordMap(self.m_model.event, DashActivityEventType.StateChanged, {
    obj = self,
    method = self.OnStateChanged
  })
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.UserClick
  })
end

function PinataRewardWindow:OnStateChanged()
  if self.m_normalRewards ~= nil then
    RewardApi.AcquireRewardsInView(self.m_normalRewards, {simpleCollect = true, alwaysInMiddle = true})
    if self.m_model:GetState() == ActivityState.Ended then
      local level = self.m_model:GetMilestoneLevel()
      local levelConfig = self.m_model:GetLevelConfigs()[level]
      if levelConfig == nil then
        GM.UIManager:OpenView(DashActivityDefinition[ActivityType.Pinata].SuccessWindowPrefabName, ActivityType.Pinata)
      end
    end
  end
  if self.m_extraRewards ~= nil then
    RewardApi.AcquireRewardsInView(self.m_extraRewards, {simpleCollect = true, alwaysInMiddle = true})
  end
  self:Close()
end

function PinataRewardWindow:_SetEventLock(duration)
  GM.UIManager:SetEventLock(true, self)
  DelayExecuteFuncInView(function()
    GM.UIManager:SetEventLock(false, self)
  end, duration, self)
end

function PinataRewardWindow:OnDestroy()
  GM.UIManager:RemoveAllEventLocks(self)
  if self.m_sequence then
    self.m_sequence:Kill()
    self.m_sequence = nil
  end
end

function PinataRewardWindow:_LoadPinata()
  local currentLevel = self.m_model:GetLevel()
  local pinataPrefabName = GetPinataSpinePrefabName(currentLevel)
  if pinataPrefabName == nil then
    return
  end
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(pinataPrefabName), self.m_spineRootRectTrans, Vector3.zero, function(go)
    if not go:IsNull() then
      local pinataAnimation = go:GetLuaTable()
      pinataAnimation:PlayDropAnimation()
      self.m_pinata = pinataAnimation
    end
  end)
end

function PinataRewardWindow:OnBreakBtnClicked()
  if self.m_step ~= RewardWindowStep.PinataDrop then
    return
  end
  local level = self.m_model:GetMilestoneLevel()
  local levelConfig = self.m_model:GetLevelConfigs()[level]
  self.m_normalRewardContent:Init(levelConfig.rewards)
  self.m_normalRewards = levelConfig.rewards
  self.m_model:Upgrade()
  level = self.m_model:GetMilestoneLevel()
  levelConfig = self.m_model:GetLevelConfigs()[level]
  self.m_bHasExtraReward = false
  if levelConfig == nil then
    local extraRewards = self.m_model:GetExtraReward()
    if extraRewards ~= nil then
      self.m_bHasExtraReward = true
      self.m_extraRewardContent:Init(extraRewards)
      self.m_extraRewards = extraRewards
    end
  end
  self:_PlayNormalRewardAnimation()
end

function PinataRewardWindow:OnMaskClicked()
  if self.m_step == RewardWindowStep.NormalReward then
    if self.m_bHasExtraReward then
      if self.m_normalRewards ~= nil then
        RewardApi.AcquireRewardsInView(self.m_normalRewards, {simpleCollect = true, alwaysInMiddle = true})
        self.m_normalRewards = nil
      end
      self:_PlayExtraRewardAnimation()
    else
      self:_PlayLeaveAnimation()
    end
  elseif self.m_step ~= RewardWindowStep.Closing then
    self:Close()
  end
end

function PinataRewardWindow:OnExtraBtnClicked()
  if self.m_step == RewardWindowStep.ExtraReward then
    UIUtil.SetActive(self.m_extraButtonGo, false)
    self:_PlayLeaveAnimation()
  elseif self.m_step ~= RewardWindowStep.Closing then
    self:Close()
  end
end

function PinataRewardWindow:_PlayNormalRewardAnimation()
  self.m_step = RewardWindowStep.NormalReward
  UIUtil.SetActive(self.m_breakButtonGo, false)
  self:_SetEventLock(0.9)
  if self.m_pinata ~= nil then
    self.m_pinata:PlayBreakAnimation()
  end
  DelayExecuteFuncInView(function()
    UIUtil.SetActive(self.m_normalContentGo, true)
    UIUtil.SetActive(self.m_maskButtonGo, true)
    self.m_normalContentAnimator:SetTrigger("Show")
  end, 0.6, self)
end

function PinataRewardWindow:_PlayExtraRewardAnimation()
  self.m_step = RewardWindowStep.ExtraReward
  self:_SetEventLock(0.6)
  self.m_normalContentAnimator:SetTrigger("Hide")
  DelayExecuteFuncInView(function()
    UIUtil.SetActive(self.m_normalContentGo, false)
    UIUtil.SetActive(self.m_extraContentGo, true)
    UIUtil.SetActive(self.m_extraButtonGo, true)
    self.m_extraContentAnimator:SetTrigger("Show")
  end, 0.4, self)
end

function PinataRewardWindow:_PlayLeaveAnimation()
  self.m_step = RewardWindowStep.Closing
  if self.m_bHasExtraReward then
    self.m_extraRewardContent:PlayRewardAnimation()
    UIUtil.SetActive(self.m_extraContentGo, false)
  else
    self.m_normalRewardContent:PlayRewardAnimation()
    UIUtil.SetActive(self.m_normalContentGo, false)
  end
  self.m_lineGroupAnimator:SetTrigger("Hide")
  local sequence = DOTween.Sequence()
  local levelConfig
  if self.m_model:GetState() == ActivityState.Started then
    local level = self.m_model:GetMilestoneLevel()
    levelConfig = self.m_model:GetLevelConfigs()[level]
  end
  local openViewInterval = levelConfig == nil and 1.2 or 3
  self:_SetEventLock(levelConfig == nil and 1.2 or 3)
  if levelConfig ~= nil then
    sequence:InsertCallback(1.5, function()
      EventDispatcher.DispatchEvent(EEventType.PinataUpdateOrderCell)
    end)
  end
  sequence:InsertCallback(openViewInterval, function()
    if self.m_model:GetState() == ActivityState.Started then
      local level = self.m_model:GetMilestoneLevel()
      local levelConfig = self.m_model:GetLevelConfigs()[level]
      if levelConfig == nil then
        GM.UIManager:OpenView(DashActivityDefinition[ActivityType.Pinata].SuccessWindowPrefabName, ActivityType.Pinata)
      else
        GM.UIManager:OpenView(DashActivityDefinition[ActivityType.Pinata].MainWindowPrefabName, ActivityType.Pinata)
      end
    end
    self:Close()
  end)
  sequence:OnComplete(function()
    if not self.gameObject:IsNull() then
      self.m_sequence = nil
    end
  end)
  self.m_sequence = sequence
end
