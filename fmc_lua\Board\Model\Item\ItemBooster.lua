EBoosterType = {Duplicate = "Duplicate", AddItem = "AddItem"}
ItemBooster = setmetatable({}, BaseItemComponent)
ItemBooster.__index = ItemBooster

function ItemBooster.Create(itemConfig)
  local cmp = setmetatable({}, ItemBooster)
  cmp:Init(itemConfig)
  return cmp
end

function ItemBooster:Init(itemConfig)
  self.boosterType = itemConfig.BoosterType
  self.effect = itemConfig.Effect
end

function ItemBooster:CanTakeEffect(targetItem)
  if self.boosterType == EBoosterType.Duplicate then
    local type = targetItem:GetType()
    local level = GM.ItemDataModel:GetChainLevel(type)
    return level <= self.effect
  elseif self.boosterType == EBoosterType.AddItem then
    local itemSpread = targetItem:GetComponent(ItemSpread)
    return itemSpread ~= nil and not itemSpread:IsDisposable()
  end
  return false
end

function ItemBooster:TakeEffect(targetItem)
  local effected = false
  if self.boosterType == EBoosterType.Duplicate then
    effected = self:_DuplicateItem(targetItem)
  elseif self.boosterType == EBoosterType.AddItem then
    effected = self:_BoostItemSpread(targetItem)
  end
  if effected then
    GM.BIManager:LogUseItem(self.m_itemModel:GetCode(), 1, "item", targetItem:GetCode())
    EventDispatcher.DispatchEvent(EEventType.ItemAffected)
  end
  return effected
end

function ItemBooster:_DuplicateItem(targetItem)
  local type = targetItem:GetType()
  local boardModel = self.m_itemModel:GetBoardModel()
  boardModel:RemoveItem(self.m_itemModel)
  local targetPosition = targetItem:GetPosition()
  local position = boardModel:FindEmptyPositionInSpreadOrder(targetPosition)
  local newItem = boardModel:GenerateItem(position, type)
  local message = {
    Duplicate = self.m_itemModel,
    Target = targetItem,
    New = newItem
  }
  boardModel.event:Call(BoardEventType.DuplicateItem, message)
  return true
end

function ItemBooster:_BoostItemSpread(targetItem)
  local itemSpread = targetItem:GetComponent(ItemSpread)
  if itemSpread == nil then
    Log.Error(targetItem:GetType() .. " 没有母棋子喷发组件，不能被特殊棋子作用！")
    return false
  end
  local boardModel = self.m_itemModel:GetBoardModel()
  boardModel:RemoveItem(self.m_itemModel)
  local effectCount = self.effect
  if self.boosterType == EBoosterType.AddItem then
    itemSpread:AddAddItemBoost(self.effect)
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxTapCard)
  end
  local message = {
    Boost = self.m_itemModel,
    Target = targetItem,
    Effect = effectCount
  }
  boardModel.event:Call(BoardEventType.BoostItemSpread, message)
  return true
end
