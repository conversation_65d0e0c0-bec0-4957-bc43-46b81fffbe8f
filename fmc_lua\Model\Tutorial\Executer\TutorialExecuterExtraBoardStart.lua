local Step = {
  ClickEntry = "1",
  ClickCache = "2",
  MergeItem = "3",
  HighlightProgressTarget = "4"
}
local EStep2TextKey = {
  [Step.ClickEntry] = "extraboard_guide_1",
  [Step.ClickCache] = "extraboard_guide_2",
  [Step.MergeItem] = "extraboard_guide_3",
  [Step.HighlightProgressTarget] = "extraboard_guide_4"
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.ExtraBoardViewCreated, self, self._OnExtraBoardViewCreated)
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self._OnPopCachedItem)
  EventDispatcher.AddListener(EEventType.ItemMerged, self, self._OnItemMerged)
  for _, v in pairs(ExtraBoardActivityDefinition) do
    EventDispatcher.AddListener(v.StateChangedEvent, self, self._OnActivityStateChanged)
  end
end

function Executer:TryStartTutorial()
  local gameMode = GM.SceneManager:GetGameMode()
  if gameMode ~= EGameMode.Board and gameMode ~= EGameMode.Main then
    return false
  end
  local model
  for activityType, activityDefinition in pairs(ExtraBoardActivityDefinition) do
    model = GM.ActivityManager:GetModel(activityType)
    if model ~= nil and model:GetState() == ActivityState.Started then
      self.m_activityType = activityType
      self.m_activityDefinition = activityDefinition
      GM.UIManager:SetEventLock(true)
      DelayExecuteFunc(function()
        GM.UIManager:SetEventLock(false)
        self:_ExecuteStep1()
      end, 0.5)
      return true
    end
  end
end

function Executer:_OnOpenView(msg)
  local name = msg and msg.name
  local window
  for k, v in pairs(ExtraBoardActivityDefinition) do
    if name == v.ReadyWindowPrefabName then
      window = GM.UIManager:GetOpenedViewByName(name)
      if window ~= nil and self.m_strOngoingDatas == Step.ClickEntry then
        TutorialHelper.HideMask()
        TutorialHelper.HideGesture(self.m_gesture)
        TutorialHelper.HideDialog()
        if GM.SceneManager:GetGameMode() == EGameMode.Board then
          TutorialHelper.DehighlightActivityBoardEntry(k)
        else
          TutorialHelper.DehighlightHudButton(v.EntryButtonKey)
        end
        self.m_highlightInBoard = nil
        break
      end
    end
  end
end

function Executer:_OnExtraBoardViewCreated(boardModel)
  if GM.ModeViewController:GetExtraBoardActivityBoardView() ~= nil then
    if self.m_activityType == nil or self.m_activityDefinition == nil then
      self.m_activityType = boardModel:GetActivityType()
      self.m_activityDefinition = ExtraBoardActivityDefinition[self.m_activityType]
    end
    self:_ExecuteStep2()
  end
end

function Executer:_OnPopCachedItem()
  if self.m_strOngoingDatas ~= Step.ClickCache then
    return
  end
  TutorialHelper.HideGesture(self.m_gesture)
  if self.m_boardContainerTrans ~= nil and not self.m_boardContainerTrans:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_boardContainerTrans)
    self.m_boardContainerTrans = nil
  end
  self:_ExecuteStep3()
end

function Executer:_OnItemMerged()
  if self.m_strOngoingDatas ~= Step.MergeItem then
    return
  end
  TutorialHelper.HideGesture(self.m_gesture)
  TutorialHelper.HideDialog()
  TutorialHelper.HideMask()
  GM.TutorialModel:SetForceSourceBoardPosition(nil)
  GM.TutorialModel:SetForceTargetBoardPosition(nil)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    self:_ExecuteStep4()
  end, 1)
end

function Executer:_OnActivityStateChanged()
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    GM.TutorialModel:SetForceSourceBoardPosition(nil)
    GM.TutorialModel:SetForceTargetBoardPosition(nil)
    self:Finish(self.m_gesture)
  end
end

function Executer:_ExecuteStep1()
  self.m_strOngoingDatas = Step.ClickEntry
  self:SetStrongTutorial(true)
  GM.TutorialModel:SetTutorialFinished(self:GetTutorialId())
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.WholeMask()
  local bInBoard = GM.SceneManager:GetGameMode() == EGameMode.Board
  local highlightTrans
  if bInBoard then
    if TutorialHelper.GetActivityBoardEntry(self.m_activityType) == nil then
      self:Finish()
      return
    end
    TutorialHelper.ScrollToActivityEntry(self.m_activityType, false)
    highlightTrans = TutorialHelper.HighlightActivityBoardEntry(self.m_activityType)
    self.m_highlightInBoard = true
  else
    if TutorialHelper.GetHudButton(self.m_activityDefinition.EntryButtonKey) == nil then
      self:Finish()
      return
    end
    highlightTrans = TutorialHelper.HighlightHudButton(self.m_activityDefinition.EntryButtonKey)
    self.m_highlightInBoard = false
  end
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(highlightTrans)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  TutorialHelper.ShowDialogWithTransAuto(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), highlightTrans)
end

function Executer:_ExecuteStep2()
  if StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    self:SetStrongTutorial(true)
    GM.TutorialModel:SetTutorialFinished(self:GetTutorialId())
  end
  self.m_strOngoingDatas = Step.ClickCache
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local window = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
    local boardView = GM.ModeViewController:GetExtraBoardActivityBoardView()
    if boardView == nil or window == nil or ExtraBoardActivityBoardContainer.GetInstance() == nil then
      self:Finish(self.m_gesture)
      return
    end
    local boardContainerTrans = window:GetBoardContainerTrans()
    TutorialHelper.HighlightForUI(boardContainerTrans)
    self.m_boardContainerTrans = boardContainerTrans
    local boardPosition = boardView:GetModel():CreatePosition(-1, -1)
    GM.TutorialModel:SetForceSourceBoardPosition(boardPosition)
    GM.TutorialModel:SetForceTargetBoardPosition(boardPosition)
    local worldPosition = boardView:GetCacheRoot():GetFlyTargetPosition()
    local screenPosition = boardView:ConvertWorldPositionToScreenPosition(worldPosition)
    local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
    self.m_gesture = TutorialHelper.TapCustomPos(uiWorldPosition)
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
    TutorialHelper.WholeMask()
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), 30)
    TutorialHelper.UpdateDialogSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  end, 0.3)
  TutorialHelper.WholeMask()
end

function Executer:_ExecuteStep3()
  local activityModel = GM.ActivityManager:GetModel(self.m_activityType)
  local boardModel = activityModel:GetBoardModel()
  local boardView = GM.ModeViewController:GetExtraBoardActivityBoardView()
  local items = boardModel:FindMergePair()
  if Table.IsEmpty(items) or boardView == nil then
    self:Finish(self.m_gesture)
    return
  end
  self.m_strOngoingDatas = Step.MergeItem
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  boardView:GetCacheRoot():SetClickable(false)
  local source = items[1]:GetPosition()
  local target = items[2]:GetPosition()
  if not boardModel:CanItemMove(items[1]) then
    source, target = target, source
  end
  GM.TutorialModel:SetForceSourceBoardPosition(source)
  GM.TutorialModel:SetForceTargetBoardPosition(target)
  self.m_gesture = TutorialHelper.DragOnItems(source, target, boardView)
  TutorialHelper.MaskOnGivenBoard(boardView, source, target)
  local text = GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas])
  TutorialHelper.ShowDialogWithGivenBoardMaskArea(boardView, text, source, target, nil, nil, 100)
end

function Executer:_ExecuteStep4()
  self.m_strOngoingDatas = Step.HighlightProgressTarget
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local window = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
  local boardView = GM.ModeViewController:GetExtraBoardActivityBoardView()
  if window == nil or boardView == nil then
    self:Finish(self.m_gesture)
    return
  end
  boardView:GetCacheRoot():SetClickable(true)
  local transf = window:GetProgressTransform()
  TutorialHelper.UpdateMask(transf.position, transf.sizeDelta * transf.localScale.x, function()
    self:Finish(self.m_gesture)
    GM.UIManager:OpenView(UIPrefabConfigName.ExtraBoardActivityHelpWindow, self.m_activityType)
  end, false)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), 40)
end

function Executer:Finish(...)
  TutorialExecuter.Finish(self, ...)
  if self.m_boardContainerTrans ~= nil and not self.m_boardContainerTrans:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_boardContainerTrans)
    self.m_boardContainerTrans = nil
  end
  if self.m_highlightInBoard ~= nil and self.m_activityDefinition ~= nil and self.m_activityType ~= nil then
    if self.m_highlightInBoard then
      TutorialHelper.DehighlightActivityBoardEntry(self.m_activityType)
    else
      TutorialHelper.DehighlightHudButton(self.m_activityDefinition.EntryButtonKey)
    end
    self.m_highlightInBoard = nil
  end
  GM.TutorialModel:SetForceSourceBoardPosition(nil)
  GM.TutorialModel:SetForceTargetBoardPosition(nil)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
