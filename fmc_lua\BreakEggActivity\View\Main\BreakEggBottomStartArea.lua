BreakEggBottomStartArea = setmetatable({}, BreakEggBaseArea)
BreakEggBottomStartArea.__index = BreakEggBottomStartArea

function BreakEggBottomStartArea:Init()
  BreakEggBaseArea.Init(self)
  self.m_priceText.text = self.m_activityModel:GetStartBreakEggPrice()
end

function BreakEggBottomStartArea:UpdateContent()
  local active = self.m_activityModel:GetBreakEggState() == BreakEggState.NoStart
  self.gameObject:SetActive(active)
  if not active then
    return
  end
  self.m_canvasGroup.alpha = 1
  if self.m_activityModel:IsFree() then
    self.m_freeButtonGo:SetActive(true)
    self.m_paidButtonGo:SetActive(false)
  else
    self.m_freeButtonGo:SetActive(false)
    self.m_paidButtonGo:SetActive(true)
  end
end

function BreakEggBottomStartArea:UpdateChild()
  self.m_canvasGroup.alpha = 1
  if self.m_activityModel:IsFree() then
    self.m_freeButtonGo:SetActive(true)
    self.m_paidButtonGo:SetActive(false)
  else
    self.m_freeButtonGo:SetActive(false)
    self.m_paidButtonGo:SetActive(true)
  end
end

function BreakEggBottomStartArea:OnStartButtonClicked()
  local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BreakEggMainWindow)
  mainWindow:OnStartButtonClicked()
end
