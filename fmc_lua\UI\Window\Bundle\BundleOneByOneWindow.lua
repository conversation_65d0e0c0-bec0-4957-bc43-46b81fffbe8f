BundleOneByOneWindow = setmetatable({disableEffectWhenCloseView = true, windowMaskAlpha = 0.8}, BundleBaseWindow)
BundleOneByOneWindow.__index = BundleOneByOneWindow

function BundleOneByOneWindow:UpdatePerSecond()
  if not self.m_bundleEndTime then
    self.m_bundleEndTime = self.m_model:GetBundleTriggerEndTime(self.m_dataGroup)
  end
  local restDuration = self.m_bundleEndTime - GM.GameModel:GetServerTime()
  if self.m_countDownText then
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(restDuration, 2, false, false)
  end
  if restDuration <= 0 then
    UIUtil.SetActive(self.m_countDownGo, false)
    return
  end
end

function BundleOneByOneWindow:OnBundleDataRefreshed(msg)
  if self.m_model ~= nil and self.m_dataGroup ~= nil and self.m_model:HasPurchaseFinished(self.m_dataGroup) then
    DelayExecuteFuncInView(function()
      self:Close()
    end, 1, self)
    return
  end
  BundleBaseWindow.OnBundleDataRefreshed(self, msg)
end

function BundleOneByOneWindow:OnDestroy()
  BundleBaseWindow.OnDestroy(self)
  if self.m_seq ~= nil then
    self.m_seq:Kill()
    self.m_seq = nil
  end
  GM.UIManager:RemoveAllEventLocks(self)
end

function BundleOneByOneWindow:Init(...)
  BundleBaseWindow.Init(self, ...)
  self.m_model = GM.BundleManager:GetModel(self.m_dataGroup:GetBundleType())
  local uiType = self.m_dataGroup:GetBundleUIType()
  self.m_uiStyle = BundleUIType[uiType]
  self.m_descText.text = GM.GameTextModel:GetText(self.m_uiStyle.descText)
  self.m_cells = {
    self.m_cell1,
    self.m_cell2,
    self.m_cell3
  }
  local cell
  for index = 1, 3 do
    cell = self.m_cells[index]
    local curData = self.m_dataGroup:GetConfigDataByIndex(index)
    if curData ~= nil then
      cell.gameObject:SetActive(true)
      cell:Init(self, self.m_dataGroup, index)
    end
    self.m_cells[index] = cell
  end
  self:UpdatePerSecond()
  EventDispatcher.AddListener(EEventType.BundleIAPRestoreSuccess, self, self.OnBundleIAPRestoreSuccess)
end

function BundleOneByOneWindow:OnBundleIAPRestoreSuccess(groupId)
  if self.m_dataGroup ~= nil and self.m_dataGroup:GetGroupId() == groupId then
    self:Close()
  end
end

function BundleOneByOneWindow:GetPurchaseIds()
  if self.m_dataGroup ~= nil then
    return self.m_dataGroup:GetBundleIds()
  end
end

function BundleOneByOneWindow:PlayUnlockAnimation()
  local curStep = self.m_model:GetCurChainStep(self.m_dataGroup)
  local cell = self.m_cells[curStep]
  if not cell then
    return
  end
  local transform = cell:GetLockGo().transform
  local sequence = DOTween.Sequence()
  sequence:AppendInterval(0.3)
  sequence:AppendCallback(function()
    cell:PlayUnlockSpine()
  end)
  sequence:AppendInterval(0.3)
  sequence:AppendCallback(function()
    cell:PlayUnlockEffect()
    cell:ToggleAvailableEffect(true)
  end)
  sequence:Append(transform:DOShakeAnchorPos(0.4, 4, 20, 90, true))
  sequence:Append(transform:DOLocalMoveY(transform.localPosition.y + 30, 0.1):SetEase(Ease.OutQuart))
  sequence:Append(transform:DOLocalMoveY(transform.localPosition.y - 1000, 0.45):SetEase(Ease.InQuart))
  sequence:AppendCallback(function()
    cell:SetUnlocked()
  end)
  GM.UIManager:SetEventLock(true, self)
  sequence:OnComplete(function()
    GM.UIManager:SetEventLock(false, self)
    self.m_seq = nil
  end)
  self.m_seq = sequence
end

function BundleOneByOneWindow:OnCloseView()
  BundleBaseWindow.OnCloseView(self)
  if self.m_dataGroup ~= nil and self.m_model ~= nil and self.m_dataGroup:IsDisposable() then
    self.m_model:TryRecoverFreeRewards(self.m_dataGroup, true)
  end
  for _, cell in pairs(self.m_cells) do
    cell:OnCloseView()
  end
end

BundleOneByOneCell = {}
BundleOneByOneCell.__index = BundleOneByOneCell

function BundleOneByOneCell:Init(window, dataGroup, index)
  self.m_window = window
  self.m_model = GM.BundleManager:GetModel(dataGroup:GetBundleType())
  self.m_dataGroup = dataGroup
  self.m_index = index
  self.m_uiStyle = BundleUIType[self.m_dataGroup:GetBundleUIType()]
  self.m_lockPosition = self.m_lockGo.transform.localPosition
  self:UpdateContent(self.m_dataGroup:GetConfigDataByIndex(index))
  self.m_lockSpine:Init()
  local bCurStep = self:_IsLogicUnlocked()
  self:ToggleAvailableEffect(bCurStep)
  self.m_frameEffectGo:SetActive(false)
end

function BundleOneByOneCell:OnDestroy()
  self:_ClearTween()
  Scheduler.UnscheduleTarget(self)
end

function BundleOneByOneCell:OnCloseView()
  Scheduler.UnscheduleTarget(self)
  self.m_frameEffectGo:SetActive(false)
end

function BundleOneByOneCell:_ClearTween()
  if self.m_tween then
    self.m_tween:Kill()
    self.m_tween = nil
  end
  if self.m_scaleTween then
    self.m_scaleTween:Kill()
    self.m_scaleTween = nil
  end
end

function BundleOneByOneCell:UpdateContent(data)
  self.m_data = data
  self.m_tagText.text = GM.GameTextModel:GetText("value_tag", self.m_data:GetDiscountTag())
  self:_UpdateRewards()
  self:_UpdateButton()
end

function BundleOneByOneCell:_UpdateButton()
  local curStep = self.m_model:GetCurChainStep(self.m_dataGroup)
  local bClaimed = curStep > self.m_index
  UIUtil.SetActive(self.m_IAPButtonGo, curStep <= self.m_index)
  UIUtil.SetActive(self.m_claimedButtonGo, bClaimed)
  UIUtil.SetActive(self.m_claimedMaskGo, bClaimed)
  self.m_IAPText.text = GM.InAppPurchaseModel:GetLocalizedPrice(self.m_data:GetPurchaseId())
  self.m_bUnlocked = self:_IsLogicUnlocked()
  self:UpdateLockState()
end

function BundleOneByOneCell:_UpdateRewards()
  self.m_rewardContent:Init(self.m_data:GetGoods())
end

function BundleOneByOneCell:SetUnlocked()
  self.m_bUnlocked = true
  self:UpdateLockState()
  self:PlayScaleAnimation()
  DelayExecuteFuncInView(function()
    self.m_frameEffectGo:SetActive(true)
  end, 0.27, self, false)
end

function BundleOneByOneCell:UpdateLockState()
  local curStep = self.m_model:GetCurChainStep(self.m_dataGroup)
  UIUtil.SetActive(self.m_lockGo, curStep < self.m_index)
  self.m_lockGo.transform.localPosition = self.m_lockPosition
end

function BundleOneByOneCell:PlayUnlockSpine()
  self.m_lockSpine:SetAnimation("0_open_1", false)
end

function BundleOneByOneCell:PlayUnlockEffect()
  self.m_unlockEffectGo:SetActive(true)
end

function BundleOneByOneCell:PlayScaleAnimation()
  self:_ClearTween()
  local s = DOTween:Sequence()
  s:Append(self.m_bgTrans:DOScale(1.1, 0.2))
  s:Append(self.m_bgTrans:DOScale(1, 0.3333333333333333))
  s:AppendCallback(function()
    self.m_scaleTween = nil
  end)
  self.m_scaleTween = s
end

function BundleOneByOneCell:ToggleAvailableEffect(visible)
  self.m_buttonEffectGo:SetActive(visible)
  if self.m_tween then
    self.m_tween:Kill()
    self.m_tween = nil
  end
  self.m_iapBtnTrans:SetLocalScaleXY(1)
  if visible then
    local s = DOTween.Sequence()
    s:Append(self.m_iapBtnTrans:DOScale(1.1, 0.3):SetEase(Ease.InOutSine):SetLoops(2, LoopType.Yoyo))
    s:AppendInterval(2.4)
    s:SetLoops(-1)
    self.m_tween = s
  end
end

function BundleOneByOneCell:_IsLogicUnlocked()
  return self.m_model:GetCurChainStep(self.m_dataGroup) == self.m_index
end

function BundleOneByOneCell:IsInCurStep()
  if not self.m_bUnlocked then
    return false
  end
  return self:_IsLogicUnlocked()
end

function BundleOneByOneCell:OnBtnClicked()
  if self:IsInCurStep() then
    self.m_model:BuyBundle(self.m_dataGroup, function(rewards)
      self:_UpdateButton()
      GM.UIManager:OpenView(UIPrefabConfigName.BundleRewardWindow, rewards, self.m_uiStyle.titleText, true, nil, function()
        if self.gameObject:IsNull() or self.m_window.gameObject:IsNull() then
          return
        end
        self.m_window:PlayUnlockAnimation()
      end)
      self:ToggleAvailableEffect(false)
    end)
  else
    GM.UIManager:ShowPromptWithKey("1by1pack_hint")
  end
end

function BundleOneByOneCell:GetLockGo()
  return self.m_lockGo
end
