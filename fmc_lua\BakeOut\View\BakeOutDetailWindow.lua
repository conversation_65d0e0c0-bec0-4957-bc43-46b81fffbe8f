BakeOutDetailWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BaseWindow)
BakeOutDetailWindow.__index = BakeOutDetailWindow

function BakeOutDetailWindow:Init()
  BaseWindow.Init(self)
  self.m_model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  self.m_modelId = self.m_model:GetId()
  local adjustSizeOffset = ScreenFitter.GetSceneViewSizeOffset()
  self.m_closeBtnGo.transform:SetLocalPosY(self.m_closeBtnGo.transform.localPosition.y - adjustSizeOffset.x)
end

function BakeOutDetailWindow:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  if self.m_model:GetState() ~= ActivityState.Started or self.m_model:GetId() ~= self.m_modelId then
    self:Close()
  end
end

function BakeOutDetailWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  if self.m_model:CanAcquireToken() and self.m_model:GetId() == self.m_modelId then
    GM.UIManager:OpenView(UIPrefabConfigName.BakeOutMainWindow)
  end
end
