PassActivityReadyWindow = setmetatable({disableEffectWhenCloseView = true}, PassActivityBaseWindow)
PassActivityReadyWindow.__index = PassActivityReadyWindow

function PassActivityReadyWindow:Init(activityType)
  PassActivityBaseWindow.Init(self, activityType, true)
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.AutoPopup
  })
  if self.m_spine then
    self.m_spine:Init()
    self.m_spine:SetAnimation("appear", false)
    self.m_spine:AddAnimation("idle", true)
  end
  if self.m_flashGo then
    DelayExecuteFuncInView(function()
      self.m_flashGo:SetActive(true)
    end, 1, self)
  end
end

function PassActivityReadyWindow:OnDestroy()
  PassActivityBaseWindow.OnDestroy(self)
  Scheduler.UnscheduleTarget(self)
end

function PassActivityReadyWindow:OnCloseBtnClick()
  self:Close(true)
  GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType)
end

PassActivityEndWindow = setmetatable({disableEffectWhenCloseView = true}, PassActivityBaseWindow)
PassActivityEndWindow.__index = PassActivityEndWindow

function PassActivityEndWindow:Init(activityType)
  PassActivityBaseWindow.Init(self, activityType, true)
  local canFinishTasks = self.m_model:GetCanFinishTasks(true)
  if #canFinishTasks ~= 0 then
    self.m_model:FinishTasks(canFinishTasks)
  end
  if self.m_spine then
    self.m_spine:Init()
    self.m_spine:SetAnimation("appear", false)
    self.m_spine:AddAnimation("idle", true)
  end
  if self.m_flashGo then
    DelayExecuteFuncInView(function()
      self.m_flashGo:SetActive(true)
    end, 1, self)
  end
end

function PassActivityEndWindow:OnDestroy()
  PassActivityBaseWindow.OnDestroy(self)
  Scheduler.UnscheduleTarget(self)
end

function PassActivityEndWindow:OnCloseBtnClick()
  PassActivityBaseWindow.OnCloseBtnClick(self)
  PassActivityViewHelper.ContinueEndChain(self.m_activityType)
end

PassActivityFinishTaskWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, PassActivityBaseWindow)
PassActivityFinishTaskWindow.__index = PassActivityFinishTaskWindow

function PassActivityFinishTaskWindow:Init(activityType, canFinishTasks)
  PassActivityBaseWindow.Init(self, activityType, false)
  self.m_canFinishTasks = canFinishTasks
  self:UpdateTokenRatio()
  self.m_actRewards = {}
  local taskNum = #canFinishTasks
  local rewardCount = 0
  local cellObject, prefab, accomplishTimes
  for i = 1, taskNum do
    local task = canFinishTasks[i]
    prefab = self:_GetCellPrefab(task.Type)
    accomplishTimes = task:GetAccomplishTimes()
    for i = 1, accomplishTimes do
      cellObject = Object.Instantiate(prefab, self.m_taskTransform)
      if task.Type == EPassActivityTaskType.VIP then
        cellObject:GetLuaTable():Init(activityType, task, true, true)
      else
        cellObject:GetLuaTable():Init(activityType, task, true, self)
      end
      if self.m_model:IsTaskGroupOpen() then
        UIUtil.SetLocalScale(cellObject.transform, 0.78, 0.78)
      end
      rewardCount = rewardCount + task.Reward
    end
  end
  self.m_model:FinishTasks(canFinishTasks)
  self.m_rewardCount = rewardCount
end

function PassActivityFinishTaskWindow:UpdateTokenRatio()
  if self.m_tokenRatioGo == nil then
    return
  end
  UIUtil.SetActive(self.m_tokenRatioGo, false)
  if self.m_model:CanBuyMaxTicket() and self.m_model:GetTicketState() == EBPTicketState.Purchase then
    UIUtil.SetActive(self.m_tokenRatioGo, true)
    self.m_tokenRatioText.text = "x" .. tostring(self.m_model:GetMaxTicketTokenRatio())
  end
end

function PassActivityFinishTaskWindow:_GetCellPrefab(taskType)
  if taskType == EPassActivityTaskType.Timelimit then
    return self.m_timelimitTaskCellPrefab
  end
  if taskType == EPassActivityTaskType.Bonus then
    return self.m_bonusTaskCellPrefab
  end
  if taskType == EPassActivityTaskType.Cycle then
    return self.m_cycleTaskCellPrefab
  end
  if taskType == EPassActivityTaskType.VIP then
    return self.m_vipTaskCellPrefab
  end
end

function PassActivityFinishTaskWindow:OnCloseBtnClick()
  self:Close(true)
  local tokenRatio = 1
  if self.m_model:CanBuyMaxTicket() and self.m_model:GetTicketState() == EBPTicketState.Purchase then
    tokenRatio = self.m_model:GetMaxTicketTokenRatio()
  end
  GM.UIManager:OpenView(self.m_activityDefinition.TokenWindowPrefabName, self.m_activityType, self.m_rewardCount, tokenRatio, self.m_actRewards)
end

PassActivityNewTimelimitTaskWindow = setmetatable({}, PassActivityBaseWindow)
PassActivityNewTimelimitTaskWindow.__index = PassActivityNewTimelimitTaskWindow

function PassActivityNewTimelimitTaskWindow:Init(activityType)
  PassActivityBaseWindow.Init(self, activityType, false)
  local newTasks = self.m_model:RefreshTimelimitTasks()
  if self.m_model:IsTaskGroupOpen() then
    local arrTask = {}
    local mapGroup = {}
    for _, task in ipairs(newTasks) do
      if task.Group == nil or mapGroup[task.Group] ~= 1 then
        table.insert(arrTask, task)
        mapGroup[task.Group] = 1
      end
    end
    newTasks = arrTask
  end
  local cellObject
  for _, task in ipairs(newTasks) do
    if task.Type == EPassActivityTaskType.Timelimit then
      cellObject = Object.Instantiate(self.m_timelimitTaskCellPrefab, self.m_taskTransform)
      cellObject:GetLuaTable():Init(activityType, task, false, self)
    elseif task.Type == EPassActivityTaskType.VIP then
      cellObject = Object.Instantiate(self.m_vipTaskCellPrefab, self.m_taskTransform)
      cellObject:GetLuaTable():Init(activityType, task, false, true)
    end
    if self.m_model:IsTaskGroupOpen() then
      UIUtil.SetLocalScale(cellObject.transform, 0.78, 0.78)
    end
  end
end

function PassActivityNewTimelimitTaskWindow:OnCloseBtnClick()
  PassActivityBaseWindow.OnCloseBtnClick(self)
  PassActivityViewHelper.ContinueViewChain(self.m_activityType)
end

function PassActivityNewTimelimitTaskWindow:OnOpenButtonClicked()
  self:Close()
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    PassActivityViewHelper.SetViewChain(true)
    PassActivityViewHelper.ContinueViewChain(self.m_activityType)
  end, 0.2)
end
