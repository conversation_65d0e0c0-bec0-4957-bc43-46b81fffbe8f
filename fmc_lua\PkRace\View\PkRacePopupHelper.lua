PkRacePopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
PkRacePopupHelper.__index = PkRacePopupHelper

function PkRacePopupHelper.Create()
  local helper = setmetatable({}, PkRacePopupHelper)
  helper:Init()
  return helper
end

function PkRacePopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(PkRaceDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
  EventDispatcher.AddListener(EEventType.ActivityRaceCompleted, self, self._OnStateChanged)
end

function PkRacePopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function PkRacePopupHelper:CheckPopup()
  for activityType, activityDefinition in pairs(PkRaceDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Started then
      if self:_CanPopupDailyPopup(model) then
        return activityDefinition.NoticeWindowPrefabName, {activityType, true}
      elseif model:HasNetwork() and model:CanClaimReward() then
        return activityDefinition.MainWindowPrefabName, {activityType, true}
      end
    elseif state == ActivityState.Ended and not model:HasWindowOpenedOnce(ActivityState.Ended) and model:HasWindowOpenedOnce(ActivityState.Started) then
      if model:HasNetwork() and model:CanClaimReward() then
        return activityDefinition.MainWindowPrefabName, {activityType, true}
      end
      if not model:CanClaimReward() then
        return activityDefinition.CompleteWindowPrefabName, {activityType}
      end
    end
  end
  return nil
end

function PkRacePopupHelper:_CanPopupDailyPopup(model)
  if model:IsDeadline() then
    return false
  end
  if model:IsInRace() then
    return false
  end
  local serverTime = GM.GameModel:GetServerTime()
  local openTime = PlayerPrefs.GetInt(EPlayerPrefKey.PkRaceDailyOpenTime, 0)
  if serverTime // Sec2Day > openTime // Sec2Day then
    return true
  end
  return false
end
