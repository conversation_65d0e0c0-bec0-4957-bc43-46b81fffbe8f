ShopCellItem = setmetatable({}, BaseShopCell)
ShopCellItem.__index = ShopCellItem

function ShopCellItem:UpdateContent(data)
  BaseShopCell.UpdateContent(self, data)
  if self.m_itemCode ~= data.Code then
    self.m_itemCode = data.Code
    self.m_iconImg.gameObject:SetActive(false)
    local spriteName = GM.ItemDataModel:GetSpriteName(data.Code)
    SpriteUtil.SetImage(self.m_iconImg, spriteName, true, function()
      self.m_iconImg.gameObject:SetActive(true)
    end)
  end
  local hasStock = data.Count > 0
  self.m_iconImg:DOFade(hasStock and 1 or 0.4, 0)
  self.m_buttonGo:SetActive(hasStock)
  self.m_outofStockGo:SetActive(not hasStock)
  self.m_effectGo:SetActive(hasStock)
  self.m_descText.gameObject:SetActive(hasStock)
  if hasStock then
    self.m_descText.text = GM.GameTextModel:GetText("shop_item_left_tip", data.Count)
    if not IsNumber(data.Price) then
      Log.Error(tostring(data.Code) .. " 没有价格！")
      data.Price = 0
    end
    self.m_propertyButton.gameObject:SetActive(0 < data.Price)
    self.m_freeButton.gameObject:SetActive(data.Price == 0)
    if 0 < data.Price then
      self.m_propertyButton:Init("gem", data.Price, self.m_callback)
    else
      self.m_freeButton:Init(nil, self.m_callback)
    end
    self.m_redTagGo:SetActive(data.RedTag ~= nil)
    if IsString(data.RedTag) then
      self.m_redTagText.text = GM.GameTextModel:GetText(data.RedTag)
    end
    self.m_purpleTagGo:SetActive(data.PurpleTag ~= nil)
    if IsString(data.PurpleTag) then
      self.m_purpleTagText.text = GM.GameTextModel:GetText(data.PurpleTag)
    end
  else
    self.m_redTagGo:SetActive(false)
    self.m_purpleTagGo:SetActive(false)
  end
end

function ShopCellItem:UpdateSortingOrder(sortingOrder)
  UIUtil.UpdateSortingOrder(self.m_effectGo, sortingOrder + 1)
end

function ShopCellItem:OnClose()
  self.m_effectGo:SetActive(false)
end

function ShopCellItem:OnDetailClick()
  ItemDetailWindow.Open(self.m_itemCode, ItemDetailWindowMode.Normal, EItemDetailWindowRefer.Shop)
end

function ShopCellItem:GetFreeBtnGo()
  return self.m_freeButton.gameObject
end

function ShopCellItem:GetLeftCount()
  return self.m_data.Count or 0
end

function ShopCellItem:GetDetailButton()
  return self.m_detailBtn
end
