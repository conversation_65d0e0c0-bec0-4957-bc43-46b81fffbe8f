DashActivityScoreSlider = {}
DashActivityScoreSlider.__index = DashActivityScoreSlider

function DashActivityScoreSlider:Init(activityType)
  self.m_model = GM.ActivityManager:GetModel(activityType)
end

function DashActivityScoreSlider:UpdateContent()
  self:UpdateLevel()
  self:UpdateScore()
end

function DashActivityScoreSlider:UpdateLevel(lvl)
  if self.m_rewardImages == nil then
    self.m_rewardImages = {
      self.m_rewardImage1,
      self.m_rewardImage2,
      self.m_rewardImage3
    }
  end
  for _, rewardImage in ipairs(self.m_rewardImages) do
    rewardImage.enabled = false
  end
  local level = lvl or self.m_model:GetMilestoneLevel()
  local rewards = self.m_model:GetLevelConfigs()[level].rewards
  if #rewards == 1 then
    self.m_rewardImage1.transform.anchoredPosition = Vector2(0, 0)
  elseif #rewards == 2 then
    self.m_rewardImage1.transform.anchoredPosition = Vector2(-7.3, -11.9)
    self.m_rewardImage2.transform.anchoredPosition = Vector2(12.8, 7.1)
  else
    self.m_rewardImage1.transform.anchoredPosition = Vector2(-7.3, -11.9)
    self.m_rewardImage2.transform.anchoredPosition = Vector2(12.8, 7.1)
    self.m_rewardImage3.transform.anchoredPosition = Vector2(-13, 13)
  end
  for i, reward in ipairs(rewards) do
    local imageName = ConfigUtil.GetCurrencyImageName(reward)
    SpriteUtil.SetImage(self.m_rewardImages[i], imageName, true)
  end
end

function DashActivityScoreSlider:UpdateScore()
  local level = self.m_model:GetMilestoneLevel()
  local score = self.m_model:GetMilestoneScore()
  local targetScore = self.m_model:GetMilestoneTargetScore(level)
  local animated = false
  local progress = math.min(score / targetScore, 1)
  if progress > self.m_slider.value then
    self.m_slider:DOValue(progress, 0.2)
    animated = true
  else
    self.m_slider.value = progress
  end
  self.m_text.text = score .. "/" .. targetScore
  return animated and 0.2 or 0
end

function DashActivityScoreSlider:SetPreLevelView()
  local level = self.m_model:GetMilestoneLevel() - 1
  level = math.max(1, level)
  self:UpdateLevel(level)
  local targetScore = self.m_model:GetMilestoneTargetScore(level)
  local progress = 1
  if progress > self.m_slider.value then
    self.m_slider:DOValue(progress, 0.2)
  else
    self.m_slider.value = progress
  end
  self.m_text.text = targetScore .. "/" .. targetScore
end

DashActivityScoreSliderWithRewardItemSimple = setmetatable({}, DashActivityScoreSlider)
DashActivityScoreSliderWithRewardItemSimple.__index = DashActivityScoreSliderWithRewardItemSimple

function DashActivityScoreSliderWithRewardItemSimple:UpdateLevel(lvl)
  if self.m_rewardItems == nil then
    self.m_rewardItems = {
      self.m_rewardItem1,
      self.m_rewardItem2,
      self.m_rewardItem3
    }
  end
  local level = self.m_model:GetMilestoneLevel()
  local rewards = self.m_model:GetLevelConfigs()[level].rewards
  if #rewards == 1 then
    self.m_rewardItem1.transform.anchoredPosition = Vector2(0, 0)
  elseif #rewards == 2 then
    self.m_rewardItem1.transform.anchoredPosition = Vector2(-7.3, -11.9)
    self.m_rewardItem2.transform.anchoredPosition = Vector2(12.8, 7.1)
  else
    self.m_rewardItem1.transform.anchoredPosition = Vector2(-7.3, -11.9)
    self.m_rewardItem2.transform.anchoredPosition = Vector2(12.8, 7.1)
    self.m_rewardItem3.transform.anchoredPosition = Vector2(-13, 13)
  end
  for i, reward in ipairs(rewards) do
    UIUtil.SetActive(self.m_rewardItems[i].gameObject, true)
    self.m_rewardItems[i]:Init(reward)
  end
  for i = #rewards + 1, #self.m_rewardItems do
    UIUtil.SetActive(self.m_rewardItems[i].gameObject, false)
  end
end
