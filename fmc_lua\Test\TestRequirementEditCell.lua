TestRequirementEditCell = {}
TestRequirementEditCell.__index = TestRequirementEditCell

function TestRequirementEditCell:UpdateContent(requirementData)
  self.requirementData = requirementData
  self:UpdateItemType(requirementData.Type)
  self:UpdateCount(requirementData.Count)
end

function TestRequirementEditCell:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function TestRequirementEditCell:UpdateItemType(itemType)
  self.requirementData.Type = itemType or ""
  if GM.ItemDataModel:IsItemExist(itemType) then
    SpriteUtil.SetImage(self.m_image, itemType, true)
  elseif itemType == CLEAN_ITEM_CODE then
    SpriteUtil.SetImage(self.m_image, ImageFileConfigName.common_btn_help, false)
  else
    self.m_image.sprite = nil
  end
  self.m_inputField.text = itemType or ""
end

function TestRequirementEditCell:UpdateCount(itemCount)
  self.requirementData.Count = itemCount or 1
  self.m_countText.text = self.requirementData.Count
end

function TestRequirementEditCell:OnClickIcon()
  EventDispatcher.AddListener(EEventType.TestOrderGroupSelectItem, self, self._OnSelectItem)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._OnCloseView)
  GM.UIManager:OpenView(UIPrefabConfigName.TestOrderGroupItemListWindow)
end

function TestRequirementEditCell:_OnSelectItem(itemType)
  self:UpdateItemType(itemType)
  EventDispatcher.RemoveTarget(self)
end

function TestRequirementEditCell:_OnCloseView(msg)
  if msg and msg.name == UIPrefabConfigName.TestOrderGroupItemListWindow then
    EventDispatcher.RemoveTarget(self)
  end
end

function TestRequirementEditCell:OnClickConfirmItem()
  local inputValue = self.m_inputField.text
  if GM.ItemDataModel:IsItemExist(inputValue) or inputValue == CLEAN_ITEM_CODE then
    self:UpdateItemType(self.m_inputField.text)
  else
    GM.UIManager:ShowTestPrompt("棋子不存在")
  end
end

function TestRequirementEditCell:AddCount()
  self:UpdateCount(self.requirementData.Count + 1)
end

function TestRequirementEditCell:SubCount()
  if self.requirementData.Count <= 1 then
    return
  end
  self:UpdateCount(self.requirementData.Count - 1)
end

function TestRequirementEditCell:OnClickDelete()
  self:UpdateItemType()
end
