local Step = {ClickSkip = "1"}
local EStep2TextAnchorPercent = {
  [Step.ClickSkip] = 77
}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:_InitArgs(args)
  self.m_itemCode = args and args[1] or "pd_1_7"
end

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OnSpeedUp, self, self._OnSpeedUp)
  local item = TutorialHelper.GetItems(self.m_itemCode)
  if 0 < #item then
    self.m_targetItem = item[1]
  else
    self:Finish()
  end
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  local itemSpread = self.m_targetItem:GetComponent(ItemSpread)
  if itemSpread and itemSpread:CanTutorialCD() then
    local boardView = MainBoardView.GetInstance()
    if boardView ~= nil then
      boardView:UpdateSelectedItem(self.m_targetItem)
    end
    self:_ExecuteStep1(itemSpread)
    return true
  end
end

function Executer:_OnSpeedUp()
  if self.m_strOngoingDatas == Step.ClickSkip and self.m_gesture then
    TutorialHelper.DehighlightBoardInfoSkipBtnGo()
    self:Finish(self.m_gesture)
  end
end

function Executer:_ExecuteStep1(itemSpread)
  itemSpread:SetTutorialCD()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickSkip
  self:_SaveOngoingDatas()
  TutorialHelper.WholeMask()
  local btn = TutorialHelper.HighlightBoardInfoSkipBtnGo()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText("tutorial_cooldown_" .. self.m_itemCode), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  self.m_gesture = TutorialHelper.TapOnBoardInfoBarButton(btn)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

return function(tutorialId, strDatas, args)
  local copy = Table.DeepCopy(Executer)
  copy:_InitArgs(args)
  return TutorialExecuter.CreateExecuter(copy, tutorialId, strDatas)
end
