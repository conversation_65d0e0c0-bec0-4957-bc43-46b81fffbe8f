ExtraBoardActivityHudButton = setmetatable({}, HudPropertyButton)
ExtraBoardActivityHudButton.__index = ExtraBoardActivityHudButton

function ExtraBoardActivityHudButton:Awake()
  HudPropertyButton.Awake(self)
  if self.m_activityDefinition ~= nil then
    self:SyncToModelValue()
  end
end

function ExtraBoardActivityHudButton:Init(activityType)
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_boardModel = self.m_model:GetBoardModel()
  if self.m_boardModel == nil then
    return
  end
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  HudPropertyButton.Init(self)
end

function ExtraBoardActivityHudButton:GetBoardModel()
  return self.m_model and self.m_model:GetBoardModel()
end

function ExtraBoardActivityHudButton:_AddListeners()
  if HudPropertyButton._AddListeners(self) then
    EventDispatcher.AddListener(EEventType.PopCachedItem, self, self.OnPopCachedItem)
  end
  if self.m_activityDefinition ~= nil then
    EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self.SyncToModelValue)
  end
end

function ExtraBoardActivityHudButton:OnPopCachedItem(msg)
  if msg ~= nil and msg.GameMode == self.m_boardModel:GetGameMode() then
    self:SyncToModelValue()
  end
end

function ExtraBoardActivityHudButton:_CheckPropertyType(checkType)
  return self.m_model:IsSelfItem(checkType)
end

function ExtraBoardActivityHudButton:GetPropertyNum()
  if self:GetBoardModel() == nil then
    return 0
  end
  return self:GetBoardModel():GetCachedItemCount()
end

function ExtraBoardActivityHudButton:SyncToModelValue()
  HudPropertyButton.SyncToModelValue(self)
  local bShowExclamation = self:GetPropertyNum() > 0
  UIUtil.SetActive(self.m_exclamationGo, bShowExclamation)
end

function ExtraBoardActivityHudButton:UpdateTextAnimation(...)
  local bShowExclamation = self:GetPropertyNum() > 0
  UIUtil.SetActive(self.m_exclamationGo, bShowExclamation)
  HudPropertyButton.UpdateTextAnimation(self, ...)
end

function ExtraBoardActivityHudButton:IconScaleAnimation(needEffect)
  if needEffect and self.m_scaleEffect then
    self.m_scaleEffect:Play()
  end
  if self.m_scale == nil or self.m_iconScaleSeq ~= nil or self.m_exclamationScaleTrans == nil then
    return
  end
  local scaleTrans = self.m_exclamationScaleTrans
  local ICON_SCALE = 1.18
  local ICON_SCALE_TIME = 0.08
  local scaleFactor = 1 - (scaleTrans.localScale.x - self.m_scale.x) / (ICON_SCALE - 1)
  local seq = DOTween.Sequence()
  seq:Append(scaleTrans:DOScale(self.m_scale * ICON_SCALE, ICON_SCALE_TIME * scaleFactor))
  seq:Append(scaleTrans:DOScale(self.m_scale, ICON_SCALE_TIME))
  seq:AppendCallback(function()
    self.m_iconScaleSeq = nil
  end)
  self.m_iconScaleSeq = seq
end
