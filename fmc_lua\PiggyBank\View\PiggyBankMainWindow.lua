PiggyBankMainWindow = setmetatable({}, BaseWindow)
PiggyBankMainWindow.__index = PiggyBankMainWindow
local SLIDER_ZERO_ROTATE_ANGLE = -28
local mapPiggyBankState2AnimationName = {
  [PiggyBankModel.PiggyBankState.CannotBuy] = "piggy_bank_idle_1",
  [PiggyBankModel.PiggyBankState.CanBuy] = "piggy_bank_idle_2",
  [PiggyBankModel.PiggyBankState.Full] = "piggy_bank_idle_3"
}

function PiggyBankMainWindow:BeforeOpenCheck()
  return GM.ActivityManager:GetModel(ActivityType.PiggyBank):CanBuy() or GM.ActivityManager:GetModel(ActivityType.PiggyBank):IsActivityOpen()
end

function PiggyBankMainWindow:Init(userClick)
  self.m_model = GM.ActivityManager:GetModel(ActivityType.PiggyBank)
  self.m_activeId = self.m_model:GetId()
  EventDispatcher.AddListener(EEventType.PiggyBankStateChanged, self, self.UpdateContent)
  local accumulatedNum = self.m_model:GetAccumulatedNum()
  local animationName = accumulatedNum <= 0 and "piggy_bank_idle_0" or mapPiggyBankState2AnimationName[self.m_model:GetPiggyBankState()]
  self.m_piggybankAnimation.AnimationState:SetAnimation(0, animationName, false)
  self.m_iapButton:Init(GM.InAppPurchaseModel:GetLocalizedPrice(self.m_model:GetPurchaseId()))
  self.m_iapButton:SetEnabled(self.m_model:CanBuy())
  self:UpdateContent()
  self.m_bubbleRectTrans.localScale = V3Zero
  self.m_discountView.transform.localScale = V3Zero
  self.m_iapButton.transform.localScale = V3Zero
  DOVirtual.DelayedCall(0.8, function()
    self.m_bubbleRectTrans:DOScale(1, 0.3)
    self.m_discountView.transform:DOScale(1, 0.3)
    self.m_iapButton.transform:DOScale(1, 0.3)
  end)
  self:LogWindowAction(EBIType.UIActionType.Open, userClick and EBIReferType.UserClick or EBIReferType.AutoPopup, self.m_model:GetPiggyBankState())
end

function PiggyBankMainWindow:OnDestroy()
  BaseWindow.OnDestroy(self)
  if self.m_scaleTween ~= nil then
    self.m_scaleTween:Kill()
    self.m_scaleTween = nil
  end
  self.m_bubbleRectTrans:DOKill()
  self.m_discountView.transform:DOKill()
  self.m_iapButton.transform:DOKill()
  if self.m_closeNumCount ~= nil then
    GM.UIManager:OpenView(UIPrefabConfigName.PiggyBankRewardWindow, self.m_closeNumCount, self.m_closeFull)
    self.m_closeNumCount = nil
    self.m_closeFull = nil
  end
end

function PiggyBankMainWindow:UpdateContent()
  if not self:BeforeOpenCheck() or self.m_activeId ~= self.m_model:GetId() then
    self:Close()
    return
  end
  self.m_model:SetWindowOpened()
  if self.m_model:GetState() == ActivityState.Started then
    self.m_finishTime = self.m_model:GetNextStateTime()
  else
    self.m_finishTime = 0
  end
  local accumulatedNum = self.m_model:GetAccumulatedNum()
  local payNum = self.m_model:GetPayableLine()
  local fullNum = self.m_model:GetFullLine()
  local full = accumulatedNum >= fullNum
  local descText, param
  if self.m_model:GetState() ~= ActivityState.Started then
    descText = "piggy_bank_desc_4"
  elseif full then
    descText = "piggy_bank_desc_3"
  elseif accumulatedNum >= payNum then
    descText = "piggy_bank_desc_2"
  else
    descText = "piggy_bank_desc_1"
    param = payNum
  end
  self.m_descText.text = GM.GameTextModel:GetText(descText, param)
  self.m_numText.text = accumulatedNum
  UIUtil.SetActive(self.m_discountView.gameObject, full)
  if full then
    local discount = math.floor((1 - payNum / fullNum) * 100)
    self.m_discountView:UpdateText(GM.GameTextModel:GetText("piggy_bank_tag_discount", discount))
  end
  if self.m_halfLocalPosition == nil then
    self.m_halfLocalPosition = self.m_buyContentRectTrans.localPosition
    self.m_fullLocalPosition = self.m_fullContentRectTrans.localPosition
  end
  local ratio = payNum / fullNum
  UIUtil.SetLocalPosition(self.m_buyContentRectTrans, self.m_halfLocalPosition.x + (self.m_fullLocalPosition.x - self.m_halfLocalPosition.x) * 2 * (payNum / fullNum - 0.5), self.m_halfLocalPosition.y + (self.m_fullLocalPosition.y - self.m_halfLocalPosition.y) * 2 * math.abs(payNum / fullNum - 0.5), 0)
  UIUtil.SetActive(self.m_buyContentRectTrans.gameObject, accumulatedNum < payNum)
  self.m_buyContentText.text = payNum
  self.m_fullContentText.text = fullNum
  self.m_sliderRectTrans:SetLocalRotation(0, 0, SLIDER_ZERO_ROTATE_ANGLE * (1 - accumulatedNum / fullNum))
  self:UpdatePerSecond()
end

function PiggyBankMainWindow:UpdatePerSecond()
  if self.m_finishTime ~= nil then
    local leftTime = math.max(0, self.m_finishTime - GM.GameModel:GetServerTime())
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(leftTime, 2, false, false)
    if self.m_model:GetState() ~= ActivityState.Started and self.m_scaleTween == nil then
      self.m_scaleTween = self.m_countdownText.transform:DOScale(1.3, 0.5):SetLoops(-1, LoopType.Yoyo)
    end
  end
end

function PiggyBankMainWindow:OnBtnClicked()
  if self.m_model:CanBuy(true) then
    self.m_model:Buy()
  end
end

function PiggyBankMainWindow:OnBuySuccess(rewards, full)
  self.m_closeNumCount = rewards[1][PROPERTY_COUNT]
  self.m_closeFull = full
  self:Close()
end

function PiggyBankMainWindow:OnCloseBtnClick()
  if self.m_model:GetState() == ActivityState.Started then
    self:Close()
  else
    local giveUpCallback = function(window)
      window:Close()
      if not self.gameObject:IsNull() then
        self:Close()
      end
    end
    GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "piggy_bank_title", "battlepass_lastChance", "battlepass_noButton", "battlepass_buyButton", giveUpCallback, nil, false, nil, nil)
  end
end
