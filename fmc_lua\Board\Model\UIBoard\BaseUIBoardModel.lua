BaseUIBoardModel = setmetatable({}, BaseInteractiveBoardModel)
BaseUIBoardModel.__index = BaseUIBoardModel

function BaseUIBoardModel.Create(args)
  local boardModel = setmetatable({}, BaseUIBoardModel)
  boardModel:Init(args)
  return boardModel
end

function BaseUIBoardModel:Init(args)
  self.m_itemDataTable = args.itemDataTable
  self.m_itemLayerDataTable = args.itemLayerDataTable
  self.m_itemCacheDataTable = args.itemCacheDataTable
  self.m_initCodeMap = args.initCodeMap
  self.m_activityType = args.activityType
  local itemManager = ItemManager.Create(self.m_itemDataTable, self)
  itemManager:OnSyncDataFinished()
  local itemCacheModel = ItemCacheModel.Create(self.m_itemCacheDataTable, itemManager:GetIdGenerator())
  itemCacheModel:OnSyncDataFinished()
  BaseInteractiveBoardModel.Init(self, args.gameMode, itemManager, itemCacheModel, args.width, args.height)
  if args.itemBookConfig ~= nil and args.activityDataTable then
    self.m_itemIllustreatedBookModel = BaseUIItemIllustratedBookModel.Create(self, args.itemBookConfig, args.activityDataTable)
  end
end

function BaseUIBoardModel:DropData()
  self.m_itemDataTable:Drop()
  self.m_itemLayerDataTable:Drop()
  self.m_itemCacheDataTable:Drop()
  if self.m_itemIllustreatedBookModel ~= nil then
    self.m_itemIllustreatedBookModel:Drop()
  end
  self.m_itemCacheModel:ResetVar()
  self.m_itemManager:ResetVar()
end

function BaseUIBoardModel:_CreateItemLayerModel()
  return BaseUIItemLayerModel.Create(self, self.m_itemLayerDataTable, self.m_itemManager, self.m_initCodeMap)
end

function BaseUIBoardModel:_MergeItem(item, targetItem, targetPosition)
  local bItemSpreadFinished = true
  for _, v in pairs({item, targetItem}) do
    local itemSpread = v:GetComponent(ItemSpread)
    if itemSpread ~= nil and not itemSpread:IsSpreadFinish() then
      bItemSpreadFinished = false
      break
    end
  end
  if not bItemSpreadFinished then
    GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "extraboard_tip1_title", "extraboard_tip1_desc", "common_button_ok", "common_button_cancel", function(window)
      if self:CheckItemStillInPosition(item) and self:CheckItemStillInPosition(targetItem) then
        BaseInteractiveBoardModel._MergeItem(self, item, targetItem, targetPosition)
      elseif self:CheckItemStillInPosition(item) then
        item:SetPosition(item:GetPosition())
      end
      window:Close()
    end, function(window)
      if self:CheckItemStillInPosition(item) then
        item:SetPosition(item:GetPosition())
      end
      window:Close()
    end, false)
  else
    BaseInteractiveBoardModel._MergeItem(self, item, targetItem, targetPosition)
  end
end

function BaseUIBoardModel:GenerateItem(position, code, cost)
  local item = BaseInteractiveBoardModel.GenerateItem(self, position, code, cost)
  if self.m_itemIllustreatedBookModel ~= nil then
    self.m_itemIllustreatedBookModel:AddCacheItem(code, self, position)
  end
  return item
end

function BaseUIBoardModel:CheckItemStillInPosition(itemModel)
  if not itemModel or self:GetItem(itemModel:GetPosition()) ~= itemModel then
    return false
  end
  return true
end

function BaseUIBoardModel:GetMergeAllIgnoreItems()
  local mergeAllIgnoredItems = BaseInteractiveBoardModel.GetMergeAllIgnoreItems(self)
  local filter = function(itemModel)
    local itemSpread = itemModel:GetComponent(ItemSpread)
    return itemSpread ~= nil and not itemSpread:IsSpreadFinish()
  end
  local arrItems = self:FilterItems(filter)
  for _, item in ipairs(arrItems or {}) do
    mergeAllIgnoredItems[item] = true
  end
  return mergeAllIgnoredItems
end

function BaseUIBoardModel:GetAllCollectRewardsInBoard()
  if not Table.IsEmpty(self.m_endRewards) then
    return self.m_endRewards
  end
  local arrRewards = {}
  for item, _ in pairs(self:GetAllBoardItems()) do
    if item ~= nil then
      local collectTb = item:GetComponent(ItemCollectable)
      if collectTb then
        Table.ListAppend(arrRewards, collectTb:GetRewards())
      end
      local bubbleTb = item:GetComponent(ItemRewardBubble)
      if bubbleTb then
        Table.ListAppend(arrRewards, {
          {
            [PROPERTY_TYPE] = bubbleTb:GetInnerItemCode(),
            [PROPERTY_COUNT] = 1
          }
        })
      end
      local itemSpread = item:GetComponent(ItemSpread)
      if itemSpread then
        local arrRwds = itemSpread:GetNotSpreadRewards()
        Table.ListAppend(arrRewards, arrRwds)
      end
    end
  end
  if self.m_itemIllustreatedBookModel ~= nil then
    local bookRewards = self.m_itemIllustreatedBookModel:CalculateItemIllustreatedItemReward()
    if not Table.IsEmpty(bookRewards) then
      Table.ListAppend(arrRewards, bookRewards)
    end
  end
  arrRewards = RewardApi.GetMergedRewards(arrRewards)
  RewardApi.CryptRewards(arrRewards)
  self.m_endRewards = arrRewards
  return arrRewards
end

function BaseUIBoardModel:GetItemIllustratedBook()
  return self.m_itemIllustreatedBookModel
end

function BaseUIBoardModel:GetActivityType()
  return self.m_activityType
end

function BaseUIBoardModel:CanShowDeleteButton()
  local hasCollectable = not Table.IsEmpty(self:FilterItems(function(item)
    return item:GetComponent(ItemCollectable) ~= nil or item:GetComponent(ItemRewardBubble) ~= nil
  end))
  local hasMergePair = not Table.IsEmpty(self:FindMergePair({}))
  return self:IsBoardFull() and not hasCollectable and not hasMergePair
end
