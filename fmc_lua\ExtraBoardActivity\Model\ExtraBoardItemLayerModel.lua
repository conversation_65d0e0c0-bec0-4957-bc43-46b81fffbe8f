ExtraBoardItemLayerModel = setmetatable({}, BaseUIItemLayerModel)
ExtraBoardItemLayerModel.__index = ExtraBoardItemLayerModel

function ExtraBoardItemLayerModel.Create(...)
  local itemLayerModel = setmetatable({}, ExtraBoardItemLayerModel)
  itemLayerModel:Init(...)
  return itemLayerModel
end

function ExtraBoardItemLayerModel:Init(...)
  BaseUIItemLayerModel.Init(self, ...)
  self:LoadCobwebItems()
end

function ExtraBoardItemLayerModel:Destroy()
  if not Table.IsEmpty(self.m_mapCbItems) then
    for itemModel, _ in pairs(self.m_mapCbItems) do
      itemModel:Destroy()
    end
  end
end

function ExtraBoardItemLayerModel:LoadCobwebItems()
  local itemCodes = self.m_boardModel:GetCobwebItemCodes()
  if Table.IsEmpty(itemCodes) then
    return
  end
  if not Table.IsEmpty(self.m_mapCbItems) then
    Log.Error("[循环蛛网小棋盘] 上一轮的蛛网棋子还未全部完成，请检查！")
  end
  self.m_mapCbItems = {}
  self.m_mapPosition2CbItem = {}
  local boardPosition, itemType
  for x, code in ipairs(itemCodes) do
    if code ~= ExtraBoardActivityModel.CobwebCompleteFlag then
      boardPosition = self.m_boardModel:CreatePosition(x, 0)
      local item = ItemModelFactory.CreateWithCode(self.m_boardModel, boardPosition, code, true)
      if item ~= nil then
        self.m_mapCbItems[item] = true
        self.m_mapPosition2CbItem[boardPosition] = item
      end
    end
  end
  self.m_bDirty = true
end

function ExtraBoardItemLayerModel:RemoveCobwebItem(item)
  if item == nil then
    return
  end
  item:Destroy()
  if self.m_mapCbItems[item] ~= nil then
    self.m_mapCbItems[item] = nil
  end
  local boardPosition = item:GetPosition()
  if self.m_mapPosition2CbItem[boardPosition] ~= nil then
    self.m_mapPosition2CbItem[boardPosition] = nil
  end
  self.m_bDirty = true
end

function ExtraBoardItemLayerModel:GetCobwebItems()
  if Table.IsEmpty(self.m_mapCbItems) then
    return {}
  end
  local arrCbItems = Table.GetKeys(self.m_mapCbItems)
  table.sort(arrCbItems, function(a, b)
    return a:GetPosition():GetX() < b:GetPosition():GetX()
  end)
  return arrCbItems
end

function ExtraBoardItemLayerModel:IsCobwebItem(item)
  return self.m_mapCbItems ~= nil and self.m_mapCbItems[item] ~= nil
end

function ExtraBoardItemLayerModel:GetCobwebItem(position)
  if not Table.IsEmpty(self.m_mapPosition2CbItem) and position ~= nil then
    return self.m_mapPosition2CbItem[position]
  end
  return nil
end

function ExtraBoardItemLayerModel:GetItem(position)
  local item = BaseUIItemLayerModel.GetItem(self, position)
  if item == nil then
    item = self:GetCobwebItem(position)
  end
  return item
end

function ExtraBoardItemLayerModel:_OnItemChange(itemModel, bAddItem)
  BaseUIItemLayerModel._OnItemChange(self, itemModel, bAddItem)
  self.m_bDirty = true
end

function ExtraBoardItemLayerModel:GetAllItemsWithCobweb()
  local mapItems = Table.ShallowCopy(self.m_mapItems)
  for item, _ in pairs(self.m_mapCbItems) do
    mapItems[item] = true
  end
  return mapItems
end
