EViewType = {
  SceneView = 1,
  Window = 2,
  Tutorial = 3,
  Other = 4
}
EViewCloseType = {Hide = 1, Destroy = 2}
EViewCloseAnimType = {None = 1, Animator = 2}
BaseView = {
  eViewType = EViewType.SceneView,
  eCloseType = EViewCloseType.Destroy,
  eCloseAnimType = EViewCloseAnimType.None,
  canCloseByAndroidBack = false
}
BaseView.__index = BaseView

function BaseView:Awake()
end

function BaseView:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
end

function BaseView:BeforeOpenCheck(...)
  return true
end

function BaseView:OnOpenView(...)
  self.m_bClosed = false
  self:Init(...)
  self:AddEventListener()
end

function BaseView:Init(...)
end

function BaseView:Close()
  if self.m_bClosed then
    return
  end
  GM.UIManager:CloseView(self)
end

function BaseView:OnCloseView()
  GM.UIManager:SetEventLock(true)
  self:RemoveEventListener()
  if self.m_mapComponents then
    for _, v in pairs(self.m_mapComponents) do
      v:OnClose()
    end
  end
  if self.eCloseAnimType == EViewCloseAnimType.None then
    self:OnCloseFinish()
  end
  self.m_bClosed = true
end

function BaseView:OnCloseFinish()
  GM.UIManager:SetEventLock(false)
  if self.eCloseType == EViewCloseType.Hide then
    self:_Hide()
  elseif self.eCloseType == EViewCloseType.Destroy then
    self:Destroy()
  end
  GM.UIManager:OnViewCloseFinish(self)
end

function BaseView:OnBack()
  if not self.canCloseByAndroidBack then
    return
  end
  self:_OnBack()
end

function BaseView:_OnBack()
  self:Close()
end

function BaseView:_Hide()
  self.gameObject:SetActive(false)
end

function BaseView:Destroy()
  self.gameObject:RemoveSelf()
end

function BaseView:AddEventListener()
end

function BaseView:RemoveEventListener()
end

function BaseView:SetSortingOrder(order)
  self.m_canvas.sortingOrder = order
end

function BaseView:GetSortingOrder()
  if self.m_canvas and not self.m_canvas:IsNull() then
    return self.m_canvas.sortingOrder
  end
  return 0
end
