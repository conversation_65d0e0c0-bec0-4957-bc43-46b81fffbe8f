DashActivityOrderCell = setmetatable({}, BaseOrderCell)
DashActivityOrderCell.__index = DashActivityOrderCell

function DashActivityOrderCell:Init(activityType, orderArea)
  self.m_activityType = activityType
  self.m_activityDefinition = DashActivityDefinition[activityType]
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_orderArea = orderArea
  AddHandlerAndRecordMap(self.m_model.event, DashActivityEventType.Upgraded, {
    obj = self,
    method = self.UpdateContent
  })
  if self:IsScoreUpdatedByFlyElementHit() then
    self.m_iconArea:Init(self)
  end
  AddHandlerAndRecordMap(self.m_model.event, DashActivityEventType.ScoreChanged, {
    obj = self,
    method = self.OnScoreChanged
  })
  if self.m_avatar ~= nil then
    self.m_avatar:Init(activityType)
  end
  self.m_scoreSlider:Init(activityType)
  self:UpdateContent()
  self.m_recordId = self.m_model:GetId()
  AddHandlerAndRecordMap(self.m_model.event, DashActivityEventType.StateChanged, {
    obj = self,
    method = self.OnStateChanged
  })
end

function DashActivityOrderCell:OnStateChanged()
  local curId = self.m_model:GetId()
  if self.m_recordId ~= curId and curId ~= nil then
    self:_ResetContent()
  end
  self.m_recordId = curId
end

function DashActivityOrderCell:_ResetContent()
  self.m_score = nil
  self:UpdateContent()
end

function DashActivityOrderCell:OnEnable()
  self:UpdatePerSecond()
end

function DashActivityOrderCell:OnDestroy()
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
  if self.m_buttonTween ~= nil then
    self.m_buttonTween:Kill()
    self.m_buttonTween = nil
  end
end

function DashActivityOrderCell:UpdatePerSecond()
  if self.m_model ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function DashActivityOrderCell:UpdateContent()
  local level = self.m_model:GetMilestoneLevel()
  local levelConfig = self.m_model:GetLevelConfigs()[level]
  if levelConfig ~= nil then
    self.m_scoreSlider:UpdateLevel()
    self:UpdateScore()
    self:UpdatePerSecond()
  end
end

function DashActivityOrderCell:OnScoreChanged()
  self:_TryScrollToOrderCell()
  if not self:IsScoreUpdatedByFlyElementHit() then
    self:UpdateScore()
  end
end

function DashActivityOrderCell:_TryScrollToOrderCell()
  local level = self.m_model:GetMilestoneLevel()
  local score = self.m_model:GetMilestoneScore()
  local targetScore = self.m_model:GetMilestoneTargetScore(level)
  if score >= targetScore and not self.m_button.gameObject.activeSelf then
    self.m_orderArea:ScrollToDashActivityOrder()
  end
end

function DashActivityOrderCell:UpdateScore()
  if self.m_playingAnimation then
    return
  end
  self.m_scoreSlider:UpdateScore()
  local score = self.m_model:GetMilestoneScore()
  if self.m_score ~= nil and score > self.m_score and self.m_scoreIconAnimator ~= nil then
    self.m_scoreIconAnimator:SetTrigger("Effect")
  end
  self.m_score = score
  local level = self.m_model:GetMilestoneLevel()
  local targetScore = self.m_model:GetMilestoneTargetScore(level)
  local progress = score / targetScore
  if self.m_avatar ~= nil then
    self.m_avatar:SetProgress(progress)
  end
  if score >= targetScore then
    if not self.m_button.gameObject.activeSelf then
      self.m_radianceGo:SetActive(true)
      self.m_starEffectGo:SetActive(true)
      self.m_button.gameObject:SetActive(true)
      if self.m_buttonTween == nil then
        local sequence = DOTween.Sequence()
        sequence:Append(self.m_button:DOScale(1.1, 0.4):SetLoops(2, LoopType.Yoyo))
        sequence:Join(self.m_buttonImage:DOColor(CSColor(0.8, 0.8, 0.8, 1), 0.4):SetLoops(2, LoopType.Yoyo))
        sequence:AppendInterval(0.2)
        sequence:SetLoops(-1)
        self.m_buttonTween = sequence
      end
    end
  else
    self.m_radianceGo:SetActive(false)
    self.m_starEffectGo:SetActive(false)
    self.m_button.gameObject:SetActive(false)
  end
end

function DashActivityOrderCell:OnMaskClicked()
  if not self.m_button.gameObject.activeSelf then
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType)
  end
end

function DashActivityOrderCell:OnServeButtonClicked()
  local level = self.m_model:GetMilestoneLevel()
  local levelConfig = self.m_model:GetLevelConfigs()[level]
  local initCallback = function(window)
    AddHandlerAndRecordMap(self.m_model.event, DashActivityEventType.StateChanged, {
      obj = window,
      method = window.Close
    })
  end
  local activityId = self.m_model:GetId()
  local closeCallback = function(window)
    RemoveAllHandlers(self.m_model.event, window)
    if activityId ~= self.m_model:GetId() then
      return
    end
    local round = self.m_model:GetCurrentRound()
    self.m_model:Upgrade()
    local level = self.m_model:GetMilestoneLevel()
    local levelConfig = self.m_model:GetLevelConfigs()[level]
    if levelConfig == nil or round ~= self.m_model:GetCurrentRound() then
      if self.m_model:GetState() ~= ActivityState.Released then
        GM.UIManager:OpenView(self.m_activityDefinition.SuccessWindowPrefabName, self.m_activityType)
        if round ~= self.m_model:GetCurrentRound() and self.m_model:GetState() == ActivityState.Started then
          GM.UIManager:OpenViewWhenIdle(self.m_activityDefinition.ReadyWindowPrefabName, self.m_activityType)
        end
      end
    elseif self.m_model:GetState() == ActivityState.Started then
      GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, true)
    end
  end
  GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, levelConfig.rewards, "coin_dash_get_reward", false, initCallback, closeCallback)
end

function DashActivityOrderCell:GetScoreSliderTransform()
  return self.m_scoreSlider.transform
end

function DashActivityOrderCell:GetIconArea()
  return self.m_iconArea
end

function DashActivityOrderCell:CanServe()
  return self.m_button.gameObject.activeSelf
end

function DashActivityOrderCell:IsScoreUpdatedByFlyElementHit()
  return self.m_bUpdateScoreByHit == "1"
end
