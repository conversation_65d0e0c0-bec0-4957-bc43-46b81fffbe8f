TestMatchStrList = {}
TestMatchStrList.__index = TestMatchStrList

function TestMatchStrList:Init(strList, clickCallback)
  self.m_strList = strList
  self.clickCallback = clickCallback
end

function TestMatchStrList:Update()
  if Input.GetKeyDown(KeyCode.UpArrow) then
    if self.selectIdx == 1 then
      return
    end
    self.selectIdx = self.selectIdx - 1
    self.m_matchListView:MovePanelToItemIndex(self.selectIdx - 1)
  end
  if Input.GetKeyDown(KeyCode.DownArrow) then
    if self.selectIdx == #self.m_matchList then
      return
    end
    self.selectIdx = self.selectIdx + 1
    self.m_matchListView:MovePanelToItemIndex(self.selectIdx - 1)
  end
  if Input.GetKeyDown(KeyCode.Return) and not Input.GetKey(KeyCode.LeftControl) then
    self:OnCellClick(self.m_matchList[self.selectIdx])
  end
  if Input.GetKeyDown(KeyCode.Tab) then
    self:OnCellClick(self.m_matchList[self.selectIdx])
  end
end

function TestMatchStrList:UpdateListView(pattern)
  self.m_matchList = {}
  self.selectIdx = 1
  if StringUtil.IsNilOrEmpty(pattern) then
    self.m_matchList = self.m_strList
  else
    local patternList = StringUtil.Split(pattern, " ")
    for i = 1, #self.m_strList do
      local indexStart, indexEnd
      local allFind = true
      for j = 1, #patternList do
        indexStart, indexEnd = string.find(string.lower(self.m_strList[i]), string.lower(patternList[j]), indexStart or 1)
        if indexStart then
          indexStart = indexEnd + 1
        else
          allFind = false
          break
        end
      end
      if allFind then
        self.m_matchList[#self.m_matchList + 1] = self.m_strList[i]
      end
    end
  end
  if not self.m_bListViewInit then
    self.m_matchListView:InitListView(#self.m_matchList, function(listView, cellIndex)
      return self:_OnListItemByIndex(listView, cellIndex)
    end)
    self.m_bListViewInit = true
  end
  if #self.m_matchList == 0 then
    self.gameObject:SetActive(false)
  else
    self.gameObject:SetActive(true)
  end
  self.m_matchListView:SetListItemCount(#self.m_matchList, true)
  self.m_matchListView:RefreshAllShownItem()
end

function TestMatchStrList:_OnListItemByIndex(listView, index)
  if index < 0 then
    return nil
  end
  local item = listView:NewListViewItem("cell")
  local luaTable = item.gameObject:GetLuaTable()
  luaTable:Init(self.m_matchList[index + 1], self, index + 1)
  return item
end

function TestMatchStrList:OnCellClick(str)
  if self.clickCallback then
    self.clickCallback(str)
  end
end
