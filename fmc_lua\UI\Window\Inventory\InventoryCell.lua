InventoryCell = {}
InventoryCell.__index = InventoryCell

function InventoryCell:Init(window, itemModel, index, bNewAdded)
  self.window = window
  self.index = index
  self.itemModel = itemModel
  self.m_bNewAdded = bNewAdded
  self.m_itemCode = itemModel:GetCode()
  self.m_maxLevelGo:SetActive(self.itemModel:CanShowMaxLevelIcon())
  self:Reset()
  self:_LoadItemView()
  self.m_tipButton:UpdateItemType(self.m_itemCode, nil, EItemDetailWindowRefer.Inventory, function()
    self:TryUpdateBoardInfoBar()
  end)
  self.m_shadowSprite.sortingOrder = InventoryWindow.itemViewSortingOrderNormal
end

function InventoryCell:Reset()
  self.m_itemImage.enabled = false
  self.m_itemViewRoot:DOKill()
  UIUtil.SetActive(self.m_rootRect.gameObject, true)
  UIUtil.SetLocalScale(self.m_itemViewRoot, 1, 1, 1)
  if self.m_itemView ~= nil then
    GameObject.Destroy(self.m_itemView.gameObject)
    self.m_itemView = nil
  end
  if self.m_effectGo ~= nil and not self.m_effectGo:IsNull() then
    GameObject.Destroy(self.m_effectGo)
  end
end

function InventoryCell:OnDestroy()
  if self.m_tween then
    self.m_tween:Kill()
    self.m_tween = nil
  end
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
  if self.m_highlighted then
    self:_ResetBoardInfoBar()
  end
end

function InventoryCell:_LoadItemView()
  if self.m_bNewAdded then
    local go = Object.Instantiate(self.m_lightEffectPrefab, self.m_itemViewRoot)
    go.transform:SetLocalPosY(58)
    self.m_effectGo = go
  end
  local isEquipment = self.itemModel:GetComponent(ItemCook)
  if isEquipment then
    local spineLoaded = false
    local bWaitSpineLoaded = false
    self.m_itemView = MainBoardView.GetInstance():CreateItemView(self.m_itemViewRoot, self.itemModel, false, function(itemView)
      spineLoaded = true
      if bWaitSpineLoaded then
        self:_OnLoadViewFinish()
      end
    end)
    if spineLoaded then
      self:_OnLoadViewFinish()
    else
      bWaitSpineLoaded = true
    end
  elseif self.m_itemImage then
    local spriteName = GM.ItemDataModel:GetSpriteName(self.m_itemCode)
    if spriteName == nil then
      GM.BIManager:LogErrorInfo("spriteName is nil")
    end
    SpriteUtil.SetImage(self.m_itemImage, spriteName, true)
    self:_OnLoadViewFinish()
  end
end

function InventoryCell:UpdateOrderCheck(codeStateMap)
  local orderState = codeStateMap[self.m_itemCode]
  local showCheck = orderState == OrderState.CanDeliver or orderState == OrderState.PartiallyFinished
  if showCheck and GM.ItemDataModel:IsDisposableInstrument(self.m_itemCode) and not self.itemModel:IsEmptyInstrument() then
    showCheck = false
  end
  self.m_checkGo:SetActive(showCheck)
end

local canvasType = typeof(CS.UnityEngine.Canvas)
local targetMaskInteraction = SpriteMaskInteraction.VisibleInsideMask

function InventoryCell:_OnLoadViewFinish()
  UIUtil.UpdateSortingOrder(self.m_itemViewRoot.gameObject, InventoryWindow.itemViewSortingOrderNormal)
  local uiLayer = LayerMask.NameToLayer("UI")
  self.m_itemViewRoot:SetLayerRecursively(uiLayer)
  local renderers = self.m_itemViewRoot.gameObject:GetComponentsInChildren(typeof(Renderer), true)
  for i = 0, renderers.Length - 1 do
    if renderers[i].maskInteraction ~= nil then
      renderers[i].maskInteraction = targetMaskInteraction
    end
  end
  local skeletonRenderers = self.m_itemViewRoot.gameObject:GetComponentsInChildren(typeof(SkeletonRenderer), true)
  local skeletonRenderer
  for i = 0, skeletonRenderers.Length - 1 do
    skeletonRenderer = skeletonRenderers[i]
    skeletonRenderer.maskInteraction = targetMaskInteraction
    if not skeletonRenderer.enabled then
      skeletonRenderer.enabled = true
      skeletonRenderer:LateUpdate()
      skeletonRenderer.enabled = false
    end
  end
  if self.m_itemView then
    local spriteRenderer = self.m_itemView:GetSpriteRenderer()
    self.m_itemView.transform:SetLocalPosY(spriteRenderer.sprite.rect.size.y * 0.5)
  end
end

function InventoryCell:TryUpdateBoardInfoBar()
  local isEquipment = self.itemModel:GetComponent(ItemCook)
  if not isEquipment then
    return
  end
  DelayExecuteFuncInView(function()
    TutorialHelper.HighlightBoardInfoBar(true)
    MainBoardView.GetInstance():UpdateBoardInfoBar(self.itemModel, false)
    self.m_highlighted = true
    EventDispatcher.AddListener(EEventType.OnViewWillClose, self, self._OnViewWillClose)
    EventDispatcher.AddListener(EEventType.ItemDetailWindowUpdated, self, self._ResetBoardInfoBar)
  end, 0.2, self, true)
end

function InventoryCell:_OnViewWillClose()
  if self.m_highlighted and GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.ItemDetailWindow) == nil then
    self:_ResetBoardInfoBar()
  end
end

function InventoryCell:_ResetBoardInfoBar()
  EventDispatcher.RemoveTarget(self)
  TutorialHelper.DehighlightBoardInfoBar()
  local mainBoardView = MainBoardView.GetInstance()
  if not mainBoardView then
    return
  end
  mainBoardView:UpdateBoardInfoBar(mainBoardView:GetSelectedItemModel(), true)
  self.m_highlighted = false
end

function InventoryCell:OnClick()
  self.window:TryRetrieveItem(self)
end

function InventoryCell:OnRetrieved()
  self.m_itemViewRoot:DOScale(0, 0.3):SetEase(Ease.InBack, 2)
  return 0.3
end

function InventoryCell:GetItemTrans()
  return self.m_itemViewRoot
end

function InventoryCell:HideAll()
  UIUtil.SetActive(self.m_rootRect.gameObject, false)
end
