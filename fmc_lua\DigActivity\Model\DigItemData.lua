DigItemData = {}
DigItemData.__index = DigItemData

function DigItemData.Create(config, pos, rotate)
  local data = setmetatable({}, DigItemData)
  data:_Init(config, pos, rotate)
  return data
end

function DigItemData:_Init(config, pos, rotate)
  self.m_config = config
  self.id = config.id
  self.width = config.width
  self.height = config.height
  self.pos = pos
  self.rotate = rotate
  self.imgKey = config.imgKey
  self.lockedImgKey = config.lockedImgKey
  self.unlockedScale = config.unlockedScale
  self.grooveRotate = config.grooveRotate
  self.modifyScale = config.modifyScale
  self:UpdateItemInfo()
end

function DigItemData:UpdateRotate(rotate)
  if rotate == self.rotate then
    return
  end
  self.width = self.m_config.width
  self.height = self.m_config.height
  self.rotate = rotate
  self:UpdateItemInfo()
end

function DigItemData:UpdatePos(pos)
  self.pos = pos
end

function DigItemData:UpdateItemInfo()
  Log.Assert(self.rotate % 90 == 0, "旋转角度配置有误, 必须为90的倍数, 请检查!")
  if math.abs(self.rotate // 90 % 2) == 1 then
    local width = self.width
    self.width = self.height
    self.height = width
  end
end

function DigItemData:GetOccupancyGrids(bForce)
  if Table.IsEmpty(self.m_arrGridPos) or bForce then
    self.m_arrGridPos = {}
    for x = 0, self.width - 1 do
      for y = 0, self.height - 1 do
        table.insert(self.m_arrGridPos, {
          x = self.pos.x + x,
          y = self.pos.y + y
        })
      end
    end
  end
  return self.m_arrGridPos
end

function DigItemData:IsPosInOccupancyGrids(pos)
  return pos.x >= self.pos.x and pos.x <= self.pos.x + self.width - 1 and pos.y >= self.pos.y and pos.y <= self.pos.y + self.height - 1
end

function DigItemData:GetGridCount()
  return self.width * self.height
end
