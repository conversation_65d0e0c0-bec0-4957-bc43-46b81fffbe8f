SpriteUtil = {}
SpriteUtil.__index = SpriteUtil
SpriteUtil.s_mapImageKey = setmetatable({}, {__mode = "k"})

function SpriteUtil.SetImage(imgComp, key, setNativeSize, sucCallback, failCallback, allowFail)
  Log.Assert(not StringUtil.IsNilOrEmpty(key), "SpriteUtil.SetImage key is nil or empty")
  sucCallback = sucCallback or function()
  end
  failCallback = failCallback or function()
  end
  if not imgComp or imgComp:IsNull() then
    failCallback()
    return
  end
  if imgComp.sprite and not imgComp.sprite:IsNull() and imgComp.sprite.name == key then
    imgComp.enabled = true
    if setNativeSize then
      SpriteUtil.SetNativeSize(imgComp)
    end
    SpriteUtil.s_mapImageKey[imgComp] = nil
    sucCallback(imgComp.sprite)
    return
  end
  SpriteUtil.s_mapImageKey[imgComp] = key
  GM.ResourceLoader:LoadLatestFile(GM.DataResource.ImageFileConfig:GetConfig(ImageFileConfigName[key]), function(sprite)
    if SpriteUtil.s_mapImageKey[imgComp] ~= key then
      return
    end
    SpriteUtil.s_mapImageKey[imgComp] = nil
    if sprite == nil then
      Log.Warning("load image file failed, key: " .. key)
      failCallback()
      return
    end
    if not imgComp or imgComp:IsNull() then
      failCallback()
      return
    end
    imgComp.sprite = sprite
    imgComp.enabled = true
    AddressableLoader.TrackSprite(imgComp, sprite)
    if setNativeSize then
      SpriteUtil.SetNativeSize(imgComp)
    end
    sucCallback(sprite)
  end, allowFail)
end

function SpriteUtil.SetNativeSize(imgComp)
  imgComp.transform.sizeDelta = imgComp.sprite.rect.size
end

function SpriteUtil.LimitSize(imgComp, width, height)
  local size = imgComp.transform.sizeDelta
  local oldWidth = size.x
  local oldHeight = size.y
  width = width or oldWidth
  height = height or oldHeight
  if oldWidth > width or oldHeight > height then
    width = math.min(width, oldWidth)
    height = math.min(height, oldHeight)
    imgComp.transform.sizeDelta = Vector2(width, height)
  end
end

function SpriteUtil.SetSpriteRenderer(spriteRenderer, key, sucCallback, failCallback, allowFail)
  sucCallback = sucCallback or function()
  end
  failCallback = failCallback or function()
  end
  if spriteRenderer.sprite ~= nil and spriteRenderer.sprite.name == key then
    sucCallback()
    return
  end
  SpriteUtil.s_mapImageKey[spriteRenderer] = key
  GM.ResourceLoader:LoadLatestFile(GM.DataResource.ImageFileConfig:GetConfig(key), function(sprite)
    if SpriteUtil.s_mapImageKey[spriteRenderer] ~= key then
      sucCallback()
      return
    end
    SpriteUtil.s_mapImageKey[spriteRenderer] = nil
    if sprite == nil then
      Log.Warning("load image file failed, key: " .. key)
      failCallback()
      return
    end
    if not spriteRenderer or spriteRenderer:IsNull() then
      failCallback()
      return
    end
    spriteRenderer.sprite = sprite
    AddressableLoader.TrackSprite(spriteRenderer, sprite)
    sucCallback()
  end, allowFail)
end

function SpriteUtil.LoadSprite(key, callback, allowFail)
  GM.ResourceLoader:LoadLatestFile(GM.DataResource.ImageFileConfig:GetConfig(key), callback, allowFail)
end
