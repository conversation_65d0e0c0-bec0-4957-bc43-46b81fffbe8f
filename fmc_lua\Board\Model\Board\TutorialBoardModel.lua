TutorialBoardModel = setmetatable({}, BaseBoardModel)
TutorialBoardModel.__index = TutorialBoardModel

function TutorialBoardModel.Create()
  local boardModel = setmetatable({}, TutorialBoardModel)
  boardModel:Init()
  return boardModel
end

function TutorialBoardModel:Init()
  BaseBoardModel.Init(self, 4, 4)
end

function TutorialBoardModel:_CreateItemLayerModel()
  return TutorialItemLayerModel.Create(self)
end

function TutorialBoardModel.CreateMatrix()
  return BaseBoardModel._CreateMatrix(TutorialBoardModel)
end

function TutorialBoardModel.CreatePosition(x, y)
  return BaseBoardModel._CreatePosition(TutorialBoardModel, x, y)
end

function TutorialBoardModel.CreatePositionFromLocalPosition(localPositionX, localPositionY)
  return BaseBoardModel._CreatePositionFromLocalPosition(TutorialBoardModel, localPositionX, localPositionY)
end

function TutorialBoardModel:IsBoardFull()
  return false
end

function TutorialBoardModel:DragItem(item, targetPosition)
  local targetItem = self:GetItem(targetPosition)
end
