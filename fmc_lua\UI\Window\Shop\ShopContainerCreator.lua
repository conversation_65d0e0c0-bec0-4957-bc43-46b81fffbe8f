ShopContainerCreator = {}
ShopContainerCreator.__index = ShopContainerCreator

function ShopContainerCreator:CreateContainer(type)
  if self.m_transform == nil then
    self.m_transform = self.transform
  end
  if self["m_" .. type] ~= nil then
    local go = GameObject.Instantiate(self["m_" .. type], self.m_transform):GetLuaTable()
    go.transform.sizeDelta = Vector2(self.m_transform.rect.width, go.transform.sizeDelta.y)
    if self.m_tailTrans then
      self.m_tailTrans:SetAsLastSibling()
    end
    return go
  end
  if GameConfig.IsTestMode() then
    Log.Error("[ShopContainerCreator] no prefab for type: " .. tostring(type))
  end
  return nil
end
