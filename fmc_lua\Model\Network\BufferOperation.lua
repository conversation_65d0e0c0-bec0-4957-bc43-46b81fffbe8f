BufferWriter = {}
BufferWriter.__index = BufferWriter

function BufferWriter.Create()
  local newWriter = setmetatable({}, BufferWriter)
  newWriter._buffer = ""
  return newWriter
end

function BufferWriter:write(str)
  if str == nil then
    return false
  end
  self._buffer = self._buffer .. str
  return true
end

function BufferWriter:GetString()
  return self._buffer
end

BufferReader = {}
BufferReader.__index = BufferReader

function BufferReader.Create(str)
  local newReader = setmetatable({}, BufferReader)
  newReader._buffer = str
  newReader._bufferLength = string.len(str)
  newReader._index = 1
  return newReader
end

function BufferReader:read(byteNumber)
  local avail = self:GetLeftLength()
  if byteNumber > avail then
    return nil
  end
  local data = string.sub(self._buffer, self._index, self._index + byteNumber - 1)
  self._index = self._index + byteNumber
  return data
end

function BufferReader:GetLeftLength()
  return self._bufferLength - self._index + 1
end

function BufferReader:End()
  return self._index > self._bufferLength
end
