PlatformInterface = {}

function PlatformInterface.GenerateUUID()
  return CSPlatform:GenerateUUID()
end

function PlatformInterface.GetStringFromClipboard()
  return CSPlatform:GetStringFromClipboard()
end

function PlatformInterface.SetString2Clipboard(text)
  CSPlatform:SetString2Clipboard(text)
end

function PlatformInterface.ExitGame()
  ApplicationManager.Exit()
end

function PlatformInterface.OpenAppSettings()
  CSPlatform:OpenAppSettings()
end

function PlatformInterface.IsAppInstalled(target)
  return CSPlatform:IsAppInstalled(target)
end

function PlatformInterface.OpenThirdApp(target)
end

function PlatformInterface.OpenStorePageInApp(appId)
end

function PlatformInterface.HasNotch()
  return CSPlatform:HasNotch()
end

function PlatformInterface.GetPackageName()
  return CSPlatform:GetPackageName()
end

function PlatformInterface.GetMemoryUsed()
  local memoryUsed = -1
  local func = function()
    memoryUsed = CSPlatform:GetMemoryUsed()
  end
  SafeCall(func)
  return memoryUsed
end

function PlatformInterface.GetRemainingDiskSpaceInBytes()
  local remainingDS = -1
  local func = function()
    remainingDS = CSPlatform:GetRemainingDiskSpaceInBytes()
  end
  SafeCall(func)
  return remainingDS
end

function PlatformInterface.GetTotalDiskSpaceInBytes()
  local totalDS = -1
  local func = function()
    totalDS = CSPlatform:GetTotalDiskSpaceInBytes()
  end
  SafeCall(func)
  return totalDS
end

function PlatformInterface.GetAppDataSize()
  return CSPlatform:GetAppDataSize()
end

function PlatformInterface.LaunchReviewFlow()
  CSPlatform:LaunchReviewFLow()
end

function PlatformInterface.PopupAppUpdate(strAppId, strLatestReleaseId)
  CSPlatform:PopupAppUpdate(strAppId, strLatestReleaseId)
end

function PlatformInterface.RequestATTPermission(callback)
  CSPlatform:RequestATTPermission(callback)
end

function PlatformInterface.IsNotificationsEnabled()
  return CSPlatform:IsNotificationsEnabled()
end

function PlatformInterface.CanVibrate()
  return CSPlatform:CanVibrate()
end

EVibrationType = {
  Light = 1,
  Medium = 2,
  Heavy = 3
}

function PlatformInterface.Vibrate(eVibrationType)
  if not PlatformInterface.CanVibrate() or not GM.ConfigModel:IsVibrateOn() then
    return
  end
  Log.Info("Vibrating with style: " .. tostring(eVibrationType))
  CSPlatform:Vibrate(eVibrationType)
end
