RedisMessage = {DEFAULT_RETRY = 2}
RedisMessage.__index = RedisMessage
local Serialization = require("Model.Network.Redis.RedisInterface")

function RedisMessage.Serialize(writer, reqCtx, strOpName, tbMsgReq)
  Serialization.MessageHeader.Serialize(writer, {
    ProtocolMd5 = Serialization.ProtocolMd5,
    MessageId = reqCtx.MessageId,
    Operation = strOpName
  })
  return Serialization.reqNameMap[strOpName].Serialize(writer, tbMsgReq)
end

function RedisMessage.Deserialize(reader, strOpName)
  local bRet, body
  bRet, body = Serialization.respNameMap[strOpName].Deserialize(reader)
  if not bRet or reader:GetLeftLength() ~= 0 then
    return nil
  end
  return body
end

function RedisMessage.GetBakeOutRank(config_key, config_id, token, callback, bShowMask)
  bShowMask = bShowMask == nil and true or bShowMask
  local tbReq = {
    userId = GM.UserModel:GetUserId(),
    config_id = config_id,
    config_key = config_key,
    name = GM.UserProfileModel:GetName(),
    icon = GM.UserProfileModel:GetIcon(),
    icon_frame = "",
    token = token
  }
  RedisMessage.SendByHttp("RSGetBakeOutRank", tbReq, callback, bShowMask, nil, nil, nil, nil, true)
end

function RedisMessage.QueryBakeOutRank(strGroupId, callback, bShowMask)
  bShowMask = bShowMask == nil and true or bShowMask
  local tbReq = {
    userId = GM.UserModel:GetUserId(),
    group_id = strGroupId or ""
  }
  Log.Assert(strGroupId ~= "", "不应该出现这种情况")
  RedisMessage.SendByHttp("RSQueryBakeOutRank", tbReq, callback, bShowMask, nil, nil, nil, nil, true)
end

function RedisMessage.HeartBeat(request, callback, bHttp)
  if bHttp then
    RedisMessage.SendByHttp("RSHeartBeat", request, callback)
  else
    RedisMessage.SendByUdp("RSHeartBeat", request, callback)
  end
end

function RedisMessage.TryFallbackRequest(reqCtx)
  if GameConfig.IsTestMode() then
    return GM.HttpManager:TryFallbackRequest(reqCtx, NetworkConfig.OfflineFallbackIp)
  else
    return GM.HttpManager:TryFallbackRequest(reqCtx, "************", "https://cliapi-ga.mergeab.com")
  end
end

function RedisMessage.SendByHttp(strOpName, tbMsgReq, callback, bShowMask, uTimeout, uRetryCount, uLowSpeedLimit, uLowSpeedTime, bLogAll)
  if bShowMask then
    GM.UIManager:ShowMask()
  end
  local strUrl = NetworkConfig.GetHttpServerUrl(strOpName)
  local reqCtx = CSNetLibManager:CreateApiServerHttpRequest(GM.UserModel:GetUserId(), GM.HttpManager:GetServerTime(), strUrl, uTimeout or 8000, uRetryCount or RedisMessage.DEFAULT_RETRY)
  if bLogAll or bShowMask then
    reqCtx:SetLogAll()
  else
    reqCtx:SetLogFail()
  end
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader())
  reqCtx:SetRetryHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader(2))
  if not MINIGAME then
    reqCtx:SetHeader(NetworkConfig.AcceptEncodingKey, "gzip, deflate")
  end
  local schema = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema)
  if not StringUtil.IsNilOrEmpty(schema) then
    reqCtx:SetHeader(NetworkConfig.SchemaHeaderKey, schema)
  end
  local strToken = GM.SsoManager:GetToken()
  local session = GM.SsoManager:GetSession()
  reqCtx:SetHeader(NetworkConfig.TokenHeaderKey, strToken)
  local writer = CSNetLibManager:CreateBufferBlockWriter(reqCtx.ReqBody)
  if not RedisMessage.Serialize(writer, reqCtx, strOpName, tbMsgReq) then
    Log.Warning("RedisMessage Serialize failed for " .. strOpName)
  end
  CSNetLibManager:ReleaseBufferBlockWriter(writer)
  if reqCtx.ContentLength > 1024 and NetworkConfig.CompressMethod ~= "" then
    reqCtx:CompressBody(NetworkConfig.CompressMethod)
  end
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    local headers = GM.HttpManager:ConvertHeaders(reqCtx.ResponseHeaders)
    local tbMsgResp
    if math.floor(reqCtx.Status / 100) == 2 then
      local reader = CSNetLibManager:CreateBufferBlockReader(reqCtx.RespBody)
      tbMsgResp = RedisMessage.Deserialize(reader, strOpName)
      CSNetLibManager:ReleaseBufferBlockReader(reader)
      if tbMsgResp == nil then
        GM.BIManager:LogNet(EBIType.NetworkCheckAction.CheckResponse, "Failed to deserialize response for " .. strOpName .. (headers["process-time"] == nil and " without " or " with ") .. "process-time header", reqCtx)
        if reqCtx:TryResend() then
          return
        end
      end
    end
    if headers["process-time"] == nil then
      local msg = "Missing process-time header for " .. strOpName .. " with headers"
      for k, v in pairs(headers) do
        msg = msg .. " " .. k .. ":" .. v
      end
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.CheckResponse, msg, reqCtx)
      if math.floor(reqCtx.Status / 100) ~= 2 and reqCtx:TryResend() then
        return
      end
    end
    if bShowMask then
      GM.UIManager:HideMask()
    end
    if reqCtx.Rcode == ResultCode.Succeeded then
      if callback ~= nil then
        callback(true, tbMsgResp, reqCtx, tbMsgReq)
      end
      return
    end
    if reqCtx.Rcode == ResultCode.Not2xx then
      if reqCtx.Status == 401 or reqCtx.Status == 403 then
        reqCtx:Retain()
        GM.SsoManager:OnTokenExpired(reqCtx:GetHeader(NetworkConfig.TokenHeaderKey), reqCtx, session)
        return
      end
      if reqCtx.Status == 404 or reqCtx.Status == 502 or reqCtx.Status == 400 and headers["server-time"] ~= nil then
        if headers["server-time"] ~= nil then
          GM.GameModel:RefreshServerTime(tonumber(headers["server-time"]))
          reqCtx.ServerTime = GM.HttpManager:GetServerTime()
        end
        if reqCtx:TryResend() then
          return
        end
      end
    end
    if RedisMessage.TryFallbackRequest(reqCtx) then
      return
    end
    Log.Warning("Redis Message failed, error message is " .. reqCtx.ErrorMsg, LogTag.Network)
    if callback ~= nil then
      callback(false, reqCtx.ErrorMsg, reqCtx, tbMsgReq)
    end
  end)
  if strToken == "" then
    GM.SsoManager:WaitForToken(reqCtx)
  else
    reqCtx:Send()
  end
end

function RedisMessage.SendByUdp(strOpName, tbMsgReq, callback, bShowMask, uTimeout, uRetryCount)
  local strToken = GM.SsoManager:GetToken()
  if strToken == "" or string.len(strToken) ~= 52 then
    RedisMessage.SendByHttp(strOpName, tbMsgReq, callback, bShowMask, uTimeout, uRetryCount)
    return
  end
  local healthCheck = GM.UdpManager:GetRedisHealthCheck()
  if not healthCheck:Available() then
    RedisMessage.SendByHttp(strOpName, tbMsgReq, callback, bShowMask, uTimeout, uRetryCount)
    return
  end
  if bShowMask then
    GM.UIManager:ShowMask()
  end
  local reqCtx = CSNetLibManager:CreateApiServerUdpRequest(GM.UserModel:GetUserId(), healthCheck:GetUrl(), uTimeout or 3000, uRetryCount or 0)
  local reqHeaders = {
    {
      code = EUdpMessageStatus.GameClient,
      content = NetworkConfig.GetClientHeader()
    },
    {
      code = EUdpMessageStatus.Authorization,
      content = strToken
    },
    {
      code = EUdpMessageStatus.RequestDate,
      content = UdpMessage.FormatIntUdpHeader(GM.GameModel:GetServerTime())
    }
  }
  local schema = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema)
  if not StringUtil.IsNilOrEmpty(schema) then
    table.insert(reqHeaders, {
      code = EUdpMessageStatus.SchemaName,
      content = schema
    })
  end
  local writer = CSNetLibManager:CreateBufferBlockWriter(reqCtx.ReqDatagram)
  if UdpMessage.SerializeMessageId(writer, reqCtx.MessageId) and UdpMessage.SerializeHeaders(writer, reqHeaders) then
    local length = writer:GetTotalWrite()
    if RedisMessage.Serialize(writer, reqCtx, strOpName, tbMsgReq) then
      reqCtx.BodyLength = writer:GetTotalWrite() - length
    else
      Log.Warning("RedisMessage Serialize udp body faild for " .. strOpName)
    end
  else
    Log.Warning("RedisMessage Serialize udp headers faild for " .. strOpName)
  end
  CSNetLibManager:ReleaseBufferBlockWriter(writer)
  local headers, status, tbMsgResp
  reqCtx:SetCheckResponse(function()
    if GM == nil then
      return nil
    end
    if reqCtx.RespDatagram.Bytes < 8 then
      return "Incomplete datagram"
    end
    local reader = CSNetLibManager:CreateBufferBlockReader(reqCtx.RespDatagram)
    if not UdpMessage.DeserializeMessageId(reader) then
      CSNetLibManager:ReleaseBufferBlockReader(reader)
      return "Failed to deserialize message id"
    end
    local bRet
    bRet, headers = UdpMessage.DeserializeHeaders(reader)
    if not bRet then
      CSNetLibManager:ReleaseBufferBlockReader(reader)
      return "Failed to deserialize headers"
    end
    headers = UdpMessage.ConvertHeaders(headers)
    status = headers.status or 0
    if status == 0 then
      CSNetLibManager:ReleaseBufferBlockReader(reader)
      return "Invalid status"
    end
    if math.floor(status / 100) == 2 then
      tbMsgResp = RedisMessage.Deserialize(reader, strOpName)
      if tbMsgResp == nil then
        CSNetLibManager:ReleaseBufferBlockReader(reader)
        return "Failed to deserialize body"
      end
    end
    CSNetLibManager:ReleaseBufferBlockReader(reader)
    return nil
  end)
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    if bShowMask then
      GM.UIManager:HideMask()
    end
    if reqCtx.Rcode == ResultCode.Succeeded then
      healthCheck:UpdateUdpStatus(true)
      if math.floor(status / 100) ~= 2 then
        reqCtx.Rcode = ResultCode.Not2xx
      end
    else
      healthCheck:UpdateUdpStatus(false)
    end
    if reqCtx.Rcode == ResultCode.Succeeded then
      if callback ~= nil then
        callback(true, tbMsgResp, reqCtx, tbMsgReq)
      end
      return
    end
    if reqCtx.Rcode == ResultCode.Not2xx then
      if reqCtx.Status == 401 or reqCtx.Status == 403 then
        RedisMessage.SendByHttp(strOpName, tbMsgReq, callback, bShowMask, uTimeout, uRetryCount)
        return
      end
      if headers["server-time"] ~= nil then
        GM.GameModel:RefreshServerTime(tonumber(headers["server-time"]))
      end
    end
    Log.Warning("Redis Message failed, error message is " .. reqCtx.ErrorMsg, LogTag.Network)
    if callback ~= nil then
      callback(false, reqCtx.ErrorMsg, reqCtx, tbMsgReq)
    end
  end)
  reqCtx:Send()
end
