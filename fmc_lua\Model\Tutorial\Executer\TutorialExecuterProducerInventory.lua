local Step = {
  ItemStore = "ItemStore",
  ClickInventory = "ClickInventory",
  ClickProducer = "ClickProducer"
}
local EStep2TextKey = {
  [Step.ItemStore] = "pd_inventory_tutorial_1",
  [Step.ClickInventory] = "pd_inventory_tutorial_2",
  [Step.ClickProducer] = "pd_inventory_tutorial_3"
}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.ItemStored, self, self._ItemStored)
  EventDispatcher.AddListener(EEventType.InventoryChangeTab, self, self._OnTabChanged)
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board or not GM.MainBoardModel:IsProducerInventoryOpen() then
    return false
  end
  local item = self:GetTargetItem(false)
  if item ~= nil then
    self.m_model:SetTutorialFinished(self:GetTutorialId())
    self:_StartStepItemStore(item)
    return true
  end
  item = self:GetTargetItem(true)
  if item ~= nil then
    self.m_model:SetTutorialFinished(self:GetTutorialId())
    self:_StartStepClickInventory(true)
    return true
  end
  return false
end

function Executer:GetTargetItem(bInventory)
  local arrUnlockedPDConfig = GM.MainBoardModel:GetUnlockedPDInventoryConfig()
  local itemModel
  if not bInventory then
    for _, producerConfig in ipairs(arrUnlockedPDConfig) do
      itemModel = GM.MainBoardModel:GetOneBoardItemByType(producerConfig.type)
      if itemModel ~= nil then
        return itemModel
      end
    end
  else
    for _, producerConfig in ipairs(arrUnlockedPDConfig) do
      itemModel = GM.MainBoardModel:GetFirstStoredItemByType(producerConfig.type)
      if itemModel ~= nil then
        return itemModel
      end
    end
  end
end

function Executer:_StartStepItemStore(item)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ItemStore
  self.m_item = item
  local itemPos = item:GetPosition()
  self.m_gesture = TutorialHelper.MaskDragToInventory(itemPos, GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]))
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  local inventoryButton = TutorialHelper.GetHudButton(ESceneViewHudButtonKey.Inventory)
  inventoryButton:SetClickEnabled(false)
  local itemTipButton = TutorialHelper.GetBoardInfoTipButton()
  if itemTipButton ~= nil then
    itemTipButton:SetEnabled(false)
  end
  local toMapBtn = TutorialHelper.GetHudButton(ESceneViewHudButtonKey.Map)
  toMapBtn:SetEnabled(false)
end

function Executer:_StartStepClickInventory(bPlayTutorialAnim)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickInventory
  TutorialHelper.HideTutorialLayer(self.m_gesture)
  self.m_gesture = nil
  GM.TutorialModel:ClearTempDatas()
  local inventoryButton = TutorialHelper.GetHudButton(ESceneViewHudButtonKey.Inventory)
  inventoryButton:SetClickEnabled(true)
  inventoryButton:SetPlayTutorialAnim(bPlayTutorialAnim)
  local itemTipButton = TutorialHelper.GetBoardInfoTipButton()
  if itemTipButton ~= nil then
    itemTipButton:SetEnabled(true)
  end
  local toMapBtn = TutorialHelper.GetHudButton(ESceneViewHudButtonKey.Map)
  toMapBtn:SetEnabled(true)
  self.m_bPlayTutorialAnim = bPlayTutorialAnim
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), 60)
  TutorialHelper.HighlightHudButton(ESceneViewHudButtonKey.Inventory)
  self.m_arrow = TutorialHelper.AddArrow2HudButton(ESceneViewHudButtonKey.Inventory)
  self.m_arrow:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_StartStepClickProducer()
  self.m_strOngoingDatas = Step.ClickProducer
  local window = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.InventoryWindow)
  if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
    self:Finish(self.m_gesture, self.m_arrow)
    return
  end
  local tabTrans = window:GetProducerTabButtonTrans()
  TutorialHelper.WholeMask()
  TutorialHelper.HighlightForUI(tabTrans)
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(tabTrans)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  self.m_tabTrans = tabTrans
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), 50)
end

function Executer:_OnOpenView(msg)
  if msg.name == UIPrefabConfigName.InventoryWindow and self.m_strOngoingDatas == Step.ClickInventory then
    TutorialHelper.HideMask()
    TutorialHelper.HideDialog()
    TutorialHelper.HideArrow(self.m_arrow)
    TutorialHelper.DehighlightHudButton(ESceneViewHudButtonKey.Inventory)
    self.m_arrow = nil
    local delay = self.m_bPlayTutorialAnim and 2 or 0.5
    GM.UIManager:SetEventLock(true)
    DelayExecuteFunc(function()
      GM.UIManager:SetEventLock(false)
      self:_StartStepClickProducer()
    end, delay)
  end
end

function Executer:_OnCloseView(msg)
  if self.m_strOngoingDatas == Step.ClickFree and msg.name == UIPrefabConfigName.RewardWindow then
    self:_ExecuteStep3()
  end
end

function Executer:_ItemStored()
  if self.m_strOngoingDatas ~= Step.ItemStore then
    return
  end
  self:_StartStepClickInventory(false)
end

function Executer:_OnTabChanged()
  if self.m_strOngoingDatas ~= Step.ClickProducer then
    return
  end
  if self.m_tabTrans ~= nil then
    TutorialHelper.DehighlightForUI(self.m_tabTrans)
    self.m_tabTrans = nil
  end
  self:Finish(self.m_gesture, self.m_arrow)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
