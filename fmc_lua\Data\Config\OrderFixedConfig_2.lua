return {
  {
    Id = "20009",
    GroupId = 1,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_friedmt_2",
      Count = 1
    }
  },
  {
    Id = "20010",
    GroupId = 1,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_1",
      Count = 1
    }
  },
  {
    Id = "20020",
    GroupId = 1,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_6", Count = 1}
  },
  {
    Id = "20030",
    GroupId = 1,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_2_1_1", Count = 1},
    Requirement_2 = {Type = "it_2_1_4", Count = 1}
  },
  {
    Id = "20040",
    PreId = {
      "20010",
      "20020",
      "20030"
    },
    GroupId = 2,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20050",
    PreId = {
      "20010",
      "20020",
      "20030"
    },
    GroupId = 2,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_3_3", Count = 1},
    Requirement_2 = {Type = "it_1_1_5", Count = 1}
  },
  {
    Id = "20060",
    PreId = {
      "20010",
      "20020",
      "20030"
    },
    GroupId = 2,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "it_2_1_6", Count = 1}
  },
  {
    Id = "20070",
    GroupId = 3,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_3",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_2_4", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20080",
    GroupId = 3,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_2_3", Count = 1},
    Requirement_2 = {Type = "it_2_3_3", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20090",
    GroupId = 3,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_1_2", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20100",
    GroupId = 3,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_1_4", Count = 1},
    Requirement_2 = {Type = "it_1_2_1_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20110",
    GroupId = 4,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {Type = "it_2_3_3", Count = 1}
  },
  {
    Id = "20120",
    GroupId = 4,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20130",
    GroupId = 4,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_4", Count = 1},
    Requirement_2 = {Type = "it_2_2_3", Count = 1}
  },
  {
    Id = "20140",
    GroupId = 4,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_1",
      Count = 2
    }
  },
  {
    Id = "20150",
    GroupId = 5,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_2_1_2", Count = 1},
    Requirement_2 = {Type = "it_2_3_1_2", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20160",
    GroupId = 5,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    }
  },
  {
    Id = "20170",
    GroupId = 5,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20180",
    GroupId = 5,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_1_2", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20190",
    GroupId = 5,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_3_4", Count = 1}
  },
  {
    Id = "20200",
    GroupId = 6,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_1_4", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20210",
    GroupId = 6,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_3_3", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20220",
    GroupId = 6,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_1_6", Count = 2},
    Requirement_2 = {
      Type = "ds_grillmt_3",
      Count = 1
    }
  },
  {
    Id = "20230",
    GroupId = 6,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20240",
    GroupId = 6,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_3_3", Count = 1},
    Requirement_2 = {Type = "it_2_2_3", Count = 1}
  },
  {
    Id = "20250",
    GroupId = 6,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_2_5", Count = 1}
  },
  {
    Id = "20260",
    GroupId = 6,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_6", Count = 1}
  },
  {
    Id = "20270",
    GroupId = 7,
    ChapterId = 2,
    Requirement_1 = {Type = "ds_flb_2", Count = 1},
    Rewards = {
      {Currency = "energy", Amount = 15}
    },
    Flambe = 1
  },
  {
    Id = "20280",
    GroupId = 7,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_3",
      Count = 2
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20290",
    GroupId = 7,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "it_4_1_5", Count = 1}
  },
  {
    Id = "20300",
    GroupId = 7,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20310",
    GroupId = 7,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_1_2", Count = 1},
    Requirement_2 = {Type = "it_1_1_6", Count = 1}
  },
  {
    Id = "20320",
    GroupId = 7,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_7", Count = 1},
    Requirement_2 = {Type = "it_2_1_4", Count = 2}
  },
  {
    Id = "20330",
    GroupId = 7,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_3", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20340",
    GroupId = 8,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_1_7", Count = 2},
    Requirement_2 = {Type = "ds_juice_1", Count = 2},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20350",
    GroupId = 8,
    ChapterId = 2,
    Requirement_1 = {Type = "ds_juice_1", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20360",
    GroupId = 8,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_6", Count = 1},
    Requirement_2 = {Type = "ds_juice_3", Count = 1}
  },
  {
    Id = "20370",
    GroupId = 8,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_4", Count = 1}
  },
  {
    Id = "20380",
    GroupId = 8,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_2_6", Count = 1},
    Requirement_2 = {Type = "ds_juice_4", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20390",
    GroupId = 8,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_3_4", Count = 1},
    Requirement_2 = {Type = "it_2_3_1_2", Count = 1}
  },
  {
    Id = "20400",
    GroupId = 8,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "20410",
    GroupId = 9,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_1_1", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20420",
    GroupId = 9,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_2",
      Count = 1
    }
  },
  {
    Id = "20430",
    GroupId = 9,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_mixdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "20440",
    GroupId = 9,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "20450",
    GroupId = 9,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_2_5", Count = 1}
  },
  {
    Id = "20460",
    GroupId = 9,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_3_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20470",
    GroupId = 9,
    ChapterId = 2,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "20480",
    GroupId = 10,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "20490",
    GroupId = 10,
    ChapterId = 2,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20500",
    GroupId = 10,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_friedmt_1",
      Count = 2
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "20510",
    GroupId = 10,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_3_4", Count = 1},
    Requirement_2 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "20520",
    GroupId = 10,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_1_8", Count = 1},
    Requirement_2 = {Type = "it_1_2_1_2", Count = 1}
  },
  {
    Id = "20530",
    GroupId = 10,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 2
    },
    Requirement_2 = {
      Type = "ds_friedmt_2",
      Count = 2
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20540",
    GroupId = 10,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_3",
      Count = 2
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20550",
    GroupId = 11,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_3_4", Count = 1}
  },
  {
    Id = "20560",
    GroupId = 11,
    ChapterId = 2,
    Requirement_1 = {Type = "it_4_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_4",
      Count = 2
    }
  },
  {
    Id = "20570",
    GroupId = 11,
    ChapterId = 2,
    Requirement_1 = {Type = "it_1_2_6", Count = 1},
    Requirement_2 = {Type = "it_1_1_1_2", Count = 1}
  },
  {
    Id = "20580",
    GroupId = 11,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_mixdrk_2",
      Count = 2
    },
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "20590",
    GroupId = 11,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_3_1_3", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "20600",
    GroupId = 11,
    ChapterId = 2,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "20610",
    GroupId = 11,
    ChapterId = 2,
    Requirement_1 = {
      Type = "ds_mixdrk_5",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  }
}
