ItemEventType = {SetPosition = 1}
ItemModel = {}
ItemModel.__index = ItemModel

function ItemModel.Create(boardModel, position, itemConfig, originalCode)
  local itemModel = setmetatable({}, ItemModel)
  itemModel:_Init(boardModel, position, itemConfig, originalCode)
  return itemModel
end

function ItemModel:_Init(boardModel, position, itemConfig, originalCode)
  self.event = PairEvent.Create(self)
  self.m_components = {}
  self.m_updateComponents = {}
  self.costEnergy = 0
  self.costEnergyCurDay = 0
  self.shopGemCost = 0
  self.bubbleGemCost = 0
  self.cookGemCost = 0
  self.cookSkipPropCost = 0
  self.m_boardModel = boardModel
  self.m_position = position
  self.m_type = itemConfig.Type
  self.m_mergedType = itemConfig.MergedType
  self.m_bubbleChance = itemConfig.BubbleChance
  self.m_sellPrice = itemConfig.SellCurrency
  self.m_code = originalCode
end

function ItemModel:FromSerialization(dbTable)
  self.m_id = dbTable.id
  self.locked = dbTable.locked
  self:SetCost(dbTable)
  self:DispatchComponentEvent("FromSerialization", dbTable)
end

function ItemModel:ToSerialization(dbTable)
  dbTable.codeStr = self.m_code
  dbTable.costEnergy = self.costEnergy
  dbTable.costEnergyCurDay = self.costEnergyCurDay
  dbTable.shopGemCost = self.shopGemCost
  dbTable.bubbleGemCost = self.bubbleGemCost
  dbTable.cookGemCost = self.cookGemCost
  dbTable.cookSkipPropCost = self.cookSkipPropCost
  dbTable.locked = self.locked
  self:DispatchComponentEvent("ToSerialization", dbTable)
end

function ItemModel:SetCost(cost)
  self.costEnergy = cost.costEnergy or self.costEnergy
  self.costEnergyCurDay = cost.costEnergyCurDay or self.costEnergyCurDay
  self.shopGemCost = cost.shopGemCost or self.shopGemCost
  self.bubbleGemCost = cost.bubbleGemCost or self.bubbleGemCost
  self.cookGemCost = cost.cookGemCost or self.cookGemCost
  self.cookSkipPropCost = cost.cookSkipPropCost or self.cookSkipPropCost
end

function ItemModel:ClearCookCost()
  self.cookSkipPropCost = 0
  self.cookGemCost = 0
  self:GetBoardModel():SaveItemProperty(self)
end

function ItemModel:AddComponent(component)
  component:SetItemModel(self)
  self.m_components[getmetatable(component)] = component
  if component.NeedUpdate and component:NeedUpdate() and component.Update then
    self.m_updateComponents[getmetatable(component)] = component
    self.m_bComponentNeedUpdate = true
  end
end

function ItemModel:RemoveComponent(component)
  component:Destroy()
  self.m_components[getmetatable(component)] = nil
  self.m_updateComponents[getmetatable(component)] = nil
  self.m_bComponentNeedUpdate = next(self.m_updateComponents) ~= nil
end

function ItemModel:GetComponent(type)
  return self.m_components[type]
end

function ItemModel:DispatchComponentUpdateEvent()
  if not self.m_bComponentNeedUpdate then
    return
  end
  local lockerCmp = self:GetComponent(ItemLocker)
  if lockerCmp ~= nil then
    return
  end
  for _, component in pairs(self.m_updateComponents) do
    component.Update(component)
  end
end

function ItemModel:DispatchComponentEvent(functionName, ...)
  local lockerCmp = self:GetComponent(ItemLocker)
  if lockerCmp ~= nil and functionName ~= "Destroy" then
    lockerCmp[functionName](lockerCmp, ...)
    return
  end
  for _, component in pairs(self.m_components) do
    component[functionName](component, ...)
  end
end

function ItemModel:Destroy()
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
  self:DispatchComponentEvent("Destroy")
end

function ItemModel:GetBoardModel()
  return self.m_boardModel
end

function ItemModel:GetGameMode()
  return self.m_boardModel:GetGameMode()
end

function ItemModel:GetPosition()
  return self.m_position
end

function ItemModel:IsInBoard()
  return self.m_boardModel:GetItem(self.m_position) == self
end

function ItemModel:SetPosition(position, withoutNotify)
  self.m_position = position
  if not withoutNotify then
    self.event:Call(ItemEventType.SetPosition, position)
  end
end

function ItemModel:GetType()
  return self.m_type
end

function ItemModel:GetMergedType()
  return self.m_mergedType
end

function ItemModel:GetBubbleChance()
  return self.m_bubbleChance
end

function ItemModel:GetSellPrice()
  return self.m_sellPrice
end

function ItemModel:GetCode()
  return self.m_code
end

function ItemModel:GetId()
  return self.m_id
end

function ItemModel:SetId(id)
  self.m_id = id
end

function ItemModel:IsShowCollectEffect()
  return self.m_type == ItemType.gold_5 or self.m_type == ItemType.gem_4 or self.m_type == ItemType.ene_5 or self.m_type == ItemType.Lollipop05 or self.m_type == ItemType.ma_balloon_4 or self.m_type == ItemType.skipprop_5
end

function ItemModel:IsBoosterType()
  local booster = self:GetComponent(ItemBooster)
  local split = self:GetComponent(ItemSplit)
  return booster ~= nil or split ~= nil
end

function ItemModel:GetSpriteName()
  local spriteName
  local itemCobweb = self:GetComponent(ItemCobweb)
  if itemCobweb ~= nil then
    spriteName = GM.ItemDataModel:GetSpriteName(itemCobweb:GetInnerItemCode())
  else
    spriteName = GM.ItemDataModel:GetSpriteName(self:GetType())
  end
  local itemBubble = self:GetComponent(ItemBubble)
  if itemBubble ~= nil then
    spriteName = GM.ItemDataModel:GetSpriteName(itemBubble:GetInnerItemCode())
  end
  local itemRewardBubble = self:GetComponent(ItemRewardBubble)
  if itemRewardBubble ~= nil then
    spriteName = GM.ItemDataModel:GetSpriteName(itemRewardBubble:GetInnerItemCode())
  end
  return spriteName
end

function ItemModel:GetCookEquipmentAudio()
  local itemCook = self:GetComponent(ItemCook)
  if itemCook and itemCook:GetState() == EItemCookState.Cooking then
    local recipe = itemCook:GetRecipe()
    local specialAnim = GM.ItemDataModel:GetModelConfig(recipe).SpecialAnim
    if specialAnim then
      local specialAudioName = specialAnim .. "_audio"
      if AudioFileConfigName.HasConfig(specialAudioName) then
        return specialAudioName
      end
    end
    local chainId = GM.ItemDataModel:GetChainId(self:GetType())
    local audioName = ECookEquipmentEffectName[chainId]
    return audioName
  end
end

function ItemModel:GetTransformAudio()
  local audioName = self.m_code .. "_transform"
  if AudioFileConfigName.HasConfig(audioName) then
    return audioName
  end
  return nil
end

function ItemModel:IsEmptyInstrument()
  local itemCook = self:GetComponent(ItemCook)
  return itemCook and itemCook:GetState() == EItemCookState.Empty
end

function ItemModel:CanShowMaxLevelIcon()
  if self:GetComponent(ItemBubble) == nil and self:GetComponent(ItemRewardBubble) == nil and self:GetComponent(ItemPaperBox) == nil and self:GetComponent(ItemCobweb) == nil and self:GetComponent(ItemSplit) == nil and not GM.ItemDataModel:IsDishes(self:GetType()) and self.m_mergedType == nil then
    return true
  end
  return false
end
