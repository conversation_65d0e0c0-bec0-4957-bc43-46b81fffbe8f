ItemSpineView = {}
ItemSpineView.__index = ItemSpineView
local IDLE_ANIM = "idle"

function ItemSpineView:Init()
  self[self.hideEffect]:SetActive(false)
  self.spineAnimation:Init()
  if self.spineAnimation:HasSetInitAnim() then
    Log.Error(self.gameObject.name .. "棋子spine不要设置初始动画！")
  end
  if self.extraSpineAnimation then
    self.extraSpineAnimation:Init()
    if self.extraSpineAnimation:HasSetInitAnim() then
      Log.Error(self.gameObject.name .. "棋子spine不要设置初始动画！")
    end
  end
  self:PlayIdleAnimation()
end

function ItemSpineView:SetOpacity(opacity)
  self.spineAnimation:SetOpacity(opacity)
  if self.extraSpineAnimation then
    self.extraSpineAnimation:SetOpacity(opacity)
  end
end

function ItemSpineView:PlayIdleAnimation(idleAnimName)
  idleAnimName = idleAnimName or IDLE_ANIM
  if self.spineAnimation:GetCurrentRunningAnimName() == idleAnimName or self.spineAnimation:GetCurrentRunningAnimName() == idleAnimName .. "_still" then
    return
  end
  self.spineAnimation:ThrowCallback()
  self:_SwitchEffect(idleAnimName)
  self.spineAnimation:PlayAnimation(idleAnimName)
  if self.extraSpineAnimation then
    self.extraSpineAnimation:PlayAnimation(idleAnimName)
  end
end

function ItemSpineView:PlayLoopAnimation(animName)
  self:_SwitchEffect(animName)
  self.spineAnimation:PlayAnimation(animName, nil, true, false)
  if self.extraSpineAnimation then
    self.extraSpineAnimation:PlayAnimation(animName, nil, true, false)
  end
end

function ItemSpineView:PlayAnimThenIdle(animName, idleAnimName)
  self.spineAnimation:PlayAnimation(animName, function()
    self:PlayIdleAnimation(idleAnimName)
  end, false, false)
  if self.extraSpineAnimation then
    self.extraSpineAnimation:PlayAnimation(animName, nil, false, false)
  end
end

function ItemSpineView:_SwitchEffect(animName)
  if self.m_showEffect ~= nil then
    self.m_showEffect:SetActive(false)
    self.m_showEffect = nil
  end
  local effectName = "m_" .. animName .. "Effect"
  if self[effectName] ~= nil then
    self.m_showEffect = self[effectName]
    self.m_showEffect:SetActive(true)
  end
end

function ItemSpineView:HasAnimation(animName)
  return self.spineAnimation:IsValidAnimName(animName)
end
