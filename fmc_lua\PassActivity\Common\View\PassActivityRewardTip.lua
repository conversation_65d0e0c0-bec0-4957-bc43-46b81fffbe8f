PassActivityRewardTip = {}
PassActivityRewardTip.__index = PassActivityRewardTip

function PassActivityRewardTip:Init(activityType, descriptionKey)
  self.m_activityDefinition = PassActivityDefinition[activityType]
  self.m_descriptionText.text = GM.GameTextModel:GetText(descriptionKey)
  self.m_currentSelected = CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject
end

function PassActivityRewardTip:Update()
  if self.m_currentSelected ~= CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject then
    local mainWindow = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
    if mainWindow ~= nil then
      mainWindow:ForceHideRewardTip()
    end
  end
end
