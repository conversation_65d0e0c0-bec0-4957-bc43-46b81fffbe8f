CrossPromotionWindow = setmetatable({}, BaseWindow)
CrossPromotionWindow.__index = CrossPromotionWindow

function CrossPromotionWindow:Init()
  self.m_bAcquireReward = false
  local arrTaskEntities = GM.CrossPromotionModel:GetTasks()
  for index, taskEntity in ipairs(arrTaskEntities) do
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.CPTaskCell), self.m_scrollRect.content, Vector3.zero, function(go)
      local tbTaskCell = go:GetLuaTable()
      tbTaskCell:Init(self, taskEntity)
    end)
  end
  GM.CrossPromotionModel:LoadUrlImageByCurLanguage(self.m_mainPicImage, GM.CrossPromotionModel.mainPic, GM.CrossPromotionModel:GetMainPicDefault())
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.UserClick
  })
  self:UpdatePerSecond()
  GM.CrossPromotionModel:SetCPWinOpended()
  GM.CrossPromotionModel:RefreshActivityButtonInNeed()
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_timeLayoutGroupRectTrans)
end

function CrossPromotionWindow:UpdatePerSecond()
  local remainTime = GM.CrossPromotionModel:GetEndTime() - GM.GameModel:GetServerTime()
  if remainTime <= 0 then
    self:Close()
    return
  end
  local timeStr = TimeUtil.ParseTimeDescription(math.max(0, remainTime), 3)
  self.m_timeText.text = timeStr
end

function CrossPromotionWindow:OnPlayClick()
  self:Close()
  GM.CrossPromotionModel:Go2NewGame()
end

function CrossPromotionWindow:SetAcquireReward()
  self.m_bAcquireReward = true
end

function CrossPromotionWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  if self.m_bAcquireReward then
    GM.SyncModel:CheckUpload()
  end
end
