StoryDataModel = {}
StoryDataModel.__index = StoryDataModel

function StoryDataModel:Init()
  self.m_mapChapterStoryData = {}
  self.m_mapSpecialStoryData = {}
end

function StoryDataModel:_LoadConfigs(chapterName, callback)
  if self.m_mapChapterStoryData[chapterName] then
    callback(self.m_mapChapterStoryData[chapterName])
    return
  end
  GM.UIManager:SetEventLock(true)
  local path = "DynamicConfig.Mainline." .. tostring(chapterName) .. ".StoryConfig_" .. tostring(chapterName)
  GM.ConfigModel:LoadDynamicConfigAsync(path, function(tb)
    GM.UIManager:SetEventLock(false)
    if not tb then
      GM.BIManager:LogErrorInfo("cs_ld_er", chapterName)
      CS.CSErrorMonitor.OpenForceRestartWindow("force_restart_desc")
      self.m_mapChapterStoryData[chapterName] = {}
      callback(self.m_mapChapterStoryData[chapterName])
      return
    end
    local mapStoryData = self:_ParseConfigs(tb)
    self.m_mapChapterStoryData[chapterName] = mapStoryData
    callback(mapStoryData)
  end)
end

function StoryDataModel:_LoadSpecialConfigs(prefix, callback)
  if self.m_mapSpecialStoryData[prefix] then
    callback(self.m_mapSpecialStoryData[prefix])
    return
  end
  GM.UIManager:SetEventLock(true)
  local path = "DynamicConfig.Special.Story." .. tostring(prefix) .. ".StoryConfig_" .. tostring(prefix)
  GM.ConfigModel:LoadDynamicConfigAsync(path, function(tb)
    GM.UIManager:SetEventLock(false)
    if not tb then
      GM.BIManager:LogErrorInfo("ss_ld_er", prefix)
      CS.CSErrorMonitor.OpenForceRestartWindow("force_restart_desc")
      self.m_mapSpecialStoryData[prefix] = {}
      callback(self.m_mapSpecialStoryData[prefix])
      return
    end
    local mapStoryData = self:_ParseConfigs(tb, prefix)
    self.m_mapSpecialStoryData[prefix] = mapStoryData
    callback(mapStoryData)
  end)
end

function StoryDataModel:_ParseConfigs(configs, specialPrefix)
  local mapStoryData = {}
  local storyData
  for i = 1, #configs do
    storyData = StoryData.Create(configs[i], specialPrefix)
    if not mapStoryData[storyData.Id] then
      mapStoryData[storyData.Id] = {}
    end
    table.insert(mapStoryData[storyData.Id], storyData)
  end
  return mapStoryData
end

function StoryDataModel:_GetMainlineStoryData(chapterName, storyId, callback)
  self:_LoadConfigs(chapterName, function(mapStoryData)
    local storyData = mapStoryData[storyId]
    if not storyData then
      Log.Error("没有找到对应的剧情配置 storyId:" .. tostring(storyId))
    end
    if callback then
      callback(storyData)
    end
  end)
end

function StoryDataModel:_GetSpecialStoryData(prefix, storyId, callback)
  self:_LoadSpecialConfigs(prefix, function(mapStoryData)
    local storyData = mapStoryData[storyId]
    if not storyData then
      Log.Error("没有找到对应的时间线配置 storyId:" .. tostring(storyId))
    end
    if callback then
      callback(storyData)
    end
  end)
end

function StoryDataModel:GetStoryData(storyId, isSpecial, callback)
  if isSpecial then
    local prefix = StringUtil.Split(storyId, "_")[1]
    GM.GameTextModel:LoadSpecialStoryText(prefix, function()
      self:_GetSpecialStoryData(prefix, storyId, callback)
    end)
  else
    self:_GetMainlineStoryData(GM.ChapterManager.curActiveChapterName, storyId, callback)
  end
end

function StoryDataModel:GetRoleName(speaker)
  return GM.GameTextModel:GetText(tostring(speaker) .. "_name")
end
