LevelUpPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  },
  canIgnorePopup = false
}, BasePopupHelper)
LevelUpPopupHelper.__index = LevelUpPopupHelper

function LevelUpPopupHelper.Create()
  local helper = setmetatable({}, LevelUpPopupHelper)
  helper:Init()
  return helper
end

function LevelUpPopupHelper:Init()
  BasePopupHelper.Init(self)
  EventDispatcher.AddListener(EEventType.LevelUp, self, self._OnLevelUp)
  EventDispatcher.AddListener(EEventType.PropertyAcquired, self, self.OnPropertyAcquired)
end

function LevelUpPopupHelper:OnPropertyAcquired(message)
  for _, item in ipairs(message.arrProperties) do
    if item[PROPERTY_TYPE] == EPropertyType.Experience then
      self.m_gotExp = true
    end
  end
end

function LevelUpPopupHelper:_OnLevelUp(level)
  self.m_newLevel = level
end

function LevelUpPopupHelper:NeedCheckPopup()
  return self.m_gotExp or self.m_newLevel ~= nil
end

function LevelUpPopupHelper:CheckPopup()
  if self.m_gotExp then
    self.m_gotExp = false
    GM.LevelModel:TryLevelUp()
  end
  if self.m_newLevel then
    local newLevel = self.m_newLevel
    self.m_newLevel = nil
    return nil
  end
end
