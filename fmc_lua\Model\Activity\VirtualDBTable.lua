function VIRTUAL_DEFINE_SYNC_DATA(model, name, value)
  local variableName = "m_" .. string.lower(string.sub(name, 1, 1)) .. string.sub(name, 2)
  
  model[variableName] = value
  model["Get" .. name] = function(model)
    return model[variableName]
  end
  model["Sync" .. name] = function(model, dataArray)
    value:Clear()
    local contents = {}
    for _, data in ipairs(dataArray) do
      local index = string.find(data, ",")
      local name = string.sub(data, 1, index - 1)
      local data = string.sub(data, index + 1)
      contents[name] = {data = data}
    end
    value:BatchSet(contents)
  end
end

VirtualDBTable = setmetatable({}, BaseDBTable)
VirtualDBTable.__index = VirtualDBTable

function VirtualDBTable.Create(dbTable, name)
  local kvTable = setmetatable({}, VirtualDBTable)
  kvTable:Init(dbTable, name)
  return kvTable
end

function VirtualDBTable:Init(dbTable, name)
  self.m_dbTable = dbTable
  self.m_dbTable.m_bIgnoreValueCommaCheck = true
  self.m_name = name
  local data = self.m_dbTable:GetValues()[name]
  if data ~= nil then
    self.m_data = json.decode(data.data)
    for key, value in pairs(self.m_data) do
      if type(value) ~= "table" then
        self.m_data[key] = {value = value}
      end
    end
  end
end

function VirtualDBTable:IsEmpty()
  return self.m_data == nil or next(self.m_data) == nil
end

function VirtualDBTable:GetValue(primaryValue, column)
  if self.m_data == nil then
    return nil
  end
  local row = self.m_data[primaryValue]
  if row == nil then
    return nil
  end
  return row[column]
end

function VirtualDBTable:GetValues()
  if self.m_data == nil then
    return {}
  end
  return Table.DeepCopy(self.m_data)
end

function VirtualDBTable:Set(primaryValue, column, columnValue)
  if self.m_data == nil then
    self.m_data = {}
  end
  if self.m_data[primaryValue] == nil then
    self.m_data[primaryValue] = {}
  end
  self.m_data[primaryValue][column] = columnValue
  self.m_dbTable:Set(self.m_name, "data", json.encode(self.m_data))
end

function VirtualDBTable:BatchSet(primaryColumnsMap)
  if self.m_data == nil then
    self.m_data = {}
  end
  for primaryValue, columns in pairs(primaryColumnsMap) do
    if self.m_data[primaryValue] == nil then
      self.m_data[primaryValue] = {}
    end
    for column, value in pairs(columns) do
      self.m_data[primaryValue][column] = value
    end
  end
  self.m_dbTable:Set(self.m_name, "data", json.encode(self.m_data))
end

function VirtualDBTable:Remove(primaryValue, column)
  if self.m_data == nil or self.m_data[primaryValue] == nil then
    return
  end
  if column == nil then
    self.m_data[primaryValue] = nil
  else
    self.m_data[primaryValue][column] = nil
  end
  self.m_dbTable:Set(self.m_name, "data", json.encode(self.m_data))
end

function VirtualDBTable:Drop()
  self.m_data = nil
  self.m_dbTable:Remove(self.m_name)
end
