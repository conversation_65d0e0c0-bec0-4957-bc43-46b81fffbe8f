UrlUtil = {}
UrlUtil.__index = UrlUtil

function UrlUtil.FillUserInfoToLink(strLink)
end

function UrlUtil.Decode(strToDecode)
  local strResult = ""
  local iHex = 0
  local uLen = string.len(strToDecode)
  for i = 1, uLen do
    local char = string.sub(strToDecode, i, i)
    if char == "+" then
      strResult = strResult .. " "
    elseif char == "%" then
      if UrlUtil.IsXDigit(string.sub(strToDecode, i + 1, i + 1)) and UrlUtil.IsXDigit(string.sub(strToDecode, i + 2, i + 2)) then
        local strHex = string.sub(strToDecode, i + 1, i + 2)
        local iHex = tonumber(strHex, 16)
        local bIsReservedWord = iHex == 33 or iHex == 36 or 38 <= iHex and iHex <= 47 or iHex == 58 or iHex == 59 or iHex == 61 or iHex == 63 or iHex == 64 or iHex == 95
        if bIsReservedWord then
          strResult = strResult .. string.char(iHex)
          i = i + 2
        elseif (not (48 <= iHex) or not (iHex <= 57)) and (not (65 <= iHex) or not (iHex <= 90)) and (not (97 <= iHex) or not (iHex <= 122)) and not bIsReservedWord then
          strResult = strResult .. string.char(iHex)
          i = i + 2
        else
          strResult = strResult .. "%"
        end
      else
        strResult = strResult .. "%"
      end
    else
      strResult = strResult .. char
    end
  end
  return strResult
end

function UrlUtil.Encode(strToEncode)
end

function UrlUtil.IsXDigit(strDigit)
  if not IsString(strDigit) then
    return false
  end
  local uLen = string.len(strDigit)
  if uLen == 0 or 1 < uLen then
    return false
  end
  local strUpper = string.upper(strDigit)
  if strUpper == "0" or strUpper == "1" or strUpper == "2" or strUpper == "3" or strUpper == "4" or strUpper == "5" or strUpper == "6" or strUpper == "7" or strUpper == "8" or strUpper == "9" or strUpper == "A" or strUpper == "B" or strUpper == "C" or strUpper == "D" or strUpper == "E" or strUpper == "F" then
    return true
  end
  return false
end

function UrlUtil.GetUrlParameterMap(strUrl)
  if StringUtil.IsNilOrEmpty(strUrl) or not string.find(strUrl, "%?") then
    return nil
  end
  local uIndex = string.find(strUrl, "%?")
  local strParameter = string.sub(strUrl, uIndex + 1)
  local arrParameters = StringUtil.Split(strParameter, "&")
  local mapParameters = {}
  for _, v in ipairs(arrParameters) do
    local uIndex = string.find(v, "=")
    if uIndex then
      mapParameters[string.sub(v, 1, uIndex - 1)] = string.sub(v, uIndex + 1)
    end
  end
  return mapParameters
end
