BaseUIItemIllustratedBookModel = {}
BaseUIItemIllustratedBookModel.__index = BaseUIItemIllustratedBookModel
local strKey = "UIItemIllustratedBookSaveCache"
BaseUIItemIllustratedBookModel.ItemState = {
  Lock = 0,
  UnLock = 1,
  Acquire = 2
}
BaseUIItemIllustratedBookModel.EventType = {Refreshed = 0, AddCacheItem = 1}

function BaseUIItemIllustratedBookModel.Create(uiBoardModel, config, dbTable)
  local model = setmetatable({}, BaseUIItemIllustratedBookModel)
  model:Init(uiBoardModel, config, dbTable)
  return model
end

function BaseUIItemIllustratedBookModel:Init(uiBoardModel, config, dBTable)
  self.m_dbTable = dBTable
  self.m_uiBoardModel = uiBoardModel
  self.m_activityType = self.m_uiBoardModel:GetActivityType()
  self.m_activityModel = GM.ActivityManager:GetModel(self.m_activityType)
  self.m_configs = {}
  self.m_finalReward = nil
  for _, info in pairs(config) do
    if info.index == 0 then
      self.m_finalReward = info.reward
    else
      table.insert(self.m_configs, info)
      if GameConfig.IsTestMode() and info.itemReward == nil then
        Log.Error("[小棋盘棋子图鉴] 棋子图鉴奖励不允许为空, 请检查!")
      end
    end
  end
  table.sort(self.m_configs, function(a, b)
    return a.index < b.index
  end)
  self.event = PairEvent.Create()
  self:UpdateCacheInfo()
end

function BaseUIItemIllustratedBookModel:UpdateCacheInfo()
  self.m_cacheInfo = {}
  if self.m_configs == nil then
    return
  end
  for _, info in pairs(self.m_configs) do
    local eleCache = {}
    eleCache.index = info.index
    eleCache.config = info
    eleCache.isFinish = false
    local dbItemInfos = json.decode(self.m_dbTable:GetValue(strKey .. tostring(info.index), "value") or "")
    if dbItemInfos ~= nil then
      eleCache.itemInfos = dbItemInfos
      eleCache.isFinish = true
      for _, eleitemInfo in ipairs(eleCache.itemInfos) do
        if eleitemInfo.state ~= BaseUIItemIllustratedBookModel.ItemState.Acquire then
          eleCache.isFinish = false
          break
        end
      end
      local mapCode2Index = {}
      for index, itemInfo in ipairs(eleCache.itemInfos) do
        mapCode2Index[itemInfo.itemCode] = index
      end
      eleCache.mapCode2Index = mapCode2Index
    else
      local items = {}
      local mapCode2Index = {}
      local chainId = info.chainId
      local chainMaxLevel = GM.ItemDataModel:GetChainMaxLevel(chainId) or 0
      for i = 1, chainMaxLevel do
        local itemCode = chainId .. "_" .. i
        local eleItemInfo = {}
        eleItemInfo.itemCode = itemCode
        eleItemInfo.state = BaseUIItemIllustratedBookModel.ItemState.Lock
        table.insert(items, eleItemInfo)
        mapCode2Index[itemCode] = #items
      end
      eleCache.itemInfos = items
      eleCache.mapCode2Index = mapCode2Index
    end
    self.m_cacheInfo[info.index] = eleCache
  end
end

function BaseUIItemIllustratedBookModel:Drop()
  self.m_configs = nil
  self.m_cacheInfo = nil
  self.m_finalReward = nil
end

function BaseUIItemIllustratedBookModel:AddCacheItem(itemCode, boardmodel, position)
  if self.m_cacheInfo == nil then
    return
  end
  if GM.ItemDataModel:GetModelConfig(itemCode, true) == nil then
    return
  end
  local chainId = GM.ItemDataModel:GetChainId(itemCode)
  if chainId == nil then
    return
  end
  local isFinish = false
  for _, info in pairs(self.m_cacheInfo) do
    if chainId == info.config.chainId then
      local isNewUnlock = false
      for _, itemInfo in pairs(info.itemInfos) do
        if itemInfo.itemCode == itemCode and itemInfo.state == BaseUIItemIllustratedBookModel.ItemState.Lock then
          itemInfo.state = BaseUIItemIllustratedBookModel.ItemState.UnLock
          self:SaveCacheInfo(info.index)
          isNewUnlock = true
          if info.config.reward ~= nil then
            self.event:Call(BaseUIItemIllustratedBookModel.EventType.AddCacheItem, {
              boardmodel = boardmodel,
              position = position,
              itemCode = itemCode
            })
          end
          if self.m_activityModel ~= nil and not self.m_activityModel:IsMainLineItem(itemCode) then
            GM.BIManager:LogActivity(self.m_activityType .. "_" .. chainId, self.m_activityModel:GetId(), EBIType.ActivityRankUp, GM.ItemDataModel:GetChainLevel(itemCode))
          end
          break
        end
      end
      if isNewUnlock then
        isFinish = info.config.reward ~= nil
        for _, itemInfo in pairs(info.itemInfos) do
          if itemInfo.state == BaseUIItemIllustratedBookModel.ItemState.Lock then
            isFinish = false
            break
          end
        end
      end
      break
    end
  end
  self.event:Call(BaseUIItemIllustratedBookModel.EventType.Refreshed, {isFinish = isFinish})
end

function BaseUIItemIllustratedBookModel:AcquireCacheItemInfo(itemCode)
  if self.m_cacheInfo == nil then
    return
  end
  local reward, groupReward, finialReward
  local chainId = GM.ItemDataModel:GetChainId(itemCode)
  for _, info in pairs(self.m_cacheInfo) do
    if chainId == info.config.chainId then
      for _, itemInfo in pairs(info.itemInfos) do
        if itemInfo.itemCode == itemCode and itemInfo.state == BaseUIItemIllustratedBookModel.ItemState.UnLock then
          itemInfo.state = BaseUIItemIllustratedBookModel.ItemState.Acquire
          reward = info.config.itemReward
          self:SaveCacheInfo(info.index)
          break
        end
      end
      if reward ~= nil then
        local bFinal = true
        for _, itemInfo in pairs(info.itemInfos) do
          if itemInfo.state ~= BaseUIItemIllustratedBookModel.ItemState.Acquire then
            bFinal = false
            break
          end
        end
        if bFinal then
          info.isFinish = true
          groupReward = info.config.reward
        end
      end
      break
    end
  end
  local bFinalReward
  if reward ~= nil and self.m_finalReward ~= nil then
    local bFinal = true
    for _, info in pairs(self.m_cacheInfo) do
      if info.isFinish ~= true then
        bFinal = false
      end
    end
    if bFinal then
      bFinalReward = self.m_finalReward
    end
  end
  if reward ~= nil then
    RewardApi.CryptRewards(reward, true)
    RewardApi.AcquireRewardsLogic(reward, EPropertySource.Give, EBIType.BookItemReward, EGameMode.Board, CacheItemType.Stack)
  end
  if groupReward ~= nil then
    RewardApi.CryptRewards(groupReward)
    RewardApi.AcquireRewardsLogic(groupReward, EPropertySource.Give, EBIType.BookSeriesReward .. chainId, EGameMode.Board, CacheItemType.Stack)
  end
  if bFinalReward ~= nil then
    RewardApi.CryptRewards(bFinalReward)
    RewardApi.AcquireRewardsLogic(bFinalReward, EPropertySource.Give, EBIType.BookFinalReward, EGameMode.Board, CacheItemType.Stack)
  end
  self.event:Call(BaseUIItemIllustratedBookModel.EventType.Refreshed)
  return reward, groupReward, bFinalReward
end

function BaseUIItemIllustratedBookModel:SaveCacheInfo(index)
  local saveInfoStr = json.encode(self.m_cacheInfo[index].itemInfos)
  self.m_dbTable:Set(strKey .. tostring(index), "value", saveInfoStr)
end

function BaseUIItemIllustratedBookModel:GetCacheInfo()
  return self.m_cacheInfo
end

function BaseUIItemIllustratedBookModel:GetItemInfo(itemCode)
  if Table.IsEmpty(self.m_cacheInfo) then
    return
  end
  for _, cacheInfo in ipairs(self.m_cacheInfo) do
    if cacheInfo.mapCode2Index[itemCode] ~= nil then
      return cacheInfo.itemInfos[cacheInfo.mapCode2Index[itemCode]]
    end
  end
end

function BaseUIItemIllustratedBookModel:GetFinalReward()
  return self.m_finalReward
end

function BaseUIItemIllustratedBookModel:GetProgress()
  local curProgress = 0
  local maxProgress = 0
  local unLockProgress = 0
  for _, info in pairs(self.m_cacheInfo) do
    for _, itemInfo in pairs(info.itemInfos) do
      if itemInfo.state == BaseUIItemIllustratedBookModel.ItemState.Acquire then
        curProgress = curProgress + 1
      end
      if itemInfo.state ~= BaseUIItemIllustratedBookModel.ItemState.Lock then
        unLockProgress = unLockProgress + 1
      end
      maxProgress = maxProgress + 1
    end
  end
  return curProgress, maxProgress, unLockProgress
end

function BaseUIItemIllustratedBookModel:GetEvent()
  return self.event
end

function BaseUIItemIllustratedBookModel:CanShowRedPoint()
  for _, info in pairs(self.m_cacheInfo) do
    for _, itemInfo in pairs(info.itemInfos) do
      if itemInfo.state == BaseUIItemIllustratedBookModel.ItemState.UnLock then
        return true
      end
    end
  end
  return false
end

function BaseUIItemIllustratedBookModel:OnTestUnlockAllItem()
  for _, info in pairs(self.m_cacheInfo) do
    for _, itemInfo in pairs(info.itemInfos) do
      if itemInfo.state == BaseUIItemIllustratedBookModel.ItemState.Lock then
        itemInfo.state = BaseUIItemIllustratedBookModel.ItemState.UnLock
      end
    end
    self:SaveCacheInfo(info.index)
  end
end

function BaseUIItemIllustratedBookModel:OnTestlockAllItem()
  for _, info in pairs(self.m_cacheInfo) do
    info.isFinish = false
    for _, itemInfo in pairs(info.itemInfos) do
      itemInfo.state = BaseUIItemIllustratedBookModel.ItemState.Lock
    end
    self:SaveCacheInfo(info.index)
  end
end

function BaseUIItemIllustratedBookModel:CalculateItemIllustreatedItemReward()
  local Rewards = {}
  local isAllFinish
  for _, info in pairs(self.m_cacheInfo) do
    local isSetFinish = true
    if info.isFinish == false then
      if isAllFinish == nil then
        isAllFinish = true
      end
      for _, itemInfo in pairs(info.itemInfos) do
        if itemInfo.state == BaseUIItemIllustratedBookModel.ItemState.UnLock then
          Table.ListAppend(Rewards, info.config.itemReward)
        elseif itemInfo.state == BaseUIItemIllustratedBookModel.ItemState.Lock then
          isAllFinish = false
          isSetFinish = false
        end
      end
    else
      isSetFinish = false
    end
    if isSetFinish and info.config.reward ~= nil then
      Table.ListAppend(Rewards, info.config.reward)
    end
  end
  if isAllFinish and self.m_finalReward ~= nil then
    Table.ListAppend(Rewards, self.m_finalReward)
  end
  return Rewards
end

function BaseUIItemIllustratedBookModel:GetBITilteProgress()
  local progress = {}
  for _, info in pairs(self.m_cacheInfo) do
    progress[info.config.chainId] = {}
    local curUnLock = 0
    local curLock = 0
    local curAcquire = 0
    for _, iteminfo in ipairs(info.itemInfos) do
      if iteminfo.state == BaseUIItemIllustratedBookModel.ItemState.UnLock then
        curUnLock = curUnLock + 1
      elseif iteminfo.state == BaseUIItemIllustratedBookModel.ItemState.Lock then
        curLock = curLock + 1
      else
        curAcquire = curAcquire + 1
      end
    end
    progress[info.config.chainId].curUnLock = curUnLock
    progress[info.config.chainId].curLock = curLock
    progress[info.config.chainId].curAcquire = curAcquire
  end
  return progress
end

function BaseUIItemIllustratedBookModel:CheckItemCodeNoUnlockByCode(itemCode)
  local chainId = GM.ItemDataModel:GetChainId(itemCode)
  for _, info in pairs(self.m_cacheInfo) do
    if chainId == info.config.chainId then
      for _, itemInfo in pairs(info.itemInfos) do
        if itemInfo.state == BaseUIItemIllustratedBookModel.ItemState.UnLock then
          return false
        end
      end
      break
    end
  end
  return true
end

function BaseUIItemIllustratedBookModel:HasAcquireAllChainItemReward(index)
  local info = self.m_cacheInfo[index]
  for _, itemInfo in pairs(info.itemInfos) do
    if itemInfo.state ~= BaseUIItemIllustratedBookModel.ItemState.Acquire then
      return false
    end
  end
  return true
end

function BaseUIItemIllustratedBookModel:GetCanClaimRewardItemByIndex(index)
  local cacheInfo = self.m_cacheInfo[index]
  for index, itemInfo in ipairs(cacheInfo.itemInfos) do
    if itemInfo.state == BaseUIItemIllustratedBookModel.ItemState.UnLock then
      return itemInfo.itemCode, index
    end
  end
  return nil, nil
end

function BaseUIItemIllustratedBookModel:GetCanClaimRewardItem()
  local itemCode, itemIndex
  for index, cacheInfo in ipairs(self.m_cacheInfo) do
    itemCode, itemIndex = self:GetCanClaimRewardItemByIndex(index)
    if itemIndex ~= nil then
      return index, itemIndex
    end
  end
end
