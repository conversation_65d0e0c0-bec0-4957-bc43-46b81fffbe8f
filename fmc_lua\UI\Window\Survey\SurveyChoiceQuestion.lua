SurveyChoiceQuestion = setmetatable({}, BaseSurveyQuestion)
SurveyChoiceQuestion.__index = SurveyChoiceQuestion

function SurveyChoiceQuestion:Init(questionData)
  self.m_questionData = questionData
  self.m_arrChoice = {}
  self.m_title.text = self.m_questionData.title
  self.m_type = self.m_questionData.type
  local bFixedSeq = self.m_questionData.fixedSeq
  local options = self.m_questionData.options
  if not bFixedSeq then
    options = Table.ListAlwaysRandomSelectN(options, #options)
  end
  local choiceGo, choiceCmp
  local parent = self.m_choiceContainer
  for i = 1, #options do
    if self.m_questionData.type == EQuestionType.PictureSingleChoice or self.m_questionData.type == EQuestionType.PictureMultipeChoice then
      parent = self["m_choiceContainer" .. (i - 1) // 4 + 1]
    end
    choiceGo = i == 1 and self.m_choiceGo or GameObject.Instantiate(self.m_choiceGo, parent)
    choiceCmp = choiceGo:GetLuaTable()
    choiceCmp:Init(options[i])
    choiceCmp:SetParent(self)
    self.m_arrChoice[#self.m_arrChoice + 1] = choiceCmp
  end
  if self.m_choiceImage and self.m_questionData.picture then
    self.m_choiceImage.sprite = AddressableLoader.LoadSpriteByBase64(self.m_questionData.picture)
    self.m_choiceImage.color = CSColor.white
  end
  self:_InitPicture()
  if self.m_questionData.type == EQuestionType.PictureSingleChoice or self.m_questionData.type == EQuestionType.PictureMultipeChoice then
    self.m_choiceContainer2.gameObject:SetActive(3 < #options)
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_choiceContainer1)
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_choiceContainer2)
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_choiceContainerRoot)
  end
end

function SurveyChoiceQuestion:UpdateView()
  if self.m_choiceContainer then
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_choiceContainer)
  end
end

function SurveyChoiceQuestion:CanNextStep()
  if self.m_questionData.rule == "free" then
    return true
  end
  local opMin = tonumber(self.m_questionData.opMin)
  local opMax = tonumber(self.m_questionData.opMax)
  local seletedCount = 0
  for i = 1, #self.m_arrChoice do
    if self.m_arrChoice[i]:IsSelected() then
      seletedCount = seletedCount + 1
    end
  end
  if opMin == nil and opMax == nil and seletedCount == 0 then
    GM.UIManager:ShowPromptWithKey("question_error_tips")
    return false
  elseif opMin and opMin > seletedCount then
    GM.UIManager:ShowPrompt(GM.GameTextModel:GetText("question_multiple_min_tip", opMin))
    return false
  elseif opMax and opMax < seletedCount then
    GM.UIManager:ShowPrompt(GM.GameTextModel:GetText("question_multiple_max_tip", opMax))
    return false
  end
  return true
end

function SurveyChoiceQuestion:GetUserAnswer()
  local answers = {}
  local nextQIds = {}
  local choiceCmp, choiceData, vmemo
  for i = 1, #self.m_arrChoice do
    choiceCmp = self.m_arrChoice[i]
    choiceData = choiceCmp.choiceData
    if choiceCmp:IsSelected() then
      if answers[EAnswerKey.AnserId] == nil then
        answers[EAnswerKey.AnserId] = {}
      end
      answers[EAnswerKey.AnserId][#answers[EAnswerKey.AnserId] + 1] = choiceData.cid
      vmemo = choiceCmp:GetInput()
      if not StringUtil.IsNilOrEmpty(vmemo) then
        if answers[EAnswerKey.Vmemo] == nil then
          answers[EAnswerKey.Vmemo] = vmemo
        else
          answers[EAnswerKey.Vmemo] = answers[EAnswerKey.Vmemo] .. ";" .. vmemo
        end
      end
      if (self.m_type == EQuestionType.SingleChoice or self.m_type == EQuestionType.PictureSingleChoice) and choiceData.visible_ids ~= nil then
        nextQIds = choiceData.visible_ids
      end
    end
  end
  local options = self.m_questionData.options
  if not Table.IsEmpty(options) and (self.m_type == EQuestionType.MultipleChoice or self.m_type == EQuestionType.MultipleChoiceFillBlank or self.m_type == EQuestionType.PictureMultipeChoice) and options[1].visible_ids ~= nil then
    nextQIds = options[1].visible_ids
  end
  return answers, nextQIds
end
