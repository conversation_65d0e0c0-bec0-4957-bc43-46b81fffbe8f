AlbumGetNewJokerWindow = setmetatable({
  canClickWindowMask = true,
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, AlbumActivityBaseWindow)
AlbumGetNewJokerWindow.__index = AlbumGetNewJokerWindow

function AlbumGetNewJokerWindow:BeforeOpenCheck()
  local model = AlbumActivityModel.GetActiveModel()
  return model and model:GetJokerCount() > 0
end

function AlbumGetNewJokerWindow:Init(activityType)
  AlbumActivityBaseWindow.Init(self, activityType, false)
  local jokerGetTime = self.m_model:GetMaxJokerCardTime()
  local nextStateTime = self.m_model:GetNextStateTime()
  self.m_jokerEndTime = jokerGetTime + Sec2Day
  self.m_jokerEndTime = math.min(self.m_jokerEndTime, nextStateTime)
  self.m_bOnce = true
  self.m_jokerCount = self.m_model:GetJokerCount()
  self.m_descText.text = GM.GameTextModel:GetText("joker_card_waiting_notice", self.m_jokerCount)
  self:UpdatePerSecond()
  UIUtil.SetActive(self.m_windowRoot, false)
  UIUtil.SetActive(self.m_cardRoot, true)
end

function AlbumGetNewJokerWindow:UpdatePerSecond()
  if self.m_jokerEndTime == nil then
    return
  end
  local delta = math.max(0, self.m_jokerEndTime - GM.GameModel:GetServerTime())
  if delta == 0 then
    if self.m_bOnce then
      self.m_bOnce = false
      self.m_countdownText.text = GM.GameTextModel:GetText("joker_card_time_expired")
      self.m_descText.text = GM.GameTextModel:GetText("joker_card_expired_notice", self.m_jokerCount)
      self.m_closeBtnGo:SetActive(false)
      self.canCloseByAndroidBack = false
    end
  else
    local str = TimeUtil.ParseTimeDescription(delta, 2, false, false)
    self.m_countdownText.text = GM.GameTextModel:GetText("joker_card_timing", str)
  end
end

function AlbumGetNewJokerWindow:OnWindowMaskClicked()
  if self.m_winAnimator and not self.m_winAnimator:IsNull() then
    self.m_winAnimator:SetTrigger("Remind")
    self.canClickWindowMask = false
  end
end

function AlbumGetNewJokerWindow:OnCloseFinish()
  if self.m_open then
    AlbumActivityModel.UseNewJokerCard = true
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, false, function()
      GM.UIManager:OpenView(UIPrefabConfigName.AlbumJokerExchangeWindow, self.m_activityType, false, nil, self.m_jokerEndTime)
    end)
  end
  AlbumActivityBaseWindow.OnCloseFinish(self)
end

function AlbumGetNewJokerWindow:OnBtnClicked()
  self:Close()
  self.m_open = true
end
