DownloadConfirmWindow = setmetatable({canCloseByAndroidBack = false}, BaseWindow)
DownloadConfirmWindow.__index = DownloadConfirmWindow

function DownloadConfirmWindow:Init(totalSize, exitTextKey, exitCallback, downloadCallback)
  self.m_exitCallback = exitCallback
  self.m_downloadCallback = downloadCallback
  local totalFormatted, unit = GM.DownloadManager:GetFormattedSize(totalSize)
  self.m_descriptionText.text = GM.GameTextModel:GetText("resource_download_confirm_desc", totalFormatted .. unit)
  self.m_exitText.text = GM.GameTextModel:GetText(exitTextKey)
end

function DownloadConfirmWindow:OnExitButtonClicked()
  self:Close()
  if self.m_exitCallback ~= nil then
    self.m_exitCallback()
  end
end

function DownloadConfirmWindow:OnDownloadButtonClicked()
  self:Close()
  self.m_downloadCallback()
end
