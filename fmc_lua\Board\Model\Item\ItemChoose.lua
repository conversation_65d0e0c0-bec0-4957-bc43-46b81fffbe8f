ItemChoose = setmetatable({}, BaseItemComponent)
ItemChoose.__index = ItemChoose
local Choice_Count = 3

function ItemChoose.Create(chooseConfig)
  local itemChoose = setmetatable({}, ItemChoose)
  itemChoose:Init(chooseConfig)
  return itemChoose
end

function ItemChoose:Init(chooseConfig)
  self.m_chooseConfig = chooseConfig
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnChangeGameMode)
end

function ItemChoose:FromSerialization(dbTable)
  if not StringUtil.IsNilOrEmpty(dbTable.choices) then
    self.m_choices = StringUtil.Split(dbTable.choices, ";")
    if #self.m_choices ~= Choice_Count then
      self.m_choices = nil
      GM.BIManager:LogErrorInfo(EBIProjectType.ItemChoose, tostring(dbTable.choices) .. "#" .. self.m_itemModel:GetId())
    end
  end
end

function ItemChoose:ToSerialization(dbTable)
  local info = ""
  if self.m_choices ~= nil then
    for i = 1, #self.m_choices do
      if i == 1 then
        info = self.m_choices[i]
      else
        info = info .. ";" .. self.m_choices[i]
      end
    end
  end
  dbTable.choices = info
end

function ItemChoose:OnChoose(index)
  if self.m_choices == nil or #self.m_choices ~= Choice_Count then
    return
  end
  local boardModel = self.m_itemModel:GetBoardModel()
  local choice = self.m_choices[index]
  local newItem = boardModel:ReplaceItem(self.m_itemModel, choice)
  boardModel.event:Call(BoardEventType.ChooseItem, {
    Source = self.m_itemModel,
    New = newItem
  })
  local biSource = "chest:" .. tostring(self.m_itemModel:GetType()) .. ";items:["
  local count = #self.m_choices
  for i = 1, count do
    biSource = biSource .. tostring(self.m_choices[i])
    if i < count then
      biSource = biSource .. ","
    end
  end
  biSource = biSource .. "]"
  GM.BIManager:LogSpread(biSource, choice, GM.ItemDataModel:GetItemScore(choice))
  PlatformInterface.Vibrate(EVibrationType.Light)
end

function ItemChoose:Destroy()
  BaseItemComponent.Destroy(self)
  if not self.m_itemModel:GetBoardModel() then
    return
  end
  if self.m_cacheOrderStatus == nil then
    return
  end
  local needRegen, _, _, _ = self:_NeedRegenChoices()
  if needRegen then
    self.m_choices = nil
    self.m_itemModel:GetBoardModel():SaveItemProperty(self.m_itemModel)
  end
end

function ItemChoose:_OnChangeGameMode()
  local enterBoard = GM.SceneManager:GetGameMode() == self.m_itemModel:GetBoardModel():GetGameMode()
  if enterBoard and self.m_cacheOrderStatus == nil and self.m_choices ~= nil and #self.m_choices == Choice_Count then
    local boardModel = self.m_itemModel:GetBoardModel()
    local status = boardModel:GetUnfilledOrderRequirementsConsiderView()
    local directNonDishLack = Table.GetKeys(status.directNonDishLack)
    local indirectNonDishLack = Table.GetKeys(status.indirectNonDishLack)
    local lackDishMaterials = Table.GetKeys(status.lackDishMaterials)
    self.m_cacheOrderStatus = {
      directNonDishLack,
      indirectNonDishLack,
      lackDishMaterials
    }
  elseif not enterBoard and self.m_cacheOrderStatus ~= nil then
    local needRegen, _, _, _ = self:_NeedRegenChoices()
    if needRegen then
      self.m_choices = nil
      self.m_itemModel:GetBoardModel():SaveItemProperty(self.m_itemModel)
    end
  end
end

function ItemChoose:_NeedRegenChoices()
  local boardModel = self.m_itemModel:GetBoardModel()
  local status = boardModel:GetUnfilledOrderRequirementsConsiderView()
  local directNonDishLack = Table.GetKeys(status.directNonDishLack)
  local indirectNonDishLack = Table.GetKeys(status.indirectNonDishLack)
  local lackDishMaterials = Table.GetKeys(status.lackDishMaterials)
  local needRegen = self.m_choices == nil or #self.m_choices ~= Choice_Count or self.m_cacheOrderStatus == nil or #self.m_cacheOrderStatus ~= 3 or not Table.CheckListEqual(directNonDishLack, self.m_cacheOrderStatus[1]) or not Table.CheckListEqual(indirectNonDishLack, self.m_cacheOrderStatus[2]) or not Table.CheckListEqual(lackDishMaterials, self.m_cacheOrderStatus[3])
  if not needRegen then
    for _, choice in ipairs(self.m_choices or {}) do
      if GM.ItemDataModel:GetModelConfig(choice, true) == nil then
        needRegen = true
        break
      end
    end
  end
  return needRegen, directNonDishLack, indirectNonDishLack, lackDishMaterials
end

function ItemChoose:GetChoices()
  local needRegen, directNonDishLack, indirectNonDishLack, lackDishMaterials = self:_NeedRegenChoices()
  if not needRegen then
    Log.Info("三选一棋子：无需重新生成，使用之前存的结果")
    return self.m_choices
  end
  Log.Info("三选一棋子：重新生成")
  self.m_cacheOrderStatus = {
    directNonDishLack,
    indirectNonDishLack,
    lackDishMaterials
  }
  self.m_choices = self:_GenerateChoices(Table.ShallowCopy(directNonDishLack), Table.ShallowCopy(indirectNonDishLack))
  self.m_itemModel:GetBoardModel():SaveItemProperty(self.m_itemModel)
  return self.m_choices
end

function ItemChoose:_GenerateChoices(directNonDishLack, indirectNonDishLack)
  local boardModel = self.m_itemModel:GetBoardModel()
  local discardChain = {}
  local arrResult = {}
  local needCount = #self.m_chooseConfig
  local targetScores = self.m_chooseConfig
  local unFilledDishMats = indirectNonDishLack
  Log.Info("三选一棋子：订单缺少的原材料（直接原材料和中间原材料） " .. table.concat(unFilledDishMats, ","))
  self:_RandomResultFromCandidateItems(targetScores, unFilledDishMats, discardChain, arrResult)
  if self:_GenerateFinished(arrResult, needCount) then
    return arrResult
  end
  local unFilledItems = directNonDishLack
  Log.Info("三选一棋子：订单缺少的不需要制作的棋子（不考虑自身） " .. table.concat(unFilledItems, ","))
  self:_RandomResultFromCandidateItems(targetScores, unFilledItems, discardChain, arrResult, true)
  if self:_GenerateFinished(arrResult, needCount) then
    return arrResult
  end
  Log.Info("三选一棋子：订单缺少的不需要制作的棋子（考虑自身） " .. table.concat(unFilledItems, ","))
  self:_RandomResultFromCandidateItems(targetScores, unFilledItems, discardChain, arrResult)
  if self:_GenerateFinished(arrResult, needCount) then
    return arrResult
  end
  local directDishes, directNonDishes, indirectDishes, indirectNonDishes = boardModel:GetOrderRequirements(true)
  local filledItems = {}
  for _, items in ipairs({directNonDishes, indirectNonDishes}) do
    for itemCode, _ in pairs(items) do
      if not Table.ListContain(filledItems, itemCode) and not Table.ListContain(unFilledDishMats, itemCode) and not Table.ListContain(unFilledItems, itemCode) then
        filledItems[#filledItems + 1] = itemCode
      end
    end
  end
  Log.Info("三选一棋子：订单已完成棋子，包括直接需求棋子、直接原材料、中间原材料 " .. table.concat(filledItems, ","))
  self:_RandomResultFromCandidateItems(targetScores, filledItems, discardChain, arrResult)
  if self:_GenerateFinished(arrResult, needCount) then
    return arrResult
  end
  local boardItems = boardModel:FilterItems(function(itemModel)
    local itemType = itemModel:GetType()
    return GM.ItemDataModel:GetModelConfig(itemType) ~= nil and GM.ItemDataModel:GetModelConfig(itemType).Reward ~= nil and not GM.ItemDataModel:IsDishes(itemType)
  end)
  for i = 1, #boardItems do
    boardItems[i] = boardItems[i]:GetCode()
  end
  Log.Info("三选一棋子：棋盘可被订单抓取的非菜品棋子 " .. table.concat(boardItems, ","))
  self:_RandomResultFromCandidateItems(targetScores, boardItems, discardChain, arrResult)
  if self:_GenerateFinished(arrResult, needCount) then
    return arrResult
  end
  Log.Info("三选一棋子：兜底不淘汰棋子")
  local backfillChains = ItemSpreadHelper.GetBackfillChains()
  local backfillItems = {}
  for _, chain in ipairs(backfillChains) do
    table.insert(backfillItems, ItemUtility.GetItemType(chain, GM.ItemDataModel:GetChainMaxLevel(chain)))
  end
  self:_RandomResultFromCandidateItems(targetScores, backfillItems, discardChain, arrResult)
  local backFill = {
    "ene_2",
    "gem_1",
    "gem_1"
  }
  for i = 1, needCount do
    if arrResult[i] == nil then
      arrResult[i] = backFill[i] or "gem_1"
      Log.Info("三选一棋子：第 " .. i .. " 个棋子兜底 " .. arrResult[i])
    end
  end
  return arrResult
end

function ItemChoose:_GenerateFinished(arrResult, needCount)
  local curCount = 0
  for i = 1, needCount do
    if arrResult[i] ~= nil then
      curCount = curCount + 1
    end
  end
  if needCount > curCount then
    return false
  end
  return true
end

function ItemChoose:_RandomResultFromCandidateItems(targetScores, candidateItems, discardChain, result, noConsiderSelf)
  self:_RemoveDiscardChain(candidateItems, discardChain)
  if #candidateItems == 0 then
    return
  end
  local score, tolerance
  local candidateResults = {}
  local chainId, itemCode, itemScore, maxLevel
  for i = 1, #targetScores do
    if result[i] == nil then
      score = targetScores[i].Score
      tolerance = targetScores[i].Tolerance
      local cdResult = {}
      for j = 1, #candidateItems do
        chainId = GM.ItemDataModel:GetChainId(candidateItems[j])
        maxLevel = GM.ItemDataModel:GetChainLevel(candidateItems[j])
        maxLevel = noConsiderSelf and maxLevel - 1 or maxLevel
        for k = maxLevel, 1, -1 do
          itemCode = ItemUtility.GetItemType(chainId, k)
          itemScore = GM.ItemDataModel:GetItemScore(itemCode)
          if itemScore ~= nil and 0 < itemScore and itemScore <= score + tolerance then
            cdResult[#cdResult + 1] = itemCode
            break
          end
        end
      end
      candidateResults[i] = cdResult
      if 1 < #cdResult then
        Log.Info("三选一棋子：第 " .. i .. " 个棋子候选项为 " .. table.concat(cdResult, ","))
      end
    else
      candidateResults[i] = {}
    end
  end
  local tmp = self:_RandomOneCandidate(candidateResults, discardChain, result)
  while tmp ~= nil do
    tmp = self:_RandomOneCandidate(candidateResults, discardChain, result)
  end
end

function ItemChoose:_RandomOneCandidate(candidateResults, discardChain, result)
  local index, count, curCount
  for i = 1, #candidateResults do
    if result[i] == nil then
      self:_RemoveDiscardChain(candidateResults[i], discardChain)
      curCount = self:_GetDistinctChainCount(candidateResults[i])
      if 0 < curCount and (count == nil or count > curCount) then
        index = i
        count = curCount
      end
    end
  end
  if count == nil or count == 0 then
    return
  end
  local itemCode = Table.ListRandomSelectOne(candidateResults[index])
  discardChain[#discardChain + 1] = GM.ItemDataModel:GetChainId(itemCode)
  candidateResults[index] = {}
  result[index] = itemCode
  Log.Info("三选一棋子：第 " .. index .. " 个棋子选中 " .. itemCode)
  return itemCode
end

function ItemChoose:_RemoveDiscardChain(list, discardChain)
  local chainid
  for i = #list, 1, -1 do
    chainid = GM.ItemDataModel:GetChainId(list[i])
    if Table.ListContain(discardChain, chainid) then
      table.remove(list, i)
    end
  end
end

function ItemChoose:_GetDistinctChainCount(list)
  local chainId
  local arrChain = {}
  for i = 1, #list do
    chainId = GM.ItemDataModel:GetChainId(list[i])
    if not Table.ListContain(arrChain, chainId) then
      arrChain[#arrChain + 1] = chainId
    end
  end
  return #arrChain
end
