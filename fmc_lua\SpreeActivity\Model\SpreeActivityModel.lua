SpreeActivityEventType = {StateChanged = 1}
SpreeRewardType = {ToSpree = 1, ToMain = 2}
SpreeActivityModel = setmetatable({}, BaseActivityModel)
SpreeActivityModel.__index = SpreeActivityModel
local SpreeLevelKey = "SpreeLevelKey"
local SpreeLevelExpKey = "SpreeLevelExpKey"
SpreeActivityModel.HotSalesRefreshTime = 43200

function SpreeActivityModel:Init(activityType, activityDataTable, itemDataTable, itemLayerDataTable, itemCacheDataTable, orderMetaDataTable, orderDataTable, shopDataTable)
  self.m_itemDataTable = itemDataTable
  self.m_itemLayerDataTable = itemLayerDataTable
  self.m_itemCacheDataTable = itemCacheDataTable
  self.m_orderMetaDataTable = orderMetaDataTable
  self.m_orderDataTable = orderDataTable
  self.m_shopDataTable = shopDataTable
  self.m_activityDefinition = SpreeActivityDefinition[activityType]
  BaseActivityModel.Init(self, activityType, activityDataTable)
end

function SpreeActivityModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function SpreeActivityModel:Update()
  if self.m_state == ActivityState.Started then
    self.m_boardModel:Update()
  end
end

function SpreeActivityModel:UpdatePerSecond()
  BaseActivityModel.UpdatePerSecond(self)
  if self.m_state == ActivityState.Started then
    self.m_boardModel:UpdatePerSecond()
    self.m_shopModel:UpdatePerSecond()
  end
end

function SpreeActivityModel:GetBoardModel()
  return self.m_boardModel
end

function SpreeActivityModel:GetShopModel()
  return self.m_shopModel
end

function SpreeActivityModel:GetResourceLabels()
  return self.m_activityDefinition.ResourceLabels
end

function SpreeActivityModel:_LoadOtherServerConfig(config)
end

function SpreeActivityModel:_DropData()
  BaseActivityModel._DropData(self)
  self.m_itemDataTable:Drop()
  self.m_itemLayerDataTable:Drop()
  self.m_itemCacheDataTable:Drop()
  self.m_orderMetaDataTable:Drop()
  self.m_orderDataTable:Drop()
  self.m_shopDataTable:Drop()
  self.m_boardModel = nil
  self.m_shopModel = nil
end

function SpreeActivityModel:_OnStateChanged()
  if self.m_state == ActivityState.Started then
    if self.m_boardModel == nil then
      self.m_boardModel = SpreeActivityBoardModel.Create(self, self.m_itemDataTable, self.m_itemLayerDataTable, self.m_itemCacheDataTable, self.m_orderMetaDataTable, self.m_orderDataTable)
      self.m_boardModel:LoadFileConfig()
      self.m_boardModel:OnSyncDataFinished()
    end
    if self.m_shopModel == nil then
      self.m_shopModel = SpreeActivityShopModel.Create(self.m_type, self.m_shopDataTable, self.m_boardModel)
      self.m_shopModel:LoadFileConfig()
      self.m_shopModel:OnSyncDataFinished()
    end
  else
    self.m_boardModel = nil
    self.m_shopModel = nil
  end
  self.event:Call(SpreeActivityEventType.StateChanged)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
end

function SpreeActivityModel:RestoreIapRewards(iapType)
  return self.m_shopModel:RestoreIapRewards(iapType)
end

function SpreeActivityModel:GetLevelConfig()
  if not self.m_arrLevelConfig then
    local fileName = "SpreeLevelConfig_" .. self.m_activityDefinition.ConfigSuffix
    self.m_arrLevelConfig = Table.DeepCopy(require("Data.Config." .. fileName), true)
    for _, config in ipairs(self.m_arrLevelConfig) do
      config.rewards = ConfigUtil.GetCurrencyFromArrStr(config.rewards)
    end
  end
  return self.m_arrLevelConfig
end

function SpreeActivityModel:GetLevelRewards(level)
  local cfg = self:GetLevelConfig()
  if level == nil or cfg[level] == nil then
    return nil
  end
  local arrRewards = Table.DeepCopy(cfg[level].rewards)
  for i = 1, #arrRewards do
    if StringUtil.StartWith(arrRewards[i][PROPERTY_TYPE], "eventtok") then
      arrRewards[i][PROPERTY_TYPE] = EPropertyType.Gold
      arrRewards[i][PROPERTY_COUNT] = math.ceil(arrRewards[i][PROPERTY_COUNT] / 5)
    end
  end
  return arrRewards
end

function SpreeActivityModel:GetLevelRewardsTargetGameMode(level)
  if self.m_type == nil or SpreeActivityDefinition[self.m_type] == nil then
    return nil
  end
  local cfg = self:GetLevelConfig()
  if level == nil or cfg[level] == nil or cfg[level].rewards_type ~= SpreeRewardType.ToMain then
    return SpreeActivityDefinition[self.m_type].GameMode
  end
  return EGameMode.Board
end

function SpreeActivityModel:GetLevelExp()
  return self.m_dbTable:GetValue(SpreeLevelExpKey, "value") or 0
end

function SpreeActivityModel:AddLevelExp(exp)
  Log.Assert(exp ~= nil or exp ~= 0, "spree level add exp error:" .. tostring(exp))
  local ex = self:GetLevelExp()
  local lELvl = self:GetLevel()
  self.m_dbTable:Set(SpreeLevelExpKey, "value", exp + ex)
  if 0 < exp then
    self:DoLevelUp()
    local biInfo = {
      addExp = exp,
      lastExp = ex,
      curExp = self:GetLevelExp(),
      level = self:GetLevel(),
      lastLevel = lELvl
    }
    GM.BIManager:LogAction(EBIType.SpreeLevelAddScore, biInfo)
  end
  EventDispatcher.DispatchEvent(EEventType.SpreeLevelExpChange, exp)
end

function SpreeActivityModel:DoLevelUp()
  while self:CanLevelUp() do
    self:AddLevelExp(-self:GetLevelUpCost())
    self:AddLevel(1)
    local rewards = self:GetLevelRewards(self:GetLevel())
    if self:CanLevelUp() then
      RewardApi.AcquireRewards(rewards, EPropertySource.Give, EBIType.SpreeLevelReward, {simpleCollect = true}, self:GetLevelRewardsTargetGameMode(self:GetLevel()), CacheItemType.Stack)
    else
      RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.SpreeLevelReward, self:GetLevelRewardsTargetGameMode(self:GetLevel()), CacheItemType.Stack)
    end
  end
end

function SpreeActivityModel:CanLevelUp()
  local curLevel = self:GetLevel()
  local cfg = self:GetLevelConfig()
  if curLevel >= #cfg then
    return false
  end
  local curExp = self:GetLevelExp()
  if curExp >= cfg[curLevel + 1].cost then
    return true
  end
  return false
end

function SpreeActivityModel:GetLevelUpCost()
  local curLevel = self:GetLevel()
  local cfg = self:GetLevelConfig()
  if curLevel + 1 > #cfg then
    return cfg[#cfg].cost
  end
  return cfg[curLevel + 1].cost
end

function SpreeActivityModel:GetLevel()
  return self.m_dbTable:GetValue(SpreeLevelKey, "value") or 0
end

function SpreeActivityModel:AddLevel(level)
  Log.Assert(level ~= nil and 0 < level, "spree level add  error!" .. tostring(level))
  self.m_dbTable:Set(SpreeLevelKey, "value", level + self:GetLevel())
  self:LogActivity(EBIType.SpreeLevelUp, self:GetLevel())
end
