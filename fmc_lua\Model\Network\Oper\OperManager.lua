OperManager = {}
OperManager.__index = OperManager
EOperActionPath = {CrossPromotion = "cp", MoreGame = "mg"}
EOperUrlType = {
  Login = "/login?type=json&op=ctrl",
  CPUpdateMissionStatus = "/cp/v3/updateMissionStatus?op=ctrl",
  CPRefreshEventStatus = "/cp/v3/refreshEventStatus?op=ctrl",
  CPEndEvent = "/cp/v3/endEvent?op=login",
  MailUserData = "/mail/user_data?op=ctrl",
  CPClientCheck = "/cp/v3/clientCheck?op=login"
}

function OperManager:Init()
  self.m_requestUrl = NetworkConfig.GetOperUrl()
  self.mailDataEnabled = false
end

function OperManager:Login(validGroupName)
  local url = Application.absoluteURL
  local trackerName = CSAppsFlyerManager:GetTrackerName()
  Log.Info("oper data url: " .. url .. ",trackerName: " .. trackerName)
  self.m_tbUrlParam = {}
  if not StringUtil.IsNilOrEmpty(url) then
    self.m_tbUrlParam = self:ParseUrl(url)
  end
  local startTime = NetTimeStamp.Create(EBIType.NetworkCheckAction.OperLogin)
  GM.BIManager:LogNet(EBIType.NetworkCheckAction.OperLogin, url)
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(self:GetUrlByType(EOperUrlType.Login), "POST", 8000, 1)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  local headers, rawData, tbData
  reqCtx:SetCheckResponse(function()
    if GM == nil then
      return nil
    end
    headers = GM.HttpManager:ConvertHeaders(reqCtx.ResponseHeaders)
    if headers["process-time"] == nil then
      return "Missing process-time header"
    end
    if math.floor(reqCtx.Status / 100) == 2 then
      if reqCtx.RespBody.Bytes == 0 then
        return "Invalid response body"
      end
      rawData = reqCtx:GetResponseString()
      tbData = json.decode(rawData)
      if tbData == nil then
        return "Malformed response body"
      end
    end
    return nil
  end)
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    local timeInterval = startTime:EndAndGetDur()
    if reqCtx.Rcode == ResultCode.Succeeded then
      GM.OperBIManager:OperLoginCallback(tbData.adjust)
      GM.SceneManager:SetLoggingFlag(tbData.debug_log)
      GM.BIManager:SetCompressMethod(tbData.compress)
      GM.CrossPromotionModel:LoginMsg(tbData.cp)
      GM.MoreGameModel:LoginMsg(tbData.more_games)
      self.mailDataEnabled = tbData.maildata == "1"
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.OperLoginSuccess, nil, reqCtx, timeInterval)
      return
    end
    if OperManager.TryFallbackRequest(reqCtx) then
      return
    end
    GM.BIManager:LogNet(EBIType.NetworkCheckAction.OperLoginFailed, "[OperCallback] Invalid data. " .. (rawData or "nil"), reqCtx, timeInterval)
  end)
  local reqBody = {
    userId = tostring(GM.UserModel:GetUserId()),
    groups = validGroupName,
    level = GM.LevelModel:GetCurrentLevel(),
    day = GM.MainBoardModel:GetCurOrderDay(),
    pay = GM.InAppPurchaseModel:GetTotalRecharge(),
    os_name = DeviceInfo.GetOsName(),
    os_version = DeviceInfo.GetOsVersion(),
    token = GameConfig.GetGameToken(),
    idfa = DeviceInfo.GetAdvertisingIdentifier(),
    imei = DeviceInfo.GetIMEI(),
    channel = GameConfig.CURRENT_PLATFORM,
    clientVer = GameConfig.GetCurrentVersion(),
    device_name = DeviceInfo.GetDeviceModel(),
    device_id = DeviceInfo.GetDeviceID(),
    idfv = DeviceInfo.GetVendorID(),
    gps_adid = DeviceInfo.GetAdvertisingIdentifier(),
    network_name = CSAppsFlyerManager:GetMediaSource(),
    tracker_name = CSAppsFlyerManager:GetTrackerName(),
    adid = "::" .. CSAppsFlyerManager:GetAppsFlyerId(),
    region = DeviceInfo.GetCountry(),
    cp = self.m_tbUrlParam
  }
  reqCtx:AppendBody(json.encode(reqBody))
  reqCtx:Send()
end

function OperManager.TryFallbackRequest(reqCtx)
  if GameConfig.IsTestMode() then
    return GM.HttpManager:TryFallbackRequest(reqCtx, NetworkConfig.OfflineFallbackIp)
  else
    return GM.HttpManager:TryFallbackRequest(reqCtx, "*************", "https://da-ga.mergecola.com")
  end
end

function OperManager:ParseUrl(url)
  local t1
  t1 = StringUtil.Split(url, ",")
  url = t1[1]
  t1 = StringUtil.Split(t1[1], "?")
  local ts = string.reverse(t1[1])
  local _, i = string.find(ts, "/")
  local m = string.len(ts) - i + 1
  local path = string.sub(t1[1], m + 1, -1)
  url = t1[2]
  t1 = StringUtil.Split(t1[2], "&")
  local res = {}
  for k, v in pairs(t1) do
    t1 = StringUtil.Split(v, "=")
    res[t1[1]] = {}
    res[t1[1]] = t1[2]
  end
  res.path = path
  return res
end

function OperManager:GetUrlByType(eOperUrlType)
  return self.m_requestUrl .. eOperUrlType
end

function OperManager:GetUrlParam()
  return self.m_tbUrlParam
end

function OperManager:SendUserDataEmail(emailAddress)
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(self:GetUrlByType(EOperUrlType.MailUserData), "POST", 8000, 1)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  GM.UIManager:ShowMask()
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    GM.UIManager:HideMask()
    if reqCtx.Rcode == ResultCode.Succeeded then
      GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("show_data_success_title"), GM.GameTextModel:GetText("show_data_success_desc"), GM.GameTextModel:GetText("common_button_ok"), nil, nil, true)
    else
      GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "network_error_title", "network_error_desc", "common_button_cancel", "common_retry_button", nil, function(window)
        window:Close()
        self:SendUserDataEmail(emailAddress)
      end, true)
    end
  end)
  local reqBody = {
    user_id = tostring(GM.UserModel:GetUserId()),
    game_token = GameConfig.GetGameToken(),
    device = DeviceInfo.GetDeviceModel(),
    adid = "::" .. CSAppsFlyerManager:GetAppsFlyerId(),
    country = DeviceInfo.GetCountry(),
    os = DeviceInfo.GetOsName(),
    email_address = emailAddress
  }
  reqCtx:AppendBody(json.encode(reqBody))
  reqCtx:Send()
end
