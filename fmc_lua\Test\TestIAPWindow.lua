TestIAPWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestIAPWindow.__index = TestIAPWindow

function TestIAPWindow:Init()
  local arrIAPTypes = {}
  for k, v in pairs(EIAPType) do
    arrIAPTypes[#arrIAPTypes + 1] = v
  end
  table.sort(arrIAPTypes)
  self.m_arrCells = {}
  self.m_mapCells = {}
  local cellObj
  for i, iapType in ipairs(arrIAPTypes) do
    cellObj = GameObject.Instantiate(self.m_cellOrigin, self.m_contentRectTrans)
    cellObj:SetActive(true)
    self.m_arrCells[i] = cellObj:GetLuaTable()
    self.m_arrCells[i]:Init(i, iapType)
    self.m_mapCells[iapType] = cellObj
  end
end

function TestIAPWindow:_OnInputChanged()
  local text = self.m_inputField.text
  local empty = StringUtil.IsNilOrEmpty(text)
  text = string.gsub(text, "%a", function(n)
    return "[" .. string.lower(n) .. string.upper(n) .. "]" .. ".*"
  end)
  for iapType, obj in pairs(self.m_mapCells) do
    UIUtil.SetActive(obj, empty or string.match(iapType, text) ~= nil)
  end
end

TestIAPCell = {}
TestIAPCell.__index = TestIAPCell

function TestIAPCell:Init(index, iapType)
  self.m_iapType = iapType
  local price = GM.InAppPurchaseModel:GetLocalizedPrice(self.m_iapType)
  self.m_dataText.text = "[" .. index .. "]:" .. iapType .. "(" .. price .. ")"
end

function TestIAPCell:OnBtnClicked()
  GM.InAppPurchaseModel:StartPurchase(self.m_iapType, function()
    local arrRewards = {
      {
        [PROPERTY_TYPE] = EPropertyType.Gem,
        [PROPERTY_COUNT] = 66
      }
    }
    RewardApi.CryptRewards(arrRewards)
    RewardApi.AcquireRewards(arrRewards, EPropertySource.Buy, EBIType.Test)
  end, EBIType.Test)
end
