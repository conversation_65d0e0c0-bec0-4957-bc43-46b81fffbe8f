TaskPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true
  },
  canIgnorePopup = false
}, BasePopupHelper)
TaskPopupHelper.__index = TaskPopupHelper

function TaskPopupHelper.Create()
  local helper = setmetatable({}, TaskPopupHelper)
  helper:Init()
  return helper
end

function TaskPopupHelper:Init()
  self:_UpdateState()
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self._OnMainTaskFinished)
  EventDispatcher.AddListener(EEventType.NewContentReleased, self, self._UpdateState)
end

function TaskPopupHelper:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function TaskPopupHelper:_OnMainTaskFinished()
  self:_UpdateState()
  self.m_canPopDayClearWindow = GM.MainBoardModel:GetOrderModel():IsAllCanUnlockGroupFinished() and not GM.TaskManager:CanFinishOngoingTask()
end

function TaskPopupHelper:_UpdateState()
  self.m_canClaimProgressReward = GM.TaskManager:CanClaimProgressReward()
  self.m_canPopChapterFinishWindow = GM.TaskManager:IsOngoingChapterProgressFinished() and not GM.TaskManager:HasPopedChapterFinishWin()
  self.m_canPopNewChapterWindow = GM.TaskManager:IsOngoingChapterProgressFinished() and not GM.TaskManager:IsLastChapter()
end

function TaskPopupHelper:NeedCheckPopup()
  return self.m_canClaimProgressReward or self.m_canPopChapterFinishWindow or self.m_canPopNewChapterWindow or self.m_nextWin ~= nil or self.m_canPopDayClearWindow
end

function TaskPopupHelper:CheckPopup()
  if self.m_canClaimProgressReward then
    self.m_canClaimProgressReward = false
    return UIPrefabConfigName.TaskGroupFinishWindow
  end
  if self.m_canPopChapterFinishWindow then
    self.m_canPopChapterFinishWindow = false
    GM.TaskManager:SetPopedChapterFinishWin()
    if GM.TaskManager:IsLastChapter() then
      self:SetNeedCheckPopup(true)
      self.m_nextWin = UIPrefabConfigName.TaskClearWindow
      self.m_canPopDayClearWindow = false
    end
    return UIPrefabConfigName.ChapterFinishWindow
  end
  if self.m_canPopNewChapterWindow then
    self.m_canPopNewChapterWindow = false
    local startNew, arrChapterUnlockRewards = GM.TaskManager:TryStartNewChapter()
    if startNew then
      if arrChapterUnlockRewards and not IsAutoRun() then
        self:SetNeedCheckPopup(true)
        self.m_nextWin = UIPrefabConfigName.RewardWindow
        self.m_nextParam = table.pack(arrChapterUnlockRewards, "chapter_reward_title", true)
      end
      return UIPrefabConfigName.ChapterUnlockWindow, table.pack(arrChapterUnlockRewards)
    end
  end
  if self.m_canPopDayClearWindow then
    self.m_canPopDayClearWindow = false
    return UIPrefabConfigName.OrderDayClearWindow
  end
  if self.m_nextWin then
    local win = self.m_nextWin
    local param = self.m_nextParam
    self.m_nextWin = nil
    self.m_nextParam = nil
    return win, param
  end
end
