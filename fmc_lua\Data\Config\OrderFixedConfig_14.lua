return {
  {
    Id = "140010",
    GroupId = 1,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "140020",
    GroupId = 1,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140030",
    GroupId = 1,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "140040",
    GroupId = 1,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_7e4semi_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "140050",
    GroupId = 1,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e4nood_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140060",
    GroupId = 1,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    }
  },
  {
    Id = "140070",
    GroupId = 1,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4assort_3",
      Count = 1
    }
  },
  {
    Id = "140080",
    GroupId = 2,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e4nood_1",
      Count = 1
    }
  },
  {
    Id = "140090",
    GroupId = 2,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140100",
    GroupId = 2,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "140110",
    GroupId = 2,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1mdrk_17",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "140120",
    GroupId = 2,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140130",
    GroupId = 2,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_13e5mt_12",
      Count = 1
    }
  },
  {
    Id = "140140",
    GroupId = 2,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_9e6assort_5",
      Count = 1
    }
  },
  {
    Id = "140150",
    GroupId = 3,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_9e6nibble_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140160",
    GroupId = 3,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "140170",
    GroupId = 3,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_24",
      Count = 1
    }
  },
  {
    Id = "140180",
    GroupId = 3,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e6soup_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4nood_2",
      Count = 1
    }
  },
  {
    Id = "140190",
    GroupId = 3,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140200",
    GroupId = 3,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "140210",
    GroupId = 3,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "140220",
    GroupId = 4,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_11e4tato_17",
      Count = 1
    }
  },
  {
    Id = "140230",
    GroupId = 4,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "140240",
    GroupId = 4,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140250",
    GroupId = 4,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_12e6porr_2",
      Count = 1
    }
  },
  {
    Id = "140260",
    GroupId = 4,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6sf_22",
      Count = 1
    }
  },
  {
    Id = "140270",
    GroupId = 4,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_13e6bec_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140280",
    GroupId = 4,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "140290",
    GroupId = 5,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4friedmt_19",
      Count = 1
    }
  },
  {
    Id = "140300",
    GroupId = 5,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_2",
      Count = 1
    }
  },
  {
    Id = "140310",
    GroupId = 5,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140320",
    GroupId = 5,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "140330",
    GroupId = 5,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_11e3icytre_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4curry_11",
      Count = 1
    }
  },
  {
    Id = "140340",
    GroupId = 5,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140350",
    GroupId = 5,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "140360",
    GroupId = 6,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "140370",
    GroupId = 6,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6dst_16",
      Count = 1
    }
  },
  {
    Id = "140380",
    GroupId = 6,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140390",
    GroupId = 6,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_26",
      Count = 1
    }
  },
  {
    Id = "140400",
    GroupId = 6,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "140410",
    GroupId = 6,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1mdrk_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140420",
    GroupId = 6,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1nutt_3",
      Count = 1
    }
  },
  {
    Id = "140430",
    GroupId = 7,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "140440",
    GroupId = 7,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e1sala_9",
      Count = 1
    }
  },
  {
    Id = "140450",
    GroupId = 7,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140460",
    GroupId = 7,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "140470",
    GroupId = 7,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e2appe_7",
      Count = 1
    }
  },
  {
    Id = "140480",
    GroupId = 7,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140490",
    GroupId = 7,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "140500",
    GroupId = 8,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "140510",
    GroupId = 8,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e5semi_16",
      Count = 1
    }
  },
  {
    Id = "140520",
    GroupId = 8,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6curry_20",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140530",
    GroupId = 8,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_grillmt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "140540",
    GroupId = 8,
    ChapterId = 14,
    Requirement_1 = {Type = "it_a14_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140550",
    GroupId = 8,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "140560",
    GroupId = 8,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e6nibble_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6porr_2",
      Count = 1
    }
  },
  {
    Id = "140570",
    GroupId = 9,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    }
  },
  {
    Id = "140580",
    GroupId = 9,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e4sf_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_3",
      Count = 1
    }
  },
  {
    Id = "140590",
    GroupId = 9,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140600",
    GroupId = 9,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "140610",
    GroupId = 9,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4curry_25",
      Count = 1
    }
  },
  {
    Id = "140620",
    GroupId = 9,
    ChapterId = 14,
    Requirement_1 = {Type = "it_a14_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140630",
    GroupId = 9,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "140640",
    GroupId = 10,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e6curry_7",
      Count = 1
    }
  },
  {
    Id = "140650",
    GroupId = 10,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e6curry_12",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_1_9", Count = 1}
  },
  {
    Id = "140660",
    GroupId = 10,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1nutt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e1icytre_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140670",
    GroupId = 10,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4sf_25",
      Count = 1
    }
  },
  {
    Id = "140680",
    GroupId = 10,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6rice_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140690",
    GroupId = 10,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e4nood_1",
      Count = 1
    }
  },
  {
    Id = "140700",
    GroupId = 10,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1mdrk_17",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "140710",
    GroupId = 11,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "140720",
    GroupId = 11,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_friedsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6rice_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140730",
    GroupId = 11,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "140740",
    GroupId = 11,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6nibble_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140750",
    GroupId = 11,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "140760",
    GroupId = 11,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e4nood_2",
      Count = 1
    }
  },
  {
    Id = "140770",
    GroupId = 11,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_13e6bec_2",
      Count = 1
    }
  },
  {
    Id = "140780",
    GroupId = 12,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "140790",
    GroupId = 12,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_13e5fd_27",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6soup_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140800",
    GroupId = 12,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1mdrk_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "140810",
    GroupId = 12,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "140820",
    GroupId = 12,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6curry_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140830",
    GroupId = 12,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e5semi_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6dst_16",
      Count = 1
    }
  },
  {
    Id = "140840",
    GroupId = 12,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "140850",
    GroupId = 13,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e2appe_6",
      Count = 1
    }
  },
  {
    Id = "140860",
    GroupId = 13,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_sal_1", Count = 1},
    Requirement_2 = {
      Type = "ds_14e4curry_24",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140870",
    GroupId = 13,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "140880",
    GroupId = 13,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1sala_9",
      Count = 1
    }
  },
  {
    Id = "140890",
    GroupId = 13,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "140900",
    GroupId = 13,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140910",
    GroupId = 13,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "140920",
    GroupId = 14,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "140930",
    GroupId = 14,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_11e4tato_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140940",
    GroupId = 14,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e6dst_11",
      Count = 1
    }
  },
  {
    Id = "140950",
    GroupId = 14,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4curry_25",
      Count = 1
    }
  },
  {
    Id = "140960",
    GroupId = 14,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e6nibble_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "140970",
    GroupId = 14,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "140980",
    GroupId = 14,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_12",
      Count = 1
    }
  },
  {
    Id = "140990",
    GroupId = 15,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "141000",
    GroupId = 15,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_11e3icytre_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141010",
    GroupId = 15,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141020",
    GroupId = 15,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_11e2mt_15",
      Count = 1
    }
  },
  {
    Id = "141030",
    GroupId = 15,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_23",
      Count = 1
    }
  },
  {
    Id = "141040",
    GroupId = 15,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141050",
    GroupId = 15,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_13e2sf_27",
      Count = 1
    }
  },
  {
    Id = "141060",
    GroupId = 16,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_17",
      Count = 1
    }
  },
  {
    Id = "141070",
    GroupId = 16,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4sf_25",
      Count = 1
    }
  },
  {
    Id = "141080",
    GroupId = 16,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4nood_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141090",
    GroupId = 16,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "141100",
    GroupId = 16,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "141110",
    GroupId = 16,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_14", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141120",
    GroupId = 16,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_mixdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141130",
    GroupId = 17,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "141140",
    GroupId = 17,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141150",
    GroupId = 17,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e4friedmt_7",
      Count = 1
    }
  },
  {
    Id = "141160",
    GroupId = 17,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "141170",
    GroupId = 17,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_6e4flb_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141180",
    GroupId = 17,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_14e4curry_10",
      Count = 1
    }
  },
  {
    Id = "141190",
    GroupId = 17,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "141200",
    GroupId = 18,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141210",
    GroupId = 18,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e6curry_19",
      Count = 1
    }
  },
  {
    Id = "141220",
    GroupId = 18,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141230",
    GroupId = 18,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "141240",
    GroupId = 18,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_mixdrk_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e5semi_16",
      Count = 1
    }
  },
  {
    Id = "141250",
    GroupId = 18,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141260",
    GroupId = 18,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1mdrk_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6rice_12",
      Count = 1
    }
  },
  {
    Id = "141270",
    GroupId = 19,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "141280",
    GroupId = 19,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_18",
      Count = 1
    }
  },
  {
    Id = "141290",
    GroupId = 19,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141300",
    GroupId = 19,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "141310",
    GroupId = 19,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6soup_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141320",
    GroupId = 19,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "141330",
    GroupId = 19,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "141340",
    GroupId = 20,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_1", Count = 1},
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "141350",
    GroupId = 20,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e3icytre_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141360",
    GroupId = 20,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_14e4curry_9",
      Count = 1
    }
  },
  {
    Id = "141370",
    GroupId = 20,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141380",
    GroupId = 20,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e3juice_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141390",
    GroupId = 20,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6rice_7",
      Count = 1
    }
  },
  {
    Id = "141400",
    GroupId = 20,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6sf_29",
      Count = 1
    }
  },
  {
    Id = "141410",
    GroupId = 21,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "141420",
    GroupId = 21,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e4nood_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141430",
    GroupId = 21,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e6rice_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141440",
    GroupId = 21,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    }
  },
  {
    Id = "141450",
    GroupId = 21,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141460",
    GroupId = 21,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "141470",
    GroupId = 21,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e4curry_25",
      Count = 1
    }
  },
  {
    Id = "141480",
    GroupId = 22,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "141490",
    GroupId = 22,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141500",
    GroupId = 22,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1dst_24",
      Count = 1
    }
  },
  {
    Id = "141510",
    GroupId = 22,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    }
  },
  {
    Id = "141520",
    GroupId = 22,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_13",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141530",
    GroupId = 22,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1sala_9",
      Count = 1
    }
  },
  {
    Id = "141540",
    GroupId = 22,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "141550",
    GroupId = 23,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "141560",
    GroupId = 23,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1mdrk_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1nutt_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141570",
    GroupId = 23,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e4nood_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6soup_15",
      Count = 1
    }
  },
  {
    Id = "141580",
    GroupId = 23,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1tato_13",
      Count = 1
    }
  },
  {
    Id = "141590",
    GroupId = 23,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6dst_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141600",
    GroupId = 23,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e4curry_25",
      Count = 1
    }
  },
  {
    Id = "141610",
    GroupId = 23,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_10e6dst_11",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141620",
    GroupId = 24,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    }
  },
  {
    Id = "141630",
    GroupId = 24,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4nood_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141640",
    GroupId = 24,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_14e4sf_30",
      Count = 1
    }
  },
  {
    Id = "141650",
    GroupId = 24,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e6curry_14",
      Count = 1
    }
  },
  {
    Id = "141660",
    GroupId = 24,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141670",
    GroupId = 24,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "141680",
    GroupId = 24,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141690",
    GroupId = 25,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_3",
      Count = 1
    }
  },
  {
    Id = "141700",
    GroupId = 25,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141710",
    GroupId = 25,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    }
  },
  {
    Id = "141720",
    GroupId = 25,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_11e4tato_19",
      Count = 1
    }
  },
  {
    Id = "141730",
    GroupId = 25,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e6curry_22",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141740",
    GroupId = 25,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "141750",
    GroupId = 25,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141760",
    GroupId = 26,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e1preingre_1",
      Count = 1
    }
  },
  {
    Id = "141770",
    GroupId = 26,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_13e6bec_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141780",
    GroupId = 26,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "141790",
    GroupId = 26,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e6curry_15",
      Count = 1
    }
  },
  {
    Id = "141800",
    GroupId = 26,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e3icytre_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141810",
    GroupId = 26,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "141820",
    GroupId = 26,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141830",
    GroupId = 27,
    ChapterId = 14,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_14e5semi_16",
      Count = 1
    }
  },
  {
    Id = "141840",
    GroupId = 27,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141850",
    GroupId = 27,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_8",
      Count = 1
    }
  },
  {
    Id = "141860",
    GroupId = 27,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e4curry_25",
      Count = 1
    }
  },
  {
    Id = "141870",
    GroupId = 27,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6curry_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141880",
    GroupId = 27,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_mixdrk_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    }
  },
  {
    Id = "141890",
    GroupId = 27,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141900",
    GroupId = 28,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e6curry_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "141910",
    GroupId = 28,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141920",
    GroupId = 28,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_25",
      Count = 1
    }
  },
  {
    Id = "141930",
    GroupId = 28,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e3icytre_14",
      Count = 1
    }
  },
  {
    Id = "141940",
    GroupId = 28,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6dst_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141950",
    GroupId = 28,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    }
  },
  {
    Id = "141960",
    GroupId = 28,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1cockt_28",
      Count = 1
    }
  },
  {
    Id = "141970",
    GroupId = 29,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6curry_4",
      Count = 1
    }
  },
  {
    Id = "141980",
    GroupId = 29,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "141990",
    GroupId = 29,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    }
  },
  {
    Id = "142000",
    GroupId = 29,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "142010",
    GroupId = 29,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4curry_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "142020",
    GroupId = 29,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e5semi_16",
      Count = 1
    }
  },
  {
    Id = "142030",
    GroupId = 29,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4assort_12",
      Count = 1
    }
  },
  {
    Id = "142040",
    GroupId = 30,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "142050",
    GroupId = 30,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "142060",
    GroupId = 30,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1cockt_27",
      Count = 1
    }
  },
  {
    Id = "142070",
    GroupId = 30,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e1sala_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "142080",
    GroupId = 30,
    ChapterId = 14,
    Requirement_1 = {
      Type = "ds_14e4curry_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "142090",
    GroupId = 30,
    ChapterId = 14,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6rice_14",
      Count = 1
    }
  },
  {
    Id = "142100",
    GroupId = 30,
    ChapterId = 14,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  }
}
