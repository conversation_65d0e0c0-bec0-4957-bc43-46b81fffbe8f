DashActivityRewardRecoverWindow = setmetatable({
  canClickWindowMask = false,
  canCloseByAndroidBack = false,
  showWindowMask = false
}, DashActivityBaseWindow)
DashActivityRewardRecoverWindow.__index = DashActivityRewardRecoverWindow

function DashActivityRewardRecoverWindow:Init(activityType)
  DashActivityBaseWindow.Init(self, activityType, true)
  self.m_activityType = activityType
  self:_GetRewards()
  self.m_rewardContent:Init(self.m_rewards)
  if #self.m_rewards <= 3 then
    UIUtil.AddSizeDelta(self.m_contentRect, 0, -300)
  end
  local showRewardEdge = #self.m_rewards > 6
  self.m_topEdgeGo:SetActive(showRewardEdge)
  self.m_bottomEdgeGo:SetActive(showRewardEdge)
  self.m_curId = self.m_model:GetId()
end

function DashActivityRewardRecoverWindow:_GetRewards()
  self.m_rewards = {}
  while true do
    local level = self.m_model:GetLevel()
    local levelConfig = self.m_model:GetLevelConfigs()[level]
    if levelConfig == nil then
      break
    end
    local score = self.m_model:GetScore()
    if score < levelConfig.score then
      break
    end
    Table.ListAppend(self.m_rewards, levelConfig.rewards)
    self.m_model:Upgrade()
  end
end

function DashActivityRewardRecoverWindow:OnButtonClicked()
  self:Close()
end

function DashActivityRewardRecoverWindow:Close()
  local viewData = {eventLock = true, interval = 0.15}
  RewardApi.AcquireRewardsInView(self.m_rewards, viewData)
  if self.m_curId == self.m_model:GetId() and self.m_model:GetState() ~= ActivityState.Released then
    local activityDefinition = DashActivityDefinition[self.m_activityType]
    local level = self.m_model:GetLevel()
    local levelConfig = self.m_model:GetLevelConfigs()[level]
    if levelConfig == nil or not self.m_model:IsPlayEntryFinal() and level > self.m_model:GetMaxRewardLevel() then
      GM.UIManager:OpenViewWhenIdle(activityDefinition.SuccessWindowPrefabName, self.m_activityType)
    else
      GM.UIManager:OpenViewWhenIdle(activityDefinition.FailWindowPrefabName, self.m_activityType)
    end
  end
  DashActivityBaseWindow.Close(self)
end
