BundleTestInfo = {}
BundleTestInfo.__index = BundleTestInfo

function BundleTestInfo:Awake()
  if not GM.UIManager:CanShowTestUI() then
    UIUtil.SetActive(self.gameObject, false)
  end
end

function BundleTestInfo:Init(model, dataGroup, eTriggerType)
  if not GM.UIManager:CanShowTestUI() then
    UIUtil.SetActive(self.gameObject, false)
    return
  end
  local groupId = dataGroup:GetGroupId()
  local testStr = ""
  testStr = testStr .. "groupId: " .. groupId .. "\n"
  local dailyShowNum = model:_GetBundleDBData(groupId, EBundleDBKey.DailyShowNum)
  local dailyMaxShowNum = dataGroup:GetDailyShowNum()
  if dailyMaxShowNum ~= nil then
    testStr = testStr .. string.format("dailyShowNum: %d/%d\n", dailyShowNum, dailyMaxShowNum)
  end
  if eTriggerType ~= nil then
    testStr = testStr .. "triggerType: " .. eTriggerType .. "\n"
    local triggerDailyNum = model:_GetTriggerDailyNum(eTriggerType, groupId)
    local triggerData = dataGroup:GetTargetTriggerData(eTriggerType)
    if triggerData ~= nil and triggerData.dailyShowNum ~= nil then
      testStr = testStr .. string.format("triggerDailyShowNum: %d/%d\n", triggerDailyNum, triggerData.dailyShowNum)
    end
  end
  self.m_text.text = testStr
end

function BundleTestInfo:OnCloseBtnClicked()
  UIUtil.SetActive(self.gameObject, false)
end
