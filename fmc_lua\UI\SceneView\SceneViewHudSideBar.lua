SceneViewHudSideBar = {}
SceneViewHudSideBar.__index = SceneViewHudSideBar

function SceneViewHudSideBar:Init(sceneViewHud)
  self.m_sceneViewHud = sceneViewHud
  self.m_mapAttachedHudType = {}
  self:_Init()
end

function SceneViewHudSideBar:_Init()
  Log.Error("_Init()是抽象接口")
end

function SceneViewHudSideBar:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function SceneViewHudSideBar:_EntryUpdateFunctionCreator(entryNodeName, entryCountName, hudType, entryPrefabConfigName, checkFunc, params, bRefreshLayout, endFunc)
  if params == nil then
    params = {}
  end
  local refreshLayoutFunc = function()
    if self[entryNodeName] ~= nil then
      LayoutRebuilder.ForceRebuildLayoutImmediate(self[entryNodeName])
      LayoutRebuilder.ForceRebuildLayoutImmediate(self[entryNodeName].parent)
    end
  end
  return function()
    if checkFunc() then
      UIUtil.SetActive(self[entryNodeName].gameObject, true)
      if not self.m_mapAttachedHudType[hudType] then
        local config = GM.DataResource.UIPrefabConfig:GetConfig(entryPrefabConfigName)
        local callback = function(gameObject)
          if self.m_mapAttachedHudType[hudType] or not checkFunc() then
            AddressableLoader.Destroy(gameObject)
            self[entryCountName] = self[entryCountName] - 1
            if endFunc ~= nil then
              endFunc()
            end
            return
          end
          local entry = gameObject:GetLuaTable()
          if entry.Init ~= nil then
            entry:Init(table.unpack(params))
          end
          entry:SetFlyTargetPosition()
          self.m_sceneViewHud:AttachHudButton(hudType, entry)
          self.m_mapAttachedHudType[hudType] = true
          if bRefreshLayout then
            refreshLayoutFunc()
          end
          if endFunc ~= nil then
            endFunc()
          end
        end
        self[entryCountName] = (self[entryCountName] or 0) + 1
        GM.ResourceLoader:LoadPrefab(config, self[entryNodeName], V3Zero, callback)
      end
    else
      if self.m_mapAttachedHudType[hudType] then
        local btn = self.m_sceneViewHud:GetHudButton(hudType)
        self.m_sceneViewHud:DetachHudButton(hudType)
        self.m_mapAttachedHudType[hudType] = nil
        self[entryCountName] = self[entryCountName] - 1
        if bRefreshLayout then
          UIUtil.SetActive(btn.gameObject, false)
          refreshLayoutFunc()
        end
        AddressableLoader.Destroy(btn.gameObject)
      end
      if self[entryCountName] == nil or self[entryCountName] == 0 then
        UIUtil.SetActive(self[entryNodeName].gameObject, false)
      end
      if endFunc ~= nil then
        endFunc()
      end
    end
  end
end

function SceneViewHudSideBar:_ActivityEntryCodeCreator(entryConfig, params)
  local entryNodeName = GetEntryRootName(entryConfig.eEntryRootKey)
  local entryCountName = GetEntryCountName(entryConfig.eEntryRootKey)
  local updateFunction = self:_EntryUpdateFunctionCreator(entryNodeName, entryCountName, entryConfig.hudKey, entryConfig.entryPrefabName, entryConfig.checkFun, params)
  if entryConfig.statusChangeEvent ~= nil then
    EventDispatcher.AddListener(entryConfig.statusChangeEvent, self, updateFunction)
  end
  if entryConfig.extraListenEvent ~= nil then
    EventDispatcher.AddListener(entryConfig.extraListenEvent, self, updateFunction)
  end
  updateFunction()
end

function SceneViewHudSideBar:ActivityEntryCodeCreator(entryConfig, params)
  self:_ActivityEntryCodeCreator(entryConfig, params)
end

function SceneViewHudSideBar:IsExistEntryNodeRect(nodeKey)
  return self[nodeKey] ~= nil
end

function SceneViewHudSideBar:BundleEntryCodeCreator(arrBundleType)
  if Table.IsEmpty(arrBundleType) then
    return
  end
  self.m_loadNum = 0
  local countFunc = function()
    self.m_loadNum = self.m_loadNum - 1
    if self.m_loadNum == 0 then
      self:SortBundleEntry()
    end
  end
  local model
  for _, bundleType in ipairs(arrBundleType) do
    model = GM.BundleManager:GetModel(bundleType)
    if model ~= nil then
      for _, dataGroup in ipairs(model:GetAllGroupDatas() or {}) do
        self.m_loadNum = self.m_loadNum + 1
        self:_OnBundleDataRefreshed(dataGroup, bundleType, countFunc)
      end
    end
  end
  EventDispatcher.AddListener(EEventType.BundleDataRefreshed, self, self.OnBundleDataRefreshed)
end

function SceneViewHudSideBar:OnBundleDataRefreshed(msg)
  self:_OnBundleDataRefreshed(msg.dataGroup, msg.bundleType)
end

function SceneViewHudSideBar:_OnBundleDataRefreshed(dataGroup, bundleType, endFunc)
  local model = GM.BundleManager:GetModel(bundleType)
  if model == nil or model.GetMapEntryShowConfig == nil then
    return
  end
  local entryConfig = model:GetMapEntryShowConfig()
  local entryNodeName = GetEntryRootName(entryConfig.eEntryRootKey or EEntryRootKey.Bundle)
  local entryCountName = GetEntryCountName(entryConfig.eEntryRootKey or EEntryRootKey.Bundle)
  local hudKey = GetBundleHudKey(bundleType, dataGroup:GetGroupId())
  local uiDefinition = BundleUIType[dataGroup:GetBundleUIType()]
  local prefabName = uiDefinition and uiDefinition.entryPrefabName or entryConfig.entryPrefabName
  local checkFunc = function()
    return entryConfig.checkFun(dataGroup)
  end
  local updateFunction = self:_EntryUpdateFunctionCreator(entryNodeName, entryCountName, hudKey, prefabName, checkFunc, {bundleType, dataGroup}, true, endFunc)
  updateFunction()
end

function SceneViewHudSideBar:SortBundleEntry()
  local arrEntry = {}
  local model, entry
  for _, bundleType in ipairs(BundleTypeOrderList) do
    model = GM.BundleManager:GetModel(bundleType)
    if model ~= nil then
      for _, dataGroup in ipairs(model:GetActiveGroupDatas() or {}) do
        entry = self.m_sceneViewHud:GetHudButton(GetBundleHudKey(bundleType, dataGroup:GetGroupId()))
        if entry ~= nil then
          table.insert(arrEntry, entry)
        end
      end
    end
  end
  table.sort(arrEntry, function(a, b)
    return a:GetTriggerStartTime() < b:GetTriggerStartTime()
  end)
  for _, entry in ipairs(arrEntry) do
    entry.transform:SetAsLastSibling()
  end
end
