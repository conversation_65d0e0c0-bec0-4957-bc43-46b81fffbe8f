BaseWindow = setmetatable({
  eViewType = EViewType.Window,
  eCloseType = EViewCloseType.Destroy,
  eCloseAnimType = EViewCloseAnimType.Animator,
  canCloseByAndroidBack = true,
  canClickWindowMask = false,
  canCloseByChangeGameMode = true,
  showWindowMask = true,
  disableWindowMaskOnOpenView = nil,
  windowMaskAlpha = nil,
  needBlurEffect = true,
  hideHudAnchorType = nil,
  disableEffectWhenCloseView = false,
  disableSpriteRendererWhenCloseView = false
}, BaseView)
BaseWindow.__index = BaseWindow
local TEMP_DISABLE_WINDOW_MASK_DURATION_DEFAULT = 2

function BaseWindow:OnOpenView(...)
  BaseView.OnOpenView(self, ...)
  self.m_needHideUI = GM.UIManager:GetOpenedViewCountByType(EViewType.Window) == 1 and self.hideHudAnchorType ~= nil
  if self.m_needHideUI then
    self:UpdateSceneViewHud(false, self.hideHudAnchorType)
  end
  self:ShowWindowOnLoadFinished()
  self.m_bCanBeCloseByChangeGameMode = true
end

function BaseWindow:ShowWindowOnLoadFinished()
  if self.m_winAnimator then
    self.m_winAnimator:SetTrigger("Show")
  end
  if self.m_strOpenWindowEffectName and self.m_strOpenWindowEffectName ~= "" then
    GM.AudioModel:PlayEffect(self.m_strOpenWindowEffectName)
  end
  if self.disableWindowMaskOnOpenView ~= nil then
    self:DisableWindowMaskTouchTemporarily(self.disableWindowMaskOnOpenView)
  end
end

function BaseWindow:OnCloseView()
  if self.eCloseAnimType == EViewCloseAnimType.Animator and self.m_winAnimator then
    self.m_winAnimator:SetTrigger("Hide")
  else
    self.eCloseAnimType = EViewCloseAnimType.None
  end
  if self.m_strCloseWindowEffectName and string.len(self.m_strCloseWindowEffectName) > 0 then
    GM.AudioModel:PlayEffect(self.m_strCloseWindowEffectName)
  end
  if self.m_needHideUI then
    self:UpdateSceneViewHud(true, self.hideHudAnchorType)
    EventDispatcher.DispatchEvent(EEventType.ShowTaskBubble)
  end
  if self.disableEffectWhenCloseView then
    local particles = self.gameObject:GetComponentsInChildren(typeof(ParticleSystem), true)
    for i = 0, particles.Length - 1 do
      particles[i].gameObject:SetActive(false)
    end
  end
  if self.disableSpriteRendererWhenCloseView then
    local spriteRenderers = self.gameObject:GetComponentsInChildren(typeof(SpriteRenderer), true)
    for i = 0, spriteRenderers.Length - 1 do
      spriteRenderers[i].gameObject:SetActive(false)
    end
  end
  self.m_bCanBeCloseByChangeGameMode = false
  BaseView.OnCloseView(self)
end

function BaseWindow:OnCloseBtnClick()
  self:Close()
end

function BaseWindow:PlayCloseBtnClickEffect()
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxButtonClick)
end

function BaseWindow:OnWindowMaskClicked()
  self:OnCloseBtnClick()
end

function BaseWindow:_OnBack()
  self:OnCloseBtnClick()
end

function BaseWindow:CloseAndOpenNext(nextWindow, ...)
  self:Close()
  GM.UIManager:OpenViewWhenIdle(nextWindow, ...)
end

function BaseWindow:LogWindowAction(eActionType, refer, ext)
  GM.BIManager:LogUI(self.name, eActionType, refer, ext)
end

function BaseWindow:ResizeByWidth(fContentWidth)
  local fRate1 = Screen.height / Screen.width
  local fRate2 = GM.UIManager:GetCanvasSize().y / fContentWidth
  if fRate1 > fRate2 then
    self.m_contentRect.parent.localScale = V3One * fRate2 / fRate1
  end
end

function BaseWindow:DisableWindowMaskTouchTemporarily(dt)
  local canCloseByAndroidBack = self.canCloseByAndroidBack
  local canClickWindowMask = self.canClickWindowMask
  if canCloseByAndroidBack or canClickWindowMask then
    self.canCloseByAndroidBack = false
    self.canClickWindowMask = false
    DelayExecuteFuncInView(function()
      self.canCloseByAndroidBack = canCloseByAndroidBack
      self.canClickWindowMask = canClickWindowMask
    end, dt or TEMP_DISABLE_WINDOW_MASK_DURATION_DEFAULT, self)
  else
    Log.Warning("BaseWindow:has temporarily disabled window mask already or disable mask with window that can't close by mask.")
  end
end

function BaseWindow:SetCloseBtnActive(active)
  self.m_closeBtnGo:SetActive(active)
end

function BaseWindow:UpdateSceneViewHud(show, arrAnchorType, ignoreAnim)
  EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {
    Show = show,
    AnchorTypes = arrAnchorType,
    IgnoreAnim = ignoreAnim
  })
end

function BaseWindow:OnGameModeChangeInBaseWindow()
  if not self.m_bCanBeCloseByChangeGameMode then
    return
  end
  if self.canCloseByChangeGameMode then
    self.eCloseAnimType = EViewCloseAnimType.None
    self:Close()
  end
  if GameConfig.IsTestMode() then
    Log.Info("[BaseWindow][" .. self.name .. "] close by change mode? " .. tostring(self.canCloseByChangeGameMode))
  end
end

EasyCloseBaseWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
EasyCloseBaseWindow.__index = EasyCloseBaseWindow
TransparentBaseWindow = setmetatable({
  canCloseByAndroidBack = false,
  windowMaskAlpha = 0,
  eCloseAnimType = EViewCloseAnimType.None
}, BaseWindow)
TransparentBaseWindow.__index = TransparentBaseWindow

function TransparentBaseWindow:_OnSkipClick()
end
