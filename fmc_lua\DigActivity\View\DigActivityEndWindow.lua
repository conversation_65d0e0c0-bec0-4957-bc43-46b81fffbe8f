DigActivityEndWindow = setmetatable({}, DigActivityMainWindow)
DigActivityEndWindow.__index = DigActivityEndWindow

function DigActivityEndWindow:BeforeOpenCheck()
  return DigActivityBaseWindow.BeforeOpenCheck(self)
end

function DigActivityEndWindow:Init(...)
  DigActivityMainWindow.Init(self, ...)
  if self.m_model == nil then
    self:Close()
    return
  end
  UIUtil.SetActive(self.m_countdownGo, false)
  local textKey = self.m_model:GetScore() > 0 and "treasure_dig_last_chance_desc" or "treasure_dig_end"
  self.m_tipDescText.text = GM.GameTextModel:GetText(textKey)
  self.m_scrollCooldown = 1
end

function DigActivityEndWindow:UpdatePerSecond()
  if self.m_scrollCooldown ~= nil and self.m_scrollCooldown > 0 then
    self.m_scrollCooldown = self.m_scrollCooldown - 1
  end
end

function DigActivityEndWindow:OnScrollValueChanged()
  if self.m_scrollCooldown ~= nil and self.m_scrollCooldown <= 0 then
    self:TryHideBubble()
  end
end

function DigActivityEndWindow:HideRewardBubble()
  DigActivityMainWindow.HideRewardBubble(self)
  if not self.m_bHideTip then
    self.m_bHideTip = true
    local rect = self.m_tipBubbleRect
    rect:DOScale(0, 0.3):SetEase(Ease.OutCubic)
  end
end

function DigActivityEndWindow:OnCloseBtnClick()
  if self.m_model ~= nil and self.m_model:GetScore() > 0 and not self.m_model:HasFinishedAllRound() and self.m_model:GetState() ~= ActivityState.Released then
    local confirmButtonCallback = function(window)
      self:Close()
      window:Close()
    end
    local tokenImgKey = self.m_activityDefinition.TokenImageName
    local descStr = GM.GameTextModel:GetText("treasure_dig_endwindow_desc", tokenImgKey, tokenImgKey)
    GM.UIManager:OpenView(self.m_activityDefinition.TwoButtonWindowPrefabName, self.m_activityType, descStr, confirmButtonCallback, nil)
  else
    self:Close()
  end
end

DigActivityTwoButtonWindow = setmetatable({}, DigActivityBaseWindow)
DigActivityTwoButtonWindow.__index = DigActivityTwoButtonWindow

function DigActivityTwoButtonWindow:Init(activityType, descStr, redButtonCallback, greenButtonCallback)
  DigActivityBaseWindow.Init(self, activityType)
  self.m_redButtonCallback = redButtonCallback
  self.m_greenButtonCallback = greenButtonCallback
  self.m_descText.text = descStr or ""
  self.m_tokenNumText.text = "X" .. tostring(self.m_model:GetScore())
end

function DigActivityTwoButtonWindow:OnRedClick()
  if self.m_redButtonCallback then
    self.m_redButtonCallback(self)
  else
    self:Close()
  end
end

function DigActivityTwoButtonWindow:OnGreenClick()
  if self.m_greenButtonCallback then
    self.m_greenButtonCallback(self)
  else
    self:Close()
  end
end

DigActivitySuccessWindow = setmetatable({canCloseByAndroidBack = false}, DigActivityBaseWindow)
DigActivitySuccessWindow.__index = DigActivitySuccessWindow

function DigActivitySuccessWindow:Init(...)
  DigActivityBaseWindow.Init(self, ...)
  if self.m_spineAnim ~= nil then
    self.m_spineAnim:Initialize()
    self.m_spineAnim.AnimationState:SetAnimation(0, "appear", false)
    self.m_spineAnim.AnimationState:AddAnimation(0, "idle", true, 0)
  end
end
