LoadingLayer = setmetatable({}, BaseLoadingLayer)
LoadingLayer.__index = LoadingLayer
local ELoadingStage = {
  Error = -1,
  Init = 0,
  PrivacyPolicy = 1,
  PrivacyPolicyFinished = 2,
  SSO = 3,
  SSOFinished = 4,
  Login = 5,
  LoginFinished = 6,
  CDNDownload = 7,
  CDNDownloadFinished = 8,
  FileLoad = 9,
  SyncData = 10,
  SyncDataFinished = 11,
  CheckResources = 12,
  CheckResourcesFinished = 13,
  SceneLoad = 14,
  SceneLoadFinished = 15,
  ViewLoad = 16,
  Finished = 17
}
local TargetProgress = {
  [ELoadingStage.Init] = 0,
  [ELoadingStage.PrivacyPolicy] = 5,
  [ELoadingStage.PrivacyPolicyFinished] = 8,
  [ELoadingStage.SSO] = 10,
  [ELoadingStage.SSOFinished] = 15,
  [ELoadingStage.Login] = 20,
  [ELoadingStage.LoginFinished] = 25,
  [ELoadingStage.CDNDownload] = 30,
  [ELoadingStage.CDNDownloadFinished] = 35,
  [ELoadingStage.FileLoad] = 40,
  [ELoadingStage.SyncData] = 50,
  [ELoadingStage.SyncDataFinished] = 55,
  [ELoadingStage.CheckResources] = 60,
  [ELoadingStage.CheckResourcesFinished] = 65,
  [ELoadingStage.SceneLoad] = 70,
  [ELoadingStage.SceneLoadFinished] = 75,
  [ELoadingStage.ViewLoad] = 80,
  [ELoadingStage.Finished] = 100
}
local ProgressGameTextKey = {
  [ELoadingStage.Init] = "loading_hint_switch_scene",
  [ELoadingStage.PrivacyPolicy] = "loading_hint_switch_scene",
  [ELoadingStage.PrivacyPolicyFinished] = "loading_hint_switch_scene",
  [ELoadingStage.SSO] = "loading_hint_sso",
  [ELoadingStage.SSOFinished] = "loading_hint_sso",
  [ELoadingStage.Login] = "loading_hint_login",
  [ELoadingStage.LoginFinished] = "loading_hint_login",
  [ELoadingStage.CDNDownload] = "loading_hint_cdn",
  [ELoadingStage.CDNDownloadFinished] = "loading_hint_cdn",
  [ELoadingStage.FileLoad] = "loading_hint_cdn",
  [ELoadingStage.SyncData] = "loading_hint_sync_data",
  [ELoadingStage.SyncDataFinished] = "loading_hint_sync_data",
  [ELoadingStage.SceneLoad] = "loading_hint_finish",
  [ELoadingStage.SceneLoadFinished] = "loading_hint_finish",
  [ELoadingStage.Finished] = "loading_hint_finish"
}
local Package_Time_Limit = 2160000

function LoadingLayer:_InitSDK()
  GM.InAppPurchaseModel:InitPurchase()
end

function LoadingLayer:Start()
  BaseLoadingLayer.Start(self)
  self.m_easeSlider.gameObject:SetActive(false)
  self.m_downloadSlider.gameObject:SetActive(true)
  self.m_mapTargetProgress = TargetProgress
  self.m_mapProgressGameTextKey = ProgressGameTextKey
  
  function self.m_updateProgressCheckFunc()
    if self.m_easeSlider:GetValueForDisplay() >= 1 and self.m_eStage == ELoadingStage.Finished then
      self.m_eStage = self.m_eStage + 1
      GM.ResourceLoader:ResetLoadCount()
      GM.SceneManager:UnloadLoading()
      MicrofunProfiler.Instance:EnterGame()
      GM.DownloadManager:TryMuteDownload()
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.LoadingFinish, nil, nil, nil, true)
      self:_DestroyServiceFloat()
    end
  end
  
  self.m_eStage = ELoadingStage.Init
  self.m_downloadSlider.gameObject:SetActive(false)
  self.m_easeSlider.gameObject:SetActive(true)
  Log.Info("LoadingLayer.OnStart ResourceLoader.toLoadCount=" .. tostring(GM.ResourceLoader._toLoadCount) .. "/loadedCnt=" .. tostring(GM.ResourceLoader._loadedCount))
end

function LoadingLayer:Update(dt)
  if not self.m_tickStageErrorCallback then
    function self.m_tickStageErrorCallback()
      self.m_eStage = ELoadingStage.Error
      
      CS.CSErrorMonitor.OpenForceRestartWindow("force_restart_desc")
    end
  end
  SafeCall(self.TickStage, self.m_tickStageErrorCallback, self, dt)
end

local ts

function LoadingLayer:TickStage(dt)
  if self.m_eStage == ELoadingStage.Error then
    return false
  end
  if not BaseLoadingLayer.Update(self, dt) then
    return false
  end
  if not self.m_funcCallback then
    function self.m_funcCallback(success, tbData)
      if success then
        self.m_eStage = self.m_eStage + 1
        
        self:Update(0)
      else
        local bHideNetError = type(tbData) == "table" and tbData.hideNetError
        if not bHideNetError then
          self:_OpenNetworkErrorWindow(tbData)
          Log.Info("[LoadingLayer]NetworkError:" .. tostring(tbData))
        end
      end
    end
  end
  if self.m_eStage == ELoadingStage.Init and GM.ResourceLoader:IsAllResourceLoaded() then
    self:_InitSDK()
    self.m_eStage = ELoadingStage.PrivacyPolicy
    self.m_easeSlider:Pause()
    ts = MicrofunProfiler.Instance:AddLaunchTimeStamp("privacyPolicy")
    if GameConfig.IsTestMode() and not StringUtil.IsNilOrEmpty(ProjectConfig.BUILD_TIME) then
      self:CheckPackageValid()
    else
      GM.GameModel:CheckPrivacyPolicy(function(success, tbData)
        if success then
          self.m_eStage = self.m_eStage + 1
        else
          local bHideNetError = type(tbData) == "table" and tbData.hideNetError
          if not bHideNetError then
            self:_OpenNetworkErrorWindow(tbData)
          end
        end
      end)
    end
  elseif self.m_eStage == ELoadingStage.PrivacyPolicyFinished then
    MicrofunProfiler.Instance:EndLaunchTimeStamp(ts)
    self.m_easeSlider:Resume()
    self.m_eStage = ELoadingStage.SSO
    ts = MicrofunProfiler.Instance:AddLaunchTimeStamp("sso")
    GM.GameModel:SSO(self.m_funcCallback)
  elseif self.m_eStage == ELoadingStage.SSOFinished then
    MicrofunProfiler.Instance:EndLaunchTimeStamp(ts)
    GM.UserProfileModel:GenerateDefaultNameIcon()
    self:_UpdateInfo()
    self.m_eStage = ELoadingStage.Login
    ts = MicrofunProfiler.Instance:AddLaunchTimeStamp("login")
    if self.m_loginCallback == nil then
      function self.m_loginCallback(bSuccess, tbLoginResp, reqCtx)
        if not bSuccess and reqCtx.Status == 503 then
          local fixedTime = json.decode(tbLoginResp)
          
          fixedTime = fixedTime and fixedTime.fixed_time
          if fixedTime ~= nil then
            fixedTime = TimeUtil.ToDate(TimeUtil.ToTimestamp(fixedTime), ETimeFormat.YMDHMS)
          end
          GM.UIManager:ShowMask()
          self.m_easeSlider:Pause()
          DelayExecuteFunc(function()
            GM.UIManager:HideMask()
            GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("server_maintenance_title"), GM.GameTextModel:GetText(fixedTime ~= nil and "server_maintenance_time_desc" or "server_maintenance_general_desc", fixedTime), GM.GameTextModel:GetText("bad_network_window_retry"), function()
              self.m_easeSlider:Resume()
              GM.GameModel:Login(self.m_loginCallback)
            end, nil, false, false)
          end, math.random(1, 5))
          return
        end
        if GM.UpdateHintModel:NeedOpenWindow() and not GM.UpdateHintModel:IsPermitCloseWindow() then
          GM.UpdateHintModel:OpenUpdateHintWindow()
          GM.BIManager:LogAction(EBIType.SceneLogin, EBIType.ForceUpdate)
          return
        end
        self.m_funcCallback(bSuccess, tbLoginResp)
      end
    end
    local schema = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema, "")
    if GameConfig.IsTestMode() and NetworkConfig.IsTestServer4() and not StringUtil.IsNilOrEmpty(schema) then
      GM.TestModel:UpdateServerFunction(function()
        GM.GameModel:Login(self.m_loginCallback, nil, true)
      end)
    else
      GM.GameModel:Login(self.m_loginCallback, nil, true)
    end
  elseif self.m_eStage == ELoadingStage.LoginFinished then
    MicrofunProfiler.Instance:EndLaunchTimeStamp(ts)
    self.m_eStage = ELoadingStage.CDNDownload
    ts = MicrofunProfiler.Instance:AddLaunchTimeStamp("cdn")
    GM.CDNResourceManager:TryDownloadAllLatestFiles(self.m_funcCallback, true)
  elseif self.m_eStage == ELoadingStage.CDNDownloadFinished then
    GM.CDNResourceManager:SyncVersionDatas2LuaManager()
    MicrofunProfiler.Instance:EndLaunchTimeStamp(ts)
    self.m_eStage = ELoadingStage.FileLoad
    ts = MicrofunProfiler.Instance:AddLaunchTimeStamp("fileCfg")
    GM:LoadFileConfig()
  elseif self.m_eStage == ELoadingStage.FileLoad and GM.ResourceLoader:IsAllResourceLoaded() then
    if not GM.LoadFileConfigFinished then
      GM.LoadFileConfigFinished = true
      GM:OnLoadFileConfigFinished()
      MicrofunProfiler.Instance:EndLaunchTimeStamp(ts)
    end
    self.m_eStage = ELoadingStage.SyncData
    ts = MicrofunProfiler.Instance:AddLaunchTimeStamp("sync")
    GM.SyncModel:SyncDataWhenLoading(self.m_funcCallback)
  elseif self.m_eStage == ELoadingStage.SyncDataFinished and GM.ResourceLoader:IsAllResourceLoaded() then
    if not self.m_bLateInit then
      GM:OnSyncDataFinished()
      GM:LateInit()
      GM.DataBalanceModel:CalculateDiff()
      GM.BIManager:LogAppInstallInfo()
      self.m_bLateInit = true
    end
    MicrofunProfiler.Instance:EndLaunchTimeStamp(ts)
    ts = MicrofunProfiler.Instance:AddLaunchTimeStamp("checkRes")
    self.m_eStage = ELoadingStage.CheckResources
    self:CheckResources()
  elseif self.m_eStage == ELoadingStage.CheckResourcesFinished and GM.ResourceLoader:IsAllResourceLoaded() then
    if not GM.CheckResourcesStageFinished then
      GM.CheckResourcesStageFinished = true
      GM:OnCheckResourcesFinished()
      MicrofunProfiler.Instance:EndLaunchTimeStamp(ts)
      self.m_eStage = ELoadingStage.SceneLoad
      ts = MicrofunProfiler.Instance:AddLaunchTimeStamp("loadscene")
      GM.SceneManager:ActivateGameRoot()
      self.m_eStage = self.m_eStage + 1
      if GameConfig.IsTestMode() then
        ApplicationManager.Instance:LogLoadedBundles()
      end
    end
  elseif self.m_eStage == ELoadingStage.SceneLoadFinished and GM.ResourceLoader:IsAllResourceLoaded() then
    MicrofunProfiler.Instance:EndLaunchTimeStamp(ts)
    self.m_eStage = ELoadingStage.ViewLoad
    ts = MicrofunProfiler.Instance:AddLaunchTimeStamp("loadview")
    GM.ModeViewController:LoadView()
  elseif self.m_eStage == ELoadingStage.ViewLoad and GM.ResourceLoader:IsAllResourceLoaded() then
    MicrofunProfiler.Instance:EndLaunchTimeStamp(ts)
    self.m_eStage = ELoadingStage.Finished
  end
  return true
end

function LoadingLayer:CheckPackageValid()
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest("https://www.baidu.com", "HEAD", 200, 0)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetCallback(function()
    if GM ~= nil then
      if reqCtx.Rcode == ResultCode.Succeeded then
        local headers = GM.HttpManager:ConvertHeaders(reqCtx.ResponseHeaders)
        local date = headers.date
        local now = TimeUtil.GetTimeSecondsFromStr(date)
        local buildTime = TimeUtil.GetTimeSecondsFromStr(ProjectConfig.BUILD_TIME)
        if 0 < now and 0 < buildTime and now - buildTime > Package_Time_Limit then
          Log.Info("TimeOut!!! Plz Install new Package.")
          self:_StopLoadingAndQuit()
          return
        end
      end
      GM.GameModel:CheckPrivacyPolicy(self.m_funcCallback)
    end
  end)
  reqCtx:Send()
end

function LoadingLayer:CheckResources()
  self.m_downloadText.text = GM.GameTextModel:GetText("resource_download_check_desc")
  local labels = GM.DownloadManager:GetForceDownloadLabels()
  GM.SceneManager:SetDownloadSize(nil)
  local callback = function(size)
    self.m_downloadText.text = ""
    if 0 < size then
      GM.SceneManager:SetDownloadSize(size)
      if not GM.DownloadManager:IsSkipConfirmWindow() and Application.internetReachability == NetworkReachability.ReachableViaCarrierDataNetwork then
        local exitCallback = function()
          PlatformInterface.ExitGame()
        end
        local downloadCallback = function()
          GM.DownloadManager:SetSkipConfirmWindow()
          self:_DownloadResources(size, labels)
        end
        GM.UIManager:OpenView(UIPrefabConfigName.DownloadConfirmWindow, size, "resource_download_confirm_quit", exitCallback, downloadCallback)
      else
        self:_DownloadResources(size, labels)
      end
    else
      self.m_funcCallback(true)
    end
  end
  GM.DownloadManager:GetDownloadSizeAsync(labels, callback)
end

function LoadingLayer:_DownloadResources(totalSize, labels)
  local progressCallback = function(progress)
    self.m_downloadSlider:SetValue(progress)
    local totalFormated, unit = GM.DownloadManager:GetFormattedSize(totalSize)
    local current = totalFormated * progress
    self.m_downloadText.text = string.format("%.1f/%.1f", current, totalFormated) .. unit
  end
  local resultCallback = function(result)
    local funcCallbackWrap = function()
      self.m_downloadSlider.gameObject:SetActive(false)
      self.m_easeSlider.gameObject:SetActive(true)
      self.m_funcCallback(result)
    end
    if result then
      function self.m_downloadSlider.consistencyCallback()
        self.m_downloadSlider.consistencyCallback = nil
        
        funcCallbackWrap()
      end
    else
      funcCallbackWrap()
    end
  end
  self.m_easeSlider.gameObject:SetActive(false)
  self.m_downloadSlider.gameObject:SetActive(true)
  self.m_downloadSlider:SetValue(0)
  self.m_downloadText.text = GM.GameTextModel:GetText("resource_download_start_desc")
  GM.DownloadManager:TryDownload(labels, progressCallback, resultCallback)
end

function LoadingLayer:_StopLoadingAndQuit()
  GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, "Package Invalid!", [[
This Package is expired, plz reinstall new Package!
Build Time:]] .. ProjectConfig.BUILD_TIME, GM.GameTextModel:GetText("common_button_ok"), function()
    PlatformInterface.ExitGame()
  end)
end

function LoadingLayer:OnDestroy()
  self:_DestroyServiceFloat()
end

function LoadingLayer:_DestroyServiceFloat()
  if self.m_serviceFloat and self.m_serviceFloat.gameObject and not self.m_serviceFloat.gameObject:IsNull() then
    self.m_serviceFloat:Destroy()
    self.m_serviceFloat = nil
  end
  if self.m_dlServiceFloat and self.m_dlServiceFloat.gameObject and not self.m_dlServiceFloat.gameObject:IsNull() then
    self.m_dlServiceFloat:Destroy()
    self.m_dlServiceFloat = nil
  end
end
