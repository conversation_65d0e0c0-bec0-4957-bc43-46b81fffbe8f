OrderReward = {}
OrderReward.__index = OrderReward
local DEFAULT_ICON_WIDTH = 45
local DEFAULT_ITEM_SCALE = 0.4

function OrderReward:Init(formattedReward)
  local rewardType = formattedReward[PROPERTY_TYPE]
  local fscale
  self.m_layoutGroup.spacing = 2
  self.m_bHasNumber = false
  local iconWidth = DEFAULT_ICON_WIDTH
  if Table.Contain(EPropertyType, rewardType) then
    fscale = 0.5
    if rewardType == EPropertyType.Gold then
      fscale = 0.65
    elseif rewardType == EPropertyType.Energy then
      fscale = 0.62
    elseif rewardType == EPropertyType.CoinRaceToken or rewardType == EPropertyType.PkRaceToken then
      fscale = 1
    elseif rewardType == EPropertyType.TreasureDigToken then
      fscale = 0.7
    elseif rewardType == EPropertyType.BlindChest1 then
      fscale = 0.3
    elseif AlbumActivityModel.IsAlbumPackReward(rewardType) then
      fscale = 0.35
    end
    self.m_iconImage.transform.localScale = V3One * fscale
    self.m_iconImage.transform:SetLocalPosY(-1.5)
    SpriteUtil.SetImage(self.m_iconImage, EPropertySprite[rewardType], true)
    self.m_bHasNumber = true
  else
    fscale = DEFAULT_ITEM_SCALE
    if ExtraBoardActivityModel.IsExtraBoardActivityItem(rewardType) then
      local level = ExtraBoardActivityModel.GetActiveModel():GetLevelByItemCode(rewardType)
      if level == 1 then
        fscale = 0.55
      elseif level == 2 then
        fscale = 0.5
      elseif level == 3 then
        fscale = 0.48
      end
      iconWidth = DEFAULT_ICON_WIDTH * fscale / DEFAULT_ITEM_SCALE
    end
    self.m_iconImage.transform.localScale = V3One * fscale
    self.m_iconImage.transform:SetLocalPosY(0)
    local spriteName = GM.ItemDataModel:GetSpriteName(rewardType)
    SpriteUtil.SetImage(self.m_iconImage, spriteName, true)
  end
  local txtWidth = 0
  local count = formattedReward[PROPERTY_COUNT]
  if count and (1 < count or self.m_bHasNumber) then
    self.m_numberText.text = count
    self.m_numberTrans:SetSizeDeltaWidth(self.m_numberText.preferredWidth)
    txtWidth = self.m_numberTrans.sizeDelta.x + 4
    self.m_bHasNumber = true
  else
    self.m_numberText.text = ""
    self.m_numberTrans:SetSizeDeltaWidth(0)
  end
  self.transform:SetSizeDeltaWidth(txtWidth + iconWidth)
  return self.transform.sizeDelta.x
end

function OrderReward:AddSpace(space)
  if self.m_bHasNumber then
    self.m_layoutGroup.spacing = 2 + space
    self.transform:SetSizeDeltaWidth(self.transform.sizeDelta.x + space)
    return true
  end
  return false
end
