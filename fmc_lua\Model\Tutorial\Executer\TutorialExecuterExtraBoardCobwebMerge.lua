local Step = {MergeCobweb = "1", HighlightCobweb = "2"}
local EStep2TextKey = {
  [Step.HighlightCobweb] = "extraboardweb_guide_2"
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.ItemMerged, self, self._OnItemMerged)
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self._TryStartTutorial)
  EventDispatcher.AddListener(EEventType.ItemSpread, self, self._TryStartTutorial)
  EventDispatcher.AddListener(EEventType.OpenView, self, self._TryStartTutorial)
  EventDispatcher.AddListener(EEventType.TutorialFinished, self, self._TryStartTutorial)
  for _, v in pairs(ExtraBoardActivityDefinition) do
    EventDispatcher.AddListener(v.StateChangedEvent, self, self._OnActivityStateChanged)
  end
end

function Executer:_OnActivityStateChanged()
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    self:Finish(self.m_gesture)
  end
end

function Executer:_OnItemMerged()
  if StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    self:_TryStartTutorial()
  elseif self.m_strOngoingDatas == Step.MergeCobweb then
    DelayExecuteFunc(function()
      self:_ExecuteStep2()
    end, 0.5)
  end
end

function Executer:_TryStartTutorial()
  local activeModel = ExtraBoardActivityModel.GetActiveModel()
  local boardModel = activeModel and activeModel:GetBoardModel()
  if boardModel ~= nil and GM.UIManager:GetOpenedViewByName(activeModel:GetActivityDefinition().MainWindowPrefabName) ~= nil and not GM.TutorialModel:HasAnyStrongTutorialOngoing() and boardModel:IsCobwebOpen() and boardModel:HasFinishedAllMainCobweb() and GM.TutorialModel:IsTutorialFinished(ETutorialId.ExtraBoardCobwebUnlock) and not Table.IsEmpty(self:_GetCbItemsToMerge(boardModel)) and StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    self.m_activityModel = activeModel
    self.m_activityDefinition = activeModel:GetActivityDefinition()
    self.m_boardModel = boardModel
    self:_ExecuteStep1()
  end
end

function Executer:_GetCbItemsToMerge(boardModel)
  local cbItems = boardModel:GetCobwebItems()
  table.sort(cbItems, function(a, b)
    local levelA = GM.ItemDataModel:GetChainLevel(a:GetComponent(ItemCobweb):GetInnerItemCode())
    local levelB = GM.ItemDataModel:GetChainLevel(b:GetComponent(ItemCobweb):GetInnerItemCode())
    return levelA < levelB
  end)
  local itemType, targetItem
  for _, item in ipairs(cbItems) do
    itemType = item:GetComponent(ItemCobweb):GetInnerItemCode()
    targetItem = boardModel:GetOneBoardItemByType(itemType)
    if targetItem ~= nil then
      return {targetItem, item}
    end
  end
end

function Executer:_ExecuteStep1()
  self.m_strOngoingDatas = Step.MergeCobweb
  self:SetStrongTutorial(true)
  GM.TutorialModel:SetTutorialFinished(self:GetTutorialId())
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local window = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
    local boardRect = window and window:GetBoardHighlightRoot()
    local arrItems = self:_GetCbItemsToMerge(self.m_boardModel)
    local boardView = GM.ModeViewController:GetExtraBoardActivityBoardView()
    if window == nil or boardRect == nil or Table.IsEmpty(arrItems) or boardView == nil then
      self:Finish()
      return
    end
    TutorialHelper.WholeMask()
    TutorialHelper.HighlightForUI(boardRect, false)
    self.m_boardRect = boardRect
    local source = arrItems[1]:GetPosition()
    local target = arrItems[2]:GetPosition()
    GM.TutorialModel:SetForceSourceBoardPosition(source)
    GM.TutorialModel:SetForceTargetBoardPosition(target)
    self.m_gesture = TutorialHelper.DragOnItems(source, target, boardView)
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  end, 0.5)
end

function Executer:_ExecuteStep2()
  self.m_strOngoingDatas = Step.HighlightCobweb
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  GM.TutorialModel:SetForceSourceBoardPosition(nil)
  GM.TutorialModel:SetForceTargetBoardPosition(nil)
  if self.m_boardRect ~= nil and not self.m_boardRect:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_boardRect)
    self.m_boardRect = nil
  end
  TutorialHelper.HideGesture(self.m_gesture)
  local window = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
  local cobwebBgRect = window and window:GetCobwebBgRect()
  if window == nil or cobwebBgRect == nil then
    self:Finish()
    return
  end
  TutorialHelper.UpdateMask(cobwebBgRect.position, cobwebBgRect.sizeDelta, function()
    self:Finish()
  end, false)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), 55)
end

function Executer:Finish(...)
  if self.m_boardRect ~= nil and not self.m_boardRect:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_boardRect)
    self.m_boardRect = nil
  end
  GM.TutorialModel:SetForceSourceBoardPosition(nil)
  GM.TutorialModel:SetForceTargetBoardPosition(nil)
  TutorialExecuter.Finish(self, ...)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
