PinataNotificationHelper = setmetatable({}, DashActivityNotificationHelper)
PinataNotificationHelper.__index = PinataNotificationHelper

function PinataNotificationHelper.IsSceneExist(strScene)
  if strScene == NotificationScene.PinataStart or strScene == NotificationScene.PinataEnd then
    return true
  end
  return false
end

function PinataNotificationHelper.Generate(strScene)
  local results = {}
  local model = GM.ActivityManager:GetModel(ActivityType.Pinata)
  local state = model:GetState()
  local strTileKey, strDescKey = GM.NotificationModel:GetTextTileAndDesc(strScene)
  if state == ActivityState.Preparing and strScene == NotificationScene.PinataStart then
    strTileKey = strTileKey ~= "" and strTileKey or "push_activity_pinata_open_title"
    strDescKey = strDescKey ~= "" and strDescKey or "push_activity_pinata_open_desc"
    DashActivityNotificationHelper._GenerateDashStartNotification(results, model, NotificationType.Lollipop, strTileKey, strDescKey)
  elseif state == ActivityState.Started and strScene == NotificationScene.PinataEnd then
    strTileKey = strTileKey ~= "" and strTileKey or "push_activity_Pinata_end_title"
    strDescKey = strDescKey ~= "" and strDescKey or "push_activity_Pinata_end_desc"
    DashActivityNotificationHelper._GenerateDashEndNotification(results, model, NotificationType.Lollipop, strTileKey, strDescKey)
  end
  return results
end
