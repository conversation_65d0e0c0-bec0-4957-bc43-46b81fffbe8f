Exclamation = {}
Exclamation.__index = Exclamation
Scheduler.Schedule(function()
  EventDispatcher.DispatchEvent(EEventType.Exclamation)
end, nil, 2)

function Exclamation:OnEnable()
  EventDispatcher.AddListener(EEventType.Exclamation, self, self.PlayAnimation)
end

function Exclamation:OnDisable()
  EventDispatcher.RemoveTarget(self)
end

function Exclamation:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function Exclamation:PlayAnimation()
  SafeCall(function()
    self.m_animator:Play("Ani_Exclamation", 0, 0)
  end)
end
