AlbumPackRewardBubble = setmetatable({}, UIRewardBubble)
AlbumPackRewardBubble.__index = AlbumPackRewardBubble

function AlbumPackRewardBubble:Init(iconRect, rewardType)
  UIRewardBubble.Init(self, iconRect)
  local albumModel = AlbumActivityModel.GetActiveModel()
  if not albumModel then
    self:_DoRecyle()
    return
  end
  local packInfo = albumModel:GetCardTipInfo(rewardType)
  if packInfo == nil then
    self:_DoRecyle()
    return
  end
  local bGold = packInfo.isGold
  self.m_descText.text = GM.GameTextModel:GetText(bGold and "gpack_desc" or "pack_desc")
  local starNum = albumModel:GetShowStar(packInfo.star)
  self.m_arrStar = self.m_arrStar or {}
  UIUtil.SetActive(self.m_starGo, false)
  for _, starGo in ipairs(self.m_arrStar) do
    UIUtil.SetActive(starGo, false)
  end
  for i = 1, starNum do
    if self.m_arrStar[i] == nil then
      self.m_arrStar[i] = GameObject.Instantiate(self.m_starGo, self.m_starGo.transform.parent)
    end
    UIUtil.SetActive(self.m_arrStar[i], true)
  end
  self.m_spreadNumText.text = "x" .. packInfo.count
end

function AlbumPackRewardBubble:OnInfoButtonClicked()
  local titleText = GM.GameTextModel:GetText("album_pack_detail_title")
  local desc = GM.GameTextModel:GetText("album_pack_detail_desc")
  local btnText = GM.GameTextModel:GetText("album_pack_detail_btn")
  GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, titleText, desc, btnText, function()
    CSPlatform:OpenURL(NetworkConfig.GetProbabilityDisclosureLink())
  end, nil, true)
end

function AlbumPackRewardBubble:GetMapInteractableObjs()
  if self.m_mapObj == nil then
    self.m_mapObj = {}
    self.m_mapObj[self.m_btnGo] = true
  end
  return self.m_mapObj
end
