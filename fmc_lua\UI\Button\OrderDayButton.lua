OrderDayButton = setmetatable({}, HudGeneralButton)
OrderDayButton.__index = OrderDayButton

function OrderDayButton:Awake()
  EventDispatcher.AddListener(EEventType.OrderDayChanged, self, self._OnOrderDayChanged)
  EventDispatcher.AddListener(EEventType.NewReleasedOrderGroupUnlocked, self, self.UpdateTextAnimation)
  HudGeneralButton.Awake(self)
  self:_OnOrderDayChanged()
end

function OrderDayButton:UpdateTextAnimation()
  self.m_calender:UpdateContent()
end

function OrderDayButton:_OnOrderDayChanged()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    self.m_calender:UpdateContent()
  end
end

function OrderDayButton:OnClicked()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  if orderModel:IsGuideGroup() then
    return
  end
  local cleared = orderModel:IsAllCanUnlockGroupFinished()
  if cleared then
    GM.UIManager:OpenView(UIPrefabConfigName.OrderDayClearWindow)
  else
    GM.UIManager:OpenView(UIPrefabConfigName.OrderDayWindow)
  end
end
