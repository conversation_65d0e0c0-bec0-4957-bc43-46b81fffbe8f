TimelineLayer = {}
TimelineLayer.__index = TimelineLayer

function TimelineLayer:Awake()
  GM:<PERSON>d<PERSON><PERSON><PERSON>("TimelineLayer", self)
  self.m_topMaskTrans:SetSizeDeltaHeight(0)
  self.m_btmMaskTrans:SetSizeDeltaHeight(0)
end

function TimelineLayer:ToggleEdgeMask(isShow, callback)
  local dt = 0.5
  local sizeDelta = Vector2(self.m_topMaskTrans.sizeDelta.x, isShow and 200 or 0)
  self.m_topMaskTrans:DOKill()
  self.m_topMaskTrans:DOSizeDelta(sizeDelta, dt)
  self.m_btmMaskTrans:DOKill()
  self.m_btmMaskTrans:DOSizeDelta(sizeDelta, dt)
  if self.m_edgeMaskTween then
    self.m_edgeMaskTween:Complete(true)
    self.m_edgeMaskTween = nil
  end
  local s = DOTween.Sequence()
  if callback then
    s:AppendCallback(callback)
  end
  s:SetDelay(dt):AppendCallback(function()
    self.m_edgeMaskTween = nil
  end)
  self.m_edgeMaskTween = s
end

function TimelineLayer:ShowColorTransition(color, callback, dt)
  if self.m_showColorTransTween then
    self.m_showColorTransTween:Kill()
  end
  color.a = self.m_colorMaskImg.color.a
  self.m_colorMaskImg.color = color
  local s = DOTween.Sequence()
  s:Append(self.m_colorMaskImg:DOFade(1, dt or 0.5))
  s:AppendCallback(function()
    self.m_showColorTransTween = nil
    if callback then
      callback()
    end
  end)
  self.m_showColorTransTween = s
end

function TimelineLayer:HideColorTransition(callback, dt)
  if self.m_hideColorTransTween then
    self.m_hideColorTransTween:Kill()
  end
  local s = DOTween.Sequence()
  s:Append(self.m_colorMaskImg:DOFade(0, dt or 0.5))
  s:AppendCallback(function()
    self.m_hideColorTransTween = nil
    if callback then
      callback()
    end
  end)
  self.m_hideColorTransTween = s
end

function TimelineLayer:PlayStartVideo()
  GM.UIManager:SetEventLock(true)
  if GameConfig.IsTestMode() and PlayerPrefs.GetInt(EPlayerPrefKey.TestSkipStartVideo, 0) == 1 then
    self:_OnVideoFinished()
    return
  end
  local url = GM.ConfigModel:GetCGPath()
  if url == nil or not File.Exists(url) then
    self:_OnVideoFinished()
    return
  end
  self.m_colorMaskImg.color = CSColor.black
  self.m_cgPlayer.gameObject:SetActive(true)
  self.m_cgPlayer:Play(url, function(success, msg)
    self:_OnVideoFinished()
    if not success then
      GM.BIManager:LogErrorInfo(EBIType.PlayStartVideoError, msg)
    end
  end)
end

function TimelineLayer:_OnVideoFinished()
  GM.UIManager:SetEventLock(false)
  EventDispatcher.DispatchEvent(EEventType.InitVideoFinished)
  self:HideColorTransition()
end

function TimelineLayer:IsPlayingVideo()
  return self.m_cgPlayer and self.m_cgPlayer:IsPlayingVideo()
end
