TestAlbumGachaWindow = setmetatable({}, BaseWindow)
TestAlbumGachaWindow.__index = TestAlbumGachaWindow

function TestAlbumGachaWindow:BeforeOpenCheckt()
  if AlbumActivityModel.GetActiveModel() == nil then
    return false
  end
  return true
end

function TestAlbumGachaWindow:OnDestroy()
  BaseWindow.OnDestroy(self)
  self.m_co = nil
end

function TestAlbumGachaWindow:Init()
  self.m_model = AlbumActivityModel.GetActiveModel()
  self.m_logs = {}
  self.m_logs[1] = self.m_LOGText
  self.m_ShowText = ""
  local allBackPack = self.m_model:GetAlbumPack()
  if allBackPack ~= nil then
    local options = CS.System.Collections.Generic.List(CS.System.String)()
    for _, v in pairs(allBackPack) do
      options:Add(v.packId)
    end
    options:Sort()
    self.m_options = options
    self.m_Dropdown:AddOptions(options)
  end
  local AlbumConfig = self.m_model:GetAlbumConfig()
  local cardGroups = AlbumConfig.setGroup
  if cardGroups ~= nil then
    local options = CS.System.Collections.Generic.List(CS.System.String)()
    for _, v in pairs(cardGroups) do
      options:Add(v)
    end
    options:Sort()
    self.m_cardGroups = options
    self.m_setGroup:AddOptions(options)
  end
  local cardConfig = self.m_model.m_mapCards
  self.m_arrAllStar = self.m_model.m_arrAllStar
  self.m_starNum = {}
  self.m_starCardList = {}
  for _, cardInfo in pairs(cardConfig) do
    if self.m_starNum[tostring(cardInfo.star)] == nil then
      self.m_starNum[tostring(cardInfo.star)] = 0
    end
    if self.m_starCardList[tostring(cardInfo.star)] == nil then
      self.m_starCardList[tostring(cardInfo.star)] = {}
    end
    table.insert(self.m_starCardList[tostring(cardInfo.star)], cardInfo.cardId)
    self.m_starNum[tostring(cardInfo.star)] = self.m_starNum[tostring(cardInfo.star)] + 1
  end
end

function TestAlbumGachaWindow:OnBtnCopyLog()
  CS.UnityEditor.EditorGUIUtility.systemCopyBuffer = self.m_ShowText
  GM.UIManager:ShowPrompt("复制成功 该功能仅限使用UnityEditor使用")
end

function TestAlbumGachaWindow:JudgeStarFinish(star)
  local cardConfig = self.m_model.m_mapCards
  for cardID, cardInfo in pairs(cardConfig) do
    if self.m_model:GetCardCount(cardID) == 0 and cardInfo.star == star then
      return false
    end
  end
  return true
end

function TestAlbumGachaWindow:JudgeCardGroupFinish(cardGroup)
  local curNum, maxNum = self.m_model:GetSetCollectProgress(cardGroup)
  if curNum < maxNum then
    return false
  end
  return true
end

function TestAlbumGachaWindow:OnCalculateMath()
  self.PackSpendNum = {}
  self.StarSpendNum = {}
  self.m_StartIndex = 1
  local i32Num = tonumber(self.m_NumInput.text)
  self.m_OpenNum = i32Num or 10000
  if self.m_co ~= nil then
    return
  end
  self.m_co = coroutine.create(function()
    self:StartCalculate()
  end)
  coroutine.resume(self.m_co)
end

function TestAlbumGachaWindow:Update()
  if self.m_co ~= nil then
    coroutine.resume(self.m_co)
  end
end

function TestAlbumGachaWindow:StartCalculate()
  local time = CS.UnityEngine.Time.realtimeSinceStartup
  while self.m_StartIndex ~= self.m_OpenNum do
    self.m_StartIndex = self.m_StartIndex + 1
    local curPackNum, starPackNum = self:OnCalculate(true)
    for strPackID, num in pairs(curPackNum) do
      if self.PackSpendNum[strPackID] == nil then
        self.PackSpendNum[strPackID] = 0
      end
      self.PackSpendNum[strPackID] = self.PackSpendNum[strPackID] + num
    end
    for strStarID, num in pairs(starPackNum) do
      if self.StarSpendNum[strStarID] == nil then
        self.StarSpendNum[strStarID] = 0
      end
      self.StarSpendNum[strStarID] = self.StarSpendNum[strStarID] + num
    end
    local curTime = CS.UnityEngine.Time.realtimeSinceStartup
    if 1 < curTime - time then
      coroutine.yield()
      self.m_ShowText = "当前计算到第" .. tostring(self.m_StartIndex) .. "个,一共要计算" .. tostring(self.m_OpenNum)
      self.m_logs[1].text = self.m_ShowText
    end
  end
  local strTarget = ""
  strTarget = strTarget .. "集齐所有卡牌的时候，每种卡包消耗的平均数:"
  for strPackID, num in pairs(self.PackSpendNum) do
    strTarget = strTarget .. strPackID .. ":" .. tostring(num / self.m_OpenNum) .. "\n"
  end
  strTarget = strTarget .. "集齐所有卡牌的时候， 每个星级卡牌获得的平均数："
  for strStar, num in pairs(self.StarSpendNum) do
    strTarget = strTarget .. strStar .. ":" .. tostring(num / self.m_OpenNum) .. "\n"
  end
  self.m_ShowText = strTarget
  self.m_logs[1].text = strTarget
  local height = self.m_logs[1].preferredHeight
  self.m_logs[1].transform.sizeDelta = Vector2(783, height)
  self.m_co = nil
end

function TestAlbumGachaWindow:OnCalculate(noText)
  local strInputNum = self.m_caucalateInpu.text
  local listNum = StringUtil.Split(strInputNum, ",")
  if #listNum ~= self.m_options.Count then
    GM.UIManager:ShowPrompt("输入的数量和卡包的数量对不上")
    return
  end
  self.m_model:ClearAllCard()
  GM.UIManager:ShowPrompt("已清空 集卡所有数据")
  local starPackNum = {}
  local starPackInfo = {}
  local cardGroupNum = {}
  local cardGroupInfo = {}
  local CurCardOpenNum = {}
  local cardFinishQueue = {}
  local JudgeStarFinish = function(strStar)
    if starPackNum[strStar] == nil and self:JudgeStarFinish(tonumber(strStar)) then
      starPackNum[strStar] = {}
      for strPackID, num in pairs(CurCardOpenNum) do
        starPackNum[strStar][strPackID] = num
      end
      starPackInfo[strStar] = {}
      for _, star in ipairs(self.m_arrAllStar) do
        local curNum = 0
        for _, cardID in pairs(self.m_starCardList[tostring(star)] or {}) do
          curNum = curNum + (0 < self.m_model:GetCardCount(cardID) and 1 or 0)
        end
        starPackInfo[strStar][tostring(star)] = curNum
      end
    end
  end
  local JudgeCardGropuFinsih = function(cardGroup)
    if cardGroupNum[cardGroup] == nil and self:JudgeCardGroupFinish(cardGroup) then
      table.insert(cardFinishQueue, cardGroup)
      cardGroupNum[cardGroup] = {}
      for strPackID, num in pairs(CurCardOpenNum) do
        cardGroupNum[cardGroup][strPackID] = num
      end
      cardGroupInfo[cardGroup] = {}
      local AlbumConfig = self.m_model:GetAlbumConfig()
      local cardGroups = AlbumConfig.setGroup
      for _, eleGroup in pairs(cardGroups) do
        local curNum, maxNum = self.m_model:GetSetCollectProgress(eleGroup)
        cardGroupInfo[cardGroup][eleGroup] = tostring(curNum) .. "/" .. tostring(maxNum)
      end
    end
  end
  local maxNum = 1000
  while true do
    maxNum = maxNum - 1
    if maxNum <= 0 then
      break
    end
    local bFinish = true
    for index, num in ipairs(listNum) do
      local strPackID = self.m_options[index - 1]
      for i = 1, num do
        local AlbumConfig = self.m_model:GetAlbumConfig()
        local cardGroups = AlbumConfig.setGroup
        for _, cardGroup in pairs(cardGroups) do
          local curNum, maxNum = self.m_model:GetSetCollectProgress(cardGroup)
          if curNum < maxNum then
            bFinish = false
            break
          end
        end
        if bFinish then
          break
        end
        self.m_model:OpenPackOneConfig(strPackID)
        if CurCardOpenNum[strPackID] == nil then
          CurCardOpenNum[strPackID] = 0
        end
        CurCardOpenNum[strPackID] = CurCardOpenNum[strPackID] + 1
        if not noText then
          for _, star in ipairs(self.m_arrAllStar) do
            JudgeStarFinish(tostring(star))
          end
          for _, cardGroup in pairs(cardGroups) do
            JudgeCardGropuFinsih(cardGroup)
          end
        end
      end
      if bFinish then
        break
      end
    end
    if bFinish then
      break
    end
  end
  if not noText then
    for _, v in pairs(self.m_logs) do
      v.text = ""
    end
    self.m_ShowText = ""
    local strTarget = ""
    strTarget = strTarget .. "卡组完成顺序：\n"
    for _, cardId in ipairs(cardFinishQueue) do
      strTarget = strTarget .. cardId .. " "
    end
    strTarget = strTarget .. "\n 所有卡牌收集的数量: \n"
    local cardConfig = self.m_model.m_mapCards
    for cardID, cardInfo in pairs(cardConfig) do
      local cardCount = self.m_model:GetCardCount(cardID)
      strTarget = strTarget .. cardID .. ":" .. cardCount .. " "
    end
    strTarget = strTarget .. "\n 集齐每个星级的卡牌时开各星级卡包的数量: \n"
    for star, info in pairs(starPackNum) do
      strTarget = strTarget .. "star:" .. star .. ": \n"
      for packId, num in pairs(info) do
        strTarget = strTarget .. packId .. ":" .. num .. ";"
      end
      strTarget = strTarget .. "\n"
    end
    strTarget = strTarget .. "\n 集齐每个星级的卡牌时各星级卡牌收集的进度: \n"
    for star, info in pairs(starPackInfo) do
      strTarget = strTarget .. "star:" .. star .. ": \n"
      for starIndex, num in pairs(info) do
        strTarget = strTarget .. "star" .. starIndex .. ":" .. num .. "/" .. self.m_starNum[starIndex] .. ";"
      end
      strTarget = strTarget .. "\n"
    end
    strTarget = strTarget .. "\n 每个卡组集齐时 消耗各星级卡包的数量"
    for cardId, info in pairs(cardGroupNum) do
      strTarget = strTarget .. cardId .. ": \n"
      for packId, num in pairs(info) do
        strTarget = strTarget .. packId .. ":" .. num .. ";"
      end
      strTarget = strTarget .. "\n"
    end
    strTarget = strTarget .. "\n 每个卡组集齐时 各卡组进度"
    for cardId, info in pairs(cardGroupInfo) do
      strTarget = strTarget .. cardId .. ": \n"
      for packId, strInfo in pairs(info) do
        strTarget = strTarget .. packId .. ":" .. strInfo .. ";"
      end
      strTarget = strTarget .. "\n"
    end
    self.m_ShowText = strTarget
    self.m_logs[1].text = strTarget
    local height = self.m_logs[1].preferredHeight
    self.m_logs[1].transform.sizeDelta = Vector2(783, height)
  end
  local targetCurStar = {}
  for _, star in ipairs(self.m_arrAllStar) do
    local starNum = self.m_model:GetCardCountByStar(star)
    targetCurStar[tostring(star)] = starNum
  end
  return CurCardOpenNum, targetCurStar
end

function TestAlbumGachaWindow:OnBtnAddCardGroup()
  local packID = self.m_Dropdown.captionText.text
  local rewards = {
    {
      [PROPERTY_TYPE] = packID,
      [PROPERTY_COUNT] = 1
    }
  }
  RewardApi.CryptRewards(rewards)
  RewardApi.AcquireRewards(rewards, EPropertySource.Give, EBIType.Test, {eventLock = false}, EGameMode.Main, CacheItemType.Stack)
end

function TestAlbumGachaWindow:OnBtnOpenSetFinishWindow()
  GM.UIManager:OpenView(self.m_model:GetActivityDefinition().TakeSetRewardPrefabName, self.m_model:GetType(), {"set1"})
end

function TestAlbumGachaWindow:OnBtnOpenAlbumFinishWindow()
  GM.UIManager:OpenView(self.m_model:GetActivityDefinition().EndWindowPrefabName, self.m_model:GetType())
end

function TestAlbumGachaWindow:OnStartGacha()
  local packID = self.m_Dropdown.captionText.text
  local i32Num = tonumber(self.m_NumInput.text)
  local PackConfig = self.m_model:GetCardPackConfig(packID)
  if PackConfig == nil then
    GM.UIManager:ShowPrompt("没有这个卡包，检查下卡包ID")
    return
  end
  self.m_ShowText = ""
  for i = 1, i32Num do
    local LogTable = {}
    LogTable.FixedStarNum = {}
    LogTable.resultNature = {}
    LogTable.listGachaResult = {}
    local target = self.m_model:OpenPackOneConfig(packID, LogTable)
    local strLog = ""
    strLog = "当前是第" .. tostring(i) .. "个卡包 \n"
    strLog = strLog .. "卡包固定抽取的星级及个数:\n"
    for fixedstar, info in pairs(LogTable.FixedStarNum) do
      strLog = strLog .. info[PROPERTY_TYPE] .. ":" .. info[PROPERTY_COUNT] .. "\n"
    end
    strLog = strLog .. "卡包随机抽取中自然随机 + 固定星级及个数:\n"
    for naturestar, naturenum in pairs(LogTable.resultNature) do
      strLog = strLog .. naturestar .. ":" .. naturenum .. "\n"
    end
    strLog = strLog .. "按照星级开始随机星级卡\n"
    for index, cardInfo in ipairs(LogTable.listGachaResult) do
      strLog = strLog .. "星级:" .. tostring(cardInfo.i32Star) .. "    "
      strLog = strLog .. "保底状态:"
      local i32State = cardInfo.state
      i32State = i32State % 10
      if i32State == 2 then
        strLog = strLog .. "正向保底"
      else
        strLog = strLog .. "无保底"
      end
      strLog = strLog .. "\n"
      strLog = strLog .. "当前可随机到的卡片"
      for _, cardConfig in ipairs(cardInfo.optionals) do
        strLog = strLog .. cardConfig.cardId .. ":" .. cardConfig.weight .. " "
      end
      strLog = strLog .. "\n"
      strLog = strLog .. "最终选择的卡牌:" .. cardInfo.targetcard .. "\n"
      strLog = strLog .. "是否为新卡: " .. tostring(cardInfo.newCard) .. "\n"
    end
    strLog = strLog .. "\n 当前卡包开出的卡片"
    for _, eleCard in ipairs(target) do
      strLog = strLog .. " " .. eleCard
    end
    self.m_ShowText = self.m_ShowText .. strLog
    self.m_ShowText = self.m_ShowText .. "/n ------------------------------------"
    if self.m_logs[i] == nil then
      self.m_logs[i] = Object.Instantiate(self.m_LOGText.gameObject, self.m_ContentRectTrans):GetComponent(typeof(CS.UnityEngine.UI.Text))
    end
    self.m_logs[i].text = strLog
    local height = self.m_logs[i].preferredHeight
    self.m_logs[i].transform.sizeDelta = Vector2(783, height)
    Debug.Log(strLog)
  end
end

function TestAlbumGachaWindow:OnBtnCardGroup()
  local groupId = self.m_setGroup.captionText.text
  local allBackPack = self.m_model:GetAlbumPack()
  local index = 1
  while not self:JudgeCardGroupFinish(groupId) do
    local packID = self.m_options[index - 1]
    index = index % self.m_options.Count + 1
    local openCards, mapNewCards, ListSetFinish, bAllAlbum = self.m_model:OpenPackOneConfig(packID)
    if self:JudgeCardGroupFinish(groupId) then
      GM.UIManager:OpenView(UIPrefabConfigName.AlbumActivityGachaWindow, self.m_model:GetType(), openCards, mapNewCards, ListSetFinish, bAllAlbum, packID)
      break
    end
  end
end

function TestAlbumGachaWindow:OnBtnOpenCycleWindow()
  GM.UIManager:OpenView(self.m_model:GetActivityDefinition().RecyleCardPrefabName, self.m_model:GetType())
end

function TestAlbumGachaWindow:OnGachaLastOneCard()
  local index = 1
  local Num = 0
  while true do
    local curCard, MaxCard = self.m_model:GetAlbumCollectProgress()
    Num = Num + 1
    if 1000 < Num or curCard == MaxCard - 1 then
      break
    end
    local packID = self.m_options[index - 1]
    index = index % self.m_options.Count + 1
    local openCards, ListSetFinish, bAllAlbum = self.m_model:OpenPackOneConfig(packID)
  end
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
end

function TestAlbumGachaWindow:OnCompleteAlbum()
  self.m_model:TestCompleteAlbum()
end

function TestAlbumGachaWindow:OnAlbumInfo()
  GM.UIManager:OpenView(UIPrefabConfigName.TestAlbumInfoWindow)
end

function TestAlbumGachaWindow:OnCompleteNormal()
  self.m_model:TestCompleteNormalCard()
end

function TestAlbumGachaWindow:OnJokerCard()
  local rewards = {
    {
      [PROPERTY_TYPE] = EPropertyType.JokerCommonCard,
      [PROPERTY_COUNT] = 1
    }
  }
  RewardApi.CryptRewards(rewards)
  RewardApi.AcquireRewards(rewards, EPropertySource.Give, EBIType.Test, {eventLock = false}, EGameMode.Board, CacheItemType.Stack)
end
