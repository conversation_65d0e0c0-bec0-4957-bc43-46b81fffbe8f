SurveyFillBlankQuestion = setmetatable({}, BaseSurveyQuestion)
SurveyFillBlankQuestion.__index = SurveyFillBlankQuestion

function SurveyFillBlankQuestion:Init(questionData)
  self.m_questionData = questionData
  self.m_title.text = self.m_questionData.title
  self:_InitPicture()
  if self.m_contentRectTrans then
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_contentRectTrans)
  end
end

function SurveyFillBlankQuestion:CanNextStep()
  if self.m_questionData.rule == "free" then
    return true
  end
  if self.m_questionData.isSkip then
    return true
  end
  if StringUtil.IsNilOrEmpty(self.m_inputField.text) then
    GM.UIManager:ShowPromptWithKey("question_error_tips")
    return false
  end
  return true
end

function SurveyFillBlankQuestion:GetUserAnswer()
  local answers = {}
  local nextQIds = {}
  local options = self.m_questionData.options
  if not Table.IsEmpty(options) and options[1].visible_ids ~= nil then
    nextQIds = options[1].visible_ids
  end
  if not StringUtil.IsNilOrEmpty(self.m_inputField.text) then
    answers[EAnswerKey.Vmemo] = self.m_inputField.text
  end
  return answers, nextQIds
end
