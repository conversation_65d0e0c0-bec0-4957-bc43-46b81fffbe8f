return {
  {
    Type = "finishOrder",
    CloseLevel = 5,
    Priority = 1
  },
  {
    Type = "finishTask",
    CloseLevel = 5,
    Priority = 2
  },
  {
    Type = "testStartCook",
    CloseLevel = 0,
    Priority = 3
  },
  {
    Type = "testTakeOutDish",
    CloseLevel = 0,
    Priority = 4
  },
  {
    Type = "testTakeOutMaterial",
    CloseLevel = 0,
    Priority = 5
  },
  {
    Type = "lastOrder",
    CloseLevel = 5,
    Priority = 6
  },
  {Type = "mergeItems", Priority = 7},
  {
    Type = "tapCacheItems",
    CloseLevel = 5,
    Priority = 8
  },
  {
    Type = "testOpenChest",
    CloseLevel = 0,
    Priority = 9
  },
  {
    Type = "testRetrieveItem",
    CloseLevel = 0,
    Priority = 10
  },
  {
    Type = "tapSpreadItem",
    CloseLevel = 5,
    Priority = 11
  },
  {
    Type = "testSpreadItem",
    CloseLevel = 0,
    Priority = 11
  },
  {
    Type = "testRecoverSpread",
    CloseLevel = 0,
    Priority = 12
  },
  {
    Type = "testCollectAll",
    CloseLevel = 0,
    Priority = 13
  },
  {
    Type = "testStoreItem",
    CloseLevel = 0,
    Priority = 14
  }
}
