PassActivityBaseWindow = setmetatable({
  bNeedSetWindowOpened = true,
  bWithoutCloseAnim = true,
  disableEffectWhenCloseView = true,
  disableSpriteRendererWhenCloseView = true
}, BaseWindow)
PassActivityBaseWindow.__index = PassActivityBaseWindow

function PassActivityBaseWindow:Init(activityType, shouldJumpToMap, ext)
  self.m_activityType = activityType
  self.m_activityDefinition = PassActivityDefinition[activityType]
  self.m_model = GM.ActivityManager:GetModel(activityType)
  if self.bNeedSetWindowOpened then
    self.m_model:SetWindowOpened()
  end
  if shouldJumpToMap and GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    GM.SceneManager:ChangeGameMode(EGameMode.Main)
  end
  AddHandlerAndRecordMap(self.m_model.event, PassActivityEventType.StateChanged, {
    obj = self,
    method = self.Close
  })
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.UserClick
  }, ext)
  if self.m_effectSortingGroup ~= nil then
    self.m_effectSortingGroup.sortingOrder = self.m_canvas.sortingOrder + 1
  end
  if self.m_frontCanvas ~= nil then
    self.m_frontCanvas.sortingOrder = self.m_canvas.sortingOrder + 2
  end
  if self.m_frontEffectSortingGroup ~= nil then
    self.m_frontEffectSortingGroup.sortingOrder = self.m_canvas.sortingOrder + 3
  end
  self.windowMaskAlpha = 1 < GM.UIManager:GetOpenedViewCountByType(EViewType.Window) and EWindowMaskAlpha.Dark or EWindowMaskAlpha.Default
end

function PassActivityBaseWindow:OnCloseBtnClick()
  self:Close(self.bWithoutCloseAnim)
end

function PassActivityBaseWindow:OnDestroy()
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
  EventDispatcher.RemoveTarget(self)
end

function PassActivityBaseWindow:OnCloseView(bWithoutAnimation)
  if self.m_effectSortingGroup ~= nil then
    UIUtil.SetActive(self.m_effectSortingGroup.gameObject, false)
  end
  if self.m_flashGo then
    self.m_flashGo:SetActive(false)
  end
  BaseWindow.OnCloseView(self, bWithoutAnimation)
end

PassActivityAnimationComponent = {}
PassActivityAnimationComponent.__index = PassActivityAnimationComponent

function PassActivityAnimationComponent:Start()
  if self.m_frontCanvas == nil or self.m_frontCanvas:IsNull() then
    Log.Error("[PassActivityAnimationComponent] 组件缺少m_frontCanvas引用, 请检查!")
    return
  end
  local orderDelta = self.m_frontOrderNum and tonumber(self.m_frontOrderNum) or 4
  self.m_canvas = self.gameObject:GetComponentInParent(typeof(CS.UnityEngine.Canvas))
  if self.m_canvas ~= nil then
    self.m_frontCanvas.sortingOrder = self.m_canvas.sortingOrder + orderDelta
  end
end
