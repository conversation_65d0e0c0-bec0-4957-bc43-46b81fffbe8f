ShopContainerDailyDeals = setmetatable({}, BaseShopListContainer)
ShopContainerDailyDeals.__index = ShopContainerDailyDeals

function ShopContainerDailyDeals:Awake()
  BaseShopListContainer.Init(self, EShopType.DailyDeals, false, 3)
end

function ShopContainerDailyDeals:_GetCellData()
  local data = {}
  local ids = GM.ShopModel:GetItemIdsDailyDeals() or {}
  for _, id in ipairs(ids) do
    local itemData = GM.ShopModel:GetItemData(id)
    local config = GM.ShopDataModel:GetDailyDealsConfig(itemData.itemCode)
    table.insert(data, {
      Id = id,
      Code = itemData.itemCode,
      Count = itemData.leftCount,
      Price = itemData.costCount,
      RedTag = config.redTag,
      PurpleTag = config.purpleTag
    })
  end
  return data
end

function ShopContainerDailyDeals:_OnCellClicked(cell)
  local success, type, count = GM.ShopModel:BuyItem(self.m_shopType, cell:GetData().Id, false, cell.transform.position)
  if success then
    self:_UpdateContent()
  else
    GM.ShopModel:OnLackOfGem(count)
  end
end
