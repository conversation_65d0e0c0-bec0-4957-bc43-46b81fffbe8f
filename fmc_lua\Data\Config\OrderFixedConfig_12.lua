return {
  {
    Id = "120010",
    GroupId = 1,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6dst_11",
      Count = 1
    }
  },
  {
    Id = "120020",
    GroupId = 1,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120030",
    GroupId = 1,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e5nutt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_1",
      Count = 1
    }
  },
  {
    Id = "120040",
    GroupId = 1,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_2",
      Count = 1
    }
  },
  {
    Id = "120050",
    GroupId = 1,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e1icytre_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120060",
    GroupId = 1,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "120070",
    GroupId = 1,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "120080",
    GroupId = 2,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1mdrk_15",
      Count = 1
    }
  },
  {
    Id = "120090",
    GroupId = 2,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120100",
    GroupId = 2,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    }
  },
  {
    Id = "120110",
    GroupId = 2,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e5bakl_7",
      Count = 1
    }
  },
  {
    Id = "120120",
    GroupId = 2,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_11e2mt_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120130",
    GroupId = 2,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "120140",
    GroupId = 2,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "120150",
    GroupId = 3,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120160",
    GroupId = 3,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1icytre_11",
      Count = 1
    }
  },
  {
    Id = "120170",
    GroupId = 3,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_14", Count = 1}
  },
  {
    Id = "120180",
    GroupId = 3,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_11",
      Count = 1
    }
  },
  {
    Id = "120190",
    GroupId = 3,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_friedsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120200",
    GroupId = 3,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "120210",
    GroupId = 3,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "120220",
    GroupId = 4,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5dst_14",
      Count = 1
    }
  },
  {
    Id = "120230",
    GroupId = 4,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    }
  },
  {
    Id = "120240",
    GroupId = 4,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120250",
    GroupId = 4,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_2",
      Count = 1
    }
  },
  {
    Id = "120260",
    GroupId = 4,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "120270",
    GroupId = 4,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120280",
    GroupId = 4,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "120290",
    GroupId = 5,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "120300",
    GroupId = 5,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "120310",
    GroupId = 5,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120320",
    GroupId = 5,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e4sf_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "120330",
    GroupId = 5,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "120340",
    GroupId = 5,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120350",
    GroupId = 5,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_16",
      Count = 1
    }
  },
  {
    Id = "120360",
    GroupId = 6,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6deli_4",
      Count = 1
    }
  },
  {
    Id = "120370",
    GroupId = 6,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "120380",
    GroupId = 6,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_11e3scsau_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120390",
    GroupId = 6,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "120400",
    GroupId = 6,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6porr_2",
      Count = 1
    }
  },
  {
    Id = "120410",
    GroupId = 6,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120420",
    GroupId = 6,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "120430",
    GroupId = 7,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "120440",
    GroupId = 7,
    ChapterId = 12,
    Requirement_1 = {Type = "it_4_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6dst_11",
      Count = 1
    }
  },
  {
    Id = "120450",
    GroupId = 7,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120460",
    GroupId = 7,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1tato_13",
      Count = 1
    }
  },
  {
    Id = "120470",
    GroupId = 7,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_13",
      Count = 1
    }
  },
  {
    Id = "120480",
    GroupId = 7,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120490",
    GroupId = 7,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "120500",
    GroupId = 8,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    }
  },
  {
    Id = "120510",
    GroupId = 8,
    ChapterId = 12,
    Requirement_1 = {Type = "it_a12_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_7",
      Count = 1
    }
  },
  {
    Id = "120520",
    GroupId = 8,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120530",
    GroupId = 8,
    ChapterId = 12,
    Requirement_1 = {Type = "it_1_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    }
  },
  {
    Id = "120540",
    GroupId = 8,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120550",
    GroupId = 8,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "120560",
    GroupId = 8,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "120570",
    GroupId = 9,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_6",
      Count = 1
    }
  },
  {
    Id = "120580",
    GroupId = 9,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e6dst_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6deli_13",
      Count = 1
    }
  },
  {
    Id = "120590",
    GroupId = 9,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120600",
    GroupId = 9,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "120610",
    GroupId = 9,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "120620",
    GroupId = 9,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120630",
    GroupId = 9,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6deli_11",
      Count = 1
    }
  },
  {
    Id = "120640",
    GroupId = 10,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e6deli_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_8",
      Count = 1
    }
  },
  {
    Id = "120650",
    GroupId = 10,
    ChapterId = 12,
    Requirement_1 = {Type = "it_4_1_7", Count = 1},
    Requirement_2 = {Type = "it_a12_2_5", Count = 1}
  },
  {
    Id = "120660",
    GroupId = 10,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120670",
    GroupId = 10,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "120680",
    GroupId = 10,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120690",
    GroupId = 10,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "120700",
    GroupId = 10,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_7",
      Count = 1
    }
  },
  {
    Id = "120710",
    GroupId = 11,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_11",
      Count = 1
    }
  },
  {
    Id = "120720",
    GroupId = 11,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e1nutt_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120730",
    GroupId = 11,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_7e6sf_22",
      Count = 1
    }
  },
  {
    Id = "120740",
    GroupId = 11,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_14", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120750",
    GroupId = 11,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfru_1",
      Count = 1
    }
  },
  {
    Id = "120760",
    GroupId = 11,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_a12_2_5", Count = 1}
  },
  {
    Id = "120770",
    GroupId = 11,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "120780",
    GroupId = 12,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "120790",
    GroupId = 12,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6deli_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120800",
    GroupId = 12,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_11e6nibble_11",
      Count = 1
    }
  },
  {
    Id = "120810",
    GroupId = 12,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5dst_18",
      Count = 1
    }
  },
  {
    Id = "120820",
    GroupId = 12,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_11e4tato_19",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120830",
    GroupId = 12,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "120840",
    GroupId = 12,
    ChapterId = 12,
    Requirement_1 = {Type = "it_a12_2_5", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "120850",
    GroupId = 13,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_6e2mt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "120860",
    GroupId = 13,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120870",
    GroupId = 13,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "120880",
    GroupId = 13,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_3",
      Count = 1
    }
  },
  {
    Id = "120890",
    GroupId = 13,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "120900",
    GroupId = 13,
    ChapterId = 12,
    Requirement_1 = {Type = "it_a12_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120910",
    GroupId = 13,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "120920",
    GroupId = 14,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "120930",
    GroupId = 14,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120940",
    GroupId = 14,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_6",
      Count = 1
    }
  },
  {
    Id = "120950",
    GroupId = 14,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5dst_19",
      Count = 1
    }
  },
  {
    Id = "120960",
    GroupId = 14,
    ChapterId = 12,
    Requirement_1 = {Type = "it_1_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6rice_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "120970",
    GroupId = 14,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "it_a12_2_5", Count = 1}
  },
  {
    Id = "120980",
    GroupId = 14,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "120990",
    GroupId = 15,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "121000",
    GroupId = 15,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121010",
    GroupId = 15,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1icytre_12",
      Count = 1
    }
  },
  {
    Id = "121020",
    GroupId = 15,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "121030",
    GroupId = 15,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_2",
      Count = 1
    }
  },
  {
    Id = "121040",
    GroupId = 15,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e6deli_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121050",
    GroupId = 15,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "121060",
    GroupId = 16,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    }
  },
  {
    Id = "121070",
    GroupId = 16,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1semi_11",
      Count = 1
    }
  },
  {
    Id = "121080",
    GroupId = 16,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e6deli_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121090",
    GroupId = 16,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "121100",
    GroupId = 16,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "121110",
    GroupId = 16,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121120",
    GroupId = 16,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121130",
    GroupId = 17,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_14",
      Count = 1
    }
  },
  {
    Id = "121140",
    GroupId = 17,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e5bakl_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121150",
    GroupId = 17,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e4sf_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "121160",
    GroupId = 17,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "121170",
    GroupId = 17,
    ChapterId = 12,
    Requirement_1 = {Type = "it_a12_2_5", Count = 1},
    Requirement_2 = {Type = "it_2_2_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121180",
    GroupId = 17,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121190",
    GroupId = 17,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_fd_11", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6porr_2",
      Count = 1
    }
  },
  {
    Id = "121200",
    GroupId = 18,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e5dst_20",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_7", Count = 1}
  },
  {
    Id = "121210",
    GroupId = 18,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e6nibble_8",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_4", Count = 1}
  },
  {
    Id = "121220",
    GroupId = 18,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121230",
    GroupId = 18,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_11e4tato_17",
      Count = 1
    }
  },
  {
    Id = "121240",
    GroupId = 18,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "121250",
    GroupId = 18,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121260",
    GroupId = 18,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e5bakl_12",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121270",
    GroupId = 19,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e6rice_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_25",
      Count = 1
    }
  },
  {
    Id = "121280",
    GroupId = 19,
    ChapterId = 12,
    Requirement_1 = {Type = "it_4_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_3",
      Count = 1
    }
  },
  {
    Id = "121290",
    GroupId = 19,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5dst_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121300",
    GroupId = 19,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "121310",
    GroupId = 19,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121320",
    GroupId = 19,
    ChapterId = 12,
    Requirement_1 = {Type = "it_a12_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    }
  },
  {
    Id = "121330",
    GroupId = 19,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_9e6nibble_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121340",
    GroupId = 20,
    ChapterId = 12,
    Requirement_1 = {Type = "it_4_2_6", Count = 1},
    Requirement_2 = {Type = "it_a12_2_5", Count = 1}
  },
  {
    Id = "121350",
    GroupId = 20,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e1hotdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121360",
    GroupId = 20,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "121370",
    GroupId = 20,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_4",
      Count = 1
    }
  },
  {
    Id = "121380",
    GroupId = 20,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121390",
    GroupId = 20,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "121400",
    GroupId = 20,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121410",
    GroupId = 21,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121420",
    GroupId = 21,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e5bakl_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121430",
    GroupId = 21,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_15",
      Count = 1
    }
  },
  {
    Id = "121440",
    GroupId = 21,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_5",
      Count = 1
    }
  },
  {
    Id = "121450",
    GroupId = 21,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121460",
    GroupId = 21,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "121470",
    GroupId = 21,
    ChapterId = 12,
    Requirement_1 = {Type = "it_a12_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_11e6porr_1",
      Count = 1
    }
  },
  {
    Id = "121480",
    GroupId = 22,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e6deli_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121490",
    GroupId = 22,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6dst_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121500",
    GroupId = 22,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6nibble_6",
      Count = 1
    }
  },
  {
    Id = "121510",
    GroupId = 22,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_24",
      Count = 1
    }
  },
  {
    Id = "121520",
    GroupId = 22,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121530",
    GroupId = 22,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "121540",
    GroupId = 22,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_7e5mt_6", Count = 1}
  },
  {
    Id = "121550",
    GroupId = 23,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    }
  },
  {
    Id = "121560",
    GroupId = 23,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e6deli_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121570",
    GroupId = 23,
    ChapterId = 12,
    Requirement_1 = {Type = "it_a12_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_8",
      Count = 1
    }
  },
  {
    Id = "121580",
    GroupId = 23,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "121590",
    GroupId = 23,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121600",
    GroupId = 23,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "121610",
    GroupId = 23,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e6cockt_7",
      Count = 1
    }
  },
  {
    Id = "121620",
    GroupId = 24,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "121630",
    GroupId = 24,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121640",
    GroupId = 24,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e1icytre_13",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121650",
    GroupId = 24,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_10",
      Count = 1
    }
  },
  {
    Id = "121660",
    GroupId = 24,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_11e4dst_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121670",
    GroupId = 24,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    }
  },
  {
    Id = "121680",
    GroupId = 24,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_7e5mt_6", Count = 1}
  },
  {
    Id = "121690",
    GroupId = 25,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "121700",
    GroupId = 25,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121710",
    GroupId = 25,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_11e5fd_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_9",
      Count = 1
    }
  },
  {
    Id = "121720",
    GroupId = 25,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e5bakl_5",
      Count = 1
    }
  },
  {
    Id = "121730",
    GroupId = 25,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121740",
    GroupId = 25,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "121750",
    GroupId = 25,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121760",
    GroupId = 26,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e1nutt_1",
      Count = 1
    }
  },
  {
    Id = "121770",
    GroupId = 26,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121780",
    GroupId = 26,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121790",
    GroupId = 26,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_5",
      Count = 1
    }
  },
  {
    Id = "121800",
    GroupId = 26,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121810",
    GroupId = 26,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "121820",
    GroupId = 26,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6dst_16",
      Count = 1
    }
  },
  {
    Id = "121830",
    GroupId = 27,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    }
  },
  {
    Id = "121840",
    GroupId = 27,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6deli_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121850",
    GroupId = 27,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6deli_12",
      Count = 1
    }
  },
  {
    Id = "121860",
    GroupId = 27,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_6e4assort_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_6",
      Count = 1
    }
  },
  {
    Id = "121870",
    GroupId = 27,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_friedve_4",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121880",
    GroupId = 27,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121890",
    GroupId = 27,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "121900",
    GroupId = 28,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6assort_5",
      Count = 1
    }
  },
  {
    Id = "121910",
    GroupId = 28,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e5bakl_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121920",
    GroupId = 28,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "121930",
    GroupId = 28,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "121940",
    GroupId = 28,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_10e4sf_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1nutt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121950",
    GroupId = 28,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_12e6deli_11",
      Count = 1
    }
  },
  {
    Id = "121960",
    GroupId = 28,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "121970",
    GroupId = 29,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "121980",
    GroupId = 29,
    ChapterId = 12,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "it_a12_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "121990",
    GroupId = 29,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e2mt_15",
      Count = 1
    }
  },
  {
    Id = "122000",
    GroupId = 29,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5dst_13",
      Count = 1
    }
  },
  {
    Id = "122010",
    GroupId = 29,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "122020",
    GroupId = 29,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_12e5bakl_5",
      Count = 1
    }
  },
  {
    Id = "122030",
    GroupId = 29,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "122040",
    GroupId = 30,
    ChapterId = 12,
    Requirement_1 = {Type = "it_a12_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfru_1",
      Count = 1
    }
  },
  {
    Id = "122050",
    GroupId = 30,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e5bakl_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "122060",
    GroupId = 30,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "122070",
    GroupId = 30,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6dst_15",
      Count = 1
    }
  },
  {
    Id = "122080",
    GroupId = 30,
    ChapterId = 12,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {Type = "ds_sal_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "122090",
    GroupId = 30,
    ChapterId = 12,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6deli_1",
      Count = 1
    }
  },
  {
    Id = "122100",
    GroupId = 30,
    ChapterId = 12,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  }
}
