BakeOutRewardNode = {}
BakeOutRewardNode.__index = BakeOutRewardNode

function BakeOutRewardNode:Awake()
  RewardApi.AddFilterRewardEventListener(self, self._UpdateRewards)
end

function BakeOutRewardNode:UpdateContent(rewards, rank, window)
  local active = self.gameObject.activeSelf
  self.m_rewards = rewards
  self.m_rank = rank
  self.m_window = window
  if rewards == nil or #rewards == 0 then
    if active then
      self.gameObject:SetActive(false)
    end
    return
  end
  if not active then
    self.gameObject:SetActive(true)
  end
  local bShowBox = 1 < #rewards or rank <= 3
  self.m_boxGo:SetActive(bShowBox)
  self.m_rewardItem.gameObject:SetActive(not bShowBox)
  if 1 <= rank and rank <= 3 then
    self.m_boxImg.sprite = self["m_boxSprite" .. rank]
  end
  self:_UpdateRewards()
end

function BakeOutRewardNode:_UpdateRewards()
  local rewards = self.m_rewards
  local rank = self.m_rank
  if rewards == nil or rank == nil then
    return
  end
  local bShowBox = 1 < #rewards or rank <= 3
  if not bShowBox then
    local arrRewards = RewardApi.FilterRewards(rewards)
    if not Table.IsEmpty(arrRewards) then
      self.m_rewardItem.gameObject:SetActive(true)
      self.m_rewardItem:Init(arrRewards[1])
    else
      self.m_rewardItem.gameObject:SetActive(false)
    end
  end
end

function BakeOutRewardNode:OnBoxClick()
  self.m_window:ShowRewardTip(self.m_rewards, self.m_boxGo.transform, -4, 42.5)
end
