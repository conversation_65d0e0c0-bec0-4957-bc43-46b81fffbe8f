CoinHudButton = setmetatable({}, HudPropertyButton)
CoinHudButton.__index = CoinHudButton

function CoinHudButton:Awake()
  HudPropertyButton.Awake(self)
  HudPropertyButton.Init(self, EPropertyType.Gold)
  self:_OnShowItemTestInfoChanged()
  EventDispatcher.AddListener(EEventType.ShowItemTestInfoChanged, self, self._OnShowItemTestInfoChanged)
end

function CoinHudButton:OnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.GoPlayWindow)
end

function CoinHudButton:_OnShowItemTestInfoChanged()
  if not GM.UIManager:CanShowTestUI() then
    self.m_testText.gameObject:SetActive(false)
    return
  end
  self.m_testText.gameObject:SetActive(true)
  local cleanGold = GM.TaskManager:GetCleanGoldCount()
  local normalGold = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gold) - cleanGold
  self.m_testText.text = "N:" .. normalGold .. "  C:" .. cleanGold
end

function CoinHudButton:OnPropertyAcquired(...)
  HudPropertyButton.OnPropertyAcquired(self, ...)
  self:_OnShowItemTestInfoChanged()
end

function CoinHudButton:OnPropertyConsumed(...)
  HudPropertyButton.OnPropertyConsumed(self, ...)
  self:_OnShowItemTestInfoChanged()
end
