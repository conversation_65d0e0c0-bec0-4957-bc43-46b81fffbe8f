SurpriseChestActivityPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
SurpriseChestActivityPopupHelper.__index = SurpriseChestActivityPopupHelper

function SurpriseChestActivityPopupHelper.Create()
  local helper = setmetatable({}, SurpriseChestActivityPopupHelper)
  helper:Init()
  return helper
end

function SurpriseChestActivityPopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(SurpriseChestActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnStateChanged)
  EventDispatcher.AddListener(EEventType.OrderStateChanged, self, self._OnStateChanged)
end

function SurpriseChestActivityPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function SurpriseChestActivityPopupHelper:CheckPopup()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  if not GM.MainBoardModel:GetOrderModel():HasOrders() then
    return
  end
  local model, state
  for activityType, activityDefinition in pairs(SurpriseChestActivityDefinition) do
    model = GM.ActivityManager:GetModel(activityType)
    state = model:GetState()
    if state == ActivityState.Started and not model:HasTriggeredSurpriseChest() and model:CanTriggerSurpriseChest() then
      return activityDefinition.MainWindowPrefabName, {activityType}
    end
  end
end
