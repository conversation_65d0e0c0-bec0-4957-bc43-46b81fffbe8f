NetworkErrorWindow = setmetatable({canCloseByAndroidBack = false, canCloseByChangeGameMode = false}, BaseWindow)
NetworkErrorWindow.__index = NetworkErrorWindow

function NetworkErrorWindow:Init(retryCallback)
  self.m_retryCallback = retryCallback
  self:SetCloseBtnActive(false)
end

function NetworkErrorWindow:Exit()
  PlatformInterface.ExitGame()
end

function NetworkErrorWindow:Retry()
  self:Close()
  if self.m_retryCallback then
    self.m_retryCallback()
    self.m_retryCallback = nil
  end
end
