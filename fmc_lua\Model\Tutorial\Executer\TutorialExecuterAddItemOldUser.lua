local itemId = "additem_1"
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self.CheckStart)
  EventDispatcher.AddListener(EEventType.ItemRetrieved, self, self.CheckStart)
  EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self.OnChangeGameModeFinished)
end

function Executer:_CanStart()
  return GM.MainBoardModel:GetBoardItemCountByType(itemId) >= 2
end

function Executer:CheckStart()
  if self:_CanStart() then
    self.AddToAutoPopup = true
    GM.TutorialModel:AddAutoPopup(self)
  end
end

function Executer:TryStartTutorial()
  if self:_CanStart() then
    GM.UIManager:OpenViewWhenIdle(UIPrefabConfigName.AddItemChangeInfoWindow)
    self:Finish()
    return true
  end
end

function Executer:OnChangeGameModeFinished()
  if GM.SceneManager:GetGameMode() == EGameMode.Board then
    self:CheckStart()
  end
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
