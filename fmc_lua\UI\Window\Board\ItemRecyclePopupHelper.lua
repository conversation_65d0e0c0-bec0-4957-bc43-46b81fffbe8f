ItemRecyclePopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
ItemRecyclePopupHelper.__index = ItemRecyclePopupHelper
local FLAG_TRUE = 1
local FLAG_FALSE = 0

function ItemRecyclePopupHelper.Create()
  local helper = setmetatable({}, ItemRecyclePopupHelper)
  helper:Init()
  return helper
end

function ItemRecyclePopupHelper:Init()
  BasePopupHelper.Init(self)
  EventDispatcher.AddListener(EEventType.OrderGroupRefreshed, self, self._OnOrderGroupRefreshed)
end

function ItemRecyclePopupHelper:_OnOrderGroupRefreshed()
  GM.MiscModel:SetCheckItemRecycle(FLAG_TRUE)
  self:SetNeedCheckPopup(true)
end

function ItemRecyclePopupHelper:CheckPopup()
  if GM.MiscModel:GetCheckItemRecycleInNumber() == FLAG_TRUE then
    GM.MiscModel:SetCheckItemRecycle(FLAG_FALSE)
    if GM.ItemRecycleModel:ShouldPopRecycleWindow(GM.MainBoardModel:GetCurOrderDay()) then
      return UIPrefabConfigName.ItemRecycleWindow
    end
  end
end
