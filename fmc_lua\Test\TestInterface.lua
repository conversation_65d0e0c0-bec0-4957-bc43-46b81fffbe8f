local Serialization = require("Model.Network.Serialization")()
Serialization.ProtocolMd5 = {
  1,
  142,
  123,
  1,
  212,
  197,
  128,
  108,
  192,
  170,
  219,
  161,
  14,
  51,
  178,
  32
}
Serialization.TestClearUserDataReq = {}
Serialization.TestClearUserDataReq.__index = Serialization.TestClearUserDataReq

function Serialization.TestClearUserDataReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  return true
end

function Serialization.TestClearUserDataReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.TestClearUserDataReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.TestClearUserDataResp = {}
Serialization.TestClearUserDataResp.__index = Serialization.TestClearUserDataResp

function Serialization.TestClearUserDataResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  return true
end

function Serialization.TestClearUserDataResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.TestClearUserDataResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.TestGetBakDataReq = {}
Serialization.TestGetBakDataReq.__index = Serialization.TestGetBakDataReq

function Serialization.TestGetBakDataReq.Serialize(writer, value)
  local bRet
  assert(value.userid ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userid)
  if not bRet then
    return false
  end
  return true
end

function Serialization.TestGetBakDataReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.TestGetBakDataReq)
  bRet, value.userid = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.TestGetBakDataResp = {}
Serialization.TestGetBakDataResp.__index = Serialization.TestGetBakDataResp

function Serialization.TestGetBakDataResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.desc ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.desc)
  if not bRet then
    return false
  end
  return true
end

function Serialization.TestGetBakDataResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.TestGetBakDataResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.desc = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.TestUseBakDataReq = {}
Serialization.TestUseBakDataReq.__index = Serialization.TestUseBakDataReq

function Serialization.TestUseBakDataReq.Serialize(writer, value)
  local bRet
  assert(value.userid ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userid)
  if not bRet then
    return false
  end
  assert(value.desc ~= nil)
  bRet = Serialization.String.Serialize(writer, value.desc)
  if not bRet then
    return false
  end
  return true
end

function Serialization.TestUseBakDataReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.TestUseBakDataReq)
  bRet, value.userid = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.desc = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.TestUseBakDataResp = {}
Serialization.TestUseBakDataResp.__index = Serialization.TestUseBakDataResp

function Serialization.TestUseBakDataResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  return true
end

function Serialization.TestUseBakDataResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.TestUseBakDataResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.TestUpdBakDataReq = {}
Serialization.TestUpdBakDataReq.__index = Serialization.TestUpdBakDataReq

function Serialization.TestUpdBakDataReq.Serialize(writer, value)
  local bRet
  assert(value.userid ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userid)
  if not bRet then
    return false
  end
  assert(value.desc ~= nil)
  bRet = Serialization.String.Serialize(writer, value.desc)
  if not bRet then
    return false
  end
  return true
end

function Serialization.TestUpdBakDataReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.TestUpdBakDataReq)
  bRet, value.userid = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.desc = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.TestUpdBakDataResp = {}
Serialization.TestUpdBakDataResp.__index = Serialization.TestUpdBakDataResp

function Serialization.TestUpdBakDataResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  return true
end

function Serialization.TestUpdBakDataResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.TestUpdBakDataResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.TestDelBakDataReq = {}
Serialization.TestDelBakDataReq.__index = Serialization.TestDelBakDataReq

function Serialization.TestDelBakDataReq.Serialize(writer, value)
  local bRet
  assert(value.desc ~= nil)
  bRet = Serialization.String.Serialize(writer, value.desc)
  if not bRet then
    return false
  end
  return true
end

function Serialization.TestDelBakDataReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.TestDelBakDataReq)
  bRet, value.desc = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.TestDelBakDataResp = {}
Serialization.TestDelBakDataResp.__index = Serialization.TestDelBakDataResp

function Serialization.TestDelBakDataResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  return true
end

function Serialization.TestDelBakDataResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.TestDelBakDataResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.reqNameMap = {
  BLTestClearUserData = Serialization.TestClearUserDataReq,
  BLTestGetBakData = Serialization.TestGetBakDataReq,
  BLTestUseBakData = Serialization.TestUseBakDataReq,
  BLTestUpdBakData = Serialization.TestUpdBakDataReq,
  BLTestDelBakData = Serialization.TestDelBakDataReq
}
Serialization.respNameMap = {
  BLTestClearUserData = Serialization.TestClearUserDataResp,
  BLTestGetBakData = Serialization.TestGetBakDataResp,
  BLTestUseBakData = Serialization.TestUseBakDataResp,
  BLTestUpdBakData = Serialization.TestUpdBakDataResp,
  BLTestDelBakData = Serialization.TestDelBakDataResp
}
return Serialization
