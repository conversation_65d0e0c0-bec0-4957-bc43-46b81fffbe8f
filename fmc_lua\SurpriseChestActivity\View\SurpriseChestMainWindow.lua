SurpriseChestMainWindow = setmetatable({
  canCloseByAndroidBack = false,
  windowMaskAlpha = EWindowMaskAlpha.Default
}, SurpriseChestBaseWindow)
SurpriseChestMainWindow.__index = SurpriseChestMainWindow

function SurpriseChestMainWindow:Init(activityType, bUser<PERSON>lick)
  SurpriseChestBaseWindow.Init(self, activityType, bUserClick)
  self.m_bUserClick = bUserClick
  if self.m_model ~= nil then
    self.m_timeText.text = self.m_model:GetCountDownText()
  end
  self.m_spine:Init()
  self:PlayEnterAnim()
end

function SurpriseChestMainWindow:PlayEnterAnim()
  self.m_spine:SetAnimation("appear", false)
  self.m_spine:AddAnimation("idle", true)
  self.m_canvasGroup.alpha = 0
  DelayExecuteFuncInView(function()
    self.m_canvasGroup:DOFade(1, 0.5)
  end, 1, self, true)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
  end, 1.5)
end

function SurpriseChestMainWindow:OnBtnClicked()
  if self.m_model:CanTriggerSurpriseChest() then
    self.m_model:TriggerSurpriseChest()
    UIUtil.SetActive(self.m_btnGo, false)
    self.m_spine:SetAnimation("open", false)
    TutorialHelper.ScrollToMainOrderRoot(false)
    GM.UIManager:SetEventLock(true)
    self.m_eventLocked = true
    local seq = DOTween.Sequence()
    seq:Append(self.m_canvasGroup:DOFade(0, 0.2))
    seq:AppendCallback(function()
      self:PlayEggFlyAnim()
    end)
    self.m_seq = seq
  else
    GM.UIManager:ShowPromptWithKey("event_general_end")
    self:Close()
  end
  self:LogWindowAction(EBIType.UIActionType.Click, "use")
end

function SurpriseChestMainWindow:PlayEggFlyAnim()
  local boardView = MainBoardView.GetInstance()
  local orderArea = boardView and boardView:GetOrderArea()
  local cam = GM.ModeViewController:GetBoardInfo()
  local flyTime = 0.5
  local flyAnimFunc = function()
    if self.m_model ~= nil and orderArea ~= nil then
      local mapOrderId2ChestData = self.m_model:GetAllChestData()
      local orderCell, screenPos
      for orderId, _ in pairs(mapOrderId2ChestData) do
        orderCell = orderArea:GetCellById(orderId)
        local surpriseChest = orderCell and orderCell:GetSurpriseChest()
        if surpriseChest ~= nil then
          screenPos = cam:WorldToScreenPoint(surpriseChest.transform.position)
          do
            local flyItemGo = GameObject.Instantiate(self.m_flyItemGo, self.m_flyItemGo.transform.parent)
            if flyItemGo ~= nil then
              UIUtil.SetActive(flyItemGo, true)
              flyItemGo.transform:DOMove(PositionUtil.UICameraScreen2World(screenPos), flyTime):SetEase(Ease.OutQuad):OnComplete(function()
                GameObject.Destroy(flyItemGo)
                if surpriseChest ~= nil and surpriseChest.gameObject ~= nil and not surpriseChest.gameObject:IsNull() then
                  UIUtil.SetActive(surpriseChest.gameObject, true)
                end
              end)
            end
          end
        end
      end
    end
  end
  local seq = DOTween.Sequence()
  seq:AppendInterval(0.3)
  seq:AppendCallback(function()
    flyAnimFunc()
  end)
  seq:AppendInterval(flyTime)
  seq:AppendCallback(function()
    GM.UIManager:SetEventLock(false)
    self.m_eventLocked = false
    self:Close()
  end)
  self.m_eggSeq = seq
end

function SurpriseChestMainWindow:OnDestroy()
  BaseWindow.OnDestroy(self)
  if self.m_seq ~= nil then
    self.m_seq:Kill()
    self.m_seq = nil
  end
  if self.m_eggSeq ~= nil then
    self.m_eggSeq:Kill()
    self.m_eggSeq = nil
  end
  if self.m_eventLocked then
    GM.UIManager:SetEventLock(false)
    self.m_eventLocked = false
  end
end
