BreakEggQuitWindow = setmetatable({}, BreakEggBaseWindow)
BreakEggQuitWindow.__index = BreakEggQuitWindow

function BreakEggQuitWindow:Init()
  BreakEggBaseWindow.Init(self)
  local maxStep = self.m_activityModel:GetStepTotal()
  local curStep = self.m_activityModel:GetCurrentStep()
  local deltaStep = math.max(maxStep - curStep + 1, 0)
  self.m_descText.text = GM.GameTextModel:GetText("break_egg_sub_leave", deltaStep)
  local completeReward = self.m_activityModel:GetCompleteReward()
  local img = self.m_activityModel:GetHighImg(completeReward[PROPERTY_TYPE]) or ConfigUtil.GetCurrencyImageName(completeReward)
  self.m_iconImg.gameObject.transform.localScale = Vector3.zero
  self.m_buttonRectTrans.localScale = Vector3.zero
  SpriteUtil.SetImage(self.m_iconImg, img, false, function()
    self.m_iconImg.gameObject:SetActive(true)
    DOVirtual.DelayedCall(0.4, function()
      self.m_iconImg.gameObject.transform:DOScale(1.0, 0.5):SetEase(Ease.OutBack)
      DOVirtual.DelayedCall(0.4, function()
        self.m_buttonRectTrans:DOScale(1.0, 0.5):SetEase(Ease.OutBack)
      end)
    end)
  end)
  self.m_numText.text = "X" .. completeReward[PROPERTY_COUNT]
end

function BreakEggQuitWindow:_OnLeaveClicked()
  local collectReward = self.m_activityModel:GetCollectRewards()
  RewardApi.AcquireRewardsLogic(collectReward, EPropertySource.Give, EBIType.BreakEggReward, EGameMode.Board, CacheItemType.Stack)
  GM.UIManager:OpenView(UIPrefabConfigName.BreakEggRewardWIndow, collectReward, "break_egg_reward_title", false, nil, function()
    local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BreakEggMainWindow)
    if mainWindow then
      mainWindow:DisPlayStart()
    end
  end)
  self.m_activityModel:LogReward(collectReward, "0")
  self.m_activityModel:ResetData()
  self:Close()
end

function BreakEggQuitWindow:_OnReturnClicked()
  self:Close()
end
