BoardCacheRoot = {}
BoardCacheRoot.__index = BoardCacheRoot

function BoardCacheRoot:Awake()
  local breathAnimation = DOTween.Sequence():SetLoops(-1, LoopType.Yoyo)
  breathAnimation:Append(self.m_bgGo.transform:DOScale(0.95 * self.m_bgGo.transform.localScale.x, 1):SetEase(Ease.InOutSine))
  breathAnimation:AppendInterval(0.1)
  self.m_breathAnimation = breathAnimation
end

function BoardCacheRoot:OnEnable()
  self:_RebuildLayout()
end

function BoardCacheRoot:Init(boardView)
  self.m_boardView = boardView
  self.m_gameMode = self.m_boardView:GetModel():GetGameMode()
  self.m_clickable = true
  self.m_originScale = 1
  self:OnStateChanged()
  self.m_arrowOriginY = self.m_arrowRectTrans.localPosition.y
  self.m_visible = false
  self.gameObject:SetActive(true)
  self.transform:SetLocalScale(0)
  local hide = DOTween.Sequence()
  hide:AppendInterval(0.4)
  hide:AppendCallback(function()
    self.m_bPlayingAnimation = true
  end)
  hide:Append(self.gameObject.transform:DOScale(0, 0.2))
  hide:Join(self.m_arrowRectTrans:DOScale(0, 0.1))
  hide:AppendCallback(function()
    self.m_bPlayingAnimation = false
  end)
  hide:SetAutoKill(false)
  hide:Pause()
  self.m_hideTween = hide
  local popIcon = DOTween.Sequence()
  popIcon:AppendInterval(0.4)
  popIcon:Append(self.m_iconImg.transform:DOScale(1.1, 0.2))
  popIcon:Append(self.m_iconImg.transform:DOScale(1, 0.1))
  popIcon:SetAutoKill(false)
  popIcon:Pause()
  self.m_popIconTween = popIcon
  EventDispatcher.DispatchEvent(EEventType.CacheRootNodeStateChanged)
  self:UpdateContent()
  self:_AddListeners()
  self:_ClearTopLogicRecord()
end

function BoardCacheRoot:_AddListeners()
  EventDispatcher.AddListener(EEventType.CacheItems, self, self.OnCacheItems)
  EventDispatcher.AddListener(EEventType.CacheActivityItems, self, self.OnCacheActivityItems)
  EventDispatcher.AddListener(EEventType.ViewCacheItems, self, self.OnViewCacheItems)
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self.OnPopCachedItem)
  EventDispatcher.AddListener(EEventType.ChangeCachedItems, self, self.OnCachedItemsChanged)
  EventDispatcher.AddListener(EEventType.Exclamation, self, self.PlayAnimation)
  EventDispatcher.AddListener(EEventType.TaskBubbleStateChanged, self, self.OnStateChanged)
  EventDispatcher.AddListener(EEventType.CloseView, self, self.TryScrollToCacheRoot)
  EventDispatcher.AddListener(EEventType.FlyingElementClear, self, self.TryScrollToCacheRoot)
end

function BoardCacheRoot:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  if self.m_showTween then
    self.m_showTween:Kill()
    self.m_showTween = nil
  end
  if self.m_hideTween then
    self.m_hideTween:Kill()
    self.m_hideTween = nil
  end
  if self.m_popIconTween then
    self.m_popIconTween:Kill()
    self.m_popIconTween = nil
  end
  if self.m_cacheIconTween then
    self.m_cacheIconTween:Kill()
    self.m_cacheIconTween = nil
  end
  if self.m_breathAnimation ~= nil then
    self.m_breathAnimation:Kill()
    self.m_breathAnimation = nil
  end
  if self.m_arrowSeq ~= nil then
    self.m_arrowSeq:Kill()
    self.m_arrowSeq = nil
  end
end

function BoardCacheRoot:OnStateChanged(bTmp)
  if not GM.ConfigModel:UseNewCacheLayout() then
    return
  end
  local bExistTaskBubble = BoardTaskBubble.CanShow()
  if not bTmp then
    self.m_originScale = bExistTaskBubble and self:GetCacheElementScale() or 1
    if self.m_stateSeq ~= nil then
      self.m_stateSeq:Kill()
      self.m_stateSeq = nil
    end
  end
  local targetHeight = bExistTaskBubble and 140 or 210
  local targetPosY = bExistTaskBubble and 68 or 98
  if self.m_visible and not bTmp and self.m_showTween == nil then
    local seq = DOTween.Sequence()
    local animTime = 0.2
    seq:Append(self.transform:DOSizeDelta(Vector2(self.transform.sizeDelta.x, targetHeight), animTime):SetEase(Ease.OutCubic))
    seq:Join(self.m_rootTrans:DOAnchorPosY(targetPosY, animTime):SetEase(Ease.OutCubic))
    seq:Join(self.transform:DOScale(Vector3(self.m_originScale, self.m_originScale, 1), animTime):SetEase(Ease.OutCubic))
    seq:AppendCallback(function()
      self.m_stateSeq = nil
      self:_RebuildLayout()
    end)
    self.m_stateSeq = seq
  else
    UIUtil.SetSizeDelta(self.transform, nil, targetHeight)
    UIUtil.SetAnchoredPosition(self.m_rootTrans, nil, targetPosY)
  end
end

function BoardCacheRoot:OnCacheItems(msg)
  if msg ~= nil and msg.eGameMode ~= self.m_gameMode then
    return
  end
  local hasAnimation = msg and msg.hasAnimation or false
  if not hasAnimation or GM.SceneManager:GetGameMode() ~= EGameMode.Board or GM.UIManager:IsViewOpen(UIPrefabConfigName.ShopWindow) then
    self:UpdateContent()
  elseif hasAnimation then
    self:_AddTopLogicRecord()
  end
  if GM.ConfigModel:UseNewCacheLayout() and (GM.SceneManager:GetGameMode() ~= EGameMode.Board or not GM.UIManager.allWindowClosed) then
    self.m_bScrollToCacheRoot = true
  end
end

function BoardCacheRoot:OnCacheActivityItems(msg)
  self:OnCacheItems(msg)
end

function BoardCacheRoot:_AddTopLogicRecord()
  self:UpdateActivityCacheCount()
  self.m_arrRecordTopLogicCount[#self.m_arrRecordTopLogicCount + 1] = self:GetCachedItemCount()
  if not Table.IsEmpty(self.m_activityProperty) then
    self.m_arrRecordTopLogicType[#self.m_arrRecordTopLogicType + 1] = self.m_activityProperty[1]
  else
    self.m_arrRecordTopLogicType[#self.m_arrRecordTopLogicType + 1] = self.m_boardView:GetModel():GetCachedItem(1)
  end
end

function BoardCacheRoot:_RetriveTopLogicRecord()
  local recordTopLogicCount = self.m_arrRecordTopLogicCount[1]
  local recordTopLogicType = self.m_arrRecordTopLogicType[1]
  table.remove(self.m_arrRecordTopLogicCount, 1)
  table.remove(self.m_arrRecordTopLogicType, 1)
  return recordTopLogicCount, recordTopLogicType
end

function BoardCacheRoot:_ClearTopLogicRecord()
  self.m_arrRecordTopLogicType = {}
  self.m_arrRecordTopLogicCount = {}
end

function BoardCacheRoot:TryScrollToCacheRoot()
  if not GM.ConfigModel:UseNewOrderRewardAnimation() then
    self:_ClearTopLogicRecord()
  end
  if self:CanScrollToCacheRoot() then
    self:ResetScrollToCacheRoot()
    TutorialHelper.ScrollToBoardCacheRoot(true)
    return true
  end
  return false
end

function BoardCacheRoot:ResetScrollToCacheRoot()
  self.m_bScrollToCacheRoot = false
end

function BoardCacheRoot:CanScrollToCacheRoot(ignoreFly)
  if ignoreFly ~= true and PropertyAnimationManager.uiLockFlyingCount > 0 then
    return false
  end
  return GM.SceneManager:GetGameMode() == EGameMode.Board and GM.UIManager.allWindowClosed and self.m_bScrollToCacheRoot
end

function BoardCacheRoot:OnViewCacheItems(delay)
  delay = (delay or 0) + 0.9
  local recordTopLogicCount, recordTopLogicType = self:_RetriveTopLogicRecord()
  if self.m_displayItemType == nil then
    self:UpdateDisplayItem(recordTopLogicCount, recordTopLogicType)
    if self.m_displayItemType ~= nil then
      self:ChangeVisible(true, false, delay)
      EventDispatcher.DispatchEvent(EEventType.RefreshPrompt)
    end
  else
    if self.m_cacheIconTween then
      self.m_cacheIconTween:Kill()
      self.m_cacheIconTween = nil
    end
    self.m_popIconTween:Pause()
    local tween = DOTween.Sequence()
    tween:AppendInterval(delay - 0.4)
    tween:Append(self.m_iconImg.transform:DOScale(1.1, 0.1))
    tween:Append(self.m_iconImg.transform:DOScale(0, 0.2))
    tween:AppendInterval(0.1)
    tween:AppendCallback(function()
      self:UpdateDisplayItem(recordTopLogicCount, recordTopLogicType)
      self.m_iconImg.transform.localScale = V3One
    end)
    tween:Append(self.m_iconImg.transform:DOScale(0.8, 0.1))
    tween:Join(self.m_rootTrans:DOScale(0.9, 0.1))
    tween:Append(self.m_iconImg.transform:DOScale(1, 0.15))
    tween:Join(self.m_rootTrans:DOScale(1, 0.15))
    tween:AppendCallback(function()
      self.m_cacheIconTween = nil
    end)
    self.m_cacheIconTween = tween
  end
end

function BoardCacheRoot:OnPopCachedItem(msg)
  if msg ~= nil and msg.GameMode == self.m_gameMode then
    self:OnCachedItemsChanged()
  end
end

function BoardCacheRoot:OnCachedItemsChanged()
  self:UpdateContent()
end

function BoardCacheRoot:UpdateActivityCacheCount()
  self.m_activityProperty = {}
  if self.m_gameMode ~= EGameMode.Board then
    return
  end
  local albumModel = AlbumActivityModel.GetActiveModel()
  if albumModel ~= nil then
    local propertyList = albumModel:GetItemCachePropertys()
    for _, property in ipairs(propertyList) do
      table.insert(self.m_activityProperty, property)
    end
  end
end

function BoardCacheRoot:UseActivityProperty()
  local albumModel = AlbumActivityModel.GetActiveModel()
  if albumModel ~= nil then
    local packId = self.m_activityProperty[1]
    table.remove(self.m_activityProperty, 1)
    albumModel:ConsumeOneCardPack(packId, self.m_iconImg.transform.position)
  end
end

function BoardCacheRoot:UpdateContent()
  self:UpdateActivityCacheCount()
  local cachedItemsCount = self:GetCachedItemCount()
  if 0 < cachedItemsCount then
    if self.m_displayItemType == nil then
      self:ChangeVisible(true, true)
    end
    self:UpdateDisplayItem()
  elseif self.m_displayItemType ~= nil then
    self.m_displayItemType = nil
    self:ChangeVisible(false, false)
  end
  self:_ClearTopLogicRecord()
end

function BoardCacheRoot:UpdateDisplayItem(forceDisplayCount, forceDisplayType)
  self:UpdateActivityCacheCount()
  local cachedItemsCount = forceDisplayCount or self:GetCachedItemCount()
  if cachedItemsCount <= 0 then
    return
  end
  local frontCachedItem
  if forceDisplayType ~= nil then
    frontCachedItem = forceDisplayType
  elseif 0 < #self.m_activityProperty then
    frontCachedItem = self.m_activityProperty[1]
  else
    frontCachedItem = self.m_boardView:GetModel():GetCachedItem(1)
  end
  Log.Assert(frontCachedItem ~= nil, "frontCachedItem不能为空")
  local displayItemType = ItemModelFactory.GetInnerType(frontCachedItem)
  if self.m_displayItemType ~= displayItemType then
    self.m_displayItemType = displayItemType
    SpriteUtil.SetImage(self.m_iconImg, GM.ItemDataModel:GetSpriteName(displayItemType), true)
  end
  self.m_countText.text = cachedItemsCount
  self.m_countBg:SetActive(1 < cachedItemsCount)
end

function BoardCacheRoot:GetDisplayItem()
  return self.m_displayItemType
end

function BoardCacheRoot:ChangeVisible(visible, noAnim, delay)
  if self.m_visible == visible then
    return
  end
  self.m_visible = visible
  EventDispatcher.DispatchEvent(EEventType.CacheBubbleStateChanged)
  EventDispatcher.DispatchEvent(EEventType.CacheRootNodeStateChanged)
  if self.m_showTween then
    self.m_showTween:Kill()
    self.m_showTween = nil
  end
  self.m_hideTween:Pause()
  if noAnim then
    if visible then
      self:_InitAnimNode(true)
    else
      Log.Assert("非棋盘，无法释放棋子！")
    end
    return
  end
  if visible then
    local show = DOTween.Sequence()
    show:AppendInterval(delay - 0.5)
    show:AppendCallback(function()
      self:_InitAnimNode(false)
      self.m_bPlayingAnimation = true
    end)
    show:Append(self.gameObject.transform:DOScale(self.m_originScale, 0.2))
    show:AppendInterval(0.3)
    show:AppendCallback(function()
      self.m_iconImg.transform.localScale = V3One
      self.m_bPlayingAnimation = false
      Object.Instantiate(self.m_effectOpenGo, self.m_effectOpenTrans)
    end)
    show:Append(self.m_iconImg.transform:DOScale(0.7, 0.1))
    show:Append(self.m_iconImg.transform:DOScale(1, 0.1))
    show:Join(self.m_arrowRectTrans:DOScale(1, 0.1))
    show:AppendCallback(function()
      self.m_effectTrans.localScale = V3One
      self.m_showTween = nil
      UIUtil.SetLocalScale(self.gameObject.transform, self.m_originScale, self.m_originScale)
    end)
    self.m_showTween = show
  else
    self.m_popIconTween:Pause()
    if self.m_cacheIconTween then
      self.m_cacheIconTween:Kill()
      self.m_cacheIconTween = nil
    end
    self.m_iconImg.transform.localScale = V3Zero
    self.m_hideTween:Restart()
  end
end

function BoardCacheRoot:_InitAnimNode(visible)
  self.m_iconImg.transform.localScale = visible and V3One or V3Zero
  self.gameObject.transform.localScale = visible and Vector3(self.m_originScale, self.m_originScale, 1) or V3Zero
  self.m_arrowRectTrans.localScale = visible and V3One or V3Zero
  self.m_effectTrans.localScale = visible and V3One or V3Zero
  self:_RebuildLayout()
end

function BoardCacheRoot:OnClicked()
  if #self.m_activityProperty > 0 then
    self:UseActivityProperty()
    return
  end
  local cachedItemsCount = self.m_boardView:GetModel():GetCachedItemCount()
  if cachedItemsCount <= 0 or not self.m_clickable then
    return
  end
  EventDispatcher.DispatchEvent(EEventType.RefreshPrompt)
  EventDispatcher.DispatchEvent(EEventType.HideTapCacheWeakTutorial)
  if not self.m_boardView:GetModel():PopCachedItem() then
    self:_PromptFullBoard()
    EventDispatcher.DispatchEvent(EEventType.PopCacheFailed)
    PlatformInterface.Vibrate(EVibrationType.Heavy)
    return
  end
  self.m_iconImg.transform.localScale = V3Zero
  if self.m_cacheIconTween then
    self.m_cacheIconTween:Kill()
    self.m_cacheIconTween = nil
  end
  if 1 < cachedItemsCount then
    self.m_popIconTween:Restart()
  else
    self.m_popIconTween:Pause()
  end
end

function BoardCacheRoot:PlayAnimation()
  if not self.m_visible then
    return
  end
  if self.m_arrowSeq ~= nil then
    self.m_arrowSeq:Kill()
    self.m_arrowSeq = nil
  end
  UIUtil.SetLocalPosition(self.m_arrowRectTrans, nil, self.m_arrowOriginY)
  self.m_arrowSeq = self.m_arrowRectTrans:DOAnchorPosY(self.m_arrowRectTrans.localPosition.y - 15, 1):SetLoops(2, LoopType.Yoyo):SetEase(Ease.Linear)
end

function BoardCacheRoot:_PromptFullBoard()
  GM.UIManager:ShowPromptWithKey("hint_board_full", self.m_boardView:ConvertWorldPositionToScreenPosition(self.transform.position + Vector3(0, 70, 0)))
end

function BoardCacheRoot:GetFlyTargetPosition()
  local originScale = self.transform.localScale
  local originIconScale = self.m_iconImg.transform.localScale
  local originHeight = self.transform.sizeDelta.y
  local originPosY = self.m_rootTrans.anchoredPosition.y
  self:OnStateChanged(true)
  local targetScale = self:GetCacheElementScale()
  self.transform.localScale = Vector3(targetScale, targetScale, 1)
  self:_RebuildLayout()
  local pos = self.m_iconImg.transform.position
  self.transform.localScale = originScale
  self.m_iconImg.transform.localScale = originIconScale
  UIUtil.SetSizeDelta(self.transform, nil, originHeight)
  UIUtil.SetAnchoredPosition(self.m_rootTrans, nil, originPosY)
  self:_RebuildLayout()
  return pos
end

function BoardCacheRoot:IsShowing()
  return self.m_visible
end

function BoardCacheRoot:ShowHandTapEffect(bShow)
  UIUtil.SetActive(self.m_handEffectGo, bShow)
end

function BoardCacheRoot:Update()
  if self.m_bPlayingAnimation then
    self:_RebuildLayout()
  end
end

function BoardCacheRoot:_RebuildLayout()
  if not self.m_contentTrans then
    self.m_contentTrans = self.transform.parent
  end
  if GM.ConfigModel:UseNewCacheLayout() then
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.transform.parent)
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_contentTrans)
end

function BoardCacheRoot:IsPlayingAnimation()
  return self.m_bPlayingAnimation
end

function BoardCacheRoot:CanPlayAnimation()
  local cachedItemsCount = self:GetCachedItemCount()
  return (not self:IsShowing() or self.m_showTween ~= nil) and 0 < cachedItemsCount
end

function BoardCacheRoot:GetCachedItemCount()
  return self.m_boardView:GetModel():GetCachedItemCount() + #self.m_activityProperty
end

function BoardCacheRoot:GetAnimationLeftWidth()
  return 198 * (self.m_originScale - self.gameObject.transform.localScale.x)
end

function BoardCacheRoot:GetCacheElementScale()
  if GM.ConfigModel:UseNewCacheLayout() and BoardTaskBubble.CanShow() then
    return 0.88
  end
  return 1
end

function BoardCacheRoot:SetClickable(bClickable)
  self.m_clickable = bClickable
end

BoardCacheRootNode = {}
BoardCacheRootNode.__index = BoardCacheRootNode

function BoardCacheRootNode:OnEnable()
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.transform)
end
