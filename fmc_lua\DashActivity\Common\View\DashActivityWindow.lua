DashActivityBaseWindow = setmetatable({showWindowMask = false}, BaseWindow)
DashActivityBaseWindow.__index = DashActivityBaseWindow

function DashActivityBaseWindow:Init(activityType, autoOpen)
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_model:SetWindowOpened()
  local isFinaling = self.m_model:IsFinalChallengeing()
  if self.m_avatarBgImg then
    local sprite = isFinaling and self.m_avatarBgSprite2 or self.m_avatarBgSprite1
    self.m_avatarBgImg.sprite = sprite
  end
  if self.m_scoreSlider and self.m_model:HasFinalChallenge() then
    self.m_scoreSlider.gameObject.transform.localPosition = isFinaling and Vector3(0, -345, 0) or Vector3(0, -213.9, 0)
  end
  if autoOpen and GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    GM.SceneManager:ChangeGameMode(EGameMode.Main)
  end
  AddHandlerAndRecordMap(self.m_model.event, DashActivityEventType.StateChanged, {
    obj = self,
    method = self.Close
  })
  if autoOpen then
    self:LogWindowAction(EBIType.UIActionType.Open, {
      EBIReferType.AutoPopup
    })
  else
    self:LogWindowAction(EBIType.UIActionType.Open, {
      EBIReferType.UserClick
    })
  end
end

function DashActivityBaseWindow:OnDestroy()
  GM.UIManager:RemoveAllEventLocks(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
end

DashActivityNoticeWindow = setmetatable({}, DashActivityBaseWindow)
DashActivityNoticeWindow.__index = DashActivityNoticeWindow

function DashActivityNoticeWindow:Init(activityType, autoOpen)
  DashActivityBaseWindow.Init(self, activityType, autoOpen)
  local activityDefinition = DashActivityDefinition[activityType]
  self.m_avatar:Init(activityDefinition.NoticeWindowAvatarAnimationName)
  self.m_nextStateTime = self.m_model:GetNextStateTime()
  self:UpdatePerSecond()
end

function DashActivityNoticeWindow:UpdatePerSecond()
  if self.m_nextStateTime ~= nil then
    local delta = math.max(0, self.m_nextStateTime - GM.GameModel:GetServerTime())
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function DashActivityNoticeWindow:OnCloseFinish()
  DashActivityBaseWindow.OnCloseFinish(self)
  GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.CashDash)
end

DashActivityReadyWindow = setmetatable({}, DashActivityBaseWindow)
DashActivityReadyWindow.__index = DashActivityReadyWindow

function DashActivityReadyWindow:BeforeOpenCheck(activityType)
  local model = GM.ActivityManager:GetModel(activityType)
  return model:GetState() == ActivityState.Started
end

function DashActivityReadyWindow:Init(activityType)
  DashActivityBaseWindow.Init(self, activityType, true)
  local activityDefinition = DashActivityDefinition[activityType]
  self.m_avatar:Init(activityDefinition.ReadyWindowAvatarAnimationName)
end

DashActivityMainWindow = setmetatable({}, DashActivityBaseWindow)
DashActivityMainWindow.__index = DashActivityMainWindow

function DashActivityMainWindow:Init(activityType, afterGetRewards)
  DashActivityBaseWindow.Init(self, activityType, false)
  self.activityDefinition = DashActivityDefinition[activityType]
  local textIndex = math.random(1, 5)
  local currentLevel = self.m_model:GetLevel()
  local maxRewardLevel = self.m_model:GetMaxRewardLevel()
  if afterGetRewards then
    self.m_avatar:Init(self.activityDefinition.MainWindowDefinition.AfterGetRewards.AvatarAnimationName)
    self.m_descriptionText.text = GM.GameTextModel:GetText(self.activityDefinition.MainWindowDefinition.AfterGetRewards.DescriptionTextPrefix .. textIndex)
  else
    local currentScore = self.m_model:GetScore()
    local currentNeedScore = self.m_model:GetLevelConfigs()[currentLevel].score
    if currentScore < currentNeedScore / 2 then
      self.m_avatar:Init(self.activityDefinition.MainWindowDefinition.LessThanHalf.AvatarAnimationName)
      self.m_descriptionText.text = GM.GameTextModel:GetText(self.activityDefinition.MainWindowDefinition.LessThanHalf.DescriptionTextPrefix .. textIndex)
    else
      self.m_avatar:Init(self.activityDefinition.MainWindowDefinition.MoreThanHalf.AvatarAnimationName)
      self.m_descriptionText.text = GM.GameTextModel:GetText(self.activityDefinition.MainWindowDefinition.MoreThanHalf.DescriptionTextPrefix .. textIndex)
    end
  end
  local bHaveFinal = self.m_model:IsFinalChallengeing()
  if bHaveFinal then
    if not self.m_model:IsPlayEntryFinal() then
      self.m_descriptionText.text = GM.GameTextModel:GetText(self.activityDefinition.MainWindowDefinition.LastLevelDesc)
    end
    if self.m_finalChallengeCanv then
      self.m_finalChallengeCanv.alpha = 1
    end
  end
  if self.m_starGo then
    self.m_starGo:SetActive(self.m_model:HasFinalChallenge() and not self.m_model:IsPlayEntryFinal())
  end
  if self.m_flyStarLuaTable then
    self.m_flyStarLuaTable:SetEffectGuangActive(self.m_model:HasFinalChallenge() and self.m_model:IsPlayEntryFinal())
  end
  if self.m_flyStar then
    self.m_flyStar.gameObject.transform.localScale = Vector3.zero
  end
  self.m_scoreSlider.gameObject.transform.localPosition = currentLevel > maxRewardLevel and Vector3(0, -345, 0) or Vector3(0, -213.9, 0)
  self.m_scoreSlider:Init(activityType)
  self.m_scoreSlider:UpdateContent()
  self.m_rewardProgress:Init(activityType)
  self.m_rewardProgress:SetRewardActive(not bHaveFinal)
  self.m_nextStateTime = self.m_model:GetNextStateTime()
  self:UpdatePerSecond()
  self:_PlayEntryFinalChallengeAnimation()
end

function DashActivityMainWindow:UpdatePerSecond()
  if self.m_nextStateTime ~= nil then
    local delta = math.max(0, self.m_nextStateTime - GM.GameModel:GetServerTime())
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function DashActivityMainWindow:OnButtonClicked()
  self:Close()
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    GM.SceneManager:ChangeGameMode(EGameMode.Board)
  end
end

function DashActivityMainWindow:GetButtonTransform()
  return self.m_button
end

function DashActivityMainWindow:_OnStarBtnClick()
  if self.m_tipBgGo == nil then
    return
  end
  local scale = self.m_tipBgGo.transform.localScale
  if scale.x == 0 then
    self.m_tipBgGo.transform:DOScale(1, 0.3)
    DOVirtual.DelayedCall(3, function()
      if self.m_tipBgGo and not self.m_tipBgGo:IsNull() then
        self.m_tipBgGo.transform:DOScale(0, 0.3)
      end
    end)
  end
end

function DashActivityMainWindow:_PlayEntryFinalChallengeAnimation()
  if not self.m_model:CanPlayEntryFinalChallengeAnimation() then
    return
  end
  self.m_model:SetPlayEntryFinal()
  GM.UIManager:SetEventLock(true, self)
  self.m_finalChallengeCanv.alpha = 0
  self.m_bubbleCanv.alpha = 0
  self.m_scoreSlider.gameObject.transform.localPosition = Vector3(0, -213.9, 0)
  self.m_flyStar.gameObject.transform.localScale = Vector3.one
  self.m_starGo:SetActive(false)
  self.m_scoreSlider:SetPreLevelView()
  local seq = DOTween.Sequence()
  seq:InsertCallback(1.8, function()
    self.m_rewardProgress:PlayFadeOut()
    self.m_scoreSlider:PlayFadeOut()
  end)
  seq:InsertCallback(2, function()
    local config = {
      control1 = Vector2(315, -258),
      control2 = Vector2(63, -134),
      to = Vector2(-212, -193),
      from = Vector2(469, -483),
      posType = BezierPosType.Local,
      easeType = BezierEaseType.Sin
    }
    self.m_flyStarLuaTable:SetStarTailActive(true)
    self.m_flyStar:MoveTo(config, 0.8)
    self.m_flyStar.gameObject.transform:DOScale(1.8 * Vector3.one, 0.3)
    self.m_flyStar.gameObject.transform:DOScale(Vector3.one, 0.3):SetDelay(0.5)
  end)
  seq:InsertCallback(2.8, function()
    self.m_scoreSlider:UpdateContent()
    self.m_scoreSlider.gameObject.transform.localPosition = Vector3(0, -345, 0)
    self.m_finalChallengeCanv:DOFade(1, 0.3)
    self.m_flyStarLuaTable:PlayEndEffect()
    self.m_flyStarLuaTable:SetStarTailActive(false)
  end)
  seq:InsertCallback(3, function()
    self.m_scoreSlider:PlayScaleIn()
    self.m_bubbleCanv.alpha = 1
    self.m_bubbleCanv:DOFade(1, 0.3)
    GM.UIManager:SetEventLock(false, self)
  end)
end

DashActivitySuccessWindow = setmetatable({}, DashActivityBaseWindow)
DashActivitySuccessWindow.__index = DashActivitySuccessWindow

function DashActivitySuccessWindow:Init(activityType)
  DashActivityBaseWindow.Init(self, activityType, true)
  local activityDefinition = DashActivityDefinition[activityType]
  self.m_avatar:Init(activityDefinition.SuccessWindowAvatarAnimationName)
  self.m_rewardProgress:Init(activityType, true)
  local bFinal = self.m_model:IsFinalSccuess()
  if self.m_completeRectTrans then
    self.m_completeRectTrans.localPosition = bFinal and Vector3(0, -286, 0) or Vector3(0, -218, 0)
    self.m_rewardProgress:SetRewardActive(not bFinal)
  end
  if self.m_avatarBgImg then
    local sprite = bFinal and self.m_avatarBgSprite2 or self.m_avatarBgSprite1
    self.m_avatarBgImg.sprite = sprite
  end
end

DashActivityFailWindow = setmetatable({}, DashActivityBaseWindow)
DashActivityFailWindow.__index = DashActivityFailWindow

function DashActivityFailWindow:Init(activityType)
  DashActivityBaseWindow.Init(self, activityType, true)
  local activityDefinition = DashActivityDefinition[activityType]
  self.m_avatar:Init(activityDefinition.FailWindowAvatarAnimationName)
  self.m_scoreSlider:Init(activityType)
  self.m_scoreSlider:UpdateContent()
  self.m_rewardProgress:Init(activityType)
  local currentLevel = self.m_model:GetLevel()
  local maxRewardLevel = self.m_model:GetMaxRewardLevel()
  if self.m_finalChallengeCanv and currentLevel > maxRewardLevel then
    self.m_finalChallengeCanv.alpha = 1
    self.m_rewardProgress:SetRewardActive(currentLevel <= maxRewardLevel)
  end
end

DashFlyStar = {}
DashFlyStar.__index = DashFlyStar

function DashFlyStar:Init()
end

function DashFlyStar:SetStarTailActive(bActive)
  self.m_effect_TWGo:SetActive(bActive)
end

function DashFlyStar:SetEffectGuangActive(bActive)
  self.m_guangGo:SetActive(bActive)
end

function DashFlyStar:PlayEndEffect()
  self.m_boomGo:SetActive(true)
  self.m_guangGo:SetActive(true)
  DOVirtual.DelayedCall(1.5, function()
    self.m_boomGo:SetActive(false)
  end)
end
