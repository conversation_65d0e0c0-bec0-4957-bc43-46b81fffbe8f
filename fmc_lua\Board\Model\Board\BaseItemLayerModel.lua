BaseItemLayerModel = {}
BaseItemLayerModel.__index = BaseItemLayerModel
BaseItemLayerModel.Directions4Way = {
  Vector.Create(1, 0),
  Vector.Create(0, 1),
  Vector.Create(-1, 0),
  Vector.Create(0, -1)
}
BaseItemLayerModel.Directions8Way = {
  Vector.Create(0, -1),
  Vector.Create(1, -1),
  Vector.Create(1, 0),
  Vector.Create(1, 1),
  Vector.Create(0, 1),
  Vector.Create(-1, 1),
  Vector.Create(-1, 0),
  Vector.Create(-1, -1)
}

function BaseItemLayerModel:Init(boardModel)
  self.m_boardModel = boardModel
  self.m_items = boardModel:CreateMatrix()
end

function BaseItemLayerModel:GetItem(position)
  Log.Assert(false, "GetItem()是抽象接口")
end

function BaseItemLayerModel:SetItem(position, item)
  Log.Assert(false, "SetItem()是抽象接口")
end

function BaseItemLayerModel:FilterItems(filter, limitCount)
  assert(false, "FilterItems()是抽象接口")
end

function BaseItemLayerModel:FilterItemsByType(itemType, filter, limitCount)
  assert(false, "FilterItemsByType()是抽象接口")
end

function BaseItemLayerModel:GetItemsByType(itemType)
  assert(false, "GetItemsByType()是抽象接口")
end

function BaseItemLayerModel:GetItemCountByType(itemType)
  assert(false, "GetItemCountByType()是抽象接口")
end

function BaseItemLayerModel:GetEmptyPositionCount()
  assert(false, "GetEmptyPositionCount()是抽象接口")
end

function BaseItemLayerModel:HasEmptyPosition()
  return self:GetEmptyPositionCount() > 0
end

function BaseItemLayerModel:FindEmptyPositionInSpreadOrder(centerPosition, openedPosition)
  local currentPosition = centerPosition
  local maxTiles = math.max(self.m_boardModel:GetHorizontalTiles(), self.m_boardModel:GetVerticalTiles())
  for i = 1, maxTiles - 1 do
    currentPosition = currentPosition + Vector.Create(-1, -1)
    local stepLength = 2 * i
    for direction = 1, 4 do
      for _ = 1, stepLength do
        currentPosition = currentPosition + BaseItemLayerModel.Directions4Way[direction]
        if currentPosition:IsValid() and self:GetItem(currentPosition) == nil then
          return currentPosition
        end
      end
    end
  end
  return openedPosition
end

function BaseItemLayerModel:FindEmptyPositionInValidOrder()
  local position
  for y = 1, self.m_boardModel:GetVerticalTiles() do
    for x = 1, self.m_boardModel:GetHorizontalTiles() do
      position = self.m_boardModel:CreatePosition(x, y)
      if self:GetItem(position) == nil then
        return position
      end
    end
  end
  return nil
end

function BaseItemLayerModel:FindEmptyPositionFromBottom2Top()
  local x = self.m_boardModel:GetHorizontalTiles()
  local y = self.m_boardModel:GetVerticalTiles()
  local position
  for j = y, 1, -1 do
    for i = 1, x do
      position = self.m_boardModel:CreatePosition(i, j)
      if self:GetItem(position) == nil then
        return position
      end
    end
  end
  return nil
end

function BaseItemLayerModel:FindEmptyPositionInCircleOrder(centerPosition)
  for _, direction in ipairs(BaseItemLayerModel.Directions8Way) do
    local position = centerPosition + direction
    if position:IsValid() and self:GetItem(position) == nil then
      return position
    end
  end
  return nil
end
