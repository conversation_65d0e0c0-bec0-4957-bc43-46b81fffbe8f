DataBalanceModel = {}
DataBalanceModel.__index = DataBalanceModel

function DataBalanceModel:Init()
end

function DataBalanceModel:CalculateDiff()
  if GM.TaskManager:IsVersionRevert() then
    return
  end
  local cacheVersion = GM.MiscModel:Get(EMiscKey.DataBalanceVersion)
  local curVersion = GameConfig.GetCurrentVersion()
  local index = StringUtil.rFindChar(curVersion, ".")
  if index then
    curVersion = string.sub(curVersion, 1, index - 1)
  end
  if cacheVersion == curVersion then
    local diffString = GM.MiscModel:Get(EMiscKey.DataBalanceDiff)
    Log.Info("投放兼容：读取之前存储的差异信息为 " .. diffString)
    local map = ItemUtility.String2Map(diffString)
    self.m_diffMap = {}
    for key, value in pairs(map) do
      value = tonumber(value)
      if value ~= nil and value ~= 0 then
        self.m_diffMap[key] = value
      end
    end
  else
    self.m_diffMap = self:_GetBalanceDiffMap()
    local diffString = ItemUtility.Map2String(self.m_diffMap)
    Log.Info("投放兼容：新计算差异信息为 " .. diffString)
    GM.MiscModel:Set(EMiscKey.DataBalanceDiff, diffString)
    GM.MiscModel:Set(EMiscKey.DataBalanceVersion, curVersion)
    if next(self.m_diffMap) ~= nil then
      GM.BIManager:LogAction(EBIType.DataBalanceInfo, diffString)
    end
  end
end

function DataBalanceModel:_GetBalanceDiffMap()
  local shouldHaveDataMap = self:_GetShouldHaveDataMap()
  local userCurDataMap = self:_GetUserCurDataMap()
  local diff = {}
  local cur
  for key, value in pairs(shouldHaveDataMap) do
    cur = userCurDataMap[key] or 0
    if cur ~= value then
      diff[key] = cur - value
    end
  end
  for key, value in pairs(userCurDataMap) do
    if not shouldHaveDataMap[key] then
      diff[key] = value
    end
  end
  return diff
end

function DataBalanceModel:_GetUserCurDataMap()
  local userCurDataMap = {}
  local codeCountMap = GM.MainBoardModel:GetCodeCountMap(true, true, true)
  local needBalance, chain, count
  for itemCode, itemCount in pairs(codeCountMap) do
    needBalance, chain, count = DataBalanceHelper.IsNeedBalanceCode(itemCode)
    if needBalance then
      userCurDataMap[chain] = (userCurDataMap[chain] or 0) + count * itemCount
    end
  end
  return userCurDataMap
end

function DataBalanceModel:_GetShouldHaveDataMap()
  local shouldHaveDataMap = {}
  local needBalance, chain, count
  local configs = require("Data.Config.BoardModelConfig")
  for _, config in ipairs(configs) do
    for _, code in ipairs(config) do
      needBalance, chain, count = DataBalanceHelper.IsNeedBalanceCode(code)
      if needBalance then
        shouldHaveDataMap[chain] = (shouldHaveDataMap[chain] or 0) + count
      end
    end
  end
  local taskChapter = GM.TaskManager:GetOngoingChapterId()
  local chapters = GM.ChapterDataModel:GetChapterSequence()
  for i = 1, taskChapter do
    self:_AddRewards(shouldHaveDataMap, GM.ChapterDataModel:GetChapterUnlockRewards(i))
  end
  local configs, chapterName
  for i = 1, taskChapter - 1 do
    chapterName = tostring(chapters[i])
    configs = require("Data.Config.Mainline." .. chapterName .. ".TaskConfig_" .. chapterName)
    configs = configs or {}
    for _, config in ipairs(configs) do
      self:_AddRewards(shouldHaveDataMap, config.Rewards)
    end
  end
  self:_AddRewards(shouldHaveDataMap, GM.TaskManager:GetCurChapterFinishedTaskRewards())
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local orderCId, orderGId = orderModel:GetOrderGroupInfo()
  for i = 1, orderCId - 1 do
    configs = require("Data.Config.OrderGroupConfig_" .. i)
    configs = configs or {}
    for _, config in ipairs(configs) do
      self:_AddRewards(shouldHaveDataMap, config.Rewards)
    end
  end
  self:_AddRewards(shouldHaveDataMap, orderModel:GetCurChapterFinishedGroupRewards())
  return shouldHaveDataMap
end

function DataBalanceModel:_AddRewards(shouldHaveDataMap, rewards)
  if rewards == nil then
    return
  end
  local needBalance, chain, count
  for _, reward in ipairs(rewards) do
    needBalance, chain, count = DataBalanceHelper.IsNeedBalanceCode(reward[PROPERTY_TYPE])
    if needBalance then
      shouldHaveDataMap[chain] = (shouldHaveDataMap[chain] or 0) + count * reward[PROPERTY_COUNT]
    end
  end
end

function DataBalanceModel:NeedBalance()
  if not self.m_diffMap or not next(self.m_diffMap) then
    return false
  end
  if not GM.ConfigModel:IsServerControlOpen(EGeneralConfType.DataBalance) then
    return false
  end
  return true
end

function DataBalanceModel:Balance()
  self.m_diffMap = self:_GetBalanceDiffMap()
  if not self.m_diffMap or not next(self.m_diffMap) then
    return {}
  end
  GM.BIManager:LogAction(EBIType.DataBalanceEffect, ItemUtility.Map2String(self.m_diffMap))
  local addChainCount = {}
  local removeCodeCount = {}
  local userCodeCountMap = GM.MainBoardModel:GetCodeCountMap(true, true, true)
  for chain, diff in pairs(self.m_diffMap) do
    if diff < 0 then
      addChainCount[chain] = (addChainCount[chain] or 0) - diff
    else
      self:_BalanceMoreItems(chain, diff, userCodeCountMap, addChainCount, removeCodeCount)
    end
  end
  local addCodeCount = {}
  local boardRemovedItems = {}
  for itemCode, count in pairs(removeCodeCount) do
    for i = 1, count do
      self:_RemoveItem(itemCode, addCodeCount, boardRemovedItems)
    end
  end
  self:_AddItems(addChainCount, addCodeCount)
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
  EventDispatcher.DispatchEvent(EEventType.ItemRetrieved)
  GM.MainBoardModel.event:Call(BoardEventType.BatchRemoveItems, {Removed = boardRemovedItems})
  self.m_diffMap = {}
  GM.MiscModel:Set(EMiscKey.DataBalanceDiff, "")
  return addCodeCount
end

function DataBalanceModel:_BalanceMoreItems(chain, diff, userCodeCountMap, addChainCount, removeCodeCount)
  local remainDiffCount = diff
  local maxLevel = GM.ItemDataModel:GetChainMaxLevel(chain)
  local itemCode, userCount, toLevel1Count
  for level = 1, maxLevel do
    itemCode = ItemUtility.GetItemType(chain, level)
    userCount = userCodeCountMap[itemCode]
    if userCount ~= nil and 0 < userCount then
      for i = 1, userCount do
        toLevel1Count = ItemUtility.GetToLevel1Count(level)
        if remainDiffCount >= toLevel1Count then
          removeCodeCount[itemCode] = (removeCodeCount[itemCode] or 0) + 1
          remainDiffCount = remainDiffCount - toLevel1Count
          if remainDiffCount == 0 then
            return
          end
        else
          removeCodeCount[itemCode] = (removeCodeCount[itemCode] or 0) + 1
          addChainCount[chain] = toLevel1Count - remainDiffCount
          return
        end
      end
    end
  end
end

function DataBalanceModel:_RemoveItem(toRemoveCode, addCodeCount, boardRemovedItems)
  local boardModel = GM.MainBoardModel
  local item, itemCode
  local cacheCount = boardModel:GetCachedItemCount()
  for i = 1, cacheCount do
    itemCode = boardModel:GetCachedItem(i)
    if itemCode == toRemoveCode then
      boardModel:RemoveCachedItem(i)
      Log.Info("投放兼容：从缓存队列移除 " .. toRemoveCode)
      return
    end
  end
  local storeCount = boardModel:GetStoredItemCount()
  for i = 1, storeCount do
    item = boardModel:GetStoredItem(i)
    if item:GetCode() == toRemoveCode then
      boardModel:GetStoreModel():RemoveItem(i)
      self:_BeforeRemoveCookItem(item, addCodeCount)
      boardModel:RemoveItem(item)
      Log.Info("投放兼容：从仓库移除 " .. toRemoveCode)
      return
    end
  end
  local items = boardModel:GetAllBoardItems()
  for item, _ in pairs(items) do
    if item:GetCode() == toRemoveCode then
      self:_BeforeRemoveCookItem(item, addCodeCount)
      boardModel:RemoveItem(item)
      table.insert(boardRemovedItems, item)
      Log.Info("投放兼容：从棋盘移除 " .. toRemoveCode)
      return
    end
  end
  Log.Error("未完成移除，不应该")
end

function DataBalanceModel:_BeforeRemoveCookItem(originItem, addCodeCount)
  local cookCmp = originItem:GetComponent(ItemCook)
  if cookCmp == nil then
    return
  end
  local state = cookCmp:GetState()
  if state == EItemCookState.Cooked or state == EItemCookState.Cooking then
    local recipe = cookCmp:GetRecipe()
    addCodeCount[recipe] = (addCodeCount[recipe] or 0) + 1
    Log.Info("投放兼容：移除厨具，补菜品 " .. recipe)
  else
    local matCodes = cookCmp:GetCurMaterialsArray()
    Log.Info("投放兼容：移除厨具，补原材料 " .. table.concat(matCodes, ","))
    for _, itemCode in ipairs(matCodes) do
      addCodeCount[itemCode] = (addCodeCount[itemCode] or 0) + 1
    end
  end
  cookCmp:RemoveInnerMaterials()
end

function DataBalanceModel:_AddItems(addChainCount, addCodeCount)
  local maxLevel, diffCount, itemCode, toLevel1Count, addCount
  for chain, count in pairs(addChainCount) do
    diffCount = count
    maxLevel = GM.ItemDataModel:GetChainMaxLevel(chain)
    for level = maxLevel, 1, -1 do
      itemCode = ItemUtility.GetItemType(chain, level)
      toLevel1Count = ItemUtility.GetToLevel1Count(level)
      if diffCount >= toLevel1Count then
        addCount = diffCount // toLevel1Count
        addCodeCount[itemCode] = (addCodeCount[itemCode] or 0) + addCount
        diffCount = diffCount - toLevel1Count * addCount
        if diffCount <= 0 then
          break
        end
      end
    end
  end
  local arrItems = {}
  for code, count in pairs(addCodeCount) do
    for i = 1, count do
      arrItems[#arrItems + 1] = code
    end
  end
  Log.Info("投放兼容：所有补棋子 " .. table.concat(arrItems, ","))
  GM.MainBoardModel:CacheItems(arrItems, CacheItemType.Stack)
  return addCodeCount
end

DataBalanceHelper = {}

function DataBalanceHelper.IsNeedBalanceCode(code)
  local index = StringUtil.rFindChar(code, "#")
  if index ~= nil then
    code = string.sub(code, index + 1)
  end
  local config = GM.ItemDataModel:GetModelConfig(code, true)
  if not config then
    return false
  end
  local chain = config.ChainId
  local level = config.ChainLevel
  if not StringUtil.StartWith(chain, "eq_") and not StringUtil.StartWith(chain, "pd_") then
    return false
  end
  local toLevel1Count = ItemUtility.GetToLevel1Count(level)
  return true, chain, toLevel1Count
end
