OrderGroupPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true
  },
  canIgnorePopup = false
}, BasePopupHelper)
OrderGroupPopupHelper.__index = OrderGroupPopupHelper

function OrderGroupPopupHelper.Create()
  local helper = setmetatable({}, OrderGroupPopupHelper)
  helper:Init()
  return helper
end

function OrderGroupPopupHelper:Init()
  BasePopupHelper.Init(self)
  self.m_orderModel = GM.MainBoardModel:GetOrderModel()
  self:_UpdateState()
  EventDispatcher.AddListener(EEventType.OrderFinished, self, self._UpdateState)
  EventDispatcher.AddListener(EEventType.OnClaimedOrderGroupReward, self, self._OnClaimedOrderGroupReward)
end

function OrderGroupPopupHelper:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function OrderGroupPopupHelper:_OnClaimedOrderGroupReward()
  self:_UpdateState()
  self.m_canPopDayClearWindow = GM.MainBoardModel:GetOrderModel():IsAllCanUnlockGroupFinished() and not GM.TaskManager:CanFinishOngoingTask()
end

function OrderGroupPopupHelper:_UpdateState()
  self.m_canClaimGroupReward = self.m_orderModel:CanClaimGroupReward()
end

function OrderGroupPopupHelper:NeedCheckPopup()
  return self.m_canClaimGroupReward or self.m_canPopDayClearWindow
end

function OrderGroupPopupHelper:CheckPopup()
  if self.m_canClaimGroupReward then
    self.m_canClaimGroupReward = false
    return UIPrefabConfigName.OrderGroupFinishWindow
  end
  if self.m_canPopDayClearWindow then
    self.m_canPopDayClearWindow = false
    return UIPrefabConfigName.OrderDayClearWindow
  end
end
