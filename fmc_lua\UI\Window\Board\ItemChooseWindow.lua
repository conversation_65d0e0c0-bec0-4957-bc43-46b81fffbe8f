ItemChooseWindow = setmetatable({}, BaseWindow)
ItemChooseWindow.__index = ItemChooseWindow

function ItemChooseWindow:Init(item)
  self.m_item = item
  local spriteName = GM.ItemDataModel:GetSpriteName(item:GetType())
  SpriteUtil.SetImage(self.m_iconImage, spriteName, true)
  self.m_cells = {}
  local itemChoose = item:GetComponent(ItemChoose)
  local choices = itemChoose:GetChoices()
  for index = 1, #choices do
    local cellObject = Object.Instantiate(self.m_cellPrefab, self.m_chooseArea)
    local cell = cellObject:GetLuaTable()
    cell:Init(self, index, choices[index])
    table.insert(self.m_cells, cell)
    cell:TurnAround(0.2 + index * 0.18)
  end
  self.m_iconGo:SetActive(true)
  self.m_selectGo:SetActive(false)
end

function ItemChooseWindow:OnCellClicked(index)
  self.m_selectedIndex = index
  for i, cell in ipairs(self.m_cells) do
    cell:SetSelected(i == index)
  end
  self.m_iconGo:SetActive(false)
  self.m_selectGo:SetActive(true)
end

function ItemChooseWindow:OnSelectButtonClicked()
  self.m_selectGo:SetActive(false)
  self:Close()
  self.m_item:GetBoardModel():ChooseItem(self.m_item, self.m_selectedIndex)
end
