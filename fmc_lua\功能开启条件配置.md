# 项目功能开启条件配置文档

## 1. 基础功能开启条件 (FunctionEnableConfig)

### 配置文件位置
`Data/Config/FunctionEnableConfig.lua`

### 功能列表

| 功能名称 | 功能键 | 开启条件类型 | 开启条件 | 说明 |
|---------|--------|-------------|----------|------|
| 图鉴系统 | discoveries | 等级 | 5级 | 玩家达到5级解锁图鉴功能 |
| 泡泡系统 | bubble | 等级 | 5级 | 玩家达到5级解锁泡泡功能 |
| 库存系统 | inventory | 等级 | 2级 | 玩家达到2级解锁库存功能 |
| 商店系统 | shop | 等级 | 6级 | 玩家达到6级解锁商店功能 |

### 开启条件类型说明
- **level**: 基于玩家等级的解锁条件
- **openTask**: 基于任务完成的解锁条件
- **openRoom**: 基于章节进度的解锁条件

## 2. 服务器控制开关 (EGeneralConfType)

### 配置说明
这些开关由服务器配置控制，可以动态开启/关闭功能。

### 开关列表

| 开关名称 | 配置键 | 功能说明 |
|---------|--------|----------|
| 防误触 | anti_mistouch | 控制防误触功能的持续时间 |
| CG控制 | cg_control | 控制CG动画播放和跳过功能 |
| iOS更新提示 | iosUpdateHint | iOS平台更新提示控制 |
| 泡泡购买奖励 | bubble_buy_bonus | 泡泡购买额外奖励功能 |
| 双倍体力触发 | doubleEnergy_trigger | 双倍体力活动触发控制 |
| 双倍体力奖励 | doubleEnergy_trigger_bonus | 双倍体力购买奖励配置 |
| 四倍体力 | quadEnergy | 四倍体力活动控制 |
| 通知提醒次数 | notify_remind_time | 通知提醒次数限制 |
| 社交绑定 | social_bind | 社交账号绑定功能 |
| 按钮显示天数 | button_show_day | 特定按钮显示天数控制 |
| 金币平衡 | balance_gold | 金币平衡机制 |
| 跳过道具不足 | skipprop_not_enough | 跳过道具不足提示 |
| 食材配方提示 | ingredient_recipe_hint | 食材配方提示功能 |
| 缓存功能 | cache | 缓存相关功能控制 |
| 热修复最低等级 | hotfix_min_level | 热修复功能最低等级要求 |
| 数据平衡 | data_balance | 数据平衡功能 |
| 设备配方提示 | equipment_recipe_hint | 设备配方提示功能 |
| 新订单奖励动画 | new_order_reward_anim | 新订单奖励动画控制 |
| 跳过奖励动画 | new_order_reward_anim_skip | 跳过奖励动画功能 |
| 无返回奖励动画 | new_order_reward_anim_no_back | 无返回的奖励动画 |
| 缓存置顶 | cache_pin_eqpd_top | 设备和生产器缓存置顶 |
| 物品自动回收 | item_auto_recycle | 物品自动回收功能 |
| 订单能量排序 | order_seq_energy_diff | 按能量差异排序订单 |
| 物品删除 | item_delete_corn_husk | 特定物品删除功能 |
| 棋盘烹饪泡泡 | board_cook_bubble | 棋盘烹饪泡泡功能 |
| 订单新标签 | order_new_tag | 订单新标签显示 |
| 跳过提示效果 | skiphint_effect | 跳过提示效果控制 |
| Facebook功能 | facebook | Facebook相关功能 |
| 回归用户奖励 | RecallOrder | 回归用户奖励功能 |

## 3. 棋盘区域解锁条件

### 配置文件位置
`Data/Config/BoardModelConfigLocker.lua`

### 解锁条件

| 区域位置 | 尺寸 | 解锁等级 | 解锁天数 |
|---------|------|----------|----------|
| (1,1) 3x2 | 3x2 | 4级 | 第4天 |
| (4,1) 4x2 | 4x2 | 5级 | 第6天 |
| (1,7) 7x1 | 7x1 | 6级 | 第9天 |
| (1,8) 2x2 | 2x2 | 12级 | 第22天 |
| (3,8) 3x2 | 3x2 | 7级 | 第12天 |
| (6,8) 2x2 | 2x2 | 9级 | 第15天 |

## 4. 章节解锁条件

### 配置文件位置
`Data/Config/ChapterConfig.lua`

### 章节列表

| 章节ID | 章节名称 | 解锁奖励 | 剩余金币要求 |
|--------|----------|----------|-------------|
| 1 | Market | - | 132 |
| 2 | BBQ | - | 292 |
| 3 | Seafood | - | 485 |
| 4 | Bakery | pd_6_4 x1 | 695 |
| 5 | Wine | pd_7_4 x1 | 796 |
| 6 | Pasta | pd_a6_1 x1 | 765 |
| 7 | Tapas | pd_a7_1 x1 | 856 |
| 8 | Sushi | pd_a8_1 x1 | 864 |
| 9 | Sausage | pd_a9_1 x1 | 713 |

## 5. 商店功能解锁条件

### 配置文件位置
`Data/Config/ShopOrderConfig.lua`

### 商店模块解锁

| 模块名称 | 显示顺序 | 解锁等级 |
|---------|----------|----------|
| 宝石商店 | 5 | 1级 |
| 闪购商店 | 4 | 6级 |
| 每日特惠 | 3 | 6级 |
| 礼包商店 | 2 | 1级 |
| 通行证 | 1 | 1级 |

## 6. 活动类型定义

### 活动类型列表

| 活动类型 | 活动键 | 说明 |
|---------|--------|------|
| 烘焙活动 | bakeOut | 烘焙模式活动 |
| 金币竞赛 | coinRace | 金币竞赛活动 |
| 寻宝挖掘 | treasureDig | 寻宝挖掘活动 |
| PK竞赛 | pkRace | PK竞赛活动 |
| 额外棋盘1-7 | extraBoard1-7 | 7个额外棋盘活动 |
| 进度活动 | progress1_new | 进度条活动 |
| 惊喜宝箱 | surpriseChest | 惊喜宝箱活动 |
| 盲盒活动 | blindChest1 | 盲盒活动 |
| 通行证1-8 | bp1-8 | 8个通行证活动 |
| 相册活动 | album1 | 相册收集活动 |

## 7. 教程触发条件

### 教程开启条件类型

| 条件类型 | 说明 |
|---------|------|
| TutorialFinished | 指定教程完成后触发 |
| FunctionEnabled | 指定功能开启后触发 |
| CachedItemPushed | 缓存物品推送后触发 |
| MainLevel | 达到指定等级后触发 |
| TaskFinished | 指定任务完成后触发 |
| OrderGroupFinished | 订单组完成后触发 |
| ItemAdded | 添加指定物品后触发 |
| HasOrder | 存在指定订单时触发 |
| 活动开始条件 | 各种活动开始时触发 |

### 主要教程触发示例

| 教程ID | 触发条件 | 说明 |
|--------|----------|------|
| bubble | 5级 | 泡泡功能教程 |
| cd_speed | 2章3组完成 + 5级 | CD道具教程 |
| order_group | order10040教程完成 | 订单组入口教程 |
| additem_+8 | 2章1组完成 | 库存+8棋子教程 |

## 8. 物品解锁条件

### 物品保护等级
- **保护等级**: 3
- **适用物品**: 所有1级生产器 (pd_*_1)
- **保护机制**: 当物品链最大解锁等级 ≤ 保护等级时，1级物品不可出售

### 物品解锁状态
- **Unknown**: 未知状态
- **Locked**: 锁定状态  
- **Unlocked**: 已解锁状态

## 9. 系统配置参数

### 配置文件位置
`Data/Config/SystemConfig.lua`

### 系统参数

| 参数名称 | 数值 | 说明 |
|---------|------|------|
| 购买体力基础价格 | 10 | 买体力的初始宝石价格 |
| 购买体力增长系数 | 2 | 买体力价格的增长倍数 |
| 购买体力最大价格 | 160 | 买体力的最大宝石价格 |
| 物品宝箱最大类型 | 2 | 棋子宝箱同时喷发的棋子种类数 |

## 10. 功能开启检查机制

### 检查时机
- 玩家升级时
- 主线任务完成时  
- 新章节解锁时

### 检查逻辑
1. 遍历所有功能配置
2. 检查功能是否已开启
3. 验证开启条件是否满足
4. 触发功能开启事件

### 相关事件
- `EEventType.FunctionOpen`: 功能开启事件
- `EEventType.LevelUp`: 等级提升事件
- `EEventType.MainTaskFinished`: 主线任务完成事件
- `EEventType.NewChapterUnlocked`: 新章节解锁事件
