TimeUtil = {}
Sec2Day = 86400
local Sec2Day = Sec2Day
ETimeFormat = {
  YMD = "%Y/%m/%d",
  MDY = "%m/%d/%Y",
  DMY = "%d/%m/%Y",
  YMDHMS = "%Y-%m-%d %H:%M:%S",
  UTCYMDHMS = "!%Y-%m-%d %H:%M:%S"
}
ETimeUnits = {
  Second = 1,
  Minute = 2,
  Hour = 3,
  Day = 4,
  Month = 5,
  Year = 6
}
local timeBase = {
  [ETimeUnits.Second] = 60,
  [ETimeUnits.Minute] = 60,
  [ETimeUnits.Hour] = 24,
  [ETimeUnits.Day] = 30,
  [ETimeUnits.Month] = 12
}

function TimeUtil.GetTimeInSecond()
  return os.time()
end

function TimeUtil.GetTimeInMS()
  return TimeUtil.GetTimeInSecond() * 1000
end

function TimeUtil.ToMSOrHMS(timeStamp, customFormatHMS, customFormatMS)
  local m = math.floor(timeStamp / 60) % 60
  local s = math.floor(timeStamp % 60)
  if 3600 <= timeStamp then
    local h = math.floor(timeStamp / 3600)
    if customFormatHMS then
      return GM.GameTextModel:GetText(customFormatHMS, h, m, s)
    else
      return string.format("%02d:%02d:%02d", h, m, s)
    end
  end
  if customFormatMS then
    return GM.GameTextModel:GetText(customFormatMS, m, s)
  else
    return string.format("%02d:%02d", m, s)
  end
end

function TimeUtil.ToDate(timeStamp, eETimeFormat)
  eETimeFormat = eETimeFormat or ETimeFormat.MDY
  return os.date(eETimeFormat, timeStamp)
end

function TimeUtil.ToNearestUnit(timeStamp, eMinTimeUnits)
  local timeUnitsStr = {
    [ETimeUnits.Second] = GM.GameTextModel:GetText("common_time_s"),
    [ETimeUnits.Minute] = GM.GameTextModel:GetText("common_time_m"),
    [ETimeUnits.Hour] = GM.GameTextModel:GetText("common_time_h"),
    [ETimeUnits.Day] = GM.GameTextModel:GetText("common_time_d")
  }
  eMinTimeUnits = eMinTimeUnits or ETimeUnits.Second
  local tempTime = 1
  for i = 1, eMinTimeUnits - 1 do
    tempTime = tempTime * timeBase[i]
  end
  for i = eMinTimeUnits, ETimeUnits.Hour do
    if timeStamp < tempTime * timeBase[i] then
      return timeStamp // tempTime, timeUnitsStr[i]
    else
      tempTime = tempTime * timeBase[i]
    end
  end
  return timeStamp // tempTime, timeUnitsStr[ETimeUnits.Day]
end

function TimeUtil.ParseTimeDescription(second, num, bForceNum, bIgnoreZero)
  num = num or 2
  local strSec = GM.GameTextModel:GetText("common_time_s")
  local strMin = GM.GameTextModel:GetText("common_time_m")
  local strHour = GM.GameTextModel:GetText("common_time_h")
  local strDay = GM.GameTextModel:GetText("common_time_d")
  local day = math.floor(second / 86400)
  local hour = math.floor(second % 86400 / 3600)
  local min = math.floor(second % 3600 / 60)
  local sec = second % 60
  local numT = {
    day,
    hour,
    min,
    sec
  }
  local strT = {
    day .. strDay,
    hour .. strHour,
    min .. strMin,
    sec .. strSec
  }
  local startIndex = #numT
  for i = 1, #numT do
    if 0 < numT[i] or bForceNum and num > #numT - i then
      startIndex = i
      break
    end
  end
  local result = ""
  for i = startIndex, #strT do
    if num <= 0 then
      break
    end
    if 0 < numT[i] or not bIgnoreZero then
      result = result .. (#result == 0 and "" or " ") .. strT[i]
    end
    num = num - 1
  end
  if #result == 0 then
    result = strT[#strT]
  end
  return result
end

function TimeUtil.GetTimeSecondsFromStr(timeStr)
  local mtab = {
    JAN = 1,
    FEB = 2,
    MAR = 3,
    APR = 4,
    MAY = 5,
    JUN = 6,
    JUL = 7,
    AUG = 8,
    SEP = 9,
    OCT = 10,
    NOV = 11,
    DEC = 12
  }
  local _weekday, _day, _monthAbbr, _year, _hour, _min, _sec = string.match(timeStr, "(%a+,) (%d+) (%a+) (%d+) (%d+):(%d+):(%d+)")
  if _weekday == nil or _day == nil or _monthAbbr == nil or _year == nil or _hour == nil or _min == nil or _sec == nil then
    return 0
  end
  local _month = mtab[string.upper(_monthAbbr)]
  local timestamp = os.time({
    year = _year,
    month = _month,
    day = _day,
    hour = _hour,
    min = _min,
    sec = _sec,
    isdst = false
  })
  return timestamp
end

local getTimeZone = function()
  local now = os.time()
  local timeZone = os.difftime(now, os.time(os.date("!*t", now)))
  if os.date("*t", os.time()).isdst then
    timeZone = timeZone + 3600
  end
  return math.ceil(timeZone)
end
TimeUtil.TimeZone = getTimeZone()

function TimeUtil.ToTimestamp(strTimeInYYYYMMDDHHMMSS)
  local _, _, year, month, day, hour, min, sec = string.find(strTimeInYYYYMMDDHHMMSS, "(%d%d%d%d)%-(%d%d)%-(%d%d)[%a%s](%d%d):(%d%d):(%d%d)")
  local ok, result = SafeCall(function()
    return os.time({
      year = year,
      month = month,
      day = day,
      hour = hour,
      min = min,
      sec = sec,
      isdst = false
    }) + getTimeZone()
  end)
  if ok then
    return result
  else
    local localTimeError = ""
    local isOk, localTime = SafeCall(function()
      return os.time()
    end, function(err)
      localTimeError = err
    end)
    if isOk then
      localTimeError = localTime
    end
    local times = {
      year,
      month,
      day,
      hour,
      min,
      sec
    }
    local timeStrData = {}
    local timeBytesData = {}
    for i = 1, #times do
      timeStrData[i] = tostring(times[i])
      if times[i] then
        timeBytesData[i] = table.concat({
          string.byte(times[i], 1, #times[i])
        }, "_")
      end
    end
    local msg = strTimeInYYYYMMDDHHMMSS .. ";localTime:" .. localTimeError .. ";timeParse:" .. table.concat(timeStrData, ",") .. ";timeBytes:" .. table.concat(timeBytesData, ",")
    GM.BIManager:LogErrorInfo("timeErr", msg)
    return 0
  end
end

function TimeUtil.ToDayofWeek(timeStamp)
  return (timeStamp // Sec2Day + 3) % 7 + 1
end

function TimeUtil.ToCertainDayofWeekTimeStamp(timeStamp, dayofWeek)
  local curDayofWeek = TimeUtil.ToDayofWeek(timeStamp)
  local curzeroTime = timeStamp // Sec2Day * Sec2Day
  return curzeroTime + Sec2Day * (dayofWeek - curDayofWeek)
end
