LevelModel = {}
LevelModel.__index = LevelModel

function LevelModel:Init()
  self.m_curLevel = GM.UserModel:GetInNumber(EUserSyncDataKey.UserLevel)
  if self.m_curLevel <= 0 then
    self.m_curLevel = 1
    GM.UserModel:Set(EUserSyncDataKey.UserLevel, self.m_curLevel)
  end
  CSFirebaseManager:CrashlyticsSetKeyValue("level", self:GetCurrentLevel())
end

function LevelModel:OnSceneViewLoaded()
  self:TryLevelUp()
end

function LevelModel:TryLevelUp()
  if self:CanLevelUp() then
    self:DoLevelUp()
  end
end

function LevelModel:GetCurrentLevel()
  return self.m_curLevel
end

function LevelModel:GetCurrentLevelConfig()
  return LevelConfig[self.m_curLevel]
end

function LevelModel:SyncLevel(level)
  self.m_curLevel = level
  if not self.m_curLevel or self.m_curLevel <= 0 then
    self.m_curLevel = 1
  end
  Log.Info("LevelModel sync level to " .. tostring(self.m_curLevel))
  GM.UserModel:Set(EUserSyncDataKey.UserLevel, self.m_curLevel)
end

function LevelModel:IfReachMaxLevel(curLevel)
  if not LevelConfig then
    return true
  end
  curLevel = curLevel == nil and self:GetCurrentLevel() or curLevel
  return curLevel >= #LevelConfig
end

function LevelModel:CanLevelUp()
  if self:IfReachMaxLevel(self:GetCurrentLevel()) then
    return false
  end
  return GM.PropertyDataManager:GetPropertyNum(EPropertyType.Experience) >= self:GetLevelUpCost()
end

function LevelModel:GetLevelUpCost(uCurLevel)
  if not LevelConfig then
    return -1
  end
  uCurLevel = uCurLevel or self:GetCurrentLevel()
  if 1 <= uCurLevel and uCurLevel <= #LevelConfig then
    return LevelConfig[uCurLevel].Cost
  end
  return 0
end

function LevelModel:GetLevelUpRewards(uCurLevel)
  uCurLevel = uCurLevel or self:GetCurrentLevel()
  if 1 <= uCurLevel and uCurLevel <= #LevelConfig then
    return LevelConfig[uCurLevel].Rewards
  end
  return nil
end

function LevelModel:DoLevelUp()
  Log.Assert(self:CanLevelUp(), "LevelModel:DoLevelUp")
  while self:CanLevelUp() and GM.PropertyDataManager:Consume(EPropertyType.Experience, self:GetLevelUpCost(), EBIType.LevelUp, false, self.m_curLevel) do
    local arrRewards = self:GetLevelUpRewards()
    RewardApi.CryptRewards(arrRewards, true)
    self.m_curLevel = self.m_curLevel + 1
    GM.UserModel:Set(EUserSyncDataKey.UserLevel, self.m_curLevel)
    CSFirebaseManager:LogLevelUpEvent(self.m_curLevel)
    GM.OperBIManager:TrackLevelUpEvent(self.m_curLevel)
    GM.BIManager:LogAction(EBIType.LevelUp, self.m_curLevel)
    EventDispatcher.DispatchEvent(EEventType.LevelUp, self.m_curLevel)
  end
  GM.GameModel:Login()
  EventDispatcher.DispatchEvent(EEventType.RefreshSettingStrongTip)
end
