BakeOutReadyWindow = setmetatable({}, BaseWindow)
BakeOutReadyWindow.__index = BakeOutReadyWindow

function BakeOutReadyWindow:BeforeOpenCheck()
  local model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  local open = model ~= nil and model:IsBakeOutModeOn()
  if not open then
    model:SetWindowReopen()
  end
  return open
end

function BakeOutReadyWindow:Init()
  self.m_model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  self.m_role:Init()
  self.m_role:PlayEnterAnimation()
  self:LogWindowAction(EBIType.UIActionType.Open, EBIReferType.AutoPopup, self.m_model:GetId())
end

function BakeOutReadyWindow:OnCloseBtnClick()
  BaseWindow.OnCloseBtnClick(self)
  if not self.m_model:IsEnlist() and self.m_model:CanEnlist() then
    self.m_model:OnEnlist(true)
  end
end
