EnergyBoostButton = setmetatable({}, HudGeneralButton)
EnergyBoostButton.__index = EnergyBoostButton
local NUM_OUTLINE_COLOR = {
  [1] = "963c1e",
  [2] = "114E98",
  [4] = "921277"
}
local COUNTDOWN_OUTLINE_COLOR = {
  [1] = "bd4b0d",
  [2] = "114E98",
  [4] = "aa29d4"
}

function EnergyBoostButton:Awake()
  self.m_model = GM.EnergyBoostModel
  HudGeneralButton.Awake(self)
  EventDispatcher.AddListener(EEventType.EnergyBoostModeChanged, self, self.UpdateContent)
  self:UpdateContent()
end

function EnergyBoostButton:UpdateContent()
  self:OnGameModeChanged()
  self:_UpdateUI()
  self.m_countdownGo:SetActive(GM.EnergyBoostModel:IsTimeLimitedOn())
  self:UpdatePerSecond()
end

function EnergyBoostButton:UpdatePerSecond()
  self.m_countdownText.text = TimeUtil.ParseTimeDescription(GM.EnergyBoostModel:GetTimeLimitedLeftTime(), nil, nil, false)
end

function EnergyBoostButton:_UpdateUI()
  self.m_numText.text = self.m_model:GetEnergyBoostCostNum()
  local nCostNum = self.m_model:GetEnergyBoostCostNum()
  self.m_iconImg.sprite = self["m_bgSpritex" .. nCostNum]
  self.m_countdownBgImg.sprite = self["m_countdownSpritex" .. nCostNum]
  self.m_numOutline:SetColor(UIUtil.ConvertHexColor2CSColor(NUM_OUTLINE_COLOR[nCostNum]))
  self.m_countdownOutline:SetColor(UIUtil.ConvertHexColor2CSColor(COUNTDOWN_OUTLINE_COLOR[nCostNum]))
end

function EnergyBoostButton:_NeedDisplay()
  return HudGeneralButton._NeedDisplay(self) and self.m_model:IsEnergyBoostConfigOn()
end

function EnergyBoostButton:OnBtnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.EnergyBoostSettingWindow)
end
