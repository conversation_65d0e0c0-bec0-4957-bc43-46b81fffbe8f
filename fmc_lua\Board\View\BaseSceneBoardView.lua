BaseSceneBoardView = setmetatable({CompleteTileColor = "FFFFFF", finishOrderRemoveItemDt = 0.5}, BaseInteractiveBoardView)
BaseSceneBoardView.__index = BaseSceneBoardView
local REGISTER_TOUCH_HANDLER = function(view, eventName)
  local triggerEventName = "OnLua" .. eventName
  local functionName = "_On" .. eventName
  view.m_eventTrigger[triggerEventName] = function(eventData)
    view[functionName](view, eventData)
  end
end

function BaseSceneBoardView:Init(boardModel)
  self.CompleteTileColor = UIUtil.ConvertHexColor2CSColor(self.CompleteTileColor)
  self.m_completeTileMap = boardModel:CreateMatrix()
  self.m_camera = GM.ModeViewController:GetBoardInfo()
  self.m_canvas.worldCamera = self.m_camera
  BaseInteractiveBoardView.Init(self, boardModel)
  self.m_infoBar:Init(boardModel)
  self.m_boardCookBubble:Init()
  EventDispatcher.AddListener(EEventType.OrderStateChanged, self, self.OnOrderStateChanged)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnGameModeChanged)
  EventDispatcher.AddActiveListener(EEventType.ItemFlyChanged, self, self.OnOrderStateChanged)
  EventDispatcher.AddActiveListener(EEventType.ShopRefreshed, self, self._OnShopRefreshed)
  EventDispatcher.AddActiveListener(EEventType.OrderAnimationFinished, self, self.OnOrderAnimationFinished)
  EventDispatcher.AddActiveListener(EEventType.ItemCookStarted, self, self._OnItemCookStarted)
  EventDispatcher.AddActiveListener(EEventType.ItemCookEnded, self, self._OnItemCookEnded)
  REGISTER_TOUCH_HANDLER(self, "PointerDown")
  REGISTER_TOUCH_HANDLER(self, "Drag")
  REGISTER_TOUCH_HANDLER(self, "PointerUp")
  REGISTER_BOARD_EVENT_HANDLER(self, "FinishOrder")
end

function BaseSceneBoardView:OnEnable()
  if not self.m_adjustedInfoBarPosition then
    local sceneView = GM.UIManager:GetOpenedTopViewByType(EViewType.SceneView)
    if sceneView ~= nil and self.m_camera ~= nil then
      self.m_adjustedInfoBarPosition = true
      local screenPosition = sceneView:GetBoardInfoBarScreenPosition()
      local worldPosition = self:ConvertScreenPositionToWorldPosition(screenPosition)
      local transform = self.m_infoBar.gameObject.transform
      local localPosition = transform.parent:InverseTransformPoint(worldPosition)
      localPosition.z = 0
      transform.localPosition = localPosition
    end
  end
  self:OnOrderStateChanged()
end

function BaseSceneBoardView:GetCanvas()
  return self.m_canvas
end

function BaseSceneBoardView:GetCamera()
  return self.m_camera
end

function BaseSceneBoardView:UpdateBoardInfoBar(itemModel, inBoard)
  BaseInteractiveBoardView.UpdateBoardInfoBar(self, itemModel, inBoard)
  if self.m_infoBar == nil or self.m_infoBar.gameObject:IsNull() then
    return
  end
  if inBoard == nil then
    inBoard = true
  end
  self.m_boardInfoBarItem = itemModel
  local cookTip = inBoard and self:_UpdateCookTip() or {}
  self.m_infoBar:UpdateInfoBar(itemModel, self.m_bShowCookStartTip, inBoard, cookTip)
  EventDispatcher.DispatchEvent(EEventType.OrderItemCookPopTip, {tip = false})
  if self.m_boardInfoBarItem ~= nil and inBoard ~= false and self.m_boardInfoBarItem:IsInBoard() then
    local selectedCode = self.m_boardInfoBarItem:GetCode()
    local directDishLack, indirectDishLack, lackDishMaterials, lackDishMat2Dish = self:CalculateOrderDishsStatus()
    if lackDishMaterials[selectedCode] then
      self:_CalOrderItemCookPopTip(selectedCode, directDishLack, indirectDishLack, lackDishMat2Dish)
    end
  end
end

function BaseSceneBoardView:GetBoardInfoBarItem()
  return self.m_boardInfoBarItem
end

function BaseSceneBoardView:OnDestroy()
  BaseInteractiveBoardView.OnDestroy(self)
  EventDispatcher.RemoveTarget(self)
end

function BaseSceneBoardView:LateUpdate()
  BaseInteractiveBoardView.LateUpdate(self)
  if self.m_model == nil then
    return
  end
  self.m_frameCache = nil
  self:_RealUpdateOrderState()
end

function BaseSceneBoardView:GetInfoBar()
  return self.m_infoBar
end

function BaseSceneBoardView:GetOrderArea()
  return self.m_orderArea
end

function BaseSceneBoardView:GetInventoryButton()
  local baseSceneView = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BaseSceneView)
  return baseSceneView:GetHudButton(ESceneViewHudButtonKey.Inventory)
end

function BaseSceneBoardView:ConvertWorldPositionToScreenPosition(position)
  return self.m_camera:WorldToScreenPoint(position)
end

function BaseSceneBoardView:ConvertScreenPositionToWorldPosition(position)
  return self.m_camera:ScreenToWorldPoint(position)
end

function BaseSceneBoardView:ConvertBoardPositionToScreenPosition(boardPosition)
  local localPosition = boardPosition:ToLocalPosition()
  return self.m_camera:WorldToScreenPoint(self:GetItemsTransform():TransformPoint(Vector3(localPosition.x + BaseBoardModel.TileSize / 2, localPosition.y + BaseBoardModel.TileSize / 2, 0)))
end

function BaseSceneBoardView:_RemoveItemView(itemView)
  if itemView and self.m_lastTouchedItem == itemView and self.m_onInventory then
    self:GetInventoryButton():PlayDragEndEffect()
    self.m_onInventory = false
  end
  BaseInteractiveBoardView._RemoveItemView(self, itemView)
end

function BaseSceneBoardView:_AddItemView(itemModel)
  local itemView = BaseInteractiveBoardView._AddItemView(self, itemModel)
  if self.m_initFinished then
    DelayExecuteFuncInView(function()
      self:OnOrderStateChanged()
    end, 0.01, self)
  end
  EventDispatcher.DispatchEvent(EEventType.ItemViewAdded, {item = itemModel})
  return itemView
end

function BaseSceneBoardView:_GetInBoardOrderRequirement()
  local cacheKey = "_GetInBoardOrderRequirement"
  local cache = self.m_frameCache ~= nil and self.m_frameCache[cacheKey] or nil
  if cache ~= nil and #cache == 2 then
    return cache[1], cache[2]
  end
  local orders = self.m_orderArea:GetInBoardOrders()
  local requireItems = {}
  local dishRequire = {}
  for _, order in ipairs(orders) do
    if order:GetState() ~= OrderState.Finished then
      for _, require in ipairs(order:GetRequirements()) do
        requireItems[require] = (requireItems[require] or 0) + 1
        if GM.ItemDataModel:IsDishes(require) then
          dishRequire[require] = (dishRequire[require] or 0) + 1
        end
      end
    end
  end
  if self.m_frameCache == nil then
    self.m_frameCache = {}
  end
  self.m_frameCache[cacheKey] = {requireItems, dishRequire}
  return requireItems, dishRequire
end

function BaseSceneBoardView:_GetInBoardCodeCountMap()
  local cacheKey = "_GetInBoardCodeCountMap"
  local cache = self.m_frameCache ~= nil and self.m_frameCache[cacheKey] or nil
  if cache ~= nil and #cache == 2 then
    return cache[1], cache[2]
  end
  local orderRequireItems, _ = self:_GetInBoardOrderRequirement()
  local codeCountMap = self.m_model:GetCodeCountMap(true, false, true)
  local inBoardCodeCountMap = {}
  for key, _ in pairs(orderRequireItems) do
    inBoardCodeCountMap[key] = codeCountMap[key]
  end
  local itemModel, itemView, itemCode
  local hasFlyingOrderItem = false
  for itemModel, _ in pairs(self.m_model:GetAllBoardItems()) do
    if inBoardCodeCountMap[itemModel:GetCode()] ~= nil then
      itemView = itemModel ~= nil and self:GetItemView(itemModel) or nil
      if itemView == nil or itemView:IsFlying() then
        itemCode = itemModel:GetCode()
        if orderRequireItems[itemCode] then
          hasFlyingOrderItem = true
        end
        inBoardCodeCountMap[itemCode] = inBoardCodeCountMap[itemCode] - 1
        if inBoardCodeCountMap[itemCode] <= 0 then
          inBoardCodeCountMap[itemCode] = nil
        end
      end
    end
  end
  if self.m_frameCache == nil then
    self.m_frameCache = {}
  end
  self.m_frameCache[cacheKey] = {inBoardCodeCountMap, hasFlyingOrderItem}
  return inBoardCodeCountMap, hasFlyingOrderItem
end

function BaseSceneBoardView:CalculateOrderDishsStatus()
  local status = self.m_model:GetUnfilledOrderRequirementsConsiderView()
  return status.directDishLack, status.indirectDishLack, status.lackDishMaterials, status.lackDishMat2Dish
end

function BaseSceneBoardView:_AddTile(boardPosition)
  BaseInteractiveBoardView._AddTile(self, boardPosition)
  local tileObject = Object.Instantiate(self.m_tilePrefab, self.m_tilesTransform)
  local tileSprite = tileObject:GetComponent(typeof(SpriteRenderer))
  SpriteUtil.SetSpriteRenderer(tileSprite, ImageFileConfigName.game_done_bg)
  self.m_completeTileMap:SetValueOnPosition(boardPosition, {Sprite = tileSprite})
  local localPosition = boardPosition:ToLocalPosition()
  tileObject.transform.localPosition = Vector3(localPosition.x + BaseBoardModel.TileSize / 2, localPosition.y + BaseBoardModel.TileSize / 2, -1)
end

function BaseSceneBoardView:UpdateCompleteTileDisplay(position, show, imageConfig)
  if self.m_completeTileMap:GetValueOnPosition(position).Show ~= show then
    self.m_completeTileMap:GetValueOnPosition(position).Show = show
    local sprite = self.m_completeTileMap:GetValueOnPosition(position).Sprite
    if show then
      SpriteUtil.SetSpriteRenderer(sprite, imageConfig)
    end
    sprite:DOKill()
    sprite:DOFade(show and 1 or 0, 0.2)
  elseif show then
    local sprite = self.m_completeTileMap:GetValueOnPosition(position).Sprite
    SpriteUtil.SetSpriteRenderer(sprite, imageConfig)
  end
end

function BaseSceneBoardView:_PlayRemoveOrderItemAnimation(targetCell, index, itemModel, moveDuration)
  self:_SetItemViewToBeRemoved(itemModel)
  local targetIcon = targetCell:GetIcon(index)
  local targetPosition = targetIcon.transform.position
  targetPosition = Vector3(targetPosition.x, targetPosition.y, 0)
  PropertyAnimationManager.AddFlyingCount()
  local itemView = self:GetItemView(itemModel)
  itemView:SetFlying(true)
  itemView:TryStopJumpTween()
  local transform = itemView.transform
  local position = transform.position
  position = Vector3(position.x, position.y, 0)
  transform.position = position
  local sequence = DOTween.Sequence()
  sequence:Insert(0, transform:DOMove(targetPosition, moveDuration):SetEase(Ease.OutQuad))
  sequence:Insert(0, transform:DOScale(1.8, moveDuration * 0.7))
  sequence:Insert(moveDuration * 0.7, transform:DOScale(0.8, moveDuration * 0.3))
  sequence:OnComplete(function()
    self:_RemoveItemView(itemView)
    PropertyAnimationManager.RemoveFlyingCount()
    PlatformInterface.Vibrate(EVibrationType.Light)
  end)
end

function BaseSceneBoardView:DirectRemoveOrderItem(itemModel)
  if self.m_selectedBoardPosition == itemModel:GetPosition() then
    self:_UpdateIndicator()
    self:UpdateBoardInfoBar()
  end
  local itemView = self:GetItemView(itemModel)
  self:_RemoveItemView(itemView)
end

function BaseSceneBoardView:_CanStartPrompt()
  return BaseInteractiveBoardView._CanStartPrompt(self) and GM.SceneManager:GetGameMode() == self.m_model:GetGameMode() and GM.UIManager.allWindowClosed and (not self.m_orderArea:IsPlayingOrderAnimation() or IsAutoRun())
end

function BaseSceneBoardView:_GetBoardPrompts()
  local prompts = {
    BoardPromptFinishOrder.Create(),
    BoardPromptLastOrder.Create(),
    BoardPromptMergeItems.Create(),
    BoardPromptTapCacheItems.Create(),
    BoardPromptTapSpreadItem.Create()
  }
  if not IsAutoRun() or not GM.TestAutoRunModel.ignoreBuild then
    table.insert(prompts, BoardPromptFinishTask.Create())
  end
  if IsAutoRun() then
    table.insert(prompts, BoardPromptTestSpreadItem.Create())
    table.insert(prompts, BoardPromptRecoverSpread.Create())
    table.insert(prompts, BoardPromptOpenChest.Create())
    table.insert(prompts, BoardPromptCollectAll.Create())
    table.insert(prompts, BoardPromptTakeOutDish.Create())
    table.insert(prompts, BoardPromptStartCook.Create())
    table.insert(prompts, BoardPromptTakeOutMaterial.Create())
    table.insert(prompts, BoardPromptStoreItem.Create())
    table.insert(prompts, BoardPromptRetrieveItem.Create())
  end
  return prompts
end

function BaseSceneBoardView:_SelectPrompt(prompts)
  local promptConfig = self.m_model:GetPromptConfig()
  local comparer = function(prompt1, prompt2)
    local priority1 = promptConfig[prompt1:GetType()].Priority
    local priority2 = promptConfig[prompt2:GetType()].Priority
    return priority1 < priority2
  end
  table.sort(prompts, comparer)
  for _, prompt in ipairs(prompts) do
    if prompt:IsOpen(promptConfig) and prompt:CanStart(self) then
      return prompt
    end
  end
  return nil
end

function BaseSceneBoardView:_StartBoardPrompt()
  if self.m_bShowCookStartTip then
    self:_CancelBoardPrompt()
    self:ShowCookStartTip()
  else
    BaseInteractiveBoardView._StartBoardPrompt(self)
  end
end

function BaseSceneBoardView:_GetPromptInterval()
  if IsAutoRun() then
    return GM.TestAutoRunModel.interval
  else
    return BaseInteractiveBoardView._GetPromptInterval(self)
  end
end

function BaseSceneBoardView:_ExecuteBoardPrompt()
  BaseInteractiveBoardView._ExecuteBoardPrompt(self)
  if IsAutoRun() then
    if self.m_boardPrompt == nil then
      GM.TestAutoRunModel:AddInterval()
      self:TryStartBoardPrompt()
    else
      self.m_boardPrompt:AutoDo(self)
      GM.TestAutoRunModel:ResetInterval()
    end
  end
end

function BaseSceneBoardView:_CancelBoardPrompt()
  BaseInteractiveBoardView._CancelBoardPrompt(self)
  self:HideCookStartTip()
end

function BaseSceneBoardView:OnOrderAnimationFinished()
  self:_UpdateOrderState()
  self:TryStartBoardPrompt()
end

function BaseSceneBoardView:_OnShopRefreshed(msg)
  if msg and msg.shopType == EShopType.FlashSale then
    self:OnOrderStateChanged()
  end
end

function BaseSceneBoardView:OnOrderStateChanged()
  self:_UpdateOrderState()
  self:TryStartBoardPrompt()
end

function BaseSceneBoardView:_UpdateOrderState()
  self.m_bNeedUpdateOrderState = true
end

function BaseSceneBoardView:_RealUpdateOrderState()
  if not self.m_bNeedUpdateOrderState then
    return
  end
  self.m_bNeedUpdateOrderState = nil
  local inboardCodeCountMap, hasFlyingOrderItem = self:_GetInBoardCodeCountMap()
  local directDishLack, _, lackDishMaterials = self:CalculateOrderDishsStatus()
  self.m_orderArea:OnOrderStateChanged(inboardCodeCountMap, hasFlyingOrderItem, directDishLack)
  self:_UpdateTileOrderTip(lackDishMaterials)
end

EItemOrderTipType = {
  None = 0,
  IsMaterial = 1,
  PartiallyFinished = 2,
  CanDeliver = 3
}

function BaseSceneBoardView:_GetItemOrderTip(item, lackDishMaterials)
  if item:GetComponent(ItemLocker) ~= nil then
    return
  end
  local itemType = item:GetCode()
  local tip = EItemOrderTipType.None
  local orders, states = self.m_orderArea:GetInBoardOrders()
  local curTip = EItemOrderTipType.None
  for i, order in ipairs(orders) do
    local requirements = order:GetRequirements()
    local index = Table.ListContain(requirements, itemType)
    if index then
      local orderState = states[i]
      if orderState == OrderState.CanDeliver then
        curTip = EItemOrderTipType.CanDeliver
      elseif orderState == OrderState.PartiallyFinished then
        curTip = EItemOrderTipType.PartiallyFinished
      end
    elseif lackDishMaterials[itemType] ~= nil then
      curTip = EItemOrderTipType.IsMaterial
    end
    if tip < curTip then
      tip = curTip
    end
  end
  if tip ~= EItemOrderTipType.None and GM.ItemDataModel:IsDisposableInstrument(itemType) and not item:IsEmptyInstrument() then
    tip = EItemOrderTipType.None
  end
  return tip
end

function BaseSceneBoardView:_UpdateTileOrderTip(lackDishMaterials)
  self.m_promptIgnoreItems = {}
  local itemDataModel = GM.ItemDataModel
  local mapRequirements = {}
  local orders, states = self.m_orderArea:GetInBoardOrders()
  local curTip = EItemOrderTipType.None
  for i, order in ipairs(orders) do
    local requirements = order:GetRequirements()
    for _, requirement in ipairs(requirements) do
      mapRequirements[requirement] = (mapRequirements[requirement] or 0) + 1
    end
  end
  local isAutoRun = IsAutoRun()
  if isAutoRun then
    local directResult, indirectResult, filledResult = self.m_model:GetUnfilledOrderRequirementsSeparately(true)
    mapRequirements = Table.ShallowCopy(filledResult)
  end
  local itemModel, itemView, itemCook, tip, position
  for y = 1, self.m_model:GetVerticalTiles() do
    for x = 1, self.m_model:GetHorizontalTiles() do
      position = self.m_model:CreatePosition(x, y)
      itemModel = self.m_model:GetItem(position)
      itemView = itemModel ~= nil and self:GetItemView(itemModel) or nil
      itemCook = itemModel ~= nil and itemModel:GetComponent(ItemCook) or nil
      tip = itemModel ~= nil and self:_GetItemOrderTip(itemModel, lackDishMaterials) or EItemOrderTipType.None
      if itemView == nil or itemView:IsFlying() or itemView:IsDragging() then
        self:UpdateCompleteTileDisplay(position, false)
        if itemModel ~= nil then
          self.m_promptIgnoreItems[itemModel] = true
        end
      elseif itemCook ~= nil and itemCook:GetState() == EItemCookState.Cooked then
        self:UpdateCompleteTileDisplay(position, true, ImageFileConfigName.game_done_bg2)
      elseif tip == EItemOrderTipType.CanDeliver or tip == EItemOrderTipType.PartiallyFinished or tip == EItemOrderTipType.IsMaterial then
        local bgImg = tip == EItemOrderTipType.CanDeliver and ImageFileConfigName.game_done_bg or ImageFileConfigName.game_done_bg3
        self:UpdateCompleteTileDisplay(position, true, bgImg)
        local itemCode = itemModel and itemModel:GetCode()
        if isAutoRun then
          if mapRequirements[itemCode] then
            self.m_promptIgnoreItems[itemModel] = true
            mapRequirements[itemCode] = mapRequirements[itemCode] - 1
            if mapRequirements[itemCode] <= 0 then
              mapRequirements[itemCode] = nil
            end
          end
        elseif tip == EItemOrderTipType.IsMaterial and lackDishMaterials[itemCode] and 0 < lackDishMaterials[itemCode] then
          self.m_promptIgnoreItems[itemModel] = true
          lackDishMaterials[itemCode] = lackDishMaterials[itemCode] - 1
        elseif mapRequirements[itemCode] then
          self.m_promptIgnoreItems[itemModel] = true
          mapRequirements[itemCode] = mapRequirements[itemCode] - 1
          if mapRequirements[itemCode] <= 0 then
            mapRequirements[itemCode] = nil
          end
        end
      else
        if itemModel and GM.ItemDataModel:GetChainId(itemModel:GetType()) == ItemChain.AddItem then
          self.m_promptIgnoreItems[itemModel] = true
        end
        self:UpdateCompleteTileDisplay(position, false)
      end
      if itemView then
        local code = itemModel:GetCode()
        local show = not itemView:IsDragging() and (tip == EItemOrderTipType.CanDeliver or tip == EItemOrderTipType.PartiallyFinished)
        itemView:UpdateChecked(show)
      end
    end
  end
end

function BaseSceneBoardView:GetPromptIgnoreItems()
  return self.m_promptIgnoreItems
end

function BaseSceneBoardView:_OnOpenView()
  BaseInteractiveBoardView._OnOpenView(self)
  if not GM.UIManager.allWindowClosed then
    self:_CancelBoardPrompt()
  end
end

function BaseSceneBoardView:NeedScroll2OrderGroup()
  if not GM.ConfigModel:IsOrderSequenceByEnergyDiff() then
    return false
  end
  if not self.m_model:GetOrderModel():IsOrderGroupButtonVisible() then
    self.m_bAlreadyScrollToOrder = true
    return false
  end
  return not self.m_bAlreadyScrollToOrder
end

function BaseSceneBoardView:_OnGameModeChanged()
  if GM.SceneManager:GetGameMode() == self.m_model:GetGameMode() then
    self:_UpdateIndicator()
    self:UpdateBoardInfoBar()
    self:TryStartBoardPrompt()
    local boardCacheRoot = self.m_orderArea:GetBoardCacheRoot()
    local bScrollToCacheRoot = boardCacheRoot:CanScrollToCacheRoot()
    if bScrollToCacheRoot and boardCacheRoot ~= nil then
      DelayExecuteFuncInView(function()
        boardCacheRoot:TryScrollToCacheRoot()
      end, 0.1, self)
    elseif self:NeedScroll2OrderGroup() then
      DelayExecuteFuncInView(function()
        if GM.TutorialModel:HasAnyStrongTutorialOngoing() then
          return
        end
        self.m_orderArea:AnimToOrderGroup()
      end, 0.3, self)
    elseif not GM.ConfigModel:IsOrderSequenceByEnergyDiff() then
      self.m_orderArea:ScrollToFront()
    end
    self.m_bAlreadyScrollToOrder = true
  else
    self.m_orderArea:ResetToOrderGroupAnim()
    self:_CancelBoardPrompt()
    if self.m_lastTouchedItem ~= nil then
      self:_ClearPointerDataOnUnexpectedPointerExit()
    end
  end
end

function BaseSceneBoardView:_ClearPointerDataOnUnexpectedPointerExit()
  BaseInteractiveBoardView._ClearPointerDataOnUnexpectedPointerExit(self)
  self.m_eventTrigger:ResetPressedState()
  if self.m_onInventory then
    self:GetInventoryButton():PlayDragEndEffect()
    self.m_onInventory = false
  end
end

function BaseSceneBoardView:_OnDrag(eventData)
  BaseInteractiveBoardView._OnDrag(self, eventData)
  if self.m_lastTouchedItem == nil or not self.m_model:CanItemMove(self.m_lastTouchedItem:GetModel()) then
    return
  end
  if eventData.pointerCurrentRaycast.gameObject and eventData.pointerCurrentRaycast.gameObject.name == "Inventory" then
    if not self.m_onInventory then
      self.m_onInventory = true
      self:GetInventoryButton():PlayDragStartEffect()
    end
  elseif self.m_onInventory then
    self:GetInventoryButton():PlayDragEndEffect()
    self.m_onInventory = false
  end
end

function BaseSceneBoardView:_OnDragBegin()
  BaseInteractiveBoardView._OnDragBegin(self)
  EventDispatcher.DispatchEvent(EEventType.OrderItemCookPopTip, {tip = false})
  self:UpdateCompleteTileDisplay(self.m_lastTouchedItem:GetModel():GetPosition(), false)
end

function BaseSceneBoardView:_OnPointerUp(eventData)
  BaseInteractiveBoardView._OnPointerUp(self, eventData)
  if self.m_onInventory then
    self:GetInventoryButton():PlayDragEndEffect()
    self.m_onInventory = false
  end
end

function BaseSceneBoardView:_OnDragEnd(itemModel, targetBoardPosition, eventData)
  if eventData.pointerCurrentRaycast.gameObject ~= nil and eventData.pointerCurrentRaycast.gameObject.name == "Inventory" then
    local stored = false
    if self.m_model:StoreItem(itemModel) then
      self:GetInventoryButton():PlayItemInEffect()
      GM.AudioModel:StopCookEffect()
      stored = true
    end
    if not stored then
      self.m_model:DragItem(itemModel, targetBoardPosition)
      self:_UpdateOrderState()
    end
  else
    self.m_model:DragItem(itemModel, targetBoardPosition)
    if self.m_model:GetItem(itemModel:GetPosition()) == itemModel then
      self.m_selectedBoardPosition = itemModel:GetPosition()
      self:_UpdateOrderState()
    else
      self.m_selectedBoardPosition = targetBoardPosition
    end
  end
end

function BaseSceneBoardView:_OnMultiPointerUpEnd(itemModel)
  if self.m_infoBar:GetOpenBtnGo().activeInHierarchy then
    self.m_infoBar:GetInfoContent():OnOpenButtonClicked()
  elseif self.m_infoBar:GetActivateBtnGo().activeInHierarchy then
    self.m_infoBar:GetInfoContent():OnActivateButtonClicked()
  else
    BaseInteractiveBoardView._OnMultiPointerUpEnd(self, itemModel)
  end
end

function BaseSceneBoardView:_OnPopCachedItem(message)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPlaceItem)
  local itemView = self:_AddItemView(message.New)
  if itemView ~= nil then
    local worldPosition = self.m_orderArea:GetBoardCacheRoot():GetFlyTargetPosition()
    local sourcePosition = self.transform:InverseTransformPoint(worldPosition)
    local targetPosition = itemView.transform.localPosition
    local sourcePositionZero = Vector3(sourcePosition.x, sourcePosition.y, 0)
    local targetPositionZero = Vector3(targetPosition.x, targetPosition.y, 0)
    local transform = itemView.gameObject.transform
    transform.localPosition = sourcePositionZero
    itemView:SetFlying(true)
    local sequence = DOTween.Sequence()
    sequence:Append(transform:DOScale(0.6, 0.1))
    sequence:Append(transform:DOScale(1, 0.15))
    sequence:Append(transform:DOScale(2.2, 0.3))
    sequence:Append(transform:DOScale(1, 0.3))
    sequence:Append(transform:DOScale(0.7, 0.1))
    sequence:Append(transform:DOScale(1, 0.15))
    sequence:Insert(0.25, transform:DOLocalMove(targetPositionZero, 0.6))
    sequence:InsertCallback(0.9, function()
      transform.localPosition = targetPosition
      itemView:ShowSpreadLight()
    end)
    itemView:SetJumpTween(sequence, function()
      itemView:SetFlying(false)
    end)
  end
end

function BaseSceneBoardView:_RemoveOrderItemWhenFinishOrder(message, targetCell)
  for i, itemModel in pairs(message.RemoveItemInfo.RemovedFromBoard) do
    local index = message.RemoveItemInfo.RemovedCellIndexFromBoard[i]
    self:_PlayRemoveOrderItemAnimation(targetCell, index, itemModel, self.finishOrderRemoveItemDt)
  end
end

function BaseSceneBoardView:_RemoveOrderCell(order)
  self.m_orderArea:RemoveCell(order)
  self.m_orderArea:SetScrollEnabled(true)
end

function BaseSceneBoardView:_TryFillNewOrder()
  self:OnOrderStateChanged()
  if not self.m_orderArea:IsPlayingOrderAnimation() then
    EventDispatcher.DispatchEvent(EEventType.OrderAnimationFinished)
  end
end

function BaseSceneBoardView:_OnFinishOrder(message)
  local playLinkAnim = GM.FlambeTimeModel:NeedPlayOrderLinkAnim(message.Order:GetId())
  if playLinkAnim then
    GM.UIManager:SetEventLock(true)
  end
  local useNewAnimation = GM.ConfigModel:UseNewOrderRewardAnimation()
  if useNewAnimation then
    GM.UIManager:SetEventLock(true)
  end
  self.m_orderArea:ForceRebuildLayout()
  self.m_orderArea:SetScrollEnabled(false)
  local targetCell = self.m_orderArea:GetCell(message.Order)
  local sequence = DOTween.Sequence()
  local fDelayTime = 0
  if targetCell:CanPlaySurpriseChestRewardAnim() then
    local animTime = targetCell:GetSurpriseChestRewardAnimTime()
    sequence:InsertCallback(fDelayTime, function()
      targetCell:PlaySurpriseChestRewardAnim()
    end)
    fDelayTime = fDelayTime + animTime
  end
  local order = targetCell:GetOrder()
  local rewards = message.Rewards
  if fDelayTime == 0 then
    self:_RemoveOrderItemWhenFinishOrder(message, targetCell)
  else
    for index, itemModel in ipairs(message.RemoveItemInfo.RemovedFromBoard) do
      self:_SetItemViewToBeRemoved(itemModel)
    end
    PropertyAnimationManager.AddFlyingCount()
    sequence:InsertCallback(fDelayTime, function()
      PropertyAnimationManager.RemoveFlyingCount()
      self:_RemoveOrderItemWhenFinishOrder(message, targetCell)
    end)
  end
  fDelayTime = fDelayTime + self.finishOrderRemoveItemDt
  sequence:AppendInterval(self.finishOrderRemoveItemDt)
  sequence:AppendCallback(function()
    targetCell:PlayCoinEffect()
    self:_PlayFinishRewardAnimation(rewards, targetCell, message.Order, playLinkAnim)
  end)
  return sequence
end

function BaseSceneBoardView:_PlayFinishRewardAnimation(rewards, targetCell, order, playLinkAnim)
  if GM.ConfigModel:UseNewOrderRewardAnimation() then
    self.m_orderArea:ToggleLayoutGroup(false)
    targetCell:PlayLeaveAnimation()
    local curNormalizedPosition = self.m_orderArea:GetNormalizedPosition()
    local funcTryOrderLeave = function(skipped)
      if not playLinkAnim then
        self.m_orderArea:ToggleLayoutGroup(true)
        self.m_orderArea:ForceRebuildLayout()
        return targetCell:PlayLeaveAnimation(function()
          self:_RemoveOrderCell(order)
          self:_TryFillNewOrder()
        end, skipped and 0 or nil)
      end
      return nil
    end
    local funcOnFinish = function()
      if playLinkAnim then
        self:_PlayFlambeLinkAnim(targetCell, order, function()
          local tempNormalizedPosition = self.m_orderArea:GetNormalizedPosition()
          self.m_orderArea:ScrollToNormalizedPosition(curNormalizedPosition, false)
          return tempNormalizedPosition
        end, function(tempNormalizedPosition)
          self.m_orderArea:ScrollToNormalizedPosition(tempNormalizedPosition, false)
          self.m_orderArea:ScrollToNormalizedPosition(curNormalizedPosition, true)
        end)
      end
    end
    local cellWidth = targetCell:GetCellWidth()
    local worldPosition = targetCell.transform.position
    GM.UIManager:SetEventLock(false)
    if playLinkAnim then
      GM.UIManager:SetEventLock(false)
    end
    GM.UIManager:OpenView(UIPrefabConfigName.OrderFinishRewardWindow, rewards, cellWidth, worldPosition, self.m_orderArea, funcTryOrderLeave, funcOnFinish, playLinkAnim)
  else
    local worldPosition = targetCell:GetIconArea().transform.position
    local screenPosition = self:ConvertWorldPositionToScreenPosition(worldPosition)
    local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
    local positionArray = Table.ListRep(uiWorldPosition, #rewards)
    local viewData = {arrWorldPos = positionArray, noDelayTime = true}
    RewardApi.AcquireRewardsInView(rewards, viewData)
    local orderGroupButton = self.m_orderArea:GetOrderGroupButton()
    orderGroupButton:FlyProgressFromPos(worldPosition)
    DelayExecuteFuncInView(function()
      if playLinkAnim then
        GM.UIManager:SetEventLock(false)
        self:_PlayFlambeLinkAnim(targetCell, order)
      else
        targetCell:PlayLeaveAnimation(function()
          self:_RemoveOrderCell(order)
          self:_TryFillNewOrder()
        end)
      end
    end, 0.2, self, false)
  end
end

function BaseSceneBoardView:_PlayFlambeLinkAnim(removeCell, order, onBeforeInit, onAfterInit)
  local onBeforeInitWarp = function()
    local retValue = onBeforeInit and onBeforeInit() or nil
    local removeCellWorldPos = removeCell.transform.position
    local removeCellScreenPos = self:ConvertWorldPositionToScreenPosition(removeCellWorldPos)
    local removeCellUIPos = PositionUtil.UICameraScreen2World(removeCellScreenPos)
    self.m_orderArea:ToggleLayoutGroup(false)
    removeCell:PlayLeaveAnimation()
    return removeCellUIPos, retValue
  end
  local removeCellFunc = function()
    self.m_orderArea:ToggleLayoutGroup(true)
    self.m_orderArea:ForceRebuildLayout()
    removeCell:PlayLeaveAnimation(function()
      self:_RemoveOrderCell(order)
    end)
  end
  local params = table.pack(removeCellFunc, function()
    self:_TryFillNewOrder()
  end, onBeforeInitWarp, onAfterInit)
  EventDispatcher.DispatchEvent(EEventType.FlambeTimePopup, params)
end

function BaseSceneBoardView:_UpdateCookTip()
  self.m_bShowCookStartTip = false
  local cookTip = {}
  if not self.m_boardInfoBarItem or self.m_boardInfoBarItem:GetComponent(ItemCook) == nil then
    return cookTip
  end
  local itemCook = self.m_boardInfoBarItem:GetComponent(ItemCook)
  local cookState = itemCook:GetState()
  local directDishLack, indirectDishLack, _, lackDishMat2Dish = self:CalculateOrderDishsStatus()
  if cookState == EItemCookState.CanCook then
    local recipe = itemCook:GetRecipe()
    local bDirectDishLack = directDishLack[recipe] ~= nil
    local bIndirectDishLack = indirectDishLack[recipe] ~= nil
    if bDirectDishLack or bIndirectDishLack then
      self.m_bShowCookStartTip = true
    end
    if not bDirectDishLack and not bIndirectDishLack then
      local directDishes, directNonDishes, indirectDishes, indirectNonDishes = self.m_model:GetOrderRequirements(true)
      if not directDishes[recipe] and not indirectDishes[recipe] then
        cookTip.cookStartStatus = ECookStartStatus.NotInOrder
      else
        cookTip.cookStartStatus = ECookStartStatus.EnoughDish
      end
    end
  end
  if (cookState == EItemCookState.Prepare or cookState == EItemCookState.CanCook) and not self.m_bShowCookStartTip and GM.ConfigModel:IsServerControlOpen(EGeneralConfType.EquipmentRecipeHint) then
    local dishArray = {}
    for _, dishes in ipairs({directDishLack, indirectDishLack}) do
      for dish, _ in pairs(dishes) do
        local canCook, lackMat = itemCook:CanCook(dish)
        if canCook and next(lackMat) ~= nil and not Table.ListContain(dishArray, dish) then
          table.insert(dishArray, dish)
        end
      end
    end
    if next(dishArray) ~= nil then
      local dishInfo = self:_SortDishForTipOrder(dishArray, lackDishMat2Dish, directDishLack)
      cookTip.canCookDishArray = dishArray
      cookTip.canCookDishInfo = dishInfo
      for _, info in pairs(dishInfo) do
        if info.greenCount > 0 then
          self:ToggleBoardPrompt(false)
          break
        end
      end
      local itemView = self:GetItemView(self.m_boardInfoBarItem)
      if itemView then
        cookTip.promptTargetPosition = itemView.transform.position
        cookTip.promptTargetItem = self.m_boardInfoBarItem
      end
    end
  end
  return cookTip
end

function BaseSceneBoardView:_OnItemCookStarted()
  self.m_bShowCookStartTip = false
  self:HideCookStartTip()
end

function BaseSceneBoardView:_OnItemCookEnded(message)
  if self.m_boardInfoBarItem == message.item then
    self:UpdateBoardInfoBar(self.m_boardInfoBarItem)
  end
end

function BaseSceneBoardView:ShowCookStartTip(animated, forTutorial)
  if GM.ConfigModel:UseBoardCookBubble() then
    self.m_boardCookBubble:Show(self.m_boardInfoBarItem, animated, forTutorial)
  elseif not self.m_gesture then
    self.m_gesture = TutorialHelper.TapOnBoardInfoBarCookStartButtonForPrompt()
  end
end

function BaseSceneBoardView:HideCookStartTip(forTutorial)
  if GM.ConfigModel:UseBoardCookBubble() then
    self.m_boardCookBubble:Hide(forTutorial)
  elseif self.m_gesture then
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
  end
end

function BaseSceneBoardView:GetCookBubble()
  return self.m_boardCookBubble
end

function BaseSceneBoardView:_CalOrderItemCookPopTip(selectedCode, directDishLack, indirectDishLack, lackDishMat2Dish)
  if lackDishMat2Dish[selectedCode] == nil or next(lackDishMat2Dish[selectedCode]) == nil then
    return
  end
  local allCookCmps = self.m_model:GetAllItemCookCmp(true, false)
  local allCookCmpsByChain = {}
  local itemModel, chainId
  for _, cmp in ipairs(allCookCmps) do
    itemModel = cmp:GetItemModel()
    chainId = GM.ItemDataModel:GetChainId(itemModel:GetType())
    if allCookCmpsByChain[chainId] == nil then
      allCookCmpsByChain[chainId] = {cmp}
    else
      table.insert(allCookCmpsByChain[chainId], cmp)
    end
  end
  local originDishMap = lackDishMat2Dish[selectedCode]
  local toAddDishMap = {}
  for originDish, _ in pairs(originDishMap) do
    local unlockedInstrument, _ = GM.ItemDataModel:GetDishUnlockedInstru(originDish)
    chainId = GM.ItemDataModel:GetChainId(unlockedInstrument)
    for _, dishes in ipairs({directDishLack, indirectDishLack}) do
      for lackDish, _ in pairs(dishes) do
        if not originDishMap[lackDish] and not toAddDishMap[lackDish] then
          local toAdd = false
          for _, cmp in ipairs(allCookCmpsByChain[chainId] or {}) do
            local canCook, lackMats = cmp:CanCook(lackDish)
            if canCook then
              if Table.ListContain(lackMats, selectedCode) then
                toAdd = true
              elseif #lackMats == 0 then
                toAdd = false
                break
              end
            end
          end
          if toAdd then
            toAddDishMap[lackDish] = true
          end
        end
      end
    end
  end
  local dishArray = Table.GetKeys(originDishMap)
  Table.ListAppend(dishArray, Table.GetKeys(toAddDishMap))
  self:_SortDishForTipOrder(dishArray, lackDishMat2Dish, directDishLack)
  local popItems = {}
  local dish, lackInstru
  for i = #dishArray, 1, -1 do
    dish = dishArray[i]
    local lackCount = 999
    local matchCmp = {}
    local unlockedInstrument, _ = GM.ItemDataModel:GetDishUnlockedInstru(dish)
    lackInstru = unlockedInstrument
    chainId = GM.ItemDataModel:GetChainId(unlockedInstrument)
    for _, cmp in ipairs(allCookCmpsByChain[chainId] or {}) do
      local canCook, lackMats = cmp:CanCook(dish)
      if canCook and Table.ListContain(lackMats, selectedCode) then
        if lackCount > #lackMats then
          matchCmp = {cmp}
          lackCount = #lackMats
        elseif #lackMats == lackCount then
          table.insert(matchCmp, cmp)
        end
      end
    end
    for _, cmp in ipairs(matchCmp) do
      popItems[cmp:GetItemModel()] = true
    end
  end
  if next(popItems) ~= nil then
    EventDispatcher.DispatchEvent(EEventType.OrderItemCookPopTip, {
      tip = true,
      itemCode = selectedCode,
      popItems = popItems,
      tipType = EItemPopTipType.InstrPop
    })
    return
  end
  chainId = GM.ItemDataModel:GetChainId(lackInstru)
  local isBusy = allCookCmpsByChain[chainId] ~= nil
  EventDispatcher.DispatchEvent(EEventType.OrderItemCookPopTip, {
    tip = true,
    itemCode = lackInstru,
    popItems = {
      [self.m_boardInfoBarItem] = true
    },
    tipType = isBusy and EItemPopTipType.MatPopWithInstruBusy or EItemPopTipType.MatPopWithInstruLack
  })
end

function BaseSceneBoardView:_SortDishForTipOrder(dishArray, lackDishMat2Dish, directDishLack)
  local boardCodeCountMap = self.m_model:GetCodeCountMap(true, false, false)
  local dishInfo = {}
  local needMat, isLack, greenItems
  for _, dish in ipairs(dishArray) do
    local info = {}
    info.direct = directDishLack[dish] ~= nil
    info.greenMat = {}
    info.greenItemView = {}
    info.greenCount = 0
    info.remainScore = 0
    needMat = GM.ItemDataModel:GetMaterials(dish)
    for _, mat in ipairs(needMat) do
      isLack = lackDishMat2Dish[mat] ~= nil
      if isLack and boardCodeCountMap[mat] ~= nil and 0 < boardCodeCountMap[mat] then
        table.insert(info.greenMat, mat)
        info.greenCount = info.greenCount + 1
        greenItems = self.m_model:FilterItemsByType(mat, nil, 1)
        if 1 <= #greenItems then
          info.greenItemView[mat] = self:GetItemView(greenItems[1])
        end
      elseif isLack then
        info.remainScore = info.remainScore + GM.ItemDataModel:GetItemScore(mat)
      end
    end
    dishInfo[dish] = info
  end
  table.sort(dishArray, function(a, b)
    local infoA = dishInfo[a]
    local infoB = dishInfo[b]
    if infoA.greenCount ~= infoB.greenCount then
      return infoA.greenCount > infoB.greenCount
    elseif infoA.direct ~= infoB.direct then
      return infoA.direct
    elseif infoA.remainScore ~= infoB.remainScore then
      return infoA.remainScore < infoB.remainScore
    end
  end)
  return dishInfo
end
