BaseItemComponent = {}
BaseItemComponent.__index = BaseItemComponent

function BaseItemComponent:FromSerialization(dbTable)
end

function BaseItemComponent:ToSerialization(dbTable)
end

function BaseItemComponent:SetItemModel(itemModel)
  self.m_itemModel = itemModel
end

function BaseItemComponent:UpdateAccelerateTime()
end

function BaseItemComponent:UpdatePerSecond()
end

function BaseItemComponent:OnTap()
end

function BaseItemComponent:OnShock()
end

function BaseItemComponent:OnOpen()
end

function BaseItemComponent:OnSpeedUp(isFree)
end

function BaseItemComponent:OnBreak(isFree)
end

function BaseItemComponent:OnChoose(index)
end

function BaseItemComponent:OnActivate()
end

function BaseItemComponent:GetItemModel()
  return self.m_itemModel
end

function BaseItemComponent:GetGameMode()
  return self.m_itemModel:GetGameMode()
end

function BaseItemComponent:Destroy()
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
end
