ExtraBoardActivityPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
ExtraBoardActivityPopupHelper.__index = ExtraBoardActivityPopupHelper

function ExtraBoardActivityPopupHelper.Create()
  local helper = setmetatable({}, ExtraBoardActivityPopupHelper)
  helper:Init()
  return helper
end

function ExtraBoardActivityPopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(ExtraBoardActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
end

function ExtraBoardActivityPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function ExtraBoardActivityPopupHelper:CheckPopup()
  for activityType, activityDefinition in pairs(ExtraBoardActivityDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Started then
      if GM.TutorialModel:TryPopupTargetTutorial(ETutorialId.ExtraBoardStart) then
        return true
      end
      if not model:HasWindowOpenedOnce(ActivityState.Started) then
        return activityDefinition.ReadyWindowPrefabName, {activityType}
      end
    elseif state == ActivityState.Ended and model:CanPopEndWindow() then
      local endRewards = model:GetEndRewards()
      if not Table.IsEmpty(endRewards) then
        return activityDefinition.RewardRecoverWindowPrefabName, {activityType, endRewards}
      else
        return activityDefinition.EndWindowPrefabName, {activityType}
      end
    end
  end
end
