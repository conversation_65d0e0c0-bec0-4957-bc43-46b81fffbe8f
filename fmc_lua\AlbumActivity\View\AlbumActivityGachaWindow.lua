AlbumActivityGachaWindow = setmetatable({
  canCloseByAndroidBack = false,
  showWindowMask = false,
  bCloseByStateChanged = false
}, AlbumActivityBaseWindow)
AlbumActivityGachaWindow.__index = AlbumActivityGachaWindow
local PackName2Skin = {
  [EPropertyType.pack_1_1] = "A",
  [EPropertyType.pack_1_2] = "B",
  [EPropertyType.pack_1_3] = "C",
  [EPropertyType.pack_1_4] = "D",
  [EPropertyType.pack_1_5] = "E",
  [EPropertyType.gpack_1_3] = "F_a",
  [EPropertyType.gpack_1_4] = "F_b",
  [EPropertyType.gpack_1_5] = "F_c"
}
local AlbumAnimStep = {
  EnterPack = 1,
  WaitOpenPack = 2,
  OpenPack = 3,
  WaitCollectCard = 4,
  CollectCard = 5
}

function AlbumActivityGachaWindow:Init(activityType, openCardLists, mapNewCards, listSetFinish, bAllbum, propertyType, startPos)
  AlbumActivityBaseWindow.Init(self, activityType)
  self.m_openCardLists = openCardLists
  self.m_listSetFinish = listSetFinish
  self.m_bAllBum = bAllbum
  self.m_strPropetyType = propertyType
  self.m_startPos = startPos
  self.m_mapNewCards = mapNewCards
  SpriteUtil.SetImage(self.m_packImg, EPropertySprite[propertyType], true)
  self:InitSpine()
  self:InitCards()
  self:LoadCollectEntry()
  self:StepEnterPack()
  UIUtil.SetActive(self.m_skipGo, not Table.IsEmpty(self.m_model:GetItemCachePropertys()) and not bAllbum)
  if self.m_model:GetState() == ActivityState.Started then
    self.m_model:SetWindowOpened()
  end
  if IsAutoRun() then
    DOVirtual.DelayedCall(0.2, function()
      self:Close()
    end)
  end
end

function AlbumActivityGachaWindow:InitSpine()
  self.m_packSpineAnim.initialSkinName = PackName2Skin[self.m_strPropetyType]
  self.m_packSpineAnim:Initialize(true)
  self.m_packSpineAnim.AnimationState:SetAnimation(0, "init", true)
end

function AlbumActivityGachaWindow:OnDestroy()
  GM.UIManager:RemoveAllEventLocks(self)
  AlbumActivityBaseWindow.OnDestroy(self)
  if self.m_packEnterSeq ~= nil then
    self.m_packEnterSeq:Kill()
    self.m_packEnterSeq = nil
  end
  if self.m_openSeq ~= nil then
    self.m_openSeq:Kill()
    self.m_openSeq = nil
  end
end

function AlbumActivityGachaWindow:InitCards()
  UIUtil.SetActive(self.m_gachaCardGo, false)
  if Table.IsEmpty(self.m_openCardLists) then
    return
  end
  self.m_cardCells = {}
  self.m_newLastIndex = 0
  local go, tbCell
  for index, cardId in ipairs(self.m_openCardLists) do
    go = GameObject.Instantiate(self.m_gachaCardGo, self.m_gachaCardGo.transform.parent)
    tbCell = go:GetLuaTable()
    tbCell:Init(cardId, self.m_model, self.m_mapNewCards[cardId] ~= nil)
    if self.m_mapNewCards[cardId] ~= nil then
      self.m_newLastIndex = index
    end
    table.insert(self.m_cardCells, tbCell)
    UIUtil.SetActive(go, true)
  end
end

function AlbumActivityGachaWindow:LoadCollectEntry()
  local parentRect = self.m_collectEntryRect
  local prefabName = self.m_activityDefinition.CollectEntryPrefabName
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(prefabName), parentRect, V3Zero)
end

function AlbumActivityGachaWindow:OnMaskClicked()
  if self.m_step == AlbumAnimStep.EnterPack then
  elseif self.m_step == AlbumAnimStep.WaitOpenPack then
    self:StepOpenPack()
  elseif self.m_step == AlbumAnimStep.OpenPack then
    self:SkipOpenPack()
  elseif self.m_step == AlbumAnimStep.WaitCollectCard then
    self:StepCollectCard()
  elseif self.m_step == AlbumAnimStep.CollectCard then
  else
    self:TryClose()
  end
end

function AlbumActivityGachaWindow:StepEnterPack()
  self.m_step = AlbumAnimStep.EnterPack
  local rect = self.m_packRootRect
  local boardCamera = GM.ModeViewController:GetBoardInfo()
  local screenPos = boardCamera:WorldToScreenPoint(self.m_startPos)
  local worldPos = PositionUtil.UICameraScreen2World(screenPos)
  rect.position = worldPos
  UIUtil.SetLocalScale(rect, 0.5, 0.5)
  local seq = DOTween.Sequence()
  seq:Append(rect:DOAnchorPos(Vector2(0, 0), 0.5):SetEase(Ease.InOutSine))
  seq:Join(rect:DOScale(Vector3(1, 1, 1), 0.5):SetEase(Ease.InOutSine))
  seq:AppendCallback(function()
    self.m_step = AlbumAnimStep.WaitOpenPack
    self.m_packSpineAnim.AnimationState:SetAnimation(0, "idle", true)
    self.m_packEnterSeq = nil
  end)
  self.m_packEnterSeq = seq
end

function AlbumActivityGachaWindow:StepOpenPack()
  self.m_step = AlbumAnimStep.OpenPack
  self:SetContinueState(false)
  local rect = self.m_packRootRect
  local seq = DOTween.Sequence()
  self.m_packSpineAnim.AnimationState:SetAnimation(0, "open", false)
  seq:AppendInterval(1.5)
  local interval = 0.2
  local rotateDelay = 0
  local onceFlyTime = AlbumGachaCard.GetOpenFlyTime()
  local onceRotateTime = AlbumGachaCard.GetOpenRotateTime()
  local totalTime = interval * (#self.m_cardCells - 1) + (onceFlyTime + onceRotateTime)
  if self.m_newLastIndex < #self.m_cardCells and 0 < self.m_newLastIndex then
    rotateDelay = totalTime - onceFlyTime
    totalTime = rotateDelay + (self.m_newLastIndex - 1) * interval + onceRotateTime + onceFlyTime
  end
  local targetPos = self.m_packRootRect.position + Vector3(0, -500, 0)
  seq:AppendCallback(function()
    for i, cell in ipairs(self.m_cardCells) do
      cell:PlayOpenAnim(targetPos, 0.2, interval * (i - 1), i <= self.m_newLastIndex and rotateDelay or 0)
    end
  end)
  seq:AppendInterval(totalTime + 0.1)
  seq:AppendCallback(function()
    self.m_step = AlbumAnimStep.WaitCollectCard
    self.m_openSeq = nil
    self:SetContinueState(true, "card_collect_hint")
    DOTween.Sequence():Append(rect:DOMove(rect.position + Vector3(0, 50, 0), 0.2):SetEase(Ease.OutSine)):Append(rect:DOAnchorPos(Vector2(0, -1000), 0.4):SetEase(Ease.InSine))
  end)
  self.m_openSeq = seq
end

function AlbumActivityGachaWindow:SkipOpenPack()
  if self.m_step ~= AlbumAnimStep.OpenPack or self.m_openSeq == nil then
    return
  end
  self.m_openSeq:Kill()
  UIUtil.SetActive(self.m_packRootRect.gameObject, false)
  self.m_step = AlbumAnimStep.WaitCollectCard
  self.m_openSeq = nil
  self:SetContinueState(true, "card_collect_hint")
  for _, cell in ipairs(self.m_cardCells) do
    cell:SkipOpenAnim()
  end
end

function AlbumActivityGachaWindow:SetContinueState(state, textKey)
  local scale = state and Vector3(1, 1, 1) or Vector3(0, 0, 1)
  local rect = self.m_continueGo.transform
  rect:DOKill()
  rect:DOScale(scale, 0.1)
  if textKey ~= nil then
    self.m_continueText.text = GM.GameTextModel:GetText(textKey)
  end
end

function AlbumActivityGachaWindow:StepCollectCard()
  self.m_step = AlbumAnimStep.CollectCard
  local originX = self.m_collectEntryRect.anchoredPosition.x
  local seq = DOTween.Sequence()
  UIUtil.SetActive(self.m_skipGo, false)
  self.m_windowCanvasGroup:DOFade(0, 0.2)
  seq:Append(self.m_collectEntryRect:DOAnchorPosX(-100, 0.2):SetEase(Ease.InOutSine))
  local interval = 0.1
  seq:AppendCallback(function()
    for i, cell in ipairs(self.m_cardCells) do
      cell:PlayCollectAnim(self.m_collectEntryRect.position, 0.2, interval * (i - 1))
    end
  end)
  local onceTime = AlbumGachaCard.GetCollectAnimTime()
  local totalTime = interval * (#self.m_cardCells - 1) + onceTime
  seq:AppendInterval(totalTime)
  seq:Append(self.m_collectEntryRect:DOAnchorPosX(originX, 0.2):SetEase(Ease.InSine))
  seq:AppendCallback(function()
    self.m_step = nil
    self:TryClose()
  end)
end

function AlbumActivityGachaWindow:TryClose()
  AlbumActivityBaseWindow.Close(self)
  if not Table.IsEmpty(self.m_listSetFinish) then
    GM.UIManager:OpenView(self.m_activityDefinition.TakeSetRewardPrefabName, self.m_model:GetType(), self.m_listSetFinish, self.m_bAllBum)
    return
  end
  self.m_model:ConsumeNextCardPack(self.m_startPos)
end

function AlbumActivityGachaWindow:OnSkipButtonClicked()
  local mapCard2Count, mapNewCards, listSetFinish, bAllbum = self.m_model:ConsumeAllCardPacks()
  if Table.IsEmpty(mapCard2Count) then
    return
  end
  for _, cardId in ipairs(self.m_openCardLists) do
    mapCard2Count[cardId] = (mapCard2Count[cardId] or 0) + 1
  end
  for cardId, _ in pairs(self.m_mapNewCards) do
    mapNewCards[cardId] = true
  end
  Table.ListAppend(self.m_listSetFinish, listSetFinish)
  self:Close()
  GM.UIManager:OpenView(UIPrefabConfigName.AlbumActivitySkipGachaWindow, self.m_activityType, mapCard2Count, mapNewCards, self.m_listSetFinish, bAllbum)
end

AlbumGachaCard = {}
AlbumGachaCard.__index = AlbumGachaCard

function AlbumGachaCard:Init(cardId, model, bRed)
  self.m_model = model
  self.m_cardLuaTable:Init(cardId, model, bRed, false)
  self.m_bRed = bRed
  self.m_bGold = model:IsGoldCard(cardId)
  local sprite = self.m_bGold and self.m_goldenBackSprite or self.m_commonBackSprite
  self.m_backImg.sprite = sprite
  self.m_frontOriginScale = self.m_cardRect.localScale
  UIUtil.SetActive(self.m_cardRect.gameObject, false)
  UIUtil.SetActive(self.m_backRect.gameObject, false)
end

function AlbumGachaCard:InitForSkip(cardId, model, bRed, num)
  self.m_model = model
  self.m_cardLuaTable:Init(cardId, model, bRed, true, num)
  self.m_bGold = model:IsGoldCard(cardId)
  self.m_bRed = bRed
  if self.m_bRed then
    local bgEffectGo = self.m_bGold and self.m_goldBgEffectGo or self.m_commonBgEffectGo
    UIUtil.SetActive(bgEffectGo, true)
  end
end

function AlbumGachaCard:OnDestroy()
  if self.m_flySeq ~= nil then
    self.m_flySeq:Kill()
    self.m_flySeq = nil
  end
  if self.m_collectSeq ~= nil then
    self.m_collectSeq:Kill()
    self.m_collectSeq = nil
  end
end

local openFlyTime = 0.3
local rotateTime = 0.4

function AlbumGachaCard:PlayOpenAnim(startPos, startScale, fDelay, rotateDelay)
  fDelay = fDelay or 0
  UIUtil.SetActive(self.m_cardRect.gameObject, true)
  UIUtil.SetActive(self.m_backRect.gameObject, true)
  local frontRect = self.m_cardRect
  local backRect = self.m_backRect
  UIUtil.SetLocalEulerAngles(frontRect, 0, -90, 0)
  local originPos = backRect.transform.position
  local originScale = backRect.transform.localScale
  backRect.transform.position = startPos + Vector3(0, 50, 0)
  UIUtil.SetLocalScale(backRect, startScale, startScale)
  UIUtil.SetLocalEulerAngles(backRect, 0, 0, 25)
  self.m_backCanvasGroup.alpha = 0
  local seq = DOTween.Sequence()
  seq:AppendInterval(fDelay)
  seq:Append(backRect:DOMove(originPos, openFlyTime):SetEase(Ease.OutSine))
  seq:Join(backRect:DOScale(originScale, openFlyTime):SetEase(Ease.OutSine))
  seq:Join(backRect:DORotate(Vector3(0, 0, 0), openFlyTime))
  seq:Join(self.m_backCanvasGroup:DOFade(1, openFlyTime))
  local originScale = frontRect.localScale
  seq:AppendInterval(rotateDelay)
  seq:Append(backRect:DORotate(Vector3(0, 90, 0), rotateTime / 2):SetEase(Ease.OutSine))
  seq:Append(frontRect:DORotate(Vector3(0, 0, 0), rotateTime / 2):SetEase(Ease.InSine))
  if self.m_bRed then
    seq:Join(frontRect:DOScale(originScale + Vector3(0.1, 0.1, 0), rotateTime / 2):SetEase(Ease.OutSine))
    seq:AppendInterval(0.2)
    seq:Append(frontRect:DOScale(originScale, 0.1):SetEase(Ease.InSine))
    seq:AppendCallback(function()
      local effectGo = self.m_bGold and self.m_goldBoomEffectGo or self.m_commonBoomEffectGo
      UIUtil.SetActive(effectGo, true)
    end)
  end
  seq:AppendCallback(function()
    self.m_flySeq = nil
    if self.m_bRed then
      local bgEffectGo = self.m_bGold and self.m_goldBgEffectGo or self.m_commonBgEffectGo
      UIUtil.SetActive(bgEffectGo, true)
    end
  end)
  self.m_flySeq = seq
end

function AlbumGachaCard:SkipOpenAnim()
  if self.m_flySeq ~= nil then
    self.m_flySeq:Kill()
    self.m_flySeq = nil
  end
  UIUtil.SetActive(self.m_cardRect.gameObject, true)
  self.m_backCanvasGroup.alpha = 1
  local frontRect = self.m_cardRect
  UIUtil.SetLocalEulerAngles(frontRect, 0, 0, 0)
  self.m_cardRect.localScale = self.m_frontOriginScale
  local backRect = self.m_backRect
  UIUtil.SetActive(backRect.gameObject, false)
end

function AlbumGachaCard.GetOpenFlyTime()
  return openFlyTime
end

function AlbumGachaCard.GetOpenRotateTime()
  return rotateTime
end

local collectFlyTime = 0.3
local collectWaitTime = 0

function AlbumGachaCard:PlayCollectAnim(endPos, endScale, fDelay)
  fDelay = fDelay or 0
  local rect = self.m_cardRect
  local seq = DOTween.Sequence()
  seq:AppendInterval(fDelay)
  seq:AppendCallback(function()
    UIUtil.SetActive(self.m_effectRootGo, false)
  end)
  seq:Append(rect:DOMove(endPos, collectFlyTime):SetEase(Ease.InOutSine))
  seq:Join(rect:DOScale(Vector3(endScale, endScale, 1), collectFlyTime))
  seq:AppendInterval(collectWaitTime)
  seq:AppendCallback(function()
    UIUtil.SetLocalScale(self.transform, 0, 0)
  end)
  self.m_collectSeq = seq
end

function AlbumGachaCard.GetCollectAnimTime()
  return collectFlyTime + collectWaitTime
end

AlbumActivitySkipGachaWindow = setmetatable({
  canCloseByAndroidBack = false,
  showWindowMask = false,
  bCloseByStateChanged = false
}, AlbumActivityBaseWindow)
AlbumActivitySkipGachaWindow.__index = AlbumActivitySkipGachaWindow

function AlbumActivitySkipGachaWindow:Init(activityType, mapCard2Count, mapNewCards, listSetFinish, bAllbum)
  AlbumActivityBaseWindow.Init(self, activityType)
  self.m_mapCard2Count = mapCard2Count
  self.m_listSetFinish = listSetFinish
  self.m_bAllBum = bAllbum
  self.m_mapNewCards = mapNewCards
  self:InitCards()
  self:LoadCollectEntry()
end

function AlbumActivitySkipGachaWindow:OnDestroy()
  GM.UIManager:RemoveAllEventLocks(self)
  AlbumActivityBaseWindow.OnDestroy(self)
end

function AlbumActivitySkipGachaWindow:InitCards()
  UIUtil.SetActive(self.m_gachaCardGo, false)
  if Table.IsEmpty(self.m_mapCard2Count) then
    return
  end
  local arrCardId = Table.GetKeys(self.m_mapCard2Count)
  table.sort(arrCardId, function(a, b)
    return self.m_model:CompareCard(a, b, self.m_mapNewCards)
  end)
  self.m_cardCells = {}
  local go, tbCell
  for _, cardId in ipairs(arrCardId) do
    go = GameObject.Instantiate(self.m_gachaCardGo, self.m_gachaCardGo.transform.parent)
    tbCell = go:GetLuaTable()
    tbCell:InitForSkip(cardId, self.m_model, self.m_mapNewCards[cardId] ~= nil, self.m_mapCard2Count[cardId])
    table.insert(self.m_cardCells, tbCell)
    UIUtil.SetActive(go, true)
  end
  if #self.m_cardCells <= 6 then
    self.m_maskImg.raycastTarget = false
    self.m_scrollRect.enabled = false
  else
    self.m_maskImg.raycastTarget = true
    self.m_scrollRect.enabled = true
  end
end

function AlbumActivitySkipGachaWindow:LoadCollectEntry()
  local parentRect = self.m_collectEntryRect
  local prefabName = self.m_activityDefinition.CollectEntryPrefabName
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(prefabName), parentRect, V3Zero)
end

function AlbumActivitySkipGachaWindow:OnMaskClicked()
  if self.m_step ~= AlbumAnimStep.CollectCard then
    self:StepCollectCard()
  end
end

function AlbumActivitySkipGachaWindow:StepCollectCard()
  self.m_step = AlbumAnimStep.CollectCard
  local originX = self.m_collectEntryRect.anchoredPosition.x
  local seq = DOTween.Sequence()
  self.m_windowCanvasGroup:DOFade(0, 0.2)
  seq:Append(self.m_collectEntryRect:DOAnchorPosX(-100, 0.2):SetEase(Ease.InOutSine))
  seq:AppendCallback(function()
    self.m_scrollMask.enabled = false
    self.m_maskImg.enabled = false
  end)
  local interval = #self.m_cardCells >= 9 and 0 or 0.05
  seq:AppendCallback(function()
    local endPos = self.m_collectEntryRect.position
    for i, cell in ipairs(self.m_cardCells) do
      cell:PlayCollectAnim(endPos, 0.2, interval * (i - 1))
    end
  end)
  local onceTime = AlbumGachaCard.GetCollectAnimTime()
  local totalTime = interval * (#self.m_cardCells - 1) + onceTime
  seq:AppendInterval(totalTime + 0.1)
  seq:Append(self.m_collectEntryRect:DOAnchorPosX(originX, 0.2):SetEase(Ease.InSine))
  seq:AppendCallback(function()
    self.m_step = nil
    self:TryClose()
  end)
end

function AlbumActivitySkipGachaWindow:TryClose()
  AlbumActivityBaseWindow.Close(self)
  if not Table.IsEmpty(self.m_listSetFinish) then
    GM.UIManager:OpenView(self.m_activityDefinition.TakeSetRewardPrefabName, self.m_model:GetType(), self.m_listSetFinish, self.m_bAllBum)
    return
  end
end
