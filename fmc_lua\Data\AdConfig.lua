AdConfig = {}
AdConfig.__index = AdConfig

function AdConfig.Create(data)
  local config = setmetatable({}, AdConfig)
  config:_Init(data)
  return config
end

function AdConfig:_Init(data)
  self.eType = data.name
  self.bEnable = data.enable > 0
  self.maxNum = data.maxNum
  self.interval = data.interval
  self.startLevel = data.sLv
  self.endLevel = data.eLv
  self.params = {}
  if data.param then
    local tbParam, arrStr
    for _, v in ipairs(data.param) do
      tbParam = {}
      arrStr = StringUtil.Split(v, "-")
      tbParam[arrStr[1]] = tonumber(arrStr[2])
      table.insert(self.params, tbParam)
    end
  end
end
