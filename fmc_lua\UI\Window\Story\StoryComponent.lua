StoryComponent = {}
StoryComponent.__index = StoryComponent
EStoryComponentType = {Left = "left", Right = "right"}

function StoryComponent:Init()
  self:SetInitState()
end

function StoryComponent:SetInitState()
  self.m_bgImg.transform:SetLocalScaleY(0)
  self.m_avatarImg.transform:SetLocalScaleXY(0.8)
  self.m_canvasGroup.alpha = 1
end

function StoryComponent:OnDestroy()
  self:TryKillSequence()
  if self.m_arrowTween then
    self.m_arrowTween:Kill()
    self.m_arrowTween = nil
  end
  if self.m_textTween then
    self.m_textTween:Kill()
    self.m_textTween = nil
  end
end

function StoryComponent:GetType()
  return self.m_type
end

function StoryComponent:UpdateContent(stepData)
  self.m_stepData = stepData
  if stepData.speaker then
    SpriteUtil.SetImage(self.m_avatarImg, ImageFileConfigName[stepData.speaker], true)
  end
  local contentText = self.m_stepData:GetContentText()
  local contentSize = string.len(contentText)
  local dt = math.min(0.6, contentSize / 280)
  if self.m_textTween then
    self.m_textTween:Kill()
    self.m_textTween = nil
  end
  self.m_contentText.text = ""
  self.m_textTween = self.m_contentText:DOText(contentText, dt, true)
end

function StoryComponent:Enter(onFinish)
  Log.Assert(self.m_stepData ~= nil, "剧情步骤数据缺失")
  self:TryKillSequence()
  self.m_canvasGroup.alpha = 1
  local sequence = DOTween.Sequence()
  sequence:Append(self.m_bgImg.transform:DOScaleY(0, 0))
  sequence:Join(self.m_avatarImg.transform:DOScale(0.8, 0))
  sequence:Append(self.m_bgImg.transform:DOScaleY(1, 0.4):SetEase(Ease.OutBack))
  sequence:Join(self.m_avatarImg.transform:DOScale(1, 0.4):SetEase(Ease.OutBack))
  sequence:AppendCallback(function()
    if onFinish then
      onFinish()
    end
  end)
  self.m_sequence = sequence
end

function StoryComponent:Leave(onFinish)
  self:TryKillSequence()
  if self.m_textTween then
    self.m_textTween:Complete(true)
    self.m_textTween = nil
  end
  GM.AudioModel:PlayEffect(AudioFileConfigName.sfxDialoguePopout)
  local sequence = DOTween.Sequence()
  sequence:Append(self.m_canvasGroup:DOFade(0, 0.2))
  sequence:AppendCallback(function()
    self.m_avatarImg.enabled = false
    if onFinish then
      onFinish()
    end
  end)
  self.m_sequence = sequence
end

function StoryComponent:TryKillSequence()
  if self.m_sequence then
    self.m_sequence:Kill()
    self.m_sequence = nil
  end
end
