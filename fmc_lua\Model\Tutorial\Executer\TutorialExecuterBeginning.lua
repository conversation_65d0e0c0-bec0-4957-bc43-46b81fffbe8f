local Step = {Timeline = "1"}
INTRO_TIMELINE = "intro_timeline"
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  if GM.LevelModel:GetCurrentLevel() > 1 then
    self:Finish(self.m_gesture, self.m_arrow)
    return
  end
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    return
  end
  if not GM.ChapterManager.roomModel or not GM.ChapterManager:GetActiveRoomView() then
    return
  end
  self:_ExecuteStep1()
  return true
end

function Executer:_ExecuteStep1()
  self.m_strOngoingDatas = Step.Timeline
  self:_SaveOngoingDatas()
  GM.TimelineManager:PlayTimeline(INTRO_TIMELINE, nil, function()
    self:Finish(self.m_gesture, self.m_arrow)
  end)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
