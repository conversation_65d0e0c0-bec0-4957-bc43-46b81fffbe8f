NameWindow = setmetatable({canCloseByAndroidBack = false}, BaseWindow)
NameWindow.__index = NameWindow

function NameWindow:Init(onFinish)
  self.m_onFinish = onFinish
  self.m_button:SetEnabled(false)
  self.m_inputField:Select()
end

function NameWindow:OnDestroy()
  if self.m_tween then
    self.m_tween:Kill()
    self.m_tween = nil
  end
end

function NameWindow:OnConfirmClick()
  if not GM.UserProfileModel:ChangeName(self.m_inputField.text) then
    GM.UIManager:ShowPromptWithKey("setting_window_change_fail")
    return
  end
  if self.m_onFinish then
    self.m_onFinish()
  end
  self:Close()
end

function NameWindow:OnInputFieldValueChanged()
  self.m_button:SetEnabled(not StringUtil.IsNilOrEmpty(self.m_inputField.text))
end
