ExtraBoardActivityMainWindow = setmetatable({}, ExtraBoardActivityBaseWindow)
ExtraBoardActivityMainWindow.__index = ExtraBoardActivityMainWindow

function ExtraBoardActivityMainWindow:BeforeOpenCheck()
  local model
  for activityType, _ in pairs(ExtraBoardActivityDefinition) do
    model = GM.ActivityManager:GetModel(activityType)
    if model and model:GetState() == ActivityState.Started then
      return true
    end
  end
  return false
end

function ExtraBoardActivityMainWindow:Init(activityType, bUserClick)
  ExtraBoardActivityBaseWindow.Init(self, activityType, bUserClick)
  self.m_boardModel = self.m_model:GetBoardModel()
  self:FitScreen()
  self:_UpdateButton()
  local length = self.m_model:GetMergeLength()
  if not UIUtil.IsEmptyComponent(self.m_levelProgress) then
    self.m_levelProgress:Init(length - 1, self.m_model, self)
  end
  if self.m_bookEntry ~= nil then
    self.m_bookEntry:Init(activityType)
  end
  UIUtil.SetActive(self.m_itemDeleteButton.gameObject, false)
  UIUtil.SetActive(self.m_itemTipButton.gameObject, false)
  GM.ModeViewController:LoadExtraBoardActivityBoardView(self.m_model:GetBoardModel(), self.m_boardRowImage, self.m_itemDeleteButton, self.m_itemTipButton, function(boardView)
    self.m_boardContainer:Init()
    if boardView ~= nil and self.m_model:IsCobwebOpen() then
      boardView:UpdateCobwebBoard(self.m_cobwebBgRect)
    end
  end)
  self.m_closed = false
  self:UpdateCobwebContent()
  self:PreLoadResource()
  local screenAjustSize = ScreenFitter.GetScreenAdjustSize()
  self:_AdjustBottomButtonRect(self.m_itemDeleteButton.transform, screenAjustSize)
  self:_AdjustBottomButtonRect(self.m_itemTipButton.transform, screenAjustSize)
  self:_AdjustBottomButtonRect(self.m_buttonTransform, screenAjustSize)
end

function ExtraBoardActivityMainWindow:FitScreen()
  local adjustSize = ScreenFitter.GetScreenAdjustSize()
  local standardWidth = ScreenFitter.GetStandardWidth()
  local standardHeight = ScreenFitter.GetStandardHeight()
  local adjustScale = 1
  if standardHeight < adjustSize.y then
    adjustScale = adjustSize.y / standardHeight
  end
  local originSize = self.m_boardContainer.transform.sizeDelta
  UIUtil.SetSizeDelta(self.m_boardContainer.transform, originSize.x * adjustScale, originSize.y * adjustScale)
  if self.m_model:IsCobwebOpen() then
    local deltaHeight = originSize.y * (adjustScale - 1) / 2
    local originPadding = self.m_boardRowImage.raycastPadding
    self.m_boardRowImage.raycastPadding = Vector4(originPadding.x, originPadding.y, originPadding.z, originPadding.w + deltaHeight)
  end
  if self.m_boardContainer:GetTargetTrans() ~= nil then
    UIUtil.SetSizeDelta(self.m_boardContainer:GetTargetTrans(), originSize.x * adjustScale, originSize.y * adjustScale)
  end
end

function ExtraBoardActivityMainWindow:AddEventListener()
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self._OnCachedItemsChanged)
  EventDispatcher.AddListener(EEventType.ItemMerged, self, self._OnItemMerged)
  EventDispatcher.AddListener(EEventType.ItemSpread, self, self._OnUpdateContent)
  EventDispatcher.AddListener(EEventType.CacheItems, self, self._OnCachedNewItem)
  EventDispatcher.AddListener(EEventType.ExtraBoardEnterNextCobwebRound, self, self.EnterNextCobwebRound)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._OnCloseView)
end

function ExtraBoardActivityMainWindow:OnDestroy()
  ExtraBoardActivityBaseWindow.OnDestroy(self)
  EventDispatcher.RemoveTarget(self)
  GM.ModeViewController:UnloadExtraBoardActivityBoardView()
  if self.m_bEventLocked then
    GM.UIManager:SetEventLock(false)
    self.m_bEventLocked = false
  end
  if self.m_bdlSeq then
    self.m_bdlSeq:Kill()
    self.m_bdlSeq = nil
  end
  if self.m_unlockSeq then
    self.m_unlockSeq:Kill()
    self.m_unlockSeq = nil
  end
  if self.m_completeSeq then
    self.m_completeSeq:Kill()
    self.m_completeSeq = nil
  end
  if self.m_beltSeq then
    self.m_beltSeq:Kill()
    self.m_beltSeq = nil
  end
end

function ExtraBoardActivityMainWindow:Update()
  if self.m_model ~= nil then
    if self.m_coroutine == nil then
      local boardModel = self.m_model:GetBoardModel()
      if boardModel ~= nil then
        boardModel:Update()
      end
    else
      coroutine.resume(self.m_coroutine)
    end
  end
end

function ExtraBoardActivityMainWindow:UpdatePerSecond()
  ExtraBoardActivityBaseWindow.UpdatePerSecond(self)
  if self.m_model ~= nil and self.m_coroutine == nil then
    local boardModel = self.m_model:GetBoardModel()
    if boardModel ~= nil then
      boardModel:UpdatePerSecond()
    end
  end
end

function ExtraBoardActivityMainWindow:Close()
  BaseWindow.Close(self)
  self.m_closed = true
end

function ExtraBoardActivityMainWindow:_OnCachedNewItem(msg)
  self:_UpdateButton(false)
end

function ExtraBoardActivityMainWindow:_OnCachedItemsChanged(msg)
  if msg == nil or msg.GameMode ~= self.m_boardModel:GetGameMode() then
    return
  end
  self:_OnUpdateContent(msg)
  self:_UpdateButton(true)
end

function ExtraBoardActivityMainWindow:_OnUpdateContent(msg)
  if not msg then
    return false
  end
  local itemType = msg.New:GetCode()
  if not self.m_model:UpdateLevel(itemType) then
    return false
  end
  self.m_isPlayingItemUnlockAnimation = true
  if not UIUtil.IsEmptyComponent(self.m_levelProgress) then
    self.m_levelProgress:PlayProgressAnimation(0.5)
  end
  local level = self.m_model:GetLevel()
  local itemCode = self.m_model:GetItemCodeByLevel(level)
  local seq = DOTween.Sequence()
  seq:AppendInterval(0.1)
  seq:AppendCallback(function()
    local newItem = msg.New
    local boardView = GM.ModeViewController:GetExtraBoardActivityBoardView()
    if boardView ~= nil then
      local sourceItemView = boardView:GetItemView(newItem)
      local worldPosition = sourceItemView and sourceItemView.transform.position or V3Zero
      local screenPosition = boardView:ConvertWorldPositionToScreenPosition(worldPosition)
      local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
      RewardApi.AcquireRewardsInView({
        {
          [PROPERTY_TYPE] = itemCode,
          [PROPERTY_COUNT] = 1
        }
      }, {
        arrWorldPos = {uiWorldPosition},
        floatLarge = true
      })
    end
  end)
  if self.m_model:IsMaxLevel() then
    self.m_bEventLocked = true
    GM.UIManager:SetEventLock(true)
    seq:AppendInterval(1.8)
    seq:AppendCallback(function()
      self.m_bEventLocked = false
      GM.UIManager:SetEventLock(false)
      GM.UIManager:OpenView(UIPrefabConfigName.ExtraBoardCompleteWindow, self.m_activityType)
    end)
  end
  seq:AppendCallback(function()
    self.m_unlockSeq = nil
  end)
  self.m_unlockSeq = seq
  return true
end

function ExtraBoardActivityMainWindow:PreLoadResource()
  if self.m_activityDefinition.BoardBgImageName ~= nil then
    SpriteUtil.LoadSprite(self.m_activityDefinition.BoardBgImageName, function()
    end)
  end
  if self.m_model:IsCobwebOpen() then
    SpriteUtil.LoadSprite(ImageFileConfigName.cobweb_tile_bg1, function()
    end)
  end
end

function ExtraBoardActivityMainWindow:_AdjustBottomButtonRect(rect, screenAdjustSize)
  local deltaY = rect.anchoredPosition.y
  local contentHeight = self.m_contentRect.sizeDelta.y * self.m_contentRect.localScale.y
  local size = screenAdjustSize
  if contentHeight / 2 - deltaY > size.y / 2 then
    UIUtil.SetAnchoredPosition(rect, nil, (contentHeight - size.y) / 2)
  end
end

function ExtraBoardActivityMainWindow:_UpdateButton(needDelay)
  local boardModel = self.m_model:GetBoardModel()
  if boardModel == nil then
    return
  end
  local cachedItemsCount = boardModel:GetCachedItemCount()
  if not UIUtil.IsEmptyComponent(self.m_buttonTransform) and not self.m_buttonTransform.gameObject.activeSelf and cachedItemsCount <= 0 and needDelay then
    DelayExecuteFuncInView(function()
      if not self.m_buttonTransform.gameObject:IsNull() then
        self.m_buttonTransform.localScale = Vector3.zero
        self.m_buttonTransform.gameObject:SetActive(true)
        local sequence = DOTween.Sequence()
        sequence:Append(self.m_buttonTransform:DOScale(1.2, 0.4))
        sequence:Append(self.m_buttonTransform:DOScale(1, 0.2))
      end
    end, 1, self)
  elseif not UIUtil.IsEmptyComponent(self.m_buttonTransform) then
    self.m_buttonTransform.gameObject:SetActive(cachedItemsCount <= 0)
  end
  local padding = self.m_boardRowImage.raycastPadding
  padding.y = 0 < cachedItemsCount and 0 or 320
  if self.m_activityDefinition.IsDigType then
    padding.y = 0
  end
  self.m_boardRowImage.raycastPadding = padding
end

function ExtraBoardActivityMainWindow:GetButtonTransform()
  return self.m_buttonTransform
end

function ExtraBoardActivityMainWindow:GetProgressTransform()
  return self.m_levelProgress.transform
end

function ExtraBoardActivityMainWindow:GetMergeLineItemIconArea(level)
  if self.m_activityDefinition.IsDigType then
    return self.m_mergeLineIconArea
  else
    return not UIUtil.IsEmptyComponent(self.m_levelProgress) and self.m_levelProgress:GetCellByLevel(level) and self.m_levelProgress:GetCellByLevel(level):GetIconArea() or nil
  end
end

function ExtraBoardActivityMainWindow:GetItemBoardAnchor()
  return self.m_itemBoardAnchor
end

function ExtraBoardActivityMainWindow:_OnButtonClicked()
  if GM.TutorialModel:HasAnyStrongTutorialOngoing() then
    return
  end
  self:Close()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    GM.SceneManager:ChangeGameMode(EGameMode.Board)
  end
end

function ExtraBoardActivityMainWindow:GetCacheIconArea()
  return self.m_cacheIconArea
end

function ExtraBoardActivityMainWindow:GetDeleteButton()
  return self.m_itemDeleteButton
end

function ExtraBoardActivityMainWindow:GetHelpBtnTrans()
  return self.m_helpBtnTrans
end

function ExtraBoardActivityMainWindow:GetBoardContainerTrans()
  return self.m_boardContainer.transform
end

function ExtraBoardActivityMainWindow:OnHelpButtonClick()
  GM.UIManager:OpenView(self.m_model:GetTipWindowName(), self.m_activityType, true)
end

function ExtraBoardActivityMainWindow:OnUnlockAnimationFinish(level)
  if not UIUtil.IsEmptyComponent(self.m_levelProgress) then
    self.m_levelProgress:UpdateLevelContent(true, level)
  end
  self.m_isPlayingItemUnlockAnimation = false
end

function ExtraBoardActivityMainWindow:UpdateCobwebContent()
  if not self.m_model:IsCobwebOpen() then
    return
  end
  local boardModel = self.m_model:GetBoardModel()
  self.m_bFinishedAllCobweb = boardModel:HasFinishedAllMainCobweb()
  UIUtil.SetActive(self.m_uncompleteGo, not self.m_bFinishedAllCobweb)
end

function ExtraBoardActivityMainWindow:_OnItemMerged(msg)
  local bUpdateLevel = self:_OnUpdateContent(msg)
  if not self.m_model:IsCobwebOpen() then
    return
  end
  if bUpdateLevel and self.m_model:IsMaxLevel() then
    return
  end
  self:_TryPlayCobwebUnlockAnim()
end

function ExtraBoardActivityMainWindow:_OnCloseView(msg)
  if msg.name == UIPrefabConfigName.ExtraBoardCompleteWindow and self.m_model:IsCobwebOpen() then
    self:_TryPlayCobwebUnlockAnim()
  end
end

function ExtraBoardActivityMainWindow:_TryPlayCobwebUnlockAnim()
  local boardModel = self.m_model:GetBoardModel()
  if not self.m_bFinishedAllCobweb and boardModel:HasFinishedAllMainCobweb() then
    self.m_bFinishedAllCobweb = true
    GM.UIManager:SetEventLock(true)
    self.m_bEventLocked = true
    local boardView = GM.ModeViewController:GetExtraBoardActivityBoardView()
    if boardView ~= nil then
      boardView:UpdateCobwebItemVisible()
      boardView:SetCobwebBoxStartState()
    end
    local lockTrans = self.m_uncompleteLockRect
    local sequence = DOTween.Sequence()
    sequence:Insert(0, lockTrans:DOShakeAnchorPos(0.5, 4, 20, 90, true))
    sequence:Insert(0.35, lockTrans:DOLocalMoveY(lockTrans.localPosition.y + 20, 0.05):SetEase(Ease.OutQuart))
    sequence:Insert(0.4, lockTrans:DOLocalMoveY(lockTrans.localPosition.y - 1500, 0.5):SetEase(Ease.InQuart))
    sequence:Insert(0.5, self.m_uncompleteBgRect:DOAnchorPosY(0, 0.5):SetEase(Ease.OutCubic))
    sequence:InsertCallback(1, function()
      self.m_bEventLocked = false
      GM.UIManager:SetEventLock(false)
      self.m_completeSeq = nil
      self:UpdateCobwebContent()
      if boardView ~= nil then
        boardView:PlayCobwebBoxAnim()
      end
    end)
    self.m_completeSeq = sequence
    EventDispatcher.DispatchEvent(EEventType.ExtraBoardCobwebUnlock)
  end
end

function ExtraBoardActivityMainWindow:PlayConveyBeltAnim(interval, animTime1, animTime2, animTime3, forwardDis, behindDis)
  local width = self.m_arrowRect1.sizeDelta.x
  UIUtil.SetAnchoredPosition(self.m_arrowRect1, 0)
  UIUtil.SetAnchoredPosition(self.m_arrowRect2, -width)
  GM.UIManager:SetEventLock(true)
  self.m_bEventLocked = true
  local seq = DOTween.Sequence()
  local arrPath1 = {}
  local arrPath2 = {}
  local rect = self.m_arrowRect1
  UIUtil.SetAnchoredPosition(rect, width + forwardDis)
  table.insert(arrPath1, rect.localPosition)
  UIUtil.SetAnchoredPosition(rect, width + behindDis)
  table.insert(arrPath1, rect.localPosition)
  UIUtil.SetAnchoredPosition(rect, width)
  table.insert(arrPath1, rect.localPosition)
  UIUtil.SetAnchoredPosition(rect, forwardDis)
  table.insert(arrPath2, rect.localPosition)
  UIUtil.SetAnchoredPosition(rect, behindDis)
  table.insert(arrPath2, rect.localPosition)
  UIUtil.SetAnchoredPosition(rect, 0)
  table.insert(arrPath2, rect.localPosition)
  seq:AppendInterval(interval)
  seq:Append(self.m_arrowRect1:DOAnchorPosX(width + forwardDis, animTime1):SetEase(Ease.InOutSine))
  seq:Join(self.m_arrowRect2:DOAnchorPosX(forwardDis, animTime1):SetEase(Ease.InOutSine))
  seq:Append(self.m_arrowRect1:DOAnchorPosX(width + behindDis, animTime2):SetEase(Ease.InOutSine))
  seq:Join(self.m_arrowRect2:DOAnchorPosX(behindDis, animTime2):SetEase(Ease.InOutSine))
  seq:Append(self.m_arrowRect1:DOAnchorPosX(width, animTime3):SetEase(Ease.InOutSine))
  seq:Join(self.m_arrowRect2:DOAnchorPosX(0, animTime3):SetEase(Ease.InOutSine))
  seq:AppendCallback(function()
    self.m_bEventLocked = false
    GM.UIManager:SetEventLock(false)
    self.m_beltSeq = nil
    UIUtil.SetAnchoredPosition(self.m_arrowRect1, 0)
    UIUtil.SetAnchoredPosition(self.m_arrowRect2, -width)
  end)
  self.m_beltSeq = seq
end

function ExtraBoardActivityMainWindow:EnterNextCobwebRound()
  DelayExecuteFuncInView(function()
    local interval = 0.4
    local animTime1 = 0.6
    local animTime2 = 0.3
    local animTime3 = 0.2
    local forwardDis = 50
    local behindDis = -25
    self:PlayConveyBeltAnim(interval, animTime1, animTime2, animTime3, forwardDis, behindDis)
    local boardView = GM.ModeViewController:GetExtraBoardActivityBoardView()
    boardView:EnterNextCobwebRound(interval, animTime1, animTime2, animTime3, forwardDis, behindDis)
  end, 1, self, true)
end

function ExtraBoardActivityMainWindow:GetCobwebBgRect()
  return self.m_cobwebBgRect
end

function ExtraBoardActivityMainWindow:GetBoardHighlightRoot()
  return self.m_boardHighlightRoot
end
