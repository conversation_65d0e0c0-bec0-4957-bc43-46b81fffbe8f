ItemTypeDeleteModel = {}
ItemTypeDeleteModel.__index = ItemTypeDeleteModel
ITEM_DELETE_CHAIN = "it_1_1_2"
LEVEL_1_REWARD_COUNT = 6
ItemDeleteState = {
  Finished = 1,
  NoticeWithoutReward = 2,
  NoticeHasReweard = 3
}

function ItemTypeDeleteModel:_CanHandle()
  if not GM.ConfigModel:IsServerControlOpen(EGeneralConfType.ItemDelete) then
    return false
  end
  if self:_GetCacheStateValue() ~= 0 then
    return false
  end
  if GM.ItemDataModel:GetModelConfig(ItemUtility.GetItemType(ITEM_DELETE_CHAIN, 1), true) ~= nil then
    return false
  end
  return true
end

function ItemTypeDeleteModel:OnItemRemoved(itemCode)
  if StringUtil.IsNilOrEmpty(itemCode) then
    return false
  end
  if not self:_CanHandle() then
    return false
  end
  local index = StringUtil.rFind<PERSON>har(itemCode, "_") or 0
  local chainId = string.sub(itemCode, 1, index - 1)
  if chainId == ITEM_DELETE_CHAIN then
    if self.m_mapDeletedItems == nil then
      self.m_mapDeletedItems = {}
    end
    self.m_mapDeletedItems[itemCode] = (self.m_mapDeletedItems[itemCode] or 0) + 1
    return true
  end
  return false
end

function ItemTypeDeleteModel:LateInit()
  if not self:_CanHandle() then
    return
  end
  if self.m_mapDeletedItems == nil then
    if GM.ItemDataModel:IsKnown(ItemUtility.GetItemType(ITEM_DELETE_CHAIN, 1)) then
      self:_SetCacheStateValue(ItemDeleteState.NoticeWithoutReward)
    else
      self:_SetCacheStateValue(ItemDeleteState.Finished)
    end
    return
  end
  self:_SetCacheStateValue(ItemDeleteState.NoticeHasReweard)
  local str = ItemUtility.Map2String(self.m_mapDeletedItems)
  GM.MiscModel:Set(self:_GetCacheItemsKey(), str)
end

function ItemTypeDeleteModel:NeedPopupWindow()
  local state = self:_GetCacheStateValue()
  if state == ItemDeleteState.NoticeWithoutReward then
    return true, 0
  elseif state ~= ItemDeleteState.NoticeHasReweard then
    return false
  end
  local items = self.m_mapDeletedItems
  if items == nil then
    local cacheItems = ItemUtility.String2Map(GM.MiscModel:Get(self:_GetCacheItemsKey()))
    items = {}
    for itemCode, count in pairs(cacheItems) do
      count = tonumber(count) or 1
      items[itemCode] = count
    end
  end
  if next(items) == nil then
    return true, 0
  end
  local rewardCount = 0
  local index, chainLevel
  for itemCode, count in pairs(items) do
    index = StringUtil.rFindChar(itemCode, "_") or 0
    chainLevel = tonumber(string.sub(itemCode, index + 1)) or 1
    rewardCount = rewardCount + ItemUtility.GetToLevel1Count(chainLevel) * LEVEL_1_REWARD_COUNT * count
  end
  rewardCount = math.max(rewardCount, 10)
  rewardCount = math.ceil(rewardCount)
  return true, rewardCount, items
end

function ItemTypeDeleteModel:OnWindowPoped(rewardCount)
  if 0 < rewardCount then
    local rewards = {
      [PROPERTY_TYPE] = EPropertyType.Energy,
      [PROPERTY_COUNT] = rewardCount,
      [PROPERTY_CRYPT] = Crypt.CryptCurrency(rewardCount)
    }
    RewardApi.AcquireRewardsLogic({rewards}, EPropertySource.Give, EBIType.ItemDelete)
  end
  GM.MiscModel:Remove(self:_GetCacheItemsKey())
  self:_SetCacheStateValue(ItemDeleteState.Finished)
end

function ItemTypeDeleteModel:_GetCacheStateValue()
  return GM.MiscModel:GetInNumber(self:_GetCacheStateKey())
end

function ItemTypeDeleteModel:_SetCacheStateValue(value)
  GM.MiscModel:Set(self:_GetCacheStateKey(), value)
end

function ItemTypeDeleteModel:_GetCacheStateKey()
  return "ItemTypeDeleteState" .. ITEM_DELETE_CHAIN
end

function ItemTypeDeleteModel:_GetCacheItemsKey()
  return "ItemTypeDeleteItems" .. ITEM_DELETE_CHAIN
end
