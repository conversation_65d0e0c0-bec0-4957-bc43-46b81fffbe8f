LoopAnimation = {}
LoopAnimation.__index = LoopAnimation

function LoopAnimation:Start()
  if not self.inited then
    self:_Init()
    self.inited = true
  end
  if not self.m_animator then
    self.gameObject:SetActive(false)
    return
  end
  self:_RegisterEvents()
  self:_PlayAnimation()
end

function LoopAnimation:_Init()
  self:_InitData()
end

function LoopAnimation:ReinitFromPool()
  if not self.inited then
    self:_Init()
    self.inited = true
  end
  self:_RegisterEvents()
  self:_PlayAnimation()
end

function LoopAnimation:OnDestroy()
  self:_UnregisterEvents()
end

function LoopAnimation:_RegisterEvents()
  function self.m_onComplete()
    self:_OnAnimationComplete()
  end
  
  if self.m_animatorState then
    self.m_animatorState:Complete("+", self.m_onComplete)
  end
end

function LoopAnimation:_UnregisterEvents()
  if self.m_onComplete ~= nil then
    if self.m_animatorState then
      self.m_animatorState:Complete("-", self.m_onComplete)
    end
    self.m_onComplete = nil
  end
end

function LoopAnimation:_InitData()
  if self.m_strBLoopAnimate and tonumber(self.m_strBLoopAnimate) and tonumber(self.m_strBLoopAnimate) == 0 then
    self.m_bLoopAnimate = false
  else
    self.m_bLoopAnimate = true
  end
  self.m_arrAnimationWeights = {}
  self.m_strCurrentAnimationName = ""
  self.m_uIdleCount = tonumber(self.m_strIdleCount) or 0
  self.m_uLeftIdleCount = self.m_randomFirst and 0 or self.m_uIdleCount
  local count = self.m_animator.Data.Animations.Count
  Log.Assert(0 < count, "LoopAnimation:_InitData count > 0")
  Log.Assert(0 <= self.m_uIdleCount, "LoopAnimation:_InitData self.m_uIdleCount > 0")
  local animationNames = self.m_animator.Data.Animations.Items
  local name, weight
  self.m_totalWeight = 0
  for i = 0, count - 1 do
    name = animationNames[i].Name
    weight = tonumber(self[name]) or 0
    if 0 < weight then
      self.m_totalWeight = self.m_totalWeight + weight
      self.m_arrAnimationWeights[#self.m_arrAnimationWeights + 1] = {
        name = name,
        weight = self.m_totalWeight
      }
    end
  end
  Log.Assert(self.m_arrAnimationWeights[1], "LoopAnimation:_InitData m_arrAnimationWeights")
end

function LoopAnimation:_PlayAnimation(animatName)
  self.m_strCurrentAnimationName = animatName or self:_GetNextAnimationName()
  if self.m_animatorState and self.m_animatorState.Tracks and self.m_animatorState.Tracks.Count and self.m_animatorState.Tracks.Count >= 0 and not StringUtil.IsNilOrEmpty(self.m_strCurrentAnimationName) then
    local func = function()
      self.m_animatorState:SetAnimation(0, self.m_strCurrentAnimationName, false)
      self:_OnPlayAnimation()
    end
    SafeCall(func)
  end
end

function LoopAnimation:_GetNextAnimationName()
  if not StringUtil.IsNilOrEmpty(self.m_entryName) then
    local entryName = self.m_entryName
    self.m_entryName = nil
    return entryName
  end
  if self.m_uLeftIdleCount > 0 and self.m_strIdleName then
    self.m_uLeftIdleCount = self.m_uLeftIdleCount - 1
    return self.m_strIdleName
  end
  local randomWeight = math.random(self.m_totalWeight)
  local deltaWeightData
  for i = 1, #self.m_arrAnimationWeights do
    deltaWeightData = self.m_arrAnimationWeights[i]
    if type(deltaWeightData) ~= "table" then
      return self.m_animator.Data.Animations.Items[0].Name
    end
    if randomWeight <= deltaWeightData.weight then
      self.m_uLeftIdleCount = self.m_uIdleCount
      return deltaWeightData.name
    end
  end
  Log.Assert(false, "LoopAnimation:_GetNextAnimationName")
  return ""
end

function LoopAnimation:_OnAnimationComplete()
  if self.m_bLoopAnimate then
    self:_PlayAnimation()
  end
end

function LoopAnimation:_OnPlayAnimation()
end

function LoopAnimation:SetIsLoopAnimation(bLoop)
  self.m_bLoopAnimate = bLoop
end

function LoopAnimation:SetAnimationCompleteCallback(callback)
  self.m_aniCompleteCallback = callback
end

function LoopAnimation:ClearAnimationCompleteCallback()
  self.m_aniCompleteCallback = nil
end
