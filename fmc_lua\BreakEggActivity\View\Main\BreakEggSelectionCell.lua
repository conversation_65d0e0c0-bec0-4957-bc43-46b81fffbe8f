BreakEggSelectionCell = {}
BreakEggSelectionCell.__index = BreakEggSelectionCell

function BreakEggSelectionCell:Init(index)
  self.m_index = index
  self.m_activityModel = GM.ActivityManager:GetModel(ActivityType.BreakEgg)
end

function BreakEggSelectionCell:UpdateContent()
  self.m_bBomb = self.m_activityModel:GetBreakEggIndex() == self.m_index
  if self.m_bBomb then
    self.m_shell.AnimationState:SetAnimation(0, "egg_bomb", true)
    self.m_countText.text = ""
    self.m_itemImage.gameObject:SetActive(false)
  end
end

function BreakEggSelectionCell:OnClicked()
  if self.m_bBomb then
    return
  end
  local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BreakEggMainWindow)
  mainWindow:OnBreakEgg(self.m_index)
end

function BreakEggSelectionCell:OnReviveSuccess()
  self.m_bBomb = self.m_activityModel:GetBreakEggIndex() == self.m_index
end

function BreakEggSelectionCell:DisplayFirstStep()
  self.m_bBomb = self.m_activityModel:GetBreakEggIndex() == self.m_index
  self.m_itemImage.color = CSColor.white
  self.m_itemImage.gameObject:SetActive(false)
  UIUtil.SetAlpha(self.m_countText, 1)
  self.m_shell.color = CSColor.white
  self.m_shell.gameObject:SetActive(false)
  DOVirtual.DelayedCall(0.5, function()
    self.m_shell.gameObject:SetActive(true)
    self.m_shell.Skeleton:SetToSetupPose()
    self.m_shell.AnimationState:ClearTracks()
    self.m_shell.AnimationState:SetAnimation(0, "egg_appear", false)
    self.m_shell:Update(0)
  end)
end

function BreakEggSelectionCell:DisplayStart()
  self.m_shell:DOFade(0, 0.5)
end

function BreakEggSelectionCell:CollectReward(selectedIndex)
  if selectedIndex == self.m_index then
    self.m_itemImage.gameObject:SetActive(false)
    self.m_shell:DOFade(0, 0.5)
  else
    self.m_itemImage:DOFade(0, 0.5)
    self.m_countText:DOFade(0, 0.5)
    self.m_shell:DOFade(0, 0.5)
  end
end

function BreakEggSelectionCell:DisplayNewStep()
  self.m_bBomb = self.m_activityModel:GetBreakEggIndex() == self.m_index
  self.m_itemImage.color = CSColor.white
  self.m_itemImage.gameObject:SetActive(false)
  UIUtil.SetAlpha(self.m_countText, 1)
  self.m_shell.color = CSColor.white
  self.m_shell.Skeleton:SetToSetupPose()
  self.m_shell.AnimationState:ClearTracks()
  self.m_shell.AnimationState:SetAnimation(0, "egg_appear", false)
  self.m_shell:Update(0)
end

function BreakEggSelectionCell:DisplayResult(result, autoBreak)
  if autoBreak and self.m_bBomb then
    return
  end
  if autoBreak then
    self.m_itemImage.color = CSColor.gray
    self.m_shell.AnimationState:SetAnimation(0, "egg_break", false)
  else
    self.m_shell.AnimationState:SetAnimation(0, "egg_smash", false)
  end
  self.m_itemImage.gameObject:SetActive(true)
  self.m_itemImage.transform.localScale = Vector3.zero
  local targetScale
  if result == nil then
    targetScale = 0.75
    self.m_itemImage.transform.localPosition = Vector3(23, 0, 0)
    SpriteUtil.SetImage(self.m_itemImage, "break_egg_bomb", true)
    self.m_countText.text = ""
  else
    targetScale = 1.6
    self.m_itemImage.transform.localPosition = Vector3.zero
    local spriteName = ConfigUtil.GetCurrencyImageName(result)
    SpriteUtil.SetImage(self.m_itemImage, spriteName, true)
    self.m_countText.text = "X" .. result[PROPERTY_COUNT]
  end
  local sequence = DOTween.Sequence()
  if not autoBreak and result ~= nil then
    sequence:InsertCallback(0.1, function()
      self.m_smashParticleSystem:Play()
    end)
  end
  sequence:Insert(0.4, self.m_itemImage.transform:DOScale(targetScale, 0.3))
end

function BreakEggSelectionCell:CloneRewardObject()
  return Object.Instantiate(self.m_itemImage.gameObject, self.transform)
end
