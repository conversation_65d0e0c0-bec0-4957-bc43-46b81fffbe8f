ApiMessage = {DEFAULT_RETRY = 2}
ApiMessage.__index = ApiMessage
local Serialization = require("Model.Network.Api.ApiInterface")
local arrRewardKeys = {}

function ApiMessage.Login(requestId, callback, retryTimes, isLoading)
  if next(arrRewardKeys) == nil then
    Table.ListAppend(arrRewardKeys, Table.GetValueList(ERewardKey))
  end
  local tbLoginReq = {
    userId = GM.UserModel:GetUserId(),
    version = GameConfig.GetCurrentVersion(),
    channel = GameConfig.CURRENT_PLATFORM,
    user_level = GM.LevelModel:GetCurrentLevel(),
    exp = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Experience),
    user_progress = GM.TaskManager:GetTaskProgress(),
    region = DeviceInfo.GetCountry(),
    lang = LocalizationModel:GetCurLanguageInString(),
    reward_keys = arrRewardKeys,
    network_name = CSAppsFlyerManager:GetMediaSource() or "",
    tracker_name = CSAppsFlyerManager:GetTrackerName(),
    adid = "::" .. CSAppsFlyerManager:GetAppsFlyerId(),
    config_entries = GM.ConfigModel:GetSeverConfigEntries(),
    requestId = requestId,
    install_id = GM.UserModel:GetInstallUuid(),
    device_model = DeviceInfo.GetDeviceModel(),
    ad_group = CSAppsFlyerManager:GetConversionValue("adgroup") or "",
    ad_group_id = CSAppsFlyerManager:GetConversionValue("adgroup_id") or ""
  }
  ApiMessage.SendByHttp("BLLogin", tbLoginReq, callback, false, 15000, retryTimes, 6000, 10, isLoading)
end

function ApiMessage.UploadData(tbReq, callback)
  ApiMessage.SendByHttp("BLUpdUserBranch", tbReq, callback, false, 60000, 2, 6000, 10)
end

function ApiMessage.DownloadData(callback, isLoading)
  local tbReq = {
    userId = GM.UserModel:GetUserId(),
    version = GameConfig.GetCurrentVersion()
  }
  GM.UIManager:ShowTestPrompt("Download")
  ApiMessage.SendByHttp("BLGetUserBranch", tbReq, callback, nil, nil, nil, nil, nil, isLoading)
end

function ApiMessage.GetUserDataProfile(otherUserId, callback)
  local tbReq = {
    otherId = otherUserId,
    version = GameConfig.GetCurrentVersion()
  }
  ApiMessage.SendByHttp("BLGetOtherProgress", tbReq, callback, true)
end

function ApiMessage.GetRewardStatus(arrKeys, callback)
  local tbReq = {
    userId = GM.UserModel:GetUserId(),
    reward_keys = arrKeys
  }
  ApiMessage.SendByHttp("BLGetUserRewards", tbReq, callback)
end

function ApiMessage.ReceiveRewards(key, token, callback)
  local tbReq = {
    userId = GM.UserModel:GetUserId(),
    key = key,
    token = token
  }
  ApiMessage.SendByHttp("BLReceiveRewards", tbReq, callback, true)
end

function ApiMessage.HeartBeat(tbReq, callback)
  ApiMessage.SendByHttp("BLHeartBeat", tbReq, callback, nil, nil, 0)
end

function ApiMessage.GetBakeOutRank(config_key, config_id, groupId, token, callback, bShowMask)
  bShowMask = bShowMask == nil and true or bShowMask
  local tbReq = {
    userId = GM.UserModel:GetUserId(),
    config_id = config_id,
    config_key = config_key,
    bakeoutGroupId = groupId,
    bakeoutToken = token
  }
  ApiMessage.SendByHttp("BLGetBakeOutRank", tbReq, callback, bShowMask)
end

function ApiMessage.MarkLogout(callback)
  local tbReq = {
    userId = GM.UserModel:GetUserId(),
    version = GameConfig.GetCurrentVersion()
  }
  ApiMessage.SendByHttp("BLMarkLogout", tbReq, callback)
end

function ApiMessage.RaceActivityEntry(opName, round, playerNum, playerTypeArr, callback, bShowMask)
  bShowMask = bShowMask == nil and true or bShowMask
  local tbReq = {
    exclude_userid = {},
    userId = GM.UserModel:GetUserId(),
    round = round,
    robot_count = playerNum,
    groups_num = playerTypeArr
  }
  ApiMessage.SendByHttp(opName, tbReq, callback, bShowMask, nil, nil, nil, nil, true)
end

function ApiMessage.Serialize(reqCtx, strOpName, tbMsgReq)
  local writer = CSNetLibManager:CreateBufferBlockWriter(reqCtx.ReqBody)
  Serialization.MessageHeader.Serialize(writer, {
    ProtocolMd5 = Serialization.ProtocolMd5,
    MessageId = reqCtx.MessageId,
    Operation = strOpName
  })
  local serializer = Serialization.reqNameMap[strOpName]
  local result = serializer.Serialize(writer, tbMsgReq)
  CSNetLibManager:ReleaseBufferBlockWriter(writer)
  return result
end

function ApiMessage.Deserialize(reqCtx, strOpName)
  local reader = CSNetLibManager:CreateBufferBlockReader(reqCtx.RespBody)
  local deserializer = Serialization.respNameMap[strOpName]
  local bRet, body
  bRet, body = deserializer.Deserialize(reader)
  local left = reader:GetLeftLength()
  CSNetLibManager:ReleaseBufferBlockReader(reader)
  if not bRet or left ~= 0 then
    return nil
  end
  return body
end

function ApiMessage.TryFallbackRequest(reqCtx)
  if GameConfig.IsTestMode() then
    return GM.HttpManager:TryFallbackRequest(reqCtx, NetworkConfig.OfflineFallbackIp)
  else
    return GM.HttpManager:TryFallbackRequest(reqCtx, "*************", "https://cliapi-ga.mergecola.com")
  end
end

function ApiMessage.SendByHttp(strOpName, tbMsgReq, callback, bShowMask, uTimeout, uRetryCount, uLowSpeedLimit, uLowSpeedTime, bLogAll)
  if bShowMask then
    GM.UIManager:ShowMask()
  end
  local strUrl = NetworkConfig.GetHttpServerUrl(strOpName)
  if strOpName == "BLLogin" then
    strUrl = strUrl .. "&requestid=" .. tbMsgReq.requestId
  end
  local reqCtx = CSNetLibManager:CreateApiServerHttpRequest(GM.UserModel:GetUserId(), GM.HttpManager:GetServerTime(), strUrl, uTimeout or 8000, uRetryCount or ApiMessage.DEFAULT_RETRY)
  if bLogAll or bShowMask then
    reqCtx:SetLogAll()
  else
    reqCtx:SetLogFail()
  end
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader())
  reqCtx:SetRetryHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader(2))
  reqCtx:SetHeader(NetworkConfig.AcceptEncodingKey, "gzip, deflate")
  if GameConfig.IsTestMode() then
    local schema = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema, "")
    if not StringUtil.IsNilOrEmpty(schema) then
      reqCtx:SetHeader(NetworkConfig.SchemaHeaderKey, schema)
    end
  end
  local strToken = GM.SsoManager:GetToken()
  local session = GM.SsoManager:GetSession()
  reqCtx:SetHeader(NetworkConfig.TokenHeaderKey, strToken)
  if not ApiMessage.Serialize(reqCtx, strOpName, tbMsgReq) then
    Log.Warning("ApiMessage Serialize failed for " .. strOpName)
  end
  if reqCtx.ContentLength > 1024 and NetworkConfig.CompressMethod ~= "" then
    reqCtx:CompressBody(NetworkConfig.CompressMethod)
  end
  local headers, tbMsgResp
  reqCtx:SetCheckResponse(function()
    if GM == nil then
      return nil
    end
    headers = GM.HttpManager:ConvertHeaders(reqCtx.ResponseHeaders)
    if headers["process-time"] == nil then
      if reqCtx.Status ~= 503 then
        return "Missing process-time header"
      end
      if reqCtx.RespBody.Bytes == 0 then
        return "Invalid response body"
      end
      local rawData = reqCtx:GetResponseString()
      local tbData = json.decode(rawData)
      if tbData == nil then
        return "Malformed response body"
      end
      if StringUtil.IsNilOrEmpty(tbData.content) then
        return "Missing content"
      end
    end
    if math.floor(reqCtx.Status / 100) == 2 then
      tbMsgResp = ApiMessage.Deserialize(reqCtx, strOpName)
      if tbMsgResp == nil then
        return "Failed to deserialize response"
      end
    end
    return nil
  end)
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    if bShowMask then
      GM.UIManager:HideMask()
    end
    if reqCtx.Rcode == ResultCode.Succeeded then
      local strToken = headers.token
      if not StringUtil.IsNilOrEmpty(strToken) then
        GM.SsoManager:SetToken(strToken)
      end
      if callback ~= nil then
        callback(true, tbMsgResp, reqCtx)
      end
      return
    end
    if reqCtx.Rcode == ResultCode.Not2xx then
      if reqCtx.Status == 401 or reqCtx.Status == 403 then
        reqCtx:Retain()
        GM.SsoManager:OnTokenExpired(reqCtx:GetHeader(NetworkConfig.TokenHeaderKey), reqCtx, session)
        return
      end
      if reqCtx.Status == 503 then
        callback(false, tostring(reqCtx:GetResponseString()), reqCtx)
        return true
      end
      if reqCtx.Status == 404 or reqCtx.Status == 502 or reqCtx.Status == 400 and headers["server-time"] ~= nil then
        if headers["server-time"] ~= nil then
          GM.GameModel:RefreshServerTime(tonumber(headers["server-time"]))
          reqCtx.ServerTime = GM.HttpManager:GetServerTime()
        end
        if reqCtx.MaxRetry > 0 then
          reqCtx.MaxRetry = reqCtx.MaxRetry - 1
          reqCtx:Retain()
          reqCtx:Send()
          return
        end
      end
    end
    if ApiMessage.TryFallbackRequest(reqCtx) then
      return
    end
    Log.Warning("Api Message failed, error message is " .. reqCtx.ErrorMsg .. ", code is " .. tostring(reqCtx.Rcode) .. ", status is " .. tostring(reqCtx.Status), LogTag.Network)
    if callback ~= nil then
      callback(false, reqCtx.ErrorMsg, reqCtx)
    end
  end)
  if strToken == "" then
    GM.SsoManager:WaitForToken(reqCtx)
  else
    reqCtx:Send()
  end
end
