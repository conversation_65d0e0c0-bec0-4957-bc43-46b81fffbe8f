ItemPaperBox = setmetatable({}, BaseItemComponent)
ItemPaperBox.__index = ItemPaperBox

function ItemPaperBox.Create(innerItemCode)
  local itemPaperBox = setmetatable({}, ItemPaperBox)
  itemPaperBox:Init(innerItemCode)
  return itemPaperBox
end

function ItemPaperBox:Init(innerItemCode)
  self.m_innerItemCode = innerItemCode
end

function ItemPaperBox:OnShock()
  self:_Disappear()
end

function ItemPaperBox:_Disappear()
  local boardModel = self.m_itemModel:GetBoardModel()
  local newItem = boardModel:ReplaceItem(self.m_itemModel, self.m_innerItemCode)
  if string.find(self.m_innerItemCode, "#") == nil then
    boardModel:ShockNeighborItems(self.m_itemModel:GetPosition())
  end
  local message = {
    Source = self.m_itemModel,
    New = newItem
  }
  boardModel.event:Call(BoardEventType.CollapseItem, message)
  EventDispatcher.DispatchEvent(EEventType.PaperboxDisappear, message)
end
