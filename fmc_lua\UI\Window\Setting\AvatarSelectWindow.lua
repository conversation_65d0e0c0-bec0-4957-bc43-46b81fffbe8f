AvatarSelectWindow = setmetatable({}, BaseWindow)
AvatarSelectWindow.__index = AvatarSelectWindow

function AvatarSelectWindow:Init()
  self.m_originalIcon = GM.UserProfileModel:GetIcon()
  local data = GM.UserProfileModel:GetAvatarImagesAll()
  local accountIcon = GM.AccountManager:GetAccountPictureUrl()
  if not StringUtil.IsNilOrEmpty(accountIcon) then
    table.insert(data, 1, accountIcon)
  end
  self.m_cells = {}
  local selectCallback = function(selected)
    self:_OnSwitched(selected)
  end
  local go, luaTable
  local parent = self.m_cellGo.transform.parent
  for i = 1, #data do
    go = i == 1 and self.m_cellGo or Object.Instantiate(self.m_cellGo, parent)
    luaTable = go:GetLuaTable()
    luaTable:Init(data[i], selectCallback)
    self.m_cells[#self.m_cells + 1] = luaTable
  end
  self:_OnSwitched(self.m_originalIcon)
end

function AvatarSelectWindow:_OnSwitched(selected)
  self.m_selectedIcon = selected
  for i = 1, #self.m_cells do
    self.m_cells[i]:UpdateContent(self.m_cells[i].icon == selected)
  end
end

function AvatarSelectWindow:OnConfirmed()
  if self.m_selectedIcon ~= self.m_originalIcon then
    GM.UserProfileModel:ChangeIcon(self.m_selectedIcon)
  end
  GM.UIManager:ShowPromptWithKey("setting_window_change_success")
  self:Close()
end

function AvatarSelectWindow:SetLastPosition()
  self.m_scrollView.verticalNormalizedPosition = 0
end

function AvatarSelectWindow:GetAvatarRewardTransf()
  for _, value in ipairs(self.m_cells) do
    if GM.UserProfileModel:IsAvatarRewardIcon(value:GetIcon()) then
      return value.gameObject.transform
    end
  end
end
