AlbumEntry = setmetatable({}, HudGeneralButton)
AlbumEntry.__index = AlbumEntry

function AlbumEntry:Init(model)
  self.m_model = model
  self.m_activityType = model:GetType()
  self.m_activityDefinition = model:GetActivityDefinition()
  self:UpdateJokerContent()
  self:UpdateRedPoint()
  AddHandlerAndRecordMap(self.m_model:GetEvent(), AlbumActivityModel.EventKey.RedRefresh, {
    obj = self,
    method = self.UpdateRedPoint
  })
  AddHandlerAndRecordMap(self.m_model:GetEvent(), AlbumActivityModel.EventKey.JokerCountRefresh, {
    obj = self,
    method = self.UpdateJokerContent
  })
end

function AlbumEntry:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  if self.m_jokerEndTime ~= nil then
    local jokerDelta = math.max(0, self.m_jokerEndTime - GM.GameModel:GetServerTime())
    if jokerDelta == 0 then
      self.m_countDownText.text = GM.GameTextModel:GetText("joker_card_time_expired")
    else
      local str = TimeUtil.ParseTimeDescription(jokerDelta, 2, false, false)
      self.m_countDownText.text = str
    end
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  elseif self.gameObject.activeSelf then
    self.gameObject:SetActive(false)
  end
end

function AlbumEntry:UpdateRedPoint()
  UIUtil.SetActive(self.m_exclamationGo, self.m_model:IsShowRedPointIcon())
end

function AlbumEntry:OnClicked()
  GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, true)
end

function AlbumEntry:UpdateJokerContent()
  local jokerCount = self.m_model:GetJokerCount()
  local bShowJoker = 0 < jokerCount
  UIUtil.SetActive(self.m_normalRootGo, not bShowJoker)
  UIUtil.SetActive(self.m_jokerRootGo, bShowJoker)
  self.m_jokerNumText.text = bShowJoker and jokerCount or ""
  local scale = bShowJoker and 1 or 0.5
  self.m_exclamationAnimator.enabled = not bShowJoker
  UIUtil.SetLocalScale(self.m_exclamationGo.transform, scale, scale)
  UIUtil.SetLocalScale(self.m_exclamationAnimator.transform, 1, 1)
  UIUtil.SetAnchoredPosition(self.m_exclamationAnimator.transform, 0, 0)
  self:UpdateRedPoint()
  if 0 < jokerCount then
    local jokerGetTime = self.m_model:GetMinJokerCardTime()
    local nextStateTime = self.m_model:GetNextStateTime()
    self.m_jokerEndTime = jokerGetTime + Sec2Day
    self.m_jokerEndTime = math.min(self.m_jokerEndTime, nextStateTime)
    self.m_bOnce = true
  else
    self.m_jokerEndTime = nil
  end
  self:UpdatePerSecond()
end
