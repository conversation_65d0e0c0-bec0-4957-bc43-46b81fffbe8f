MapButtonAnimationType = {Common = "1", Hard = "2"}
MapButtonAnimation = {}
MapButtonAnimation.__index = MapButtonAnimation

function MapButtonAnimation:Awake()
  if StringUtil.IsNilOrEmpty(self.m_aniType) or self.m_aniType ~= MapButtonAnimationType.Common and self.m_aniType ~= MapButtonAnimationType.Hard then
    Log.Error("MapButtonAnimation定义错误m_aniType  prefab：" .. self.gameObject.name)
    return
  end
  if StringUtil.IsNilOrEmpty(self.m_beatName) then
    Log.Error("MapButtonAnimation定义错误m_beatName  prefab：" .. self.gameObject.name)
    return
  end
  if self.m_aniType == MapButtonAnimationType.Hard and StringUtil.IsNilOrEmpty(self.m_priority) then
    Log.Error("MapButtonAnimation定义错误m_priority  prefab：" .. self.gameObject.name)
    return
  end
  self.m_skes = self.gameObject:GetComponentsInChildren(typeof(CS.Spine.Unity.SkeletonGraphic))
  for i = 0, self.m_skes.Length - 1 do
    self.m_skes[i]:Initialize(false)
  end
  EventDispatcher.DispatchEvent(EEventType.IconAnimationCreate, {btn = self})
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnGameModeChanged)
end

function MapButtonAnimation:OnDestroy()
  EventDispatcher.DispatchEvent(EEventType.IconAnimationDestroy, {btn = self})
  EventDispatcher.RemoveTarget(self)
end

function MapButtonAnimation:GetButtonAnimationType()
  return self.m_aniType
end

function MapButtonAnimation:GetIdleAnimationName()
  return self.m_idleName
end

function MapButtonAnimation:GetBeatAnimationName()
  return self.m_beatName
end

function MapButtonAnimation:GetBeatPriority()
  return tonumber(self.m_priority)
end

function MapButtonAnimation:PlayIconAnimation()
  local delayTime = 0
  for i = 0, self.m_skes.Length - 1 do
    local ske = self.m_skes[i]
    ske.AnimationState:ClearTracks()
    ske.AnimationState:SetAnimation(0, self:GetBeatAnimationName(), false)
    if self.m_beatParticleSystem ~= nil then
      self.m_beatParticleSystem:Simulate(0)
      self.m_beatParticleSystem:Play()
    end
    local curAni = ske.AnimationState:GetCurrent(0)
    if curAni and curAni.AnimationEnd and delayTime < curAni.AnimationEnd then
      delayTime = curAni.AnimationEnd
    end
    local idleAnimationName = self:GetIdleAnimationName()
    if not StringUtil.IsNilOrEmpty(idleAnimationName) then
      ske.AnimationState:AddAnimation(0, idleAnimationName, true, 0)
    end
  end
  return delayTime
end

function MapButtonAnimation:OnGameModeChanged()
  local gameMode = GM.SceneManager:GetGameMode()
  if self.m_beatParticleSystem == nil or self.m_originParticleScale == nil then
  end
end
