UpdateHintModel = {}
UpdateHintModel.__index = UpdateHintModel
EUpdateHintType = {
  None = 0,
  Strong = 1,
  HotUpdate = 2,
  Both = 3
}
local OPEN_MAX_COUNT_PER_DAY = 2

function UpdateHintModel:OnSceneViewLoaded()
  if self:NeedOpenWindow() then
    self:OpenUpdateHintWindow()
  end
end

function UpdateHintModel:OpenUpdateHintWindow(whenIdle)
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.UpdateHintWindow) then
    return
  end
  if self.m_bHasPopupWindow then
    return
  end
  GM.SyncModel:CheckUpload(false)
  local canClose = self:IsPermitCloseWindow()
  if not canClose then
    GM.HeartBeatManager:Unschedule()
  end
  if whenIdle then
    GM.UIManager:OpenViewWhenIdle(UIPrefabConfigName.UpdateHintWindow, canClose)
  else
    GM.UIManager:OpenView(UIPrefabConfigName.UpdateHintWindow, canClose)
  end
  self.m_bHasPopupWindow = true
end

function UpdateHintModel:TryGoToStorePage()
  if self.m_strStoreURL and #self.m_strStoreURL > 0 then
    CSPlatform:OpenURL(self.m_strStoreURL)
  else
    CSPlatform:OpenURL(DeviceInfo.IsSystemIOS() and "https://apps.apple.com/app/id6618142292" or "https://play.google.com/store/apps/details?id=com.cola.game")
  end
end

function UpdateHintModel:ParseLogin(tbLoginResp)
  self.m_strStoreURL = self:_ParseStoreUrl(tbLoginResp.store_url)
  self.m_eHintType = tbLoginResp.update_hint
  self.m_endEnterTime = tbLoginResp.update_hint_end_enter_time and tbLoginResp.update_hint_end_enter_time // 1000 or 0
  if not self:IsPermitCloseWindow() then
    self.m_bHasPopupWindow = false
  end
  local needOpenWindow = self:NeedOpenWindow()
  if needOpenWindow and GM.SceneManager:GetGameMode() ~= EGameMode.Loading then
    self:OpenUpdateHintWindow()
  end
  local strIOSLatesetReleaseId = GM.ConfigModel:GetGeneralConfByType(EGeneralConfType.IOSUpdateHint)
  if strIOSLatesetReleaseId then
    PlatformInterface.PopupAppUpdate("6618142292", tostring(strIOSLatesetReleaseId))
  end
end

function UpdateHintModel:NeedOpenWindow(ignoreLimit)
  if self.m_eHintType ~= EUpdateHintType.Strong and self.m_eHintType ~= EUpdateHintType.Both then
    return false
  end
  if GM.TimelineLayer:IsPlayingVideo() then
    return false
  end
  if not self:IsPermitCloseWindow() then
    return true
  end
  if ignoreLimit then
    return true
  end
  local openedTime = self:GetPopupWindowCount()
  return openedTime < OPEN_MAX_COUNT_PER_DAY
end

function UpdateHintModel:IsPermitCloseWindow()
  local serverTime = GM.GameModel:GetServerTime()
  if serverTime < self.m_endEnterTime then
    return true
  end
end

function UpdateHintModel:SetHasPopupWindow(flag)
  self.m_bHasPopupWindow = flag
end

function UpdateHintModel:AddPopupWindowCount()
  PlayerPrefs.SetInt(EPlayerPrefKey.UpdateHintWindowOpenCount, self:GetPopupWindowCount() + 1)
end

function UpdateHintModel:GetPopupWindowCount()
  if PlayerPrefs.GetInt(EPlayerPrefKey.UpdateHintWindowOpenDay, 0) ~= GM.GameModel:GetServerTime() // Sec2Day then
    PlayerPrefs.SetInt(EPlayerPrefKey.UpdateHintWindowOpenDay, GM.GameModel:GetServerTime() // Sec2Day)
    PlayerPrefs.SetInt(EPlayerPrefKey.UpdateHintWindowOpenCount, 0)
  end
  return PlayerPrefs.GetInt(EPlayerPrefKey.UpdateHintWindowOpenCount, 0)
end

function UpdateHintModel:_ParseStoreUrl(url)
  if not StringUtil.IsNilOrEmpty(url) then
    url = StringUtil.Replace(url, "uuid", GM.UserModel:GetInstallUuid())
    url = StringUtil.Replace(url, "uid", GM.UserModel:GetDisplayUserId())
  end
  Log.Info("[UpdateHintModel]ParseStoreUrl:" .. url)
  return url
end
