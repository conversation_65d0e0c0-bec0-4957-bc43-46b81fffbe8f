PassActivityProgressCell = {}
PassActivityProgressCell.__index = PassActivityProgressCell

function PassActivityProgressCell:Init(activityType, level)
  self.m_activityDefinition = PassActivityDefinition[activityType]
  self.m_level = level
  self:UpdateLevel(level)
  self.m_model = GM.ActivityManager:GetModel(activityType)
end

function PassActivityProgressCell:UpdateLevel(level)
  self.m_levelText.text = level
end

function PassActivityProgressCell:TryPlayAnimation()
  local currentLevel = self.m_model:GetCurrentLevel()
  if currentLevel >= self.m_level and not self.m_model:HasShownProgressEffect(self.m_level) then
    if self.m_level ~= 1 then
      self.m_effectGo:SetActive(true)
    end
    self.m_model:SetShownProgressEffect(self.m_level)
  end
end

function PassActivityProgressCell:UpdateState(tokenLevel)
  if self.m_level == 1 then
    self.m_iconImage.sprite = self.m_firstLevelSprite
    SpriteUtil.SetNativeSize(self.m_iconImage)
    self.m_levelText.gameObject:SetActive(false)
  elseif tokenLevel >= self.m_level then
    self.m_iconImage.sprite = self.m_previousLevelSprite
    SpriteUtil.SetNativeSize(self.m_iconImage)
    self.m_levelText.gameObject:SetActive(true)
    if self.m_levelOutline and not self.m_levelOutline:IsNull() then
      self.m_levelOutline:SetColor(UIUtil.ConvertHexColor2CSColor(self.m_activityDefinition.PreviousProgressTextOutlineColor, true))
    end
    if self.m_activityDefinition.PreviousProgressTextColor then
      self.m_levelText.color = UIUtil.ConvertHexColor2CSColor(self.m_activityDefinition.PreviousProgressTextColor)
    end
  end
end
