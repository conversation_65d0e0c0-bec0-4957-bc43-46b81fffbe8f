ItemChooseCell = {}
ItemChooseCell.__index = ItemChooseCell

function ItemChooseCell:Init(chooseWindow, index, choiceItemType)
  self.m_chooseWindow = chooseWindow
  self.m_index = index
  local spriteName = GM.ItemDataModel:GetSpriteName(choiceItemType)
  SpriteUtil.SetImage(self.m_iconImage, spriteName, true)
  self:SetSelected(false)
  self.m_backGo:SetActive(true)
  self.m_frontGo:SetActive(false)
end

function ItemChooseCell:OnClicked()
  self.m_chooseWindow:OnCellClicked(self.m_index)
end

function ItemChooseCell:SetSelected(selected)
  self.m_selectedBackgroundGo:SetActive(selected)
  self.m_unselectedBackgroundGo:SetActive(not selected)
end

function ItemChooseCell:TurnAround(delay)
  local sequnce = DOTween.Sequence()
  self.m_sequnce = sequnce
  sequnce:AppendInterval(delay)
  sequnce:Append(self.m_backGo.transform:DOScaleX(0, 0.2))
  sequnce:AppendCallback(function()
    self.m_backGo:SetActive(false)
    self.m_frontGo:SetActive(true)
    self.m_frontGo.transform.localScale = Vector3(0, 1, 1)
  end)
  sequnce:Append(self.m_frontGo.transform:DOScaleX(1, 0.2))
  sequnce:OnComplete(function()
    self.m_sequnce = nil
  end)
end

function ItemChooseCell:OnDestroy()
  if self.m_sequnce then
    self.m_sequnce:Kill()
    self.m_sequnce = nil
  end
end
