PassActivityViewHelper = {}
local TASK_IMAGE_KEY = {
  [EPassActivityTargetType.Customer] = ImageFileConfigName.common_task_cook,
  [EPassActivityTargetType.Merge] = ImageFileConfigName.common_task_merge,
  [EPassActivityTargetType.Gold] = ImageFileConfigName.common_task_coin,
  [EPassActivityTargetType.UseGem] = ImageFileConfigName.common_task_gem,
  [EPassActivityTargetType.Energy] = ImageFileConfigName.common_task_energy,
  [EPassActivityTargetType.Dish] = ImageFileConfigName.common_task_dish
}

function PassActivityViewHelper.SetViewChain(showMainWindow)
  PassActivityViewHelper.s_showMainWindow = showMainWindow
end

function PassActivityViewHelper.ContinueViewChain(activityType)
  local model = GM.ActivityManager:GetModel(activityType)
  if model:GetState() ~= ActivityState.Started then
    return
  end
  local activityDefinition = PassActivityDefinition[activityType]
  local canFinishTasks = model:GetCanFinishTasks()
  if #canFinishTasks ~= 0 then
    GM.UIManager:OpenView(activityDefinition.FinishTaskWindowPrefabName, activityType, canFinishTasks)
    return
  end
  if model:ShouldRefreshTimelimitTasks() then
    return GM.UIManager:OpenView(activityDefinition.NewTimelimitTaskWindowPrefabName, activityType)
  end
  if PassActivityViewHelper.s_showMainWindow then
    if model:CanOpenBuyTicketPopupWindow() then
      GM.UIManager:OpenView(activityDefinition.BuyTicketPopupWindowPrefabName, activityType)
      return
    end
    local mainWindow = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
    if mainWindow == nil then
      GM.UIManager:OpenView(activityDefinition.MainWindowPrefabName, activityType)
    else
      mainWindow:OnGetFocus()
    end
  end
  PassActivityViewHelper.TryGiveExtraRewards(activityDefinition)
end

function PassActivityViewHelper.TryGiveExtraRewards(activityDefinition)
  local mainWindow = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
  if not mainWindow then
    return
  end
  mainWindow:TryClaimExtraRewards()
end

function PassActivityViewHelper.ContinueEndChain(activityType)
  local activityDefinition = PassActivityDefinition[activityType]
  local model = GM.ActivityManager:GetModel(activityType)
  if model:CanOpenBuyTicketPopupWindow() then
    GM.UIManager:OpenView(activityDefinition.BuyTicketPopupWindowPrefabName, activityType)
    return
  end
  local canTakeRewards = model:GetCanTakeRewards()
  if #canTakeRewards ~= 0 then
    GM.UIManager:OpenView(activityDefinition.RewardRecoverWindowPrefabName, activityType, canTakeRewards)
  end
end

function PassActivityViewHelper.OpenBuyTicketWindow(activityType, ext)
  local activityDefinition = PassActivityDefinition[activityType]
  local activityModel = GM.ActivityManager:GetModel(activityType)
  if activityModel ~= nil and activityModel:CanBuyMaxTicket() then
    local viewName = activityModel:GetBuyTicketWindowByCurState()
    if not StringUtil.IsNilOrEmpty(viewName) then
      GM.UIManager:OpenView(viewName, activityType, ext)
    end
  else
    GM.UIManager:OpenView(activityDefinition.BuyTicketWindowPrefabName, activityType, ext)
  end
end

function PassActivityViewHelper.SetTaskTargetImage(image, task, bFinished, bTip)
  local target = task:GetFinalTarget()
  local spriteName = TASK_IMAGE_KEY[target]
  local activityModel = task:GetActivityModel()
  local bUseGroup = activityModel:IsTaskGroupOpen()
  local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  if target == EPassActivityTargetType.Gold and bakeOutModel:CanAcquireToken() then
    spriteName = EPropertySprite[EPropertyType.BakeOutToken]
  end
  image.transform:DestroyAllChildren()
  if target == EPassActivityTargetType.Day then
    image.sprite = nil
    image.enabled = false
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.PassActivityTaskOrderDay), image.transform, Vector3(0, 0, 0), function(go)
      if go == nil or go:IsNull() then
        return
      end
      UIUtil.SetActive(go, true)
      if bTip then
        UIUtil.SetLocalScale(go.transform, 0.6, 0.6)
      elseif not bUseGroup then
        UIUtil.SetLocalScale(go.transform, 0.7, 0.7)
      end
    end)
    return
  end
  Log.Assert(spriteName ~= nil, "Invalid EPassActivityTargetType")
  SpriteUtil.SetImage(image, spriteName)
end
