CoinRaceHelpWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BaseWindow)
CoinRaceHelpWindow.__index = CoinRaceHelpWindow

function CoinRaceHelpWindow:Init(activityType, autoOpen)
  self.m_model = GM.ActivityManager:GetModel(activityType)
  if self.m_model == nil then
    return
  end
  if autoOpen then
    self:LogWindowAction(EBIType.UIActionType.Open, {
      EBIReferType.AutoPopup
    })
  else
    self:LogWindowAction(EBIType.UIActionType.Open, {
      EBIReferType.UserClick
    })
  end
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.StateChanged, {
    obj = self,
    method = self.Close
  })
end
