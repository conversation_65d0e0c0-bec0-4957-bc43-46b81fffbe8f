MainBoardButton = setmetatable({}, HudGeneralButton)
MainBoardButton.__index = MainBoardButton

function MainBoardButton:Awake()
  HudGeneralButton.Awake(self)
  self:_OnDisplay()
  self.m_dayGo:SetActive(GM.ConfigModel:ButtonShowDay())
  self.m_normalGo:SetActive(not GM.ConfigModel:ButtonShowDay())
end

function MainBoardButton:_OnDisplay()
  HudGeneralButton._OnDisplay(self)
  if not GM.ConfigModel:ButtonShowDay() then
    return
  end
  self.m_orderDayCalender:UpdateContent()
end

function MainBoardButton:OnClicked()
  if GM.TimelineManager:IsPlayingTimeline() then
    return
  end
  GM.SceneManager:ChangeGameMode(EGameMode.Board)
end

MainBoardHighlightButton = setmetatable({}, HudGeneralButton)
MainBoardHighlightButton.__index = MainBoardHighlightButton

function MainBoardHighlightButton:Awake()
  HudGeneralButton.Awake(self)
  self:Hide()
end

function MainBoardHighlightButton:OnTutorialHighlight(bHighlight)
  if bHighlight then
    self:Show()
  else
    self:Hide()
  end
end

function MainBoardHighlightButton:Show()
  UIUtil.SetActive(self.gameObject, true)
end

function MainBoardHighlightButton:Hide()
  UIUtil.SetActive(self.gameObject, false)
end
