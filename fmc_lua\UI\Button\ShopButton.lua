ShopButton = setmetatable({}, HudGeneralButton)
ShopButton.__index = ShopButton

function ShopButton:Awake()
  HudGeneralButton.Awake(self)
  EventDispatcher.AddListener(EEventType.OpenView, self, self.OnOpenView)
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self.UpdateContent)
  EventDispatcher.AddListener(EEventType.NewChapterUnlocked, self, self.UpdateContent)
  EventDispatcher.AddListener(EEventType.FunctionOpen, self, self._CheckOpen)
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self._OnShopRefreshed)
  self:_CheckOpen()
  self:UpdateContent()
end

function ShopButton:OnBtnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.ShopWindow)
end

function ShopButton:OnOpenView(msg)
  if msg.name == UIPrefabConfigName.ShopWindow and self.gameObject.activeSelf then
    ShopButton.ShopWindowOpened = true
    self:UpdateContent()
  end
end

function ShopButton:_CheckOpen()
  local open = GM.OpenFunctionModel:IsFunctionOpen(EFunction.Shop)
  if self.gameObject.activeSelf ~= open then
    self.gameObject:SetActive(open)
    self:UpdateContent()
  end
end

function ShopButton:_OnShopRefreshed(msg)
  if msg and msg.shopType == EShopType.DailyDeals then
    ShopButton.ShopWindowOpened = false
    self:UpdateContent()
  end
end

function ShopButton:UpdateContent()
  local needExclamation = false
  if not ShopButton.ShopWindowOpened and GM.ShopModel:HasFreeDailyDeals() then
    needExclamation = true
  end
  if needExclamation ~= self.m_exclamationGo.activeSelf then
    self.m_exclamationGo:SetActive(needExclamation)
  end
end
