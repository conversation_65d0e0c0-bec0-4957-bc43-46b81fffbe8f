LevelProgressCell = {}
LevelProgressCell.__index = LevelProgressCell

function LevelProgressCell:Init(level)
  self.m_level = level
end

function LevelProgressCell:UpdateLevelContent(currentLevel)
  Log.Assert(false, "UpdateLevelContent()是抽象函数")
end

LevelRewardCell = {}
LevelRewardCell.__index = LevelRewardCell

function LevelRewardCell:Init(level)
  self.m_level = level
end

function LevelRewardCell:UpdateLevelContent(currentLevel)
  Log.Assert(false, "UpdateLevelContent()是抽象函数")
end

LevelRewardArea = {}
LevelRewardArea.__index = LevelRewardArea

function LevelRewardArea:Init(maxLevel)
  self.m_topPadding = self.m_rewardsNode:GetComponent(typeof(CS.UnityEngine.UI.VerticalLayoutGroup)).padding.top
  local sliderLayoutElement = self.m_sliderNode:GetComponent(typeof(CS.UnityEngine.UI.LayoutElement))
  sliderLayoutElement.preferredHeight = self:_GetSliderHeight(maxLevel)
  self.m_progressCells = {}
  for level = maxLevel, 1, -1 do
    local progressCell = self:GetProgressCell(level)
    progressCell.transform.anchoredPosition = Vector2(0, -self:_GetSliderHeight(level))
    table.insert(self.m_progressCells, 1, progressCell)
  end
  self.m_rewardCells = {}
  for level = maxLevel, 1, -1 do
    table.insert(self.m_rewardCells, 1, self:GetRewardCell(level))
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_contentNode)
  self:_UpdateLevelContent()
  self:_UpdatePositionContent()
end

function LevelRewardArea:GetProgressCell(level)
  Log.Assert(false, "GetProgressCell()是抽象函数")
end

function LevelRewardArea:GetRewardCell(level)
  Log.Assert(false, "GetRewardCell()是抽象函数")
end

function LevelRewardArea:GetCurrentLevel()
  Log.Assert(false, "GetCurrentLevel()是抽象函数")
end

function LevelRewardArea:_UpdateLevelContent()
  local currentLevel = self:GetCurrentLevel()
  for _, progressCell in ipairs(self.m_progressCells) do
    progressCell:UpdateLevelContent(currentLevel)
  end
  for _, rewardCell in ipairs(self.m_rewardCells) do
    rewardCell:UpdateLevelContent(currentLevel)
  end
end

function LevelRewardArea:_UpdatePositionContent()
  local currentLevel = self:GetCurrentLevel()
  UIUtil.SetSizeDelta(self.m_sliderFillNode, nil, self:_GetSliderHeight(currentLevel))
  self:JumpToLevel(currentLevel)
end

function LevelRewardArea:PlayUpgradeAnimation(duration)
  local sequence = DOTween.Sequence()
  local currentLevel = self:GetCurrentLevel()
  local sizeDelta = self.m_sliderFillNode.sizeDelta
  if sizeDelta.y == 0 then
    UIUtil.SetSizeDelta(self.m_sliderFillNode, nil, self.m_topPadding)
  end
  sizeDelta.y = self:_GetSliderHeight(currentLevel)
  sequence:Insert(0, self.m_sliderFillNode:DOSizeDelta(sizeDelta, duration):SetEase(Ease.Linear))
  local scrollRect = self.transform:GetComponent(typeof(ScrollRect))
  local verticalNormalizedPosition = self:_GetMiddleVerticalNormalizedPosition(currentLevel)
  sequence:Insert(0, scrollRect:DOVerticalNormalizedPos(verticalNormalizedPosition, duration):SetEase(Ease.Linear))
  sequence:AppendCallback(function()
    self:_UpdateLevelContent()
  end)
end

function LevelRewardArea:_GetSliderHeight(level)
  if level == 0 then
    return 0
  end
  return self.RewardCellHeight * (level - 0.5) + self.m_topPadding
end

function LevelRewardArea:_GetMiddleVerticalNormalizedPosition(level)
  local sliderHeight = self:_GetSliderHeight(level + 2)
  sliderHeight = sliderHeight - self.m_topPadding
  local areaHeight = self.transform.rect.height
  local offset = areaHeight * 0.5
  if sliderHeight <= offset then
    return 0
  end
  local contentHeight = self.m_contentNode.rect.height
  local normalized = (contentHeight - sliderHeight - offset) / (contentHeight - areaHeight)
  return 1 - math.max(normalized, 0)
end

function LevelRewardArea:JumpToLevel(level, duration)
  local scrollRect = self.transform:GetComponent(typeof(ScrollRect))
  if duration and 0 < duration then
    scrollRect:DOVerticalNormalizedPos(self:_GetMiddleVerticalNormalizedPosition(level), duration)
  else
    scrollRect.verticalNormalizedPosition = self:_GetMiddleVerticalNormalizedPosition(level)
  end
end
