PrivacySettingWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
PrivacySettingWindow.__index = PrivacySettingWindow

function PrivacySettingWindow:Init()
  if GM.OperManager.mailDataEnabled then
    UIUtil.AddSizeDelta(self.m_contentRectTrans, 0, 180)
    self.m_mailDataGo:SetActive(true)
  else
    self.m_mailDataGo:SetActive(false)
  end
end

function PrivacySettingWindow:OnUserAgreementClicked()
  CSPlatform:OpenURL(NetworkConfig.GetUserAgreementLink())
end

function PrivacySettingWindow:OnChildPrivacyClicked()
  CSPlatform:OpenURL(NetworkConfig.GetChildPrivacyLink())
end

function PrivacySettingWindow:OnPrivacyPolicyClicked()
  CSPlatform:OpenURL(NetworkConfig.GetPrivacyPolicyLink())
end

function PrivacySettingWindow:OnDeleteAccountClicked()
  GM.AccountManager:DeleteAccount()
end

function PrivacySettingWindow:OnMailDataClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.MailDataWindow)
end
