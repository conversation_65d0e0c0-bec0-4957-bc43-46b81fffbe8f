AddressableLabel = {
  Default = "Default",
  Album1 = "Album1",
  AlbumCommon = "AlbumCommon",
  BP1 = "BP1",
  BP2 = "BP2",
  BP3 = "BP3",
  BP4 = "BP4",
  BP5 = "BP5",
  BP6 = "BP6",
  BP7 = "BP7",
  BP8 = "BP8",
  BPCommon = "BPCommon",
  BakeOut = "BakeOut",
  BlindChest1 = "BlindChest1",
  BlindChestCommon = "BlindChestCommon",
  BoaerThailand = "BoaerThailand",
  BoardHanbi = "BoardHanbi",
  BoardMorocco = "BoardMorocco",
  BoardNice = "BoardNice",
  BoardOrleans = "BoardOrleans",
  BoardVietnam = "BoardVietnam",
  ChapterBBQ = "ChapterBBQ",
  ChapterBakery = "ChapterBakery",
  ChapterDimSum = "ChapterDimSum",
  ChapterHanbi = "ChapterHanbi",
  ChapterMarket = "ChapterMarket",
  ChapterMorocco = "ChapterMorocco",
  ChapterNice = "ChapterNice",
  ChapterOrleans = "ChapterOrleans",
  ChapterOttoman = "ChapterOttoman",
  ChapterPasta = "ChapterPasta",
  ChapterSausage = "ChapterSausage",
  ChapterSeafood = "ChapterSeafood",
  ChapterSushi = "ChapterSushi",
  ChapterTacos = "ChapterTacos",
  ChapterTapas = "ChapterTapas",
  ChapterThailand = "ChapterThailand",
  ChapterVietnam = "ChapterVietnam",
  ChapterWine = "ChapterWine",
  CoinRace = "CoinRace",
  DigActivityCommon = "DigActivityCommon",
  DigItems1 = "DigItems1",
  ExtraBoard1 = "ExtraBoard1",
  ExtraBoard2 = "ExtraBoard2",
  ExtraBoard3 = "ExtraBoard3",
  ExtraBoard4 = "ExtraBoard4",
  ExtraBoard5 = "ExtraBoard5",
  ExtraBoard6 = "ExtraBoard6",
  ExtraBoard7 = "ExtraBoard7",
  ExtraBoardCommon = "ExtraBoardCommon",
  Lua = "Lua",
  PkRace = "PkRace",
  PkRaceCommon = "PkRaceCommon",
  ProgressActivity1 = "ProgressActivity1",
  ProgressActivityCommon = "ProgressActivityCommon",
  SurpriseChest = "SurpriseChest",
  SurpriseChestCommon = "SurpriseChestCommon",
  TreasureDig = "TreasureDig"
}
if GameConfig.IsTestMode() then
  setmetatable(AddressableLabel, {
    __index = function(_, key)
      Debug.LogError("AddressableLabel try to index a nil key: " .. tostring(key) .. "\n" .. debug.traceback())
      return key
    end
  })
end

function AddressableLabel.HasLabel(name)
  if name == nil then
    return false
  end
  return rawget(AddressableLabel, name) ~= nil
end
