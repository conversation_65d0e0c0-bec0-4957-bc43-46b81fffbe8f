DataConflictWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.DataConflictWindow,
  canCloseByChangeGameMode = false
}, BaseWindow)
DataConflictWindow.__index = DataConflictWindow
DataConflictWindow.SelectCountDown = 3

function DataConflictWindow.CreateDisplayData(gems, coins, level, day, currentChapterId, currentTasks, timestamp)
  return {
    Gems = gems,
    Coins = coins,
    Level = level,
    Day = day,
    CurrentChapter = currentChapterId,
    CurrentTasks = currentTasks,
    Timestamp = timestamp
  }
end

function DataConflictWindow:Init(descriptionTextKey, serverDisplayData, selectLocalCallback, selectNewCallback, closeCallback)
  self.m_selectLocalCallback = selectLocalCallback
  self.m_selectNewCallback = selectNewCallback
  self.m_closeCallback = closeCallback
  self.canCloseByAndroidBack = closeCallback ~= nil
  self.m_closeBtnGo:SetActive(self.canCloseByAndroidBack)
  self.m_descriptionText.text = GM.GameTextModel:GetText(descriptionTextKey)
  local localDisplayData = self:_GetLocalDisplayData()
  self.m_data1:Init(self, 1, localDisplayData, nil)
  self.m_data2:Init(self, 2, serverDisplayData, localDisplayData)
  GM.BIManager:LogAction(EBIType.SceneDataConflict, EBIType.SceneDataConflictAction.Show)
end

function DataConflictWindow:UpdatePerSecond()
  if self.m_countDownTimer == nil then
    return
  end
  self.m_countDownTimer = self.m_countDownTimer - 1
  local text = GM.GameTextModel:GetText("progressConflict_confirm_button")
  if self.m_countDownTimer == 0 then
    self.m_confirmButton:SetEnabled(true)
    self.m_confirmButtonText.text = text
    self.m_countDownTimer = nil
  else
    self.m_confirmButtonText.text = text .. " (" .. self.m_countDownTimer .. "s)"
  end
end

function DataConflictWindow:_GetLocalDisplayData()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local curDay = orderModel:GetCurOrderDay()
  return DataConflictWindow.CreateDisplayData(GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem), GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gold), GM.LevelModel:GetCurrentLevel(), curDay, GM.TaskManager:GetOngoingChapterId(), GM.TaskManager:GetOngoingTasksString(), 0)
end

function DataConflictWindow:OnRegionSelected(index)
  self.m_selectedIndex = index
  self.m_data1:SetSelected(index == 1)
  self.m_data2:SetSelected(index == 2)
  local sizeDelta = self.m_contentRect.sizeDelta
  sizeDelta.y = 1350
  self.m_contentRect.sizeDelta = sizeDelta
  self.m_confirmDescriptionGo:SetActive(true)
  self.m_confirmButton.gameObject:SetActive(true)
  self.m_confirmButton:SetEnabled(false)
  self.m_countDownTimer = DataConflictWindow.SelectCountDown + 1
  self:UpdatePerSecond()
end

function DataConflictWindow:OnConfirmButtonClicked()
  self:Close()
  if self.m_selectedIndex == 1 then
    self.m_selectLocalCallback()
  else
    self.m_selectNewCallback()
  end
end

function DataConflictWindow:OnCloseBtnClick()
  self:Close()
  self.m_closeCallback()
end
