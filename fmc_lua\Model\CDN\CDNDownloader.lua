CDNDownloader = {DEFAULT_TIME_OUT = 15000}
CDNDownloader.__index = CDNDownloader

function CDNDownloader.Create(strUrl, strSavePath, callback)
  local downloader = setmetatable({}, CDNDownloader)
  downloader:_Init(strUrl, strSavePath, callback)
  return downloader
end

function CDNDownloader:_Init(strUrl, strSavePath, callback)
  self.m_strUrl = strUrl
  self.m_strSavePath = strSavePath
  self.m_funcCallback = callback
  self.m_bFlying = false
  self.m_startTime = 0
end

function CDNDownloader:CanSend()
  return not self.m_bFlying
end

function CDNDownloader:StartDownload()
  local tempFilePath
  if self.m_strSavePath then
    local func = function()
      tempFilePath = CDNWriter.GetTempFilePath(self.m_strSavePath)
    end
    SafeCall(func)
    if StringUtil.IsNilOrEmpty(tempFilePath) then
      self.m_funcCallback(false, self, "Failed to get temp file with " .. self.m_strSavePath)
      return
    end
  end
  self.m_bFlying = true
  self.m_startTime = NetTimeStamp.Create(EBIType.NetworkCheckAction.StartCDNDownload)
  GM.BIManager:LogNet(EBIType.NetworkCheckAction.StartCDNDownload, self.m_strSavePath, nil, nil, self.bIsLoading)
  if not StringUtil.IsNilOrEmpty(tempFilePath) then
    self:_DownloadToFile(tempFilePath)
  else
    self:_DownloadToMemory()
  end
end

function CDNDownloader:_DownloadToFile(tempFilePath)
  local reqCtx = CSNetLibManager:CreateFileDownloader(tempFilePath, self.m_strUrl, CDNDownloader.DEFAULT_TIME_OUT, 2)
  reqCtx:SetHeader(NetworkConfig.AcceptEncodingKey, "gzip, deflate")
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  if self.bIsLoading then
    reqCtx:SetLogAll()
  else
    reqCtx:SetLogFail()
  end
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    self.m_reqCtx = reqCtx
    if reqCtx.Rcode == ResultCode.Succeeded then
      if GM.CDNResourceManager:IsDownloadingDownloader(self) then
        local callback = function(bSuccess, strErr)
          if bSuccess then
            local valid, errInfo = self:_CheckDownloadedFileValid(self.m_strSavePath)
            self:_OnDownloadFinished(valid, errInfo)
            Log.Info("CDNDownloader Write File " .. (valid and "Success " or "Failed ") .. self.m_strSavePath)
            if valid then
              Log.Info("CDNDownloader zip content is " .. table.concat(self.strFileContent or {}, ", "))
            end
          else
            self:_OnDownloadFinished(bSuccess, strErr)
            Log.Info("CDNDownloader Write File Failed " .. self.m_strSavePath)
          end
        end
        CDNWriter.ReplaceFile(self.m_strSavePath, tempFilePath, callback)
      elseif File.Exists(tempFilePath) then
        File.Delete(tempFilePath)
      end
    elseif reqCtx.MaxRetry > 0 then
      reqCtx.MaxRetry = reqCtx.MaxRetry - 1
      reqCtx:Retain()
      reqCtx:Send()
    else
      if CDNDownloader.TryFallbackRequest(reqCtx) then
        return
      end
      self:_OnDownloadFinished(false, tostring(reqCtx.Rcode) .. "," .. reqCtx.ErrorMsg)
    end
  end)
  reqCtx:Send()
end

function CDNDownloader:_DownloadToMemory()
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(self.m_strUrl, "GET", CDNDownloader.DEFAULT_TIME_OUT, 2)
  reqCtx:SetHeader(NetworkConfig.AcceptEncodingKey, "gzip, deflate")
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  if self.bIsLoading then
    reqCtx:SetLogAll()
  else
    reqCtx:SetLogFail()
  end
  local body, mapData
  reqCtx:SetCheckResponse(function()
    if GM == nil then
      return nil
    end
    if math.floor(reqCtx.Status / 100) == 2 then
      if reqCtx.RespBody.Bytes == 0 then
        return "Invalid response body"
      end
      body = reqCtx:GetResponseString()
      mapData = json.decode(body)
      if mapData == nil then
        return "Malformed response body"
      end
    end
    return nil
  end)
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    self.m_reqCtx = reqCtx
    if reqCtx.Rcode == ResultCode.Succeeded then
      self:_OnDownloadFinished(true, "", body)
      return
    end
    if reqCtx.MaxRetry > 0 then
      reqCtx.MaxRetry = reqCtx.MaxRetry - 1
      reqCtx:Retain()
      reqCtx:Send()
      return
    end
    if CDNDownloader.TryFallbackRequest(reqCtx) then
      return
    end
    self:_OnDownloadFinished(false, tostring(reqCtx.Rcode) .. "," .. reqCtx.ErrorMsg)
  end)
  reqCtx:Send()
end

function CDNDownloader:_OnDownloadFinished(bSuccess, errorMsg, fileContent)
  self.m_bFlying = false
  local timeInterval = self.m_startTime:EndAndGetDur()
  if bSuccess then
    self.m_funcCallback(true, self, errorMsg, fileContent)
    GM.BIManager:LogNet(EBIType.NetworkCheckAction.CDNDownloadSuccess, self.strFileKey, self.m_reqCtx, timeInterval, self.bIsLoading)
  else
    self.m_funcCallback(false, self, errorMsg)
    GM.BIManager:LogNet(EBIType.NetworkCheckAction.CDNDownloadFailed, errorMsg, self.m_reqCtx, timeInterval, self.bIsLoading)
  end
end

function CDNDownloader:_CheckDownloadedFileValid(strPath)
  if not File.Exists(strPath) then
    return false, "no file"
  end
  local valid = true
  local info
  local fileName, extension = CDNResourceManager.GetFileNameByFileKey(strPath)
  if StringUtil.EndWith(extension, "zip") then
    valid = ZipHelper.UnZip(strPath, FileUtils.CdnPath)
    if valid then
      local contentList = self.strFileContent or {}
      local contentValid, errInfo
      for _, contentPath in ipairs(contentList) do
        contentValid, errInfo = self:_CheckDownloadedFileValid(CDNResourceManager.GetCdnPath(contentPath))
        if not contentValid then
          valid = false
          info = info and info .. "," .. errInfo or errInfo
        end
      end
    end
  elseif extension == "json" or extension == "json.bytes" then
    local text = GM.ResourceLoader:LoadCDNFileByFullPath(strPath)
    if StringUtil.IsNilOrEmpty(text) or not json.decode(text) then
      valid = false
      info = "wrote json invalid: " .. fileName
    end
  elseif extension == "lua" or extension == "lua.bytes" then
    valid = LuaManager:CheckLuaValid(strPath)
    if not valid then
      info = "wrote lua invalid: " .. fileName
    end
  end
  if not valid and File.Exists(strPath) then
    File.Delete(strPath)
  end
  return valid, info
end

function CDNDownloader.TryFallbackRequest(reqCtx)
  if GameConfig.IsTestMode() then
    return false
  end
  return GM.HttpManager:TryFallbackRequest(reqCtx, "207.90.252.34", "https://cdn-ga.mergecola.com")
end
