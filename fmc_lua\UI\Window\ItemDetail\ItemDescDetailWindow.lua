ItemDescDetailWindow = setmetatable({}, ItemDetailWindow)
ItemDescDetailWindow.__index = ItemDescDetailWindow

function ItemDescDetailWindow:UpdateView(itemType)
  self.m_groupRect.gameObject:RemoveChildren()
  self:_UpdateView(itemType, ItemDetailWindowMode.Normal)
  EventDispatcher.DispatchEvent(EEventType.ItemDetailWindowUpdated)
end

function ItemDescDetailWindow:_UpdateView(itemType, mode)
  self.m_tipGo:SetActive(GM.ItemDataModel:IsBox(itemType))
  self.m_arrCells = {}
  self.m_titleText.text = GM.GameTextModel:GetText(ItemNameDefinition.GetName(itemType))
  self.m_desc.text = GM.GameTextModel:GetText("item_" .. itemType .. "_desc")
  local chainId = GM.ItemDataModel:GetChainId(itemType)
  local chain = GM.ItemDataModel:GetChain(chainId)
  local count = #chain
  for index, type in ipairs(chain) do
    local cell = self:_AddCell(type, false, index < count, true, false, self.m_groupRect)
    if type == itemType then
      cell:ShowAsSelected()
    end
  end
  self.m_addItemGo:SetActive(false)
  self.m_skipTimeGo:SetActive(false)
  local config = GM.ItemDataModel:GetModelConfig(itemType)
  if config.BoosterType == EBoosterType.AddItem then
    self.m_addItemGo:SetActive(true)
    UIUtil.SetSizeDelta(self.m_contentRect, nil, 900)
  elseif config.BoosterType == "SkipTime" or config.BoosterType == "NewDay" then
    if config.BoosterType == "SkipTime" then
      self.m_skipTimeDesc.text = GM.GameTextModel:GetText("item_cdspeed_desc")
      UIUtil.SetSizeDelta(self.m_contentRect, nil, 900)
    else
      self.m_skipTimeDesc.text = ""
      UIUtil.SetSizeDelta(self.m_contentRect, nil, 820)
    end
    SpriteUtil.SetImage(self.m_skipTimeItemImg, GM.ItemDataModel:GetSpriteName(itemType), true)
    self.m_skipTimeGo:SetActive(true)
  else
    UIUtil.SetSizeDelta(self.m_contentRect, nil, 650)
  end
  if chainId == ItemChain.AddItem then
    SpriteUtil.SetImage(self.m_addItemImg, GM.ItemDataModel:GetSpriteName(itemType), true)
    local itemConfig = GM.ItemDataModel:GetModelConfig(itemType)
    self.m_addItemText.text = itemConfig.Effect
  end
end
