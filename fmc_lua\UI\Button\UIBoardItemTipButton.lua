UIBoardItemTipButton = setmetatable({}, ItemTipButton)
UIBoardItemTipButton.__index = UIBoardItemTipButton

function UIBoardItemTipButton:Init(boardModel)
  self.m_boardModel = boardModel
end

function UIBoardItemTipButton:UpdateItem(itemModel)
  local itemBubble, itemCobweb, itemRewardBubble
  if itemModel ~= nil then
    itemBubble = itemModel:GetComponent(ItemBubble)
    itemCobweb = itemModel:GetComponent(ItemCobweb)
    itemRewardBubble = itemModel:GetComponent(ItemRewardBubble)
    local itemCode = itemModel:GetType()
    if itemBubble ~= nil then
      itemCode = itemBubble:GetInnerItemCode()
    elseif itemCobweb ~= nil then
      itemCode = itemCobweb:GetInnerItemCode()
    elseif itemRewardBubble ~= nil then
      itemCode = itemRewardBubble:GetInnerItemCode()
    end
    self:UpdateItemType(itemCode, nil, EItemDetailWindowRefer.UIBoard)
    self:_SetVisibility(true)
  else
    self:_SetVisibility(false)
  end
end

function UIBoardItemTipButton:_SetVisibility(bVisible)
  if self.gameObject and not self.gameObject:IsNull() then
    UIUtil.SetActive(self.gameObject, bVisible)
  end
end
