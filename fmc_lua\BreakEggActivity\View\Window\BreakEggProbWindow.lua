BreakEggProbWindow = setmetatable({}, BreakEggBaseWindow)
BreakEggProbWindow.__index = BreakEggProbWindow

function BreakEggProbWindow:Init()
  BreakEggBaseWindow.Init(self)
  self.m_activityModel = GM.ActivityManager:GetModel(ActivityType.BreakEgg)
  local data = self.m_activityModel:GetShowProbability()
  local go, luaTable
  local parent = self.m_cellGo.transform.parent
  for i = 1, #data do
    go = i == 1 and self.m_cellGo or Object.Instantiate(self.m_cellGo, parent)
    luaTable = go:GetLuaTable()
    luaTable:Init(data[i])
  end
end

BreakEggProbCell = {}
BreakEggProbCell.__index = BreakEggProbCell

function BreakEggProbCell:Init(data)
  SpriteUtil.SetImage(self.m_iconImg, data.icon)
  self.m_numText.text = "X" .. data.num
  self.m_probText.text = data.prob
end
