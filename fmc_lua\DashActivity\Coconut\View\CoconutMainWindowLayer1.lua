CoconutMainWindowLayer1 = {}
CoconutMainWindowLayer1.__index = CoconutMainWindowLayer1
CoconutMainWindowLayer1.TrunkCellHeight = 722
CoconutMainWindowLayer1.RiseDuration = 2.33
CoconutMainWindowLayer1.LandingDuration = 2.1
CoconutMainWindowLayer1.FlyDurationPerProgress = 2.75
CoconutMainWindowLayer1.AnimationMoveTime = 0.8
CoconutMainWindowLayer1.AnimationMoveDistance = 130
CoconutMainWindowLayer1.CoconutPositionOffset = 190

function CoconutMainWindowLayer1:Init()
  self.m_model = GM.ActivityManager:GetModel(ActivityType.Coconut)
  local levelConfigs = self.m_model:GetLevelConfigs()
  self.m_trunkCells = {}
  for index, config in ipairs(levelConfigs) do
    local trunkCell = self.m_trunkNode:Find("CoconutTrunkCell" .. index).gameObject:GetLuaTable()
    trunkCell:Init(config)
    table.insert(self.m_trunkCells, trunkCell)
  end
  self.m_progressCells = {}
  for i = 1, #levelConfigs do
    local progressCell = self.m_levelNode:Find("CoconutProgressCell" .. i).gameObject:GetLuaTable()
    table.insert(self.m_progressCells, progressCell)
  end
  local displayedLevel, displayedScore = self.m_model:GetDisplayedLevelAndScore()
  local lastProgress = self:_GetProgress(displayedLevel, displayedScore)
  self:_UpdateContent(lastProgress)
  self.m_coconutSkeleton.AnimationState:ClearTracks()
  self.m_coconutSkeleton.Skeleton:SetToSetupPose()
  self:_SetIdleAnimation()
  self.m_rewards = {}
  local level = self.m_model:GetLevel()
  local score = self.m_model:GetScore()
  local levelConfig = levelConfigs[level]
  while levelConfig ~= nil and score >= levelConfig.score do
    Table.ListAppend(self.m_rewards, levelConfig.rewards)
    self.m_model:Upgrade()
    level = self.m_model:GetLevel()
    score = self.m_model:GetScore()
    levelConfig = levelConfigs[level]
  end
  local currentProgress = self:_GetProgress(level, score)
  if lastProgress ~= currentProgress then
    self:RetainFlyAnimation()
    DOVirtual.DelayedCall(0.5, function()
      local func = function()
        self:_FlyAnimation(currentProgress)
      end
      self.m_coroutine = coroutine.create(func)
    end)
  end
  self.m_model:SetDisplayedLevelAndScore()
end

function CoconutMainWindowLayer1:Update()
  if self.m_coroutine ~= nil then
    coroutine.resume(self.m_coroutine)
  end
end

function CoconutMainWindowLayer1:OnDestroy()
  GM.UIManager:RemoveAllEventLocks(self)
end

function CoconutMainWindowLayer1:_SetIdleAnimation()
  if math.tointeger(self.m_progress) == nil then
    self.m_coconutSkeleton.AnimationState:SetAnimation(0, "coconut_activity_sky_idle", true)
  else
    self.m_coconutSkeleton.AnimationState:SetAnimation(0, "coconut_activity_idle", true)
  end
end

function CoconutMainWindowLayer1:_FlyAnimation(targetProgress)
  local deltaProgress = CoconutMainWindowLayer1.AnimationMoveDistance / CoconutMainWindowLayer1.TrunkCellHeight
  local flyStartProgress = self.m_progress
  if self.m_progress ~= 0 and math.tointeger(self.m_progress) ~= nil then
    flyStartProgress = math.min(self.m_progress + deltaProgress, targetProgress)
  end
  local flyEndProgress = targetProgress
  if math.tointeger(targetProgress) ~= nil then
    flyEndProgress = math.max(flyStartProgress, targetProgress - deltaProgress)
  end
  if self.m_progress == 0 or self.m_progress ~= flyStartProgress then
    self.m_coconutSkeleton.AnimationState:SetAnimation(0, "coconut_activity_rise2", false)
    Coroutine.Wait(CoconutMainWindowLayer1.RiseDuration - CoconutMainWindowLayer1.AnimationMoveTime)
    if self.m_progress == 0 then
      Coroutine.WaitForTween(self.m_coconutSkeleton.transform:DOAnchorPosY(CoconutMainWindowLayer1.CoconutPositionOffset, CoconutMainWindowLayer1.AnimationMoveTime):SetEase(Ease.InQuad))
    else
      self:_UpdateContentAnimation(self.m_progress, flyStartProgress, CoconutMainWindowLayer1.AnimationMoveTime, Ease.InQuad)
    end
  end
  if flyStartProgress ~= flyEndProgress then
    self.m_coconutSkeleton.AnimationState:SetAnimation(0, "coconut_activity_sky_idle", true)
    self:_UpdateContentAnimation(flyStartProgress, flyEndProgress, (flyEndProgress - flyStartProgress) * CoconutMainWindowLayer1.FlyDurationPerProgress, Ease.Linear)
  end
  if flyEndProgress ~= targetProgress then
    self.m_coconutSkeleton.AnimationState:SetAnimation(0, "coconut_activity_landing", false)
    self:_UpdateContentAnimation(flyEndProgress, targetProgress, CoconutMainWindowLayer1.AnimationMoveTime, Ease.OutQuad)
    Coroutine.Wait(CoconutMainWindowLayer1.LandingDuration - CoconutMainWindowLayer1.AnimationMoveTime)
  end
  self.m_coroutine = nil
  self:_SetIdleAnimation()
  self:ReleaseFlyAnimation()
end

function CoconutMainWindowLayer1:RetainFlyAnimation()
  if self.m_flyAnimationCounter == nil then
    self.m_flyAnimationCounter = 1
    GM.UIManager:SetEventLock(true, self)
  else
    self.m_flyAnimationCounter = self.m_flyAnimationCounter + 1
  end
end

function CoconutMainWindowLayer1:ReleaseFlyAnimation()
  self.m_flyAnimationCounter = self.m_flyAnimationCounter - 1
  if self.m_flyAnimationCounter ~= 0 then
    return
  end
  GM.UIManager:SetEventLock(false, self)
  if #self.m_rewards == 0 then
    return
  end
  local rewardCountMap = {}
  for _, reward in ipairs(self.m_rewards) do
    local rewardType = reward[PROPERTY_TYPE]
    if rewardCountMap[rewardType] == nil then
      rewardCountMap[rewardType] = 0
    end
    rewardCountMap[rewardType] = rewardCountMap[rewardType] + reward[PROPERTY_COUNT]
  end
  local formattedRewards = {}
  for reward, count in pairs(rewardCountMap) do
    table.insert(formattedRewards, {
      [PROPERTY_TYPE] = reward,
      [PROPERTY_COUNT] = count
    })
  end
  GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, formattedRewards, "rewards_window_title_normal", false)
end

function CoconutMainWindowLayer1:_UpdateContentAnimation(startProgress, endProgress, duration, ease)
  local elapsedTime = 0
  while duration > elapsedTime do
    elapsedTime = elapsedTime + CSTime.deltaTime
    local progress = DOVirtual.EasedValue(startProgress, endProgress, elapsedTime / duration, ease)
    self:_UpdateContent(progress)
    coroutine.yield()
  end
  self:_UpdateContent(endProgress)
end

function CoconutMainWindowLayer1:_GetProgress(level, score)
  local progress = level - 1
  local levelConfig = self.m_model:GetLevelConfigs()[level]
  if levelConfig ~= nil then
    Log.Assert(score < levelConfig.score, "Coconut活动实现错误")
    progress = progress + score / levelConfig.score
  end
  return progress
end

function CoconutMainWindowLayer1:_UpdateContent(progress)
  local sliderHeight = CoconutMainWindowLayer1.TrunkCellHeight * progress
  UIUtil.SetSizeDelta(self.m_progressFill, nil, sliderHeight)
  local level = math.floor(progress) + 1
  local levelConfig = self.m_model:GetLevelConfigs()[level]
  if levelConfig == nil then
    self.m_scoreNode.gameObject:SetActive(false)
  else
    UIUtil.SetAnchoredPosition(self.m_scoreNode, 0, sliderHeight)
    local progressInLevel = progress - level + 1
    self.m_scoreText.text = math.floor(levelConfig.score * progressInLevel + 0.5) .. "/" .. levelConfig.score
  end
  local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.CoconutMainWindow)
  if self.m_progress == nil then
    mainWindow:SetSceneOffset(CoconutMainWindowLayer1.TrunkCellHeight * (level - 1), 0)
  end
  if self.m_progress ~= nil then
    local lastTargetLevel = math.ceil(self.m_progress)
    if lastTargetLevel > self.m_progress and progress >= lastTargetLevel then
      for i = 1, 3 do
        self:_PlayRewardEffect(self.m_trunkCells[lastTargetLevel]:GetRewardTransform(i))
      end
      self.m_progressCells[lastTargetLevel]:PlayLevelFinishEffect()
      if mainWindow ~= nil then
        mainWindow:SetSceneOffset(CoconutMainWindowLayer1.TrunkCellHeight * lastTargetLevel, CoconutMainWindowLayer1.FlyDurationPerProgress)
      end
    end
  end
  for i = 1, level - 1 do
    self.m_trunkCells[i]:SetLevelFinished()
    self.m_progressCells[i]:SetLevelFinished()
  end
  if progress ~= 0 then
    UIUtil.SetAnchoredPosition(self.m_coconutSkeleton.transform, nil, sliderHeight + CoconutMainWindowLayer1.CoconutPositionOffset)
  end
  self.m_progress = progress
end

function CoconutMainWindowLayer1:_PlayRewardEffect(rewardTransform)
  if not rewardTransform.gameObject.activeSelf then
    return
  end
  rewardTransform:SetParent(self.m_coconutSkeleton.transform, true)
  local sequence = DOTween.Sequence()
  sequence:Append(rewardTransform:DOLocalMove(Vector3(0, 350, 0), 0.5):SetEase(Ease.InQuad))
  sequence:Insert(0, rewardTransform:DOScale(0, 0.5):SetEase(Ease.InBack))
  sequence:AppendCallback(function()
    local effectObject = Object.Instantiate(self.m_rewardEffect, self.m_coconutSkeleton.transform)
    effectObject.transform.localPosition = Vector3(0, 350, -1)
  end)
end
