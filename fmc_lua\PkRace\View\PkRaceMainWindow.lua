PkRaceMainWindow = setmetatable({}, PkRaceBaseWindow)
PkRaceMainWindow.__index = PkRaceMainWindow

function PkRaceMainWindow:Init(activityType, bAutoOpen, bEntry)
  PkRaceBaseWindow.Init(self, activityType, bAutoOpen)
  self.m_bEntry = bEntry
  self.m_ownData = nil
  self.m_competitorData = nil
  local arrPlayerData = self.m_model:GetAllPlayerData()
  for _, playerData in ipairs(arrPlayerData or {}) do
    if playerData:IsMySelf() then
      self.m_ownData = playerData
    else
      self.m_competitorData = playerData
    end
  end
  if self.m_ownData == nil or self.m_competitorData == nil then
    Log.Error("[PkRaceMainWindow] 参赛者数据出错！")
    return
  end
  self:UpdateContent()
  self:TryPlayAnim()
  self.m_model:OpenMainWindowEventCall()
  if self.m_model:GetState() == ActivityState.Ended and bAutoOpen then
    local baseSceneView = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BaseSceneView)
    baseSceneView:GetPopupChain():GetHelper(EPopupHelper.PkRace):SetNeedCheckPopup(true)
  end
  self:TryPlayEntryAnim()
  self:UpdatePerSecond()
end

function PkRaceMainWindow:OnDestroy()
  PkRaceBaseWindow.OnDestroy(self)
  if self.m_sliderSeq ~= nil then
    self.m_sliderSeq:Kill()
    self.m_sliderSeq = nil
  end
  if self.m_entrySeq ~= nil then
    self.m_entrySeq:Kill()
    self.m_entrySeq = nil
  end
  if self.m_bEventLocked then
    self.m_bEventLocked = false
    GM.UIManager:SetEventLock(false)
  end
  Scheduler.UnscheduleTarget(self)
end

function PkRaceMainWindow:UpdateContent()
  self.m_model:UpdatePlayerRank()
  local bAcquired, rewards, progressRewards = self.m_model:TryClaimReward()
  self.m_bAcquired = bAcquired
  self.m_rankRewards = rewards
  self.m_progressRewards = progressRewards
  local targetScore = self.m_model:GetTargetScore()
  self.m_ownCell:Init(self.m_ownData, targetScore, self.m_model, self)
  self.m_competitorCell:Init(self.m_competitorData, targetScore, self.m_model, self)
  if self.m_competitorData:GetLastScore() == targetScore then
    self:OnCompetitorFinished()
  end
  self.m_model:UpdateLastScore()
  local roundRewards = self.m_model:GetCurrentRoundReward()
  self.m_firstRewardContent:Init(roundRewards[1])
  self.m_secondRewardContent:Init(roundRewards[2])
  self.m_firstRewardItem = self.m_firstRewardContent:GetRewardItem(1)
  self.m_secondRewardItem = self.m_secondRewardContent:GetRewardItem(1)
  if self.m_firstRewardItem ~= nil then
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_firstRewardItem.transform)
  end
  if self.m_secondRewardItem ~= nil then
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_secondRewardItem.transform)
  end
  if 1 < self.m_ownData:GetRank() then
    local tmpPosX = self.m_firstRewardRect.position.x
    UIUtil.SetPosition(self.m_firstRewardRect, self.m_secondRewardRect.position.x)
    UIUtil.SetPosition(self.m_secondRewardRect, tmpPosX)
  end
  self:InitRoundSlider()
end

function PkRaceMainWindow:TryPlayEntryAnim()
  local effectFunc = function()
    UIUtil.SetActive(self.m_ownAvatarEffectGo, true)
    UIUtil.SetActive(self.m_competitorEffectGo, true)
    DelayExecuteFuncInView(function()
      if not self.m_bClosed then
        UIUtil.SetActive(self.m_vsEffectGo, true)
      end
    end, 1, self)
  end
  if not self.m_bEntry then
    effectFunc()
    return
  end
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPkRaceActivityVS3)
  self.m_ownCell:PlayEntryAnim()
  self.m_competitorCell:PlayEntryAnim()
  local seq = DOTween.Sequence()
  UIUtil.SetLocalScale(self.m_vsRect, 0, 0)
  seq:AppendInterval(0.6)
  seq:Append(self.m_vsRect:DOScale(Vector3(0.5, 0.5, 1), 0.3):SetEase(Ease.InCubic))
  seq:AppendCallback(function()
    UIUtil.SetActive(self.m_vsOpenEffectGo, true)
  end)
  seq:Append(self.m_vsRect:DOScale(Vector3(0.43, 0.43, 1), 0.1):SetEase(Ease.OutCubic))
  seq:AppendCallback(function()
    effectFunc()
    self.m_entrySeq = nil
  end)
  self.m_entrySeq = seq
end

function PkRaceMainWindow:OnOwnFinished()
  if self.m_ownData:GetRank() == 1 then
    UIUtil.SetActive(self.m_firstCheckGo, true)
    self.m_firstRewardCanvasGroup.alpha = 0.5
    UIUtil.SetActive(self.m_firstSuccessEffectGo, false)
    UIUtil.SetActive(self.m_firstIdleEffectGo, false)
    self.m_firstRewardRect:DOKill()
  else
    UIUtil.SetActive(self.m_secondCheckGo, true)
    UIUtil.SetActive(self.m_secondEffectGo, false)
    self.m_secondRewardCanvasGroup.alpha = 0.5
    self.m_secondRewardRect:DOKill()
  end
end

function PkRaceMainWindow:OnCompetitorFinished()
  if self.m_competitorData:GetRank() == 1 then
    self:UpdateCompetitorWinnerName()
    UIUtil.SetActive(self.m_firstCheckGo, true)
    self.m_firstRewardCanvasGroup.alpha = 0.5
    UIUtil.SetActive(self.m_firstSuccessEffectGo, false)
    UIUtil.SetActive(self.m_firstIdleEffectGo, false)
  else
    UIUtil.SetActive(self.m_secondCheckGo, true)
    UIUtil.SetActive(self.m_secondEffectGo, false)
    self.m_secondRewardCanvasGroup.alpha = 0.5
  end
end

function PkRaceMainWindow:UpdateOwnWinnerName()
  self.m_ownCell:UpdateWinnerName()
end

function PkRaceMainWindow:UpdateCompetitorWinnerName()
  self.m_competitorCell:UpdateWinnerName()
end

function PkRaceMainWindow:PlayFirstEffect()
  local targetPos = self.m_firstHeadTargetRect.position
  UIUtil.SetActive(self.m_firstHeadGo, true)
  local headRect = self.m_firstHeadGo.transform
  UIUtil.SetLocalScale(headRect, 0, 0)
  local seq = DOTween.Sequence()
  seq:Append(headRect:DOScale(Vector3(1.6, 1.6, 1), 0.3))
  seq:Append(headRect:DOScale(Vector3(1.5, 1.5, 1), 0.1))
  seq:AppendInterval(0.6)
  seq:Append(headRect:DOJump(targetPos, 1, 1, 0.4):SetEase(Ease.InCubic))
  seq:Join(headRect:DOScale(Vector3(1, 1, 1), 0.4):SetEase(Ease.OutCubic))
  seq:AppendCallback(function()
    UIUtil.SetActive(self.m_firstSuccessEffectGo, true)
    UIUtil.SetActive(self.m_firstIdleEffectGo, false)
    UIUtil.SetActive(self.m_firstHeadGo, false)
  end)
  seq:Append(self.m_firstRewardRect:DOScale(Vector3(1.2, 1.2, 1), 0.2):SetEase(Ease.InCubic))
  seq:Append(self.m_firstRewardRect:DOScale(Vector3(1, 1, 1), 0.3):SetEase(Ease.OutCubic))
end

function PkRaceMainWindow:PlayOwnRankEffect(rank)
  local bFirst = rank == 1
  local targetPos = bFirst and self.m_firstHeadTargetRect.position or self.m_secondHeadTargetRect.position
  local headGo = bFirst and self.m_firstHeadGo or self.m_secondHeadGo
  UIUtil.SetActive(headGo, true)
  local headRect = headGo.transform
  UIUtil.SetLocalScale(headRect, 0, 0)
  local seq = DOTween.Sequence()
  seq:Append(headRect:DOScale(Vector3(1.6, 1.6, 1), 0.3))
  seq:Append(headRect:DOScale(Vector3(1.5, 1.5, 1), 0.1))
  seq:AppendInterval(0.6)
  seq:Append(headRect:DOJump(targetPos, 1, 1, 0.4):SetEase(Ease.InCubic))
  seq:Insert(1, headRect:DOScale(Vector3(2, 2, 2), 0.2):SetEase(Ease.InCubic))
  seq:Insert(1.2, headRect:DOScale(Vector3(1, 1, 1), 0.2):SetEase(Ease.OutCubic))
  seq:AppendCallback(function()
    if bFirst then
      UIUtil.SetActive(self.m_firstSuccessEffectGo, true)
      UIUtil.SetActive(self.m_firstIdleEffectGo, false)
    else
      UIUtil.SetActive(self.m_secondEffectGo, true)
    end
    UIUtil.SetActive(headGo, false)
  end)
  local rewardRect = bFirst and self.m_firstRewardRect or self.m_secondRewardRect
  seq:Append(rewardRect:DOScale(Vector3(1.2, 1.2, 1), 0.2):SetEase(Ease.InCubic))
  seq:Append(rewardRect:DOScale(Vector3(1, 1, 1), 0.3):SetEase(Ease.OutCubic))
end

function PkRaceMainWindow:PlaySecondEffect()
  local targetPos = self.m_secondHeadTargetRect.position
  UIUtil.SetActive(self.m_secondHeadGo, true)
  local headRect = self.m_secondHeadGo.transform
  UIUtil.SetLocalScale(headRect, 0, 0)
  local seq = DOTween.Sequence()
  seq:Append(headRect:DOScale(Vector3(1.6, 1.6, 1), 0.3))
  seq:Append(headRect:DOScale(Vector3(1.5, 1.5, 1), 0.1))
  seq:AppendInterval(0.2)
  seq:Append(headRect:DOJump(targetPos, 1, 1, 0.4):SetEase(Ease.InCubic))
  seq:Join(headRect:DOScale(Vector3(1, 1, 1), 0.4):SetEase(Ease.OutCubic))
  seq:AppendCallback(function()
    UIUtil.SetActive(self.m_secondEffectGo, true)
    UIUtil.SetActive(self.m_secondHeadGo, false)
    self.m_secondRewardRect:DOScale(1.1, 0.2):SetLoops(-1, LoopType.Yoyo)
  end)
end

function PkRaceMainWindow:TryClose()
  if self.m_bPlayAnim then
    self.m_bGoing2Closed = true
    return
  end
  self:Close()
end

function PkRaceMainWindow:TryPlayAnim()
  local animNum = 0
  local callback = function()
    animNum = animNum - 1
    if animNum == 0 then
      GM.UIManager:SetEventLock(false)
      if self.m_bAcquired and not Table.IsEmpty(self.m_rankRewards) then
        local startPos = V3Zero
        if self.m_ownData:GetRank() == 1 then
          if self.m_firstRewardItem ~= nil then
            startPos = self.m_firstRewardItem:GetIcon().transform.position
          end
        elseif self.m_secondRewardItem ~= nil then
          startPos = self.m_secondRewardItem:GetIcon().transform.position
        end
        GM.UIManager:OpenView(UIPrefabConfigName.PkRaceRewardWindow, self.m_ownData:GetRank(), self.m_rankRewards, function()
          if self.gameObject ~= nil and not self.gameObject:IsNull() then
            self:PlayRoundSliderAnim()
          end
        end, startPos)
        self:OnOwnFinished()
      elseif self.m_bGoing2Closed then
        self.m_bPlayAnim = false
        self:Close()
      end
    end
  end
  local delayTime = 0
  if self.m_ownCell:CanPlayAnim() and self.m_competitorCell:CanPlayAnim() and self.m_ownCell:GetCurScore() == self.m_competitorCell:GetCurScore() then
    local ownTime = self.m_ownCell:GetAnimTime()
    local competitorTime = self.m_competitorCell:GetAnimTime()
    delayTime = math.abs(ownTime - competitorTime) + 0.5
  end
  if self.m_ownCell:TryPlayAnim(callback, delayTime) then
    animNum = animNum + 1
  end
  if self.m_competitorCell:TryPlayAnim(callback, delayTime) then
    animNum = animNum + 1
  end
  if 0 < animNum then
    GM.UIManager:SetEventLock(true)
    self.m_bPlayAnim = true
  end
end

function PkRaceMainWindow:InitRoundSlider()
  local arrConfig, maxRound = self.m_model:GetRoundProgressRewardConfigs()
  local sliderRect = self.m_roundSlider.transform
  local maxWidth = sliderRect.sizeDelta.x - 10
  local intervalWidth = maxWidth / maxRound
  local currentRound = self.m_model:GetCurrentRound()
  self.m_mapRound2RewardBox = {}
  local boxGo, tbRewardBox
  for _, rewardConfig in ipairs(arrConfig) do
    boxGo = GameObject.Instantiate(self.m_rewardBoxGo, self.m_rewardBoxGo.transform.parent)
    UIUtil.SetActive(boxGo, true)
    UIUtil.SetAnchoredPosition(boxGo.transform, rewardConfig.round * intervalWidth + 10)
    tbRewardBox = boxGo:GetLuaTable()
    tbRewardBox:Init(rewardConfig, rewardConfig.round, maxRound, currentRound > rewardConfig.round, self)
    self.m_mapRound2RewardBox[rewardConfig.round] = tbRewardBox
  end
  self.m_roundSlider.value = (currentRound - 1) / maxRound
  self.m_maxRound = maxRound
  self.m_curRoundText.text = currentRound - 1
end

function PkRaceMainWindow:PlayRoundSliderAnim()
  self.m_bEventLocked = true
  GM.UIManager:SetEventLock(true)
  local seq = DOTween.Sequence()
  local flyTime = 0.6
  local currentRound = self.m_model:GetCurrentRound()
  seq:AppendInterval(2)
  seq:InsertCallback(1.6, function()
    UIUtil.SetActive(self.m_medalEffectGo, true)
  end)
  UIUtil.SetActive(self.m_medalGo, true)
  UIUtil.SetLocalScale(self.m_medalGo.transform, 0, 0)
  seq:Append(self.m_medalGo.transform:DOScale(Vector3(1, 1, 1), 0.2))
  seq:AppendCallback(function()
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPkRaceActivityStar)
  end)
  seq:Append(self.m_medalGo.transform:DOMove(self.m_targetMedalRect.position, flyTime):SetEase(Ease.InOutSine))
  seq:AppendCallback(function()
    UIUtil.SetActive(self.m_medalGo, false)
    DOTween.Sequence():Append(self.m_targetMedalRect:DOScale(Vector3(1.2, 1.2, 1), 0.1)):Append(self.m_targetMedalRect:DOScale(Vector3(1, 1, 1), 0.2))
    UIUtil.SetActive(self.m_medalBoomEffectGo, true)
    self.m_curRoundText.text = self.m_model:GetCurrentRound()
  end)
  seq:Append(self.m_roundSlider:DOValue(currentRound / self.m_maxRound, 0.8):SetEase(Ease.InOutSine))
  seq:AppendInterval(0.5)
  seq:AppendCallback(function()
    GM.UIManager:SetEventLock(false)
    self.m_bEventLocked = false
    if self.m_mapRound2RewardBox[currentRound] ~= nil then
      self.m_mapRound2RewardBox[currentRound]:UpdateContent(true)
    end
    if not Table.IsEmpty(self.m_progressRewards) then
      GM.UIManager:OpenView(UIPrefabConfigName.PkRaceRoundRewardWindow, self.m_model:GetCurrentRound(), self.m_progressRewards, function()
        self:OnFinishRound()
      end)
    else
      self:OnFinishRound()
    end
    self.m_sliderSeq = nil
  end)
  self.m_sliderSeq = seq
end

function PkRaceMainWindow:OnFinishRound()
  self.m_bPlayAnim = false
  self:Close()
  local model = self.m_model
  if model:GetState() == ActivityState.Started and not model:HasFinishedAllRounds() and not model:IsDeadline() then
    GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, true, true)
  end
end

function PkRaceMainWindow:ShowRewardTip(rewards, rectTrans, offsetX, offsetY)
  self.m_rewardTip:Show(rewards, rectTrans, offsetX, offsetY, true)
end

function PkRaceMainWindow:HideRewardTip()
  self.m_rewardTip:Hide()
end

function PkRaceMainWindow:OnCloseBtnClick()
  PkRaceBaseWindow.OnCloseBtnClick(self)
  self:HideRewardTip()
end

function PkRaceMainWindow:OnHelpBtnClick()
  GM.UIManager:OpenView(self.m_activityDefinition.HelpWindowPrefabName, self.m_activityType)
  self:HideRewardTip()
end

function PkRaceMainWindow:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  if self.m_model:GetState() == ActivityState.Ended then
    UIUtil.SetActive(self.m_countdownGo, false)
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  else
    self:Close()
  end
end

function PkRaceMainWindow:OnBtnClicked()
  self:Close()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    GM.SceneManager:ChangeGameMode(EGameMode.Board)
  end
end

function PkRaceMainWindow:GetCompetitorTutorialRect()
  return self.m_competitorTutorialRect
end

function PkRaceMainWindow:GetRankRewardTutorialRect()
  return self.m_rankRewardTutorialRect
end

function PkRaceMainWindow:GetRoundRewardTutorialRect()
  return self.m_roundRewardTutorialRect
end

PkRacePlayerCell = {}
PkRacePlayerCell.__index = PkRacePlayerCell

function PkRacePlayerCell:Init(playerData, targetScore, model, window)
  self.m_playerData = playerData
  self.m_nameText.text = playerData:GetUserName()
  self.m_userAvatar:SetAvatar(EAvatarFrame.PkRace, playerData:GetIcon())
  self.m_model = model
  self.m_window = window
  self.m_originItemHeight = self.m_cakeItemMoveRect.transform.sizeDelta.y
  self.m_lastScore = playerData:GetLastScore() or 0
  self.m_curScore = playerData:GetCurScore(model:GetGapTime())
  self.m_targetScore = targetScore
  UIUtil.SetActive(self.m_cakeItemRect.gameObject, false)
  self:InitCakeLayers()
  self:UpdateContentByScore(self.m_lastScore)
  if self:CanPlayAnim() then
    UIUtil.SetActive(self.m_winnerNameGo, false)
  else
    self:UpdateWinnerName()
  end
end

function PkRacePlayerCell:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  if self.m_entrySeq ~= nil then
    self.m_entrySeq:Kill()
    self.m_entrySeq = nil
  end
end

function PkRacePlayerCell:UpdateWinnerName()
  local bWinner = self.m_curScore == self.m_targetScore and self.m_playerData:GetRank() == 1
  UIUtil.SetActive(self.m_winnerNameGo, bWinner)
  UIUtil.SetActive(self.m_nameText.gameObject, not bWinner)
end

function PkRacePlayerCell:InitCakeLayers()
  local rootRect = self.m_cakeRootRect
  self.m_arrCakeLayers = {}
  self.m_layerCount = rootRect.childCount
  local targetScore = self.m_targetScore
  local tbLayer
  for i = 1, self.m_layerCount do
    tbLayer = rootRect:GetChild(i - 1).gameObject:GetLuaTable()
    tbLayer:Init(i, self.m_layerCount, targetScore)
    table.insert(self.m_arrCakeLayers, tbLayer)
  end
end

function PkRacePlayerCell:UpdateContentByScore(score, bAnim, endScore)
  for _, cakeLayer in ipairs(self.m_arrCakeLayers) do
    cakeLayer:UpdateByScore(score, bAnim, endScore)
  end
  self:UpdateContentForAnim(score)
  if bAnim then
    self.m_animScore = score
  end
end

function PkRacePlayerCell:GetCurAnimLayerIndex()
  local score = self.m_animScore or self.m_lastScore
  local layerRatio = 1 / self.m_layerCount
  local curRatio = score / self.m_targetScore
  local layerNum = math.min(curRatio // layerRatio + 1, self.m_layerCount)
  return layerNum
end

function PkRacePlayerCell:UpdateContentForAnim(score)
  local ratio = score / self.m_targetScore
  UIUtil.SetSizeDelta(self.m_cakeItemMoveRect, nil, (1 - ratio) * self.m_originItemHeight)
  self:UpdateScoreText(math.ceil(score))
end

function PkRacePlayerCell:UpdateScoreText(score)
  self.m_tokenText.text = string.format("<color=#63bd27>%d</color>/%d", score, self.m_targetScore)
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_tokenText.transform.parent)
end

function PkRacePlayerCell:CanPlayAnim()
  return self.m_curScore ~= self.m_lastScore
end

function PkRacePlayerCell:GetCurScore()
  return self.m_curScore
end

function PkRacePlayerCell:TryPlayAnim(callback, delayTime)
  if self.m_curScore == self.m_lastScore then
    return false
  end
  local seq = DOTween.Sequence()
  local delay = 0
  if 0 < delayTime and self.m_playerData:GetRank() > 1 then
    delay = delayTime
  end
  local lastRatio = self.m_lastScore / self.m_targetScore
  local curRatio = self.m_curScore / self.m_targetScore
  local layerRatio = 1 / self.m_layerCount
  local minLayerNum = math.min(lastRatio // layerRatio + 1, self.m_layerCount)
  local bPlaySpine = curRatio >= minLayerNum * layerRatio
  local animTime = self:GetAnimTime()
  seq:AppendInterval(delay)
  seq:AppendCallback(function()
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPkRaceActivityCakeMake)
    DOVirtual.Float(self.m_lastScore, self.m_curScore, animTime, function(x)
      self:UpdateContentForAnim(x)
      self:UpdateContentByScore(x, true, self.m_curScore)
    end):SetEase(Ease.InQuad):OnComplete(function()
      self:StopCakeItemCircleAnim()
    end)
    self:PlayCakeItemCircleAnim()
  end)
  local waitTime = 0
  local bSelf = self.m_playerData:IsMySelf()
  local bFinished = self.m_curScore == self.m_targetScore
  if bFinished and bSelf then
    waitTime = waitTime + 2.5
  end
  seq:AppendInterval(animTime)
  seq:AppendCallback(function()
    local pos = self.m_cakeItemRect.position
    local delayTime = bPlaySpine and 0.7 or 0
    UIUtil.SetPosition(self.m_animEndEffectGo.transform, nil, pos.y - 30)
    DelayExecuteFuncInView(function()
      UIUtil.SetActive(self.m_animEndEffectGo, true)
      if bFinished then
        GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPkRaceActivityCakeComplete)
        UIUtil.SetActive(self.m_successEffectGo, true)
        UIUtil.SetActive(self.m_successEffectGo2, true)
        if bSelf then
          self.m_window:PlayOwnRankEffect(self.m_playerData:GetRank())
          if self.m_playerData:GetRank() == 1 then
            self.m_window:UpdateOwnWinnerName()
          end
        else
          self.m_window:OnCompetitorFinished()
        end
      end
    end, delayTime, self)
  end)
  seq:AppendInterval(waitTime)
  seq:AppendCallback(function()
    self.m_entrySeq = nil
    if callback ~= nil then
      callback()
    end
  end)
  self.m_entrySeq = seq
  return true
end

function PkRacePlayerCell:GetAnimTime()
  local totalTime = 2
  local minTime = 1
  local lastRatio = self.m_lastScore / self.m_targetScore
  local curRatio = self.m_curScore / self.m_targetScore
  local totalRatio = curRatio - lastRatio
  local animTime = math.max(totalTime * totalRatio, 1)
  return animTime
end

function PkRacePlayerCell:PlayCakeItemCircleAnim()
  UIUtil.SetActive(self.m_cakeItemRect.gameObject, true)
  local rect = self.m_cakeItemRect
  local a = 30
  local b = 10
  local duration = 1
  self.m_circleAnim = DOTween.To(function()
    return 0
  end, function(x)
    rect.localPosition = Vector3(a * Mathf.Cos(x), b * Mathf.Sin(x), 0)
  end, 2 * Mathf.PI, duration):SetEase(Ease.Linear):SetLoops(-1, LoopType.Restart)
end

function PkRacePlayerCell:StopCakeItemCircleAnim()
  UIUtil.SetActive(self.m_cakeItemRect.gameObject, false)
  if self.m_circleAnim ~= nil then
    self.m_circleAnim:Kill()
    self.m_circleAnim = nil
  end
  UIUtil.SetActive(self.m_cakeItemRect.gameObject, false)
end

function PkRacePlayerCell:PlayEntryAnim()
  local bSelf = self.m_playerData:IsMySelf()
  local originPosX = bSelf and -1000 or 1000
  local firstPosX = bSelf and 20 or -20
  local rect = self.m_avatarMoveRect
  UIUtil.SetAnchoredPosition(rect, originPosX)
  local seq = DOTween.Sequence()
  seq:AppendInterval(0)
  seq:Append(rect:DOAnchorPosX(firstPosX, 0.6):SetEase(Ease.InCubic))
  seq:Append(rect:DOAnchorPosX(0, 0.2):SetEase(Ease.OutCubic))
end

local CakeAnimState = {
  init = "0",
  appear = "1",
  idle = "2"
}
PkRaceCakeLayer = {}
PkRaceCakeLayer.__index = PkRaceCakeLayer

function PkRaceCakeLayer:Init(index, maxLayerNum, targetScore)
  self.m_index = index
  self.m_maxLayerNum = maxLayerNum
  self.m_targetScore = targetScore
  self.m_minRatio = (self.m_index - 1) / self.m_maxLayerNum
  self.m_maxRatio = self.m_index / self.m_maxLayerNum
  self.m_maxHeight = self.transform.sizeDelta.y
  self.m_minHeight = tonumber(self.m_originHeight)
  self.m_spine:Initialize()
end

function PkRaceCakeLayer:OnDestroy()
  Scheduler.UnscheduleTarget(self)
end

function PkRaceCakeLayer:UpdateByScore(score, bAnim, endScore)
  local scoreRatio = score / self.m_targetScore
  UIUtil.SetActive(self.gameObject, scoreRatio > self.m_minRatio)
  UIUtil.SetSizeDelta(self.transform, nil, self:GetTargetHeightByScore(score))
  local bActive = self.m_decorRootGo.activeSelf
  UIUtil.SetActive(self.m_decorRootGo, scoreRatio >= self.m_maxRatio)
  if bAnim and self.m_decorRootGo.activeSelf and not bActive then
    local deltaRatio = (endScore - score) / self.m_targetScore
    local speed = 1 + (5 - deltaRatio // 0.2) * 0.2
    DelayExecuteFuncInView(function()
      UIUtil.SetActive(self.m_topRect.gameObject, false)
    end, 1 / speed, self)
    self.m_spine.timeScale = speed
    self.m_spine.AnimationState:SetAnimation(0, self:GetAnimName(CakeAnimState.appear), false)
    self.m_spine.AnimationState:AddAnimation(0, self:GetAnimName(CakeAnimState.idle), false, 0)
  end
end

function PkRaceCakeLayer:GetTargetHeightByScore(score)
  local scoreRatio = score / self.m_targetScore
  if scoreRatio >= self.m_maxRatio then
    return self.m_maxHeight
  elseif scoreRatio > self.m_minRatio then
    return (scoreRatio - self.m_minRatio) / (self.m_maxRatio - self.m_minRatio) * (self.m_maxHeight - self.m_minHeight) + self.m_minHeight
  end
  return self.m_minHeight
end

function PkRaceCakeLayer:GetAnimName(state)
  return "0_appear_" .. state
end

function PkRaceCakeLayer:GetIndex()
  return self.m_index
end

PkRaceRewardBox = {}
PkRaceRewardBox.__index = PkRaceRewardBox

function PkRaceRewardBox:Init(rewardConfig, round, maxRound, bAcquired, window)
  self.m_rewardConfig = rewardConfig
  self.m_window = window
  SpriteUtil.SetImage(self.m_icon, rewardConfig.uiCode or "dig_chest_1")
  if round == maxRound then
    UIUtil.SetLocalScale(self.m_icon.transform, 0.35, 0.35)
  end
  self.m_roundNumText.text = round
  self:UpdateContent(bAcquired)
end

function PkRaceRewardBox:UpdateContent(bAcquired)
  UIUtil.SetActive(self.m_checkGo, bAcquired)
  self.m_button.enabled = not bAcquired
end

function PkRaceRewardBox:OnBtnClicked()
  self.m_window:ShowRewardTip(self.m_rewardConfig.roundRewards, self.m_icon.transform, 0, 40)
end
