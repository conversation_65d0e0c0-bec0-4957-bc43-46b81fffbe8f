local Step = {HighlightOrder = "1", ItemDetailWindow = "2"}
local EStep2TextKey = {
  [Step.HighlightOrder] = "tutorial_item_detail_1",
  [Step.ItemDetailWindow] = "tutorial_item_detail_2"
}
local EStep2TextAnchorPercent = {
  [Step.HighlightOrder] = 40,
  [Step.ItemDetailWindow] = 80
}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnViewOpened)
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  local order = self:_GetValidFixedOrder()
  if order == nil then
    return
  end
  self:_ExecuteStep1(order)
  return true
end

function Executer:_OnViewOpened(msg)
  if self.m_strOngoingDatas == Step.HighlightOrder and (msg.name == UIPrefabConfigName.ItemDetailWindow or msg.name == UIPrefabConfigName.ItemDishDetailWindow) and self.m_orderCell then
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    self.m_strOngoingDatas = Step.ItemDetailWindow
    TutorialHelper.DehighlightOrder(self.m_orderCell)
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
    self.m_model:SetTutorialFinished(self:GetTutorialId())
    local callback = function()
      self:Finish()
    end
    TutorialHelper.SetMaskAlphaOnce(0)
    TutorialHelper.WholeMask(callback)
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  end
end

function Executer:_ExecuteStep1(order)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.HighlightOrder
  self:_SaveOngoingDatas()
  local orderCell = TutorialHelper.HighlightOrder(order)
  self.m_orderCell = orderCell
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(self.m_orderCell:GetIcon(1).transform)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_GetValidFixedOrder()
  local orders = GM.MainBoardModel:GetOrders()
  for _, order in pairs(orders) do
    if order:GetType() == OrderType.Fixed and order:GetState() ~= OrderState.CanDeliver then
      local boardView = MainBoardView.GetInstance()
      if boardView ~= nil then
        local orderArea = boardView:GetOrderArea()
        local orderCell = orderArea:GetCell(order)
        if not orderArea:IsPlayingOrderAnimation() and orderCell ~= nil then
          return order
        end
      end
    end
  end
  return nil
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
