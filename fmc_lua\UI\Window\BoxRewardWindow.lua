BoxRewardWindow = setmetatable({disableWindowMaskOnOpenView = 2, disableEffectWhenCloseView = false}, RewardWindow)
BoxRewardWindow.__index = BoxRewardWindow
EBoxRewardType = {
  Green = 1,
  Blue = 2,
  <PERSON> = 3,
  <PERSON> = 4,
  Box = 5
}
local arrRewardPos = {
  [1] = {
    Vector.Create(0, 0)
  },
  [2] = {
    Vector.Create(-120, 0),
    Vector.Create(120, 0)
  },
  [3] = {
    Vector.Create(-220, 0),
    Vector.Create(0, 0),
    Vector.Create(220, 0)
  },
  [4] = {
    Vector.Create(-300, 0),
    Vector.Create(-100, 0),
    Vector.Create(100, 0),
    Vector.Create(300, 0)
  },
  [5] = {
    Vector.Create(-220, 140),
    Vector.Create(0, 140),
    Vector.Create(220, 140),
    Vector.Create(-120, -140),
    Vector.Create(120, -140)
  },
  [6] = {
    Vector.Create(-300, 140),
    Vector.Create(-100, 140),
    Vector.Create(100, 140),
    Vector.Create(300, 140),
    Vector.Create(-120, -140),
    Vector.Create(120, -140)
  },
  [7] = {
    Vector.Create(-300, 140),
    Vector.Create(-100, 140),
    Vector.Create(100, 140),
    Vector.Create(300, 140),
    Vector.Create(-220, -140),
    Vector.Create(0, -140),
    Vector.Create(220, -140)
  }
}

function BoxRewardWindow:Init(position, scaleX, eBoxRewardType, arrRewards, titleTextKey, playRewardAnim, initCallback, closeCallback)
  arrRewards = RewardApi.FilterRewards(arrRewards)
  RewardWindow.Init(self, arrRewards, titleTextKey, playRewardAnim, initCallback, closeCallback)
  local originalPos = self.m_boxRoot.anchoredPosition
  self.m_boxRoot.position = position
  self.m_boxRoot:SetLocalScaleXY(scaleX)
  self.m_boxRoot:DOAnchorPos(originalPos, 0.3):SetDelay(0.2)
  self.m_boxRoot:DOScale(1, 0.3):SetDelay(0.2)
  self:SetSprite(eBoxRewardType)
  local rewardCount = #arrRewards
  for i = 1, rewardCount do
    local arrPos = arrRewardPos[rewardCount]
    local pos = arrPos and arrPos[i] or arrRewardPos[1][1]
    local rewardItem = self.m_rewardContent:GetRewardItem(i)
    rewardItem.transform:SetLocalPosXY(pos.x, pos.y)
  end
  if IsAutoRun() then
    DOVirtual.DelayedCall(0.2, function()
      self:OnWindowMaskClicked()
    end)
  end
end

function BoxRewardWindow:SetSprite(eBoxRewardType)
  local spriteName
  if Table.Contain(EBoxRewardType, eBoxRewardType) then
    spriteName = "task_gift" .. eBoxRewardType
  else
    spriteName = eBoxRewardType
  end
  SpriteUtil.SetImage(self.m_boxImg, ImageFileConfigName[spriteName], true)
end

function BoxRewardWindow:OnDestroy()
  RewardWindow.OnDestroy(self)
  GM.UIManager:RemoveAllEventLocks(self)
end
