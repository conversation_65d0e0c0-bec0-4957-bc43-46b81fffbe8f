StoreFailedReason = {CannotStore = 1, InventoryFull = 2}
MainBoardModel = setmetatable({}, BaseSceneBoardModel)
MainBoardModel.__index = MainBoardModel

function MainBoardModel:Init()
  ModelHelper.DefineSyncData(self, "ItemData", GM.DBTableManager:GetTable(EDBTableConfigs.Item))
  ModelHelper.DefineSyncData(self, "ItemLayerData", GM.DBTableManager:GetTable(EDBTableConfigs.Board))
  ModelHelper.DefineSyncData(self, "ItemCacheData", GM.DBTableManager:GetTable(EDBTableConfigs.CacheItem))
  ModelHelper.DefineSyncData(self, "ItemStoreData", GM.DBTableManager:GetTable(EDBTableConfigs.Inventory))
  ModelHelper.DefineSyncData(self, "OrderMetaData", GM.DBTableManager:GetTable(EDBTableConfigs.OrderMeta))
  ModelHelper.DefineSyncData(self, "OrderData", GM.DBTableManager:GetTable(EDBTableConfigs.Order))
  local itemManager = ItemManager.Create(self.m_itemData, self)
  local itemCacheModel = ItemCacheModel.Create(self.m_itemCacheData, itemManager:GetIdGenerator())
  local orderModel = MainOrderModel.Create(self.m_orderMetaData, self.m_orderData, self)
  BaseSceneBoardModel.Init(self, EGameMode.Board, itemManager, itemCacheModel, 7, 9, orderModel)
  self.m_itemStoreModel = ItemStoreModel.Create(self.m_itemStoreData, itemManager)
end

function MainBoardModel:_CreateItemLayerModel()
  return SceneItemLayerModel.Create(self, self.m_itemLayerData, self.m_itemManager, "BoardModelConfig")
end

function MainBoardModel:LoadFileConfig()
  BaseSceneBoardModel.LoadFileConfig(self)
  self.m_itemStoreModel:LoadFileConfig()
end

function MainBoardModel:OnSyncDataFinished()
  self.m_itemManager:OnSyncDataFinished()
  self.m_itemStoreModel:OnSyncDataFinished()
  local noItem = self.m_itemManager:IsEmpty() or self.m_itemStoreModel:GetItemCount() <= 0
  self.m_itemLayerModel:OnSyncDataFinished(noItem)
  self.m_itemCacheModel:OnSyncDataFinished()
  self.m_orderModel:OnSyncDataFinished()
  self:UpdateOrderState()
  self:UpdateOpeningItem()
end

function MainBoardModel:LateInit()
  self.m_orderModel:LateInit()
  self:_RemoveEQPiece()
  self:CheckItemLeak()
  INVENTORY_COLUMN_COUNT = 5
  INVENTORY_ROW_COUNT = 12
  INVENTORY_MAX_COUNT = INVENTORY_ROW_COUNT * INVENTORY_COLUMN_COUNT
end

function MainBoardModel:UpdatePerSecond()
  if self.m_gameMode ~= GM.SceneManager:GetGameMode() then
    return
  end
  BaseSceneBoardModel.UpdatePerSecond(self)
  local item, cookCmp
  for i = 1, self.m_itemStoreModel:GetItemCount() do
    item = self.m_itemStoreModel:GetItem(i)
    if item ~= nil then
      cookCmp = item:GetComponent(ItemCook)
      if cookCmp then
        cookCmp:UpdatePerSecond()
      end
    end
  end
end

function MainBoardModel:_RemoveEQPiece()
  local number = GM.MiscModel:GetInNumber(EMiscKey.EQPieceRemove)
  if 0 < number then
    Log.Info("厨具碎片处理：已处理过")
    return
  end
  local finished = self.m_orderModel:IsOrderGroupFinished(1, 4)
  if not finished then
    GM.MiscModel:Set(EMiscKey.EQPieceRemove, 1)
    Log.Info("厨具碎片处理：订单组进度小于1_4（未领1_4的奖励），无需处理")
    return
  end
  local level = GM.LevelModel:GetCurrentLevel()
  if 4 <= level then
    GM.MiscModel:Set(EMiscKey.EQPieceRemove, 2)
    Log.Info("厨具碎片处理：等级大于等于4级（已经领取了4级的奖励），无需处理")
    return
  end
  self:BatchRemoveItemsOfItemType("eq_1_1")
  GM.MiscModel:Set(EMiscKey.EQPieceRemove, 3)
  Log.Info("厨具碎片处理：订单组进度大于等于1_4（领取了1_4的奖励），但等级小于4级（未领4级的奖励），将玩家棋盘/缓存队列/仓库中的eq_1_1棋子移除")
end

function MainBoardModel:GetCodeCountMap(includeBoard, includeCache, includeStore)
  local codeCountMap = BaseSceneBoardModel.GetCodeCountMap(self, includeBoard, includeCache, includeStore)
  includeStore = includeStore ~= false
  if includeStore then
    local item
    for i = 1, self.m_itemStoreModel:GetItemCount() do
      item = self.m_itemStoreModel:GetItem(i)
      if item ~= nil then
        local code = item:GetCode()
        if codeCountMap[code] == nil then
          codeCountMap[code] = 0
        end
        codeCountMap[code] = codeCountMap[code] + 1
      end
    end
  end
  return codeCountMap
end

function MainBoardModel:FilterItems(filter, limitCount, includeStore)
  local arrItems = BaseBoardModel.FilterItems(self, filter, limitCount)
  local count = #arrItems
  if limitCount and limitCount <= count then
    return arrItems
  end
  if includeStore then
    local item
    for i = 1, self.m_itemStoreModel:GetItemCount() do
      item = self.m_itemStoreModel:GetItem(i)
      if item ~= nil and filter(item) then
        arrItems[#arrItems + 1] = item
        count = count + 1
        if limitCount and limitCount <= count then
          return arrItems
        end
      end
    end
  end
  return arrItems
end

function MainBoardModel:FilterItemsByType(itemType, filter, limitCount, includeStore)
  local arrItems = BaseBoardModel.FilterItemsByType(self, itemType, filter, limitCount)
  local count = #arrItems
  if limitCount and limitCount <= count then
    return arrItems
  end
  if includeStore then
    for item, _ in pairs(self.m_itemStoreModel:GetItemsByType(itemType, false)) do
      if filter(item) then
        arrItems[#arrItems + 1] = item
        count = count + 1
        if limitCount and limitCount <= count then
          return arrItems
        end
      end
    end
  end
  return arrItems
end

function MainBoardModel:GetStoreModel()
  return self.m_itemStoreModel
end

function MainBoardModel:StoreItem(item)
  if not self:CanItemStore(item) then
    self.event:Call(BoardEventType.StoreFailed, {
      Item = item,
      Reason = StoreFailedReason.CannotStore
    })
    return false
  end
  local bProducerItem = false
  if self:IsProducerInventoryOpen() then
    local day = self:GetProducerOpenDayByType(item:GetType())
    if day ~= nil and self:HasUnlockedPDSlot(day) then
      bProducerItem = true
    end
  end
  if not bProducerItem and self:IsInventoryFull() then
    self.event:Call(BoardEventType.StoreFailed, {
      Item = item,
      Reason = StoreFailedReason.InventoryFull
    })
    return false
  end
  self:_SetItem(item:GetPosition(), nil, false)
  item:SetPosition(nil, true)
  self.m_itemStoreModel:AddItem(item)
  self:UpdateOrderState()
  GM.BIManager:LogAction(EBIType.StoreItem, tostring(item:GetCode()))
  self.event:Call(BoardEventType.StoreItem, {Source = item})
  EventDispatcher.DispatchEvent(EEventType.ItemStored)
  return true
end

function MainBoardModel:SortInventory()
  self.m_itemStoreModel:Sort()
end

function MainBoardModel:GetStoredDuplicateItems(copy)
  return self.m_itemStoreModel:GetDuplicateItems(copy)
end

function MainBoardModel:RetrieveStoredItemByItemModel(itemModel)
  local index = self.m_itemStoreModel:GetIndexByItemModel(itemModel)
  if 0 < index then
    return self:RetrieveStoredItem(index)
  end
end

function MainBoardModel:RetrieveStoredItem(index)
  if self:IsBoardFull() then
    return false
  end
  local position = self.m_itemLayerModel:FindEmptyPositionFromBottom2Top()
  if position == nil then
    return false
  end
  local item = self.m_itemStoreModel:GetItem(index)
  if item == nil then
    Log.Assert(false, "item不能为空")
    return false
  end
  self.m_itemStoreModel:RemoveItem(index)
  self:_SetItem(position, item)
  item:SetPosition(position)
  GM.BIManager:LogAction(EBIType.RetrieveItem, tostring(item:GetCode()))
  self.event:Call(BoardEventType.RetrieveStoredItem, {Source = item})
  EventDispatcher.DispatchEvent(EEventType.ItemRetrieved)
  self:_Try2LogBoardFull()
  return true
end

function MainBoardModel:SwapStoredItem(itemModel1, itemModel2)
  self.m_itemStoreModel:SwapItem(itemModel1, itemModel2)
end

function MainBoardModel:RepositionStoredItem(itemModel, index)
  self.m_itemStoreModel:RepositionStoredItem(itemModel, index)
end

function MainBoardModel:GetStoredItem(index)
  return self.m_itemStoreModel:GetItem(index)
end

function MainBoardModel:GetStoredItemCount()
  return self.m_itemStoreModel:GetItemCount()
end

function MainBoardModel:IsInventoryFull()
  return self:GetStoredItemCount() >= self:GetStoreSlotCount()
end

function MainBoardModel:BuyStoreSlot()
  local slotConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.InventorySlot)
  local cap = GM.MiscModel:GetInventoryBoughtCapInNumber() + 1
  if GM.PropertyDataManager:Consume(EPropertyType.Gem, slotConfig.UnlockCost[cap], EBIType.BuyInventorySlot, false, cap) then
    GM.MiscModel:SetInventoryBoughtCap(cap)
    EventDispatcher.DispatchEvent(EEventType.InventoryNewSlot)
    return true
  end
  return false
end

function MainBoardModel:GetStoreSlotCount()
  local slotConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.InventorySlot)
  local boughtCount = GM.MiscModel:GetInventoryBoughtCapInNumber()
  local levelCount = GM.MiscModel:GetInventoryLevelCapInNumber()
  local total = boughtCount + levelCount + slotConfig.DefaultNumber
  local maxCount = INVENTORY_MAX_COUNT
  total = math.min(maxCount, total)
  local producerItemNum = 0
  if self:IsProducerInventoryOpen() then
    local item, day
    for i = 1, self.m_itemStoreModel:GetItemCount() do
      item = self.m_itemStoreModel:GetItem(i)
      if self:CanStoreItemInProducer(item:GetType()) then
        producerItemNum = producerItemNum + 1
      end
    end
    total = total + producerItemNum
  end
  if IsAutoRun() and GM.TestAutoRunModel.useInventory and total <= self:GetStoredItemCount() then
    total = self:GetStoredItemCount() + 1
  end
  return total, producerItemNum
end

function MainBoardModel:CanItemStore(item)
  if not self:CanItemMove(item) then
    return false
  end
  if item:GetComponent(ItemBubble) ~= nil then
    return false
  end
  if item:GetComponent(ItemRewardBubble) ~= nil then
    return false
  end
  local itemSpread = item:GetComponent(ItemSpread)
  if itemSpread ~= nil then
    local spreadState = itemSpread:GetState()
    if spreadState == ItemSpreadState.Opening then
      return false
    end
  end
  return true
end

function MainBoardModel:GetNewStoredItems()
  return Table.ShallowCopy(self.m_itemStoreModel:GetNewAddedItems())
end

function MainBoardModel:ClearNewStoredItems()
  self.m_itemStoreModel:ClearNewAddedItems()
end

function MainBoardModel:GetStoredItemsByType(itemType, copy)
  return self.m_itemStoreModel:GetItemsByType(itemType, copy)
end

function MainBoardModel:GetFirstStoredItemByType(itemType)
  local mapCode = self.m_itemStoreModel:GetItemsByType(itemType, false)
  if not Table.IsEmpty(mapCode) then
    for item, _ in pairs(mapCode) do
      return item
    end
  end
end

function MainBoardModel:GetProducerInventoryConfig()
  return self.m_itemStoreModel:GetProducerInventoryConfig()
end

function MainBoardModel:GetUnlockedPDInventoryConfig()
  local arrConfig = {}
  for _, config in ipairs(self:GetProducerInventoryConfig() or {}) do
    if self:HasUnlockedPDSlot(config.day) then
      table.insert(arrConfig, config)
    end
  end
  return arrConfig
end

function MainBoardModel:HasUnlockedPDSlot(targetDay)
  local curDay = self:GetCurOrderDay()
  return targetDay < curDay or curDay == targetDay and self.m_orderModel:HasClaimedGroupReward()
end

function MainBoardModel:GetProducerOpenDayByType(type)
  return self.m_itemStoreModel:GetProducerOpenDayByType(type)
end

function MainBoardModel:GetProducerTypeByOpenDay(day)
  return self.m_itemStoreModel:GetProducerTypeByOpenDay(day)
end

function MainBoardModel:CanStoreItemInProducer(itemType)
  local day = self:GetProducerOpenDayByType(itemType)
  return day ~= nil and self:HasUnlockedPDSlot(day)
end

function MainBoardModel:IsProducerInventoryOpen()
  local InventoryProducerInfo = self:GetProducerInventoryConfig()
  for k, v in pairs(InventoryProducerInfo) do
    if self:HasUnlockedPDSlot(v.day) then
      return true
    end
  end
  return false
end

function MainBoardModel:FinishOrder(order, checkError)
  self:_Try2AddPiggyBankGem(order)
  GM.TaskManager:AddTaskCleanGold(order:GetCleanGoldCount())
  local userLevel = GM.LevelModel:GetCurrentLevel()
  BaseSceneBoardModel.FinishOrder(self, order, checkError)
  if userLevel < GM.LevelModel:GetCurrentLevel() or self.m_orderModel:CanClaimGroupReward() then
    if GM.ConfigModel:CanNewOrderRewardAnimationSkip() then
      return
    end
    GM.UIManager:SetEventLockUntilNextPopup()
  end
end

function MainBoardModel:RemoveOrderRequirementItems(order, checkError)
  local removeInfo = {}
  local removedItemsFromBoard = {}
  local removedItemsFromInventory = {}
  local removedCellIndexFromBoard = {}
  local removedCellIndexFromInventory = {}
  local itemDataModel = GM.ItemDataModel
  local countMap, arrOrder = order:GetUniqueRequirements()
  for i, requirement in ipairs(arrOrder) do
    local itemRemoved = false
    local left = countMap[requirement]
    if self.m_itemStoreModel ~= nil then
      local itemsInInventory = self.m_itemStoreModel:GetItemsByType(requirement, true)
      local isDisposableInstrument = itemDataModel:IsDisposableInstrument(requirement)
      for item, _ in pairs(itemsInInventory) do
        if not isDisposableInstrument or item:IsEmptyInstrument() then
          self.m_itemStoreModel:RemoveItemByItemModel(item)
          item:Destroy()
          self:_RemoveItemProperty(item)
          itemRemoved = true
          removedItemsFromInventory[#removedItemsFromInventory + 1] = item
          removedCellIndexFromInventory[#removedCellIndexFromInventory + 1] = i
          left = left - 1
          if left <= 0 then
            break
          end
        end
      end
    end
    if itemRemoved then
      EventDispatcher.DispatchEvent(EEventType.ItemRetrieved)
    end
    if 0 < left then
      local code
      for item, _ in pairs(self:GetAllBoardItems(true)) do
        code = item:GetCode()
        if code == requirement and (not itemDataModel:IsDisposableInstrument(code) or item:IsEmptyInstrument()) then
          self:RemoveItem(item)
          itemRemoved = true
          removedItemsFromBoard[#removedItemsFromBoard + 1] = item
          removedCellIndexFromBoard[#removedCellIndexFromBoard + 1] = i
          left = left - 1
          if left <= 0 then
            break
          end
        end
      end
    end
    if 0 < left and checkError ~= false then
      GM.BIManager:LogErrorInfo(EBIType.FinishOrderRemoveItemError, requirement)
    end
  end
  removeInfo.RemovedFromBoard = removedItemsFromBoard
  removeInfo.RemovedFromInventory = removedItemsFromInventory
  removeInfo.RemovedCellIndexFromBoard = removedCellIndexFromBoard
  removeInfo.RemovedCellIndexFromInventory = removedCellIndexFromInventory
  return removeInfo
end

function MainBoardModel:_Try2AddPiggyBankGem(order)
  local gemNum = order:GetPiggyBankAccumulateGemNum()
  if gemNum == nil or gemNum <= 0 then
    return
  end
  GM.ActivityManager:GetModel(ActivityType.PiggyBank):Accumulate(gemNum)
end

function MainBoardModel:GetCurOrderDay()
  return self.m_orderModel:GetCurOrderDay()
end

function MainBoardModel:_BatchRemoveItems(itemFilter, typeFilter)
  local removedItems = {}
  for item, _ in pairs(self:GetAllBoardItems(true)) do
    if itemFilter(item) then
      self:RemoveItem(item)
      table.insert(removedItems, item)
    end
  end
  local i = 1
  while i <= self:GetStoredItemCount() do
    local item = self:GetStoredItem(i)
    if item == nil then
      Log.Assert(false, "item不能为空")
    end
    if item ~= nil and itemFilter(item) then
      self.m_itemStoreModel:RemoveItem(i)
      item:Destroy()
      self:_RemoveItemProperty(item)
    else
      i = i + 1
    end
  end
  local i = 1
  while i <= self:GetCachedItemCount() do
    local code = self:GetCachedItem(i)
    if typeFilter(code) then
      self:RemoveCachedItem(i)
    else
      i = i + 1
    end
  end
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
  EventDispatcher.DispatchEvent(EEventType.ItemRetrieved)
  return removedItems
end

function MainBoardModel:BatchRemoveItemsOfItemType(itemType)
  local typeFilter = function(type)
    return type == itemType
  end
  local itemFilter = function(item)
    local itemBubble = item:GetComponent(ItemBubble)
    local type = itemBubble == nil and item:GetType() or itemBubble:GetInnerItemCode()
    return typeFilter(type)
  end
  local removedItems = self:_BatchRemoveItems(itemFilter, typeFilter)
  self.event:Call(BoardEventType.BatchRemoveItems, {Removed = removedItems})
end

function MainBoardModel:TransformItems(chainId)
  local typeNumberMap = {}
  local recordType = function(type)
    if typeNumberMap[type] == nil then
      typeNumberMap[type] = 0
    end
    typeNumberMap[type] = typeNumberMap[type] + 1
  end
  local typeFilter = function(type)
    return GM.ItemDataModel:GetChainId(type) == chainId
  end
  local removedItems = {}
  for item, _ in pairs(self:GetAllBoardItems(true)) do
    if typeFilter(item:GetType()) then
      self:RemoveItem(item)
      recordType(item:GetType())
      table.insert(removedItems, item)
    else
      local itemBubble = item:GetComponent(ItemBubble)
      if itemBubble ~= nil and typeFilter(itemBubble:GetInnerItemCode()) then
        self:RemoveItem(item)
        table.insert(removedItems, item)
      end
    end
  end
  local i = 1
  while i <= self:GetStoredItemCount() do
    local item = self:GetStoredItem(i)
    if item ~= nil and typeFilter(item:GetType()) then
      self.m_itemStoreModel:RemoveItem(i)
      item:Destroy()
      self:_RemoveItemProperty(item)
      recordType(item:GetType())
    else
      i = i + 1
    end
  end
  i = 1
  while i <= self:GetCachedItemCount() do
    local code = self:GetCachedItem(i)
    if typeFilter(code) then
      self:RemoveCachedItem(i)
      recordType(code)
    else
      i = i + 1
    end
  end
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
  EventDispatcher.DispatchEvent(EEventType.ItemRetrieved)
  GM.UIManager:CloseView(UIPrefabConfigName.InventoryWindow)
  self.event:Call(BoardEventType.BatchRemoveItems, {Removed = removedItems})
  local score = 0
  for type, number in pairs(typeNumberMap) do
    local config = GM.ItemDataModel:GetModelConfig(type)
    local rewards = config.CollectRewards
    score = score + rewards[1][PROPERTY_COUNT] * number
  end
  return score
end

function MainBoardModel:GetAllItemCookCmp(includBoard, includeStore)
  local arrResult = BaseSceneBoardModel.GetAllItemCookCmp(self, includBoard)
  if includeStore == false then
    return arrResult
  end
  for i = 1, self.m_itemStoreModel:GetItemCount() do
    local item = self.m_itemStoreModel:GetItem(i)
    if item ~= nil then
      local itemCook = item:GetComponent(ItemCook)
      if itemCook ~= nil then
        arrResult[#arrResult + 1] = itemCook
      end
    end
  end
  return arrResult
end

function MainBoardModel:SellItem(item, inBoard)
  if inBoard == false then
    self.m_itemStoreModel:RemoveItemByItemModel(item)
    EventDispatcher.DispatchEvent(EEventType.OnInventoryItemSold, item)
  end
  BaseSceneBoardModel.SellItem(self, item, inBoard)
end

function MainBoardModel:UndoSellItem(item, inBoard)
  BaseSceneBoardModel.UndoSellItem(self, item, inBoard)
  if inBoard == false then
    self.m_itemStoreModel:AddItem(item)
    EventDispatcher.DispatchEvent(EEventType.OnInventoryItemUndoSell, item)
  end
end

function MainBoardModel:_GetItemsIncludeBoardStoreAndCook()
  local items = {}
  local cookCmps = {}
  local itemCook
  for item, _ in pairs(self:GetAllBoardItems()) do
    table.insert(items, item)
    itemCook = item:GetComponent(ItemCook)
    if itemCook ~= nil then
      table.insert(cookCmps, itemCook)
    end
  end
  local item
  for i = 1, self.m_itemStoreModel:GetItemCount() do
    item = self.m_itemStoreModel:GetItem(i)
    if item ~= nil then
      table.insert(items, item)
      itemCook = item:GetComponent(ItemCook)
      if itemCook ~= nil then
        table.insert(cookCmps, itemCook)
      end
    end
  end
  local changeCountMap = {}
  local materialIds, materialId, isCooked, code
  for _, itemCook in ipairs(cookCmps) do
    materialIds = itemCook:GetMaterialIds()
    isCooked = itemCook:GetState() == EItemCookState.Cooked
    if isCooked then
      code = itemCook:GetRecipe()
      changeCountMap[code] = (changeCountMap[code] or 0) + 1
    end
    for _, materialId in ipairs(materialIds) do
      item = self.m_itemManager:GetItem(materialId)
      if item then
        table.insert(items, item)
        if isCooked then
          code = item:GetCode()
          changeCountMap[code] = (changeCountMap[code] or 0) - 1
        end
      end
    end
  end
  return items, changeCountMap
end

function MainBoardModel:GetItemsEnergyCost()
  local energyCost = 0
  local codeCountMap = {}
  local items, changeCountMap = self:_GetItemsIncludeBoardStoreAndCook()
  local code
  for _, item in ipairs(items) do
    code = item:GetCode()
    codeCountMap[code] = (codeCountMap[code] or 0) + 1
    energyCost = energyCost + (item.costEnergy or 0) + (item.costEnergyCurDay or 0)
  end
  local cost
  for i = 1, self.m_itemCacheModel:GetItemCount() do
    code, cost = self.m_itemCacheModel:GetItem(i)
    cost = cost or {}
    if code ~= nil then
      codeCountMap[code] = (codeCountMap[code] or 0) + 1
      energyCost = energyCost + (cost.costEnergy or 0) + (cost.costEnergyCurDay or 0)
    end
  end
  for code, count in pairs(changeCountMap) do
    codeCountMap[code] = (codeCountMap[code] or 0) + count
    if codeCountMap[code] == 0 then
      codeCountMap[code] = nil
    end
  end
  return energyCost, codeCountMap
end

function MainBoardModel:SetCurDayEnergyCostToPastDay()
  self.m_itemCacheModel:SetCurDayEnergyCostToPastDay()
  local items = self:_GetItemsIncludeBoardStoreAndCook()
  for _, item in ipairs(items) do
    if item.costEnergyCurDay and item.costEnergyCurDay > 0 then
      item.costEnergy = (item.costEnergy or 0) + (item.costEnergyCurDay or 0)
      item.costEnergyCurDay = 0
      self:SaveItemProperty(item)
    end
  end
end

function MainBoardModel:GetMergeAllIgnoreItems()
  local mergeAllIgnoredItems = BaseSceneBoardModel.GetMergeAllIgnoreItems(self)
  local boardView = MainBoardView.GetInstance()
  local promptIgnoreItems = boardView:GetPromptIgnoreItems() or {}
  for item, _ in pairs(promptIgnoreItems) do
    mergeAllIgnoredItems[item] = true
  end
  return mergeAllIgnoredItems
end

function MainBoardModel:CheckItemLeak()
  local boardItems = self.m_itemLayerModel:GetAllItems()
  local inventoryItems = {}
  local item
  for i = 1, self.m_itemStoreModel:GetItemCount() do
    item = self.m_itemStoreModel:GetItem(i)
    if item ~= nil then
      inventoryItems[item] = true
    end
  end
  local inCookItems = {}
  for _, cookCmp in ipairs(self:GetAllItemCookCmp(true, true)) do
    for _, inCookItem in ipairs(cookCmp:GetMaterialItems()) do
      inCookItems[inCookItem] = true
    end
  end
  local arrLeakedItems = {}
  for _, item in ipairs(self.m_itemManager:GetAllItems()) do
    if not boardItems[item] and not inventoryItems[item] and not inCookItems[item] then
      arrLeakedItems[#arrLeakedItems + 1] = item
      item:Destroy()
      self:_RemoveItemProperty(item)
    end
  end
  local leakedItem = arrLeakedItems[1]
  if leakedItem then
    local info = leakedItem:GetId() .. "," .. leakedItem:GetCode()
    for i = 2, #arrLeakedItems do
      leakedItem = arrLeakedItems[i]
      info = info .. "#" .. leakedItem:GetId() .. "," .. leakedItem:GetCode()
    end
    if GameConfig.IsTestMode() then
      Log.Error("泄露了 " .. #arrLeakedItems .. " 个棋子！" .. info)
    end
    GM.BIManager:LogProject(EBIProjectType.ItemLeak, info)
  else
    Log.Info("未发生棋子泄露。")
  end
end
