BaseUIItemBookEntry = {}
BaseUIItemBookEntry.__index = BaseUIItemBookEntry

function BaseUIItemBookEntry:Init(activityType)
  self.m_activityType = activityType
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_bookModel = self.m_model:GetBoardModel():GetItemIllustratedBook()
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  if self.m_bookModel == nil then
    UIUtil.SetActive(self.gameObject, false)
    return
  end
  self:RefreshExclamation()
  AddHandlerAndRecordMap(self.m_bookModel.event, BaseUIItemIllustratedBookModel.EventType.Refreshed, {
    obj = self,
    method = self.RefreshExclamation
  })
end

function BaseUIItemBookEntry:OnDestroy()
  if self.m_bookModel ~= nil then
    RemoveAllHandlers(self.m_bookModel.event, self)
  end
end

function BaseUIItemBookEntry:OnButtonClicked()
  GM.UIManager:OpenView(self.m_activityDefinition.BookWindowPrefabName, self.m_activityType, self.m_bookModel, true)
end

function BaseUIItemBookEntry:RefreshExclamation()
  UIUtil.SetActive(self.m_exclamationGo, self.m_bookModel:CanShowRedPoint())
end
