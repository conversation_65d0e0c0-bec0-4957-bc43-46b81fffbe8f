return {
  {
    Id = "10010",
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_1_2", Count = 1}
  },
  {
    Id = "10020",
    PreId = {"10010"},
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_1_3", Count = 1}
  },
  {
    Id = "10030",
    PreId = {"10010"},
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_1_5", Count = 1}
  },
  {
    Id = "10040",
    PreId = {"10020", "10030"},
    GroupId = 1,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_2_1", Count = 1}
  },
  {
    Id = "10050",
    PreId = {"10040"},
    GroupId = 1,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_2_2", Count = 1},
    Requirement_2 = {Type = "it_1_1_3", Count = 1}
  },
  {
    Id = "10060",
    PreId = {"10040"},
    GroupId = 1,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_2_3", Count = 1},
    Requirement_2 = {Type = "it_1_1_4", Count = 1}
  },
  {
    Id = "10061",
    PreId = {"10050", "10060"},
    GroupId = 1,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_1_2", Count = 1}
  },
  {
    Id = "10070",
    GroupId = 2,
    ChapterId = 1,
    Requirement_1 = {Type = "it_2_1_1", Count = 1}
  },
  {
    Id = "10080",
    GroupId = 2,
    ChapterId = 1,
    Requirement_1 = {Type = "it_2_1_2", Count = 1}
  },
  {
    Id = "10081",
    PreId = {"10070", "10080"},
    GroupId = 2,
    ChapterId = 1,
    Requirement_1 = {Type = "it_2_1_3", Count = 1}
  },
  {
    Id = "10090",
    GroupId = 3,
    ChapterId = 1,
    Requirement_1 = {Type = "it_2_1_3", Count = 1},
    Requirement_2 = {Type = "it_1_2_3", Count = 1}
  },
  {
    Id = "10100",
    GroupId = 3,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_1_5", Count = 1},
    Requirement_2 = {Type = "it_2_1_2", Count = 1}
  },
  {
    Id = "10110",
    PreId = {"10090", "10100"},
    GroupId = 3,
    ChapterId = 1,
    Requirement_1 = {Type = "it_2_1_5", Count = 1}
  },
  {
    Id = "10120",
    PreId = {"10110"},
    GroupId = 4,
    ChapterId = 1,
    Requirement_1 = {Type = "it_2_1_4", Count = 1}
  },
  {
    Id = "10130",
    PreId = {"10110"},
    GroupId = 4,
    ChapterId = 1,
    Requirement_1 = {Type = "it_2_2_3", Count = 1}
  },
  {
    Id = "10140",
    PreId = {"10120", "10130"},
    GroupId = 4,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_2_4", Count = 1}
  },
  {
    Id = "10150",
    PreId = {"10120", "10130"},
    GroupId = 4,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_1_1_1", Count = 1}
  },
  {
    Id = "10160",
    GroupId = 5,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_1_6", Count = 1}
  },
  {
    Id = "10170",
    GroupId = 5,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_2_1_1", Count = 1}
  },
  {
    Id = "10180",
    PreId = {"10160", "10170"},
    GroupId = 5,
    ChapterId = 1,
    Requirement_1 = {
      Type = "ds_chopve_1",
      Count = 1
    }
  },
  {
    Id = "10190",
    PreId = {"10180"},
    GroupId = 5,
    ChapterId = 1,
    Requirement_1 = {Type = "it_2_3_2", Count = 1}
  },
  {
    Id = "10200",
    GroupId = 6,
    ChapterId = 1,
    Requirement_1 = {Type = "it_2_3_1_2", Count = 1}
  },
  {
    Id = "10210",
    PreId = {"10200"},
    GroupId = 6,
    ChapterId = 1,
    Requirement_1 = {
      Type = "ds_chopve_2",
      Count = 1
    }
  },
  {
    Id = "10220",
    PreId = {"10210"},
    GroupId = 6,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_1_5", Count = 1},
    Requirement_2 = {Type = "it_2_2_3", Count = 1}
  },
  {
    Id = "10230",
    PreId = {"10210"},
    GroupId = 6,
    ChapterId = 1,
    Requirement_1 = {
      Type = "ds_chopve_3",
      Count = 1
    }
  },
  {
    Id = "10240",
    PreId = {"10210"},
    GroupId = 6,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_2_3", Count = 1},
    Requirement_2 = {Type = "it_2_1_4", Count = 1}
  },
  {
    Id = "10250",
    GroupId = 7,
    ChapterId = 1,
    Requirement_1 = {Type = "it_2_1_5", Count = 1},
    Requirement_2 = {Type = "it_2_3_2", Count = 1}
  },
  {
    Id = "10260",
    GroupId = 7,
    ChapterId = 1,
    Requirement_1 = {Type = "ds_flb_1", Count = 1},
    Rewards = {
      {Currency = "energy", Amount = 10}
    },
    Flambe = 1
  },
  {
    Id = "10270",
    PreId = {"10250", "10260"},
    GroupId = 7,
    ChapterId = 1,
    Requirement_1 = {
      Type = "ds_chopve_4",
      Count = 1
    }
  },
  {
    Id = "10280",
    PreId = {"10250", "10260"},
    GroupId = 7,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_1_1_1", Count = 1}
  },
  {
    Id = "10290",
    PreId = {"10250", "10260"},
    GroupId = 7,
    ChapterId = 1,
    Requirement_1 = {Type = "it_1_2_1_1", Count = 1},
    Requirement_2 = {Type = "it_2_2_3", Count = 1}
  }
}
