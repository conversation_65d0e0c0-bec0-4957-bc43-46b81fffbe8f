LanguageSettingWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
LanguageSettingWindow.__index = LanguageSettingWindow

function LanguageSettingWindow:Init()
  local options = LocalizationModel:GetLanguageOptions()
  local currentLanguage = LocalizationModel:GetCurLanguageInString()
  for i = 1, options.Length do
    local language = options[i - 1]:ToString()
    if language == currentLanguage then
      GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.LanguageCurrentCell), self.m_contentRectTrans, V3Zero, function(go)
        go.transform:SetSiblingIndex(i - 1)
        local cell = go:GetLuaTable()
        cell:Init(GM.GameTextModel:GetText("language_" .. LocalizationModel:GetLanguageAbbreviation(language) .. "_button"))
      end)
    else
      GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.LanguageOptionCell), self.m_contentRectTrans, V3Zero, function(go)
        go.transform:SetSiblingIndex(i - 1)
        local cell = go:GetLuaTable()
        cell:Init(GM.GameTextModel:GetText("language_" .. LocalizationModel:GetLanguageAbbreviation(language) .. "_button"), function()
          self:OpenConfirmWindow(language)
        end)
      end)
    end
  end
end

function LanguageSettingWindow:OpenConfirmWindow(language)
  GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "language_change_confirm_window_title", "language_change_confirm_desc", "language_change_confirm_button_yes", "language_change_confirm_button_no", function()
    LocalizationModel:ChangeLanguageWithString(language)
    GM:RestartGame(ERestartType.WithLocalization, EBIProjectType.RestartGameAction.Language)
  end, nil, true)
end
