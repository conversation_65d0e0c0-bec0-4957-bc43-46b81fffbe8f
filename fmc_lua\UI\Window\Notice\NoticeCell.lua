NoticeCell = {}
NoticeCell.__index = NoticeCell

function NoticeCell:Init(notice)
  self.m_notice = notice
  self.m_titleText.text = notice.Title
  self.m_releaseTimeText.text = TimeUtil.ToDate(notice.ReleaseTime, ETimeFormat.MDY)
  self:_UpdateDisappearTime()
  self:UpdateState()
end

function NoticeCell:UpdatePerSecond()
  self:_UpdateDisappearTime()
end

function NoticeCell:_UpdateDisappearTime()
  local delta = self.m_notice.DisappearTime - GM.GameModel:GetServerTime()
  delta = math.max(delta, 0)
  local time, unit = TimeUtil.ToNearestUnit(delta)
  local msg = "<color=#F26286>" .. time .. unit .. "</color>"
  self.m_disappearTimeText.text = GM.GameTextModel:GetText("mail_window_end_time", msg)
end

function NoticeCell:UpdateState()
  local state = GM.NoticeModel:GetNoticeState(self.m_notice)
  if self.m_notice.Rewards ~= nil then
    SpriteUtil.SetImage(self.m_iconImage, ImageFileConfigName.notice_gift, true)
  elseif state == NoticeState.Read then
    SpriteUtil.SetImage(self.m_iconImage, ImageFileConfigName.notice_letter_open, true)
  else
    SpriteUtil.SetImage(self.m_iconImage, ImageFileConfigName.notice_letter_close, true)
  end
  self.m_readMaskGo:SetActive(state == NoticeState.Read)
  self.m_exclamationGo:SetActive(state ~= NoticeState.Read)
end

function NoticeCell:OnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.NoticeContentWindow, self.m_notice)
end
