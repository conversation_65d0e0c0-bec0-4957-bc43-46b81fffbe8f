RemoveBubbleConfirmWindow = setmetatable({}, TwoButtonWindow)
RemoveBubbleConfirmWindow.__index = RemoveBubbleConfirmWindow

function RemoveBubbleConfirmWindow:BeforeOpenCheck(itemModel, confirmCallback)
  if itemModel:GetComponent(ItemBubble) == nil then
    return false
  end
  if itemModel:GetBoardModel():GetItem(itemModel:GetPosition()) ~= itemModel then
    return false
  end
  return true
end

function RemoveBubbleConfirmWindow:Init(itemModel, confirmCallback)
  self:_AdjustSize()
  local spriteName = GM.ItemDataModel:GetSpriteName(itemModel:GetComponent(ItemBubble):GetInnerItemCode())
  SpriteUtil.SetImage(self.m_itemImg, spriteName, true)
  self.m_itemModel = itemModel
  self.m_redButtonCallback = confirmCallback
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.UserClick
  })
end

function RemoveBubbleConfirmWindow:OnRedClick()
  if self.m_redButtonCallback then
    self.m_redButtonCallback(self.m_itemModel)
  end
  self:OnCloseBtnClick()
end
