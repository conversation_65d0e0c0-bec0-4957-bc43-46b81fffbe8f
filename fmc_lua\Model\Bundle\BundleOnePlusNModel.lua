BundleOnePlusNModel = setmetatable({}, BundleChainModel)
BundleOnePlusNModel.__index = BundleOnePlusNModel
local DBKey = {ClaimedIndexes = "clmd_i"}

function BundleOnePlusNModel:_LoadLocalConfigs(...)
  BundleChainModel._LoadLocalConfigs(self, ...)
  local data
  for _, dataGroup in ipairs(self.m_dataGroups) do
    for index = 1, #dataGroup:GetBundleIds() do
      data = dataGroup:GetConfigDataByIndex(index)
      if index == 1 then
        Log.Assert(not data:IsFree(), "1+N礼包 " .. data:GetBundleId() .. " 第一项必须为付费！")
      else
        Log.Assert(data:IsFree(), "1+N礼包 " .. data:GetBundleId() .. " 非第一项必须为免费！")
      end
      if data:IsCircle() then
        Log.Error("1+N礼包禁止配置循环！" .. data:GetBundleId())
      end
    end
  end
end

function BundleOnePlusNModel:GetNCount(dataGroup)
  if not dataGroup then
    Log.Error("礼包逻辑错误")
    return 1
  end
  return #dataGroup:GetBundleIds() - 1
end

function BundleOnePlusNModel:_IsGroupDataEligible(dataGroup)
  if not dataGroup then
    return false
  end
  if self:GetRestDuration(dataGroup) >= 0 then
    for _, bundleId in ipairs(dataGroup:GetBundleIds()) do
      local index = dataGroup:GetConfigDataIndexByBundleId(bundleId)
      if not self:IsIndexClaimed(dataGroup, index) then
        return true
      end
    end
  end
  return BundleChainModel._IsGroupDataEligible(self, dataGroup)
end

function BundleOnePlusNModel:BuyBundle(dataGroup, index, callback)
  local bundleData = dataGroup:GetConfigDataByIndex(index)
  self:_BuyBundle(dataGroup, bundleData, callback)
end

function BundleOnePlusNModel:_OnBuyFinished(dataGroup, bundleId)
  local index = dataGroup:GetConfigDataIndexByBundleId(bundleId)
  if 0 < index then
    self:_ClaimIndex(dataGroup, index)
  end
  if self:GetCurChainStep(dataGroup) == 2 then
    self:_RecordBuyNumData(dataGroup)
  end
  if self:HasPurchaseFinished(dataGroup) then
    self:OnBundleStateChanged(dataGroup)
    self:_FinishAndEnterBuyCD(dataGroup)
  end
end

function BundleOnePlusNModel:OnRestoreIAPRewards(eIAPType)
  local success, dataGroup = BundleChainModel.OnRestoreIAPRewards(self, eIAPType)
  if success and dataGroup then
    self:_ClaimIndex(dataGroup, 1)
    local step = self:GetCurChainStep(dataGroup)
    Log.Info("1+N礼包补单 step:" .. step)
    if 2 < step then
      local bundleCount = #dataGroup:GetBundleIds()
      for index = 2, bundleCount do
        self:_ClaimIndex(dataGroup, index)
      end
    end
  end
  return success
end

function BundleOnePlusNModel:_OnBundleTriggered(dataGroup)
  BundleChainModel._OnBundleTriggered(self, dataGroup)
  self:_ClearClaim(dataGroup)
end

function BundleOnePlusNModel:_GetRecordPurchaseRewards(dataGroup, bIAPBefore, step)
  local rewards = {}
  local mapClaimedIndexes = self:_GetClaimedIndexes(dataGroup)
  local bundleCount = #dataGroup:GetBundleIds()
  local startIndex = 1
  if step and 2 <= step then
    startIndex = 2
  end
  for index = startIndex, bundleCount do
    local data = dataGroup:GetConfigDataByIndex(index)
    if not mapClaimedIndexes[index] then
      Table.ListAppend(rewards, data:GetGoods())
    end
  end
  return rewards, bundleCount + 1
end

function BundleOnePlusNModel:TryRecoverFreeRewards(dataGroup, bImmediately)
  if BundleChainModel.TryRecoverFreeRewards(self, dataGroup, bImmediately) then
    local bundleCount = #dataGroup:GetBundleIds()
    for index = 1, bundleCount do
      self:_ClaimIndex(dataGroup, index)
    end
  end
end

local CLAIM_INDEX_SEPARATOR = ","

function BundleOnePlusNModel:_ClaimIndex(dataGroup, index)
  local mapClaimedIndexes = self:_GetClaimedIndexes(dataGroup)
  mapClaimedIndexes[index] = true
  local arr = Table.GetKeys(mapClaimedIndexes)
  table.sort(arr)
  local str = table.concat(arr, CLAIM_INDEX_SEPARATOR)
  self:_SetBundleDBData(dataGroup:GetGroupId(), DBKey.ClaimedIndexes, str)
end

function BundleOnePlusNModel:_GetClaimedIndexes(dataGroup)
  local str = self:_GetBundleDBData(dataGroup:GetGroupId(), DBKey.ClaimedIndexes) or ""
  local arrIndexes = StringUtil.SplitToNum(str, CLAIM_INDEX_SEPARATOR)
  local mapClaimedIndexes = {}
  for _, index in ipairs(arrIndexes) do
    mapClaimedIndexes[index] = true
  end
  return mapClaimedIndexes
end

function BundleOnePlusNModel:_ClearClaim(dataGroup)
  self:_SetBundleDBData(dataGroup:GetGroupId(), DBKey.ClaimedIndexes, "")
end

function BundleOnePlusNModel:IsIndexClaimed(dataGroup, index)
  local mapClaimedIndexes = self:_GetClaimedIndexes(dataGroup)
  return mapClaimedIndexes[index] == true
end
