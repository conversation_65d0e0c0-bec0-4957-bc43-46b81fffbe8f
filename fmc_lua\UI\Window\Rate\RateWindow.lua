RateWindow = setmetatable({disableEffectWhenCloseView = true}, BaseWindow)
RateWindow.__index = RateWindow
local RATE_STAR_COUNT = 5
local HAPPY_STAR_COUNT = 5

function RateWindow:Init()
  GM.RateModel:OnWindowPopup()
  self.m_spine:Init()
  self.m_lightSpine:Init()
  self.m_lightEffectGo:SetActive(false)
  self.m_star = {}
  for i = 1, RATE_STAR_COUNT do
    self.m_star[i] = self["m_star" .. i]
    self.m_star[i]:Init(i, self)
  end
  self.m_confirmButton:SetUIEnabled(false)
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.AutoPopup
  })
end

function RateWindow:OnDestroy()
  Scheduler.UnscheduleTarget(self)
end

function RateWindow:OnConfirmBtnClicked()
  if not self.m_curStarCount then
    GM.UIManager:ShowPromptWithKey("rate_us_tips")
    return
  end
  GM.BIManager:LogAction(EBIType.RateConfirm, tostring(self.m_curStarCount))
  if self.m_curStarCount >= HAPPY_STAR_COUNT then
    GM.RateModel:FinishRate()
    CSPlatform:OpenURL(GM.RateModel:GetRateLink())
  elseif GM.RateModel:WhetherJumpToCustomerCenterWhenGiveBadReview() then
    GM.SDKHelper:OpenCustomerCenter()
  end
  GM.RateModel:SetConfigTriggered()
  self:Close()
end

function RateWindow:OnStarClicked(index)
  for i = 1, index do
    self.m_star[i]:UpdateContent(true)
  end
  for i = index + 1, RATE_STAR_COUNT do
    self.m_star[i]:UpdateContent(false)
  end
  self.m_curStarCount = index
  self.m_confirmButton:SetUIEnabled(self.m_curStarCount ~= nil)
  if index >= HAPPY_STAR_COUNT and not self.m_bHappy then
    self.m_bHappy = true
    self.m_spine:SetAnimation("over_anim", false)
    self.m_spine:AddAnimation("expression2_loop", true)
    DelayExecuteFuncInView(function()
      self.m_lightSpine.gameObject:SetActive(true)
    end, 1, self, false)
    DelayExecuteFuncInView(function()
      self.m_lightEffectGo:SetActive(true)
    end, 2.2, self, false)
  end
end

function RateWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  self:LogWindowAction(EBIType.UIActionType.Close, {
    EBIReferType.AutoPopup
  })
end

RateStarCell = {}
RateStarCell.__index = RateStarCell

function RateStarCell:Init(index, window)
  self.m_index = index
  self.m_window = window
  self:UpdateContent(false)
  UIUtil.UpdateSortingOrder(self.m_effectGo, window:GetSortingOrder() + 1)
end

function RateStarCell:UpdateContent(isOn)
  local changed = self.m_bOn ~= isOn
  self.m_bOn = isOn
  self.m_enableImg.enabled = self.m_bOn
  self.m_disableImg.enabled = not self.m_bOn
  if isOn and changed then
    self.m_effectGo:SetActive(isOn)
  end
end

function RateStarCell:OnClicked()
  self.m_window:OnStarClicked(self.m_index)
end
