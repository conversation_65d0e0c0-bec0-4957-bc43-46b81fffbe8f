local Step = {ClickRefill = "1"}
local EStep2TextKey = {
  [Step.ClickRefill] = "tutorial_out_of_energy"
}
local EStep2TextAnchorPercent = {
  [Step.ClickRefill] = 80
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.OnViewWillClose, self, self._OnCloseView)
end

function Executer:_OnOpenView(msg)
  if msg.name == UIPrefabConfigName.BuyEnergyWindow and GM.EnergyModel:GetEnergy(EnergyType.Main) == 0 then
    if not GM.ShopModel:IsEnergyFreeRefill(EnergyType.Main) then
      self:Finish()
      return
    end
    self:LogTutorialStepFinish(TutorialExecuter.StepStart)
    self:_ExecuteStep1()
  end
end

function Executer:_OnCloseView(msg)
  if msg.name == UIPrefabConfigName.BuyEnergyWindow and self.btnRectTrans then
    TutorialHelper.DehighlightForUI(self.btnRectTrans)
    self:Finish(self.m_gesture)
  end
end

function Executer:_ExecuteStep1()
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickRefill
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  local window = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BuyEnergyWindow)
  if not window then
    self:Finish()
    return
  end
  self.btnRectTrans = window:GetBtnRectTrans()
  TutorialHelper.HighlightForUI(self.btnRectTrans)
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(self.btnRectTrans)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
