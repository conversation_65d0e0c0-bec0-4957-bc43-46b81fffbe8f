function DEFINE_ITEM_EVENT(model, eventName)
  local functionName = eventName .. "Item"
  
  local dispatchFunctionName = "On" .. eventName
  model[functionName] = function(model, item, ...)
    item:DispatchComponentEvent(dispatchFunctionName, ...)
  end
end

BaseBoardModel = {}
BaseBoardModel.__index = BaseBoardModel
BaseBoardModel.TileSize = 130

function BaseBoardModel:Init(width, height)
  self.event = PairEvent.Create(self)
  self.m_horizontalTiles = width
  self.m_verticalTiles = height
  self.m_itemLayerModel = self:_CreateItemLayerModel()
  DEFINE_ITEM_EVENT(self, "Tap")
end

function BaseBoardModel:Destroy()
end

function BaseBoardModel:_CreateItemLayerModel()
  Log.Error("_CreateItemlayerModel() 是个抽象接口")
end

function BaseBoardModel:GetGameMode()
  Log.Error("GetGameMode()是抽象接口")
end

function BaseBoardModel:CreateMatrix()
  Log.Assert(false, "CreateMatrix()是抽象接口")
end

function BaseBoardModel:_CreateMatrix()
  local matrix = setmetatable({}, Matrix)
  matrix:Init(self)
  return matrix
end

function BaseBoardModel:_CreatePosition(x, y)
  local position = setmetatable({}, BoardPosition)
  position:Init(self, x, y)
  return position
end

function BaseBoardModel:_CreatePositionFromLocalPosition(localPositionX, localPositionY)
  local x = math.floor(localPositionX) // BaseBoardModel.TileSize + 1
  local y = self:GetVerticalTiles() - math.floor(localPositionY) // BaseBoardModel.TileSize
  return self:_CreatePosition(x, y)
end

function BaseBoardModel:GetVerticalTiles()
  return self.m_verticalTiles
end

function BaseBoardModel:GetHorizontalTiles()
  return self.m_horizontalTiles
end

function BaseBoardModel:Update()
  for item, _ in pairs(self:GetAllBoardItems(true)) do
    item:DispatchComponentUpdateEvent()
  end
end

function BaseBoardModel:UpdatePerSecond()
  for item, _ in pairs(self:GetAllBoardItems(true)) do
    item:DispatchComponentEvent("UpdatePerSecond")
  end
end

function BaseBoardModel:SaveItemProperty(item)
end

function BaseBoardModel:_RemoveItemProperty(item)
  if item == nil then
    Log.Error("remove item is nil")
    return
  end
  local position = item:GetPosition()
  if position ~= nil and self.m_itemLayerModel:GetItem(item:GetPosition()) == item then
    Log.Error("Should remove from ItemLayerModel BEFORE remove from ItemManager. type:" .. tostring(item:GetType()) .. " pos:" .. tostring(item:GetPosition()))
  end
end

function BaseBoardModel:GetItem(position)
  return self.m_itemLayerModel:GetItem(position)
end

function BaseBoardModel:_SetItem(position, item, autoUpdateOrderState)
  self.m_itemLayerModel:SetItem(position, item)
end

function BaseBoardModel:FilterItems(filter, limitCount, includeStore)
  return self.m_itemLayerModel:FilterItems(filter, limitCount)
end

function BaseBoardModel:FilterItemsByType(itemType, filter, limitCount, includeStore)
  return self.m_itemLayerModel:FilterItemsByType(itemType, filter, limitCount)
end

function BaseBoardModel:GetOneBoardItemByType(itemType)
  return self.m_itemLayerModel:GetOneBoardItemByType(itemType)
end

function BaseBoardModel:IsBoardFull()
  return not self.m_itemLayerModel:HasEmptyPosition()
end

function BaseBoardModel:GetEmptyPositionCount()
  return self.m_itemLayerModel:GetEmptyPositionCount()
end

function BaseBoardModel:GetAllBoardItems(copy)
  local items = self.m_itemLayerModel:GetAllItems()
  if copy then
    return Table.ShallowCopy(items)
  end
  return items
end

function BaseBoardModel:GetBoardItemCountByType(itemType)
  return self.m_itemLayerModel:GetItemCountByType(itemType)
end

function BaseBoardModel:FindEmptyPositionInValidOrder()
  return self.m_itemLayerModel:FindEmptyPositionInValidOrder()
end

function BaseBoardModel:FindEmptyPositionInSpreadOrder(centerPosition, openedPosition)
  return self.m_itemLayerModel:FindEmptyPositionInSpreadOrder(centerPosition, openedPosition)
end

function BaseBoardModel:FindEmptyPositionInCircleOrder(centerPosition)
  return self.m_itemLayerModel:FindEmptyPositionInCircleOrder(centerPosition)
end

function BaseBoardModel:GetCodeCountMap(includeBoard, includeCache, includeStore)
  local codeCountMap = {}
  includeBoard = includeBoard ~= false
  if includeBoard then
    for item, _ in pairs(self:GetAllBoardItems()) do
      local code = item:GetCode()
      if codeCountMap[code] == nil then
        codeCountMap[code] = 0
      end
      codeCountMap[code] = codeCountMap[code] + 1
    end
  end
  return codeCountMap
end

function BaseBoardModel:FindMergePair(ignoredItems, ignoreCobweb)
  ignoredItems = ignoredItems or {}
  local mergedTypeMapCobweb = {}
  local mergedTypeMapNormal = {}
  local splitItemsArray = {}
  for itemModel, _ in pairs(self:GetBoardItemsForMerge()) do
    if ignoredItems[itemModel] == nil and self:CanItemMerge(itemModel) then
      local itemCobweb = itemModel:GetComponent(ItemCobweb)
      if itemCobweb == nil and itemModel:GetMergedType() ~= nil then
        local mergedType = itemModel:GetMergedType()
        if StringUtil.StartWith(mergedType, ItemCodePrefix.RewardBubble) then
          mergedType = string.sub(mergedType, string.len(ItemCodePrefix.RewardBubble) + 1)
        end
        if mergedTypeMapNormal[mergedType] == nil then
          mergedTypeMapNormal[mergedType] = {}
        end
        table.insert(mergedTypeMapNormal[mergedType], itemModel)
      elseif itemCobweb ~= nil and not itemCobweb:IsInnerItemMaxLevel() then
        local mergedType = GM.ItemDataModel:GetModelConfig(itemCobweb:GetInnerItemCode()).MergedType
        if mergedTypeMapCobweb[mergedType] == nil then
          mergedTypeMapCobweb[mergedType] = {}
        end
        table.insert(mergedTypeMapCobweb[mergedType], itemModel)
      elseif itemModel:GetComponent(ItemSplit) ~= nil then
        splitItemsArray[#splitItemsArray + 1] = itemModel
      end
    end
  end
  local levelCache = {}
  local isOrderDirectCache = {}
  local isOrderIndirectCache = {}
  local addSortInfo = function(key)
    if levelCache[key] == nil then
      levelCache[key] = GM.ItemDataModel:GetChainLevel(key)
      isOrderDirectCache[key] = self:IsUnfilledOrderRequirementsChainOrPdChainItem(key, true, true)
      isOrderIndirectCache[key] = self:IsUnfilledOrderRequirementsChainOrPdChainItem(key, false, true)
    end
  end
  local sortFunc = function(a, b)
    addSortInfo(a)
    addSortInfo(b)
    local levelA = levelCache[a]
    local levelB = levelCache[b]
    local isOrderDirectA = isOrderDirectCache[a]
    local isOrderDirectB = isOrderDirectCache[b]
    local isOrderIndirectA = isOrderIndirectCache[a]
    local isOrderIndirectB = isOrderIndirectCache[b]
    if isOrderDirectA ~= isOrderDirectB then
      return isOrderDirectA
    end
    if isOrderIndirectA ~= isOrderIndirectB then
      return isOrderIndirectA
    end
    return levelA > levelB
  end
  local keys = Table.GetKeys(mergedTypeMapCobweb)
  for _, key in ipairs(keys) do
    if mergedTypeMapNormal[key] == nil then
      mergedTypeMapCobweb[key] = nil
    end
  end
  if not ignoreCobweb then
    keys = Table.GetKeys(mergedTypeMapCobweb)
    Table.ListRandomReorder(keys)
    table.sort(keys, sortFunc)
    for _, key in ipairs(keys) do
      local cobweb = Table.ListRandomSelectOne(mergedTypeMapCobweb[key])
      local normal = Table.ListRandomSelectOne(mergedTypeMapNormal[key])
      return {normal, cobweb}
    end
  end
  keys = Table.GetKeys(mergedTypeMapNormal)
  for _, key in ipairs(keys) do
    if #mergedTypeMapNormal[key] < 2 then
      mergedTypeMapNormal[key] = nil
    end
  end
  local isAutoRun = IsAutoRun()
  local orderNeedKeys = {}
  local merge2PdKeys = {}
  local merge2PdItems = {}
  local otherNormalKeys = {}
  local otherPdKeys = {}
  local otherEqKeys = {}
  for key, items in pairs(mergedTypeMapNormal) do
    local itemCode = items[1]:GetCode()
    if GM.ItemDataModel:GetModelConfig(itemCode).GeneratedItems ~= nil and not GM.ItemDataModel:IsTransformItem(itemCode) then
      merge2PdItems[key] = {}
      for _, pdItem in ipairs(items) do
        if pdItem:GetComponent(ItemSpread) ~= nil and pdItem:GetComponent(ItemSpread):ShowCountDown() and pdItem:GetComponent(ItemSpread):GetState() == ItemSpreadState.Opened then
          table.insert(merge2PdItems[key], pdItem)
        end
      end
      if 2 <= #merge2PdItems[key] then
        merge2PdKeys[#merge2PdKeys + 1] = key
      else
        otherPdKeys[#otherPdKeys + 1] = key
        merge2PdItems[key] = nil
      end
    elseif self:IsUnfilledOrderRequirementsChainOrPdChainItem(key, false, true) then
      orderNeedKeys[#orderNeedKeys + 1] = key
    elseif StringUtil.StartWith(itemCode, "pd_") then
      merge2PdKeys[#merge2PdKeys + 1] = key
    elseif not isAutoRun or not GM.ItemDataModel:IsDisposableInstrument(itemCode) then
      if GM.ItemDataModel:IsInstrument(itemCode) then
        otherEqKeys[#otherEqKeys + 1] = key
      else
        otherNormalKeys[#otherNormalKeys + 1] = key
      end
    end
  end
  Table.ListRandomReorder(orderNeedKeys)
  table.sort(orderNeedKeys, sortFunc)
  for _, key in ipairs(orderNeedKeys) do
    local normal = Table.ListRandomSelectN(mergedTypeMapNormal[key], 2)
    return normal
  end
  Table.ListRandomReorder(merge2PdKeys)
  table.sort(merge2PdKeys, sortFunc)
  for _, key in ipairs(merge2PdKeys) do
    if merge2PdItems[key] ~= nil then
      local normal = Table.ListRandomSelectN(merge2PdItems[key], 2)
      return normal
    else
      local normal = Table.ListRandomSelectN(mergedTypeMapNormal[key], 2)
      return normal
    end
  end
  if 2 <= #splitItemsArray then
    Table.ListRandomReorder(splitItemsArray)
    local normal = Table.ListRandomSelectN(splitItemsArray, 2)
    return normal
  end
  Table.ListRandomReorder(otherNormalKeys)
  table.sort(otherNormalKeys, sortFunc)
  for _, key in ipairs(otherNormalKeys) do
    local normal = Table.ListRandomSelectN(mergedTypeMapNormal[key], 2)
    return normal
  end
  Table.ListRandomReorder(otherPdKeys)
  table.sort(otherPdKeys, sortFunc)
  for _, key in ipairs(otherPdKeys) do
    local normal = Table.ListRandomSelectN(mergedTypeMapNormal[key], 2)
    return normal
  end
  Table.ListRandomReorder(otherEqKeys)
  table.sort(otherEqKeys, sortFunc)
  for _, key in ipairs(otherEqKeys) do
    local normal = Table.ListRandomSelectN(mergedTypeMapNormal[key], 2)
    return normal
  end
end

function BaseBoardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(itemCode, direct, excludeStore, selectOrder)
  return false
end

function BaseBoardModel:GetBoardItemsForMerge()
  return self:GetAllBoardItems()
end

function BaseBoardModel:CanItemMove(item)
  return item:GetComponent(ItemPaperBox) == nil and item:GetComponent(ItemCobweb) == nil and item:GetComponent(ItemLocker) == nil
end

function BaseBoardModel:CanItemAtPositionTutorial(pos1, pos2)
  local item1 = self:GetItem(pos1)
  local item2 = self:GetItem(pos2)
  if not item1 or not item2 then
    return false
  end
  if self:CanTwoItemsMerge(item1, item2) or self:CanItemAffect(item1, item2) then
    return true
  end
  local targetItemCook = item2:GetComponent(ItemCook)
  if targetItemCook ~= nil and targetItemCook:CanAddMaterial(item1) then
    return true
  end
end

function BaseBoardModel:CanItemMerge(item)
  if item:GetComponent(ItemLocker) ~= nil or item:GetComponent(ItemPaperBox) ~= nil then
    return false
  end
  local itemCook = item:GetComponent(ItemCook)
  if itemCook ~= nil and itemCook:GetState() ~= EItemCookState.Empty then
    return false
  end
  local itemSpread = item:GetComponent(ItemSpread)
  if itemSpread ~= nil and itemSpread:IsChestUsedOnce() then
    return false
  end
  return true
end

function BaseBoardModel:CanTwoItemsMerge(item1, item2)
  if not self:CanItemMerge(item1) or not self:CanItemMerge(item2) then
    return false
  end
  local itemType1, itemType2, mergedType
  local itemCobweb1 = item1:GetComponent(ItemCobweb)
  if itemCobweb1 ~= nil then
    itemType1 = itemCobweb1:GetInnerItemCode()
  else
    itemType1 = item1:GetType()
    mergedType = item1:GetMergedType()
  end
  local itemCobweb2 = item2:GetComponent(ItemCobweb)
  if itemCobweb2 ~= nil then
    itemType2 = itemCobweb2:GetInnerItemCode()
  else
    itemType2 = item2:GetType()
    mergedType = item2:GetMergedType()
  end
  return itemType1 == itemType2 and mergedType ~= nil
end

function BaseBoardModel:CanItemAffect(specialItem, targetItem)
  if targetItem:GetComponent(ItemLocker) ~= nil then
    return false
  end
  local split = specialItem:GetComponent(ItemSplit)
  if split ~= nil then
    return split:CanTakeEffect(targetItem)
  end
  local booster = specialItem:GetComponent(ItemBooster)
  if booster ~= nil then
    return booster:CanTakeEffect(targetItem)
  end
  return false
end

function BaseBoardModel:CanItemSell(item)
  if item:GetSellPrice() == nil then
    return false
  end
  local itemCook = item:GetComponent(ItemCook)
  if itemCook ~= nil and itemCook:GetState() ~= EItemCookState.Empty then
    return false
  end
  local itemSpread = item:GetComponent(ItemSpread)
  if itemSpread ~= nil and itemSpread:ShowCountDown() then
    return false
  end
  local itemChainId = GM.ItemDataModel:GetChainId(item:GetType())
  local protectLevel = GM.ItemDataModel:GetChainProtectLevel(itemChainId)
  if protectLevel == nil then
    return true
  end
  local maxUnlockedLevel = 0
  for level, type in ipairs(GM.ItemDataModel:GetChain(itemChainId)) do
    if GM.ItemDataModel:IsUnlocked(type) then
      maxUnlockedLevel = level
    end
  end
  if protectLevel >= maxUnlockedLevel then
    return false
  end
  local itemLevel = GM.ItemDataModel:GetChainLevel(item:GetType())
  return maxUnlockedLevel > itemLevel
end

function BaseBoardModel:GenerateItem(position, code, cost)
  local newItem = ItemModelFactory.CreateWithCode(self, position, code, true)
  if cost then
    newItem:SetCost(cost)
  end
  self:SaveItemProperty(newItem)
  self:_SetItem(position, newItem)
  newItem:DispatchComponentEvent("UpdatePerSecond")
  return newItem
end

function BaseBoardModel:ReplaceItem(sourceItem, newItemCode, cost, log)
  self:RemoveItem(sourceItem)
  local newItem = self:GenerateItem(sourceItem:GetPosition(), newItemCode, cost)
  local message = {Source = sourceItem, New = newItem}
  if log ~= false then
    GM.BIManager:LogReplace(sourceItem:GetCode(), newItemCode, self:GetGameMode())
  end
  EventDispatcher.DispatchEvent(EEventType.ItemReplaced, message)
  return newItem
end

function BaseBoardModel:RemoveItem(item)
  local spreadCmp = item:GetComponent(ItemSpread)
  local isOpeningBox = spreadCmp ~= nil and spreadCmp:GetState() == ItemSpreadState.Opening
  item:Destroy()
  self:_SetItem(item:GetPosition(), nil)
  self:_RemoveItemProperty(item)
  if isOpeningBox and self.UpdateOpeningItem then
    self:UpdateOpeningItem()
  end
end

function BaseBoardModel:SpreadItem(sourceItem, newItemPosition, newItemCode, logSpread, cost, boostLevelSpan, boostEnergySpared)
  local newItem = self:GenerateItem(newItemPosition, newItemCode, cost)
  local message = {
    Source = sourceItem,
    New = newItem,
    BoostLevelSpan = boostLevelSpan,
    BoostEnergySpared = boostEnergySpared
  }
  self.event:Call(BoardEventType.SpreadItem, message)
  return message
end

function BaseBoardModel:DragItem(item, targetPosition)
  Log.Assert(false, "DragItem()是抽象接口")
end

function BaseBoardModel:MergeAll()
end
