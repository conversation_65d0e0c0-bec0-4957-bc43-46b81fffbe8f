AlbumUpgradeConfirmWindow = setmetatable({windowMaskAlpha = 0.85}, AlbumActivityBaseWindow)
AlbumUpgradeConfirmWindow.__index = AlbumUpgradeConfirmWindow

function AlbumUpgradeConfirmWindow:Init(activityType, parentWnd)
  AlbumActivityBaseWindow.Init(self, activityType)
  self.m_parentWnd = parentWnd
  self.m_rewards = {}
  self.m_rewards[1] = self.m_rewardItemSimpleLuaTable
  local rewards = self.m_model:GetAlbumConfig().upgradeReward
  for i, v in ipairs(rewards) do
    if self.m_rewards[i] == nil then
      self.m_rewards[i] = Object.Instantiate(self.m_rewardItemSimpleLuaTable.gameObject, self.m_rewardItemSimpleLuaTable.transform.parent):GetLuaTable()
    end
    self.m_rewards[i]:Init(v)
    self.m_rewards[i]:SetAmountText("X" .. v[PROPERTY_COUNT])
  end
  self:LogWindowAction(EBIType.UIActionType.Open, EBIReferType.UserClick)
end

function AlbumUpgradeConfirmWindow:OnUpgradeClicked()
  self:Close()
  GM.BIManager:LogAction(EBIType.AlbumClickUpgrade, self.m_model:GetId())
  local upgradeFun = function()
    self.m_model:UpgradeAlbum()
    GM.UIManager:OpenView(UIPrefabConfigName.AlbumUpgradeRewardWindow, self.m_activityType, self.m_parentWnd)
  end
  if self.m_model:CanExchangeStarReward() then
    GM.UIManager:OpenView(UIPrefabConfigName.AlbumUpgradeTipWindow, self.m_activityType, function(window)
      window:Close()
      upgradeFun()
    end, function(window)
      window:Close()
      GM.UIManager:OpenView(UIPrefabConfigName.AlbumRecyleWindow, self.m_activityType)
    end)
  else
    upgradeFun()
  end
end
