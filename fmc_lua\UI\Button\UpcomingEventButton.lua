UpcomingEventButton = setmetatable({}, HudCountDownButton)
UpcomingEventButton.__index = UpcomingEventButton

function UpcomingEventButton:Awake()
  self.m_model = GM.UpcomingEventModel
  HudCountDownButton.Awake(self)
  self:UpdateContent()
  EventDispatcher.AddListener(EEventType.UpcomingEventStateChanged, self, self.UpdateContent)
end

function UpcomingEventButton:UpdateContent()
  if self.m_model:HasUpcomingEvent() then
    UIUtil.SetActive(self.gameObject, true)
    self.m_finishTime = self.m_model:GetLastTime()
    if self.m_curIconLink ~= self.m_model:GetIconLink() then
      self.m_curIconLink = self.m_model:GetIconLink()
      self.m_iconImg.enabled = false
      if self.m_curIconLink ~= nil then
        local downloadIconLink = self.m_curIconLink
        LoadUrlImage:LoadSprite(downloadIconLink, function(bSuccess, sprite)
          if bSuccess and sprite and self.m_iconImg and not self.m_iconImg:IsNull() and self.m_curIconLink == downloadIconLink then
            self.m_iconImg.enabled = true
            self.m_iconImg.sprite = sprite
            SpriteUtil.SetNativeSize(self.m_iconImg)
          end
        end, true)
      end
    end
    self:UpdatePerSecond()
  else
    UIUtil.SetActive(self.gameObject, false)
    self.m_curIconLink = nil
  end
end

function UpcomingEventButton:OnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.UpcomingEventWindow)
end
