BundleDefinition = {}
EBundleDBKey = {
  LockedConfig = "lockedConfig",
  TriggerTime = "triggerTime",
  MaxBuyNum = "maxBuyNum",
  DailyBuyNum = "dailyBuyNum",
  LastBuyDay = "lastBuyDay",
  LastShowDay = "lastShowDay",
  DailyShowNum = "dailyShowNum",
  LastPopTime = "lastPopTime",
  TriggerDailyShowNum = "tgDailyShowNum_%s",
  TriggerLastShowDay = "tgLastShowDay"
}
BundleColumnValue = "value"
BundleMetaPurchaseInfoKeySuffix = "PurchaseBundleId"
EBundleType = {
  Starter = "starter",
  Energy = "energy",
  CD = "cd",
  CDFill = "cdFill",
  OrderGroup = "orderGroup",
  Normal = "normal",
  MultiTier = "multiTier",
  Chain = "chain",
  OnePlusN = "onePlusN",
  OneByOne = "oneByOne"
}
BundleTypeOrderList = {
  EBundleType.Starter,
  EBundleType.Energy,
  EBundleType.CD,
  EBundleType.CDFill,
  EBundleType.Normal,
  EBundleType.OrderGroup,
  EBundleType.MultiTier,
  EBundleType.Chain,
  EBundleType.OnePlusN,
  EBundleType.OneByOne
}
EBundleTriggerType = {
  Login = "login",
  LackEnergy = "lack_energy",
  LackGem = "lack_gem",
  ClickCDGenerator = "pd_cd",
  FinishOrderGroup = "finish_order_group",
  TaskFinished = "task_finished",
  PdCDNumber = "pd_cd_number",
  LackDigToken = "lack_dig_token",
  LackChestToken = "lack_chest_token",
  BlindChestLastChance = "blindchest_last_chance"
}
EBundleEntryScene = {Main = "main", Activity = "activity"}
BundleUIType = {
  starter = {
    window = UIPrefabConfigName.StarterBundleWindow,
    entryPrefabName = UIPrefabConfigName.StarterBundleButton,
    bundleType = EBundleType.Starter,
    maxRewardCount = 5
  },
  energy = {
    window = UIPrefabConfigName.EnergyBundleWindow,
    entryPrefabName = UIPrefabConfigName.EnergyBundleButton,
    bundleType = EBundleType.Energy,
    maxRewardCount = 5
  },
  cd = {
    window = UIPrefabConfigName.CDBundleWindow,
    entryPrefabName = UIPrefabConfigName.CDBundleButton,
    bundleType = EBundleType.CD,
    maxRewardCount = 5
  },
  cdFill = {
    window = UIPrefabConfigName.CDFillBundleWindow,
    entryPrefabName = UIPrefabConfigName.CDFillBundleButton,
    bundleType = EBundleType.CDFill,
    maxRewardCount = 5
  },
  album1Normal = {
    window = UIPrefabConfigName.Album1NormalBundleWindow,
    entryPrefabName = UIPrefabConfigName.Album1NormalBundleButton,
    bundleType = EBundleType.Normal,
    maxRewardCount = 5
  },
  orderGroup = {
    window = UIPrefabConfigName.OrderGroupBundleWindow,
    entryPrefabName = UIPrefabConfigName.OrderGroupBundleButton,
    bundleType = EBundleType.OrderGroup,
    maxRewardCount = 5
  },
  multiTier = {
    window = UIPrefabConfigName.MultiTierBundleWindow,
    entryPrefabName = UIPrefabConfigName.MultiTierBundleButton,
    bundleType = EBundleType.MultiTier,
    maxRewardCount = 1
  },
  chain = {
    window = UIPrefabConfigName.SpringChainBundleWindow,
    entryPrefabName = UIPrefabConfigName.SpringChainBundleButton,
    bundleType = EBundleType.Chain,
    titleText = "chain_bundle_title"
  },
  chainDig = {
    window = UIPrefabConfigName.TreasureDigChainBundleWindow,
    entryPrefabName = UIPrefabConfigName.TreasureDigChainBundleButton,
    bundleType = EBundleType.Chain,
    titleText = "dig_chain_bundle_title",
    entryInfo = {
      {
        scene = EBundleEntryScene.Activity,
        activity = ActivityType.TreasureDig,
        window = UIPrefabConfigName.TreasureDigMainWindow
      }
    }
  },
  chainChest = {
    window = UIPrefabConfigName.BlindChest1ChainBundleWindow,
    entryPrefabName = UIPrefabConfigName.BlindChest1ChainBundleButton,
    bundleType = EBundleType.Chain,
    titleText = "blindchest_bundle_title",
    entryInfo = {
      {
        scene = EBundleEntryScene.Activity,
        activity = ActivityType.BlindChest1,
        window = UIPrefabConfigName.BlindChest1MainWindow
      }
    }
  },
  onePlusNChest = {
    window = UIPrefabConfigName.BlindChest1OnePlusNBundleWindow,
    entryPrefabName = UIPrefabConfigName.BlindChest1OnePlusNBundleButton,
    bundleType = EBundleType.OnePlusN,
    titleText = "blindchest_bundle_title",
    descText = "blindchest_digoneplus_desc",
    maxRewardCount = 4,
    maxBundleCount = 3,
    entryInfo = {
      {
        scene = EBundleEntryScene.Activity,
        activity = ActivityType.BlindChest1,
        window = UIPrefabConfigName.BlindChest1MainWindow
      }
    }
  },
  onePlusNDig = {
    window = UIPrefabConfigName.TreasureDigOnePlusNBundleWindow,
    entryPrefabName = UIPrefabConfigName.TreasureDigOnePlusNBundleButton,
    bundleType = EBundleType.OnePlusN,
    titleText = "dig_chain_bundle_title",
    descText = "dig_digoneplus_desc",
    maxRewardCount = 4,
    maxBundleCount = 3,
    entryInfo = {
      {
        scene = EBundleEntryScene.Activity,
        activity = ActivityType.TreasureDig,
        window = UIPrefabConfigName.TreasureDigMainWindow
      }
    }
  },
  oneByOneSummerChristmas = {
    window = UIPrefabConfigName.SummerChristmasOneByOneBundleWindow,
    entryPrefabName = UIPrefabConfigName.SummerChristmasOneByOneBundleButton,
    bundleType = EBundleType.OneByOne,
    titleText = "1by1pack_title",
    descText = "1by1pack_desc",
    maxRewardCount = 5,
    maxBundleCount = 3
  }
}
EBundleState = {Close = 1, Opening = 2}

function BundleDefinition.GetCellTitleText(bundleUIType, index)
  return bundleUIType .. "_pack" .. index
end
