ShopCellProperty = setmetatable({}, BaseShopCell)
ShopCellProperty.__index = ShopCellProperty

function ShopCellProperty:UpdateContent(tbData)
  BaseShopCell.UpdateContent(self, tbData)
  if self.m_icon ~= tbData.icon then
    self.m_icon = tbData.icon
    self.m_iconImg.gameObject:SetActive(false)
    SpriteUtil.SetImage(self.m_iconImg, self:GetSpriteName(), true, function()
      if not self.m_iconImg:IsNull() then
        self.m_iconImg.gameObject:SetActive(true)
      end
    end)
  end
  local realCount = tbData.goods[1][PROPERTY_COUNT]
  self.m_numText.text = realCount
  UIUtil.SetSizeDelta(self.m_numText.transform, self.m_numText.preferredWidth)
  if self.m_numIcon ~= tbData.goods[1][PROPERTY_TYPE] then
    self.m_numIcon = tbData.goods[1][PROPERTY_TYPE]
    self.m_numIconImg.enabled = false
    SpriteUtil.SetImage(self.m_numIconImg, EPropertySprite[self.m_numIcon], true, function()
      if not self.m_numIconImg:IsNull() then
        self.m_numIconImg.enabled = true
      end
    end)
  end
  self.m_IAPButton:Init(GM.InAppPurchaseModel:GetLocalizedPrice(tbData.purchaseId), self.m_callback)
  self.m_redTagGo:SetActive(tbData.redTag ~= nil)
  if IsString(tbData.redTag) then
    self.m_redTagText.text = GM.GameTextModel:GetText(tbData.redTag)
  end
  self.m_purpleTagGo:SetActive(tbData.purpleTag ~= nil)
  if IsString(tbData.purpleTag) then
    self.m_purpleTagText.text = GM.GameTextModel:GetText(tbData.purpleTag)
  end
end

function ShopCellProperty:GetSpriteName(iconName)
  iconName = iconName or self.m_data.icon
  return ShopSpriteKeyPrefix .. iconName
end

function ShopCellProperty:UpdateSortingOrder(sortingOrder)
  UIUtil.UpdateSortingOrder(self.m_effectGo, sortingOrder + 1)
end

function ShopCellProperty:OnClose()
  self.m_effectGo:SetActive(false)
end
