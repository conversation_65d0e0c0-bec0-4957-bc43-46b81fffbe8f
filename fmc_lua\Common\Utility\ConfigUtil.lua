ConfigUtil = {}

function ConfigUtil.GetArrFromCurrency(currency)
  if not currency then
    return nil
  end
  local result = {}
  for _, oneCurrency in ipairs(currency) do
    table.insert(result, oneCurrency[PROPERTY_TYPE] .. "-" .. oneCurrency[PROPERTY_COUNT])
  end
  return result
end

function ConfigUtil.GetCurrencyFromStr(str)
  local splitArr = StringUtil.Split(str, "-")
  return {
    [PROPERTY_TYPE] = splitArr[1],
    [PROPERTY_COUNT] = tonumber(splitArr[2]),
    [PROPERTY_CRYPT] = Crypt.CryptCurrency(splitArr[2])
  }
end

function ConfigUtil.GetCurrencyFromArrStr(arr)
  if arr == nil then
    return nil
  end
  local results = {}
  for _, str in ipairs(arr) do
    table.insert(results, ConfigUtil.GetCurrencyFromStr(str))
  end
  return results
end

function ConfigUtil.GetCurrencyImageName(currency)
  if Table.Contain(EPropertyType, currency[PROPERTY_TYPE]) then
    return EPropertySpriteBig[currency[PROPERTY_TYPE]]
  else
    return GM.ItemDataModel:GetSpriteName(currency[PROPERTY_TYPE])
  end
end

function ConfigUtil.Rewards2String(rewards)
  local data = ""
  for index, item in ipairs(rewards) do
    data = data .. item[PROPERTY_TYPE] .. "-" .. item[PROPERTY_COUNT]
    if index ~= #rewards then
      data = data .. ";"
    end
  end
  return data
end

function ConfigUtil.StringToRewards(data)
  local array = StringUtil.Split(data, ";")
  return ConfigUtil.GetCurrencyFromArrStr(array)
end

function ConfigUtil.GetGoldCount(rewards)
  local count = 0
  for _, item in ipairs(rewards) do
    if item[PROPERTY_TYPE] == EPropertyType.Gold then
      count = count + item[PROPERTY_COUNT]
    end
  end
  return count
end
