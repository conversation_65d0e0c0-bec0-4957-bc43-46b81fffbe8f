ButterflyAni = {}
ButterflyAni.__index = ButterflyAni

function ButterflyAni:OnEnable()
  local time = math.random()
  Scheduler.Schedule(self.Play, self, nil, nil, time)
end

function ButterflyAni:Play()
  self.m_animator:Set<PERSON>rigger("fly")
  Scheduler.UnscheduleTarget(self)
end

function ButterflyAni:OnDisable()
  Scheduler.UnscheduleTarget(self)
end

function ButterflyAni:OnDestroy()
  Scheduler.UnscheduleTarget(self)
end
