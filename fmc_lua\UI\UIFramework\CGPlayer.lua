CGPlayer = {}
CGPlayer.__index = CGPlayer

function CGPlayer:_Init()
  if ScreenFitter.NeedNotch() then
    local notchHeight = ScreenFitter.GetNotchHeight()
    UIUtil.AddAnchoredPosition(self.m_button, 0, -notchHeight)
  end
  local tex = CS.UnityEngine.RenderTexture(1080, 1920, 0)
  local size = GM.UIManager:GetCanvasSize()
  self.m_render.transform:SetLocalScale(math.min(size.x / 1080, size.y / 1920))
  self.m_render.texture = tex
  self.m_video.targetTexture = tex
  self.m_renderTexture = tex
  self.m_button.gameObject:SetActive(GM.ConfigModel:CanCGSkip())
end

function CGPlayer:Play(url, callback)
  self:_Init()
  self.m_callback = callback
  self.m_video.url = url
  self.m_video:loopPointReached("+", function(vp)
    Object.Destroy(self.gameObject)
    self.m_renderTexture:Release()
    callback(true)
  end)
  self.m_video:errorReceived("+", function(player, msg)
    Object.Destroy(self.gameObject)
    self.m_renderTexture:Release()
    callback(false, msg)
  end)
  self.m_video:Play()
  self.m_render.enabled = true
end

function CGPlayer:Skip()
  self.m_video:Stop()
  Object.Destroy(self.gameObject)
  self.m_renderTexture:Release()
  self.m_callback(true)
end

function CGPlayer:IsPlayingVideo()
  return self.m_video and self.m_video.isPlaying
end
