LoopAnimationManager = {}
LoopAnimationManager.__index = LoopAnimationManager

function LoopAnimationManager:Init()
  self:Clear()
end

function LoopAnimationManager:AddToQueue(animator)
  if not GM.ChapterManager:GetActiveRoomView() then
    return
  end
  if animator.notUseManager then
    return
  end
  for _, a in ipairs(self._waitingQueue) do
    if a == animator then
      return
    end
  end
  table.insert(self._waitingQueue, animator)
  return true
end

function LoopAnimationManager:Clear()
  self._waitingQueue = {}
  self._cursor = nil
  self._playingQueue = {}
end

function LoopAnimationManager:RemoveFromQueue(animator)
  for i = #self._waitingQueue, 1, -1 do
    if self._waitingQueue[i] == animator then
      table.remove(self._waitingQueue, i)
    end
  end
  for i = #self._playingQueue, 1, -1 do
    if self._playingQueue[i] == animator then
      table.remove(self._playingQueue, i)
    end
  end
end

function LoopAnimationManager:IsPlayingQueueFull()
  return #self._playingQueue >= self:_GetMaxAnimations()
end

function LoopAnimationManager:OnAnimationCompleted(animator)
  for i = #self._playingQueue, 1, -1 do
    if self._playingQueue[i] == animator then
      table.remove(self._playingQueue, i)
    end
  end
  self:UpdateWaitingQueue()
end

function LoopAnimationManager:UpdateWaitingQueue()
  if self:IsPlayingQueueFull() then
    return
  end
  if #self._waitingQueue == 0 then
    return
  end
  self._cursor = self._cursor or 1
  local startCur = self._cursor
  for i = 1, #self._waitingQueue do
    self._cursor = self._cursor + 1
    if self._cursor > #self._waitingQueue then
      self._cursor = 1
    end
    local animator = self._waitingQueue[self._cursor]
    if not animator:IsPlaying() and not animator:IsCulled() then
      table.insert(self._playingQueue, animator)
      animator:PlayAnimation()
    end
    if self:IsPlayingQueueFull() or self._cursor == startCur then
      break
    end
  end
end

function LoopAnimationManager:UpdatePerSecond()
  if not self._playingQueue then
    return
  end
  local dirty = #self._playingQueue == 0
  for i = #self._playingQueue, 1, -1 do
    if self._playingQueue[i]:IsCulled() then
      table.remove(self._playingQueue, i)
      dirty = true
    end
  end
  if dirty then
    self:UpdateWaitingQueue()
  end
end

function LoopAnimationManager:_GetMaxAnimations()
  return 4
end
