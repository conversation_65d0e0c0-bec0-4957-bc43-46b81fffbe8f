CustomerPrefabConfigName = {
  CustomerBoy = "CustomerBoy",
  CustomerGirl = "CustomerGirl",
  CustomerGrandma = "CustomerGrandma",
  CustomerGrandpa = "CustomerGrandpa",
  CustomerMom = "CustomerMom",
  CustomerSister = "CustomerSister",
  CustomerSister2 = "CustomerSister2"
}
setmetatable(CustomerPrefabConfigName, {
  __index = function(_, key)
    Log.Error("CustomerPrefabConfigName try to index a nil key: " .. tostring(key))
    return nil
  end
})

function CustomerPrefabConfigName.HasConfig(name)
  if name == nil then
    return false
  end
  return rawget(CustomerPrefabConfigName, name) ~= nil
end
