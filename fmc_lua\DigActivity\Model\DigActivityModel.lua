DigActivityModel = setmetatable({}, BaseActivityModel)
DigActivityModel.__index = DigActivityModel
DigActivityEventType = {StateChanged = 1}
DigActivityModel.RoundKey = "round"
DigActivityModel.ScoreKey = "score"
DigActivityModel.LevelKey = "level"
DigActivityModel.DigPosStateKey = "digPosState"
DigActivityModel.HasAddToken = "hasAddToken"

function DigActivityModel:Init(activityType, virtualDBTable)
  self.m_activityDefinition = DigActivityDefinition[activityType]
  self.m_tokenHelper = ActivityTokenHelper.Create(self, virtualDBTable, EFlyElementLabelStyle.Default)
  BaseActivityModel.Init(self, activityType, virtualDBTable)
end

function DigActivityModel:Destroy()
  BaseActivityModel.Destroy(self)
  self.m_tokenHelper:Destroy()
end

function DigActivityModel:GetResourceLabels()
  return self.m_activityDefinition.ResourceLabels
end

function DigActivityModel:_CalculateState()
  if self.m_config == nil then
    return ActivityState.Released, nil
  end
  local serverTime = GM.GameModel:GetServerTime()
  if serverTime < self.m_config.sTime then
    return ActivityState.Preparing, self.m_config.sTime
  elseif serverTime < self.m_config.eTime and not self:HasFinishedAllRound() then
    return ActivityState.Started, self.m_config.eTime
  elseif self.m_config.rTime and serverTime < self.m_config.rTime then
    return ActivityState.Ended, self.m_config.rTime
  else
    return ActivityState.Released, nil
  end
end

function DigActivityModel:_OnStateChanged()
  self.event:Call(DigActivityEventType.StateChanged)
  if self:GetState() ~= ActivityState.Released then
    self:_UpdateLevel()
    if self:HasFinishedCurRound() and not self:HasFinishedAllRound() then
      self:EnterNextRound()
    end
  end
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
end

function DigActivityModel:_DropData()
  BaseActivityModel._DropData(self)
  self.m_mapDigPosState = nil
  self.m_bTriggerLackToken = false
end

function DigActivityModel:_LoadFileConfig()
  local arrDigItemConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.DigItemConfig) or {}
  self.m_mapId2ItemConfig = {}
  for _, config in ipairs(arrDigItemConfig) do
    config.imgKey = config.id
    config.grooveRotate = config.grooveRotate or 0
    config.unlockedScale = config.unlockedScale or 1
    self.m_mapId2ItemConfig[config.id] = config
  end
  local arrDigLevelConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.DigLevelsConfig) or {}
  self.m_mapId2LevelConfig = {}
  for _, config in ipairs(arrDigLevelConfig) do
    self.m_mapId2LevelConfig[config.id] = config
  end
end

function DigActivityModel:_LoadOtherServerConfig(config)
  self:_LoadFileConfig()
  self.m_tokenHelper:LoadConfig(config)
  self.m_roundConfig = {}
  self.m_totalRound = 0
  local maxRound = 0
  local levelConfig
  for _, lConfig in ipairs(config.digLevelConfig or {}) do
    self.m_roundConfig[lConfig.round] = lConfig
    self.m_totalRound = self.m_totalRound + 1
    maxRound = math.max(maxRound, lConfig.round)
    if not Table.IsEmpty(lConfig.pool) then
      for _, levelId in ipairs(lConfig.pool) do
        if self.m_mapId2LevelConfig[levelId] == nil then
          Log.Error("[DigActivityModel] 关卡id不存在: " .. levelId)
        end
      end
    end
  end
  Log.Assert(maxRound == self.m_totalRound, "DigLevelConfig配置中的round配置不匹配, 请检查")
  self.m_arrScoreConfig = config.digScoreConfig
end

function DigActivityModel:GetBoardEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = self.m_activityDefinition.eEntryRootKey,
    entryPrefabName = self.m_activityDefinition.BoardEntryPrefabName,
    extraListenEvent = self.m_activityDefinition.DigItemStateChangedEvent,
    checkFun = function()
      return self:CanAddScore()
    end
  }
end

function DigActivityModel:GetMapEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = self.m_activityDefinition.eEntryRootKey,
    entryPrefabName = self.m_activityDefinition.EntryPrefabName,
    hudKey = self.m_activityDefinition.EntryButtonKey,
    extraListenEvent = self.m_activityDefinition.DigItemStateChangedEvent,
    checkFun = function()
      return self:CanAddScore()
    end
  }
end

function DigActivityModel:GetAllStateChangedEvent()
  return {
    self.m_activityDefinition.StateChangedEvent,
    self.m_activityDefinition.DigItemStateChangedEvent
  }
end

function DigActivityModel:IsActivityOpen()
  return self:CanAddScore()
end

function DigActivityModel:HasFinishedAllRound()
  return self:GetRound() == self:GetTotalRound() and self:HasFinishedCurRound()
end

function DigActivityModel:HasFinishedCurRound()
  local cur, total = self:GetItemAcquiredProgress()
  return cur == total
end

function DigActivityModel:GetTotalRound()
  return self.m_totalRound
end

function DigActivityModel:GetRound()
  return self.m_dbTable:GetValue(DigActivityModel.RoundKey, "value") or 1
end

function DigActivityModel:EnterNextRound()
  if self:HasFinishedAllRound() then
    return
  end
  self.m_dbTable:Set(DigActivityModel.RoundKey, "value", self:GetRound() + 1)
  self:_RefreshNextLevel()
end

function DigActivityModel:CanAddScore()
  if self:GetState() ~= ActivityState.Started then
    return false
  end
  return not self:HasFinishedAllRound()
end

function DigActivityModel:GetScore()
  return self.m_dbTable:GetValue(DigActivityModel.ScoreKey, "value") or 2
end

function DigActivityModel:AddScore(delta, bUpdateScore)
  if self:CanAddScore() then
    self:_AddScore(delta, bUpdateScore)
  end
end

function DigActivityModel:GetActivityTokenNumber()
  return self:GetScore()
end

function DigActivityModel:AcquireActivityToken(count)
  self:AddScore(count)
end

function DigActivityModel:_AddScore(delta, bUpdateScore)
  self.m_dbTable:Set(DigActivityModel.ScoreKey, "value", self:GetScore() + delta)
  if 0 < delta then
    GM.BIManager:LogAcquire(self.m_activityDefinition.ActivityTokenPropertyType, delta, self.m_activityDefinition.AcquireScoreBIType, true)
    EventDispatcher.DispatchEvent(EEventType.AcquireActivityToken, {
      num = delta,
      activityType = self:GetType()
    })
    if self.m_dbTable:GetValue(DigActivityModel.HasAddToken, "value") ~= 1 then
      self.m_dbTable:Set(DigActivityModel.HasAddToken, "value", 1)
    end
  else
    GM.BIManager:LogUseItem(self.m_activityDefinition.ActivityTokenPropertyType, -delta, self.m_activityDefinition.UseScoreBIType, "treasureDig")
    if self:GetScore() == 0 and self.m_dbTable:GetValue(DigActivityModel.HasAddToken, "value") == 1 then
      self.m_bTriggerLackToken = true
    end
  end
  EventDispatcher.DispatchEvent(self.m_activityDefinition.ScoreChangedEvent, {
    UpdateScore = delta < 0 or bUpdateScore
  })
end

function DigActivityModel:GetOrderExtraReward(orderScore, coinNum, orderId)
  if self.m_tokenHelper:IsAcquireTypeValid(EActTokenAcquireType.FinishOrder) then
    return self.m_tokenHelper:GetOrderExtraReward(orderScore, orderId)
  end
  if Table.IsEmpty(self.m_arrScoreConfig) or coinNum == nil then
    return
  end
  for _, scoreConfig in ipairs(self.m_arrScoreConfig) do
    if (scoreConfig.coin_min == nil or coinNum >= scoreConfig.coin_min) and (scoreConfig.coin_max == nil or coinNum <= scoreConfig.coin_max) then
      return {
        [PROPERTY_TYPE] = self.m_activityDefinition.ActivityTokenPropertyType,
        [PROPERTY_COUNT] = scoreConfig.num
      }
    end
  end
end

function DigActivityModel:CanAddOrderReward(orderType)
  if self.m_tokenHelper:IsAcquireTypeValid(EActTokenAcquireType.FinishOrder) then
    return self.m_tokenHelper:CanAddOrderReward(orderType)
  end
  return self:CanAddScore()
end

function DigActivityModel:TryTriggerLackDigToken()
  if self.m_bTriggerLackToken then
    self.m_bTriggerLackToken = false
    GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.LackDigToken, nil, nil, nil, true)
  end
end

function DigActivityModel:OnLackDigToken()
  if self.m_dbTable:GetValue(DigActivityModel.HasAddToken, "value") == 1 then
    self.m_bTriggerLackToken = false
    return GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.LackDigToken, nil, nil, nil, true)
  end
end

function DigActivityModel:GetLevel()
  return self.m_dbTable:GetValue(DigActivityModel.LevelKey, "value")
end

function DigActivityModel:_RefreshNextLevel()
  if Table.IsEmpty(self.m_roundConfig) then
    Debug.LogError("没有可用的关卡id, 请检查!")
    return false
  end
  local levelId
  local levelPool = self.m_roundConfig[self:GetRound()].pool
  if not Table.IsEmpty(levelPool) then
    levelId = Table.ListRandomSelectOne(levelPool)
    GM.BIManager:LogAction(self.m_activityDefinition.RandomSelectLevelBIType, {
      id = levelId,
      round = self:GetRound()
    })
  end
  local levelConfig = self:GetLevelConfig(levelId)
  if levelConfig == nil then
    return
  end
  self.m_mapDigPosState = {}
  for i = 1, levelConfig.height do
    self.m_mapDigPosState[i] = {}
    for j = 1, levelConfig.width do
      self.m_mapDigPosState[i][j] = false
    end
  end
  self.m_dbTable:Set(DigActivityModel.LevelKey, "value", levelId)
  self:SetDigPosStateMap()
  self:_UpdateLevel()
  EventDispatcher.DispatchEvent(self.m_activityDefinition.DigItemStateChangedEvent)
  return true
end

function DigActivityModel:_UpdateLevel()
  if self:GetLevel() == nil then
    self:_RefreshNextLevel()
    return
  end
  local levelConfig = self:GetCurLevelConfig()
  if levelConfig == nil then
    return
  end
  local strData = self.m_dbTable:GetValue(DigActivityModel.DigPosStateKey, "value")
  self.m_mapDigPosState = {}
  if not StringUtil.IsNilOrEmpty(strData) then
    strData = StringUtil.Replace(strData, "@", ",")
    local tmpTable = json.decode(strData) or {}
    self.m_mapDigPosState = Table.DeepCopy(tmpTable, true)
  end
  self.m_arrItemData = {}
  local digItem, itemConfig
  for _, config in ipairs(levelConfig.treasures) do
    itemConfig = self.m_mapId2ItemConfig[config.id]
    if itemConfig ~= nil then
      digItem = DigItemData.Create(itemConfig, config.pos, config.rotate)
      table.insert(self.m_arrItemData, digItem)
    else
      Debug.LogError("[DigActivityModel] 不存在此物品的配置 id:" .. (config.id or ""))
    end
  end
end

function DigActivityModel:IsGridDig(x, y)
  return self.m_mapDigPosState[y][x]
end

function DigActivityModel:SetDigPosStateMap()
  self.m_dbTable:Set(DigActivityModel.DigPosStateKey, "value", StringUtil.Replace(json.encode(self.m_mapDigPosState), ",", "@"))
end

function DigActivityModel:GetLevelConfig(id)
  if Table.IsEmpty(self.m_mapId2LevelConfig) or self.m_mapId2LevelConfig[id] == nil then
    Debug.LogError("关卡配置不存在, 关卡id:" .. (id or ""))
    return {
      width = 4,
      height = 4,
      mapScale = 1,
      treasures = {
        {
          id = "treasure1_1",
          pos = {x = 1, y = 1},
          rotate = 0
        }
      }
    }
  end
  return self.m_mapId2LevelConfig[id]
end

function DigActivityModel:GetCurLevelConfig()
  local id = self:GetLevel()
  return self:GetLevelConfig(id)
end

function DigActivityModel:GetCurDigItemDataList()
  return self.m_arrItemData or {}
end

function DigActivityModel:DigTargetGrid(pos)
  if self.m_mapDigPosState[pos.y][pos.x] or self:HasFinishedCurRound() then
    Debug.LogError("禁止重复挖掘，轮次通关后不可挖掘")
    return
  end
  self.m_mapDigPosState[pos.y][pos.x] = true
  self:SetDigPosStateMap()
  self:_AddScore(-1)
  local digItemIndex
  for index, itemData in ipairs(self.m_arrItemData) do
    if itemData:IsPosInOccupancyGrids(pos) and self:HasAcquiredItem(index) then
      digItemIndex = index
      break
    end
  end
  self:TryClaimReward()
  if digItemIndex ~= nil then
    EventDispatcher.DispatchEvent(self.m_activityDefinition.DigItemStateChangedEvent)
  end
  EventDispatcher.DispatchEvent(self.m_activityDefinition.GridChangedEvent)
  self:LogGridState()
  return digItemIndex
end

function DigActivityModel:LogGridState()
  local levelConfig = self:GetCurLevelConfig()
  if levelConfig == nil then
    return
  end
  local total = 0
  local validCount = 0
  local digCount = 0
  local digSuccessCount = 0
  local bDig, bValid
  for i = 1, levelConfig.height do
    for j = 1, levelConfig.width do
      bDig = self.m_mapDigPosState[i][j]
      bValid = self:IsValidGrid(j, i)
      if bDig then
        digCount = digCount + 1
      end
      if bValid then
        validCount = validCount + 1
      end
      if bDig and bValid then
        digSuccessCount = digSuccessCount + 1
      end
      total = total + 1
    end
  end
  GM.BIManager:LogAction(self.m_activityDefinition.DigActionBIType, {
    round = self:GetRound(),
    total = total,
    available = validCount,
    dig = digCount,
    success = digSuccessCount
  })
end

function DigActivityModel:GetCurRoundReward()
  if not Table.IsEmpty(self.m_roundConfig) then
    return self.m_roundConfig[self:GetRound()].rewards
  end
end

function DigActivityModel:GetAllRoundConfig()
  return self.m_roundConfig
end

function DigActivityModel:TryClaimReward()
  if self:HasFinishedCurRound() then
    self:LogActivity(EBIType.ActivityRankUp, self:GetRound())
    RewardApi.CryptRewards(self:GetCurRoundReward())
    RewardApi.AcquireRewardsLogic(self:GetCurRoundReward(), EPropertySource.Give, self.m_activityDefinition.GetRewardsBIType, EGameMode.Board, CacheItemType.Stack)
  end
end

function DigActivityModel:HasAcquiredItem(index)
  local digItemData = self.m_arrItemData[index]
  for _, pos in ipairs(digItemData:GetOccupancyGrids()) do
    if not self.m_mapDigPosState[pos.y][pos.x] then
      return false
    end
  end
  return true
end

function DigActivityModel:GetItemAcquiredProgress()
  if Table.IsEmpty(self.m_arrItemData) then
    return 0, 0
  end
  local totalProgress = #self.m_arrItemData
  local curProgress = 0
  for index, digItemData in ipairs(self.m_arrItemData) do
    if self:HasAcquiredItem(index) then
      curProgress = curProgress + 1
    end
  end
  return curProgress, totalProgress
end

function DigActivityModel:IsValidGrid(x, y, bTreasureState)
  for index, digItemData in ipairs(self.m_arrItemData or {}) do
    for _, pos in ipairs(digItemData:GetOccupancyGrids()) do
      if x == pos.x and y == pos.y then
        return not bTreasureState or not self:HasAcquiredItem(index)
      end
    end
  end
  return false
end

function DigActivityModel:GetOneItemForTutorial()
  for _, digItemData in ipairs(self.m_arrItemData or {}) do
    if digItemData:GetGridCount() == 2 then
      return digItemData
    end
  end
end

function DigActivityModel.IsDigActivityScoreType(type)
  for _, v in pairs(DigActivityDefinition) do
    if type == v.ActivityTokenPropertyType then
      return true
    end
  end
end

function DigActivityModel.GetButtonTarget(propertyType)
  local mainWindow
  for activityType, activityDefinition in pairs(DigActivityDefinition) do
    if propertyType == activityDefinition.ActivityTokenPropertyType then
      mainWindow = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
      if mainWindow ~= nil then
        return mainWindow:GetHudButton()
      elseif GM.SceneManager:GetGameMode() == EGameMode.Board then
        local boardView = MainBoardView.GetInstance()
        if boardView ~= nil then
          local orderArea = boardView:GetOrderArea()
          if orderArea ~= nil then
            return orderArea:GetIconAreaByActivityType(activityType)
          end
        end
      elseif GM.SceneManager:GetGameMode() == EGameMode.Main then
        return TutorialHelper.GetHudButton(activityDefinition.EntryButtonKey)
      end
    end
  end
  return nil
end
