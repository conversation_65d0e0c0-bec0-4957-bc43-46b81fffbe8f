local Step = {
  Merge = "1",
  ShowInfo = "2",
  StartCook = "3",
  SkipCook = "4",
  TakeOut = "5"
}
local EStep2TextKey = {
  [Step.Merge] = "tutorial_equipment_4",
  [Step.ShowInfo] = "tutorial_equipment_5",
  [Step.StartCook] = "tutorial_equipment_6",
  [Step.SkipCook] = "tutorial_equipment_7",
  [Step.TakeOut] = "tutorial_equipment_8"
}
local EStep2TextAnchorPercent = {
  [Step.ShowInfo] = 67,
  [Step.StartCook] = 70,
  [Step.SkipCook] = 67
}
local instruItemId = "eq_1_4"
local materialItemId = "it_1_1_1"
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.ItemCookAddMaterial, self, self._OnItemCookAddMaterial)
  EventDispatcher.AddListener(EEventType.ItemCookStarted, self, self._OnItemCookStarted)
  EventDispatcher.AddListener(EEventType.ItemCookEnded, self, self._OnItemCookEnded)
  EventDispatcher.AddListener(EEventType.ItemSpread, self, self._OnItemSpread)
  EventDispatcher.AddListener(EEventType.ItemSpreadFailed, self, self._OnItemSpread)
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnItemCookStarted)
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  if (StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) or self.m_strOngoingDatas == Step.Merge) and self:_TryExecuteStep1() then
    return true
  elseif (StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) or self.m_strOngoingDatas == Step.ShowInfo) and self:_TryExecuteStep2() then
    return true
  elseif (StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) or self.m_strOngoingDatas == Step.StartCook) and self:_TryExecuteStep3() then
    return true
  elseif (StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) or self.m_strOngoingDatas == Step.SkipCook) and (self:_TryExecuteStep4() or self:_TryExecuteStep5()) then
    return true
  elseif (StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) or self.m_strOngoingDatas == Step.TakeOut) and self:_TryExecuteStep5() then
    return true
  end
end

function Executer:_OnItemCookAddMaterial()
  if self.m_strOngoingDatas == Step.Merge and self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    if not self:_TryExecuteStep2() then
      self:Finish(self.m_gesture)
    end
  end
end

function Executer:_OnItemCookStarted()
  if self.m_strOngoingDatas == Step.StartCook and (self.m_gesture or self.m_bHightlightBoardCookBubble) then
    if self.m_bHightlightBoardCookBubble then
      MainBoardView:GetInstance():HideCookStartTip(true)
      TutorialHelper.DehighlightBoardCookBubbleBtnGo()
      self.m_bHightlightBoardCookBubble = nil
    else
      TutorialHelper.DehighlightBoardInfoCookBtnGo()
    end
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    if not self:_TryExecuteStep4() then
      self:Finish(self.m_gesture)
    end
  end
end

function Executer:_OnItemCookEnded()
  if self.m_strOngoingDatas == Step.SkipCook and self.m_gesture then
    TutorialHelper.DehighlightBoardInfoSkipBtnGo()
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    TutorialHelper.HideDialog()
    if not self:_TryExecuteStep5() then
      self:Finish(self.m_gesture)
    end
  end
end

function Executer:_OnItemSpread()
  if self.m_strOngoingDatas == Step.TakeOut and self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
  end
end

function Executer:_TryExecuteStep1()
  local items1 = TutorialHelper.GetItems(materialItemId)
  local items2 = TutorialHelper.GetItems(instruItemId)
  local instru, cmp
  for _, item in ipairs(items2) do
    cmp = item:GetComponent(ItemCook)
    if cmp ~= nil and cmp:GetState() == EItemCookState.Empty then
      instru = item
      break
    end
  end
  if 0 < #items1 and instru ~= nil then
    local targetPos = instru:GetPosition()
    local minDistance, targetItem
    for _, item in ipairs(items1) do
      local itemPos = item:GetPosition()
      local distance = math.abs(itemPos:GetX() - targetPos:GetX()) + math.abs(itemPos:GetY() - targetPos:GetY())
      if minDistance == nil or minDistance > distance then
        minDistance = distance
        targetItem = item
      end
    end
    self:_ExecuteStep1(targetItem, instru)
    return true
  end
end

function Executer:_ExecuteStep1(item1, item2)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.Merge
  self:_SaveOngoingDatas()
  local from = item1:GetPosition()
  local to = item2:GetPosition()
  GM.TutorialModel:SetForceSourceBoardPosition(from)
  GM.TutorialModel:SetForceTargetBoardPosition(to)
  self.m_gesture = TutorialHelper.DragOnItems(from, to)
  if not self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
    return
  end
  TutorialHelper.MaskOnItemBoard(from, to)
  TutorialHelper.ShowDialogWithBoardMaskArea(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), from, to)
end

function Executer:_TryExecuteStep2()
  local items = TutorialHelper.GetItems(instruItemId)
  if #items <= 0 then
    return
  end
  local itemCook = items[1]:GetComponent(ItemCook)
  if itemCook:GetState() ~= EItemCookState.CanCook then
    return
  end
  local material = itemCook:GetCurMaterialsArray()
  if not Table.ListContain(material, materialItemId) then
    return
  end
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    boardView:UpdateSelectedItem(items[1])
  end
  self:_ExecuteStep2()
  return true
end

function Executer:_ExecuteStep2()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ShowInfo
  self:_SaveOngoingDatas()
  TutorialHelper.HighlightBoardInfoBar()
  TutorialHelper.ToggleBoardHighlightRaycast(false)
  TutorialHelper.WholeMask(function()
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    TutorialHelper.DehighlightBoardInfoBar()
    TutorialHelper.ToggleBoardHighlightRaycast(true)
    if not self:_TryExecuteStep3() then
      self:Finish(self.m_gesture)
    end
  end)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

function Executer:_TryExecuteStep3()
  local items = TutorialHelper.GetItems(instruItemId)
  if #items <= 0 then
    return
  end
  local itemCook = items[1]:GetComponent(ItemCook)
  if itemCook:GetState() ~= EItemCookState.CanCook then
    return
  end
  local material = itemCook:GetCurMaterialsArray()
  if not Table.ListContain(material, materialItemId) then
    return
  end
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    boardView:UpdateSelectedItem(items[1])
  end
  self:_ExecuteStep3()
  return true
end

function Executer:_ExecuteStep3()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.StartCook
  self:_SaveOngoingDatas()
  TutorialHelper.WholeMask()
  if GM.ConfigModel:UseBoardCookBubble() then
    local boardView = MainBoardView.GetInstance()
    if boardView ~= nil then
      local item = boardView:GetBoardInfoBarItem()
      if item then
        local itemPosition = item:GetPosition()
        TutorialHelper.MaskOnItemBoard(itemPosition, itemPosition, nil, false)
      end
    end
    MainBoardView:GetInstance():ShowCookStartTip(false, true)
    TutorialHelper.HighlightBoardCookBubbleBtnGo()
    self.m_bHightlightBoardCookBubble = true
  else
    local btn = TutorialHelper.HighlightBoardInfoCookBtnGo()
    self.m_gesture = TutorialHelper.TapOnBoardInfoBarButton(btn)
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  end
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

function Executer:_TryExecuteStep4()
  local items = TutorialHelper.GetItems(instruItemId)
  if #items <= 0 then
    return
  end
  local itemCook = items[1]:GetComponent(ItemCook)
  if itemCook:GetState() ~= EItemCookState.Cooking then
    return
  end
  local material = itemCook:GetCurMaterialsArray()
  if not Table.ListContain(material, materialItemId) then
    return
  end
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    boardView:UpdateSelectedItem(items[1])
  end
  itemCook:SetTutorialCD()
  self:_ExecuteStep4()
  return true
end

function Executer:_ExecuteStep4()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.SkipCook
  self:_SaveOngoingDatas()
  TutorialHelper.WholeMask()
  local btn = TutorialHelper.HighlightBoardInfoSkipBtnGo()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  self.m_gesture = TutorialHelper.TapOnBoardInfoBarButton(btn)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_TryExecuteStep5()
  local items = TutorialHelper.GetItems(instruItemId)
  if #items <= 0 then
    return
  end
  local itemCook = items[1]:GetComponent(ItemCook)
  if itemCook:GetState() ~= EItemCookState.Cooked then
    return
  end
  local material = itemCook:GetCurMaterialsArray()
  if not Table.ListContain(material, materialItemId) then
    return
  end
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    boardView:UpdateSelectedItem(items[1])
  end
  self:_ExecuteStep5(items[1])
  return true
end

function Executer:_ExecuteStep5(item)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.TakeOut
  self:_SaveOngoingDatas()
  local tapPos = item:GetPosition()
  TutorialHelper.MaskOnItemBoard(tapPos, tapPos)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    TutorialHelper.ShowDialogWithBoardMaskArea(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), tapPos, tapPos)
    self.m_gesture = TutorialHelper.TapOnBoard(tapPos)
    if not self.m_gesture then
      GM.TutorialModel:ClearTempDatas()
      self:Finish()
      return
    end
    GM.TutorialModel:SetForceSourceBoardPosition(tapPos)
    GM.TutorialModel:SetForceTargetBoardPosition(tapPos)
  end, 1, self)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
