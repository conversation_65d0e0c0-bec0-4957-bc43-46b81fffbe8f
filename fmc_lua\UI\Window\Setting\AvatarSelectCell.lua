AvatarSelectCell = {}
AvatarSelectCell.__index = AvatarSelectCell

function AvatarSelectCell:Init(icon, selectCallback)
  self.icon = icon
  self.m_selectCallback = selectCallback
  self.m_avatar:SetAvatar(EAvatarFrame.Normal, self.icon)
end

function AvatarSelectCell:OnSelected()
  self.m_selectCallback(self.icon)
end

function AvatarSelectCell:UpdateContent(selected)
  self.m_avatar:UpdateFrame(selected and EAvatarFrame.Highlight or EAvatarFrame.Normal)
  self.m_checkedGo:SetActive(selected)
end

function AvatarSelectCell:GetIcon()
  return self.icon
end
