PinataOrderCell = setmetatable({}, DashActivityOrderCell)
PinataOrderCell.__index = PinataOrderCell

function PinataOrderCell:Init(activityType, orderArea)
  DashActivityOrderCell.Init(self, activityType, orderArea)
  self.m_arrHitLevelMinScore = {}
  local targetItem = {
    ItemType.pinata_1,
    ItemType.pinata_2,
    ItemType.pinata_3,
    ItemType.pinata_4
  }
  for index, itemType in ipairs(targetItem) do
    local itemConfig = GM.ItemDataModel:GetModelConfig(itemType, true)
    if itemConfig ~= nil and itemConfig.CollectRewards ~= nil and #itemConfig.CollectRewards > 0 then
      self.m_arrHitLevelMinScore[#self.m_arrHitLevelMinScore + 1] = itemConfig.CollectRewards[1][PROPERTY_COUNT]
    elseif GameConfig.IsTestMode() then
      Log.Error("皮纳塔棋子配置缺失：pinata_" .. index)
    end
  end
  table.sort(self.m_arrHitLevelMinScore, function(a, b)
    return a < b
  end)
  if self.m_arrPinataOrigin == nil then
    self.m_arrPinataOrigin = {}
    for i = 1, 8 do
      self.m_arrPinataOrigin[i] = self["m_pinata" .. i]
    end
  end
  self:_UpdatePinata()
  EventDispatcher.AddListener(EEventType.PinataUpdateOrderCell, self, self.ShowPinata)
end

function PinataOrderCell:_ResetContent()
  self.m_score = nil
  self.m_level = nil
  self:_UpdatePinata(true)
  self:UpdateContent()
end

function PinataOrderCell:ShowPinata()
  if self.m_curPinata ~= nil then
    UIUtil.SetActive(self.m_curPinata.gameObject, true)
    self.m_curPinata:PlayEnterAnimation()
  end
end

function PinataOrderCell:OnDisable()
  if self.m_curPinata ~= nil then
    UIUtil.SetActive(self.m_curPinata.gameObject, false)
  end
end

function PinataOrderCell:_TryScrollToOrderCell()
end

function PinataOrderCell:UpdateScore()
  if self.m_playingAnimation then
    return
  end
  local sliderAnimationDuration = self.m_scoreSlider:UpdateScore()
  local score = self.m_model:GetMilestoneScore()
  local delta = score - (self.m_score or 0)
  if self.m_score ~= nil and score > self.m_score and self.m_curPinata ~= nil and self.m_curPinata.gameObject.activeSelf then
    local delta = score - self.m_score
    local level = 1
    for i, minScore in ipairs(self.m_arrHitLevelMinScore) do
      if minScore <= delta then
        level = i
      end
    end
    self.m_curPinata:PlayHitAnimation(level)
  end
  self.m_score = score
  local level = self.m_model:GetMilestoneLevel()
  local targetScore = self.m_model:GetMilestoneTargetScore(level)
  if self.m_level ~= nil and self.m_level ~= level then
    self:_UpdatePinata()
  end
  self.m_level = level
  if score >= targetScore then
    DelayExecuteFuncInView(function()
      local score = self.m_model:GetMilestoneScore()
      local level = self.m_model:GetMilestoneLevel()
      local targetScore = self.m_model:GetMilestoneTargetScore(level)
      if score >= targetScore then
        if not self.m_button.gameObject.activeSelf then
          self.m_radianceGo:SetActive(true)
          self.m_starEffectGo:SetActive(true)
          self.m_button.gameObject:SetActive(true)
          if self.m_buttonTween == nil then
            local sequence = DOTween.Sequence()
            sequence:Append(self.m_button:DOScale(1.1, 0.4):SetLoops(2, LoopType.Yoyo))
            sequence:Join(self.m_buttonImage:DOColor(CSColor(0.8, 0.8, 0.8, 1), 0.4):SetLoops(2, LoopType.Yoyo))
            sequence:AppendInterval(0.2)
            sequence:SetLoops(-1)
            self.m_buttonTween = sequence
          end
        end
        self.m_boardGo:SetActive(false)
      else
        self.m_boardGo:SetActive(true)
      end
    end, sliderAnimationDuration, self)
  else
    self.m_radianceGo:SetActive(false)
    self.m_starEffectGo:SetActive(false)
    self.m_button.gameObject:SetActive(false)
    self.m_boardGo:SetActive(true)
  end
end

function PinataOrderCell:OnServeButtonClicked()
  GM.UIManager:OpenView(self.m_activityDefinition.RewardWindowPrefabName)
end

function PinataOrderCell:OnMaskClicked()
  GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType)
end

function PinataOrderCell:_UpdatePinata(showPinata)
  local curOrigin = self:_GetPinataOrigin(self.m_model:GetLevel())
  if curOrigin ~= self.m_curPinataOrigin then
    self.m_curPinataOrigin = curOrigin
    if self.m_curPinata ~= nil then
      self.m_curPinata.gameObject:RemoveSelf()
      self.m_curPinata = nil
    end
    if self.m_curPinataOrigin ~= nil then
      self.m_curPinata = GameObject.Instantiate(self.m_curPinataOrigin, self.m_modelRootRectTrans):GetLuaTable()
      if showPinata then
        self.m_curPinata:PlayEnterAnimation()
      else
        self.m_curPinata:Hide()
      end
    end
  end
end

function PinataOrderCell:_GetPinataOrigin(level)
  return self.m_arrPinataOrigin[math.max(math.min(level, #self.m_arrPinataOrigin), 1)]
end
