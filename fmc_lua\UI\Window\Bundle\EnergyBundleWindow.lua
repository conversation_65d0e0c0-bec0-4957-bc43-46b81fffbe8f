EnergyBundleWindow = setmetatable({}, BundleNormalWindow)
EnergyBundleWindow.__index = EnergyBundleWindow

function EnergyBundleWindow:Init(bundleType, dataGroup, bUserClick, eTriggerType)
  BundleNormalWindow.Init(self, bundleType, dataGroup, bUserClick, eTriggerType)
  self.m_spineAnim:Initialize(false)
  self.m_lightSpineAnim:Initialize(false)
  self.m_spineAnim.AnimationState:SetAnimation(0, "init", true)
  self.m_lightSpineAnim.AnimationState:SetAnimation(0, "init", true)
  DelayExecuteFuncInView(function()
    self.m_spineAnim.AnimationState:SetAnimation(0, "appear", false)
    self.m_spineAnim.AnimationState:AddAnimation(0, "idle", true, 0)
    self.m_lightSpineAnim.AnimationState:SetAnimation(0, "appear", false)
    self.m_lightSpineAnim.AnimationState:AddAnimation(0, "idle", true, 0)
  end, 0.15, self, false)
end
