PkRaceBoardEntry = {}
PkRaceBoardEntry.__index = PkRaceBoardEntry

function PkRaceBoardEntry:Awake()
  self.m_bAwaked = true
  if self.m_bWaitAddListener then
    self:_AddListeners()
  end
end

function PkRaceBoardEntry:Init(model, orderArea)
  self.m_model = model
  self.m_activityType = self.m_model:GetType()
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  self.m_orderArea = orderArea
  self.m_lastUpdateTime = 0
  self.m_iconArea:Init(self.m_activityType)
  self.m_iconArea:SetInBoardView()
  if self.m_bAwaked then
    self:_AddListeners()
  else
    self.m_bWaitAddListener = true
  end
  self:UpdateContent()
  self:UpdatePerSecond()
end

function PkRaceBoardEntry:_AddListeners()
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self.UpdateContent)
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.OpenMainWindow, {
    obj = self,
    method = self.UpdateContent
  })
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.ScoreChanged, {
    obj = self,
    method = self._UpdateRankText
  })
end

function PkRaceBoardEntry:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
  Scheduler.UnscheduleTarget(self)
end

function PkRaceBoardEntry:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  elseif self.gameObject.activeSelf then
    self.gameObject:SetActive(false)
  end
  local serTime = GM.GameModel:GetServerTime()
  if serTime - self.m_lastUpdateTime > 120 then
    self.m_lastUpdateTime = serTime
    self:_UpdateRankText()
  end
end

function PkRaceBoardEntry:UpdateContent()
  if self.m_model == nil then
    return
  end
  self.m_iconArea:SyncToModelValue()
  self:_UpdateRankText()
end

function PkRaceBoardEntry:_UpdateRankText()
  if self.m_model == nil or not self.m_model:HasNetwork() then
    return
  end
  self.m_exclamationGo:SetActive(self.m_model:CanShowExclaimation())
  local rank = not self.m_model:IsInRace() and -1 or self.m_model:GetMyRank(true)
  UIUtil.SetActive(self.m_firstRankGo, rank == 1)
  UIUtil.SetActive(self.m_secondRankGo, rank == 2)
end

function PkRaceBoardEntry:OnBtnClicked()
  local state = self.m_model:GetState()
  if self.m_model:IsInRace() then
    self.m_model:TryOpenMainWindow()
  else
    GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, false, true)
  end
end

function PkRaceBoardEntry:GetIconArea()
  return self.m_iconArea
end

PkRaceHudButton = setmetatable({}, HudPropertyButton)
PkRaceHudButton.__index = PkRaceHudButton

function PkRaceHudButton:Init(activityType)
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  HudPropertyButton.Init(self, self.m_activityDefinition.ActivityTokenPropertyType)
end

function PkRaceHudButton:UpdateValueText()
  if self.m_valueText ~= nil and self.m_model ~= nil then
    local targetValue = self.m_model:GetTargetScore()
    self.m_valueText.text = math.floor(self.m_value + 0.5) .. "/" .. targetValue
  end
end
