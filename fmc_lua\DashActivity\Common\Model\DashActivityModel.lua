DashActivityEventType = {
  StateChanged = 1,
  Upgraded = 2,
  ScoreChanged = 3
}
DashActivityModel = setmetatable({}, BaseActivityModel)
DashActivityModel.__index = DashActivityModel
DashActivityModel.LevelKey = "level"
DashActivityModel.ScoreKey = "score"
DashActivityModel.FinishedLevelKey = "finishedLevel"
DashActivityModel.RoundKey = "round"
DashActivityModel.PlayEntryFinal = "playEntryFinal"

function DashActivityModel:Init(activityType, virtualDBTable)
  self.m_activityDefinition = DashActivityDefinition[activityType]
  BaseActivityModel.Init(self, activityType, virtualDBTable)
  EventDispatcher.AddListener(EEventType.BoardCollect, self, self._OnBoardCollect)
end

function DashActivityModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function DashActivityModel:_LoadOtherServerConfig(config)
  if self.m_type == ActivityType.CashDash or self.m_type == ActivityType.Lollipop or self.m_type == ActivityType.Coconut then
    self:_LoadOtherServerConfigOld(config)
  else
    self:_LoadOtherServerConfigNew(config)
  end
end

function DashActivityModel:_LoadOtherServerConfigOld(config)
  self.m_config.levelConfigs = {}
  local lastItem
  for _, lConfig in ipairs(config[self.m_activityDefinition.RewardConfigName]) do
    local levelConfigItem = {}
    levelConfigItem.score = lConfig.score
    levelConfigItem.rewards = ConfigUtil.GetCurrencyFromArrStr(lConfig.rewards)
    if lConfig.level == 0 then
      lastItem = levelConfigItem
    else
      self.m_config.levelConfigs[lConfig.level] = levelConfigItem
    end
  end
  self.m_config.levelConfigs[#self.m_config.levelConfigs + 1] = lastItem
end

function DashActivityModel:_LoadOtherServerConfigNew(config)
  self.m_config.levelConfigs = {}
  for _, lConfig in ipairs(config[self.m_activityDefinition.RewardConfigName]) do
    local levelConfigItem = {}
    levelConfigItem.score = lConfig.score
    levelConfigItem.rewards = Table.DeepCopy(lConfig.rewards, true)
    self.m_config.levelConfigs[lConfig.level] = levelConfigItem
  end
end

function DashActivityModel:_OnStateChanged()
  self.event:Call(DashActivityEventType.StateChanged)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
end

function DashActivityModel:GetResourceLabels()
  return {
    AddressableLabel.DashCommon,
    self.m_activityDefinition.ResourceLabel
  }
end

function DashActivityModel:GetBoardEntryShowConfig()
  return {
    statusChangeName = self.m_activityDefinition.StateChangedEvent,
    listener2 = self.m_activityDefinition.UpgradedEvent,
    bubbleName = "m_" .. self.m_type .. "Order",
    bubbleNodeName = "m_dashActivityOrderNode",
    entryPrefabName = self.m_activityDefinition.OrderCellPrefabName,
    bubbleCountName = "m_dashActivityOrderCount",
    scrollToFunName = "ScrollToDashActivityOrder",
    activityType = self:GetType(),
    bSetSize = true,
    checkFun = function()
      return self:CanAddScore()
    end
  }
end

function DashActivityModel:CanShowDashAcivityEntry()
  local state = self:GetState()
  if state ~= ActivityState.Preparing and state ~= ActivityState.Started then
    return false
  end
  local level = self:GetLevel()
  return level <= #self.m_config.levelConfigs
end

function DashActivityModel:LogActivity(scene, action)
  GM.BIManager:LogActivity(self.m_type, self:GetId() .. "_" .. self:GetCurrentRound(), scene, action)
end

function DashActivityModel:GetCurrentRound()
  return self.m_dbTable:GetValue(DashActivityModel.RoundKey, "value") or 1
end

function DashActivityModel:GetLevelConfigs()
  return self.m_config.levelConfigs
end

function DashActivityModel:GetLevel()
  return self.m_dbTable:GetValue(DashActivityModel.LevelKey, "value") or 1
end

function DashActivityModel:GetScore()
  return self.m_dbTable:GetValue(DashActivityModel.ScoreKey, "value") or 0
end

function DashActivityModel:SetPlayEntryFinal()
  self.m_dbTable:Set(DashActivityModel.PlayEntryFinal, "value", 1)
end

function DashActivityModel:IsPlayEntryFinal()
  local pef = self.m_dbTable:GetValue(DashActivityModel.PlayEntryFinal, "value") or 0
  return pef == 1
end

function DashActivityModel:GetFinishLevel()
  return self.m_dbTable:GetValue(DashActivityModel.FinishedLevelKey, "value") or 0
end

function DashActivityModel:GetMilestoneLevel()
  return self:GetLevel()
end

function DashActivityModel:GetMilestoneScore()
  return self:GetScore()
end

function DashActivityModel:GetMilestoneTargetScore(lvl)
  local level = lvl or self:GetLevel()
  return self.m_config.levelConfigs[level].score
end

function DashActivityModel:CanAddScore()
  if self:GetState() ~= ActivityState.Started then
    return false
  end
  local level = self:GetLevel()
  return level <= #self.m_config.levelConfigs
end

function DashActivityModel:_OnBoardCollect(message)
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  local score = 0
  for _, reward in ipairs(message.Rewards) do
    if reward[PROPERTY_TYPE] == self.m_activityDefinition.ScorePropertyType then
      score = score + reward[PROPERTY_COUNT]
    end
  end
  if score ~= 0 then
    self:AddScore(score)
  end
end

function DashActivityModel:AddScore(delta)
  if not self:CanAddScore() then
    return
  end
  self:_AddScore(delta)
end

function DashActivityModel:_AddScore(delta)
  local level = self:GetLevel()
  local newScore = self:GetScore() + delta
  self:LogActivity(EBIType.ActivityAddScore, delta)
  self:LogActivity(EBIType.ActivityScoreChanged, newScore)
  local finishedLevel = level - 1
  local tempScore = newScore
  while level <= #self.m_config.levelConfigs do
    local levelConfig = self.m_config.levelConfigs[level]
    if tempScore < levelConfig.score then
      break
    end
    tempScore = tempScore - levelConfig.score
    finishedLevel = level
    level = level + 1
  end
  local dataFinishedLevel = self.m_dbTable:GetValue(DashActivityModel.FinishedLevelKey, "value") or 0
  if finishedLevel > dataFinishedLevel then
    self:LogActivity(EBIType.ActivityRankUp, finishedLevel)
  end
  self.m_dbTable:Set(DashActivityModel.FinishedLevelKey, "value", finishedLevel)
  self.m_dbTable:Set(DashActivityModel.ScoreKey, "value", newScore)
  self.event:Call(DashActivityEventType.ScoreChanged)
end

function DashActivityModel:Upgrade()
  local level = self:GetLevel()
  local levelConfig = self:GetLevelConfigs()[level]
  RewardApi.AcquireRewardsLogic(levelConfig.rewards, EPropertySource.Give, self.m_activityDefinition.GetRewardsBIType, EGameMode.Board)
  self:LogActivity(EBIType.ActivityGetRewards, level)
  local newScore = self:GetScore() - levelConfig.score
  self:LogActivity(EBIType.ActivityScoreChanged, newScore)
  self.m_dbTable:Set(DashActivityModel.ScoreKey, "value", newScore)
  self.m_dbTable:Set(DashActivityModel.LevelKey, "value", level + 1)
  self.event:Call(DashActivityEventType.Upgraded)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.UpgradedEvent)
end

function DashActivityModel:GetMaxRewardLevel()
  return 10
end

function DashActivityModel:CanPlayEntryFinalChallengeAnimation()
  return self:IsFinalChallengeing() and not self:IsPlayEntryFinal()
end

function DashActivityModel:IsFinalChallengeing()
  local maxRewardLevel = self:GetMaxRewardLevel()
  local totalLevel = #self.m_config.levelConfigs
  local curLevel = self:GetLevel()
  return maxRewardLevel < totalLevel and maxRewardLevel < curLevel
end

function DashActivityModel:HasFinalChallenge()
  local maxRewardLevel = self:GetMaxRewardLevel()
  local totalLevel = #self.m_config.levelConfigs
  return maxRewardLevel < totalLevel
end

function DashActivityModel:IsFinalSccuess()
  return self:HasFinalChallenge() and self:GetFinishLevel() == #self.m_config.levelConfigs
end
