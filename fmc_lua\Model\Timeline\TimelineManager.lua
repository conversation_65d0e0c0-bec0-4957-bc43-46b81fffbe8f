TimelineManager = {}
TimelineManager.__index = TimelineManager

function TimelineManager:Init()
  self.m_bInTimeline = false
  self.m_nStep = 0
  self.m_timelineData = nil
end

function TimelineManager:PlayTimeline(timelineId, chapterName, callback, onStart)
  Log.Assert(GM.SceneManager:GetGameMode() == EGameMode.Main, "时间线只能在 Main 场景播放")
  if self.m_bInTimeline then
    Log.Error("已经正在播时间线了！" .. tostring(self:GetCurTimelineId() or nil))
    return
  end
  self.m_bInTimeline = true
  GM.TimelineDataModel:GetTimelineData(timelineId, chapterName, false, function(timelineData)
    if not timelineData then
      EventDispatcher.DispatchEvent(EEventType.TimelineStart)
      GM.UIManager:SetEventLock(true)
      if onStart then
        onStart()
      end
      DelayExecuteFunc(function()
        GM.UIManager:SetEventLock(false)
        self.m_bInTimeline = false
        if callback then
          callback()
        end
        EventDispatcher.DispatchEvent(EEventType.TimelineComplete)
      end, 0.5)
      return
    end
    timelineData:Init()
    self:_StartPlay(timelineData, callback, onStart)
  end)
end

function TimelineManager:_StartPlay(timelineData, callback, onStart)
  self.m_timelineData = timelineData
  self.m_callback = callback
  self.m_nStep = 0
  self.m_skipping = false
  self.m_arrCurExecuterList = {}
  EventDispatcher.DispatchEvent(EEventType.TimelineStart)
  if timelineData.id ~= INTRO_TIMELINE_ID then
    EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {Show = false})
  end
  self:_NextStep()
  GM.TimelineLayer:ToggleEdgeMask(true)
  if onStart then
    onStart()
  end
end

function TimelineManager:_OnTimelineFinished()
  if self.m_callback then
    self.m_callback()
  end
  self.m_timelineData = nil
  self.m_bInTimeline = false
  self.m_skipping = false
  self.m_arrCurExecuterList = {}
  EventDispatcher.DispatchEvent(EEventType.TimelineComplete)
  EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {Show = true})
end

function TimelineManager:_NextStep()
  if not self.m_timelineData then
    return
  end
  self.m_nStep = self.m_nStep + 1
  if self.m_nStep > self.m_timelineData.stepCount then
    GM.BIManager:LogAction(EBIType.TimelineStep, GM.BIManager:TableToString({
      id = self.m_timelineData.id,
      step = "fin"
    }))
    if GameConfig.IsTestMode() then
      Log.Info("[Timeline][" .. self.m_timelineData.id .. "]时间线结束", nil, "#1E8C1C")
    end
    GM.TimelineLayer:ToggleEdgeMask(false, function()
      self:_OnTimelineFinished()
    end)
    return
  end
  local onFinish = function(step)
    self:_OnStepFinish(step)
  end
  self.count = #self.m_timelineData.stepList[self.m_nStep]
  GM.BIManager:LogAction(EBIType.TimelineStep, GM.BIManager:TableToString({
    id = self.m_timelineData.id,
    step = self.m_nStep
  }))
  self.m_arrCurExecuterList = {}
  if GameConfig.IsTestMode() then
    Log.Info("[Timeline][" .. self.m_timelineData.id .. "]第" .. self.m_nStep .. "步开始", nil, "#00CED1")
  end
  for k, step in pairs(self.m_timelineData.stepList[self.m_nStep]) do
    local timelineExecuter = TimelineExecuter[step.type]
    if GameConfig.IsTestMode() then
      Log.Info("[Timeline][" .. self.m_timelineData.id .. "][不跳过]" .. step.type .. ": " .. tostring(step.data.content), nil, "#FFA500")
    end
    table.insert(self.m_arrCurExecuterList, {executer = timelineExecuter, step = step})
    timelineExecuter.ExecuteFunc(self.m_timelineData, step, onFinish)
  end
end

function TimelineManager:_OnStepFinish(step)
  self.count = self.count - 1
  for _, v in ipairs(self.m_arrCurExecuterList) do
    if v.step == step then
      v.finish = true
      break
    end
  end
  if GameConfig.IsTestMode() and self.m_timelineData then
    Log.Info("[Timeline][" .. self.m_timelineData.id .. "]action结束，当前剩余action数量" .. self.count, nil, "#FFA500")
  end
  if self.count == 0 then
    self:_NextStep()
  end
end

function TimelineManager:IsPlayingTimeline()
  return self.m_bInTimeline
end

function TimelineManager:GetCurTimelineId()
  if not self:IsPlayingTimeline() then
    return nil
  end
  if not self.m_timelineData then
    return nil
  end
  return self.m_timelineData.id
end

function TimelineManager:IsSkipping()
  return self.m_skipping == true
end

function TimelineManager:Skip()
  if not self.m_timelineData then
    return
  end
  if self.m_skipping then
    return
  end
  self.m_skipping = true
  local step = self.m_nStep
  local onFinish = function(step)
    self:_OnStepFinish(step)
  end
  local info = {
    char = GM.ChapterDataModel:GetChapterIdByName(GM.ChapterManager.curActiveChapterName),
    id = self.m_timelineData.id,
    step = step
  }
  GM.BIManager:LogAction(EBIType.SkipTimelineBuild, info)
  local bReachEnd = true
  repeat
    bReachEnd = true
    local arrCurExecuterList = Table.ShallowCopy(self.m_arrCurExecuterList)
    for _, v in ipairs(arrCurExecuterList) do
      if not v.finish and v.executer.StopFunc then
        if GameConfig.IsTestMode() then
          Log.Info("[Timeline][" .. self.m_timelineData.id .. "]中断" .. v.step.type .. ": " .. v.step.data.Content, nil, "#FF6347")
        end
        v.executer.StopFunc(self.m_timelineData, v.step, onFinish)
        bReachEnd = false
      end
    end
  until bReachEnd
end
