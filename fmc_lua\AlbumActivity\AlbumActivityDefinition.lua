AlbumActivityDefinition = {}
local CreateActivityDefinition = function(activityType, prefix, overrideTable)
  local BaseTable = {
    ActivityDataTableName = VirtualDBTableName[prefix],
    EntryButtonKey = ESceneViewHudButtonKey[prefix],
    StateChangedEvent = EEventType[prefix .. "StateChanged"],
    StartWindowPrefabName = UIPrefabConfigName[prefix .. "StartWindow"],
    EndWindowPrefabName = UIPrefabConfigName[prefix .. "EndWindow"],
    MainWindowPrefabName = UIPrefabConfigName[prefix .. "MainWindow"],
    HelpWindowPrefabName = UIPrefabConfigName[prefix .. "HelpWindow"],
    PackWindowPrefabName = UIPrefabConfigName[prefix .. "PackWindow"],
    TakeSetRewardPrefabName = UIPrefabConfigName[prefix .. "TakeSetRewardWindow"],
    TakeFinishRewardPrefabName = UIPrefabConfigName[prefix .. "TakeFinishRewardWindow"],
    EntryPrefabName = UIPrefabConfigName[prefix .. "Entry"],
    CollectEntryPrefabName = UIPrefabConfigName[prefix .. "CollectEntry"],
    TutorialStartCondition = ETutorialStartCondition.AlbumActivityStart,
    ResourceLabels = {
      AddressableLabel.AlbumCommon,
      AddressableLabel[prefix]
    }
  }
  BaseTable.__index = BaseTable
  AlbumActivityDefinition[activityType] = setmetatable(overrideTable, BaseTable)
  AlbumActivityDefinition[activityType].__index = AlbumActivityDefinition[activityType]
end
CreateActivityDefinition(ActivityType.Album1, "Album1", {})
