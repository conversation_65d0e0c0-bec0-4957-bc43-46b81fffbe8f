BaseDBTable = {}
BaseDBTable.__index = BaseDBTable

function BaseDBTable:IsEmpty()
end

function BaseDBTable:GetValue(primaryValue, column)
end

function BaseDBTable:GetValues()
end

function BaseDBTable:Set(primaryValue, column, columnValue)
end

function BaseDBTable:BatchSet(primaryColumnsMap)
end

function BaseDBTable:Remove(primaryValue, column)
end

function BaseDBTable.IsValidValueType(type, value)
  if value == nil then
    return true
  end
  if type == EDBValueType.Text then
    return IsString(value)
  elseif type == EDBValueType.Integer then
    return IsNumber(value) and math.floor(value) == value
  elseif type == EDBValueType.Real then
    return IsNumber(value) and value >= math.floor(value)
  else
    Log.Error("不支持的类型检查, type=" .. tostring(type))
    return false
  end
end

function BaseDBTable.ConvertValueType(type, value)
  if value == nil then
    return value
  end
  if type == EDBValueType.Text then
    return tostring(value)
  elseif type == EDBValueType.Integer or type == EDBValueType.Real then
    return tonumber(value)
  else
    Log.Error("不支持的类型检查, type=" .. type)
    return value
  end
end
