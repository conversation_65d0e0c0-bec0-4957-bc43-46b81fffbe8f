local toStringEx = function(value)
  if value ~= nil and type(value) == "string" then
    return "\"" .. value .. "\""
  end
  return tostring(value)
end
local GetTableStr = function(tab)
  if not GameConfig.IsTestMode() then
    return
  end
  local tabRecord = {}
  
  local function printTableImp(tab, tabRecord, space)
    local ret = ""
    if tab == nil then
      return "nil"
    end
    local nextSpace = space .. "    "
    if type(tab) == "table" then
      ret = ret .. "\n" .. space .. "{\n"
      for k, v in pairs(tab) do
        ret = ret .. nextSpace .. "[" .. toStringEx(k) .. "]" .. " = " .. printTableImp(v, tabRecord, nextSpace)
      end
      ret = ret .. space .. "},"
    else
      ret = ret .. toStringEx(tab) .. ","
    end
    return ret .. "\n"
  end
  
  local str = printTableImp(tab, tabRecord, "")
  if StringUtil.StartWith(str, "\n") then
    str = string.sub(str, 2)
  end
  if StringUtil.EndWith(str, ",\n") then
    str = string.sub(str, 1, string.len(str) - 2)
  end
  return str
end
TestServerConfigInfoWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestServerConfigInfoWindow.__index = TestServerConfigInfoWindow

function TestServerConfigInfoWindow:Init()
  self:OnRefreshClick()
end

function TestServerConfigInfoWindow:OnRefreshClick()
  local config = GM.ConfigModel.m_serverConfigs
  self.m_configList = {}
  for k, v in pairs(config) do
    if IsString(k) then
      table.insert(self.m_configList, {
        key = k,
        value = v,
        expand = false
      })
    end
  end
  table.sort(self.m_configList, function(a, b)
    return string.lower(a.key) < string.lower(b.key)
  end)
  self:_UpdateListView()
end

function TestServerConfigInfoWindow:_UpdateListView()
  self.m_showList = {}
  for i, v in ipairs(self.m_configList) do
    table.insert(self.m_showList, {
      str = v.key,
      cell = "fieldCell",
      expand = v.expand
    })
    if v.expand then
      local str = GetTableStr(v.value)
      local lines = StringUtil.Split(str, "\n")
      for j, line in ipairs(lines) do
        table.insert(self.m_showList, {
          str = line,
          cell = "valueCell",
          expand = false
        })
      end
    end
  end
  if not self.m_bListViewInit then
    self.m_ListView:InitListView(#self.m_showList, function(listView, cellIndex)
      return self:_OnListItemByIndex(listView, cellIndex)
    end)
    self.m_bListViewInit = true
  end
  self.m_ListView:SetListItemCount(#self.m_showList, false)
  self.m_ListView:RefreshAllShownItem()
end

function TestServerConfigInfoWindow:_OnListItemByIndex(listView, index)
  if index < 0 then
    return nil
  end
  local data = self.m_showList[index + 1]
  local item = listView:NewListViewItem(data.cell)
  local luaTable = item.gameObject:GetLuaTable()
  luaTable:Init(data.str, self, index + 1, data.expand)
  return item
end

function TestServerConfigInfoWindow:OnClickField(key)
  for i, v in ipairs(self.m_configList) do
    if v.key == key then
      v.expand = not v.expand
      break
    end
  end
  self:_UpdateListView()
end

function TestServerConfigInfoWindow:OnCopyBtnClicked(key)
  local config
  for i, v in ipairs(self.m_configList) do
    if v.key == key then
      config = v.value
      break
    end
  end
  PlatformInterface.SetString2Clipboard(key .. " = " .. GetTableStr(config))
  GM.UIManager:ShowPrompt("已复制到剪贴板")
end

TestServerConfigInfoFieldCell = {}
TestServerConfigInfoFieldCell.__index = TestServerConfigInfoFieldCell

function TestServerConfigInfoFieldCell:Init(str, parent, index, expand)
  self.m_str.text = str
  self.m_parent = parent
  self.m_index = index
  self.m_arrowRectTrans:SetLocalRotationZ(expand and 0 or 90)
end

function TestServerConfigInfoFieldCell:OnClick()
  self.m_parent:OnClickField(self.m_str.text)
end

function TestServerConfigInfoFieldCell:OnCopyBtnClicked()
  self.m_parent:OnCopyBtnClicked(self.m_str.text)
end

TestServerConfigInfoValueCell = {}
TestServerConfigInfoValueCell.__index = TestServerConfigInfoValueCell

function TestServerConfigInfoValueCell:Init(str, parent, index)
  self.m_str.text = str
  self.m_parent = parent
  self.m_index = index
end
