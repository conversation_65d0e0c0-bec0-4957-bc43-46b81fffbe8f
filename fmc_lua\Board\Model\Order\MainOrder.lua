MainOrder = setmetatable({}, BaseOrder)
MainOrder.__index = MainOrder

function MainOrder.Create(orderModel, id, groupId, chapterId, avatarId, requirements, type, createTime, rewards, cleanGoldCount)
  local order = setmetatable({}, MainOrder)
  order:Init(orderModel, id, groupId, chapterId, avatarId, requirements, type, createTime, rewards, cleanGoldCount)
  return order
end

function MainOrder:Init(orderModel, id, groupId, chapterId, avatarId, requirements, type, createTime, rewards, cleanGoldCount)
  if id == nil then
    id = orderModel:GenerateOrderId()
  end
  BaseOrder.Init(self, id, requirements, createTime)
  self.m_orderModel = orderModel
  self.m_groupId = groupId
  self.m_chapterId = chapterId
  self.m_avatarId = avatarId
  self.m_type = type
  self.m_rewards = rewards
  self.m_cleanGoldCount = cleanGoldCount or 0
  GM.ItemDataModel:SetRewardsLocked(rewards)
end

function MainOrder:GetGroupId()
  return self.m_groupId
end

function MainOrder:GetChapterId()
  return self.m_chapterId
end

function MainOrder:GetType()
  return self.m_type
end

function MainOrder:GetAvatarId()
  return self.m_avatarId
end

function MainOrder:UpdateAvatarId(avatarId)
  self.m_avatarId = avatarId
end

function MainOrder:GetAvatarName()
  local avatarConfig = self.m_orderModel:GetAvatarConfig()
  if avatarConfig.BakeOutAvatars[self.m_avatarId] ~= nil then
    return avatarConfig.BakeOutAvatars[self.m_avatarId]
  elseif avatarConfig.NormalAvatars[self.m_avatarId] ~= nil then
    return avatarConfig.NormalAvatars[self.m_avatarId].Avatar
  end
  Log.Error("取不到订单的头像名称 " .. tostring(self.m_avatarId))
  return "Jenny"
end

function MainOrder:GetCleanGoldCount()
  return self.m_cleanGoldCount
end

function MainOrder:GetMaxPossibleRewardNumber()
  local number = 1
  local lollipopModel = GM.ActivityManager:GetModel(ActivityType.Lollipop)
  if lollipopModel and lollipopModel:CanAddScore() then
    number = number + 1
  end
  local pinataModel = GM.ActivityManager:GetModel(ActivityType.Pinata)
  if pinataModel and pinataModel:CanAddScore() then
    number = number + 1
  end
  return number
end

function MainOrder:GetRewards(forStorage)
  if forStorage then
    return self.m_rewards
  end
  local rewards = {}
  Table.ListAppend(rewards, self.m_rewards)
  local extraRewards = {}
  self:_AppendActivityRewards(extraRewards)
  if not Table.IsEmpty(extraRewards) then
    RewardApi.CryptRewards(extraRewards)
    Table.ListAppend(rewards, extraRewards)
  end
  return rewards
end

function MainOrder:_GetFirstRewardPropertyType()
  local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  if bakeOutModel and bakeOutModel:CanAcquireToken() then
    return EPropertyType.BakeOutToken
  end
  return EPropertyType.Gold
end

function MainOrder:_GetLollipopRewardCount(price)
  if price <= 80 then
    return 2
  elseif price <= 560 then
    return (price - 1) // 40 + 1
  elseif price <= 700 then
    return 15
  else
    return 16
  end
end

function MainOrder:GetScore(includeGold)
  local rewards = self:GetRewards(true)
  return self:_CalculateScore(rewards, includeGold)
end

local ExcludePiggyBankInnerType = {}

function MainOrder:GetPiggyBankAccumulateGemNum()
  if Table.ListContain(ExcludePiggyBankInnerType, self:GetType()) then
    return nil
  end
  local model = GM.ActivityManager:GetModel(ActivityType.PiggyBank)
  if model == nil or not model:IsActivityOpen() then
    return nil
  end
  local score = self:GetScore(true)
  return model:GetExchangeGemNum(score)
end
