BreakEggBaseWindow = setmetatable({}, BaseWindow)
BreakEggBaseWindow.__index = BreakEggBaseWindow

function BreakEggBaseWindow:Init()
  self.m_activityModel = GM.ActivityManager:GetModel(ActivityType.BreakEgg)
  if self.m_collectReward then
    self.m_collectReward:Init()
    self.m_collectReward:UpdateContent()
  end
  EventDispatcher.AddListener(EEventType.BreakEggStateChanged, self, self.Close)
end

function BreakEggBaseWindow:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  GM.UIManager:RemoveAllEventLocks(self)
end
