DigActivityMainWindow = setmetatable({}, DigActivityBaseWindow)
DigActivityMainWindow.__index = DigActivityMainWindow

function DigActivityMainWindow:BeforeOpenCheck()
  local model
  for activityType, _ in pairs(DigActivityDefinition) do
    model = GM.ActivityManager:GetModel(activityType)
    if model and model:GetState() == ActivityState.Started then
      return true
    end
  end
  return false
end

function DigActivityMainWindow:Init(activityType, bUserClick)
  local model = GM.ActivityManager:GetModel(activityType)
  local bFirstPop = not model:HasWindowOpenedOnce(ActivityState.Started)
  DigActivityBaseWindow.Init(self, activityType, bUserClick, true)
  UIUtil.SetActive(self.m_curtainGo, false)
  self.m_handEffectSortingGroup.sortingOrder = self.m_canvas.sortingOrder + 3
  self.m_grooveOriginPosX = self.m_titleContentRect.anchoredPosition.x
  self:_Init()
  if bFirstPop and GM.TutorialModel:IsTutorialFinished(ETutorialId.DigActivityFirstDig) then
    self:ShowHandEffectToHelpBtn(true)
  end
  self.m_activityBundleEntryRoot:Init(self.m_activityType)
  AddHandlerAndRecordMap(self.m_model.event, DigActivityEventType.StateChanged, {
    obj = self,
    method = self.OnStateChanged
  })
  return bFirstPop
end

function DigActivityMainWindow:_Init(bChangeRound)
  if self.m_model:HasFinishedCurRound() and not self.m_model:HasFinishedAllRound() then
    self.m_model:EnterNextRound()
  end
  self.m_levelConfig = self.m_model:GetCurLevelConfig()
  if self.m_levelConfig == nil then
    self:Close()
    return
  end
  self:UpdateDigItemGrooves()
  self:UpdateRewardSlider()
  self:InitBoardView()
  if not bChangeRound then
    self:UpdateBoardViewSize()
  end
  self:ShowHandEffectToHelpBtn(false)
  self:UpdatePerSecond()
end

function DigActivityMainWindow:OnStateChanged()
  if self.m_model ~= nil and self.m_model:HasFinishedAllRound() then
    return
  end
  if self.m_digActivityBoardView:IsPlayingAnimation() then
    self.m_bActivityStateChanged = true
    return
  end
  self.m_bActivityStateChanged = false
  self:Close()
end

function DigActivityMainWindow:OnDestroy()
  DigActivityBaseWindow.OnDestroy(self)
  if self.m_eventLocked then
    self.m_eventLocked = false
    GM.UIManager:SetEventLock(false)
  end
  if self.m_sliderTween ~= nil then
    self.m_sliderTween:Kill()
    self.m_sliderTween = nil
  end
  EventDispatcher.RemoveTarget(self)
  Scheduler.UnscheduleTarget(self)
end

function DigActivityMainWindow:UpdateRewardSlider()
  local arrRoundConfig = self.m_model:GetAllRoundConfig()
  if Table.IsEmpty(arrRoundConfig) or self.m_rewardBoxGo == nil then
    return
  end
  local curRound = self.m_model:GetRound()
  local totalRound = self.m_model:GetTotalRound()
  UIUtil.SetActive(self.m_rewardBoxGo, false)
  self.m_roundText.text = tostring(curRound)
  local totalLength = self:GetSliderTotalLength()
  UIUtil.SetSizeDelta(self.m_sliderRect, totalLength)
  self.m_arrRewardBox = self.m_arrRewardBox or {}
  if self.m_arrRewardBox[#arrRoundConfig] == nil then
    self.m_arrRewardBox[#arrRoundConfig] = self.m_endRewardBox
  end
  local go
  for round, roundConfig in ipairs(arrRoundConfig) do
    if self.m_arrRewardBox[round] == nil then
      go = GameObject.Instantiate(self.m_rewardBoxGo, self.m_rewardBoxGo.transform.parent)
      UIUtil.SetActive(go, true)
      self.m_arrRewardBox[round] = go:GetLuaTable()
      UIUtil.SetAnchoredPosition(go.transform, self:GetBoxSpaceLength() * round)
    end
    self.m_arrRewardBox[round]:Init(roundConfig, round < curRound, self)
  end
  self:SetSliderValue((curRound - 1) / totalRound)
  local norPos = self:_GetMiddleHorizontalNormalizedPosition(curRound - 1)
  self.m_rewardScrollRect.horizontalNormalizedPosition = norPos
end

function DigActivityMainWindow:SetSliderValue(ratio)
  UIUtil.SetSizeDelta(self.m_sliderImgRect, self:GetSliderValue(ratio))
end

function DigActivityMainWindow:GetSliderValue(ratio)
  local fixLength = 0 < ratio and 500 or 0
  return fixLength + self:GetSliderTotalLength() * ratio
end

function DigActivityMainWindow:UpdateBoardViewSize()
  local targetScale = self:GetBoardViewAdjustScale()
  UIUtil.SetLocalScale(self.m_boardViewRootRect, targetScale, targetScale)
end

function DigActivityMainWindow:GetBoardViewAdjustScale()
  self.m_originBoardPosY = self.m_originBoardPosY or self.m_boardViewRootRect.anchoredPosition.y
  local maxSizeDelta = self.m_boardViewRootRect.sizeDelta
  local width, height = self.m_digActivityBoardView:GetRealBoardFrameSize()
  local frameSize = 150
  height = height + (self.m_activityDefinition.BoardBottomMargin or 0) + frameSize
  width = width + frameSize
  local wScale = math.min(maxSizeDelta.x / width, 1)
  local hScale = math.min(maxSizeDelta.y / height, 1)
  local targetScale = math.min(wScale, hScale)
  return targetScale
end

function DigActivityMainWindow:GetSliderTotalLength()
  local totalRound = self.m_model:GetTotalRound()
  return self:GetBoxSpaceLength() * totalRound
end

function DigActivityMainWindow:_GetMiddleHorizontalNormalizedPosition(round)
  local targetSliderLength = self:GetBoxSpaceLength() * round
  local progressNodeLength = self.m_contentRect.rect.width
  if targetSliderLength > progressNodeLength / 2 then
    local progressContentLength = self:GetSliderTotalLength()
    local normalized = (progressContentLength - targetSliderLength - progressNodeLength / 2) / (progressContentLength - progressNodeLength)
    return math.min(1 - normalized, 1)
  else
    return 0
  end
end

function DigActivityMainWindow:UpdatePerSecond()
  if self.m_model ~= nil and self.m_model:GetState() == ActivityState.Ended then
    UIUtil.SetActive(self.m_countdownGo, false)
    return
  end
  if self.m_model ~= nil then
    local nextStateTime = self.m_model:GetNextStateTime()
    if nextStateTime ~= nil then
      local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
      self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
    end
  end
end

function DigActivityMainWindow:InitBoardView()
  self.m_digActivityBoardView:Init(self.m_model, self)
end

function DigActivityMainWindow:UpdateDigItemGrooves()
  self.m_arrDigItemGroove = self.m_arrDigItemGroove or {}
  for _, groove in ipairs(self.m_arrDigItemGroove) do
    UIUtil.SetActive(groove.gameObject, false)
  end
  local arrDigItemData = self.m_model:GetCurDigItemDataList() or {}
  local bAdapt = #arrDigItemData <= self.m_activityDefinition.ItemGrooveMaxNum // 2
  local go, scale
  for i = 1, #arrDigItemData do
    if self.m_arrDigItemGroove[i] == nil then
      go = GameObject.Instantiate(self.m_digItemGrooveGo, self.m_digItemGrooveGo.transform.parent)
      self.m_arrDigItemGroove[i] = go:GetLuaTable()
    end
    self.m_arrDigItemGroove[i]:Init(arrDigItemData[i], self.m_model:HasAcquiredItem(i))
    UIUtil.SetActive(self.m_arrDigItemGroove[i].gameObject, true)
    scale = bAdapt and 1 or 0.5
    UIUtil.SetLocalScale(self.m_arrDigItemGroove[i].transform, scale, scale)
  end
end

function DigActivityMainWindow:UpdateDigItemGrooveByIndex(index)
  local arrDigItemData = self.m_model:GetCurDigItemDataList() or {}
  if index == nil or self.m_arrDigItemGroove[index] == nil then
    return
  end
  self.m_arrDigItemGroove[index]:Init(arrDigItemData[index], self.m_model:HasAcquiredItem(index))
end

function DigActivityMainWindow:GetDigGrooveItem(index)
  return self.m_arrDigItemGroove[index]
end

function DigActivityMainWindow:OnDigItemFlyAnimFinished(index, digScore)
  self:UpdateDigItemGrooveByIndex(index)
  if self.m_model:HasFinishedCurRound() and not self.m_digActivityBoardView:IsPlayingAnimation() then
    local closeCallback = function()
      if self == nil or self.gameObject == nil or self.gameObject:IsNull() then
        return
      end
      if not self.m_model:HasFinishedAllRound() then
        GM.UIManager:SetEventLock(true)
        self.m_eventLocked = true
        DelayExecuteFuncInView(function()
          GM.UIManager:SetEventLock(false)
          self.m_eventLocked = false
          self:PlayEnterAnim()
        end, 2, self)
      else
        self:Close()
        GM.UIManager:OpenView(self.m_activityDefinition.SuccessWindowPrefabName, self.m_activityType)
      end
    end
    local rewardWindowName = self.m_activityDefinition.RewardWindowPrefabName or UIPrefabConfigName.RewardWindow
    self:PlayRewardSliderAnim(rewardWindowName, closeCallback)
  end
  if not self.m_model:HasFinishedCurRound() and not self.m_digActivityBoardView:IsPlayingAnimation() then
    self.m_model:TryTriggerLackDigToken()
  end
  if self.m_bActivityStateChanged then
    self:OnStateChanged()
  end
end

function DigActivityMainWindow:PlayRewardSliderAnim(rewardWindowName, closeCallback)
  local curRound = self.m_model:GetRound()
  local targetValue = curRound / self.m_model:GetTotalRound()
  self.m_eventLocked = true
  GM.UIManager:SetEventLock(true)
  self.m_rewardScrollRect.horizontalNormalizedPosition = self:_GetMiddleHorizontalNormalizedPosition(curRound - 1)
  self.m_rewardScrollRect:DOHorizontalNormalizedPos(self:_GetMiddleHorizontalNormalizedPosition(curRound), 0.5)
  self.m_sliderTween = self.m_sliderImgRect:DOSizeDelta(Vector2(self:GetSliderValue(targetValue), self.m_sliderImgRect.sizeDelta.y), 0.5):OnComplete(function()
    self.m_eventLocked = false
    GM.UIManager:SetEventLock(false)
    self.m_sliderTween = nil
    self:PlayQuitAnim()
    GM.UIManager:OpenView(rewardWindowName, self.m_model:GetCurRoundReward(), nil, true, nil, closeCallback)
    if self.m_arrRewardBox ~= nil and self.m_arrRewardBox[curRound] ~= nil then
      self.m_arrRewardBox[curRound]:SetCheckEnable(true)
    end
  end)
end

function DigActivityMainWindow:GetBoxSpaceLength()
  return 200
end

function DigActivityMainWindow:OnDigGridAnimFinished()
  if self.m_bActivityStateChanged then
    self:OnStateChanged()
  end
  if not self.m_digActivityBoardView:IsPlayingAnimation() then
    self.m_model:TryTriggerLackDigToken()
  end
end

function DigActivityMainWindow:PlayQuitAnim()
  self.m_titleContentRect:DOAnchorPosX(self.m_grooveOriginPosX - 1500, 0.3):SetEase(Ease.OutCubic)
  self.m_boardViewRootRect:DOScale(Vector3(0, 0, 1), 0.3):SetEase(Ease.OutCubic)
end

function DigActivityMainWindow:PlayEnterAnim()
  self:_Init(true)
  self.m_titleContentRect:DOAnchorPosX(self.m_grooveOriginPosX, 0.5):SetEase(Ease.OutCubic)
  local targetScale = self:GetBoardViewAdjustScale()
  DOTween.Sequence():Append(self.m_boardViewRootRect:DOScale(Vector3(targetScale + 0.1, targetScale + 0.1, 1), 0.4):SetEase(Ease.OutSine)):Append(self.m_boardViewRootRect:DOScale(Vector3(targetScale, targetScale, 1), 0.1):SetEase(Ease.InCubic)):AppendCallback(function()
    if self.m_model ~= nil then
      self.m_model:TryTriggerLackDigToken()
    end
  end)
end

function DigActivityMainWindow:OnHelpBtnClicked()
  GM.UIManager:OpenView(self.m_activityDefinition.HelpWindowPrefabName, self.m_activityType, true)
  self:ShowHandEffectToHelpBtn(false)
  self:HideRewardBubble()
end

function DigActivityMainWindow:OnCloseBtnClick()
  self:HideRewardBubble()
  DigActivityBaseWindow.OnCloseBtnClick(self)
end

function DigActivityMainWindow:HideRewardBubble()
  self.m_rewardBubble:Hide(false)
end

function DigActivityMainWindow:ShowHandEffectToHelpBtn(bShow)
  if self.m_helpHandEffectGo ~= nil then
    UIUtil.SetActive(self.m_helpHandEffectGo, bShow or false)
  end
end

function DigActivityMainWindow:GetBoardViewRect()
  return self.m_digActivityBoardView.gameObject.transform
end

function DigActivityMainWindow:GetTitleContentRect()
  return self.m_titleContentRect
end

function DigActivityMainWindow:GetHelpBtnRect()
  return self.m_helpBtnGo.transform
end

function DigActivityMainWindow:SetTargetGridForTutorial(bOpen, pos)
  self.m_digActivityBoardView:SetTargetGridForTutorial(bOpen, pos)
end

function DigActivityMainWindow:GetTwoValidGridPos()
  return self.m_digActivityBoardView:GetTwoValidGridPos()
end

function DigActivityMainWindow:GetGridRectTrans(x, y)
  return self.m_digActivityBoardView:GetGridRectTrans(x, y)
end

function DigActivityMainWindow:ShowBubble(reward, rect, offsetX, offsetY)
  local scrollRect = self.m_rewardScrollRect
  if scrollRect.horizontalNormalizedPosition < 0 then
    self.m_skipHideRewardBubbleOnce = true
    scrollRect.horizontalNormalizedPosition = 0
  elseif scrollRect.horizontalNormalizedPosition > 1 then
    self.m_skipHideRewardBubbleOnce = true
    scrollRect.horizontalNormalizedPosition = 1
  else
    scrollRect:StopMovement()
  end
  self.m_rewardBubble:Show(reward, rect, offsetX, offsetY, true)
end

function DigActivityMainWindow:TryHideBubble()
  if self.m_skipHideRewardBubbleOnce then
    self.m_skipHideRewardBubbleOnce = nil
    return
  end
  self:HideRewardBubble()
end

function DigActivityMainWindow:OnBgClicked()
  self:HideRewardBubble()
end

function DigActivityMainWindow:OnDigGridClicked()
  self:HideRewardBubble()
end

function DigActivityMainWindow:OnScrollValueChanged()
  self:TryHideBubble()
end

function DigActivityMainWindow:GetHudButton()
  return self.m_digActivityBoardView:GetHudButton()
end

DigItemGroove = {}
DigItemGroove.__index = DigItemGroove

function DigItemGroove:Init(digItemData, bAcquired)
  SpriteUtil.SetImage(self.m_iconImg, digItemData.imgKey, true)
  SpriteUtil.SetImage(self.m_grooveIconImg, digItemData.lockedImgKey, true)
  local eulerAngle = Quaternion.Euler(Vector3(0, 0, digItemData.grooveRotate or 0))
  self.m_iconImg.transform.localRotation = eulerAngle
  UIUtil.SetLocalScale(self.m_iconImg.transform, digItemData.unlockedScale, digItemData.unlockedScale, 1)
  UIUtil.SetActive(self.m_iconImg.gameObject, bAcquired)
  UIUtil.SetActive(self.m_grooveIconImg.gameObject, not bAcquired)
  self.m_digItemData = digItemData
end

function DigItemGroove:GetIconRect()
  return self.m_iconImg.transform
end

function DigItemGroove:GetGrooveRotate()
  return self.m_digItemData.grooveRotate
end

function DigItemGroove:GetGrooveScale()
  return self.m_digItemData.unlockedScale * self.transform.localScale.x
end

DigRewardItem = setmetatable({}, RewardItem)
DigRewardItem.__index = DigRewardItem

function DigRewardItem:Init(data)
  RewardItem.Init(self, data)
  self.m_amount.text = "X" .. (data[PROPERTY_COUNT] or 0)
end

local imgBoxScale = {
  [ImageFileConfigName.dig_chest_1] = 0.35,
  [ImageFileConfigName.dig_chest_2] = 0.37,
  [ImageFileConfigName.dig_chest_3] = 0.42
}
DigRewardBox = {}
DigRewardBox.__index = DigRewardBox

function DigRewardBox:Init(config, bClaimed, window)
  self.m_config = config
  self.m_window = window
  self.m_rewards = config.rewards
  local imgKey = self.m_config.icon_ui or ImageFileConfigName.dig_chest_1
  SpriteUtil.SetImage(self.m_icon, imgKey, true)
  local scale = imgBoxScale[imgKey]
  if scale ~= nil then
    UIUtil.SetLocalScale(self.m_icon.transform, scale, scale)
  end
  self.m_originIconScale = self.m_icon.transform.localScale
  self:SetCheckEnable(bClaimed)
  self.m_button.enabled = not bClaimed
  self.m_icon.raycastTarget = not bClaimed
  self.m_bClaimed = bClaimed
end

function DigRewardBox:SetCheckEnable(bClaimed)
  UIUtil.SetActive(self.m_checkGo, bClaimed)
end

function DigRewardBox:OnRewardBoxClicked()
  if self.m_bClaimed then
    return
  end
  self.m_icon.transform.localScale = self.m_originIconScale - Vector3(0.05, 0.05, 0)
  self.m_icon.transform:DOScale(self.m_originIconScale, 0.4):SetEase(Ease.OutBack)
  self.m_window:ShowBubble(self.m_rewards, self.m_icon.transform, 0, 60)
end
