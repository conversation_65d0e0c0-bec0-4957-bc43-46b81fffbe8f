ExtraBoardActivityReadyWindow = setmetatable({}, ExtraBoardActivityBaseWindow)
ExtraBoardActivityReadyWindow.__index = ExtraBoardActivityReadyWindow

function ExtraBoardActivityReadyWindow:Init(activityType, bUser<PERSON>lick)
  ExtraBoardActivityBaseWindow.Init(self, activityType, bUserClick)
end

function ExtraBoardActivityReadyWindow:OnCloseBtnClick()
  ExtraBoardActivityBaseWindow.OnCloseBtnClick(self)
  if GM.TutorialModel:IsTutorialFinished(ETutorialId.ExtraBoardStart) then
    GM.UIManager:OpenView(self.m_model.m_activityDefinition.MainWindowPrefabName, self.m_model.m_type)
  end
end

function ExtraBoardActivityReadyWindow:OnButtonClick()
  self:Close()
  GM.UIManager:OpenView(self.m_model.m_activityDefinition.MainWindowPrefabName, self.m_model.m_type)
end
