PiggyBankNoticeBubble = {}
PiggyBankNoticeBubble.__index = PiggyBankNoticeBubble
local INIT_CONTENT_POSITION_X = -250

function PiggyBankNoticeBubble:Init()
  self.m_showAnimationNum = 0
  self.m_gemTargetPosition = self.m_gemTargetRectTrans.position
  self.m_model = GM.ActivityManager:GetModel(ActivityType.PiggyBank)
  self:_Init()
  self:_UpdatePosition(false, false)
  self:UpdateContent()
  self.m_activeId = self.m_model:GetId()
  EventDispatcher.AddListener(EEventType.PiggyBankStateChanged, self, self.OnPiggyBankStateChanged)
  EventDispatcher.AddListener(EEventType.PiggyBankAccumulateGem, self, self.OnPiggyBankAccumulateGem)
end

function PiggyBankNoticeBubble:Start()
  if self.m_displayNum == 0 then
    self.m_piggybankAnimation.AnimationState:SetAnimation(0, "piggy_bank_collect_0", false)
  else
    self.m_piggybankAnimation.AnimationState:SetAnimation(0, "piggy_bank_collect_" .. (self.m_recordPiggybankState == PiggyBankModel.PiggyBankState.Full and "2" or "1"), false)
    self.m_piggybankAnimation:Update(0)
    self.m_piggybankAnimation.AnimationState:ClearTracks()
  end
end

function PiggyBankNoticeBubble:_Init()
  self.m_displayNum = self.m_model:GetAccumulatedNum()
  self.m_recordPiggybankState = self.m_model:GetPiggyBankState()
end

function PiggyBankNoticeBubble:OnPiggyBankStateChanged()
  if self.m_activeId ~= self.m_model:GetId() then
    self.m_activeId = self.m_model:GetId()
    self:_Init()
    self:Start()
    self:UpdateContent()
  end
end

function PiggyBankNoticeBubble:OnPiggyBankAccumulateGem()
  if self.m_recordPiggybankState ~= self.m_model:GetPiggyBankState() then
    self.m_bLocking = true
    GM.UIManager:SetEventLock(true, self)
  end
end

function PiggyBankNoticeBubble:UpdateContent()
  local full = self:GetDisplayGemNum() >= self.m_model:GetFullLine()
  UIUtil.SetActive(self.m_fullTextGo, full)
  UIUtil.SetActive(self.m_numContentGo, not full)
  self.m_numText.text = self:GetDisplayGemNum()
end

function PiggyBankNoticeBubble:GetDisplayGemNum()
  return math.floor(self.m_displayNum + 0.5)
end

function PiggyBankNoticeBubble:Show2AccumulateGem()
  self.m_showAnimationNum = self.m_showAnimationNum + 1
  self:_UpdatePosition(true, true)
  if self.m_hideDelayCallTween ~= nil then
    self.m_hideDelayCallTween:Kill()
    self.m_hideDelayCallTween = nil
  end
end

function PiggyBankNoticeBubble:AddAccumulate(num, final)
  self.m_displayNum = self.m_displayNum + num
  self:UpdateContent()
  local state = self.m_model:GetPiggyBankState(self:GetDisplayGemNum())
  self.m_piggybankAnimation.AnimationState:SetAnimation(0, "piggy_bank_collect_" .. (state == PiggyBankModel.PiggyBankState.Full and "2" or "1"), false)
  if final then
    self.m_showAnimationNum = self.m_showAnimationNum - 1
    if self.m_showAnimationNum == 0 then
      if self.m_bNeedDestroy then
        self:_Destroy()
      else
        if self.m_recordPiggybankState ~= state then
          self.m_recordPiggybankState = state
          GM.UIManager:OpenView(UIPrefabConfigName.PiggyBankMainWindow)
        end
        if self.m_bLocking then
          self.m_bLocking = false
          GM.UIManager:SetEventLock(false, self)
        end
        self.m_hideDelayCallTween = DOVirtual.DelayedCall(1.5, function()
          self:_UpdatePosition(false, true)
          self.m_hideDelayCallTween = nil
        end)
      end
    end
  end
end

function PiggyBankNoticeBubble:GetGemTargetPosition()
  return self.m_gemTargetPosition
end

function PiggyBankNoticeBubble:_UpdatePosition(show, animation)
  if self.m_bShow ~= show then
    self.m_bShow = show
    if self.m_showTween ~= nil then
      self.m_showTween:Kill(true)
      self.m_showTween = nil
    end
    if animation then
      if show then
        self.m_showTween = self.m_contentRectTrans:DOLocalMoveX(0, 0.2):SetEase(Ease.OutBack):OnComplete(function()
          self.m_showTween = nil
        end)
      else
        self.m_showTween = self.m_contentRectTrans:DOLocalMoveX(INIT_CONTENT_POSITION_X, 0.2):SetEase(Ease.InBack):OnComplete(function()
          self.m_showTween = nil
        end)
      end
    else
      UIUtil.SetLocalPosition(self.m_contentRectTrans, show and 0 or INIT_CONTENT_POSITION_X)
    end
  end
end

function PiggyBankNoticeBubble:Destroy()
  if self.m_showAnimationNum > 0 then
    self.m_bNeedDestroy = true
    return
  end
  GM.UIManager:RemoveAllEventLocks(self)
  self:_Destroy()
end

function PiggyBankNoticeBubble:_Destroy()
  self.m_bNeedDestroy = false
  AddressableLoader.Destroy(self.gameObject)
end

function PiggyBankNoticeBubble:OnDestroy()
  if self.m_showTween ~= nil then
    self.m_showTween:Kill()
    self.m_showTween = nil
  end
  if self.m_hideDelayCallTween ~= nil then
    self.m_hideDelayCallTween:Kill()
    self.m_hideDelayCallTween = nil
  end
end
