TestPlayerprefsWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestPlayerprefsWindow.__index = TestPlayerprefsWindow

function TestPlayerprefsWindow:Init()
  self.m_arrCells = {}
  local cellObj
  for i, config in ipairs(GM.TestModel:GetSwitchTestPlayerPrefs()) do
    cellObj = GameObject.Instantiate(self.m_cellOrigin, self.m_contentRectTrans)
    cellObj:SetActive(true)
    self.m_arrCells[i] = cellObj:GetLuaTable()
    self.m_arrCells[i]:Init(config[1], config[2])
  end
end

function TestPlayerprefsWindow:SelectAll()
  for _, cell in ipairs(self.m_arrCells) do
    cell:UpdateToggle(true)
  end
end

function TestPlayerprefsWindow:UnselectAll()
  for _, cell in ipairs(self.m_arrCells) do
    cell:UpdateToggle(false)
  end
end

function TestPlayerprefsWindow:Apply()
  for _, cell in ipairs(self.m_arrCells) do
    cell:Apply()
  end
  EventDispatcher.DispatchEvent(EEventType.ShowItemTestInfoChanged)
  GM.TestModel:RefreshTestItemContent()
  self:Close()
end

TestPlayerprefsCell = {}
TestPlayerprefsCell.__index = TestPlayerprefsCell

function TestPlayerprefsCell:Init(playerprefKey, text)
  self.m_playerprefKey = playerprefKey
  self.m_dataText.text = text
  self.m_toggle.isOn = PlayerPrefs.GetInt(self.m_playerprefKey, 0) == 1
end

function TestPlayerprefsCell:UpdateToggle(toggle)
  self.m_toggle.isOn = toggle
end

function TestPlayerprefsCell:Apply()
  if self.m_playerprefKey ~= nil then
    PlayerPrefs.SetInt(self.m_playerprefKey, self.m_toggle.isOn and 1 or 0)
    EventDispatcher.DispatchEvent(EEventType.ShowItemTestInfoChanged)
  end
end
