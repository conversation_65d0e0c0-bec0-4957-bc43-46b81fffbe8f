PDItemSPModel = {}
PDItemSPModel.__index = PDItemSPModel

function PDItemSPModel:LoadFileConfig()
  self.m_mapConfigs = {}
  self.m_mapMaxIndex = {}
  local configs = require("Data.Config.PDItemSPConfig")
  local pd, index
  for _, config in ipairs(configs) do
    pd = config.PD
    index = config.Index
    if self.m_mapConfigs[pd] == nil then
      self.m_mapConfigs[pd] = {}
      self.m_mapMaxIndex[pd] = index
    elseif index > self.m_mapMaxIndex[pd] then
      self.m_mapMaxIndex[pd] = index
    end
    self.m_mapConfigs[pd][index] = config
  end
end

function PDItemSPModel:OnSyncDataFinished()
  self.m_mapCreatedNumber = {}
  local cache = GM.MiscModel:Get(EMiscKey.PDItemSPIndex)
  local strArray = StringUtil.Split(cache, ";")
  for _, str in ipairs(strArray) do
    local innerArray = StringUtil.Split(str, "-")
    local pd = innerArray[1]
    local number = tonumber(innerArray[2])
    if pd and number then
      self.m_mapCreatedNumber[pd] = number
    end
  end
end

function PDItemSPModel:_Save()
  local cacheStr = ""
  for pd, number in pairs(self.m_mapCreatedNumber) do
    cacheStr = cacheStr .. pd .. "-" .. number .. ";"
  end
  if 0 < #cacheStr then
    cacheStr = string.sub(cacheStr, 1, #cacheStr - 1)
  end
  GM.MiscModel:Set(EMiscKey.PDItemSPIndex, cacheStr)
end

function PDItemSPModel:OnPDCreated(pdCode)
  if not self.m_mapCreatedNumber or not self.m_mapConfigs then
    Log.Error("PDItemSPModel 执行时序出问题，初始化完成前创建棋子！")
    return
  end
  if not self.m_mapConfigs[pdCode] then
    Log.Info("母棋子特殊处理：无该母棋子配置，无需记录。" .. pdCode)
    return
  end
  local curNumer = self.m_mapCreatedNumber[pdCode] or 0
  local maxNumber = self.m_mapMaxIndex[pdCode] or 0
  if curNumer >= maxNumber then
    Log.Info("母棋子特殊处理：个数已超配置最大index，无需记录。" .. pdCode)
    return
  end
  local newNumber = curNumer + 1
  self.m_mapCreatedNumber[pdCode] = newNumber
  self:_Save()
  local config = self.m_mapConfigs[pdCode][newNumber]
  if config then
    Log.Info("母棋子特殊处理：第" .. newNumber .. "个，替换配置。" .. pdCode)
    return config.StartTapeItems, config.InitialNumber
  else
    Log.Info("母棋子特殊处理：第" .. newNumber .. "个，无配置。" .. pdCode)
  end
end
