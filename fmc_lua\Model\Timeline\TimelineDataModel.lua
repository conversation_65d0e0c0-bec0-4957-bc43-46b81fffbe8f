TimelineDataModel = {}
TimelineDataModel.__index = TimelineDataModel

function TimelineDataModel:Init()
  self.m_mapChapterTimelineData = {}
  self.m_mapSpecialTimelineData = {}
end

function TimelineDataModel:_LoadConfigs(chapterName, callback)
  if self.m_mapChapterTimelineData[chapterName] then
    callback(self.m_mapChapterTimelineData[chapterName])
    return
  end
  GM.UIManager:SetEventLock(true)
  local path = "DynamicConfig.Mainline." .. tostring(chapterName) .. ".TimelineConfig_" .. tostring(chapterName)
  GM.ConfigModel:LoadDynamicConfigAsync(path, function(tb)
    GM.UIManager:SetEventLock(false)
    if not tb then
      GM.BIManager:LogErrorInfo("ct_ld_er", chapterName)
      CS.CSErrorMonitor.OpenForceRestartWindow("force_restart_desc")
      self.m_mapChapterTimelineData[chapterName] = {}
      callback(self.m_mapChapterTimelineData[chapterName])
      return
    end
    local mapTimelineData = self:_ParseConfigs(tb, chapterName)
    self.m_mapChapterTimelineData[chapterName] = mapTimelineData
    callback(mapTimelineData)
  end)
end

function TimelineDataModel:_LoadSpecialConfigs(timelinePrefix, callback)
  if self.m_mapSpecialTimelineData[timelinePrefix] then
    callback(self.m_mapSpecialTimelineData[timelinePrefix])
    return
  end
  GM.UIManager:SetEventLock(true)
  local path = "DynamicConfig.Special.Timeline." .. tostring(timelinePrefix) .. ".TimelineConfig_" .. tostring(timelinePrefix)
  GM.ConfigModel:LoadDynamicConfigAsync(path, function(tb)
    GM.UIManager:SetEventLock(false)
    if not tb then
      GM.BIManager:LogErrorInfo("st_ld_er", timelinePrefix)
      CS.CSErrorMonitor.OpenForceRestartWindow("force_restart_desc")
      self.m_mapSpecialTimelineData[timelinePrefix] = {}
      callback(self.m_mapSpecialTimelineData[timelinePrefix])
      return
    end
    local mapTimelineData = self:_ParseConfigs(tb)
    self.m_mapChapterTimelineData[timelinePrefix] = mapTimelineData
    callback(mapTimelineData)
  end)
end

function TimelineDataModel:_ParseConfigs(configs, chapterName)
  local mapTimelineData = {}
  local timelineData, id
  for i = 1, #configs do
    id = configs[i].Id
    local timelineData = mapTimelineData[id]
    if not timelineData then
      timelineData = TimelineData.Create(id, chapterName)
      timelineData:AddStep(configs[i])
      mapTimelineData[id] = timelineData
    else
      timelineData:AddStep(configs[i])
    end
  end
  return mapTimelineData
end

function TimelineDataModel:_GetMainlineTimelineData(timelineId, chapterName, bError, callback)
  self:_LoadConfigs(chapterName, function(mapTimelineData)
    local timelineData = mapTimelineData[timelineId]
    if not timelineData and bError ~= false then
      Log.Error("没有找到对应的时间线配置 timelineId:" .. tostring(timelineId))
    end
    if callback then
      callback(timelineData)
    end
  end)
end

function TimelineDataModel:_GetSpecialTimelineData(timelineId, bError, callback)
  local prefix = StringUtil.Split(timelineId, "_")[1]
  self:_LoadSpecialConfigs(prefix, function(mapTimelineData)
    local timelineData = mapTimelineData[timelineId]
    if not timelineData and bError ~= false then
      Log.Error("没有找到对应的时间线配置 timelineId:" .. tostring(timelineId))
    end
    if callback then
      callback(timelineData)
    end
  end)
end

function TimelineDataModel:GetTimelineData(timelineId, chapterName, bError, callback)
  if chapterName then
    self:_GetMainlineTimelineData(timelineId, chapterName, bError, callback)
  else
    self:_GetSpecialTimelineData(timelineId, bError, callback)
  end
end
