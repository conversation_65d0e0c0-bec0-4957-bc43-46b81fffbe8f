RoomSeats = {}
RoomSeats.__index = RoomSeats

function RoomSeats:GetSeats()
  local seatTrans
  local arrSeats = {}
  if not self.m_seatsRoot or self.m_seatsRoot:IsNull() then
    return arrSeats
  end
  local seatsNum = self.m_seatsRoot.childCount
  for i = 1, seatsNum do
    seatTrans = self.m_seatsRoot:GetChild(i - 1)
    arrSeats[i] = {
      root = seatTrans,
      flip = tonumber(seatTrans.gameObject.name) > 10
    }
  end
  return arrSeats
end

function RoomSeats:GetRoot()
  return self.m_seatsRoot
end
