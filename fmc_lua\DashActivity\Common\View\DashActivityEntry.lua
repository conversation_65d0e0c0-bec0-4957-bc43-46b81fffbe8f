DashActivityEntry = setmetatable({}, HudGeneralButton)
DashActivityEntry.__index = DashActivityEntry

function DashActivityEntry:Init(activityType)
  self.m_activityType = activityType
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_activityDefinition = DashActivityDefinition[activityType]
  SpriteUtil.SetImage(self.m_iconImage, self.m_activityDefinition.EntryImageName, true)
  self:UpdatePerSecond()
end

function DashActivityEntry:UpdatePerSecond()
  local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
  self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
end

function DashActivityEntry:OnClicked()
  local state = self.m_model:GetState()
  if state == ActivityState.Preparing then
    GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, false)
  elseif state == ActivityState.Started then
    if self.m_activityType == ActivityType.Coconut then
      GM.UIManager:OpenView(UIPrefabConfigName.CoconutMainWindow, false)
    else
      GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType)
    end
  end
end
