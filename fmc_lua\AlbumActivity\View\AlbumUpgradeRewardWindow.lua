AlbumUpgradeRewardWindow = setmetatable({
  canClickWindowMask = true,
  canCloseByAndroidBack = false,
  windowMaskAlpha = 0.85
}, AlbumActivityBaseWindow)
AlbumUpgradeRewardWindow.__index = AlbumUpgradeRewardWindow

function AlbumUpgradeRewardWindow:Init(activityType, parentWnd)
  AlbumActivityBaseWindow.Init(self, activityType)
  self.m_parentWnd = parentWnd
  self.m_rewards = {}
  self.m_rewards[1] = self.m_rewardItemSimpleLuaTable
  self.m_arrRewards = self.m_model:GetAlbumConfig().upgradeBonus
  for i, v in ipairs(self.m_arrRewards) do
    if self.m_rewards[i] == nil then
      self.m_rewards[i] = Object.Instantiate(self.m_rewardItemSimpleLuaTable.gameObject, self.m_rewardItemSimpleLuaTable.transform.parent):GetLuaTable()
    end
    self.m_rewards[i]:Init(v)
    self.m_rewards[i]:SetAmountText("X" .. v[PROPERTY_COUNT])
  end
  if #self.m_arrRewards == 1 then
    self.m_rewards[1].gameObject.transform.localScale = Vector3(3, 3, 3)
    self.m_amountRectTrans.localScale = Vector3(0.6, 0.6, 0.6)
  end
end

function AlbumUpgradeRewardWindow:Close()
  AlbumActivityBaseWindow.Close(self)
  if self.m_parentWnd then
    self.m_parentWnd:Close()
  end
end

function AlbumUpgradeRewardWindow:OnCloseFinish()
  BaseWindow.OnCloseFinish(self)
  if self.m_model:IsActivityOpen() then
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, nil, "album_upgrade_congrat_title")
  end
end
