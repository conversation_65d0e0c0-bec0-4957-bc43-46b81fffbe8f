SpreeActivityShopModel = {}
SpreeActivityShopModel.__index = SpreeActivityShopModel
SpreeActivityShopModel.CurrentBatchIdKey = "CurrentBatchId"
SpreeActivityShopModel.CurrentBatchTimestampKey = "CurrentBatchTimestamp"
SpreeActivityShopModel.BundleBaughtKey = "Bundle_%d_Baught"
SpreeActivityShopModel.HotSalesTimestampKey = "HotSalesTimestamp"
SpreeActivityShopModel.HotSalesLeftKey = "HotSales_%d_Left"
SpreeActivityShopModel.WindowOpenedKey = "WindowOpened"
local CachePrefix = "Cache_"

function SpreeActivityShopModel.Create(activityType, shopDataTable, boardModel)
  local shopModel = setmetatable({}, SpreeActivityShopModel)
  shopModel:Init(activityType, shopDataTable, boardModel)
  return shopModel
end

function SpreeActivityShopModel:Init(activityType, shopDataTable, boardModel)
  self.m_activityDefinition = SpreeActivityDefinition[activityType]
  self.m_dbTable = shopDataTable
  self.m_boardModel = boardModel
end

function SpreeActivityShopModel:LoadFileConfig()
  self.m_bundleBatchConfig = {}
  local fileName = self.m_activityDefinition.BundleBatchConfigFileName or "SpreeBundleBatchConfig_" .. self.m_activityDefinition.ConfigSuffix
  local bundleBatchConfigs = require("Data.Config." .. fileName)
  for _, config in ipairs(bundleBatchConfigs) do
    self.m_bundleBatchConfig[config.Id] = config
  end
  self.m_bundleConfig = {}
  fileName = self.m_activityDefinition.BundleConfigFileName or "SpreeBundleConfig_" .. self.m_activityDefinition.ConfigSuffix
  local bundleConfigs = require("Data.Config." .. fileName)
  for _, config in ipairs(bundleConfigs) do
    local batchData = self.m_bundleConfig[config.Batch]
    if batchData == nil then
      batchData = {}
      self.m_bundleConfig[config.Batch] = batchData
    end
    batchData[config.Index] = {
      Batch = config.Batch,
      Index = config.Index,
      Name = config.Name,
      Rewards = ConfigUtil.GetCurrencyFromArrStr(config.Rewards),
      PurchaseId = config.PurchaseId
    }
  end
  self.m_hotSalesConfig = {}
  fileName = self.m_activityDefinition.HotSalesConfigFileName or "SpreeHotSalesConfig_" .. self.m_activityDefinition.ConfigSuffix
  local hotSalesConfigs = require("Data.Config." .. fileName)
  for _, config in ipairs(hotSalesConfigs) do
    self.m_hotSalesConfig[config.Slot] = config
  end
end

function SpreeActivityShopModel:OnSyncDataFinished()
  self:_RefreshHotSalesData()
end

function SpreeActivityShopModel:UpdatePerSecond()
  self:_RefreshHotSalesData()
end

function SpreeActivityShopModel:RefreshBundleData()
  local activeBundleData = self:GetActiveBundleData()
  if #activeBundleData ~= 0 then
    return
  end
  local currentBatchId = self.m_dbTable:GetValue(SpreeActivityShopModel.CurrentBatchIdKey, "value")
  if currentBatchId == nil then
    currentBatchId = 0
  end
  local nextBatchId = currentBatchId + 1
  local nextBatchConfig = self.m_bundleBatchConfig[nextBatchId]
  while nextBatchConfig ~= nil do
    if nextBatchConfig.UnlockOrders ~= nil and self.m_boardModel ~= nil then
      for _, orderId in ipairs(nextBatchConfig.UnlockOrders) do
        if not self.m_boardModel:IsOrderFinished(orderId) then
          return
        end
      end
    end
    self:_SetCurrentBatch(nextBatchId)
    nextBatchId = nextBatchId + 1
    nextBatchConfig = self.m_bundleBatchConfig[nextBatchId]
  end
end

function SpreeActivityShopModel:_SetCurrentBatch(batchId)
  self.m_dbTable:Set(SpreeActivityShopModel.CurrentBatchIdKey, "value", batchId)
  self.m_dbTable:Set(SpreeActivityShopModel.CurrentBatchTimestampKey, "value", GM.GameModel:GetServerTime())
  for _, config in ipairs(self.m_bundleConfig[batchId]) do
    local bundleBaughtKey = string.format(SpreeActivityShopModel.BundleBaughtKey, config.Index)
    self.m_dbTable:Remove(bundleBaughtKey)
  end
end

function SpreeActivityShopModel:_RefreshHotSalesData()
  if self:GetHotSalesRefreshTime() - GM.GameModel:GetServerTime() > 0 then
    return
  end
  self.m_dbTable:Set(SpreeActivityShopModel.HotSalesTimestampKey, "value", GM.GameModel:GetServerTime())
  for slot, config in ipairs(self.m_hotSalesConfig) do
    local hotSalesLeftKey = string.format(SpreeActivityShopModel.HotSalesLeftKey, slot)
    self.m_dbTable:Set(hotSalesLeftKey, "value", config.Count)
  end
  EventDispatcher.DispatchEvent(EEventType.ShopRefreshed, {
    shopType = self.m_activityDefinition.HotSalesShopType
  })
end

function SpreeActivityShopModel:GetActiveBundleData()
  local currentBatchId = self.m_dbTable:GetValue(SpreeActivityShopModel.CurrentBatchIdKey, "value")
  if currentBatchId == nil then
    return {}
  end
  local currentBatchTimestamp = self.m_dbTable:GetValue(SpreeActivityShopModel.CurrentBatchTimestampKey, "value")
  local batchConfig = self.m_bundleBatchConfig[currentBatchId]
  if GM.GameModel:GetServerTime() - currentBatchTimestamp > batchConfig.Duration then
    return {}
  end
  local results = {}
  for _, config in ipairs(self.m_bundleConfig[currentBatchId]) do
    local bundleBaughtKey = string.format(SpreeActivityShopModel.BundleBaughtKey, config.Index)
    if self.m_dbTable:GetValue(bundleBaughtKey, "value") ~= 1 then
      table.insert(results, config)
    end
  end
  return results
end

function SpreeActivityShopModel:GetActiveBundleRemainTime()
  if self.m_dbTable:IsEmpty() then
    return 0
  end
  local currentBatchId = self.m_dbTable:GetValue(SpreeActivityShopModel.CurrentBatchIdKey, "value")
  local currentBatchTimestamp = self.m_dbTable:GetValue(SpreeActivityShopModel.CurrentBatchTimestampKey, "value")
  local batchConfig = self.m_bundleBatchConfig[currentBatchId]
  if batchConfig == nil then
    return 0
  end
  return math.max(0, currentBatchTimestamp + batchConfig.Duration - GM.GameModel:GetServerTime())
end

function SpreeActivityShopModel:GetHotSalesRefreshTime()
  local timestamp = self.m_dbTable:GetValue(SpreeActivityShopModel.HotSalesTimestampKey, "value")
  if timestamp == nil then
    return 0
  end
  return timestamp + SpreeActivityModel.HotSalesRefreshTime
end

function SpreeActivityShopModel:GetHotSaleData()
  local results = {}
  for slot, config in ipairs(self.m_hotSalesConfig) do
    local hotSalesLeftKey = string.format(SpreeActivityShopModel.HotSalesLeftKey, slot)
    local count = self.m_dbTable:GetValue(hotSalesLeftKey, "value")
    local price = config.Price + (config.Count - count) * config.PriceIncrement
    table.insert(results, {
      Slot = slot,
      Code = config.Item,
      Count = count,
      Price = price
    })
  end
  return results
end

function SpreeActivityShopModel:BuyBundle(data, onSuccess)
  local targetGameMode = self.m_activityDefinition.GameMode
  local purchaseCallback = function()
    local boardModel = BoardModelHelper.GetModelByGameMode(targetGameMode)
    if boardModel == nil then
      GM.BIManager:LogAction(EBIType.SpreeIAPSuccessAfterActivityEnded, data.PurchaseId)
      return
    end
    RewardApi.AcquireRewardsLogic(data.Rewards, EPropertySource.Buy, EBIType.IAP, self.m_activityDefinition.GameMode, CacheItemType.Stack)
    local bundleBaughtKey = string.format(SpreeActivityShopModel.BundleBaughtKey, data.Index)
    self.m_dbTable:Set(bundleBaughtKey, "value", 1)
    self.m_dbTable:Remove(CachePrefix .. data.PurchaseId)
    EventDispatcher.DispatchEvent(EEventType.ShopRefreshed, {
      shopType = self.m_activityDefinition.BundleShopType
    })
    onSuccess()
  end
  GM.InAppPurchaseModel:StartPurchase(data.PurchaseId, purchaseCallback, EBIType.ShopBuy)
  self.m_dbTable:Set(CachePrefix .. data.PurchaseId, "value", data.Name)
end

function SpreeActivityShopModel:BuyItem(slot, code, price, onSuccess, onOutOfGem)
  local gemNumber = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
  if price > gemNumber then
    onOutOfGem(price - gemNumber)
    return
  end
  GM.PropertyDataManager:Consume(EPropertyType.Gem, price, EBIType.ShopBuy, false, EBIConsumerType.SpreeShop)
  local formattedRewards = {
    {
      [PROPERTY_TYPE] = code,
      [PROPERTY_COUNT] = 1,
      cost = {shopGemCost = price}
    }
  }
  RewardApi.AcquireRewardsLogic(formattedRewards, EPropertySource.Buy, EBIType.ShopBuy, self.m_activityDefinition.GameMode, CacheItemType.Stack)
  GM.BIManager:LogStore(EPropertyType.Gem, price, code, 1, self.m_activityDefinition.HotSalesShopType)
  local hotSalesLeftKey = string.format(SpreeActivityShopModel.HotSalesLeftKey, slot)
  local count = self.m_dbTable:GetValue(hotSalesLeftKey, "value")
  self.m_dbTable:Set(hotSalesLeftKey, "value", count - 1)
  onSuccess()
end

function SpreeActivityShopModel:RestoreIapRewards(iapType)
  local data = self:_GetBundleDataWithPurchaseId(iapType)
  if data == nil then
    return false
  end
  RewardApi.AcquireRewards(data.Rewards, EPropertySource.Buy, EBIType.Restore, nil, self.m_activityDefinition.GameMode, CacheItemType.Stack)
  local bundleData = self:GetActiveBundleData()
  for _, data in ipairs(bundleData) do
    if data.PurchaseId == iapType then
      local bundleBaughtKey = string.format(SpreeActivityShopModel.BundleBaughtKey, data.Index)
      self.m_dbTable:Set(bundleBaughtKey, "value", 1)
      EventDispatcher.DispatchEvent(EEventType.ShopRefreshed, {
        shopType = self.m_activityDefinition.BundleShopType
      })
      break
    end
  end
  return true
end

function SpreeActivityShopModel:_GetBundleDataWithPurchaseId(purchaseId)
  for _, batchData in ipairs(self.m_bundleConfig) do
    for _, bundleData in ipairs(batchData) do
      if bundleData.PurchaseId == purchaseId then
        local cacheName = self.m_dbTable:GetValue(CachePrefix .. purchaseId, "value")
        if cacheName then
          if cacheName == bundleData.Name then
            self.m_dbTable:Remove(purchaseId)
            Log.Info("GetBundleDataWithPurchaseId with cache purchaseId")
            return bundleData
          end
        else
          return bundleData
        end
      end
    end
  end
  return nil
end

function SpreeActivityShopModel:IsWindowOpened()
  return self.m_dbTable:GetValue(SpreeActivityShopModel.WindowOpenedKey, "value") == 1
end

function SpreeActivityShopModel:SetWindowOpened()
  self.m_dbTable:Set(SpreeActivityShopModel.WindowOpenedKey, "value", 1)
end
