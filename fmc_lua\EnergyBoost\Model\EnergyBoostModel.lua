EnergyBoostModel = {}
EnergyBoostModel.__index = EnergyBoostModel
local nStart = -1
local autoIncrease = function()
  nStart = nStart + 1
  return nStart
end
EEnergyBoostType = {
  None = autoIncrease(),
  Double = autoIncrease(),
  Quad = autoIncrease()
}
local TRIGGER_DURATION_IN_MINS = 30
local TRIGGER_DURATION_IN_SECONDS = TRIGGER_DURATION_IN_MINS * 60

function EnergyBoostModel:Init()
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self.OnLoginFinished)
  EventDispatcher.AddListener(EEventType.OnAddEnergy, self, self.OnAddEnergy)
  EventDispatcher.AddListener(EEventType.BuyEnergySuccess, self, self.OnBuyEnergySuccess)
  self.m_mapTriggerCondition = {}
end

function EnergyBoostModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function EnergyBoostModel:OnLoginFinished()
  local bChanged = false
  local mapOldCondition = Table.ShallowCopy(self.m_mapTriggerCondition)
  local bTriggerOn, triggerCount = GM.ConfigModel:IsEnergyBoostDoubleTriggerConfigOn()
  self.m_mapTriggerCondition[EEnergyBoostType.Double] = bTriggerOn and triggerCount or nil
  bTriggerOn, triggerCount = GM.ConfigModel:IsEnergyBoostQuadTriggerConfigOn()
  self.m_mapTriggerCondition[EEnergyBoostType.Quad] = bTriggerOn and triggerCount or nil
  if mapOldCondition[EEnergyBoostType.Double] ~= self.m_mapTriggerCondition[EEnergyBoostType.Double] or mapOldCondition[EEnergyBoostType.Quad] ~= self.m_mapTriggerCondition[EEnergyBoostType.Quad] then
    EventDispatcher.DispatchEvent(EEventType.EnergyBoostModeChanged)
  end
  self:UpdatePerSecond()
end

function EnergyBoostModel:LateInit()
  self.m_mapBoostBonusConfig = {}
  self.m_maxBoostBonusPrice = -1
  local arrConfig = GM.ConfigModel:GetGeneralConfByType(EGeneralConfType.EnergyBoostTriggerBonus, EConfigParamType.IntArray) or {}
  if #arrConfig % 2 ~= 0 then
    Log.Error("买体力送双倍配置格式错误")
    return
  end
  local price = -1
  for i = 2, #arrConfig, 2 do
    price = arrConfig[i - 1]
    self.m_mapBoostBonusConfig[price] = arrConfig[i]
    if price > self.m_maxBoostBonusPrice then
      self.m_maxBoostBonusPrice = price
    else
      Log.Error("买体力送双倍配置顺序错误")
    end
  end
  self.m_bLateInited = true
end

function EnergyBoostModel:GetTimeLimitedConfigDurationInMinutes()
  return TRIGGER_DURATION_IN_MINS
end

function EnergyBoostModel:GetTriggerCount(eBoostType)
  return self.m_mapTriggerCondition[eBoostType]
end

function EnergyBoostModel:OnAddEnergy(msg)
  if not next(self.m_mapTriggerCondition) or msg.type ~= EnergyType.Main then
    return
  end
  local leftTime = self:GetTimeLimitedLeftTime()
  local arrTypes = {
    EEnergyBoostType.Quad,
    EEnergyBoostType.Double
  }
  for _, eBoostType in ipairs(arrTypes) do
    local triggerCount = self.m_mapTriggerCondition[eBoostType]
    if triggerCount and 0 < triggerCount and triggerCount <= msg.cur and leftTime < TRIGGER_DURATION_IN_SECONDS and (leftTime <= 0 or eBoostType >= self:GetTriggerBoostType()) then
      self:_TriggerTimeLimitedEnergyBoost(TRIGGER_DURATION_IN_SECONDS - leftTime, eBoostType)
      break
    end
  end
end

function EnergyBoostModel:OnBuyEnergySuccess(buyEnergyCost)
  local freeBoostInMin = self:GetBoostBonusDurationInMin(buyEnergyCost)
  if 0 < freeBoostInMin then
    self:_TriggerTimeLimitedEnergyBoost(freeBoostInMin * 60, EEnergyBoostType.Double)
  end
end

function EnergyBoostModel:GetBoostBonusDurationInMin(buyEnergyCost)
  if not self.m_mapTriggerCondition[EEnergyBoostType.Double] then
    return 0
  end
  if self.m_mapTriggerCondition[EEnergyBoostType.Quad] then
    return 0
  end
  if not self.m_mapBoostBonusConfig then
    return 0
  end
  local durationInMin = self.m_mapBoostBonusConfig[buyEnergyCost]
  if durationInMin then
    return durationInMin
  end
  if self.m_maxBoostBonusPrice and buyEnergyCost and buyEnergyCost > self.m_maxBoostBonusPrice then
    return self.m_mapBoostBonusConfig[self.m_maxBoostBonusPrice] or 0
  end
  return 0
end

function EnergyBoostModel:UpdatePerSecond()
  if not self.m_bLateInited then
    return
  end
  local isTimeLimitedOn = self:_IsTimeLimitedEnergyBoostOn()
  if isTimeLimitedOn ~= self.m_bTimeLimitedOn then
    self.m_bTimeLimitedOn = isTimeLimitedOn
    EventDispatcher.DispatchEvent(EEventType.EnergyBoostModeChanged)
  end
end

function EnergyBoostModel:_TriggerTimeLimitedEnergyBoost(duration, eBoostType)
  local eLastTriggerType = self:GetTriggerBoostType()
  local currentEndTime = GM.MiscModel:GetEnergyBoostTriggerEndTimeInNumber()
  currentEndTime = math.max(currentEndTime, GM.GameModel:GetServerTime())
  GM.MiscModel:SetEnergyBoostTriggerEndTime(currentEndTime + duration)
  GM.MiscModel:SetEnergyBoostTriggerType(eBoostType)
  if not self.m_bTimeLimitedOn then
    self:UpdatePerSecond()
  elseif eLastTriggerType ~= eBoostType then
    EventDispatcher.DispatchEvent(EEventType.EnergyBoostModeChanged)
  end
  local eScene = eBoostType == EEnergyBoostType.Quad and EBIType.TimeLimitedEnergyBoostQuadTriggered or EBIType.TimeLimitedEnergyBoostTriggered
  GM.BIManager:LogAction(eScene, duration)
end

function EnergyBoostModel:_IsTimeLimitedEnergyBoostOn()
  return GM.GameModel:GetServerTime() < GM.MiscModel:GetEnergyBoostTriggerEndTimeInNumber()
end

function EnergyBoostModel:IsEnergyBoostModeOn()
  return self:IsEnergyBoostConfigOn() and self:GetUserBoostType() >= EEnergyBoostType.Double
end

function EnergyBoostModel:IsEnergyBoostConfigOn()
  return self.m_bTimeLimitedOn
end

function EnergyBoostModel:IsTimeLimitedOn()
  return self.m_bTimeLimitedOn
end

function EnergyBoostModel:GetTimeLimitedLeftTime()
  local leftTime = GM.MiscModel:GetEnergyBoostTriggerEndTimeInNumber() - GM.GameModel:GetServerTime()
  return leftTime < 0 and 0 or leftTime
end

function EnergyBoostModel:GetTriggerBoostType()
  return tonumber(GM.MiscModel:GetEnergyBoostTriggerType()) or EEnergyBoostType.Double
end

function EnergyBoostModel:GetUserBoostType()
  local eTriggerType = self:GetTriggerBoostType()
  local eUserType
  if eTriggerType == EEnergyBoostType.Quad then
    eUserType = GM.MiscModel:GetEnergyBoostQuadUserOnInNumber()
  else
    eUserType = GM.MiscModel:GetEnergyBoostUserOnInNumber()
  end
  return math.min(eTriggerType, eUserType)
end

function EnergyBoostModel:SetUserBoostType(eEnergyBoostType)
  local eTriggerType = self:GetTriggerBoostType()
  if eTriggerType == EEnergyBoostType.Quad then
    GM.MiscModel:SetEnergyBoostQuadUserOn(eEnergyBoostType)
  else
    GM.MiscModel:SetEnergyBoostUserOn(eEnergyBoostType)
  end
end

function EnergyBoostModel:SwitchUserBoostType()
  local eUserBoostType = self:GetUserBoostType()
  local eNextType = eUserBoostType >= self:GetTriggerBoostType() and EEnergyBoostType.None or eUserBoostType + 1
  local hasStrongTutorial, tutorialId = GM.TutorialModel:HasAnyStrongTutorialOngoing()
  if hasStrongTutorial and tutorialId == ETutorialId.EnergyBoostQuad then
    eNextType = EEnergyBoostType.Quad
  end
  self:SetUserBoostType(eNextType)
  GM.BIManager:LogAction(EBIType.EnergyBoostSwitchUserOn, {on = eNextType})
  EventDispatcher.DispatchEvent(EEventType.EnergyBoostModeChanged)
end

function EnergyBoostModel:CanEnergyBoost(type)
  if not self:IsEnergyBoostModeOn() then
    return false
  end
  if not GM.ItemDataModel:IsPd(type) then
    return false
  end
  local modelConfig = GM.ItemDataModel:GetModelConfig(type)
  return modelConfig and modelConfig.UseEnergy == 1
end

function EnergyBoostModel:GetEnergyBoostCostNum(eBoostType)
  eBoostType = eBoostType or self:GetUserBoostType()
  if eBoostType == EEnergyBoostType.Quad then
    return 4
  elseif eBoostType == EEnergyBoostType.Double then
    return 2
  end
  return 1
end
