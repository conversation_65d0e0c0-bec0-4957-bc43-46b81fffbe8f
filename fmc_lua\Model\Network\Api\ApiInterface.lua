local Serialization = require("Model.Network.Serialization")()
Serialization.ProtocolMd5 = {
  1,
  10,
  174,
  108,
  108,
  228,
  133,
  123,
  56,
  196,
  212,
  192,
  212,
  11,
  160,
  187
}
Serialization.ConfigEntry = {}
Serialization.ConfigEntry.__index = Serialization.ConfigEntry

function Serialization.ConfigEntry.Serialize(writer, value)
  local bRet
  assert(value.config_key ~= nil)
  bRet = Serialization.String.Serialize(writer, value.config_key)
  if not bRet then
    return false
  end
  assert(value.config_md5 ~= nil)
  bRet = Serialization.String.Serialize(writer, value.config_md5)
  if not bRet then
    return false
  end
  return true
end

function Serialization.ConfigEntry.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.ConfigEntry)
  bRet, value.config_key = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.config_md5 = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.RewardInfo = {}
Serialization.RewardInfo.__index = Serialization.RewardInfo

function Serialization.RewardInfo.Serialize(writer, value)
  local bRet
  assert(value.key ~= nil)
  bRet = Serialization.String.Serialize(writer, value.key)
  if not bRet then
    return false
  end
  assert(value.content ~= nil)
  bRet = Serialization.String.Serialize(writer, value.content)
  if not bRet then
    return false
  end
  assert(value.status ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.status)
  if not bRet then
    return false
  end
  return true
end

function Serialization.RewardInfo.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.RewardInfo)
  bRet, value.key = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.content = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.status = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.SocialPackage = {}
Serialization.SocialPackage.__index = Serialization.SocialPackage

function Serialization.SocialPackage.Serialize(writer, value)
  local bRet
  assert(value.type ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.type)
  if not bRet then
    return false
  end
  assert(value.id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.id)
  if not bRet then
    return false
  end
  assert(value.name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.name)
  if not bRet then
    return false
  end
  assert(value.icon ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon)
  if not bRet then
    return false
  end
  assert(value.phone ~= nil)
  bRet = Serialization.String.Serialize(writer, value.phone)
  if not bRet then
    return false
  end
  return true
end

function Serialization.SocialPackage.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.SocialPackage)
  bRet, value.type = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.phone = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.LoginReq = {}
Serialization.LoginReq.__index = Serialization.LoginReq

function Serialization.LoginReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.version ~= nil)
  bRet = Serialization.String.Serialize(writer, value.version)
  if not bRet then
    return false
  end
  assert(value.channel ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.channel)
  if not bRet then
    return false
  end
  assert(value.user_level ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.user_level)
  if not bRet then
    return false
  end
  assert(value.user_progress ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.user_progress)
  if not bRet then
    return false
  end
  assert(value.exp ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.exp)
  if not bRet then
    return false
  end
  assert(value.region ~= nil)
  bRet = Serialization.String.Serialize(writer, value.region)
  if not bRet then
    return false
  end
  assert(value.lang ~= nil)
  bRet = Serialization.String.Serialize(writer, value.lang)
  if not bRet then
    return false
  end
  assert(value.network_name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.network_name)
  if not bRet then
    return false
  end
  assert(value.tracker_name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.tracker_name)
  if not bRet then
    return false
  end
  assert(value.ad_group ~= nil)
  bRet = Serialization.String.Serialize(writer, value.ad_group)
  if not bRet then
    return false
  end
  assert(value.ad_group_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.ad_group_id)
  if not bRet then
    return false
  end
  assert(value.adid ~= nil)
  bRet = Serialization.String.Serialize(writer, value.adid)
  if not bRet then
    return false
  end
  assert(value.config_entries ~= nil)
  bRet = Serialization.Array(Serialization.ConfigEntry).Serialize(writer, value.config_entries)
  if not bRet then
    return false
  end
  assert(value.requestId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.requestId)
  if not bRet then
    return false
  end
  assert(value.reward_keys ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.reward_keys)
  if not bRet then
    return false
  end
  assert(value.install_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.install_id)
  if not bRet then
    return false
  end
  assert(value.device_model ~= nil)
  bRet = Serialization.String.Serialize(writer, value.device_model)
  if not bRet then
    return false
  end
  return true
end

function Serialization.LoginReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.LoginReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.version = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.channel = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.user_level = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.user_progress = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.exp = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.region = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.lang = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.network_name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.tracker_name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ad_group = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ad_group_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.adid = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.config_entries = Serialization.Array(Serialization.ConfigEntry).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.requestId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.reward_keys = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.install_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.device_model = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.LoginResp = {}
Serialization.LoginResp.__index = Serialization.LoginResp

function Serialization.LoginResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.catalogue ~= nil)
  bRet = Serialization.String.Serialize(writer, value.catalogue)
  if not bRet then
    return false
  end
  assert(value.catalogue_md5 ~= nil)
  bRet = Serialization.String.Serialize(writer, value.catalogue_md5)
  if not bRet then
    return false
  end
  assert(value.force_download ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.force_download)
  if not bRet then
    return false
  end
  assert(value.config ~= nil)
  bRet = Serialization.String.Serialize(writer, value.config)
  if not bRet then
    return false
  end
  assert(value.last_sync_time ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.last_sync_time)
  if not bRet then
    return false
  end
  assert(value.server_time ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.server_time)
  if not bRet then
    return false
  end
  assert(value.update_hint ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.update_hint)
  if not bRet then
    return false
  end
  assert(value.update_hint_end_enter_time ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.update_hint_end_enter_time)
  if not bRet then
    return false
  end
  assert(value.store_url ~= nil)
  bRet = Serialization.String.Serialize(writer, value.store_url)
  if not bRet then
    return false
  end
  assert(value.rewards ~= nil)
  bRet = Serialization.Array(Serialization.RewardInfo).Serialize(writer, value.rewards)
  if not bRet then
    return false
  end
  assert(value.social_bindings ~= nil)
  bRet = Serialization.Array(Serialization.SocialPackage).Serialize(writer, value.social_bindings)
  if not bRet then
    return false
  end
  assert(value.total_pay ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.total_pay)
  if not bRet then
    return false
  end
  assert(value.month_pay ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.month_pay)
  if not bRet then
    return false
  end
  assert(value.requestid ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.requestid)
  if not bRet then
    return false
  end
  assert(value.group_name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.group_name)
  if not bRet then
    return false
  end
  assert(value.valid_group_name ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.valid_group_name)
  if not bRet then
    return false
  end
  assert(value.force_sync ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.force_sync)
  if not bRet then
    return false
  end
  assert(value.force_sso ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.force_sso)
  if not bRet then
    return false
  end
  assert(value.installation_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.installation_id)
  if not bRet then
    return false
  end
  assert(value.update_device_model ~= nil)
  bRet = Serialization.String.Serialize(writer, value.update_device_model)
  if not bRet then
    return false
  end
  assert(value.session ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.session)
  if not bRet then
    return false
  end
  assert(value.logout_time ~= nil)
  bRet = Serialization.DateTime.Serialize(writer, value.logout_time)
  if not bRet then
    return false
  end
  assert(value.heart_beat_interval ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.heart_beat_interval)
  if not bRet then
    return false
  end
  assert(value.heart_beat_interval_idle ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.heart_beat_interval_idle)
  if not bRet then
    return false
  end
  assert(value.received_bulletin_ids ~= nil)
  bRet = Serialization.Array(Serialization.VarUInt64).Serialize(writer, value.received_bulletin_ids)
  if not bRet then
    return false
  end
  assert(value.return_expired_time ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.return_expired_time)
  if not bRet then
    return false
  end
  assert(value.lost_days ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.lost_days)
  if not bRet then
    return false
  end
  return true
end

function Serialization.LoginResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.LoginResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.catalogue = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.catalogue_md5 = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.force_download = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.config = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.last_sync_time = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.server_time = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.update_hint = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.update_hint_end_enter_time = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.store_url = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.rewards = Serialization.Array(Serialization.RewardInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.social_bindings = Serialization.Array(Serialization.SocialPackage).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.total_pay = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.month_pay = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.requestid = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.group_name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.valid_group_name = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.force_sync = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.force_sso = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.installation_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.update_device_model = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.session = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.logout_time = Serialization.DateTime.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.heart_beat_interval = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.heart_beat_interval_idle = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.received_bulletin_ids = Serialization.Array(Serialization.VarUInt64).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.return_expired_time = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.lost_days = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.UserBasicInfo = {}
Serialization.UserBasicInfo.__index = Serialization.UserBasicInfo

function Serialization.UserBasicInfo.Serialize(writer, value)
  local bRet
  assert(value.gold ~= nil)
  bRet = Serialization.String.Serialize(writer, value.gold)
  if not bRet then
    return false
  end
  assert(value.gem ~= nil)
  bRet = Serialization.String.Serialize(writer, value.gem)
  if not bRet then
    return false
  end
  assert(value.skipprop ~= nil)
  bRet = Serialization.String.Serialize(writer, value.skipprop)
  if not bRet then
    return false
  end
  assert(value.level ~= nil)
  bRet = Serialization.String.Serialize(writer, value.level)
  if not bRet then
    return false
  end
  assert(value.day ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.day)
  if not bRet then
    return false
  end
  assert(value.exp ~= nil)
  bRet = Serialization.String.Serialize(writer, value.exp)
  if not bRet then
    return false
  end
  assert(value.energy ~= nil)
  bRet = Serialization.String.Serialize(writer, value.energy)
  if not bRet then
    return false
  end
  assert(value.progress ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.progress)
  if not bRet then
    return false
  end
  return true
end

function Serialization.UserBasicInfo.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.UserBasicInfo)
  bRet, value.gold = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.gem = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.skipprop = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.level = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.day = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.exp = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.energy = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.progress = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.UpdUserBranchReq = {}
Serialization.UpdUserBranchReq.__index = Serialization.UpdUserBranchReq

function Serialization.UpdUserBranchReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.version ~= nil)
  bRet = Serialization.String.Serialize(writer, value.version)
  if not bRet then
    return false
  end
  assert(value.syncTime ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.syncTime)
  if not bRet then
    return false
  end
  assert(value.syncList ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.syncList)
  if not bRet then
    return false
  end
  assert(value.user ~= nil)
  bRet = Serialization.Array(Serialization.UserBasicInfo).Serialize(writer, value.user)
  if not bRet then
    return false
  end
  assert(value.energy ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.energy)
  if not bRet then
    return false
  end
  assert(value.board ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.board)
  if not bRet then
    return false
  end
  assert(value.cacheItem ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.cacheItem)
  if not bRet then
    return false
  end
  assert(value.mainTask ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.mainTask)
  if not bRet then
    return false
  end
  assert(value.slot ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.slot)
  if not bRet then
    return false
  end
  assert(value.itemUnlock ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.itemUnlock)
  if not bRet then
    return false
  end
  assert(value.shopItem ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.shopItem)
  if not bRet then
    return false
  end
  assert(value.shop ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.shop)
  if not bRet then
    return false
  end
  assert(value.inventory ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.inventory)
  if not bRet then
    return false
  end
  assert(value.item ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.item)
  if not bRet then
    return false
  end
  assert(value.tutorial ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.tutorial)
  if not bRet then
    return false
  end
  assert(value.orders ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.orders)
  if not bRet then
    return false
  end
  assert(value.orderMeta ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.orderMeta)
  if not bRet then
    return false
  end
  assert(value.misc ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.misc)
  if not bRet then
    return false
  end
  assert(value.bundle ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.bundle)
  if not bRet then
    return false
  end
  assert(value.ad_status ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.ad_status)
  if not bRet then
    return false
  end
  assert(value.bundle_meta ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.bundle_meta)
  if not bRet then
    return false
  end
  assert(value.activity_data ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.activity_data)
  if not bRet then
    return false
  end
  assert(value.profile ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.profile)
  if not bRet then
    return false
  end
  assert(value.return_user ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.return_user)
  if not bRet then
    return false
  end
  assert(value.mainTaskMeta ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.mainTaskMeta)
  if not bRet then
    return false
  end
  assert(value.local_rewards ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.local_rewards)
  if not bRet then
    return false
  end
  assert(value.bi_sync ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.bi_sync)
  if not bRet then
    return false
  end
  assert(value.notice ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.notice)
  if not bRet then
    return false
  end
  assert(value.currentTask ~= nil)
  bRet = Serialization.String.Serialize(writer, value.currentTask)
  if not bRet then
    return false
  end
  assert(value.currentChapter ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.currentChapter)
  if not bRet then
    return false
  end
  assert(value.orderChapterId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.orderChapterId)
  if not bRet then
    return false
  end
  assert(value.orderGroupId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.orderGroupId)
  if not bRet then
    return false
  end
  assert(value.installation_id ~= nil)
  bRet = Serialization.String.Serialize(writer, value.installation_id)
  if not bRet then
    return false
  end
  assert(value.device_model ~= nil)
  bRet = Serialization.String.Serialize(writer, value.device_model)
  if not bRet then
    return false
  end
  assert(value.icon ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon)
  if not bRet then
    return false
  end
  assert(value.name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.name)
  if not bRet then
    return false
  end
  return true
end

function Serialization.UpdUserBranchReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.UpdUserBranchReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.version = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.syncTime = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.syncList = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.user = Serialization.Array(Serialization.UserBasicInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.energy = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.board = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.cacheItem = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.mainTask = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.slot = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.itemUnlock = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.shopItem = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.shop = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.inventory = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.item = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.tutorial = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orders = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orderMeta = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.misc = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.bundle = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ad_status = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.bundle_meta = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.activity_data = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.profile = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.return_user = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.mainTaskMeta = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.local_rewards = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.bi_sync = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.notice = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.currentTask = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.currentChapter = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orderChapterId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orderGroupId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.installation_id = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.device_model = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.UpdUserBranchResp = {}
Serialization.UpdUserBranchResp.__index = Serialization.UpdUserBranchResp

function Serialization.UpdUserBranchResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.newSyncTime ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.newSyncTime)
  if not bRet then
    return false
  end
  return true
end

function Serialization.UpdUserBranchResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.UpdUserBranchResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.newSyncTime = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.GetUserBranchReq = {}
Serialization.GetUserBranchReq.__index = Serialization.GetUserBranchReq

function Serialization.GetUserBranchReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.version ~= nil)
  bRet = Serialization.String.Serialize(writer, value.version)
  if not bRet then
    return false
  end
  return true
end

function Serialization.GetUserBranchReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.GetUserBranchReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.version = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.GetUserBranchResp = {}
Serialization.GetUserBranchResp.__index = Serialization.GetUserBranchResp

function Serialization.GetUserBranchResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.syncTime ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.syncTime)
  if not bRet then
    return false
  end
  assert(value.user ~= nil)
  bRet = Serialization.Array(Serialization.UserBasicInfo).Serialize(writer, value.user)
  if not bRet then
    return false
  end
  assert(value.energy ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.energy)
  if not bRet then
    return false
  end
  assert(value.board ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.board)
  if not bRet then
    return false
  end
  assert(value.cacheItem ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.cacheItem)
  if not bRet then
    return false
  end
  assert(value.mainTask ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.mainTask)
  if not bRet then
    return false
  end
  assert(value.slot ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.slot)
  if not bRet then
    return false
  end
  assert(value.itemUnlock ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.itemUnlock)
  if not bRet then
    return false
  end
  assert(value.shopItem ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.shopItem)
  if not bRet then
    return false
  end
  assert(value.shop ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.shop)
  if not bRet then
    return false
  end
  assert(value.inventory ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.inventory)
  if not bRet then
    return false
  end
  assert(value.item ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.item)
  if not bRet then
    return false
  end
  assert(value.tutorial ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.tutorial)
  if not bRet then
    return false
  end
  assert(value.orders ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.orders)
  if not bRet then
    return false
  end
  assert(value.orderMeta ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.orderMeta)
  if not bRet then
    return false
  end
  assert(value.misc ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.misc)
  if not bRet then
    return false
  end
  assert(value.bundle ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.bundle)
  if not bRet then
    return false
  end
  assert(value.ad_status ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.ad_status)
  if not bRet then
    return false
  end
  assert(value.bundle_meta ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.bundle_meta)
  if not bRet then
    return false
  end
  assert(value.activity_data ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.activity_data)
  if not bRet then
    return false
  end
  assert(value.profile ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.profile)
  if not bRet then
    return false
  end
  assert(value.return_user ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.return_user)
  if not bRet then
    return false
  end
  assert(value.mainTaskMeta ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.mainTaskMeta)
  if not bRet then
    return false
  end
  assert(value.local_rewards ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.local_rewards)
  if not bRet then
    return false
  end
  assert(value.bi_sync ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.bi_sync)
  if not bRet then
    return false
  end
  assert(value.notice ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.notice)
  if not bRet then
    return false
  end
  assert(value.force_sync ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.force_sync)
  if not bRet then
    return false
  end
  assert(value.currentTask ~= nil)
  bRet = Serialization.String.Serialize(writer, value.currentTask)
  if not bRet then
    return false
  end
  assert(value.currentChapter ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.currentChapter)
  if not bRet then
    return false
  end
  assert(value.orderChapterId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.orderChapterId)
  if not bRet then
    return false
  end
  assert(value.orderGroupId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.orderGroupId)
  if not bRet then
    return false
  end
  assert(value.icon ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon)
  if not bRet then
    return false
  end
  assert(value.name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.name)
  if not bRet then
    return false
  end
  return true
end

function Serialization.GetUserBranchResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.GetUserBranchResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.syncTime = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.user = Serialization.Array(Serialization.UserBasicInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.energy = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.board = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.cacheItem = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.mainTask = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.slot = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.itemUnlock = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.shopItem = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.shop = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.inventory = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.item = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.tutorial = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orders = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orderMeta = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.misc = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.bundle = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.ad_status = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.bundle_meta = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.activity_data = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.profile = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.return_user = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.mainTaskMeta = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.local_rewards = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.bi_sync = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.notice = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.force_sync = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.currentTask = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.currentChapter = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orderChapterId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orderGroupId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.GetOtherProgressReq = {}
Serialization.GetOtherProgressReq.__index = Serialization.GetOtherProgressReq

function Serialization.GetOtherProgressReq.Serialize(writer, value)
  local bRet
  assert(value.otherId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.otherId)
  if not bRet then
    return false
  end
  assert(value.version ~= nil)
  bRet = Serialization.String.Serialize(writer, value.version)
  if not bRet then
    return false
  end
  return true
end

function Serialization.GetOtherProgressReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.GetOtherProgressReq)
  bRet, value.otherId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.version = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.GetOtherProgressResp = {}
Serialization.GetOtherProgressResp.__index = Serialization.GetOtherProgressResp

function Serialization.GetOtherProgressResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.syncTime ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.syncTime)
  if not bRet then
    return false
  end
  assert(value.gem ~= nil)
  bRet = Serialization.String.Serialize(writer, value.gem)
  if not bRet then
    return false
  end
  assert(value.skipprop ~= nil)
  bRet = Serialization.String.Serialize(writer, value.skipprop)
  if not bRet then
    return false
  end
  assert(value.gold ~= nil)
  bRet = Serialization.String.Serialize(writer, value.gold)
  if not bRet then
    return false
  end
  assert(value.level ~= nil)
  bRet = Serialization.String.Serialize(writer, value.level)
  if not bRet then
    return false
  end
  assert(value.currentTask ~= nil)
  bRet = Serialization.String.Serialize(writer, value.currentTask)
  if not bRet then
    return false
  end
  assert(value.currentChapter ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.currentChapter)
  if not bRet then
    return false
  end
  assert(value.orderChapterId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.orderChapterId)
  if not bRet then
    return false
  end
  assert(value.orderGroupId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.orderGroupId)
  if not bRet then
    return false
  end
  return true
end

function Serialization.GetOtherProgressResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.GetOtherProgressResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.syncTime = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.gem = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.skipprop = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.gold = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.level = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.currentTask = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.currentChapter = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orderChapterId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.orderGroupId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.GetUserRewardsReq = {}
Serialization.GetUserRewardsReq.__index = Serialization.GetUserRewardsReq

function Serialization.GetUserRewardsReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.reward_keys ~= nil)
  bRet = Serialization.Array(Serialization.String).Serialize(writer, value.reward_keys)
  if not bRet then
    return false
  end
  return true
end

function Serialization.GetUserRewardsReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.GetUserRewardsReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.reward_keys = Serialization.Array(Serialization.String).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.GetUserRewardsResp = {}
Serialization.GetUserRewardsResp.__index = Serialization.GetUserRewardsResp

function Serialization.GetUserRewardsResp.Serialize(writer, value)
  local bRet
  assert(value.rewards ~= nil)
  bRet = Serialization.Array(Serialization.RewardInfo).Serialize(writer, value.rewards)
  if not bRet then
    return false
  end
  return true
end

function Serialization.GetUserRewardsResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.GetUserRewardsResp)
  bRet, value.rewards = Serialization.Array(Serialization.RewardInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.ReceiveRewardsReq = {}
Serialization.ReceiveRewardsReq.__index = Serialization.ReceiveRewardsReq

function Serialization.ReceiveRewardsReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.key ~= nil)
  bRet = Serialization.String.Serialize(writer, value.key)
  if not bRet then
    return false
  end
  assert(value.token ~= nil)
  bRet = Serialization.String.Serialize(writer, value.token)
  if not bRet then
    return false
  end
  return true
end

function Serialization.ReceiveRewardsReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.ReceiveRewardsReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.key = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.token = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.ReceiveRewardsResp = {}
Serialization.ReceiveRewardsResp.__index = Serialization.ReceiveRewardsResp

function Serialization.ReceiveRewardsResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  return true
end

function Serialization.ReceiveRewardsResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.ReceiveRewardsResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.HeartBeatReq = {}
Serialization.HeartBeatReq.__index = Serialization.HeartBeatReq

function Serialization.HeartBeatReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.session ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.session)
  if not bRet then
    return false
  end
  assert(value.user_level ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.user_level)
  if not bRet then
    return false
  end
  return true
end

function Serialization.HeartBeatReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.HeartBeatReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.session = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.user_level = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.HeartBeatResp = {}
Serialization.HeartBeatResp.__index = Serialization.HeartBeatResp

function Serialization.HeartBeatResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  return true
end

function Serialization.HeartBeatResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.HeartBeatResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.MarkLogoutReq = {}
Serialization.MarkLogoutReq.__index = Serialization.MarkLogoutReq

function Serialization.MarkLogoutReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.version ~= nil)
  bRet = Serialization.String.Serialize(writer, value.version)
  if not bRet then
    return false
  end
  return true
end

function Serialization.MarkLogoutReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.MarkLogoutReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.version = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.MarkLogoutResp = {}
Serialization.MarkLogoutResp.__index = Serialization.MarkLogoutResp

function Serialization.MarkLogoutResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  return true
end

function Serialization.MarkLogoutResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.MarkLogoutResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.CoinRaceGroupsNum = {}
Serialization.CoinRaceGroupsNum.__index = Serialization.CoinRaceGroupsNum

function Serialization.CoinRaceGroupsNum.Serialize(writer, value)
  local bRet
  assert(value.group_type ~= nil)
  bRet = Serialization.String.Serialize(writer, value.group_type)
  if not bRet then
    return false
  end
  assert(value.group_num ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.group_num)
  if not bRet then
    return false
  end
  return true
end

function Serialization.CoinRaceGroupsNum.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.CoinRaceGroupsNum)
  bRet, value.group_type = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.group_num = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.CoinRaceRobotInfo = {}
Serialization.CoinRaceRobotInfo.__index = Serialization.CoinRaceRobotInfo

function Serialization.CoinRaceRobotInfo.Serialize(writer, value)
  local bRet
  assert(value.userid ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userid)
  if not bRet then
    return false
  end
  assert(value.name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.name)
  if not bRet then
    return false
  end
  assert(value.icon ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon)
  if not bRet then
    return false
  end
  assert(value.icon_frame ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon_frame)
  if not bRet then
    return false
  end
  assert(value.group_type ~= nil)
  bRet = Serialization.String.Serialize(writer, value.group_type)
  if not bRet then
    return false
  end
  assert(value.score ~= nil)
  bRet = Serialization.String.Serialize(writer, value.score)
  if not bRet then
    return false
  end
  return true
end

function Serialization.CoinRaceRobotInfo.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.CoinRaceRobotInfo)
  bRet, value.userid = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon_frame = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.group_type = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.score = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.CoinRaceEntryReq = {}
Serialization.CoinRaceEntryReq.__index = Serialization.CoinRaceEntryReq

function Serialization.CoinRaceEntryReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.exclude_userid ~= nil)
  bRet = Serialization.Array(Serialization.VarUInt64).Serialize(writer, value.exclude_userid)
  if not bRet then
    return false
  end
  assert(value.round ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.round)
  if not bRet then
    return false
  end
  assert(value.robot_count ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.robot_count)
  if not bRet then
    return false
  end
  assert(value.groups_num ~= nil)
  bRet = Serialization.Array(Serialization.CoinRaceGroupsNum).Serialize(writer, value.groups_num)
  if not bRet then
    return false
  end
  return true
end

function Serialization.CoinRaceEntryReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.CoinRaceEntryReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.exclude_userid = Serialization.Array(Serialization.VarUInt64).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.round = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.robot_count = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.groups_num = Serialization.Array(Serialization.CoinRaceGroupsNum).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.CoinRaceEntryResp = {}
Serialization.CoinRaceEntryResp.__index = Serialization.CoinRaceEntryResp

function Serialization.CoinRaceEntryResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.robots ~= nil)
  bRet = Serialization.Array(Serialization.CoinRaceRobotInfo).Serialize(writer, value.robots)
  if not bRet then
    return false
  end
  return true
end

function Serialization.CoinRaceEntryResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.CoinRaceEntryResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.robots = Serialization.Array(Serialization.CoinRaceRobotInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.PkRaceGroupsNum = {}
Serialization.PkRaceGroupsNum.__index = Serialization.PkRaceGroupsNum

function Serialization.PkRaceGroupsNum.Serialize(writer, value)
  local bRet
  assert(value.group_type ~= nil)
  bRet = Serialization.String.Serialize(writer, value.group_type)
  if not bRet then
    return false
  end
  assert(value.group_num ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.group_num)
  if not bRet then
    return false
  end
  return true
end

function Serialization.PkRaceGroupsNum.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.PkRaceGroupsNum)
  bRet, value.group_type = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.group_num = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.PkRaceRobotInfo = {}
Serialization.PkRaceRobotInfo.__index = Serialization.PkRaceRobotInfo

function Serialization.PkRaceRobotInfo.Serialize(writer, value)
  local bRet
  assert(value.userid ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userid)
  if not bRet then
    return false
  end
  assert(value.name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.name)
  if not bRet then
    return false
  end
  assert(value.icon ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon)
  if not bRet then
    return false
  end
  assert(value.icon_frame ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon_frame)
  if not bRet then
    return false
  end
  assert(value.group_type ~= nil)
  bRet = Serialization.String.Serialize(writer, value.group_type)
  if not bRet then
    return false
  end
  assert(value.score ~= nil)
  bRet = Serialization.String.Serialize(writer, value.score)
  if not bRet then
    return false
  end
  return true
end

function Serialization.PkRaceRobotInfo.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.PkRaceRobotInfo)
  bRet, value.userid = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon_frame = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.group_type = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.score = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.PkRaceEntryReq = {}
Serialization.PkRaceEntryReq.__index = Serialization.PkRaceEntryReq

function Serialization.PkRaceEntryReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.exclude_userid ~= nil)
  bRet = Serialization.Array(Serialization.VarUInt64).Serialize(writer, value.exclude_userid)
  if not bRet then
    return false
  end
  assert(value.round ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.round)
  if not bRet then
    return false
  end
  assert(value.robot_count ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.robot_count)
  if not bRet then
    return false
  end
  assert(value.groups_num ~= nil)
  bRet = Serialization.Array(Serialization.PkRaceGroupsNum).Serialize(writer, value.groups_num)
  if not bRet then
    return false
  end
  return true
end

function Serialization.PkRaceEntryReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.PkRaceEntryReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.exclude_userid = Serialization.Array(Serialization.VarUInt64).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.round = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.robot_count = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.groups_num = Serialization.Array(Serialization.PkRaceGroupsNum).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.PkRaceEntryResp = {}
Serialization.PkRaceEntryResp.__index = Serialization.PkRaceEntryResp

function Serialization.PkRaceEntryResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.robots ~= nil)
  bRet = Serialization.Array(Serialization.PkRaceRobotInfo).Serialize(writer, value.robots)
  if not bRet then
    return false
  end
  return true
end

function Serialization.PkRaceEntryResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.PkRaceEntryResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.robots = Serialization.Array(Serialization.PkRaceRobotInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.PkRace1GroupsNum = {}
Serialization.PkRace1GroupsNum.__index = Serialization.PkRace1GroupsNum

function Serialization.PkRace1GroupsNum.Serialize(writer, value)
  local bRet
  assert(value.group_type ~= nil)
  bRet = Serialization.String.Serialize(writer, value.group_type)
  if not bRet then
    return false
  end
  assert(value.group_num ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.group_num)
  if not bRet then
    return false
  end
  return true
end

function Serialization.PkRace1GroupsNum.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.PkRace1GroupsNum)
  bRet, value.group_type = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.group_num = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.PkRace1RobotInfo = {}
Serialization.PkRace1RobotInfo.__index = Serialization.PkRace1RobotInfo

function Serialization.PkRace1RobotInfo.Serialize(writer, value)
  local bRet
  assert(value.userid ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userid)
  if not bRet then
    return false
  end
  assert(value.name ~= nil)
  bRet = Serialization.String.Serialize(writer, value.name)
  if not bRet then
    return false
  end
  assert(value.icon ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon)
  if not bRet then
    return false
  end
  assert(value.icon_frame ~= nil)
  bRet = Serialization.String.Serialize(writer, value.icon_frame)
  if not bRet then
    return false
  end
  assert(value.group_type ~= nil)
  bRet = Serialization.String.Serialize(writer, value.group_type)
  if not bRet then
    return false
  end
  assert(value.second ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.second)
  if not bRet then
    return false
  end
  return true
end

function Serialization.PkRace1RobotInfo.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.PkRace1RobotInfo)
  bRet, value.userid = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.name = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.icon_frame = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.group_type = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.second = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.PkRace1EntryReq = {}
Serialization.PkRace1EntryReq.__index = Serialization.PkRace1EntryReq

function Serialization.PkRace1EntryReq.Serialize(writer, value)
  local bRet
  assert(value.userId ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.userId)
  if not bRet then
    return false
  end
  assert(value.exclude_userid ~= nil)
  bRet = Serialization.Array(Serialization.VarUInt64).Serialize(writer, value.exclude_userid)
  if not bRet then
    return false
  end
  assert(value.round ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.round)
  if not bRet then
    return false
  end
  assert(value.robot_count ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.robot_count)
  if not bRet then
    return false
  end
  assert(value.groups_num ~= nil)
  bRet = Serialization.Array(Serialization.PkRace1GroupsNum).Serialize(writer, value.groups_num)
  if not bRet then
    return false
  end
  return true
end

function Serialization.PkRace1EntryReq.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.PkRace1EntryReq)
  bRet, value.userId = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.exclude_userid = Serialization.Array(Serialization.VarUInt64).Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.round = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.robot_count = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.groups_num = Serialization.Array(Serialization.PkRace1GroupsNum).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.PkRace1EntryResp = {}
Serialization.PkRace1EntryResp.__index = Serialization.PkRace1EntryResp

function Serialization.PkRace1EntryResp.Serialize(writer, value)
  local bRet
  assert(value.rcode ~= nil)
  bRet = Serialization.VarUInt64.Serialize(writer, value.rcode)
  if not bRet then
    return false
  end
  assert(value.robots ~= nil)
  bRet = Serialization.Array(Serialization.PkRace1RobotInfo).Serialize(writer, value.robots)
  if not bRet then
    return false
  end
  return true
end

function Serialization.PkRace1EntryResp.Deserialize(reader)
  local bRet
  local value = setmetatable({}, Serialization.PkRace1EntryResp)
  bRet, value.rcode = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, value.robots = Serialization.Array(Serialization.PkRace1RobotInfo).Deserialize(reader)
  if not bRet then
    return false
  end
  return true, value
end

Serialization.reqNameMap = {
  BLLogin = Serialization.LoginReq,
  BLUpdUserBranch = Serialization.UpdUserBranchReq,
  BLGetUserBranch = Serialization.GetUserBranchReq,
  BLGetOtherProgress = Serialization.GetOtherProgressReq,
  BLGetUserRewards = Serialization.GetUserRewardsReq,
  BLReceiveRewards = Serialization.ReceiveRewardsReq,
  BLHeartBeat = Serialization.HeartBeatReq,
  BLMarkLogout = Serialization.MarkLogoutReq,
  BLCoinRaceEntry = Serialization.CoinRaceEntryReq,
  BLPkRaceEntry = Serialization.PkRaceEntryReq,
  BLPkRace1Entry = Serialization.PkRace1EntryReq
}
Serialization.respNameMap = {
  BLLogin = Serialization.LoginResp,
  BLUpdUserBranch = Serialization.UpdUserBranchResp,
  BLGetUserBranch = Serialization.GetUserBranchResp,
  BLGetOtherProgress = Serialization.GetOtherProgressResp,
  BLGetUserRewards = Serialization.GetUserRewardsResp,
  BLReceiveRewards = Serialization.ReceiveRewardsResp,
  BLHeartBeat = Serialization.HeartBeatResp,
  BLMarkLogout = Serialization.MarkLogoutResp,
  BLCoinRaceEntry = Serialization.CoinRaceEntryResp,
  BLPkRaceEntry = Serialization.PkRaceEntryResp,
  BLPkRace1Entry = Serialization.PkRace1EntryResp
}
return Serialization
