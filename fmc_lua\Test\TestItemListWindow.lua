TestItemListWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestItemListWindow.__index = TestItemListWindow
local EListMode = {
  CacheItem = 1,
  AddToBoard = 2,
  MarkFavorite = 3,
  UnmarkFavorite = 4
}

function TestItemListWindow:Init()
  self.m_curMode = EListMode.CacheItem
  self:_UpdateSwitchText()
  local mapItemKeys = GM.TestModel:GetMapItemTypeKeysInSequence()
  for _, v in ipairs(mapItemKeys) do
    local obj = GameObject.Instantiate(self.m_groupGo, self.m_contentRectTrans)
    obj:SetActive(true)
    local cell = obj:GetLuaTable()
    cell:LazyInit(v, self)
  end
end

function TestItemListWindow:GetListMode()
  return self.m_curMode
end

function TestItemListWindow:Refresh()
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_contentRectTrans)
end

function TestItemListWindow:OnSwitchButtonClicked()
  self.m_curMode = self.m_curMode % 4 + 1
  self:_UpdateSwitchText()
end

function TestItemListWindow:_UpdateSwitchText()
  if self.m_curMode == EListMode.CacheItem then
    self.m_switchText.text = "添加到缓存队列"
  elseif self.m_curMode == EListMode.AddToBoard then
    self.m_switchText.text = "添加到棋盘"
  elseif self.m_curMode == EListMode.MarkFavorite then
    self.m_switchText.text = "收藏"
  elseif self.m_curMode == EListMode.UnmarkFavorite then
    self.m_switchText.text = "取消收藏"
  end
end

function TestItemListWindow:OnAddItem()
  local boardModel = BoardModelHelper.GetActiveModel()
  if boardModel == nil then
    GM.UIManager:ShowPrompt("当前不在棋盘场景")
    return
  end
  local arrItems = GM.TestModel:GetFavoriteItems()
  if #arrItems == 0 then
    GM.UIManager:ShowPrompt("没有收藏棋子")
    return
  end
  local index = 1
  local testModel = GM.TestModel
  local position
  for y = 1, boardModel:GetVerticalTiles() do
    for x = 1, boardModel:GetHorizontalTiles() do
      position = boardModel:CreatePosition(x, y)
      if boardModel:GetItem(position) == nil then
        testModel:AddItemOnBoard(boardModel, position, arrItems[index])
        index = index + 1
        if index > #arrItems then
          index = 1
        end
      end
    end
  end
  GM.UIManager:ShowPrompt("棋盘已满")
end

function TestItemListWindow:OnClickClean()
  EventDispatcher.DispatchEvent(EEventType.TestOrderGroupSelectItem, CLEAN_ITEM_CODE)
  self:Close()
end

TestItemListGroup = {}
TestItemListGroup.__index = TestItemListGroup

function TestItemListGroup:LazyInit(itemKey, listWindow)
  local text = itemKey
  if tonumber(text) ~= nil and tonumber(text) >= 4000 and tonumber(text) < 5000 then
    text = text .. " " .. GM.GameTextModel:GetText("chain_" .. itemKey .. "_name")
  elseif tonumber(text) ~= nil then
    text = text .. " " .. GM.GameTextModel:GetText("chain_it_" .. itemKey .. "_1_name")
  end
  self.m_titleText.text = text
  self.m_contentTrans.gameObject:SetActive(false)
  UIUtil.SetSizeDelta(self.m_groupTrans, nil, 100)
  self.m_itemKey = itemKey
  self.m_window = listWindow
end

function TestItemListGroup:_RealInit()
  local items = GM.TestModel:GetItemTypesInMap()
  items = items[self.m_itemKey]
  for _, v in ipairs(items) do
    local obj = GameObject.Instantiate(self.m_itemCellGo, self.m_contentTrans)
    obj:SetActive(true)
    local cell = obj:GetLuaTable()
    cell:Init(v, self.m_window)
  end
  self.m_contentHeight = 220 * math.ceil(#items / 6)
  self.m_realInit = true
end

function TestItemListGroup:OnClicked()
  if not self.m_realInit then
    self:_RealInit()
  end
  local active = self.m_contentTrans.gameObject.activeInHierarchy
  self.m_contentTrans.gameObject:SetActive(not active)
  UIUtil.SetSizeDelta(self.m_groupTrans, nil, 100 + (active and 0 or self.m_contentHeight))
  self.m_arrowTrans.rotation = Quaternion.Euler(Vector3(0, 0, active and 0 or -90))
  self.m_window:Refresh()
end

TestItemListCell = {}
TestItemListCell.__index = TestItemListCell

function TestItemListCell:Init(itemType, listWindow)
  self.m_type = itemType
  self.m_window = listWindow
  self.m_typeText.text = itemType
  self:UpdateFavorite()
  local config = GM.ItemDataModel:GetViewConfig(itemType)
  local imageFileConfig = GM.DataResource.ImageFileConfig
  if config and config.Image ~= nil then
    for _, spriteName in ipairs(config.Image) do
      if imageFileConfig:GetConfig(spriteName) == nil then
        GM.UIManager:ShowPrompt("资源缺失，棋子类型：" .. itemType .. " 资源名称：" .. spriteName)
        self.m_typeText.text = self.m_typeText.text .. "(缺失)"
        return
      end
    end
  elseif imageFileConfig:GetConfig(itemType) == nil then
    GM.UIManager:ShowPrompt("资源缺失，棋子类型：" .. itemType)
    self.m_typeText.text = self.m_typeText.text .. "(缺失)"
    return
  end
  SpriteUtil.SetImage(self.m_iconImg, GM.ItemDataModel:GetSpriteName(itemType), true)
end

function TestItemListCell:OnBtnClicked()
  local mode = self.m_window:GetListMode()
  if mode == EListMode.CacheItem then
    local boardModel = BoardModelHelper.GetActiveModel()
    local targetGameMode = ItemUtility.GetModeByCode(self.m_type)
    local targetBoardModel = BoardModelHelper.GetModelByGameMode(targetGameMode)
    if targetGameMode ~= EGameMode.Board and targetBoardModel ~= nil then
      targetBoardModel:CacheItems({
        self.m_type
      }, CacheItemType.Stack)
    elseif boardModel == nil then
      GM.UIManager:ShowPrompt("当前不在棋盘场景")
    else
      boardModel:CacheItems({
        self.m_type
      }, CacheItemType.Stack)
      GM.UIManager:ShowPrompt("添加成功")
    end
  elseif mode == EListMode.AddToBoard then
    local boardModel = BoardModelHelper.GetActiveModel()
    if boardModel == nil then
      GM.UIManager:ShowPrompt("当前不在棋盘场景")
    else
      local position = boardModel:FindEmptyPositionInValidOrder()
      if position == nil then
        GM.UIManager:ShowPrompt("棋盘已满")
      else
        GM.TestModel:AddItemOnBoard(boardModel, position, self.m_type)
        GM.UIManager:ShowPrompt("添加成功")
      end
    end
  elseif mode == EListMode.MarkFavorite then
    if GM.TestModel:AddItemToFavorite(self.m_type) then
      self:UpdateFavorite()
      GM.UIManager:ShowPrompt("添加收藏成功")
    else
      GM.UIManager:ShowPrompt("添加收藏失败")
    end
  elseif mode == EListMode.UnmarkFavorite then
    if GM.TestModel:RemoveItemFromFavorite(self.m_type) then
      self:UpdateFavorite()
      GM.UIManager:ShowPrompt("取消收藏成功")
    else
      GM.UIManager:ShowPrompt("取消收藏失败")
    end
  end
end

function TestItemListCell:UpdateFavorite()
  self.m_favoriteGo:SetActive(GM.TestModel:IsFavoriteItem(self.m_type))
end
