BreakEggScoreBoard = {}
BreakEggScoreBoard.__index = BreakEggScoreBoard

function BreakEggScoreBoard:Init()
  self.m_activityModel = GM.ActivityManager:GetModel(ActivityType.BreakEgg)
  self.m_stepCells = {}
  local totalStep = self.m_activityModel:GetStepTotal()
  for step = 1, totalStep do
    local cellObject = Object.Instantiate(self.m_stepCellPrefab, self.m_progressContentNode)
    local cell = cellObject:GetLuaTable()
    cell:Init(step)
    table.insert(self.m_stepCells, cell)
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_progressContentNode)
end

function BreakEggScoreBoard:UpdateContent()
  local currentStep
  if self.m_activityModel:GetBreakEggState() == BreakEggState.NoStart then
    currentStep = self.m_activityModel:GetStepTotal()
  else
    currentStep = self.m_activityModel:GetCurrentStep()
  end
  for _, cell in ipairs(self.m_stepCells) do
    cell:UpdateContent(currentStep)
  end
  local horizontalNormalizedPosition = self:_GetHorizontalNormalizedPosition(currentStep)
  self.m_progressScrollRect.horizontalNormalizedPosition = horizontalNormalizedPosition
  self:_UpdateJackpot()
end

function BreakEggScoreBoard:PlayFocusAnimation(duration, step)
  local currentStep = step or self.m_activityModel:GetCurrentStep()
  for _, cell in ipairs(self.m_stepCells) do
    cell:UpdateContent(currentStep)
  end
  local horizontalNormalizedPosition = self:_GetHorizontalNormalizedPosition(currentStep)
  self.m_progressScrollRect:DOHorizontalNormalizedPos(horizontalNormalizedPosition, duration)
  self:PlayJackpotChange()
end

function BreakEggScoreBoard:PlayJackpotChange()
  local completeReward = self.m_activityModel:GetCompleteReward()
  local tp = ConfigUtil.GetCurrencyImageName(completeReward)
  local ct = completeReward[PROPERTY_COUNT]
  if self.m_jackpotTp ~= tp and self.m_jackpotCt ~= ct then
    self.m_jackpotCountText:DOFade(0, 0.5)
    self.m_jackpotIconImage:DOFade(0, 0.5)
    DOVirtual.DelayedCall(0.5, function()
      self:_UpdateJackpot()
      self.m_jackpotCountText:DOFade(1, 0.5)
      self.m_jackpotIconImage:DOFade(1, 0.5)
    end)
  end
end

function BreakEggScoreBoard:_UpdateJackpot()
  local completeReward = self.m_activityModel:GetCompleteReward()
  self.m_jackpotTp = ConfigUtil.GetCurrencyImageName(completeReward)
  SpriteUtil.SetImage(self.m_jackpotIconImage, self.m_jackpotTp, true)
  self.m_jackpotCt = completeReward[PROPERTY_COUNT]
  self.m_jackpotCountText.text = "X" .. self.m_jackpotCt
end

function BreakEggScoreBoard:_GetHorizontalNormalizedPosition(step)
  local worldPosition = self.m_stepCells[step].transform.position
  local localPosition = self.m_progressContentNode:InverseTransformPoint(worldPosition)
  local progressViewportWidth = self.m_progressViewportNode.rect.width
  if localPosition.x > progressViewportWidth / 2 then
    local progressContentWidth = self.m_progressContentNode.rect.width
    local normalized = (localPosition.x - progressViewportWidth / 2) / (progressContentWidth - progressViewportWidth)
    return math.min(normalized, 1)
  else
    return 0
  end
end
