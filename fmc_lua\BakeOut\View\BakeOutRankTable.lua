BakeOutRankTable = {INDEX_OFFSET = 1}
BakeOutRankTable.__index = BakeOutRankTable

function BakeOutRankTable:Init(arrRanks, lastRank, window)
  if arrRanks == nil or #arrRanks < self.INDEX_OFFSET then
    return
  end
  self.m_model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  self.m_arrData = arrRanks
  self.m_window = window
  self:_InitContent()
  self.m_lastRank = lastRank
  local initParam = ListViewParam.CopyDefaultInitParam()
  initParam.mItemDefaultWithPaddingSize = self.m_cellHeight
  local listCellCount = self:_GetMaxCellCount() + 1
  self.m_loopListView:InitListView(listCellCount, function(listView, cellIndex)
    return self:GetListItemByIndex(listView, cellIndex)
  end, initParam)
  self.m_loopListView:SetItemSize(0, 25)
  for i = 1, self:_GetMaxCellCount() do
    self.m_loopListView:SetItemSize(i, self.m_cellHeight, 4)
  end
  self.m_loopListView:UpdateContentSize()
  self:FocusCell(self.m_curRank)
  self:_PlayRankUpAnimationIfNeed()
end

function BakeOutRankTable:GetListItemByIndex(listView, cellIndex)
  if cellIndex < 0 then
    return nil
  end
  if cellIndex == 0 then
    return listView:NewListViewItem(UIPrefabConfigName.BakeOutRankCellTransparent)
  end
  cellIndex = cellIndex - 1
  local tbData = self.m_arrData[cellIndex + self.INDEX_OFFSET]
  if not tbData then
    return nil
  end
  local rank = tbData.rank
  local isMyCell = tbData.userid == GM.UserModel:GetUserId()
  local item = listView:NewListViewItem(isMyCell and UIPrefabConfigName.BakeOutOwnRankCell or UIPrefabConfigName.BakeOutRankCell)
  local luaTable = item.gameObject:GetLuaTable()
  luaTable:UpdateContent(tbData, false, self.m_window)
  if self.m_bRankingUp and cellIndex == self.m_lastRank - self.INDEX_OFFSET then
    item.gameObject:SetActive(false)
  end
  return item
end

function BakeOutRankTable:OnDestroy()
  GM.UIManager:RemoveAllEventLocks(self)
end

function BakeOutRankTable:GetCell(cellIndex)
  return self.m_loopListView:GetShownItemByItemIndex(cellIndex - self.INDEX_OFFSET)
end

function BakeOutRankTable:Update()
  self:_AdjustFloatOwnCellAppearance()
end

function BakeOutRankTable:_GetMaxCellCount()
  return #self.m_arrData + 1 - self.INDEX_OFFSET
end

function BakeOutRankTable:_InitContent()
  self.m_scrollRect = self.m_loopListView:GetComponent(typeof(ScrollRect))
  self.m_scrollContentTransform = self.m_scrollRect.content
  self.m_scrollContentHeight = self.m_scrollRect.transform.sizeDelta.y
  self.m_cellHeight = self.m_floatOwnRankCell.gameObject.transform.sizeDelta.y
  self.m_maxCellCountInContent = math.floor(self.m_scrollContentHeight / self.m_cellHeight)
  self.m_padding = self.m_floatOwnRankCell:GetPadding()
  self.m_halfValidContentHeight = (self.m_scrollContentHeight - self.m_padding - self.m_cellHeight) / 2
  self.m_floatOwnRankCellGo = self.m_floatOwnRankCell.gameObject
  self.m_floatOwnRankCellTransform = self.m_floatOwnRankCellGo.transform
  self.m_floatOwnRankCellTopPosition = self.m_padding - self.m_cellHeight / 2 - 18
  self.m_floatOwnRankCellBottomPosition = -self.m_scrollContentHeight + self.m_cellHeight / 2
  self:_InitFloatOwnCell()
end

function BakeOutRankTable:FocusCell(rank)
  if rank and rank >= self.INDEX_OFFSET then
    self.m_loopListView:MovePanelToItemIndex(rank - self.INDEX_OFFSET, self.m_halfValidContentHeight)
  end
end

function BakeOutRankTable:_InitFloatOwnCell()
  local ownData = self.m_model:GetOwnDataFromRanksData(self.m_arrData)
  self.m_bNeedFloatOwnCell = ownData ~= nil and ownData.rank >= self.INDEX_OFFSET
  if self.m_bNeedFloatOwnCell then
    self.m_curRank = ownData.rank
    self.m_floatOwnRankCell:UpdateContent(ownData, false, self.m_window)
  else
    self.m_floatOwnRankCell.gameObject:SetActive(false)
  end
end

function BakeOutRankTable:_AdjustFloatOwnCellAppearance()
  if not self.m_bNeedFloatOwnCell then
    return
  end
  local curPosY = self.m_scrollContentTransform.localPosition.y
  local topThreshold = self:_GetTopThresholdPosY(self.m_curRank)
  local bottomThreshold = self:_GetBottomThresholdPosY(self.m_curRank)
  if not topThreshold or not bottomThreshold then
    return
  end
  local floatOwnCellActive = false
  if math.floor(curPosY) < math.floor(bottomThreshold) then
    floatOwnCellActive = true
    if not self.m_bRankingUp then
      self.m_floatOwnRankCellTransform:SetAnchoredPosY(self.m_floatOwnRankCellBottomPosition)
    end
  elseif math.floor(curPosY) > math.floor(topThreshold) then
    floatOwnCellActive = true
    if not self.m_bRankingUp then
      self.m_floatOwnRankCellTransform:SetAnchoredPosY(self.m_floatOwnRankCellTopPosition)
    end
  end
  self:_ToggleFloatOwnCellVisible(floatOwnCellActive)
end

function BakeOutRankTable:_GetTopThresholdPosY(rank)
  if not rank then
    return nil
  end
  return (rank - self.INDEX_OFFSET) * (self.m_cellHeight + 4) + self.m_padding + 8
end

function BakeOutRankTable:_GetBottomThresholdPosY(rank)
  if not rank then
    return nil
  end
  return self:_GetTopThresholdPosY(rank) - self.m_scrollContentHeight + self.m_cellHeight - self.m_loopListView.EndPadding + 40
end

function BakeOutRankTable:_ToggleFloatOwnCellVisible(active)
  if self.m_floatOwnCellVisible ~= active then
    if self.m_floatOwnCellTween then
      self.m_floatOwnCellTween:Kill()
      self.m_floatOwnCellTween = nil
    end
    if active then
      self.m_floatOwnRankCellGo:SetActive(true)
      self.m_floatOwnRankCellTransform:SetLocalScaleXY(1)
      self.m_floatOwnCellTween = self.m_floatOwnRankCellTransform:DOScale(1.03, 0.2)
    else
      self.m_floatOwnRankCellGo:SetActive(false)
    end
    self.m_floatOwnCellVisible = active
  end
end

function BakeOutRankTable:_PlayRankUpAnimationIfNeed()
  local canPlay = self.m_curRank and self.m_lastRank and self.m_curRank >= self.INDEX_OFFSET and self.m_curRank < self.m_lastRank
  if canPlay then
    self:_PlayRankUpAnimation()
  end
end

function BakeOutRankTable:_PlayRankUpAnimation()
  local beforeStartPauseDt, floatDt, scrollDt = self:_GetRankingUpAnimationDuration(self.m_lastRank, self.m_curRank)
  self.m_scrollRect.movementType = ScrollRect.MovementType.Unrestricted
  if floatDt == 0 then
    self.m_scrollContentTransform:SetLocalPosY(self:_GetTopThresholdPosY(self.m_lastRank))
  else
    self.m_scrollContentTransform:SetLocalPosY(self:_GetBottomThresholdPosY(self.m_lastRank))
    local cell = self.m_loopListView:GetShownItemByItemIndex(self.m_lastRank - self.INDEX_OFFSET)
    if cell ~= nil then
      cell.gameObject:SetActive(false)
    end
  end
  self.m_loopListView:RefreshAllShownItem()
  self.m_bRankingUp = true
  self.m_floatOwnRankCell:UpdateRank(self.m_lastRank)
  self.m_floatOwnRankCellGo:SetActive(true)
  self.m_floatOwnRankCellTransform:SetAnchoredPosY(floatDt == 0 and self.m_floatOwnRankCellTopPosition or self.m_floatOwnRankCellBottomPosition)
  GM.UIManager:SetEventLock(true, self)
  local seq = DOTween.Sequence()
  seq:AppendInterval(beforeStartPauseDt)
  seq:Append(self.m_floatOwnRankCellTransform:DOAnchorPosY(self.m_floatOwnRankCellTopPosition, floatDt):SetEase(Ease.InSine))
  seq:Join(self.m_scrollContentTransform:DOLocalMoveY(self:_GetRankingUpScrollContentTargetY(), scrollDt):SetEase(Ease.InOutQuint))
  seq:Join(self:_GetFloatCellRankChangeTween(scrollDt):SetEase(Ease.InOutCubic))
  seq:OnKill(function()
    self.m_bRankingUp = false
    self.m_loopListView:RefreshAllShownItem()
    self.m_scrollRect.movementType = ScrollRect.MovementType.Elastic
    GM.UIManager:SetEventLock(false, self)
    self.m_lastRank = self.m_curRank
  end)
end

function BakeOutRankTable:_GetRankingUpScrollContentTargetY()
  local myNewRankCenterOffsetY = self:_GetTopThresholdPosY(self.m_curRank) - self.m_halfValidContentHeight
  myNewRankCenterOffsetY = math.max(myNewRankCenterOffsetY, 0)
  myNewRankCenterOffsetY = math.min(myNewRankCenterOffsetY, self:_GetMaxScrollContentOffsetY())
  return myNewRankCenterOffsetY
end

function BakeOutRankTable:_GetMaxScrollContentOffsetY()
  return self:_GetMaxCellCount() * self.m_cellHeight + self.m_loopListView.EndPadding - self.m_scrollContentHeight / 2 + 25
end

function BakeOutRankTable:_GetRankingUpAnimationDuration(rankFrom, rankTo)
  Log.Assert(rankFrom and rankTo and rankTo < rankFrom, "BakeOutRankTable:_GetRankingUpAnimationDuration")
  local deltaRank = rankFrom - rankTo
  local beforeStartPauseDt = 0.5
  local scrollDt = deltaRank * 0.4
  scrollDt = math.max(scrollDt, 1.3)
  scrollDt = math.min(scrollDt, 4)
  local floatDt = scrollDt * 0.4
  if deltaRank <= self.m_maxCellCountInContent then
    floatDt = 0
  end
  return beforeStartPauseDt, floatDt, scrollDt
end

function BakeOutRankTable:_GetFloatCellRankChangeTween(duration)
  return DOTween.To(function()
    return self.m_floatOwnRankCell:GetRank()
  end, function(rank)
    self.m_floatOwnRankCell:UpdateRank(rank)
  end, self.m_curRank, duration)
end
