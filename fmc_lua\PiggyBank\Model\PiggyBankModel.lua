PiggyBankModel = setmetatable({AllowRestoreOnEnd = true}, BaseActivityModel)
PiggyBankModel.__index = PiggyBankModel
local DBKey = {
  AccumulateNum = "an",
  BoughtTag = "bt",
  RecordBuyNum = "rbn",
  RecordFull = "rf",
  RecordIapType = "rit"
}
PiggyBankModel.PiggyBankState = {
  CannotBuy = 1,
  CanBuy = 2,
  Full = 3
}
local DBColumnValue = "value"
local Sec2Day = Sec2Day

function PiggyBankModel:Init(virtualDBTable)
  BaseActivityModel.Init(self, ActivityType.PiggyBank, virtualDBTable)
end

function PiggyBankModel:_OnStateChanged()
  EventDispatcher.DispatchEvent(EEventType.PiggyBankStateChanged)
end

function PiggyBankModel:_LoadOtherServerConfig(config)
  local contentConfig = config.piggy_bank_content and config.piggy_bank_content[1] or Table.Empty
  self.m_payableLine = contentConfig.gemavailable
  self.m_fullLine = contentConfig.gemcapacity
  self.m_payID = contentConfig.payID
  local exchangeConfig = config.piggy_bank_exchange or Table.Empty
  self.m_arrExchangeConfig = {}
  for i, config in pairs(exchangeConfig) do
    self.m_arrExchangeConfig[i] = config
  end
  table.sort(self.m_arrExchangeConfig, function(a, b)
    return a.coin_min < b.coin_min
  end)
end

function PiggyBankModel:GetResourceLabels()
  return {
    AddressableLabel.PiggyBank
  }
end

function PiggyBankModel:IsActivityOpen()
  return self:GetState() == ActivityState.Started and not self:HasBought() and self:GetPurchaseId() ~= nil and self.m_arrExchangeConfig ~= nil and #self.m_arrExchangeConfig > 0
end

function PiggyBankModel:_CanBuyAfterActivityEnd(ignoreRtimeEndShift)
  if self.m_config == nil then
    return false
  end
  local curTime = GM.GameModel:GetServerTime()
  return not self:HasBought() and curTime >= self.m_config.eTime and curTime < self.m_config.rTime - (ignoreRtimeEndShift and 0 or Sec2Day) and self:GetAccumulatedNum() >= self:GetPayableLine()
end

function PiggyBankModel:CanBuy(ignoreRtimeEndShift)
  return self:IsActivityOpen() and self:GetAccumulatedNum() >= self:GetPayableLine() or self:_CanBuyAfterActivityEnd(ignoreRtimeEndShift)
end

function PiggyBankModel:HasBought()
  return self.m_dbTable:GetValue(DBKey.BoughtTag, DBColumnValue) == 1
end

function PiggyBankModel:GetAccumulatedNum()
  return math.min(self:_GetAccumulatedNum(), self:GetFullLine())
end

function PiggyBankModel:_GetAccumulatedNum()
  return self.m_dbTable:GetValue(DBKey.AccumulateNum, DBColumnValue) or 0
end

function PiggyBankModel:GetPayableLine()
  return self.m_payableLine
end

function PiggyBankModel:GetFullLine()
  return self.m_fullLine
end

function PiggyBankModel:GetPiggyBankState(customAccumulatedNum)
  if self:GetFullLine() == nil or self:GetPayableLine() == nil then
    return PiggyBankModel.PiggyBankState.CannotBuy
  end
  local accumulatedNum = customAccumulatedNum or self:_GetAccumulatedNum()
  if accumulatedNum >= self:GetFullLine() then
    return PiggyBankModel.PiggyBankState.Full
  elseif accumulatedNum >= self:GetPayableLine() then
    return PiggyBankModel.PiggyBankState.CanBuy
  else
    return PiggyBankModel.PiggyBankState.CannotBuy
  end
end

function PiggyBankModel:IsFull()
  return self:GetPiggyBankState() == PiggyBankModel.PiggyBankState.Full
end

function PiggyBankModel:GetExchangeGemNum(coinNum)
  if not self:IsActivityOpen() then
    return 0
  end
  for _, config in ipairs(self.m_arrExchangeConfig) do
    if coinNum <= config.coin_max and coinNum >= config.coin_min then
      return config.progress
    end
  end
  return self.m_arrExchangeConfig[#self.m_arrExchangeConfig].progress
end

function PiggyBankModel:Accumulate(num)
  if num == nil or num <= 0 then
    return
  end
  self.m_dbTable:Set(DBKey.AccumulateNum, DBColumnValue, math.min(self:_GetAccumulatedNum() + num, self:GetFullLine()))
  EventDispatcher.DispatchEvent(EEventType.PiggyBankAccumulateGem, {num = num})
end

function PiggyBankModel:GetPurchaseId()
  return self.m_payID
end

function PiggyBankModel:Buy(callback)
  if not self:CanBuy(true) then
    return
  end
  local accumulatedGemNum = self:GetAccumulatedNum()
  local full = self:IsFull()
  local iapType = self:GetPurchaseId()
  self.m_dbTable:Set(DBKey.RecordBuyNum, DBColumnValue, accumulatedGemNum)
  self.m_dbTable:Set(DBKey.RecordFull, DBColumnValue, full and 1 or 0)
  self.m_dbTable:Set(DBKey.RecordIapType, DBColumnValue, iapType)
  GM.InAppPurchaseModel:StartPurchase(self:GetPurchaseId(), function()
    self:_OnBuySuccess(accumulatedGemNum, full, iapType)
  end, EBIType.PiggyBankBuy)
end

function PiggyBankModel:RestoreIapRewards(iapType)
  if iapType == self.m_dbTable:GetValue(DBKey.RecordIapType, DBColumnValue) and self.m_dbTable:GetValue(DBKey.RecordBuyNum, DBColumnValue) ~= nil then
    local accumulatedGemNum = tonumber(self.m_dbTable:GetValue(DBKey.RecordBuyNum, DBColumnValue))
    local full = tonumber(self.m_dbTable:GetValue(DBKey.RecordFull, DBColumnValue)) == 1
    local iapType = self.m_dbTable:GetValue(DBKey.RecordIapType, DBColumnValue)
    self:_OnBuySuccess(accumulatedGemNum, full, iapType)
    return true
  end
  return false
end

function PiggyBankModel:GetBoardEntryShowConfig()
  return {
    statusChangeName = EEventType.PiggyBankStateChanged,
    bubbleName = "m_piggyBankNoticeBubble",
    bubbleNodeName = "m_piggyBankNoticeBubbleNode",
    entryPrefabName = UIPrefabConfigName.PiggyBankNoticeBubble,
    activityType = self:GetType(),
    getBubbleFunName = "GetPiggyBankNoticeBubble",
    destroyFun = function(bubble)
      bubble:Destroy()
    end,
    checkFun = function()
      return self:IsActivityOpen()
    end
  }
end

function PiggyBankModel:_OnBuySuccess(accumulatedGemNum, full, iapType)
  if not self.m_dbTable:IsEmpty() then
    self.m_dbTable:Remove(DBKey.RecordBuyNum)
    self.m_dbTable:Remove(DBKey.RecordFull)
    self.m_dbTable:Remove(DBKey.RecordIapType)
    self.m_dbTable:Set(DBKey.BoughtTag, DBColumnValue, 1)
  end
  local rewards = {
    {
      [PROPERTY_TYPE] = EPropertyType.Gem,
      [PROPERTY_COUNT] = accumulatedGemNum
    }
  }
  local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.PiggyBankMainWindow)
  if mainWindow ~= nil then
    RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Buy, EBIType.PiggyBankBuy, EGameMode.Board, CacheItemType.Stack)
    GM.BIManager:LogAction(EBIType.PiggyBankBuy, {
      n = accumulatedGemNum,
      f = full,
      c = iapType
    })
    mainWindow:OnBuySuccess(rewards, full)
  else
    RewardApi.AcquireRewards(rewards, EPropertySource.Buy, EBIType.PiggyBankBuy, nil, EGameMode.Board, CacheItemType.Stack)
  end
  EventDispatcher.DispatchEvent(EEventType.PiggyBankStateChanged)
end
