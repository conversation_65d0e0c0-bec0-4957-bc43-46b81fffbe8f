LevelDownConfirmWindow = setmetatable({}, TwoButtonWindow)
LevelDownConfirmWindow.__index = LevelDownConfirmWindow

function LevelDownConfirmWindow:Init(beforeItemCode, afterItemCode, confirmCallback)
  local spriteName = GM.ItemDataModel:GetSpriteName(beforeItemCode)
  SpriteUtil.SetImage(self.m_beforeImg, spriteName, true)
  spriteName = GM.ItemDataModel:GetSpriteName(afterItemCode)
  SpriteUtil.SetImage(self.m_afterImg1, spriteName, true)
  SpriteUtil.SetImage(self.m_afterImg2, spriteName, true)
  self.m_greenButtonCallback = confirmCallback
  self:SetCloseBtnActive(true)
end

function LevelDownConfirmWindow:OnGreenClick()
  if self.m_greenButtonCallback then
    self.m_greenButtonCallback()
  end
  self:Close()
end

function LevelDownConfirmWindow:OnCloseView()
  TwoButtonWindow.OnCloseView(self)
  self.m_starEffect:SetActive(false)
end
