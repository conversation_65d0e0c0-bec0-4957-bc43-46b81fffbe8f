BreakEggPopupHelper = setmetatable({}, BasePopupHelper)
BreakEggPopupHelper.__index = BreakEggPopupHelper

function BreakEggPopupHelper.Create()
  local helper = setmetatable({}, BreakEggPopupHelper)
  helper:Init()
  return helper
end

function BreakEggPopupHelper:Init()
  BasePopupHelper.Init(self)
  EventDispatcher.AddListener(EEventType.BreakEggStateChanged, self, self._OnStateChanged)
end

function BreakEggPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function BreakEggPopupHelper:CheckPopup()
  local model = GM.ActivityManager:GetModel(ActivityType.BreakEgg)
  local state = model:GetState()
  if state == ActivityState.Started then
    if not model:HasWindowOpenedOnce(ActivityState.Started) then
      return UIPrefabConfigName.BreakEggMainWindow, {true}
    end
  elseif state == ActivityState.Ended and model:HasReward() then
    model:GetRewardWhenActivityEnd()
  end
end
