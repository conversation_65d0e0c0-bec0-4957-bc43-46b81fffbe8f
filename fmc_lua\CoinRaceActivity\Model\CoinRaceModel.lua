COIN_RACE_MAX_PLAYER_COUNT = 5
CoinRaceModel = setmetatable({
  EntryOpName = "BLCoinRaceEntry"
}, BaseRaceActivityModel)
CoinRaceModel.__index = CoinRaceModel
CoinRaceModel.RoundConfigKey = "coin_race"
local ShowRoundKey = "coinrace_show_round"

function CoinRaceModel:Init(activityType, virtualDBTable)
  BaseRaceActivityModel.Init(self, activityType, virtualDBTable)
end

function CoinRaceModel:GetActivityDefinitionByType(activityType)
  return CoinRaceActivityDefinition[activityType]
end

function CoinRaceModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function CoinRaceModel:GetBoardEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.CoinRace,
    entryPrefabName = self.m_activityDefinition.OrderCellPrefabName,
    checkFun = function()
      return self:CanShowOrderCell()
    end
  }
end

function CoinRaceModel:GetMapEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.CoinRace,
    entryPrefabName = self.m_activityDefinition.EntryPrefabName,
    hudKey = self.m_activityDefinition.EntryButtonKey,
    checkFun = function()
      return self:IsActivityOpen()
    end
  }
end

function CoinRaceModel:GetAllStateChangedEvent()
  return {
    self.m_activityDefinition.StateChangedEvent
  }
end

function CoinRaceModel:AcquireActivityToken(score)
  if 0 < score then
    self:_AddMyScore(score, "order")
  end
end

function CoinRaceModel:GetActivityTokenNumber()
  return self:GetMyScore()
end

function CoinRaceModel:_CreateRobotPlayers(robots)
  local targetScore = self:GetTargetScore()
  local arrTrack = {
    1,
    2,
    4,
    5
  }
  Table.Shuffle(arrTrack)
  self.m_arrPlayerData = {}
  local arrAvatars = GM.UserProfileModel:GetAvatarImagesAll()
  Table.ListRemove(arrAvatars, GM.UserProfileModel:GetIcon())
  local arrSelectAvatar = Table.ListAlwaysRandomSelectN(arrAvatars, #robots)
  local playerData
  for index, value in ipairs(robots) do
    value.icon = arrSelectAvatar[index]
    playerData = RacePlayerData.Create(self, value, targetScore, 0)
    playerData:SetTrack(arrTrack[index])
    self.m_arrPlayerData[#self.m_arrPlayerData + 1] = playerData
  end
end

local arrDefaultRank = {
  [3] = 1,
  [2] = 2,
  [4] = 3,
  [1] = 4,
  [5] = 5
}

function CoinRaceModel:_PlayerRankSortFunc(a, b)
  if a.m_curScore == 0 and b.m_curScore == 0 then
    return arrDefaultRank[a:GetTrack()] < arrDefaultRank[b:GetTrack()]
  else
    return BaseRaceActivityModel._PlayerRankSortFunc(self, a, b)
  end
end

function CoinRaceModel:CanShowOrderCell()
  return self:GetState() == ActivityState.Started and self:IsActivityOpen() and self:IsInRace()
end

function CoinRaceModel:CanAddOrderReward(orderType)
  return self:CanAddScore()
end

function CoinRaceModel:GetOrderExtraReward(orderScore, coinNum)
  if coinNum == nil or coinNum <= 0 then
    return 0
  end
  local ratio = self:GetScoreRatio()
  local tokenNum = math.max(math.floor(coinNum * ratio), 1)
  return {
    [PROPERTY_TYPE] = self.m_activityDefinition.ActivityTokenPropertyType,
    [PROPERTY_COUNT] = tokenNum
  }
end

function CoinRaceModel:GetScoreRatio()
  local divided = self:GetGeneralConfig("divided", EConfigParamType.Int) or 15
  return 1 / divided
end

function CoinRaceModel:_LoadOtherServerConfig(config)
  BaseRaceActivityModel._LoadOtherServerConfig(self, config)
  local arrConfig = Table.DeepCopy(config[CoinRaceModel.RoundConfigKey], true)
  table.sort(arrConfig, function(a, b)
    return a.round < b.round
  end)
  self.m_config.roundConfigs = arrConfig
  self:CheckConfigValid()
end

function CoinRaceModel:_CreateMySelfData()
  local data = BaseRaceActivityModel._CreateMySelfData(self)
  data:SetTrack(3)
  return data
end

function CoinRaceModel:GetCurrentRoundReward()
  local curRound = self:GetCurrentRound()
  local roundConfig = self.m_config.roundConfigs[curRound]
  if not roundConfig then
    return {}
  end
  local rewards = {
    roundConfig.rankReward1,
    roundConfig.rankReward2,
    roundConfig.rankReward3
  }
  return rewards
end

function CoinRaceModel:CanShowRound()
  local switch = self:GetGeneralConfig(ShowRoundKey, EConfigParamType.Int) or 0
  return switch == 1
end
