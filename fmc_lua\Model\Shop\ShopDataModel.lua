ShopDataModel = {}
ShopDataModel.__index = ShopDataModel
local FLASH_SALE_IT_KEY = "it"
local nShopManualRefreshNoBuyItemMultiple = 0.33333

function ShopDataModel:OnLoadFileConfigFinished()
  self:_InitShopOrderConfig()
  self:_InitIAPConfigDatas()
  self:_InitDailyDealsDatas()
  self:_InitFlashSaleConfigDatas()
end

function ShopDataModel:_InitShopOrderConfig()
  local shopOrderConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.ShopOrder)
  local mapShopOrderConfig = {}
  for _, orderConfig in ipairs(shopOrderConfig) do
    mapShopOrderConfig[orderConfig.Name] = orderConfig
  end
  self.m_mapShopOrderConfig = mapShopOrderConfig
end

function ShopDataModel:GetShopOrderConfig()
  return self.m_mapShopOrderConfig
end

function ShopDataModel:_InitIAPConfigDatas()
  self.m_mapIAPDatas = {}
  local eShopType
  for _, data in ipairs(GM.ConfigModel:GetLocalConfig(LocalConfigKey.ShopIAP)) do
    data.goods = ConfigUtil.GetCurrencyFromArrStr(data.goods)
    eShopType = data.type
    if self.m_mapIAPDatas[eShopType] == nil then
      self.m_mapIAPDatas[eShopType] = {}
    end
    self.m_mapIAPDatas[eShopType][#self.m_mapIAPDatas[eShopType] + 1] = data
  end
end

function ShopDataModel:GetIAPConfigDatas(shopType)
  return self.m_mapIAPDatas[shopType]
end

function ShopDataModel:GetIAPConfigData(shopType, eIAPType)
  if self.m_mapIAPDatas[shopType] == nil then
    return nil
  end
  for _, v in ipairs(self.m_mapIAPDatas[shopType]) do
    if v.purchaseId == eIAPType then
      return v
    end
  end
  return nil
end

function ShopDataModel:GeRefreshBaseGemCost()
  return tonumber(self.m_mapFlashSaleConfig.refreshCostCount) or 10
end

function ShopDataModel:GetNextRefreshGemCost(former)
  local multiplier = tonumber(self.m_mapFlashSaleConfig.refreshMultiplier) or 2
  local maxCost = tonumber(self.m_mapFlashSaleConfig.refreshMaxCost) or 40
  return math.ceil(math.min(maxCost, former * multiplier))
end

function ShopDataModel:_InitDailyDealsDatas()
  self.m_arrDailyDealsConfigs = Table.DeepCopy(GM.ConfigModel:GetLocalConfig(LocalConfigKey.ShopDailyDeals) or Table.Empty, true)
  self.m_mapDailyDealsConfigs = {}
  table.sort(self.m_arrDailyDealsConfigs, function(a, b)
    return a.id < b.id
  end)
  for _, config in ipairs(self.m_arrDailyDealsConfigs) do
    config.id = nil
    self.m_mapDailyDealsConfigs[config.itemCode] = config
  end
end

function ShopDataModel:GetNewItemDatasForDailyDeals()
  return self.m_arrDailyDealsConfigs
end

function ShopDataModel:GetDailyDealsConfig(itemCode)
  return self.m_mapDailyDealsConfigs[itemCode] or {}
end

function ShopDataModel:GetNextDailyDealsPrice(itemCode, lastPrice)
  local config = self.m_mapDailyDealsConfigs[itemCode]
  if not config or not config.multiplier then
    return lastPrice
  end
  local nextPrice = lastPrice * config.multiplier
  local maxCost = config.maxCostCount
  if maxCost and nextPrice > maxCost then
    nextPrice = maxCost
  end
  return math.ceil(nextPrice)
end

function ShopDataModel:_InitFlashSaleConfigDatas()
  self.m_mapItemPrice = {}
  local config = GM.ConfigModel:GetLocalConfig(LocalConfigKey.ItemProperty)
  for index, config in ipairs(config) do
    self.m_mapItemPrice[config.Type] = config.BubblePrice
  end
  local itemDataModel = GM.ItemDataModel
  self.m_mapFlashSaleConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.ShopFlashSale) or Table.Empty
  local flashSaleSlotCount = 0
  for _, slotConfig in ipairs(self.m_mapFlashSaleConfig.slots or {}) do
    flashSaleSlotCount = flashSaleSlotCount + 1
    if not tonumber(slotConfig.leftCount) then
      Log.Error("ShopFlashSaleConfig 闪售 leftCount 配置错误")
    end
    if not slotConfig.item or not slotConfig.item[1] then
      Log.Error("ShopFlashSaleConfig 闪售 item 配置错误")
    else
      for _, slotItemConfig in ipairs(slotConfig.item) do
        if not slotItemConfig.itemId or slotItemConfig.itemId ~= FLASH_SALE_IT_KEY and not itemDataModel:IsItemExist(slotItemConfig.itemId) then
          Log.Error("ShopFlashSaleConfig 闪售 itemId 配置错误")
        elseif slotItemConfig.itemId ~= FLASH_SALE_IT_KEY and not self.m_mapItemPrice[slotItemConfig.itemId] then
          Log.Error("ShopFlashSaleConfig 闪售 itemId 没有价格 " .. tostring(slotItemConfig.itemId))
        end
      end
    end
  end
  Log.Assert(0 < flashSaleSlotCount and flashSaleSlotCount % 3 == 0, "闪售商品槽位数不能被3整除！")
end

function ShopDataModel:_GetLackedCount(itemCode, itemCount, codeCountMap)
  local existCount = codeCountMap[itemCode]
  if existCount and 0 < existCount then
    if itemCount <= existCount then
      codeCountMap[itemCode] = existCount - itemCount
      if existCount == itemCount then
        codeCountMap[itemCode] = nil
      end
      return 0
    else
      codeCountMap[itemCode] = nil
      return itemCount - existCount
    end
  else
    return itemCount
  end
end

function ShopDataModel:GetNewItemDatasForFlashSale(aLastShopItem)
  local arrItemSelected = {}
  local arrCount = {}
  local arrITCount = {}
  local mapITIndex = {}
  for _, slotConfig in ipairs(self.m_mapFlashSaleConfig.slots or {}) do
    local selected = Table.ListWeightSelectOne(slotConfig.item, "weight")
    arrItemSelected[#arrItemSelected + 1] = selected.itemId
    arrCount[#arrCount + 1] = slotConfig.leftCount
    if selected.itemId == FLASH_SALE_IT_KEY then
      arrITCount[#arrITCount + 1] = slotConfig.leftCount
    end
  end
  local arrITResults = self:_GetNewItemDatasForFlashSale(aLastShopItem, arrITCount)
  local arrResults = {}
  for index, itemId in ipairs(arrItemSelected) do
    if itemId == FLASH_SALE_IT_KEY then
      arrResults[#arrResults + 1] = arrITResults[1]
      table.remove(arrITResults, 1)
    else
      arrResults[#arrResults + 1] = {
        itemCode = itemId,
        leftCount = arrCount[index],
        startCount = arrCount[index],
        costType = EPropertyType.Gem,
        costCount = self.m_mapItemPrice[itemId] or 999
      }
    end
  end
  return arrResults
end

function ShopDataModel:_GetNewItemDatasForFlashSale(aLastShopItem, arrITCount)
  if #arrITCount <= 0 then
    if GameConfig.IsTestMode() then
      TestAccumulatedLogInfo.LogNow("--------------商城闪售刷新了----------------")
      TestAccumulatedLogInfo.LogNow("商城闪售无需随机生成 IT 棋子")
    end
    return {}
  end
  if GameConfig.IsTestMode() then
    TestAccumulatedLogInfo.Start()
  end
  local directResult, codeCountMap = GM.MainBoardModel:GetUnfilledDirectOrderRequirements()
  local mapPool = {}
  local mapDishResult = {}
  local itemDataModel = GM.ItemDataModel
  for directCode, count in pairs(directResult) do
    if itemDataModel:IsDishes(directCode) then
      mapDishResult[directCode] = (mapDishResult[directCode] or 0) + count
    else
      mapPool[directCode] = (mapPool[directCode] or 0) + count
    end
  end
  repeat
    local mapMaterials = {}
    for code, count in pairs(mapDishResult) do
      for _, materialCode in ipairs(itemDataModel:GetMaterials(code, false, true)) do
        mapMaterials[materialCode] = (mapMaterials[materialCode] or 0) + count
      end
    end
    local tempDishResult = {}
    for materialCode, count in pairs(mapMaterials) do
      local lackedCount = self:_GetLackedCount(materialCode, count, codeCountMap)
      if 0 < lackedCount then
        if not itemDataModel:IsDishes(materialCode) or self.m_mapItemPrice[materialCode] then
          mapPool[materialCode] = (mapPool[materialCode] or 0) + lackedCount
        else
          tempDishResult[materialCode] = (tempDishResult[materialCode] or 0) + count
        end
      end
    end
    mapDishResult = tempDishResult
  until next(mapDishResult) == nil
  local mapWeight = {}
  local mapNoBuyTarget = self:GetShopLastNoBuyItem(aLastShopItem)
  for code, _ in pairs(mapPool) do
    self:_ExpandFlashSalePool(itemDataModel, mapWeight, mapNoBuyTarget, code, 128, 1)
  end
  local arrWeight = {}
  local mapFinalWeight = {}
  local index = 0
  local mapNoSellItems = {}
  for code, weight in pairs(mapWeight) do
    if self.m_mapItemPrice[code] then
      index = index + 1
      arrWeight[index] = {Code = code, Weight = weight}
      mapFinalWeight[code] = weight
    else
      mapNoSellItems[code] = true
    end
  end
  local arrResult = {}
  for i = 1, #arrITCount do
    if not arrWeight[1] then
      break
    end
    local selected, index = Table.ListWeightSelectOne(arrWeight)
    if selected then
      table.remove(arrWeight, index)
      arrResult[i] = {
        itemCode = selected.Code,
        leftCount = arrITCount[i],
        startCount = arrITCount[i],
        costType = EPropertyType.Gem,
        costCount = self.m_mapItemPrice[selected.Code]
      }
    else
      break
    end
  end
  local arrPocketSelected = {}
  if #arrResult < #arrITCount then
    local arrItems = {}
    local index = 0
    for code, price in pairs(self.m_mapItemPrice) do
      if 0 < price and not mapWeight[code] and itemDataModel:IsUnlocked(code) then
        index = index + 1
        arrItems[index] = code
      end
    end
    arrPocketSelected = Table.ListRandomSelectN(arrItems, #arrITCount - #arrResult)
    for _, code in ipairs(arrPocketSelected) do
      arrResult[#arrResult + 1] = {
        itemCode = code,
        leftCount = arrITCount[#arrResult + 1],
        startCount = arrITCount[#arrResult + 1],
        costType = EPropertyType.Gem,
        costCount = self.m_mapItemPrice[code]
      }
    end
  end
  if GameConfig.IsTestMode() then
    TestAccumulatedLogInfo.TestPrintShopFlashSaleInfo(aLastShopItem, mapNoBuyTarget, directResult, mapPool, mapNoSellItems, mapFinalWeight, arrPocketSelected, arrResult)
    TestAccumulatedLogInfo.LogNow()
  end
  return arrResult
end

function ShopDataModel:_ExpandFlashSalePool(itemDataModel, mapWeight, mapNoBuyTarget, code, weight, level)
  local saveWeight = mapWeight[code] or 0
  local newWeight = mapNoBuyTarget[code] and weight * nShopManualRefreshNoBuyItemMultiple or weight
  if saveWeight < newWeight then
    mapWeight[code] = newWeight
  end
  if 3 <= level then
    return
  end
  if itemDataModel:IsDishes(code) then
    for _, materialCode in ipairs(itemDataModel:GetMaterials(code, false, true)) do
      self:_ExpandFlashSalePool(itemDataModel, mapWeight, mapNoBuyTarget, materialCode, weight / 2, level + 1)
    end
    return
  end
  local itemConfig = itemDataModel:GetModelConfig(code)
  local transformFrom = itemConfig.TransformFrom
  if transformFrom then
    self:_ExpandFlashSalePool(itemDataModel, mapWeight, mapNoBuyTarget, transformFrom, weight / 2, level + 1)
    return
  end
  local mergeFrom = itemConfig.MergedFromType
  if mergeFrom then
    self:_ExpandFlashSalePool(itemDataModel, mapWeight, mapNoBuyTarget, mergeFrom, weight / 2, level + 1)
    return
  end
end

function ShopDataModel:GetShopLastNoBuyItem(aLastShopItem)
  local mapNoBuyTarget = {}
  for _, eleItemCfg in pairs(aLastShopItem) do
    local startCount = tonumber(eleItemCfg.startCount) or 5
    if startCount == eleItemCfg.leftCount then
      mapNoBuyTarget[eleItemCfg.itemCode] = true
    end
  end
  return mapNoBuyTarget
end

function ShopDataModel:GetBuyEnergyCount()
  return 100
end

function ShopDataModel:GetBuyEnergyBaseGemCost()
  return GM.SystemConfigModel:GetConfig(SystemConfigKey.BuyEnergyBaseGemPrice) or 10
end

function ShopDataModel:GetMaxBuyEnergyGemCost()
  return GM.SystemConfigModel:GetConfig(SystemConfigKey.BuyEnergyMaxGemPrice) or 40
end

function ShopDataModel:GetBuyEnergyMultiplier()
  return GM.SystemConfigModel:GetConfig(SystemConfigKey.BuyEnergyMultiplier) or 2
end

function ShopDataModel:GetNextBuyEnergyGemCost(former)
  local nextCost = math.min(self:GetMaxBuyEnergyGemCost(), former * self:GetBuyEnergyMultiplier())
  return math.ceil(nextCost)
end
