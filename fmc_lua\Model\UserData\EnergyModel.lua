local Energy_DBKey = "energyValue"
local GenerationTime_DBKey = "generateTime"
local InfiniteTime_DBKey = "infiniteTime"
EnergyType = {Main = "1", Event = "2"}
EnergyModel = {}
EnergyModel.__index = EnergyModel
EnergyModel.MaxEnergy = 100
EnergyModel.TimePerEnergy = 120

function EnergyModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.Energy)
end

function EnergyModel:LateInit()
  self.m_bStartRestore = true
  self:_CheckDefaultValue()
  self:_RestoreEnergy()
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self._RestoreEnergy)
end

function EnergyModel:GetData()
  return self.m_dbTable
end

function EnergyModel.PropertyType2EnergyType(pType)
  if EnergyModel.m_mapPropertyType2EnergyType == nil then
    EnergyModel.m_mapPropertyType2EnergyType = {
      [EPropertyType.Energy] = EnergyType.Main
    }
  end
  return EnergyModel.m_mapPropertyType2EnergyType[pType]
end

function EnergyModel.EnergyType2PropertyType(eType)
  EnergyModel.PropertyType2EnergyType(EPropertyType.Energy)
  for k, v in pairs(EnergyModel.m_mapPropertyType2EnergyType) do
    if v == eType then
      return k
    end
  end
end

function EnergyModel.PropertyType2InfiniteEnergyType(pType)
  if EnergyModel.m_mapPropertyType2InfiniteEnergyType == nil then
    EnergyModel.m_mapPropertyType2InfiniteEnergyType = {}
  end
  return EnergyModel.m_mapPropertyType2InfiniteEnergyType[pType]
end

function EnergyModel.InfiniteEnergyType2PropertyType(iType)
  EnergyModel.PropertyType2InfiniteEnergyType(EPropertyType.EnergyInfiniteTime)
  for k, v in pairs(EnergyModel.m_mapPropertyType2InfiniteEnergyType) do
    if v == iType then
      return k
    end
  end
end

function EnergyModel:GetEnergy(type)
  return self.m_dbTable:GetValue(type, Energy_DBKey) or 0
end

function EnergyModel:SetEnergy(type, energy)
  local prevEnergy = self:GetEnergy(type)
  self:GetData():Set(type, Energy_DBKey, energy)
  EventDispatcher.DispatchEvent(EEventType.EnergyChanged, {type = type, prevEnergy = prevEnergy})
end

function EnergyModel:GetGenerationTime(type)
  return self.m_dbTable:GetValue(type, GenerationTime_DBKey) or 0
end

function EnergyModel:SetGenerationTime(type, time)
  self:GetData():Set(type, GenerationTime_DBKey, time)
end

function EnergyModel:GetEnergyInfiniteTime(type)
  return self.m_dbTable:GetValue(type, InfiniteTime_DBKey)
end

function EnergyModel:AddEnergyInfiniteTime(type, delta)
  if delta <= 0 then
    GM.BIManager:LogErrorInfo(EBIType.AddInfiniteEnergyError, {t = type, d = delta})
    return
  end
  local curTime = math.max(self:GetEnergyInfiniteTime(type) or 0, GM.GameModel:GetServerTime())
  GM.BIManager:LogAction(EBIType.AddInfiniteEnergy, {t = type, d = delta})
  self.m_dbTable:Set(type, InfiniteTime_DBKey, curTime + delta)
end

function EnergyModel:IsEnergyInfinite(type)
  return (self:GetEnergyInfiniteTime(type) or 0) > GM.GameModel:GetServerTime()
end

function EnergyModel:HasEnoughEnergy(type, count)
  return self:IsEnergyInfinite(type) or count <= self:GetEnergy(type)
end

function EnergyModel:ResetEnergy(type)
  self:SetEnergy(type, EnergyModel.MaxEnergy)
end

function EnergyModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function EnergyModel:_CheckDefaultValue()
  for _, type in pairs(EnergyType) do
    if not self.m_dbTable:HasRow(type) then
      self:ResetEnergy(type)
    end
  end
end

function EnergyModel:_GetPropertyType(energyType)
  if energyType == EnergyType.Event then
    return EPropertyType.EventEnergy
  end
  return EPropertyType.Energy
end

function EnergyModel:_RestoreEnergy()
  local curTime = GM.GameModel:GetServerTime()
  for key, type in pairs(EnergyType) do
    local curEnergy = self:GetEnergy(type)
    if curEnergy < EnergyModel.MaxEnergy then
      local recordTime = self:GetGenerationTime(type)
      if recordTime == 0 then
        recordTime = curTime
        self:SetGenerationTime(type, curTime)
      end
      local gapTime = curTime - recordTime
      local addedEnergy = gapTime // EnergyModel.TimePerEnergy
      local newEnergy = curEnergy + math.max(0, addedEnergy)
      newEnergy = math.floor(math.min(newEnergy, EnergyModel.MaxEnergy))
      if addedEnergy < 0 and GameConfig.IsTestMode() then
        GM.UIManager:ShowTestPrompt("体力类型 " .. key .. " 时间回退" .. gapTime .. "秒，体力相关数值修正")
      end
      if addedEnergy ~= 0 then
        self:SetEnergy(type, newEnergy)
        local generationTime = recordTime + addedEnergy * EnergyModel.TimePerEnergy
        self:SetGenerationTime(type, generationTime)
        local msg = {
          type = EnergyModel.EnergyType2PropertyType(type),
          count = newEnergy - curEnergy
        }
        EventDispatcher.DispatchEvent(EEventType.EnergyRestored, msg)
        GM.BIManager:LogAcquire(self:_GetPropertyType(type), newEnergy - curEnergy, EBIType.EnergyRestore, true)
      end
    end
  end
end

function EnergyModel:IsEnergyFull(type)
  return self:GetEnergy(type) >= EnergyModel.MaxEnergy
end

function EnergyModel:GetRestoreOneRestDuration(type)
  return self:GetGenerationTime(type) + EnergyModel.TimePerEnergy - GM.GameModel:GetServerTime()
end

function EnergyModel:GetEnergyFullDuration(type)
  if self:IsEnergyFull(type) then
    return nil
  end
  return self:GetRestoreOneRestDuration(type) + (EnergyModel.MaxEnergy - self:GetEnergy(type) - 1) * EnergyModel.TimePerEnergy
end

function EnergyModel:SubtractEnergy(type, cost, scene)
  if cost < 0 then
    Log.Error("SubtractEnergy cost < 0")
    return false
  end
  if self:IsEnergyInfinite(type) then
    return true, 0
  end
  local curEnergy = self:GetEnergy(type)
  if cost > curEnergy then
    return false
  end
  local newEnergy = curEnergy - cost
  self:SetEnergy(type, newEnergy)
  if curEnergy >= EnergyModel.MaxEnergy and newEnergy < EnergyModel.MaxEnergy then
    self:SetGenerationTime(type, GM.GameModel:GetServerTime())
  end
  if type == EnergyType.Main and scene ~= EBIType.UndoSellItem then
    GM.BIManager:ChangeNumber(EBISyncKey.ConsumedEnergy, cost)
    local orderModel = GM.MainBoardModel:GetOrderModel()
    orderModel:AddOrderGroupConsumeEnergy(cost)
  end
  return true, cost
end

function EnergyModel:AddEnergy(type, value, allowOverfull)
  local curEnergy = self:GetEnergy(type)
  local newEnergy = curEnergy + value
  if not allowOverfull then
    newEnergy = math.min(newEnergy, EnergyModel.MaxEnergy)
  end
  self:SetEnergy(type, newEnergy)
  EventDispatcher.DispatchEvent(EEventType.OnAddEnergy, {
    val = value,
    cur = newEnergy,
    type = type
  })
end

function EnergyModel:FromSyncData(dataArr)
  self.m_dbTable:FromArr(dataArr)
end

function EnergyModel:UpdatePerSecond()
  if not self.m_bStartRestore then
    return
  end
  self:_RestoreEnergy()
end

function EnergyModel:OnLackEnergy(type)
  GM.ShopModel:OpenEnergyWindow(type, true)
end

function EnergyModel.OnEnergyConsumed(itemModel, nCostEnergy, gameMode)
  EventDispatcher.DispatchEvent(EEventType.CosumeEnergy, {
    pos = itemModel:GetPosition(),
    num = nCostEnergy,
    gameMode = gameMode
  })
end
