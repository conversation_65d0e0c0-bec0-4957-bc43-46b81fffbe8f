DiscoveriesCell = {}
DiscoveriesCell.__index = DiscoveriesCell
local white = CSColor.white
local transparent = CSColor(1, 1, 1, 0.4)

function DiscoveriesCell:Init(chainId, chainIndex, itemType, showArrow)
  self.m_chainId = chainId
  self.m_chainIndex = chainIndex
  self.m_type = itemType
  local state = GM.ItemDataModel:GetUnlockState(itemType)
  self.m_boxGo:SetActive(false)
  self.m_icon.gameObject:SetActive(true)
  self.m_icon.color = white
  self:StopBoxAnimation()
  self.m_icon.enabled = false
  self.m_detailBtn.enabled = false
  if state == EItemUnlockState.Unknown then
    SpriteUtil.SetImage(self.m_icon, ImageFileConfigName.detail_future, true)
  elseif state == EItemUnlockState.Locked then
    SpriteUtil.SetImage(self.m_icon, GM.ItemDataModel:GetSpriteName(itemType), true)
    self.m_icon.color = transparent
  else
    if state == EItemUnlockState.Unlocked then
      self.m_button.interactable = true
      self.m_boxGo:SetActive(true)
      self.m_icon.gameObject:SetActive(false)
      self:_TryCreateBoxAnimation()
    else
      self.m_detailBtn.enabled = true
    end
    SpriteUtil.SetImage(self.m_icon, GM.ItemDataModel:GetSpriteName(itemType), true)
  end
  self.m_showArrow = showArrow
  self.m_arrow:SetActive(showArrow)
end

function DiscoveriesCell:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  if self.m_boxTween then
    self.m_boxTween:Kill()
    self.m_boxTween = nil
  end
end

function DiscoveriesCell:_TryCreateBoxAnimation()
  if self.m_boxTween then
    self.m_boxTween:Restart()
    return
  end
  local boxTransform = self.m_boxGo.transform
  local s = DOTween.Sequence()
  s:Append(boxTransform:DOScaleY(0.8, 0.05)):SetEase(Ease.InSine)
  s:Join(boxTransform:DOScaleX(1.2, 0.05)):SetEase(Ease.InSine)
  s:Append(boxTransform:DOScaleY(1.1, 0.2)):SetEase(Ease.OutSine)
  s:Join(boxTransform:DOScaleX(0.9, 0.2)):SetEase(Ease.OutSine)
  s:Append(boxTransform:DOScale(1, 0.2)):SetEase(Ease.InSine)
  s:AppendInterval(1)
  self.m_boxTween = s:SetLoops(-1)
end

function DiscoveriesCell:StopBoxAnimation()
  if self.m_boxTween then
    self.m_boxTween:Pause()
  end
  local boxTransform = self.m_boxGo.transform
  boxTransform:SetLocalPosY(0)
  boxTransform:SetLocalScaleXY(1)
end

function DiscoveriesCell:OnClickDetail()
  ItemDetailWindow.Open(self.m_type, ItemDetailWindowMode.Normal, EItemDetailWindowRefer.Discovery)
end

function DiscoveriesCell:OnClickBox()
  self.m_button.interactable = false
  local rewards = GM.ItemDataModel:ClaimUnlockedRewards(self.m_type)
  RewardApi.AcquireRewardsInView(rewards, {
    arrWorldPos = {
      self.m_icon.transform.position
    },
    noDelayTime = true
  })
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxUnlockGift)
  self.m_boxGo:SetActive(false)
  self.m_icon.gameObject:SetActive(true)
  self.m_detailBtn.enabled = true
  self:StopBoxAnimation()
  local msg = {
    chainId = self.m_chainId,
    chainIndex = self.m_chainIndex
  }
  EventDispatcher.DispatchEvent(EEventType.DiscoveriesUpdate, msg)
end
