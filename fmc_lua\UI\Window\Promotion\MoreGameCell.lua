MoreGameCell = {}
MoreGameCell.__index = MoreGameCell

function MoreGameCell:Init(tbGame)
  self.m_playButtonGo:SetActive(not GM.MoreGameModel:IsMoreNextCell(tbGame.gameId))
  self.m_comingSoonGo:SetActive(GM.MoreGameModel:IsMoreNextCell(tbGame.gameId))
  self.m_gameId = tbGame.gameId
  GM.CrossPromotionModel:LoadUrlImageByCurLanguage(self.m_gameImage, tbGame.picture, tbGame.pictureName .. "_EN_US")
end

function MoreGameCell:OnPlayClick()
  GM.MoreGameModel:Go2NewGame(self.m_gameId)
end
