OrderIcon = {}
OrderIcon.__index = OrderIcon

function OrderIcon:Init(order, outType, count, orderCell)
  self.m_order = order
  self.m_outType = outType
  self.m_count = count
  self.m_orderCell = orderCell
  self.m_type = ItemModelFactory.GetInnerType(outType)
  local useNewTag = GM.ConfigModel:IsServerControlOpen(EGeneralConfType.OrderNewTag)
  self.m_newGo:SetActive(useNewTag and not GM.ItemDataModel:IsUnlocked(self.m_type) and GM.ItemDataModel:IsDishes(self.m_type))
  self.m_tipImage.enabled = not useNewTag and not GM.ItemDataModel:IsUnlocked(self.m_type)
  self.m_normalBgGo:SetActive(true)
  self.m_greenBgGo:SetActive(false)
  self.m_greenBgImg.color = CSColor(1, 1, 1, 0)
  self.m_darkGreenBgImg.color = CSColor(1, 1, 1, 0)
  self.m_greenBgVisible = false
  self.m_lightGreen = false
  self.m_arrIndex = {}
  for i, requirement in ipairs(order:GetRequirements()) do
    if requirement == outType then
      self.m_arrIndex[#self.m_arrIndex + 1] = i
    end
  end
  self.m_arrNeedMaterials = GM.ItemDataModel:GetMaterials(outType, false, false)
  if self.m_arrNeedMaterials then
    EventDispatcher.AddListener(EEventType.ItemCookAddMaterial, self, self._OnItemCookAddMaterial, true)
    EventDispatcher.AddListener(EEventType.ItemViewAdded, self, self._OnItemViewAdded, true)
  end
  EventDispatcher.AddListener(EEventType.FlambeTimeChanged, self, self._OnFlambeTimeChanged, true)
  if not self.m_inited then
    self:_OnFlambeTimeChanged()
    self.m_inited = true
  end
  local imageName = GM.ItemDataModel:GetSpriteName(self.m_type)
  SpriteUtil.SetImage(self.m_iconImage, imageName, true)
  self.m_popTrans.gameObject:SetActive(false)
  self.m_cookingSpine:Init()
  self:_ResetCookingTrans()
  self:_ResetCookedTrans()
end

function OrderIcon:OnDestroy()
  self:_ResetCookingTrans()
  self:_ResetCookedTrans()
  if self.m_popTween then
    self.m_popTween:Kill()
    self.m_popTween = nil
  end
  EventDispatcher.RemoveTarget(self)
end

function OrderIcon:GetType()
  return self.m_type
end

function OrderIcon:_ResetCookingTrans()
  self.m_cookingTrans.gameObject:SetActive(false)
end

function OrderIcon:_ResetCookedTrans()
  if self.m_cookedTween then
    self.m_cookedTween:Kill()
    self.m_cookedTween = nil
  end
  self.m_cookedTrans.gameObject:SetActive(false)
  ItemCookView.ResetTrans(self.m_cookedTrans)
end

function OrderIcon:OnOrderStateChanged(boardModel, codeCountMap, canDeliverOrder, directDishLack)
  local nowCount = codeCountMap[self.m_outType] or 0
  local success, lackCount = OrderStateHelper.TryFillOrderRequirement(boardModel, self.m_outType, self.m_count)
  if not success then
    nowCount = lackCount
  end
  if nowCount > self.m_count then
    nowCount = self.m_count
  end
  local fillRequirement = nowCount >= self.m_count
  self.m_showDishTip = directDishLack[self.m_outType] ~= nil and directDishLack[self.m_outType] > 0
  if fillRequirement then
    self.m_countOutline:SetColor(UIUtil.ConvertHexColor2CSColor("17920A"))
    self:_ResetCookingTrans()
    self:_ResetCookedTrans()
  else
    self.m_countOutline:SetColor(UIUtil.ConvertHexColor2CSColor("703A1C"))
    local fillState = self.m_order:GetRequirementFillStates()
    local showState = ERequirementFillState.Init
    for _, index in ipairs(self.m_arrIndex) do
      if showState < fillState[index] and fillState[index] ~= ERequirementFillState.Filled then
        showState = fillState[index]
      end
    end
    if showState == ERequirementFillState.Cooked then
      if self.m_cookedTween == nil then
        self.m_cookedTween = ItemCookView.Shake(self.m_cookedTrans)
        self.m_cookedTrans.gameObject:SetActive(true)
      end
      self:_ResetCookingTrans()
    elseif showState == ERequirementFillState.Cooking then
      if not self.m_cookingTrans.gameObject.activeSelf then
        self.m_cookingTrans.gameObject:SetActive(true)
        self.m_cookingSpine:PlayAnimation("cook", nil, true)
      end
      self:_ResetCookedTrans()
    else
      self:_ResetCookingTrans()
      self:_ResetCookedTrans()
    end
  end
  self.m_countText.text = nowCount .. "/" .. self.m_count
  self:_ChangeGreenBgGo(fillRequirement, canDeliverOrder)
end

function OrderIcon:OnClicked()
  if self.m_orderCell:GetOrderViewState() == OrderState.Finished then
    return
  end
  if self.m_orderCell:GetOrderViewState() == OrderState.CanDeliver then
    self.m_orderCell:OnGoButtonClicked()
    return
  end
  ItemDetailWindow.Open(self.m_type, ItemDetailWindowMode.Order, EItemDetailWindowRefer.Order)
end

function OrderIcon:_OnItemCookAddMaterial(message)
  local itemCook = message.itemCook
  local materials = itemCook:GetCurMaterialsArray()
  for _, material in ipairs(materials) do
    if not Table.ListContain(self.m_arrNeedMaterials, material) then
      return
    end
  end
  if itemCook:CanCook(self.m_outType) then
    self:_PopTip(ImageFileConfigName.pop_yellow, materials[#materials])
  end
end

function OrderIcon:_OnItemViewAdded(message)
  local item = message.item
  local itemCode = item:GetCode()
  if Table.ListContain(self.m_arrNeedMaterials, itemCode) then
    if not self.m_boardView then
      self.m_boardView = self.m_orderCell.m_orderArea:GetBoardView()
    end
    local _, _, lackDishMaterials = self.m_boardView:CalculateOrderDishsStatus()
    if lackDishMaterials[itemCode] then
      self:_PopTip(ImageFileConfigName.pop_green, itemCode)
    end
  end
end

function OrderIcon:_PopTip(bgImage, popItemCode)
  if not self.m_showDishTip then
    return
  end
  if self.m_popTrans.gameObject.activeSelf then
    return
  end
  self.m_popTrans.gameObject:SetActive(true)
  self.m_popTrans.localScale = V3Zero
  SpriteUtil.SetImage(self.m_bgImage, bgImage)
  local imageName = GM.ItemDataModel:GetSpriteName(popItemCode)
  SpriteUtil.SetImage(self.m_popIconImage, imageName, true)
  local showTween = DOTween.Sequence()
  self.m_popTween = showTween
  showTween:Append(self.m_popTrans:DOScale(1.03, 0.15))
  showTween:Append(self.m_popTrans:DOScale(1.0, 0.07))
  showTween:AppendInterval(0.5)
  showTween:Append(self.m_popTrans:DOScale(0, 0.15))
  showTween:OnComplete(function()
    self.m_popTween = nil
    self.m_popTrans.gameObject:SetActive(false)
  end)
end

function OrderIcon:_ChangeGreenBgGo(visible, lightGreen)
  if self.m_greenBgVisible == visible and self.m_lightGreen == lightGreen then
    return
  end
  self.m_greenBgVisible = visible
  self.m_lightGreen = lightGreen
  self.m_normalBgGo:SetActive(true)
  self.m_greenBgGo:SetActive(true)
  self.m_darkGreenBgGo:SetActive(true)
  self.m_greenBgImg:DOKill()
  self.m_darkGreenBgImg:DOKill()
  self.m_normalBgImg:DOKill()
  if visible then
    if lightGreen then
      self.m_greenBgImg:DOFade(1, 0.2)
      self.m_normalBgImg:DOFade(0, 0.2):OnComplete(function()
        self.m_normalBgGo:SetActive(false)
      end)
      self.m_darkGreenBgImg:DOFade(0, 0.2):OnComplete(function()
        self.m_darkGreenBgGo:SetActive(false)
      end)
    else
      self.m_darkGreenBgImg:DOFade(1, 0.2)
      self.m_normalBgImg:DOFade(0, 0.2):OnComplete(function()
        self.m_normalBgGo:SetActive(false)
      end)
      self.m_greenBgImg:DOFade(0, 0.2):OnComplete(function()
        self.m_greenBgGo:SetActive(false)
      end)
    end
  else
    self.m_greenBgImg:DOFade(0, 0.2):OnComplete(function()
      self.m_greenBgGo:SetActive(false)
    end)
    self.m_darkGreenBgImg:DOFade(0, 0.2):OnComplete(function()
      self.m_darkGreenBgGo:SetActive(false)
    end)
    self.m_normalBgImg:DOFade(1, 0.2)
  end
end

function OrderIcon:_OnFlambeTimeChanged()
  if self.m_order:GetState() == OrderState.Finished then
    return
  end
  local showFire = false
  if GM.FlambeTimeModel:IsOrderShowFire(self.m_order:GetId()) and GM.FlambeTimeModel:GetFlambeTimeType() ~= EFlambeTimeType.link then
    showFire = true
  end
  if self.m_flambeFire.activeSelf and not showFire then
    self.m_flambeFire:SetActive(false)
  elseif not self.m_flambeFire.activeSelf and showFire then
    self.m_flambeFire:SetActive(true)
    self.m_flambeFire.transform.localScale = V3Zero
    self.m_flambeFire.transform:DOScale(1, 0.2)
  end
end

function OrderIcon:HideFire()
  self.m_flambeFire:SetActive(false)
end

function OrderIcon:TryShowFire()
  self:_OnFlambeTimeChanged()
end
