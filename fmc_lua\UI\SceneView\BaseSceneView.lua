BaseSceneView = setmetatable({
  eViewType = EViewType.SceneView
}, BaseView)
BaseSceneView.__index = BaseSceneView
SCENE_BUTTONS_MOVE_DURATION = 0.4

function BaseSceneView:Awake()
  self:FitScreenSize()
end

function BaseSceneView:Init()
  BaseView.Init(self)
  local adjustSizeOffset = ScreenFitter.GetSceneViewSizeOffset()
  UIUtil.AddSizeDelta(self.m_sceneViewHud.transform, 0, -adjustSizeOffset.x - adjustSizeOffset.y)
  UIUtil.SetLocalPosition(self.m_sceneViewHud.transform, 0, -(adjustSizeOffset.x - adjustSizeOffset.y) / 2)
  self.m_tutorialLayer:Init(self)
  self.m_sceneViewHud:Init()
  GM.InAppPurchaseModel:RestorePurchase(false)
  GM.AccountManager:GiveBindRewardIfCould()
  GM.LevelModel:OnSceneViewLoaded()
  GM.GameModel:OnSceneViewLoaded()
  GM.AccountManager:OnSceneViewLoaded()
  GM.SyncModel:OnSceneViewLoaded()
  GM.UpdateHintModel:OnSceneViewLoaded()
  GM.NoticeModel:OnSceneViewLoaded()
  GM.BundleManager:OnSceneViewLoaded()
  self.m_popupChain:OnSceneViewLoaded()
  GM.SurveyModel:TryPopWindow()
end

function BaseSceneView:GetMinFitScale()
  return ScreenFitter.GetFitScale(1, 1)
end

function BaseSceneView:GetBoardInfoBarScreenPosition()
  return PositionUtil.UICameraWorld2Screen(self.m_infoBarRectTrans.position)
end

function BaseSceneView:HudForceImmediatelyShow(arrHudAnchorTypes)
  self.m_sceneViewHud:Show(arrHudAnchorTypes, true)
  self.m_sceneViewHud:LateUpdate()
end

function BaseSceneView:HudForceImmediatelyHide(arrHudAnchorTypes)
  self.m_sceneViewHud:Hide(arrHudAnchorTypes, true)
  self.m_sceneViewHud:LateUpdate()
end

function BaseSceneView:GetHudButton(hudButtonKey)
  return self.m_sceneViewHud:GetHudButton(hudButtonKey)
end

function BaseSceneView:GetHudHighlightRoot()
  return self.m_sceneViewHud:GetHighlightRoot()
end

function BaseSceneView:GetBoardEntryPrefab()
  return self.m_sceneViewHud:GetBoardEntryPrefab()
end

function BaseSceneView:GetBoardEntryAnchor()
  return self.m_sceneViewHud:GetBoardEntryAnchor()
end

function BaseSceneView:GetFlyCachedItemTargetPosition(sameGameMode)
  sameGameMode = sameGameMode ~= false
  if sameGameMode then
    return self:_GetFlyCachedItemTargetPositionInSameGameMode()
  else
    return self:_GetFlyCachedItemTargetPositionInDifferentGameMode()
  end
end

function BaseSceneView:_GetFlyCachedItemTargetPositionInSameGameMode()
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    return self:GetHudButton(SceneViewHud.GetMainBoardHighlightHudKey()):GetFlyTargetPosition() or V3Zero
  else
    local boardView = BoardViewHelper.GetActiveView()
    local originalPosition = boardView and boardView:GetOrderArea():GetBoardCacheRoot():GetFlyTargetPosition()
    if originalPosition then
      return PositionUtil.UICameraScreen2World(boardView:ConvertWorldPositionToScreenPosition(originalPosition))
    else
      return V3Zero
    end
  end
end

function BaseSceneView:_GetFlyCachedItemTargetPositionInDifferentGameMode()
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    return self:GetHudButton(SceneViewHud.GetMainBoardHighlightHudKey()):GetFlyTargetPosition() or V3Zero
  else
    return self:GetHudButton(ESceneViewHudButtonKey.Map):GetFlyTargetPosition() or V3Zero
  end
end

function BaseSceneView:GetTutorialLayer()
  return self.m_tutorialLayer
end

function BaseSceneView:GetPropertyAnimationManager()
  return self.m_propertyAnimationManager
end

function BaseSceneView:GetPopupChain()
  return self.m_popupChain
end

function BaseSceneView:FitScreenSize()
  local scale = self:GetMinFitScale()
  local child
  for k = 0, self.gameObject.transform.childCount - 1 do
    child = self.gameObject.transform:GetChild(k)
    child.localScale = child.localScale * scale
  end
end

function BaseSceneView:OnReturnChapterClicked()
  GM.UIManager:CloseView(UIPrefabConfigName.InventoryWindow)
  GM.SceneManager:ChangeGameMode(EGameMode.Main)
end
