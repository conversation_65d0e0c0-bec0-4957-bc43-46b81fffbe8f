return {
  {
    ChapterId = "Bakery",
    Id = 1,
    Cost = 499,
    <PERSON><PERSON><PERSON> = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cookFloorOld",
        State = 100
      },
      {Slot = "cookFloor", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 2,
    StartConditions = {1},
    Cost = 563,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cookWallPipe",
        State = 100
      },
      {Slot = "cookWall", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 3,
    StartConditions = {2},
    Cost = 531,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cookOldCounter",
        State = 100
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 4,
    StartConditions = {3},
    Cost = 467,
    <PERSON>wards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cookCounter",
        State = 9
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 5,
    StartConditions = {4},
    Cost = 436,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cookCounteritems",
        State = 9
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 6,
    StartConditions = {5},
    Cost = 467,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "cookStove", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 7,
    StartConditions = {6},
    Cost = 467,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cookOldOven",
        State = 100
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 8,
    StartConditions = {7},
    Cost = 525,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "cookWare", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 9,
    StartConditions = {8},
    Cost = 576,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "cookOven", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 10,
    StartConditions = {9},
    Cost = 525,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "cookDough", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 11,
    StartConditions = {10},
    Cost = 576,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cookFreezer",
        State = 9
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 12,
    StartConditions = {11},
    Cost = 525,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cookOldRack",
        State = 100
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 13,
    StartConditions = {12},
    Cost = 525,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "cookRack", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 14,
    StartConditions = {13},
    Cost = 525,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "cookBread", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 15,
    StartConditions = {14},
    Cost = 605,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "cookHood", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 16,
    StartConditions = {15},
    Cost = 605,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "cookPre", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 17,
    StartConditions = {16},
    Cost = 553,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cookPreitems",
        State = 9
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 18,
    StartConditions = {17},
    Cost = 708,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "sellFloorOld",
        State = 100
      },
      {Slot = "sellFloor", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 19,
    StartConditions = {18},
    Cost = 502,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "sellOldWallM",
        State = 100
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 20,
    StartConditions = {19},
    Cost = 553,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "sellCoffee", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 21,
    StartConditions = {20},
    Cost = 605,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "sellCounterM",
        State = 9
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 22,
    StartConditions = {21},
    Cost = 576,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "sellMenu", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 23,
    StartConditions = {22},
    Cost = 576,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "sellOldTeller",
        State = 100
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 24,
    StartConditions = {23},
    Cost = 619,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "sellTableM", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 25,
    StartConditions = {24},
    Cost = 619,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "sellTeller", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 26,
    StartConditions = {25},
    Cost = 661,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "sellTrophy", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 27,
    StartConditions = {26},
    Cost = 704,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "sellCounterL",
        State = 9
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 28,
    StartConditions = {27},
    Cost = 747,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "sellBread", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 29,
    StartConditions = {28},
    Cost = 619,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "sellWallR", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 30,
    StartConditions = {29},
    Cost = 739,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "sellTableR", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 31,
    StartConditions = {30},
    Cost = 627,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outFloor", State = 9},
      {
        Slot = "outFloorOld",
        State = 100
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 32,
    StartConditions = {31},
    Cost = 796,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "outOldWallL",
        State = 100
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 33,
    StartConditions = {32},
    Cost = 739,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outWallL", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 34,
    StartConditions = {33},
    Cost = 683,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outCoverL", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 35,
    StartConditions = {34},
    Cost = 627,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outTableL", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 36,
    StartConditions = {35},
    Cost = 627,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outChairL", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 37,
    StartConditions = {36},
    Cost = 683,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outPlantL", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 38,
    StartConditions = {37},
    Cost = 832,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "outOldWallR",
        State = 100
      }
    }
  },
  {
    ChapterId = "Bakery",
    Id = 39,
    StartConditions = {38},
    Cost = 774,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outWallR", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 40,
    StartConditions = {39},
    Cost = 715,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outCoverR", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 41,
    StartConditions = {40},
    Cost = 657,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outTableR", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 42,
    StartConditions = {41},
    Cost = 715,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outChairR", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 43,
    StartConditions = {42},
    Cost = 715,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outPlantR", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 44,
    StartConditions = {43},
    Cost = 657,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outDoor", State = 9}
    }
  },
  {
    ChapterId = "Bakery",
    Id = 45,
    StartConditions = {44},
    Cost = 774,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outLed", State = 9}
    }
  }
}
