CDNResourceManager = {}
CDNResourceManager.__index = CDNResourceManager
local CatalogueFileKey = "DataConfig/catalogue.json"
local StrGameTextPrefix = "game_text_"
local DBColumnValue = "value"
local BYTE_FILE_SUFFIX = ".bytes"
ECDNDataKey = {
  DownloadedFileVersion = "Version",
  DownloadedFileMd5 = "Md5",
  DownloadedFileArr = "DFileArr"
}
local ConfigFilePrefix = {
  "Data/Config/",
  "DynamicConfig"
}

function CDNResourceManager:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.CDN)
  self.m_arrDownloadedFile = {}
  self.m_strLocalCatalogueMd5 = nil
  local strDownloadedFile = self.m_dbTable:GetValue(ECDNDataKey.DownloadedFileArr, DBColumnValue)
  if not StringUtil.IsNilOrEmpty(strDownloadedFile) then
    local jsonData = json.decode(strDownloadedFile)
    if jsonData then
      for k = 1, #jsonData do
        self.m_arrDownloadedFile[#self.m_arrDownloadedFile + 1] = jsonData[k]
      end
    end
  end
end

function CDNResourceManager:OnLoginSuccess(strCatalogue, strCatalogueMd5, bForceDownload)
  self.m_strCDNCatalogue = strCatalogue
  self.m_strCDNCatalogueMd5 = strCatalogueMd5
  self.m_bForceDownload = bForceDownload
end

function CDNResourceManager:IsNeedRestartGame()
  return self.m_bForceDownload and not StringUtil.IsNilOrEmpty(self.m_strCDNCatalogueMd5) and not StringUtil.IsNilOrEmpty(self.m_strCDNCatalogue) and (self.m_strCDNCatalogueMd5 ~= self.m_strLocalCatalogueMd5 or self.m_bOnlyGameText)
end

function CDNResourceManager:TryDownloadAllLatestFiles(callback, isLoading)
  self.m_arrPendingDownloaders = {}
  self.m_funcDownloadAllCallback = nil
  self.m_bDownloadError = false
  self.m_errorMsg = nil
  self.m_mapCDNCatalogue = {}
  self.m_arrConfigDownloaders = {}
  self.m_gameTextDownloader = nil
  if StringUtil.IsNilOrEmpty(self.m_strCDNCatalogue) or StringUtil.IsNilOrEmpty(self.m_strCDNCatalogueMd5) then
    self:_TryDeleteDownloadedFile({})
    callback(true)
    return
  end
  
  function self.m_funcDownloadAllCallback(bSuccess, tbData)
    self.m_funcDownloadAllCallback = nil
    callback(bSuccess, tbData)
  end
  
  self:_LoadCatalogue(false, isLoading)
end

function CDNResourceManager:TryDownloadLatestGameText(isLoading)
  if StringUtil.IsNilOrEmpty(self.m_strCDNCatalogue) or StringUtil.IsNilOrEmpty(self.m_strCDNCatalogueMd5) or self.m_bCatalogueLoading then
    return
  end
  if self.m_strLocalCatalogueMd5 ~= self.m_strCDNCatalogueMd5 then
    self.m_gameTextDownloader = nil
    self:_LoadCatalogue(true, isLoading)
    self.m_bOnlyGameText = true
  elseif self.m_gameTextDownloader and self.m_gameTextDownloader:CanSend() then
    self.m_gameTextDownloader.bIsLoading = isLoading
    self.m_gameTextDownloader:StartDownload()
  end
end

function CDNResourceManager:SyncVersionDatas2LuaManager()
  local csVersionDatas = LuaManager:ResetCdnConfigFileVersionMap()
  local matchPrefixList = {}
  for idx, filePrefix in ipairs(ConfigFilePrefix) do
    matchPrefixList[idx] = ECDNDataKey.DownloadedFileVersion .. filePrefix
  end
  local curVersion = GameConfig.GetCurrentVersion()
  for k, data in pairs(self.m_dbTable:GetValues()) do
    if data.value == curVersion then
      for _, matchPrefix in ipairs(matchPrefixList) do
        if StringUtil.StartWith(k, matchPrefix) then
          csVersionDatas:Add(StringUtil.Replace(k, ECDNDataKey.DownloadedFileVersion, ""), true)
          break
        end
      end
    end
  end
end

function CDNResourceManager:_LoadCatalogue(bOnlyDownloadGameText, isLoading)
  Log.Assert(not self.m_bCatalogueLoading, "catalogue loading")
  local funcDownloadCatalogueCallback = function(bSuccess, downloader, errorMsg, fileContent)
    self.m_bCatalogueLoading = false
    if bSuccess then
      local strFilePath = self.GetCdnPath(CatalogueFileKey)
      CDNWriter.WriteFileBytesAsync(strFilePath, fileContent, function(bSuccess)
        if bSuccess then
          self.m_dbTable:Set(ECDNDataKey.DownloadedFileVersion .. CatalogueFileKey, DBColumnValue, GameConfig.GetCurrentVersion())
        end
      end)
      self:_ParseCatalogue(fileContent, bOnlyDownloadGameText, isLoading)
    elseif self.m_funcDownloadAllCallback then
      self.m_funcDownloadAllCallback(false, errorMsg)
    end
  end
  local funcDownloadCatalogue = function()
    local strUrl = NetworkConfig.GetCdnFileUrl(self.m_strCDNCatalogue)
    local downloader = CDNDownloader.Create(strUrl, nil, funcDownloadCatalogueCallback)
    downloader.bIsLoading = isLoading
    downloader:StartDownload()
    Log.Info("CDNResourceManager Start Download Catalogue")
  end
  self.m_bCatalogueLoading = true
  if self.m_strLocalCatalogueMd5 == nil and self.IsCacheFileExist(CatalogueFileKey) and self:IsFileVersionValid(CatalogueFileKey) then
    local text = GM.ResourceLoader:LoadCDNFile(CatalogueFileKey)
    if not StringUtil.IsNilOrEmpty(text) and self.m_strCDNCatalogueMd5 == MD5.Encode(text) then
      self.m_bCatalogueLoading = false
      self:_ParseCatalogue(text, bOnlyDownloadGameText, isLoading)
    else
      funcDownloadCatalogue()
    end
  else
    funcDownloadCatalogue()
  end
end

function CDNResourceManager:_ParseCatalogue(fileContent, bOnlyDownloadGameText, isLoading)
  self.m_mapCDNCatalogue = json.decode(fileContent) or {}
  self.m_strLocalCatalogueMd5 = self.m_strCDNCatalogueMd5
  local funcDownloadCallback = function(bSuccess, downloader, errorMsg)
    self:_OnFileDownloaded(bSuccess, downloader, errorMsg)
    self:_DownloadPendingFiles()
  end
  local strUrl, strSavePath
  local curTextShortName = LocalizationModel:GetTextShortName()
  local curFileList = {}
  for k, v in pairs(self.m_mapCDNCatalogue) do
    curFileList[#curFileList + 1] = k
    for _, contentKey in ipairs(v.content) do
      curFileList[#curFileList + 1] = contentKey
    end
    if self:_CheckLegacyFileInvalid(k, v.hash, v.content) then
      if self:_IsGameTextFile(v.content) or not bOnlyDownloadGameText and string.find(k, StrGameTextPrefix) == nil then
        strUrl = NetworkConfig.GetCdnFileUrl(v.hash)
        strSavePath = self.GetCdnPath(k)
        local downloader = CDNDownloader.Create(strUrl, strSavePath, funcDownloadCallback)
        downloader.strFileKey = k
        downloader.strFileMd5 = v.hash
        downloader.strFileContent = v.content
        downloader.bIsLoading = isLoading
        if self:_IsGameTextFile(v.content) then
          self.m_gameTextDownloader = downloader
        else
          table.insert(self.m_arrConfigDownloaders, downloader)
          table.insert(self.m_arrPendingDownloaders, downloader)
        end
        Log.Info("CDNResourceManager Start Download File " .. k)
      end
    elseif not self:IsFileVersionValid(k) then
      self:_SaveFileDownloadedInfo(k, v.hash, v.content)
      if self:_IsGameTextFile(v.content) then
        GM.GameTextModel:LoadCdnText()
      end
    end
  end
  if self.m_gameTextDownloader ~= nil then
    table.insert(self.m_arrPendingDownloaders, self.m_gameTextDownloader)
  end
  self:_DownloadPendingFiles()
  if #self.m_arrConfigDownloaders == 0 then
    self:_OnAllFileDownloadFinished()
  end
  self:_TryDeleteDownloadedFile(curFileList)
end

function CDNResourceManager:_IsGameTextFile(contentList)
  local curTextShortName = LocalizationModel:GetTextShortName()
  for _, contentKey in ipairs(contentList) do
    if self.GetFileNameByFileKey(contentKey) == curTextShortName then
      return true
    end
  end
  return false
end

function CDNResourceManager:_CheckLegacyFileInvalid(fileKey, hash, contentList)
  if not self.IsCacheFileExist(fileKey) or hash ~= self.m_dbTable:GetValue(ECDNDataKey.DownloadedFileMd5 .. fileKey, DBColumnValue) then
    return true
  end
  contentList = contentList or {}
  for _, contentKey in ipairs(contentList) do
    if self:_CheckLegacyFileInvalid(contentKey, hash) then
      return true
    end
  end
  return false
end

function CDNResourceManager:_DeleteDownloadedFileByIndex(index)
  if index > #self.m_arrDownloadedFile then
    return
  end
  local strFileKey = self.m_arrDownloadedFile[index]
  table.remove(self.m_arrDownloadedFile, index)
  self.m_dbTable:Remove(ECDNDataKey.DownloadedFileMd5 .. strFileKey)
  self.m_dbTable:Remove(ECDNDataKey.DownloadedFileVersion .. strFileKey)
  self.m_dbTable:Set(ECDNDataKey.DownloadedFileArr, DBColumnValue, json.encode(self.m_arrDownloadedFile))
  local fullPath = self.GetCdnPath(strFileKey)
  if File.Exists(fullPath) then
    File.Delete(fullPath)
  end
  local fileName, extension = self.GetFileNameByFileKey(strFileKey)
  if fileName == LocalizationModel:GetTextShortName() then
    GM.GameTextModel:ReloadLocalText()
  end
  if GameConfig.IsTestMode() and StringUtil.StartWith(strFileKey, "Data/") and StringUtil.EndWith(strFileKey, BYTE_FILE_SUFFIX) then
    local rawDataFileKey = "DataRaw" .. string.sub(strFileKey, 5)
    rawDataFileKey = StringUtil.Replace(rawDataFileKey, BYTE_FILE_SUFFIX, "")
    local rawDataPath = StringUtil.Replace(fullPath, strFileKey, rawDataFileKey)
    if File.Exists(rawDataPath) then
      File.Delete(rawDataPath)
    end
    self.m_dbTable:Remove(ECDNDataKey.DownloadedFileVersion .. rawDataFileKey)
  end
end

function CDNResourceManager:_TryDeleteDownloadedFile(curFileList)
  local strFileKey
  for k = #self.m_arrDownloadedFile, 1, -1 do
    strFileKey = self.m_arrDownloadedFile[k]
    if not Table.ListContain(curFileList, strFileKey) then
      self:_DeleteDownloadedFileByIndex(k)
    end
  end
end

function CDNResourceManager:_SaveFileDownloadedInfo(fileKey, hash, contentList)
  local setValueTable = {}
  setValueTable[ECDNDataKey.DownloadedFileVersion .. fileKey] = {
    [DBColumnValue] = GameConfig.GetCurrentVersion()
  }
  setValueTable[ECDNDataKey.DownloadedFileMd5 .. fileKey] = {
    [DBColumnValue] = hash
  }
  if not Table.ListContain(self.m_arrDownloadedFile, fileKey) then
    self.m_arrDownloadedFile[#self.m_arrDownloadedFile + 1] = fileKey
  end
  contentList = contentList or {}
  for _, contentKey in ipairs(contentList) do
    setValueTable[ECDNDataKey.DownloadedFileVersion .. contentKey] = {
      [DBColumnValue] = GameConfig.GetCurrentVersion()
    }
    setValueTable[ECDNDataKey.DownloadedFileMd5 .. contentKey] = {
      [DBColumnValue] = hash
    }
    if not Table.ListContain(self.m_arrDownloadedFile, contentKey) then
      self.m_arrDownloadedFile[#self.m_arrDownloadedFile + 1] = contentKey
    end
  end
  setValueTable[ECDNDataKey.DownloadedFileArr] = {
    [DBColumnValue] = json.encode(self.m_arrDownloadedFile)
  }
  self.m_dbTable:BatchSet(setValueTable)
end

function CDNResourceManager:_OnFileDownloaded(bSuccess, downloader, errorMsg)
  if bSuccess then
    self:_SaveFileDownloadedInfo(downloader.strFileKey, downloader.strFileMd5, downloader.strFileContent)
    for _, innerFile in ipairs(downloader.strFileContent) do
      if GameConfig.IsTestMode() and StringUtil.StartWith(innerFile, "Data/") and StringUtil.EndWith(innerFile, BYTE_FILE_SUFFIX) then
        local rawDataFileKey = "DataRaw" .. string.sub(innerFile, 5)
        rawDataFileKey = StringUtil.Replace(rawDataFileKey, BYTE_FILE_SUFFIX, "")
        local strSavePath = self.GetCdnPath(innerFile)
        local rawDataSavePath = StringUtil.Replace(strSavePath, innerFile, rawDataFileKey)
        local fileInfo = CS.System.IO.FileInfo(rawDataSavePath)
        if not fileInfo.Directory.Exists then
          Directory.CreateDirectory(fileInfo.DirectoryName)
        end
        local file = io.open(strSavePath, "rb")
        local content = file:read("*all")
        file:close()
        if content then
          CS.Crypt.CryptFile(strSavePath, rawDataSavePath)
          self.m_dbTable:Set(ECDNDataKey.DownloadedFileVersion .. rawDataFileKey, DBColumnValue, GameConfig.GetCurrentVersion())
        end
      end
    end
  else
    local index = Table.ListContain(self.m_arrDownloadedFile, downloader.strFileKey)
    if index then
      self:_DeleteDownloadedFileByIndex(index)
    end
    for _, contentKey in ipairs(downloader.strFileContent) do
      index = Table.ListContain(self.m_arrDownloadedFile, contentKey)
      if index then
        self:_DeleteDownloadedFileByIndex(index)
      end
    end
    if downloader ~= self.m_gameTextDownloader or self.m_bForceDownload then
      self.m_bDownloadError = true
      self.m_errorMsg = self.m_errorMsg and self.m_errorMsg .. "," .. errorMsg or errorMsg
    end
  end
  if downloader == self.m_gameTextDownloader then
    self:_OnGameTextDownloaded(bSuccess)
  else
    for k, v in ipairs(self.m_arrConfigDownloaders) do
      if v == downloader then
        table.remove(self.m_arrConfigDownloaders, k)
        break
      end
    end
    if #self.m_arrConfigDownloaders == 0 then
      self:_OnAllFileDownloadFinished()
    end
  end
end

function CDNResourceManager:_CanCallDownloadAllCallback()
  if self.m_bForceDownload then
    return #self.m_arrConfigDownloaders == 0 and self.m_gameTextDownloader == nil
  else
    return #self.m_arrConfigDownloaders == 0
  end
end

function CDNResourceManager:_OnGameTextDownloaded(bSuccess)
  if bSuccess then
    self.m_gameTextDownloader = nil
    GM.GameTextModel:LoadCdnText()
  elseif self.m_bForceDownload then
    self.m_gameTextDownloader = nil
  end
  self:_OnAllFileDownloadFinished()
end

function CDNResourceManager:_OnAllFileDownloadFinished()
  if self:_CanCallDownloadAllCallback() and self.m_funcDownloadAllCallback ~= nil then
    self.m_funcDownloadAllCallback(not self.m_bDownloadError, self.m_errorMsg)
  end
end

function CDNResourceManager:IsDownloadingDownloader(downloader)
  if self.m_gameTextDownloader == downloader then
    return true
  end
  for _, v in ipairs(self.m_arrConfigDownloaders) do
    if v == downloader then
      return true
    end
  end
  return false
end

function CDNResourceManager.GetFileNameByFileKey(strFileKey)
  local strFileName = string.sub(strFileKey, (StringUtil.rFindChar(strFileKey, "/") or 0) + 1)
  local dotIndex = string.find(strFileName, "%.") or string.len(strFileName) + 1
  local shortName = string.sub(strFileName, 1, dotIndex - 1)
  local extension = string.sub(strFileName, dotIndex + 1)
  return shortName, extension
end

function CDNResourceManager.GetCdnPath(fileKey)
  return Path.Combine(FileUtils.CdnPath, fileKey)
end

function CDNResourceManager.IsCacheFileExist(fileKey)
  local fullPath = CDNResourceManager.GetCdnPath(fileKey)
  if File.Exists(fullPath) then
    return true
  end
  return false
end

function CDNResourceManager:IsFileVersionValid(strFileKey)
  return self.m_dbTable:GetValue(ECDNDataKey.DownloadedFileVersion .. strFileKey, DBColumnValue) == GameConfig.GetCurrentVersion()
end

function CDNResourceManager:_DownloadPendingFiles()
  if #self.m_arrPendingDownloaders > 0 then
    local downloader = self.m_arrPendingDownloaders[1]
    table.remove(self.m_arrPendingDownloaders, 1)
    downloader:StartDownload()
  end
end
