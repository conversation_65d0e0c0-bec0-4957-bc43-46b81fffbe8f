ShopContainerPassActivity = setmetatable({}, BaseShopContainer)
ShopContainerPassActivity.__index = ShopContainerPassActivity

function ShopContainerPassActivity:Awake()
  self.m_shopType = EShopType.PassActivityTicket
  self.m_shopCellCount = 0
  self.gameObject:SetActive(false)
  for activityType, activityDefinition in pairs(PassActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, function()
      self:_OnPassActivityStateChanged(activityType)
    end)
    EventDispatcher.AddListener(activityDefinition.BuyTicketSuccessEvent, self, function()
      self:_OnPassActivityStateChanged(activityType)
    end)
    self:_OnPassActivityStateChanged(activityType)
  end
end

function ShopContainerPassActivity:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function ShopContainerPassActivity:GetContentHeight()
  return self.gameObject.activeSelf and self.transform.sizeDelta.y or 0
end

function ShopContainerPassActivity:_GetCellData()
  return {}
end

function ShopContainerPassActivity:_OnCellClicked()
end

function ShopContainerPassActivity:_OnPassActivityStateChanged(activityType)
  local activityDefinition = PassActivityDefinition[activityType]
  local model = GM.ActivityManager:GetModel(activityType)
  local cellName = "m_" .. activityType .. "ShopCell"
  if model:GetState() == ActivityState.Started and (not model:HasTicket() or model:CanBuyMaxTicket() and not model:HasMaxTicket()) then
    self.gameObject:SetActive(true)
    if self[cellName] == nil then
      local config = GM.DataResource.UIPrefabConfig:GetConfig(activityDefinition.ShopCellPrefabName, activityType)
      local callback = function(gameObject)
        local cell = gameObject:GetLuaTable()
        cell:Init(activityType)
        self[cellName] = cell
      end
      self.m_shopCellCount = self.m_shopCellCount + 1
      GM.ResourceLoader:LoadPrefab(config, self.transform, V3Zero, callback)
    end
  else
    if self[cellName] ~= nil then
      self.m_shopCellCount = self.m_shopCellCount - 1
      Object.Destroy(self[cellName].gameObject)
      self[cellName] = nil
    end
    if self.m_shopCellCount == 0 then
      self.gameObject:SetActive(false)
    end
  end
  EventDispatcher.DispatchEvent(EEventType.ShopRefreshed, {
    shopType = EShopType.PassActivityTicket
  })
end
