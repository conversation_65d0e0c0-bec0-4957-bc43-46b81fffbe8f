local formerRequire = require
local requireErrorHandler = function(msg)
  Debug.LogError("[xpcall]require failed:" .. (msg or "nil") .. "\n" .. debug.traceback())
end

function require(path)
  local replacedRequirePath = path
  if GM and GM.ConfigModel then
    replacedRequirePath = GM.ConfigModel:GetReplacesFilePath(path)
  end
  local result, ret = xpcall(formerRequire, requireErrorHandler, replacedRequirePath)
  if result then
    return ret
  else
    return formerRequire(path)
  end
end

require("MainInclude")
math.randomseed(os.time())

function LuaUpdate(dt)
  if GM.destroying then
    return
  end
  GM:Update(dt)
  Scheduler.Update(dt)
  if Input.GetKeyUp(KeyCode.Escape) then
    OnBack()
  end
end

function LuaLateUpdate()
  if GM.destroying then
    return
  end
  GM:LateUpdate()
end

function LuaOnApplicationFocus(bHasFocus)
  GM:ApplicationFocusChanged(bHasFocus)
  if DeviceInfo.IsUnityEditor() then
    LuaOnApplicationPause(not bHasFocus)
  end
end

function LuaOnApplicationPause(bPaused)
  if bPaused then
    GM:ApplicationDidEnterBackground()
  else
    GM:ApplicationWillEnterForeground()
  end
end

function LuaDestroy()
  GM:Destroy()
  GM = nil
  EventDispatcher.Clear()
  Scheduler.Clear()
end

function CreateLuaComponentByName(gameObject, typename)
  if not _ENV[typename] then
    return nil
  end
  local newComponent = setmetatable({}, _ENV[typename])
  newComponent.gameObject = gameObject
  newComponent.transform = gameObject.transform
  return newComponent
end

function Awake()
  GM = GlobalManager.Create()
  GM:Init()
  CSFirebaseManager:CrashlyticsSetKeyValue("version", GameConfig.GetCurrentVersion())
  CSFirebaseManager:CrashlyticsSetKeyValue("mode", GameConfig.IsTestMode() and "test" or "prod")
  CSFirebaseManager:SetUserProperty("idfa", DeviceInfo.GetAdvertisingIdentifier())
end

function OnBack()
  if GM.UIManager then
    GM.UIManager:OnBack()
  end
end
