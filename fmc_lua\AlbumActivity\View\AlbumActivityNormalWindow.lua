AlbumActivityNoticeWindow = setmetatable({}, AlbumActivityBaseWindow)
AlbumActivityNoticeWindow.__index = AlbumActivityNoticeWindow

function AlbumActivityNoticeWindow:Init(userClick, activityType)
  AlbumActivityBaseWindow.Init(self, activityType)
  self.m_model:SetWindowOpened()
  self:UpdatePerSecond()
  self:LogWindowAction(EBIType.UIActionType.Open, userClick and EBIReferType.UserClick or EBIReferType.AutoPopup)
end

function AlbumActivityNoticeWindow:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  if self.m_model:GetState() ~= ActivityState.Preparing then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  else
    self:Close()
  end
end

AlbumActivityStartWindow = setmetatable({}, AlbumActivityBaseWindow)
AlbumActivityStartWindow.__index = AlbumActivityStartWindow

function AlbumActivityStartWindow:Init(activityType)
  AlbumActivityBaseWindow.Init(self, activityType, false)
  self:UpdatePerSecond()
  if self.m_model:GetState() == ActivityState.Started then
    self.m_model:SetWindowOpened()
  end
end

function AlbumActivityStartWindow:UpdatePerSecond()
  if self.m_model ~= nil and self.m_model:GetState() == ActivityState.Started then
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime())
  end
end

function AlbumActivityStartWindow:OnBtnClicked()
  self:Close()
  GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, true)
  EventDispatcher.DispatchEvent(EEventType.AlbumStartButtonClick)
end

function AlbumActivityStartWindow:GetButtonRect()
  return self.m_button.transform
end

AlbumActivityEndWindow = setmetatable({}, AlbumActivityBaseWindow)
AlbumActivityEndWindow.__index = AlbumActivityEndWindow

function AlbumActivityEndWindow:Init(activityType)
  AlbumActivityBaseWindow.Init(self, activityType, false)
  self.m_model:SetWindowOpened(ActivityState.Ended)
end
