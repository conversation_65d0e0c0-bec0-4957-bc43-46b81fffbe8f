GemHudButton = setmetatable({
  HitAudio = AudioFileConfigName.SfxGemCrush
}, HudPropertyButton)
GemHudButton.__index = GemHudButton

function GemHudButton:Awake()
  HudPropertyButton.Awake(self)
  HudPropertyButton.Init(self, EPropertyType.Gem)
end

function GemHudButton:OnClicked()
  local shopWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.ShopWindow)
  if shopWindow then
    shopWindow:LocateShopType(EShopType.Diamonds)
    return
  end
  GM.UIManager:OpenView(UIPrefabConfigName.ShopWindow, EShopType.Diamonds)
end
