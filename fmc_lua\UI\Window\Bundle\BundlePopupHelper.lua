BundlePopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true,
    [EPopupScene.Main] = true
  }
}, BasePopupHelper)
BundlePopupHelper.__index = BundlePopupHelper

function BundlePopupHelper.Create()
  local helper = setmetatable({}, BundlePopupHelper)
  helper:Init()
  return helper
end

function BundlePopupHelper:Init()
  BasePopupHelper.Init(self)
  self.m_waitingPopUpWindow = {}
  EventDispatcher.AddListener(EEventType.AddBundleWindowToPopupChain, self, self._AddWindowToPopupChain)
  EventDispatcher.AddListener(EEventType.RemoveBundleWindowFromPopupChain, self, self._RemoveWindowFromPopupChain)
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnViewOpen)
end

function BundlePopupHelper:NeedCheckPopup()
  return not Table.IsEmpty(self.m_waitingPopUpWindow)
end

function BundlePopupHelper:_AddWindowToPopupChain(msg)
  if not Table.IsEmpty(msg) then
    if msg.notRepeat then
      if not GM.UIManager:IsViewExisting(msg.name) then
        local isRepeatWindow = false
        for k, v in ipairs(self.m_waitingPopUpWindow) do
          if v.name == msg.name then
            self.m_waitingPopUpWindow[k] = msg
            isRepeatWindow = true
            break
          end
        end
        if not isRepeatWindow then
          table.insert(self.m_waitingPopUpWindow, msg)
        end
      end
    else
      table.insert(self.m_waitingPopUpWindow, msg)
    end
  end
end

function BundlePopupHelper:_RemoveWindowFromPopupChain(msg)
  for i = #self.m_waitingPopUpWindow, 1, -1 do
    if self.m_waitingPopUpWindow[i].name == msg.name then
      table.remove(self.m_waitingPopUpWindow, i)
    end
  end
end

function BundlePopupHelper:_OnViewOpen(msg)
  local windowName = msg and msg.name or nil
  if windowName then
    for i, v in ipairs(self.m_waitingPopUpWindow) do
      if v.notRepeat and v.name == windowName then
        table.remove(self.m_waitingPopUpWindow, i)
        break
      end
    end
  end
end

function BundlePopupHelper:CheckPopup()
  if not Table.IsEmpty(self.m_waitingPopUpWindow) then
    for i, v in ipairs(self.m_waitingPopUpWindow) do
      if v.popScene and v.popScene ~= GM.SceneManager:GetGameMode() then
      else
        local toPopWindow = table.remove(self.m_waitingPopUpWindow, i)
        return toPopWindow.name, toPopWindow.args
      end
    end
  end
end

function BundlePopupHelper:IsViewInChain(viewName)
  if not Table.IsEmpty(self.m_waitingPopUpWindow) then
    for i, v in ipairs(self.m_waitingPopUpWindow) do
      if v.name == viewName then
        return true
      end
    end
  end
  return false
end

function BundlePopupHelper.IsViewExisting(viewName)
  local baseSceneView = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BaseSceneView)
  local popupChain = baseSceneView and baseSceneView:GetPopupChain()
  local bundlePopupHelper = popupChain and popupChain:GetHelper(EPopupHelper.Bundle)
  return bundlePopupHelper ~= nil and bundlePopupHelper:IsViewInChain(viewName)
end

function BundlePopupHelper.RemoveWindowFromPopupChain(windowName)
  EventDispatcher.DispatchEvent(EEventType.RemoveBundleWindowFromPopupChain, {name = windowName})
end

function BundlePopupHelper.AddWindowToPopupChain(windowName, ...)
  EventDispatcher.DispatchEvent(EEventType.AddBundleWindowToPopupChain, {
    name = windowName,
    args = {
      ...
    }
  })
end

function BundlePopupHelper.AddWindowToPopupChainWithoutRepeat(windowName, ...)
  EventDispatcher.DispatchEvent(EEventType.AddBundleWindowToPopupChain, {
    name = windowName,
    notRepeat = true,
    args = {
      ...
    }
  })
end
