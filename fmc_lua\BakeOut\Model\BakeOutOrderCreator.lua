BakeOutOrderCreator = {}
local MAX_ORDER_COUNT = 5

function BakeOutOrderCreator.Create(orderModel, arrCurrentOrders)
  local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  local eventId = bakeOutModel:GetId()
  local incremNum = bakeOutModel:GetLastIncremNum()
  local needCount = MAX_ORDER_COUNT - #arrCurrentOrders
  local avatarIds = orderModel:GetAvatarIds(needCount, OrderType.BakeOut)
  local mapCurrentDishes = {}
  for _, currentOrder in ipairs(arrCurrentOrders) do
    mapCurrentDishes[currentOrder:GetRequirements()[1]] = true
  end
  if GameConfig.IsTestMode() then
    TestAccumulatedLogInfo.Start()
  end
  local arrDishWeights, mapExtraWeight, mapExtraWeightStr = BakeOutOrderCreator._CreateDishWeights(orderModel, mapCurrentDishes)
  local arrNewOrders = {}
  for i = 1, needCount do
    incremNum = incremNum + 1
    local newOrder = BakeOutOrderCreator._Create(orderModel, arrDishWeights, mapExtraWeight, mapExtraWeightStr, eventId, incremNum, avatarIds[i])
    arrNewOrders[#arrNewOrders + 1] = newOrder
    bakeOutModel:SetLastIncremNum(incremNum)
  end
  return arrNewOrders
end

function BakeOutOrderCreator._CreateDishWeights(orderModel, mapCurrentDishes)
  local codeCountMap = GM.MainBoardModel:GetCodeCountMap(true, false, true)
  local mapCodeScore = {}
  local itemDataModel = GM.ItemDataModel
  for itemCode, count in pairs(codeCountMap) do
    mapCodeScore[itemCode] = (mapCodeScore[itemCode] or 0) + itemDataModel:GetItemScore(itemCode) * count
  end
  local mapRandomDishes = orderModel.dataModel:GetRandomDishMap()
  local arrDishWeights = {}
  local mapExtraWeight = {}
  local mapExtraWeightStr = {}
  local index = 0
  for dishType, _ in pairs(mapRandomDishes) do
    if not mapCurrentDishes[dishType] then
      index = index + 1
      local extraWeight = 0
      local strExtraWeight = ""
      local arrAllMaterials = itemDataModel:GetAllMaterials(dishType)
      local mapChainLevel = {}
      for _, materialType in ipairs(arrAllMaterials) do
        local itemConfig = itemDataModel:GetModelConfig(materialType)
        local chainId = itemConfig.ChainId
        local chainLevel = itemConfig.ChainLevel
        if chainLevel > (mapChainLevel[chainId] or 0) then
          mapChainLevel[chainId] = chainLevel
        end
      end
      for chainId, chainLevel in pairs(mapChainLevel) do
        local itemType
        for i = 1, chainLevel do
          itemType = chainId .. "_" .. i
          if mapCodeScore[itemType] then
            extraWeight = extraWeight + mapCodeScore[itemType]
            strExtraWeight = strExtraWeight .. itemType .. "=" .. mapCodeScore[itemType] .. " "
          end
        end
      end
      arrDishWeights[index] = {
        Code = dishType,
        Weight = 20 + extraWeight
      }
      if strExtraWeight ~= "" then
        mapExtraWeightStr[dishType] = strExtraWeight
        mapExtraWeight[dishType] = extraWeight
      end
    end
  end
  return arrDishWeights, mapExtraWeight, mapExtraWeightStr
end

function BakeOutOrderCreator._Create(orderModel, arrDishWeights, mapExtraWeight, mapExtraWeightStr, eventId, incremNum, avatarId)
  local orderId = "bakeOut_" .. eventId .. "_" .. incremNum
  local arrRequirements = {}
  if arrDishWeights and arrDishWeights[1] then
    local selected, index = Table.ListWeightSelectOne(arrDishWeights)
    if selected then
      table.remove(arrDishWeights, index)
      arrRequirements[1] = selected.Code
      mapExtraWeightStr[selected.Code] = nil
    end
  end
  if not arrRequirements[1] then
    Log.Error("BakeOut 订单生成失败！")
    arrRequirements[1] = "it_1_1_1"
  end
  if GameConfig.IsTestMode() then
    TestAccumulatedLogInfo.TestPrintBakeOutOrderCreation(mapExtraWeight, mapExtraWeightStr, orderId, arrRequirements[1])
    TestAccumulatedLogInfo.LogNow()
  end
  local arrRewards = {}
  local goldCount = BakeOutOrderCreator._GetRewardCount(arrRequirements)
  if 0 < goldCount then
    arrRewards[#arrRewards + 1] = {
      [PROPERTY_TYPE] = EPropertyType.BakeOutToken,
      [PROPERTY_COUNT] = goldCount
    }
  end
  RewardApi.CryptRewards(arrRewards)
  local groupId = 0
  return MainOrder.Create(orderModel, orderId, groupId, GM.TaskManager:GetOngoingChapterId(), avatarId, arrRequirements, OrderType.BakeOut, GM.GameModel:GetServerTime(), arrRewards)
end

function BakeOutOrderCreator._GetRewardCount(arrRequirements)
  local total = 0
  for _, itemType in ipairs(arrRequirements) do
    local itemModelConfig = GM.ItemDataModel:GetModelConfig(itemType)
    if itemModelConfig and itemModelConfig.Reward then
      total = total + itemModelConfig.Reward
    end
  end
  return total
end
