local DashActivityOrderState = {
  LessThanHalf = 1,
  MoreThanHalf = 2,
  One = 3
}
DashActivityOrderAnimation = setmetatable({}, BaseOrderAnimation)
DashActivityOrderAnimation.__index = DashActivityOrderAnimation

function DashActivityOrderAnimation:Init(activityType)
  local activityDefinition = DashActivityDefinition[activityType]
  BaseOrderAnimation.Init(self, activityDefinition.OrderAvatarAnimationName)
end

function DashActivityOrderAnimation:SetProgress(progress)
  local newState
  if progress < 0.5 then
    newState = DashActivityOrderState.LessThanHalf
  elseif progress < 1 then
    newState = DashActivityOrderState.MoreThanHalf
  else
    newState = DashActivityOrderState.One
  end
  if self.m_state == newState then
    return
  end
  if newState == DashActivityOrderState.LessThanHalf then
    self:SetAnimation("appear", {})
  elseif newState == DashActivityOrderState.MoreThanHalf then
    self:SetAnimation("progress", {
      DashActivityOrderState.LessThanHalf
    })
  elseif newState == DashActivityOrderState.One then
    self:SetAnimation("finished", {
      DashActivityOrderState.LessThanHalf,
      DashActivityOrderState.MoreThanHalf
    })
  end
  self.m_state = newState
end

function DashActivityOrderAnimation:PlayEnterAnimation(progress)
  self.m_state = nil
  self:SetProgress(progress)
end
