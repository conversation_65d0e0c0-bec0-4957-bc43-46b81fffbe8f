DailyTaskTaskPrompt = {}
DailyTaskTaskPrompt.__index = DailyTaskTaskPrompt
DailyTaskTaskPrompt.SortingOrder = ESpecialViewSortingOrder.Prompt - 100
DailyTaskTaskPrompt.Showing = false
DailyTaskTaskPrompt.ArrCachedPromptTaskData = {}

function DailyTaskTaskPrompt.OnDailyTaskFinished(prompt, msg)
  prompt.ArrCachedPromptTaskData[#prompt.ArrCachedPromptTaskData + 1] = msg.data
  prompt.Try2ShowPrompt()
end

function DailyTaskTaskPrompt.Try2ShowPrompt()
  if DailyTaskTaskPrompt.Showing or #DailyTaskTaskPrompt.ArrCachedPromptTaskData == 0 then
    return
  end
  DailyTaskTaskPrompt.Showing = true
  local data = DailyTaskTaskPrompt.ArrCachedPromptTaskData[1]
  table.remove(DailyTaskTaskPrompt.ArrCachedPromptTaskData, 1)
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.DailyTaskTaskPrompt), GM.UIManager:GetCanvasRoot(), Vector3.zero, function(go)
    if not go:IsNull() then
      local prompt = go:GetLuaTable()
      prompt:Show(data)
    else
      DailyTaskTaskPrompt.Showing = false
    end
  end)
end

function DailyTaskTaskPrompt:Show(data)
  self.m_taskCell:Init(data, true)
  self.m_canvas.sortingOrder = DailyTaskTaskPrompt.SortingOrder
  DailyTaskTaskPrompt.SortingOrder = DailyTaskTaskPrompt.SortingOrder + 1
  local seq = DOTween.Sequence()
  seq:AppendInterval(self.m_taskCell.FinishAnimationDuration)
  
  function seq.onComplete()
    DailyTaskTaskPrompt.SortingOrder = DailyTaskTaskPrompt.SortingOrder - 1
    AddressableLoader.Destroy(self.gameObject)
    DailyTaskTaskPrompt.Showing = false
    DailyTaskTaskPrompt.Try2ShowPrompt()
  end
end
