InventoryProducerEmptyCell = {}
InventoryProducerEmptyCell.__index = InventoryProducerEmptyCell

function InventoryProducerEmptyCell:Init(itemCode)
  self.m_itemCode = itemCode
  local spriteName = GM.ItemDataModel:GetSpriteName(self.m_itemCode)
  self.m_itemImage.enabled = false
  self.m_itemMask.enabled = false
  SpriteUtil.SetImage(self.m_itemImage, spriteName, true)
  SpriteUtil.SetImage(self.m_itemMask, spriteName, true)
end

function InventoryProducerEmptyCell:OnClick()
  if GM.ItemDataModel:IsUnlocked(self.m_itemCode) then
    GM.UIManager:ShowPromptWithKey("pd_inventory_tip1")
  else
    GM.UIManager:ShowPromptWithKey("pd_inventory_tip2")
  end
end

InventoryProducerLockedCell = {}
InventoryProducerLockedCell.__index = InventoryProducerLockedCell

function InventoryProducerLockedCell:Init(unlockDay)
  self.m_descText.text = GM.GameTextModel:GetText("pd_inventory_lock_desc", unlockDay)
end
