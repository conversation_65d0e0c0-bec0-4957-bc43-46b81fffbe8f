ItemUtility = {}

function ItemUtility.GetItemType(chain, level)
  return chain .. "_" .. level
end

function ItemUtility.CodeWeightPairsToString(codeWeightPairs)
  local data = ""
  for index, item in ipairs(codeWeightPairs) do
    data = data .. item.Code .. "-" .. item.Weight
    if index ~= #codeWeightPairs then
      data = data .. ";"
    end
  end
  return data
end

function ItemUtility.StringToCodeWeightPairs(data)
  local array = StringUtil.Split(data, ";")
  return ItemModelFactory.GetCodeAndWeightPairs(array)
end

function ItemUtility.NumberArrayToString(array)
  return table.concat(array, ";")
end

function ItemUtility.StringToNumberArray(data)
  local array = StringUtil.Split(data, ";")
  local ret = {}
  for _, str in ipairs(array) do
    ret[#ret + 1] = tonumber(str)
  end
  return ret
end

function ItemUtility.GetModeByCode(code, forcedGameMode)
  if ExtraBoardActivityModel.IsExtraBoardActivityItem(code) then
    return EGameMode.ExtraBoard
  end
  return forcedGameMode or EGameMode.Board
end

function ItemUtility.Map2String(map)
  local arr = {}
  for key, value in pairs(map) do
    arr[#arr + 1] = key .. "#" .. value
  end
  return table.concat(arr, ";")
end

function ItemUtility.String2Map(data)
  local map = {}
  local array = StringUtil.Split(data, ";")
  local kvStr
  for _, str in ipairs(array) do
    kvStr = StringUtil.Split(str, "#")
    map[kvStr[1]] = kvStr[2]
  end
  return map
end

function ItemUtility.GetInnerCodeAndPrefixByCode(code)
  local index = StringUtil.rFindChar(code, "#")
  if index then
    return string.sub(code, index + 1), string.sub(code, 1, index)
  else
    return code
  end
end

function ItemUtility.GetToLevel1Count(level)
  return math.floor(2 ^ (level - 1))
end

function ItemUtility.GetRemainRequireToLevel1CountMinusBelowLevelItems(requireItem, requireCount, codeCountMap)
  local requireChain = GM.ItemDataModel:GetChainId(requireItem)
  local requireLevel = GM.ItemDataModel:GetChainLevel(requireItem)
  local requireToLevel1Count = ItemUtility.GetToLevel1Count(requireLevel) * requireCount
  local itemCode, haveItemCount, haveToLevel1Count
  for level = requireLevel, 1, -1 do
    itemCode = ItemUtility.GetItemType(requireChain, level)
    haveItemCount = codeCountMap[itemCode] or 0
    haveToLevel1Count = ItemUtility.GetToLevel1Count(level)
    for i = 1, haveItemCount do
      requireToLevel1Count = requireToLevel1Count - haveToLevel1Count
      codeCountMap[itemCode] = (codeCountMap[itemCode] or 0) - 1
      if codeCountMap[itemCode] <= 0 then
        codeCountMap[itemCode] = nil
      end
      if requireToLevel1Count <= 0 then
        break
      end
    end
    if requireToLevel1Count <= 0 then
      break
    end
  end
  return requireToLevel1Count
end
