UIBoardPromptTapCacheItems = setmetatable({}, BoardPrompt)
UIBoardPromptTapCacheItems.__index = UIBoardPromptTapCacheItems

function UIBoardPromptTapCacheItems.Create()
  local prompt = setmetatable({}, UIBoardPromptTapCacheItems)
  prompt:Init(BoardPromptType.TapCacheItems)
  return prompt
end

function UIBoardPromptTapCacheItems:CanStart(boardView)
  return not boardView:GetModel():IsBoardFull() and boardView:GetModel():GetCachedItemCount() > 0
end

function UIBoardPromptTapCacheItems:_Start(boardView)
  boardView:GetCacheRoot():ShowHandTapEffect(true)
end

function UIBoardPromptTapCacheItems:AutoDo(boardView)
  self:_Stop(boardView)
  boardView:GetCacheRoot():OnClicked()
end

function UIBoardPromptTapCacheItems:_Stop(boardView)
  boardView:GetCacheRoot():ShowHandTapEffect(false)
end

UIBoardPromptTapSpreadItem = setmetatable({}, BoardPromptTapSpreadItem)
UIBoardPromptTapSpreadItem.__index = UIBoardPromptTapSpreadItem

function UIBoardPromptTapSpreadItem.Create()
  local prompt = setmetatable({}, UIBoardPromptTapSpreadItem)
  prompt:Init(BoardPromptType.TapSpreadItem)
  return prompt
end

function UIBoardPromptTapSpreadItem:_CanStart(boardView)
  local boardModel = boardView:GetModel()
  local filter = function(itemModel)
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and itemSpread:GetState() == ItemSpreadState.Opened and itemSpread:GetStorageRestNumber() ~= 0 then
      return true
    end
  end
  self.m_itemModels = boardView:GetModel():FilterItems(filter)
  return #self.m_itemModels ~= 0
end

UIBoardPromptTapRewardBubbleItem = setmetatable({}, BoardPrompt)
UIBoardPromptTapRewardBubbleItem.__index = UIBoardPromptTapRewardBubbleItem

function UIBoardPromptTapRewardBubbleItem.Create()
  local prompt = setmetatable({}, UIBoardPromptTapRewardBubbleItem)
  prompt:Init(BoardPromptType.TapRewardBubble)
  return prompt
end

function UIBoardPromptTapRewardBubbleItem:CanStart(boardView)
  local boardModel = boardView:GetModel()
  local filter = function(itemModel)
    local itemRewardBubble = itemModel:GetComponent(ItemRewardBubble)
    return itemRewardBubble ~= nil
  end
  self.m_itemModels = boardView:GetModel():FilterItems(filter)
  return #self.m_itemModels ~= 0
end

function UIBoardPromptTapRewardBubbleItem:_Start(boardView)
  self.m_itemModel = Table.ListRandomSelectOne(self.m_itemModels)
  local itemView = boardView:GetItemView(self.m_itemModel)
  boardView:ShowHandTapEffect(itemView.transform.position)
end

function UIBoardPromptTapRewardBubbleItem:AutoDo(boardView)
  self:_Stop(boardView)
  boardView:GetModel():TapItem(self.m_itemModel)
end

function UIBoardPromptTapRewardBubbleItem:_Stop(boardView)
  boardView:HideHandTapEffect()
end
