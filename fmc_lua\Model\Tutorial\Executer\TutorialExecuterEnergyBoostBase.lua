local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer
Executer.eBoostType = EEnergyBoostType.Double
Executer.arrPreTutorialIds = {}
Executer.Step = {
  WindowDialog = "1",
  WindowDialogUpgrade = "11",
  ClickToggle = "2",
  ClickToggleUpgrade = "22",
  CloseWindow = "3",
  HighlightPds = "4",
  HighlightEntry = "5"
}
Executer.EStep2TextKey = {
  [Executer.Step.WindowDialog] = "double_energy_t1_1",
  [Executer.Step.WindowDialogUpgrade] = "double_energy_t1_1",
  [Executer.Step.ClickToggle] = "double_energy_t1_2",
  [Executer.Step.ClickToggleUpgrade] = "double_energy_t1_2",
  [Executer.Step.HighlightPds] = "double_energy_t2_1",
  [Executer.Step.HighlightEntry] = "double_energy_t3_1"
}
Executer.EStep2TextAnchorPercent = {
  [Executer.Step.WindowDialog] = 82,
  [Executer.Step.WindowDialogUpgrade] = 82,
  [Executer.Step.ClickToggle] = 50,
  [Executer.Step.ClickToggleUpgrade] = 50,
  [Executer.Step.HighlightPds] = 15,
  [Executer.Step.HighlightEntry] = 35
}

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OnViewWillClose, self, self._OnViewWillClose)
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.EnergyBoostModeChanged, self, self._OnEnergyBoostModeChanged)
  EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self._OnChangeGameModeFinished)
end

function Executer:_Finish()
  self:Finish(self.m_gesture, self.m_arrow)
  if self.m_boostHighlighted then
    TutorialHelper.DehighlightHudButton(ESceneViewHudButtonKey.EnergyBoost)
    self.m_boostHighlighted = false
  end
  if self.m_mainBoardHighlighted then
    TutorialHelper.DehighlightHudButton(ESceneViewHudButtonKey.MainBoard)
    self.m_mainBoardHighlighted = false
  end
end

function Executer:_OnChangeGameModeFinished()
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) and self:IsInStrongTutorial() and GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    self:_Finish(self.m_gesture)
  elseif self.m_strOngoingDatas == self.Step.CloseWindow and GM.SceneManager:GetGameMode() == EGameMode.Board then
    self:ExecuteStep4()
  end
end

function Executer:_OnOpenView(message)
  if StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) and not GM.TutorialModel:HasAnyStrongTutorialOngoing() and message.name == UIPrefabConfigName.EnergyBoostSettingWindow and GM.EnergyBoostModel:GetTriggerBoostType() == self.eBoostType then
    self:ExecuteStep1()
  end
end

function Executer:_IsPreTutorialFinished()
  for _, preId in ipairs(self.arrPreTutorialIds) do
    if GM.TutorialModel:IsTutorialFinished(preId) then
      return true
    end
  end
  return false
end

function Executer:ExecuteStep1()
  self.m_strOngoingDatas = self:_IsPreTutorialFinished() and self.Step.WindowDialogUpgrade or self.Step.WindowDialog
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  self:SetStrongTutorial(true)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  for _, tutorialId in ipairs(self.arrPreTutorialIds) do
    self.m_model:SetTutorialFinished(tutorialId)
  end
  TutorialHelper.WholeMask(function()
    self:ExecuteStep2()
  end)
  TutorialHelper.SetMaskAlphaOnce(0)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(self.EStep2TextKey[self.m_strOngoingDatas]), self.EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

function Executer:ExecuteStep2()
  if self.m_strOngoingDatas == self.Step.WindowDialogUpgrade then
    self.m_strOngoingDatas = self.Step.ClickToggleUpgrade
  else
    self.m_strOngoingDatas = self.Step.ClickToggle
  end
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local window = GM.UIManager:GetOpenedTopView()
  if not window or window.name ~= UIPrefabConfigName.EnergyBoostSettingWindow then
    self:_Finish()
    return
  end
  if GM.EnergyBoostModel:GetUserBoostType() == self.eBoostType then
    self:ExecuteStep3()
    return
  end
  local trans = window:GetToggleRectTrans()
  TutorialHelper.UpdateMask(trans.position, trans.sizeDelta, nil, true)
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(trans)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(self.EStep2TextKey[self.m_strOngoingDatas]), self.EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

function Executer:ExecuteStep3()
  if self.m_strOngoingDatas == self.Step.ClickToggleUpgrade then
    self:_Finish()
    return
  end
  self.m_strOngoingDatas = self.Step.CloseWindow
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.HideDialog()
  TutorialHelper.HideTutorialLayer(self.m_gesture)
  self.m_gesture = nil
  local window = GM.UIManager:GetOpenedTopView()
  if not window or window.name ~= UIPrefabConfigName.EnergyBoostSettingWindow then
    self:_Finish()
    return
  end
  local trans = window:GetCloseBtnTrans()
  TutorialHelper.UpdateMask(trans.position, trans.sizeDelta, nil, true)
  self.m_arrow = TutorialHelper.AddArrow2CustomRectTrans(trans, 135)
  self.m_arrow:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_OnViewWillClose(message)
  if self.m_strOngoingDatas == self.Step.CloseWindow and message.name == UIPrefabConfigName.EnergyBoostSettingWindow then
    TutorialHelper.HideDialog()
    TutorialHelper.HideTutorialLayer(self.m_gesture, self.m_arrow)
    self.m_gesture = nil
    if GM.SceneManager:GetGameMode() == EGameMode.Board then
      self:ExecuteStep4()
    elseif GM.SceneManager:GetGameMode() == EGameMode.Main then
      TutorialHelper.HighlightHudButton(ESceneViewHudButtonKey.MainBoard, false)
      self.m_mainBoardHighlighted = true
      TutorialHelper.WholeMask()
      self.m_arrow = TutorialHelper.AddArrow2HudButton(ESceneViewHudButtonKey.MainBoard, 0)
      self.m_arrow:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
    end
  end
end

function Executer:_OnEnergyBoostModeChanged()
  if (self.m_strOngoingDatas == self.Step.ClickToggle or self.m_strOngoingDatas == self.Step.ClickToggleUpgrade) and GM.UIManager:GetOpenedTopView() and GM.UIManager:GetOpenedTopView().name == UIPrefabConfigName.EnergyBoostSettingWindow and GM.EnergyBoostModel:IsEnergyBoostConfigOn() and GM.EnergyBoostModel:GetUserBoostType() == self.eBoostType then
    self:ExecuteStep3()
  elseif not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) and not GM.EnergyBoostModel:IsEnergyBoostConfigOn() then
    self:_Finish()
  end
end

function Executer:ExecuteStep4()
  self.m_strOngoingDatas = self.Step.HighlightPds
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.HideTutorialLayer(self.m_gesture, self.m_arrow)
  self.m_arrow = nil
  if self.m_mainBoardHighlighted then
    TutorialHelper.DehighlightHudButton(ESceneViewHudButtonKey.MainBoard)
    self.m_mainBoardHighlighted = false
  end
  TutorialHelper.WholeMask(function()
    self:ExecuteStep5()
  end)
  local energyBoostModel = GM.EnergyBoostModel
  local itemDatasArr = {}
  for item, _ in pairs(GM.MainBoardModel:GetAllBoardItems(false)) do
    if energyBoostModel:CanEnergyBoost(item:GetCode()) then
      table.insert(itemDatasArr, item)
    end
  end
  TutorialHelper.HighlightItems(itemDatasArr)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(self.EStep2TextKey[self.m_strOngoingDatas]), self.EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

function Executer:ExecuteStep5()
  self.m_strOngoingDatas = self.Step.HighlightEntry
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.UnHighlightItems()
  TutorialHelper.HighlightHudButton(ESceneViewHudButtonKey.EnergyBoost, true)
  self.m_boostHighlighted = true
  TutorialHelper.WholeMask(function()
    self:_Finish()
  end)
  self.m_arrow = TutorialHelper.AddArrow2HudButton(ESceneViewHudButtonKey.EnergyBoost, 180)
  self.m_arrow:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(self.EStep2TextKey[self.m_strOngoingDatas]), self.EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

return Executer
