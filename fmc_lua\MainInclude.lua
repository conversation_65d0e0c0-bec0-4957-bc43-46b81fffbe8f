require("Common.CommonInclude")
require("Data.DataInclude")
require("Board.BoardInclude")
require("Chapter.ChapterInclude")
require("Model.ModelInclude")
require("UI.UIInclude")
require("DashActivity.DashActivityInclude")
require("PassActivity.PassActivityInclude")
require("SpreeActivity.SpreeActivityInclude")
require("BakeOut.BakeOutInclude")
require("CoinRaceActivity.CoinRaceActivityInclude")
require("BreakEggActivity.BreakEggActivityInclude")
require("PiggyBank.PiggyBankInclude")
require("EnergyBoost.EnergyBoostInclude")
require("DailyTask.DailyTaskInclude")
require("ReturnUser.ReturnUserInclude")
require("DigActivity.DigActivityInclude")
require("ProgressActivity.ProgressActivityInclude")
require("ExtraBoardActivity.ExtraBoardActivityInclude")
require("SurpriseChestActivity.SurpriseChestActivityInclude")
require("BlindChest.BlindChestInclude")
require("PkRace.PkRaceInclude")
require("AlbumActivity.AlbumInclude")
if GameConfig.IsTestMode() then
  require("Test.TestInclude")
end
