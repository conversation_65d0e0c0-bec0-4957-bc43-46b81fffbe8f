TestAutoRunSettingWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestAutoRunSettingWindow.__index = TestAutoRunSettingWindow

function TestAutoRunSettingWindow:Init()
  self.m_consumeInput.text = GM.TestAutoRunModel.stopConsumedEnergy
  self.m_dayInput.text = GM.TestAutoRunModel.stopDay
  self.m_dayTipText.text = "当前在第" .. GM.MainBoardModel:GetOrderModel():GetCurOrderDay() .. "天"
  self.m_toggle.isOn = GM.TestAutoRunModel.leastEnergyDiffFirst
end

function TestAutoRunSettingWindow:RemoveAllNormal()
  GM.TestModel:RemoveAllNormal()
  self:Close()
end

function TestAutoRunSettingWindow:OnInputConsumeCondition()
  GM.TestAutoRunModel.stopConsumedEnergy = tonumber(self.m_consumeInput.text)
end

function TestAutoRunSettingWindow:OnInputDayCondition()
  GM.TestAutoRunModel.stopDay = tonumber(self.m_dayInput.text)
end

function TestAutoRunSettingWindow:OnToggleValueChanged()
  GM.TestAutoRunModel.leastEnergyDiffFirst = self.m_toggle.isOn
end
