LevelProgress = {ProgressCellGap = 200}
LevelProgress.__index = LevelProgress

function LevelProgress:Init(maxLevel)
  local sliderLayoutElement = self.m_sliderNode:GetComponent(typeof(CS.UnityEngine.UI.LayoutElement))
  sliderLayoutElement.preferredWidth = self.ProgressCellGap * maxLevel
  self.m_leftPendding = self.m_horLayout.padding.left
  self.m_progressCells = {}
  for level = 0, maxLevel do
    local progressCell = self:GetProgressCell(level)
    progressCell.transform.anchoredPosition = Vector2(self.ProgressCellGap * level, 0)
    table.insert(self.m_progressCells, progressCell)
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_contentNode)
  self:UpdateLevelContent()
  self:_UpdatePositionContent()
end

function LevelProgress:GetProgressCell(level)
  Log.Assert(false, "GetProgressCell()是抽象函数")
end

function LevelProgress:ProgressCellGap()
  Log.Assert(false, "GetCurrentLevel()是抽象函数")
end

function LevelProgress:GetProgressLevel()
  Log.Assert(false, "GetProgressLevel()是抽象函数")
end

function LevelProgress:PlayProgressAnimation(duration)
  local currentLevel = self:GetCurrentLevel()
  local sizeDelta = self.m_sliderFillNode.sizeDelta
  sizeDelta.x = self.ProgressCellGap * (currentLevel - 1)
  self.m_sliderFillNode:DOSizeDelta(sizeDelta, duration):SetEase(Ease.Linear)
  local normalizedPosition = self:_GetMiddleNormalizedPosition(currentLevel)
  local scrollRect = self.transform:GetComponent(typeof(ScrollRect))
  scrollRect:DOHorizontalNormalizedPos(normalizedPosition, duration)
end

function LevelProgress:PlayUpgradeAnimation(duration)
  local currentLevel = self:GetCurrentLevel()
  local sizeDelta = self.m_sliderFillNode.sizeDelta
  sizeDelta.x = self.ProgressCellGap * (currentLevel - 1)
  self.m_sliderFillNode:DOSizeDelta(sizeDelta, duration):SetEase(Ease.Linear)
end

function LevelProgress:UpdateLevelContent(needAnimation)
  local currentLevel = self:GetCurrentLevel()
  for _, progressCell in ipairs(self.m_progressCells) do
    progressCell:UpdateLevelContent(currentLevel, needAnimation)
  end
end

function LevelProgress:_UpdatePositionContent()
  local progressLevel = self:GetProgressLevel()
  UIUtil.SetSizeDelta(self.m_sliderFillNode, self.ProgressCellGap * math.max(0, progressLevel - 1))
  local currentLevel = self:GetCurrentLevel()
  local scrollRect = self.transform:GetComponent(typeof(ScrollRect))
  scrollRect.horizontalNormalizedPosition = self:_GetMiddleNormalizedPosition(currentLevel)
end

function LevelProgress:_GetMiddleNormalizedPosition(level)
  local sliderWidth = self.ProgressCellGap * (level - 1)
  local areaWidth = self.transform.rect.width
  if sliderWidth + self.m_leftPendding <= areaWidth / 2 then
    return 0
  end
  local contentWidth = self.m_contentNode.rect.width
  local normalized = (sliderWidth + self.m_leftPendding - areaWidth / 2) / (contentWidth - areaWidth)
  return math.min(normalized, 1)
end

function LevelProgress:GetCellByLevel(level)
  return self.m_progressCells[level]
end
