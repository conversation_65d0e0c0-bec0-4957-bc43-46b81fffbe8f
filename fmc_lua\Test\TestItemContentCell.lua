TestItemContentCell = {}
TestItemContentCell.__index = TestItemContentCell

function TestItemContentCell:Init(itemType)
  self.m_type = itemType
  local config = GM.ItemDataModel:GetViewConfig(itemType)
  local imageFileConfig = GM.DataResource.ImageFileConfig
  if config and config.Image ~= nil then
    for _, spriteName in ipairs(config.Image) do
      if imageFileConfig:GetConfig(spriteName) == nil then
        GM.UIManager:ShowPrompt("资源缺失，棋子类型：" .. itemType .. " 资源名称：" .. spriteName)
        return
      end
    end
  elseif imageFileConfig:GetConfig(itemType) == nil then
    GM.UIManager:ShowPrompt("资源缺失，棋子类型：" .. itemType)
    return
  end
  SpriteUtil.SetImage(self.m_iconImg, GM.ItemDataModel:GetSpriteName(itemType), true)
end

function TestItemContentCell:GetType()
  return self.m_type
end

function TestItemContentCell:OnBtnClicked()
  local boardModel = BoardModelHelper.GetActiveModel()
  if boardModel == nil then
    GM.UIManager:ShowPrompt("当前不在棋盘场景")
  else
    local position = boardModel:FindEmptyPositionInValidOrder()
    if position == nil then
      GM.UIManager:ShowPrompt("棋盘已满")
    else
      GM.TestModel:AddItemOnBoard(boardModel, position, self.m_type)
      GM.UIManager:ShowPrompt("添加成功")
    end
  end
end
