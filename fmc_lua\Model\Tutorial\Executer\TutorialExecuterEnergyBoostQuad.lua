local basePath = "Model.Tutorial.Executer.TutorialExecuterEnergyBoostBase"
local Executer = setmetatable({}, require(basePath))
Executer.__index = Executer
Executer.eBoostType = EEnergyBoostType.Quad
Executer.arrPreTutorialIds = {
  ETutorialId.EnergyBoostDouble
}
Executer.EStep2TextKey = {
  [Executer.Step.WindowDialog] = "quad_energy_t1_1",
  [Executer.Step.WindowDialogUpgrade] = "quad_energy_t4_1",
  [Executer.Step.ClickToggle] = "quad_energy_t1_2",
  [Executer.Step.ClickToggleUpgrade] = "quad_energy_t4_2",
  [Executer.Step.HighlightPds] = "quad_energy_t2_1",
  [Executer.Step.HighlightEntry] = "quad_energy_t3_1"
}
return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
