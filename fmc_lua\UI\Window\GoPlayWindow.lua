GoPlayWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  hideHudAnchorType = {
    EHudAnchorType.All
  }
}, BaseWindow)
GoPlayWindow.__index = GoPlayWindow

function GoPlayWindow:Init()
  BaseWindow.Init(self)
  local adjustSizeOffset = ScreenFitter.GetSceneViewSizeOffset()
  self.m_closeGo.transform:SetLocalPosY(self.m_closeGo.transform.localPosition.y - adjustSizeOffset.x)
  if IsAutoRun() then
    DOVirtual.DelayedCall(0.2, function()
      self:OnGoBtnClick()
    end)
  end
end

function GoPlayWindow:OnGoBtnClick()
  self:Close()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    return
  end
  if GM.ChapterManager.curActiveChapterName ~= GM.TaskManager:GetOngoingChapterName() then
    return
  end
  if GM.TimelineManager:IsPlayingTimeline() then
    return
  end
  GM.SceneManager:ChangeGameMode(EGameMode.Board)
end
