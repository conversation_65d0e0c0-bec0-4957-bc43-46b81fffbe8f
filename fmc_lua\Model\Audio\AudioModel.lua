AudioModel = {}
AudioModel.__index = AudioModel
local _AudioRoot
local _ClipPlayStartTimeTable = {}
AudioType = {
  Music = 1,
  Effect = 2,
  CookEffect = 3,
  BgEffect = 4
}
local AudioType = AudioType
local EAudioFadeType = {
  None = 1,
  FadeIn = 2,
  FadeOut = 3
}
AudioModel.AudioClipCacheTable = {}
AudioModel.AudioSourceCacheTable = {}
AudioModel.AudioLoopEffectDataMap = {}
AudioModel.AudioVolumeTable = {
  [AudioType.Music] = 0.75,
  [AudioType.Effect] = 1,
  [AudioType.CookEffect] = 1,
  [AudioType.BgEffect] = 1
}
AudioModel.AudioMuteTable = {
  [AudioType.Music] = false,
  [AudioType.Effect] = false,
  [AudioType.CookEffect] = false,
  [AudioType.BgEffect] = false
}
AudioModel.PrefsMuteKeys = {
  [AudioType.Music] = "MUSIC_MUTE",
  [AudioType.Effect] = "EFFECT_MUTE",
  [AudioType.CookEffect] = "EFFECT_MUTE",
  [AudioType.BgEffect] = "MUSIC_MUTE"
}
AudioModel.AudioSourcePool = {
  PoolCount = 0,
  UsedAudioSourceList = {},
  UnusedAudioSourceList = {},
  Init = function(self)
    local go, audioSource
    for i = 1, 5 do
      go = GameObject()
      go.name = "AudioSource_" .. i
      go.transform:SetParent(_AudioRoot.transform, false)
      audioSource = go:AddComponent(typeof(CS.UnityEngine.AudioSource))
      audioSource.enabled = false
      table.insert(self.UnusedAudioSourceList, audioSource)
      self.PoolCount = self.PoolCount + 1
    end
  end,
  Acquire = function(self, uniqueKey)
    local audioSource
    local cnt = #self.UnusedAudioSourceList
    if 0 < cnt then
      audioSource = self.UnusedAudioSourceList[cnt]
      table.remove(self.UnusedAudioSourceList, cnt)
    else
      local go = GameObject()
      self.PoolCount = self.PoolCount + 1
      go.name = "AudioSource_" .. self.PoolCount
      go.transform:SetParent(_AudioRoot.transform, false)
      audioSource = go:AddComponent(typeof(AudioSource))
      audioSource.enabled = false
    end
    self.UsedAudioSourceList[uniqueKey] = audioSource
    return audioSource
  end,
  Release = function(self, uniqueKey)
    local audioSource = self.UsedAudioSourceList[uniqueKey]
    self.UsedAudioSourceList[uniqueKey] = nil
    audioSource.enabled = false
    table.insert(self.UnusedAudioSourceList, audioSource)
  end,
  GetAudioSource = function(self, uniqueKey)
    return self.UsedAudioSourceList[uniqueKey]
  end
}

function AudioModel:Init()
  _AudioRoot = GameObject()
  _AudioRoot.name = "AudioRoot"
  self.m_audioFader = _AudioRoot:AddComponent(typeof(CS.AudioSourceFader))
  GameObject.DontDestroyOnLoad(_AudioRoot)
  Log.Assert(_AudioRoot, "_AudioRoot is nil, AudioModel init failed")
  self.AudioSourcePool:Init()
  self.AudioSourceCacheTable[AudioType.Music] = self.AudioSourcePool:Acquire(AudioType.Music)
  self.AudioSourceCacheTable[AudioType.Effect] = self.AudioSourcePool:Acquire(AudioType.Effect)
  self.AudioSourceCacheTable[AudioType.CookEffect] = self.AudioSourcePool:Acquire(AudioType.CookEffect)
  self.AudioSourceCacheTable[AudioType.BgEffect] = self.AudioSourcePool:Acquire(AudioType.BgEffect)
  self:_ClearLoopEffect()
  Log.Assert(self.AudioSourceCacheTable[AudioType.Music], "self.AudioSourcePool:Acquire Music failed")
  Log.Assert(self.AudioSourceCacheTable[AudioType.Effect], "self.AudioSourcePool:Acquire Effect failed")
  Log.Assert(self.AudioSourceCacheTable[AudioType.CookEffect], "self.AudioSourcePool:Acquire CookEffect failed")
  Log.Assert(self.AudioSourceCacheTable[AudioType.BgEffect], "self.AudioSourcePool:Acquire BgEffect failed")
  self:_GetDataFromPrefs()
end

function AudioModel:Destroy()
  if _AudioRoot then
    _AudioRoot:RemoveSelf()
    _AudioRoot = nil
  end
end

function AudioModel:PlayBGM(url, loop)
  loop = loop == nil and true or loop
  self:_PlayAudio2D(url, AudioType.Music, loop, nil, nil)
  self.m_bPlayingBGM = true
end

function AudioModel:PlayEffect(url)
  self:_PlayAudio2D(url, AudioType.Effect, false, nil, nil)
end

function AudioModel:StopBGM()
  if not self.m_bPlayingBGM then
    return
  end
  self.m_bPlayingBGM = false
  local source = self:_GetAudioSourceByType(AudioType.Music)
  source:Stop()
  source.clip = nil
end

function AudioModel:ReplayBGM()
  if self.m_bPlayingBGM then
    return
  end
  self.m_bPlayingBGM = true
  local source = self:_GetAudioSourceByType(AudioType.Music)
  source:Play()
end

function AudioModel:PlayEffectLoop(url, ownedObject, mixInterval)
  if self.AudioLoopEffectDataMap[ownedObject] ~= nil then
    return
  end
  mixInterval = mixInterval or 0.1
  local audioSource = self.AudioSourcePool:Acquire(ownedObject)
  audioSource.playOnAwake = true
  audioSource.spatialBlend = 0
  audioSource.enabled = true
  audioSource.mute = self.AudioMuteTable[AudioType.Effect] or false
  audioSource.volume = self.AudioVolumeTable[AudioType.Effect] or 1
  local firstLoop = true
  local loopPlay = function(ac)
    if self.AudioLoopEffectDataMap[ownedObject] == nil then
      return
    end
    local playCallback = function()
      local eFadeType = firstLoop and EAudioFadeType.FadeIn or EAudioFadeType.None
      self:_PlayAudioClip(AudioType.Effect, audioSource, ac, url, nil, nil, eFadeType)
      firstLoop = false
    end
    Scheduler.Schedule(playCallback, self, ac.length - mixInterval, Scheduler.RepeatForever, 0)
    self.AudioLoopEffectDataMap[ownedObject] = {audioSource = audioSource, scheduler = playCallback}
  end
  local ac = self.AudioClipCacheTable[url]
  self.AudioLoopEffectDataMap[ownedObject] = {audioSource = audioSource}
  if ac and not ac:IsNull() then
    loopPlay(ac)
  else
    self:_LoadAudioClipAsync(url, function(ac)
      loopPlay(ac)
    end)
  end
end

function AudioModel:StopEffectLoop(ownedObject)
  if self.AudioLoopEffectDataMap[ownedObject] ~= nil then
    local data = self.AudioLoopEffectDataMap[ownedObject]
    if data.audioSource and not data.audioSource:IsNull() then
      data.audioSource:Stop()
    end
    if data.scheduler then
      Scheduler.Unschedule(data.scheduler, self)
    end
    self.AudioSourcePool:Release(ownedObject)
    self.AudioLoopEffectDataMap[ownedObject] = nil
  end
end

function AudioModel:PlayEffectWithTimeLimit(url, timeLimit, durInS)
  if IsString(timeLimit) then
    timeLimit = tonumber(timeLimit)
  end
  if IsString(durInS) then
    durInS = tonumber(durInS)
  end
  if not timeLimit or not durInS then
    return
  end
  self:_PlayAudio2D(url, AudioType.Effect, false, timeLimit, durInS)
end

function AudioModel:StopAudio(audioType)
  local audioSource = self:_GetAudioSourceByType(audioType)
  audioSource:Stop()
  if audioType == AudioType.Effect then
    self:_ClearLoopEffect()
  end
end

function AudioModel:StopAllAudio()
  for _, audioSource in pairs(self.AudioSourceCacheTable) do
    audioSource:Stop()
  end
  self:_ClearLoopEffect()
end

function AudioModel:PauseAudio(audioType)
  local audioSource = self:_GetAudioSourceByType(audioType)
  audioSource:Pause()
  if audioType == AudioType.Effect then
    for object, data in pairs(self.AudioLoopEffectDataMap) do
      data.audioSource:Pause()
    end
    Scheduler.PauseTarget(self)
  end
end

function AudioModel:PauseAllAudio()
  for _, audioSource in pairs(self.AudioSourceCacheTable) do
    audioSource:Pause()
  end
  for object, data in pairs(self.AudioLoopEffectDataMap) do
    data.audioSource:Pause()
  end
  Scheduler.PauseTarget(self)
end

function AudioModel:UnPauseAudio(audioType)
  self:_GetAudioSourceByType(audioType):UnPause()
  if audioType == AudioType.Effect then
    for object, data in pairs(self.AudioLoopEffectDataMap) do
      data.audioSource:UnPause()
    end
    Scheduler.ResumeTarget(self)
  end
end

function AudioModel:UnPauseAllAudio()
  for _, audioSource in pairs(self.AudioSourceCacheTable) do
    audioSource:UnPause()
  end
  for object, data in pairs(self.AudioLoopEffectDataMap) do
    data.audioSource:UnPause()
  end
  Scheduler.ResumeTarget(self)
end

function AudioModel:SetAudioMute(audioType, isMute, isSave)
  local audioSource = self:_GetAudioSourceByType(audioType)
  audioSource.mute = isMute
  if audioType == AudioType.Effect then
    for object, data in pairs(self.AudioLoopEffectDataMap) do
      data.audioSource.mute = isMute
    end
    self:_GetAudioSourceByType(AudioType.CookEffect).mute = isMute
    self.AudioMuteTable[AudioType.CookEffect] = isMute
  elseif audioType == AudioType.Music then
    self:_GetAudioSourceByType(AudioType.BgEffect).mute = isMute
    self.AudioMuteTable[AudioType.BgEffect] = isMute
  end
  self.AudioMuteTable[audioType] = isMute
  if isSave then
    PlayerPrefs.SetInt(self:_GetPrefsMuteKeysByType(audioType), isMute and 1 or 0)
  end
end

function AudioModel:GetAudioMute(audioType)
  local audioSource = self:_GetAudioSourceByType(audioType)
  return audioSource.mute
end

function AudioModel:GetAudioVolume(audioType)
  local audioSource = self:_GetAudioSourceByType(audioType)
  return audioSource.volume
end

function AudioModel:_PlayAudio2D(url, audioType, isLoop, numLimit, durInS, eFadeType)
  local audioSource = self:_GetAudioSourceByType(audioType, isLoop)
  if audioSource:IsNull() then
    return
  end
  audioSource.loop = isLoop
  audioSource.playOnAwake = true
  audioSource.spatialBlend = 0
  audioSource.enabled = true
  audioSource.mute = self.AudioMuteTable[audioType] or false
  audioSource.volume = self.AudioVolumeTable[audioType] or 1
  local ac = self.AudioClipCacheTable[url]
  if ac and not ac:IsNull() then
    self:_PlayAudioClip(audioType, audioSource, ac, url, numLimit, durInS, eFadeType)
  else
    self:_LoadAudioClipAsync(url, function(ac)
      self:_PlayAudioClip(audioType, audioSource, ac, url, numLimit, durInS, eFadeType)
    end)
  end
end

function AudioModel:_GetDataFromPrefs()
  for k, audioType in pairs(AudioType) do
    local nMute = PlayerPrefs.GetInt(self:_GetPrefsMuteKeysByType(audioType), 0)
    self.AudioMuteTable[audioType] = nMute == 1 and true or false
    local source = self:_GetAudioSourceByType(audioType)
    if source ~= nil then
      source.volume = self.AudioVolumeTable[audioType] or 1
      source.mute = self.AudioMuteTable[audioType]
    end
    if audioType == AudioType.Effect then
      for object, data in pairs(self.AudioLoopEffectDataMap) do
        data.audioSource.mute = self.AudioMuteTable[audioType]
      end
    end
  end
end

function AudioModel:_LoadAudioClipAsync(url, callback)
  local func = function(AudioClip)
    if AudioClip == nil then
      Log.Error("LoadAudioClip failed, AudioClipName = [" .. (url or "nil") .. "]")
      return
    end
    self.AudioClipCacheTable[url] = AudioClip
    if callback then
      callback(AudioClip)
    end
  end
  GM.ResourceLoader:LoadLatestFile(GM.DataResource.AudioFileConfig:GetConfig(url), func)
end

function AudioModel:_GetAudioSourceByType(audioType)
  local audioSource = self.AudioSourceCacheTable[audioType]
  Log.Assert(audioSource, "_GetAudioSourceByType failed, type = [" .. (audioType or "nil") .. "]")
  return audioSource
end

function AudioModel:_GetPrefsMuteKeysByType(audioType)
  local key = self.PrefsMuteKeys[audioType]
  Log.Assert(key and key ~= "", "_GetPrefsMuteKeysByType key not found,Type = [" .. (audioType or "nil") .. "]")
  return key
end

function AudioModel:_PlayAudioClip(audioType, audioSource, audioClip, url, numLimit, durInS, eFadeType)
  if not audioSource or audioSource:IsNull() or not audioClip then
    return
  end
  durInS = durInS and durInS or 0
  local curTime = DeviceInfo.GetCpuTime()
  if not _ClipPlayStartTimeTable[url] then
    _ClipPlayStartTimeTable[url] = {}
  end
  local cnt = #_ClipPlayStartTimeTable[url]
  local clipDur = audioClip.length
  local num = 0
  local tmp = 0
  if 0 < cnt then
    for i = cnt, 1, -1 do
      tmp = _ClipPlayStartTimeTable[url][i] + clipDur - curTime
      if tmp < 0 then
        table.remove(_ClipPlayStartTimeTable[url], i)
      else
        num = num + (durInS >= curTime - _ClipPlayStartTimeTable[url][i] and 1 or 0)
      end
    end
  end
  if numLimit and numLimit < num then
    Log.Info("[Audio]limit " .. num .. " " .. numLimit, LogTag.Audio)
    return
  end
  table.insert(_ClipPlayStartTimeTable[url], 1, curTime)
  if audioSource.loop then
    if audioSource.clip == audioClip and not audioSource.isPlaying then
      audioSource:Play()
    elseif audioSource.clip ~= audioClip or self.m_audioFader:IsFading(audioSource) then
      if audioSource.clip then
        self.m_audioFader:StartFade(audioSource, 0.5, 0, function()
          audioSource.clip = audioClip
          audioSource:Play()
          self.m_audioFader:StartFade(audioSource, 0.6, AudioModel.AudioVolumeTable[audioType])
        end)
      else
        audioSource.clip = audioClip
        audioSource:Play()
      end
    end
  else
    audioSource:PlayOneShot(audioClip)
    if eFadeType == EAudioFadeType.FadeIn then
      audioSource.volume = 0
      self.m_audioFader:StartFade(audioSource, 0.4, AudioModel.AudioVolumeTable[audioType])
    elseif eFadeType == EAudioFadeType.FadeOut then
      self.m_audioFader:StartFade(audioSource, 0.3, 0)
    end
  end
  Log.Info("Audio: " .. url, LogTag.Audio)
end

function AudioModel:_ClearLoopEffect()
  for object, data in pairs(self.AudioLoopEffectDataMap) do
    data.audioSource:Stop()
    Scheduler.Unschedule(data.scheduler, self)
    self.AudioSourcePool:Release(object)
  end
  self.AudioLoopEffectDataMap = {}
end

function AudioModel:PlayCookEffect(url)
  self:_PlayAudio2D(url, AudioType.CookEffect, false, nil, nil)
end

function AudioModel:StopCookEffect()
  self:_GetAudioSourceByType(AudioType.CookEffect):Stop()
end

function AudioModel:PlayLoopBgEffect(url)
  self:_PlayAudio2D(url, AudioType.BgEffect, true, nil, nil)
end

function AudioModel:StopLoopBgEffect()
  self:_GetAudioSourceByType(AudioType.BgEffect):Stop()
end
