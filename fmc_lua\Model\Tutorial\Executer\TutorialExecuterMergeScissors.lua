local Step = {Merge = "1"}
local EStep2TextKey = {
  [Step.Merge] = "tutorial_scissors"
}
local itemId = "scissors_1"
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.ItemMerged, self, self.OnItemMerged)
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  local items = TutorialHelper.GetItems(itemId)
  if 2 <= #items then
    self.m_model:SetTutorialFinished(self:GetTutorialId())
    self:_ExecuteStep1(items[1], items[2])
    return true
  end
end

function Executer:OnItemMerged()
  if self.m_strOngoingDatas == Step.Merge and self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
  end
end

function Executer:_ExecuteStep1(item1, item2)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.Merge
  local from = item1:GetPosition()
  local to = item2:GetPosition()
  if from:GetX() > to:GetX() or from:GetX() == to:GetX() and from:GetY() > to:GetY() then
    local tmp = from
    from = to
    to = tmp
  end
  GM.TutorialModel:SetForceSourceBoardPosition(from)
  GM.TutorialModel:SetForceTargetBoardPosition(to)
  self.m_gesture = TutorialHelper.DragOnItems(from, to)
  if not self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
    return
  end
  TutorialHelper.MaskOnItemBoard(from, to)
  TutorialHelper.ShowDialogWithBoardMaskArea(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), from, to)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
