return {
  {
    ChapterId = "Pasta",
    Id = 1,
    Cost = 856,
    <PERSON><PERSON><PERSON> = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftWall", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 2,
    StartConditions = {1},
    Cost = 805,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "Floor", State = 9},
      {Slot = "oldFloor", State = 100}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 3,
    StartConditions = {2},
    Cost = 703,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midWall", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 4,
    StartConditions = {3},
    Cost = 754,
    <PERSON>wards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldMidSofa", State = 100}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 5,
    StartConditions = {4},
    Cost = 549,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midRug", State = 9},
      {Slot = "midRugA", State = 9},
      {Slot = "midRugB", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 6,
    StartConditions = {5},
    Cost = 549,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midTable", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 7,
    StartConditions = {6},
    Cost = 652,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midSofa", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 8,
    StartConditions = {7},
    Cost = 652,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midUtensil", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 9,
    StartConditions = {8},
    Cost = 705,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cornerPlantW",
        State = 9
      },
      {
        Slot = "cornerTrashW",
        State = 100
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 10,
    StartConditions = {9},
    Cost = 817,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cornerPlantS",
        State = 9
      },
      {
        Slot = "cornerTrash",
        State = 100
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 11,
    StartConditions = {10},
    Cost = 538,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "kitRug", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 12,
    StartConditions = {11},
    Cost = 761,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "kitTable", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 13,
    StartConditions = {12},
    Cost = 873,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "kitPasta", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 14,
    StartConditions = {13},
    Cost = 761,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "kitSauce", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 15,
    StartConditions = {14},
    Cost = 649,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "kitOven", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 16,
    StartConditions = {15},
    Cost = 817,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "kitCookware",
        State = 9
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 17,
    StartConditions = {16},
    Cost = 722,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "kitJam", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 18,
    StartConditions = {17},
    Cost = 667,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "oldRightSofa",
        State = 100
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 19,
    StartConditions = {18},
    Cost = 831,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightSofa", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 20,
    StartConditions = {19},
    Cost = 831,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldLadder", State = 100}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 21,
    StartConditions = {20},
    Cost = 831,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightSofaTwo",
        State = 9
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 22,
    StartConditions = {21},
    Cost = 776,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "oldBigTable",
        State = 100
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 23,
    StartConditions = {22},
    Cost = 831,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "bigTableTwo",
        State = 9
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 24,
    StartConditions = {23},
    Cost = 831,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "bigUtensilTwo",
        State = 9
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 25,
    StartConditions = {24},
    Cost = 810,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldCases", State = 100}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 26,
    StartConditions = {25},
    Cost = 810,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "cornerPlantN",
        State = 9
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 27,
    StartConditions = {26},
    Cost = 810,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "bigTable", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 28,
    StartConditions = {27},
    Cost = 810,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "bigUtensil", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 29,
    StartConditions = {28},
    Cost = 858,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "winRug", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 30,
    StartConditions = {29},
    Cost = 858,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightWall", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 31,
    StartConditions = {30},
    Cost = 907,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "Windows", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 32,
    StartConditions = {31},
    Cost = 858,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "winTable", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 33,
    StartConditions = {32},
    Cost = 930,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "winTableR", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 34,
    StartConditions = {33},
    Cost = 930,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "winTableL", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 35,
    StartConditions = {34},
    Cost = 823,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "winCounter", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 36,
    StartConditions = {35},
    Cost = 984,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barCounter", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 37,
    StartConditions = {36},
    Cost = 930,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barWineM", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 38,
    StartConditions = {37},
    Cost = 930,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barWine", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 39,
    StartConditions = {38},
    Cost = 823,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barLight", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 40,
    StartConditions = {39},
    Cost = 769,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barTable", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 41,
    StartConditions = {40},
    Cost = 789,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barChair", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 42,
    StartConditions = {41},
    Cost = 852,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barItems", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 43,
    StartConditions = {42},
    Cost = 1044,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "innerWall", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 44,
    StartConditions = {43},
    Cost = 980,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "innerPlantR",
        State = 9
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 45,
    StartConditions = {44},
    Cost = 980,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "innerPlantL",
        State = 9
      }
    }
  },
  {
    ChapterId = "Pasta",
    Id = 46,
    StartConditions = {45},
    Cost = 789,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "oldBigFloor",
        State = 100
      },
      {Slot = "bigFloor", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 47,
    StartConditions = {46},
    Cost = 980,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outWallR", State = 9}
    }
  },
  {
    ChapterId = "Pasta",
    Id = 48,
    StartConditions = {47},
    Cost = 1107,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outWallL", State = 9},
      {Slot = "Brand", State = 9}
    }
  }
}
