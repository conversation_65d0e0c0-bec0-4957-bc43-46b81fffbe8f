CrossPromotionModel = {}
CrossPromotionModel.__index = CrossPromotionModel
ECPMissionType = {Level = "level", Day = "day"}
ECPTaskStatus = {
  Done = 1,
  Doing = 2,
  Awarded = 3
}
ECPEventType = {
  StateChanged = "StateChanged"
}
local ECPActivityOpenStatus = {Close = "close", Open = "open"}
local ECrossPromtionWinOpenStatus = {NotOpen = "notopen", Opened = "open"}
local ELogBIStatus = {Log = "log", NotLog = "notlog"}

function CrossPromotionModel:Init()
  self.event = PairEvent.Create(self)
  self.m_bActivityOpened = false
  self:Reset()
end

function CrossPromotionModel:_UpdateActivityState()
  self.m_bActivityOpened = self.clientCheck
  if self.clientCheck then
    return
  end
  PlayerPrefs.SetString(EPlayerPrefKey.CrossPromtionWindowOpened, ECrossPromtionWinOpenStatus.NotOpen)
  local serStatus = self:_GetServerOpenStatus()
  local bOpen = false
  if StringUtil.IsNilOrEmpty(serStatus) then
    local cpOpenStatus = self:_GetOpenStatus()
    bOpen = cpOpenStatus == ECPActivityOpenStatus.Open
  else
    bOpen = serStatus == ECPActivityOpenStatus.Open
  end
  local url = GM.OperManager:GetUrlByType(EOperUrlType.CPClientCheck)
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(url, "POST", 8000, 1)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetCallback(function()
    if self and GM and reqCtx.Rcode == ResultCode.Succeeded and bOpen then
      self.m_bActivityOpened = true
      self:_UpdateTaskStatusAfterLogin()
    end
  end)
  local reqBody = {
    userId = GM.UserModel:GetUserId(),
    eventId = self.eventId,
    gameToken = GameConfig.GetGameToken(),
    hasTargetGame = not bOpen
  }
  reqCtx:AppendBody(json.encode(reqBody))
  reqCtx:Send()
end

function CrossPromotionModel:Reset()
  self.m_bPreActivityOpened = false
  self.m_bPreStrongTip = false
end

function CrossPromotionModel:LoginMsg(tbData)
  self:_ParseCPData(tbData)
  self:_UpdateTaskStatusAfterLogin()
end

function CrossPromotionModel:CallStateChanged()
  self.event:Call(ECPEventType.StateChanged)
end

function CrossPromotionModel:RefreshActivityButtonInNeed()
  local bStrongTip = self:IsStrongTipShow()
  if self.m_bPreActivityOpened ~= self.m_bActivityOpened or self.m_bPreStrongTip ~= bStrongTip then
    self:UpdateButtonPreStatus()
    self:CallStateChanged()
  end
end

function CrossPromotionModel:UpdateButtonPreStatus()
  self.m_bPreActivityOpened = self.m_bActivityOpened
  self.m_bPreStrongTip = self:IsStrongTipShow()
end

function CrossPromotionModel:CheckShow()
  return self:IsActivityOpen()
end

function CrossPromotionModel:IsActivityOpen()
  return self.m_bActivityOpened and GM.GameModel:GetServerTime() < self.endTime
end

function CrossPromotionModel:GetTasks()
  if self.m_taskSortFunc == nil then
    function self.m_taskSortFunc(t1, t2)
      if t1.eProgressStatus ~= t2.eProgressStatus then
        return t1.eProgressStatus < t2.eProgressStatus
      else
        return t1.order < t2.order
      end
    end
  end
  table.sort(self.arrMission, self.m_taskSortFunc)
  return self.arrMission
end

function CrossPromotionModel:GetEndTime()
  return self.endTime or 0
end

function CrossPromotionModel:Go2NewGame()
  local param = "?campaign=" .. GameConfig.GetGameToken() .. "&adgroup=" .. self.eventId .. "&creative=" .. GM.UserModel:GetUserId()
  if DeviceInfo.IsSystemIOS() then
    param = param .. "&idfa=" .. DeviceInfo.GetAdvertisingIdentifier()
  else
    param = param .. "&gps_adid=" .. DeviceInfo.GetAdvertisingIdentifier()
  end
  local urlPath
  if PlatformInterface.IsAppInstalled(self.m_judgeInstallKey) then
    urlPath = self.scheme .. "://" .. EOperActionPath.CrossPromotion .. param
    GM.BIManager:LogAction(EBIType.CrossPromotionPlayAction.PullUp, urlPath .. "&targetGameToken=" .. self.targetGameToken)
  else
    urlPath = self:ReplaceURLParam(self.link)
    GM.BIManager:LogAction(EBIType.CrossPromotionPlayAction.Down, urlPath .. "&targetGameToken=" .. self.targetGameToken)
  end
  CSPlatform:OpenURL(urlPath)
end

function CrossPromotionModel:ReplaceURLParam(url, gid)
  return string.gsub(url, "({[%w_]+})", function(s)
    if s == "{gameToken}" then
      return GameConfig.GetGameToken()
    elseif s == "{eventId}" then
      return self.eventId or 0
    elseif s == "{gameId}" then
      return gid or 0
    elseif s == "{userId}" then
      return GM.UserModel:GetUserId()
    elseif s == "{gps_adid}" or s == "{idfa}" then
      return DeviceInfo.GetAdvertisingIdentifier()
    end
    return ""
  end)
end

function CrossPromotionModel:GetMainPicDefault()
  return self.mainPicName .. "_EN_US"
end

function CrossPromotionModel:ParseFileName()
  self.mainPicName = GM.MoreGameModel:ParseFileName(self.mainPic)
  self.iconPicName = GM.MoreGameModel:ParseFileName(self.iconPic)
end

function CrossPromotionModel:SendMissionStatusData(eCPMissionType)
  local level = GM.LevelModel:GetCurrentLevel()
  local day = GM.MainBoardModel:GetCurOrderDay()
  if eCPMissionType == ECPMissionType.Level then
    if self.level == nil or level < self.level then
      return
    end
  elseif eCPMissionType == ECPMissionType.Day then
    if self.day == nil or day < self.day then
      return
    end
  else
    return
  end
  local url = GM.OperManager:GetUrlByType(EOperUrlType.CPUpdateMissionStatus)
  local param = "&token=" .. GameConfig.GetGameToken() .. "&user_id=" .. GM.UserModel:GetUserId() .. "&level=" .. level .. "&day=" .. day
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(url .. param, "GET", 8000, 0)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:Send()
end

function CrossPromotionModel:OpenCrossPromotionWindow()
  local osName = "Android"
  if DeviceInfo.IsSystemIOS() then
    osName = "iOS"
  end
  local url = GM.OperManager:GetUrlByType(EOperUrlType.CPRefreshEventStatus)
  local param = "&gameToken=" .. GameConfig.GetGameToken() .. "&userId=" .. GM.UserModel:GetUserId() .. "&osName=" .. osName
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(url .. param, "GET", 8000, 0)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  local requestCount = 2
  local sucRequestCount = 0
  local FailTipFun = function(tag)
    if GM ~= nil then
      GM.UIManager:ShowPromptWithKey("bad_network_window_desc")
    end
  end
  GM.UIManager:ShowMask()
  reqCtx:SetCallback(function()
    requestCount = requestCount - 1
    if GM == nil then
      return
    end
    if requestCount == 0 then
      GM.UIManager:HideMask()
    end
    if reqCtx.Rcode == ResultCode.Succeeded and 0 < reqCtx.RespBody.Bytes then
      sucRequestCount = sucRequestCount + 1
      local rawData = reqCtx:GetResponseString()
      local tbData = json.decode(rawData)
      if tbData then
        self:_ParseCPData(tbData.cp)
      end
      if GM ~= nil and sucRequestCount == 2 then
        self:UpdateTaskStatus()
        GM.UIManager:OpenView(UIPrefabConfigName.CrossPromotionWindow)
      elseif requestCount == 0 then
        FailTipFun()
      end
    elseif requestCount == 0 then
      FailTipFun()
    end
  end)
  reqCtx:Send()
  ApiMessage.GetRewardStatus(self:GetArrRewardKeys(), function(status, data)
    requestCount = requestCount - 1
    if GM == nil then
      return
    end
    if requestCount == 0 then
      GM.UIManager:HideMask()
    end
    if status then
      sucRequestCount = sucRequestCount + 1
      self:_SetServeArrRewards(data.rewards)
      if GM ~= nil and sucRequestCount == 2 then
        self:UpdateTaskStatus()
        GM.UIManager:OpenView(UIPrefabConfigName.CrossPromotionWindow)
      elseif requestCount == 0 then
        FailTipFun()
      end
    elseif requestCount == 0 then
      FailTipFun()
    end
  end)
end

function CrossPromotionModel:GetArrRewardKeys()
  local arrKeys = {}
  for i = 1, #self.arrMission do
    arrKeys[#arrKeys + 1] = self.arrMission[i]:GetTaskRewardKey()
  end
  return arrKeys
end

function CrossPromotionModel:GetTaskByRewardKey(rk)
  for i = 1, #self.arrMission do
    if self.arrMission[i]:GetTaskRewardKey() == rk then
      return self.arrMission[i]
    end
  end
  return nil
end

function CrossPromotionModel:ParseSerReward()
  if Table.IsEmpty(self.serArrRewards) then
    return
  end
  for i = 1, #self.serArrRewards do
    local task = self:GetTaskByRewardKey(self.serArrRewards[i].key)
    if task ~= nil then
      task:ParseSerReward(self.serArrRewards[i])
    end
  end
end

function CrossPromotionModel:UpdateTaskStatus()
  self:ParseSerReward()
  for i = 1, #self.arrMission do
    self.arrMission[i]:UpdateTaskStatus()
  end
  self:RefreshActivityButtonInNeed()
end

function CrossPromotionModel:IsStrongTipShow()
  if not self.m_bActivityOpened then
    return false
  end
  local strOpenStatus = PlayerPrefs.GetString(EPlayerPrefKey.CrossPromtionWindowOpened, ECrossPromtionWinOpenStatus.NotOpen)
  if strOpenStatus == ECrossPromtionWinOpenStatus.NotOpen then
    return true
  end
  if Table.IsEmpty(self.arrMission) then
    return false
  end
  for i = 1, #self.arrMission do
    if self.arrMission[i]:HasReward() then
      return true
    end
  end
  return false
end

function CrossPromotionModel:SetCPWinOpended()
  PlayerPrefs.SetString(EPlayerPrefKey.CrossPromtionWindowOpened, ECrossPromtionWinOpenStatus.Opened)
end

function CrossPromotionModel:LoadUrlImageByCurLanguage(imgComp, preUrl, defFile)
  if not imgComp or imgComp:IsNull() then
    return
  end
  local funUpdateSprite = function(sprite)
    if sprite and imgComp and not imgComp:IsNull() then
      imgComp.sprite = sprite
      imgComp.enabled = true
      imgComp.color = CSColor.white
      SpriteUtil.SetNativeSize(imgComp)
    end
  end
  if StringUtil.IsNilOrEmpty(preUrl) then
    if not StringUtil.IsNilOrEmpty(defFile) and ImageFileConfigName[defFile] ~= nil then
      SpriteUtil.SetImage(imgComp, defFile, true, funUpdateSprite)
    end
    return
  end
  local lang = LocalizationModel:GetCurLanguageInString()
  local downUrl = preUrl .. "_" .. lang .. ".png"
  local canUseDefaultIcon = defFile ~= nil and ImageFileConfigName[defFile] ~= nil
  LoadUrlImage:LoadSprite(downUrl, function(bSuccess, sprite, rCode)
    if bSuccess then
      funUpdateSprite(sprite)
    end
  end, true, canUseDefaultIcon and defFile or nil, canUseDefaultIcon and funUpdateSprite or nil)
end

function CrossPromotionModel:_ParseCPData(tbData)
  self.m_bActivityOpened = false
  if Table.IsEmpty(tbData) then
    return
  end
  self.sourceGameToken = tbData.source_game_token
  self.sourceEventId = tbData.source_event_id
  if not IsNil(self.sourceGameToken) then
    self:_LogBI()
  end
  self.level = tbData.level_up
  self.day = tbData.day_up
  self.endTime = tbData.end_time
  if self.endTime == nil then
    return
  end
  self.eventId = tbData.event_id
  self.packageName = tbData.package_name
  self.scheme = tbData.scheme
  self.endTime = tbData.end_time
  self.link = tbData.link
  self.mainPic = tbData.main_pic
  self.iconPic = tbData.icon_pic
  self.targetGameToken = tbData.target_game_token or ""
  self.clientCheck = tbData.client_checked
  self.m_judgeInstallKey = DeviceInfo.IsSystemIOS() and self.scheme or self.packageName
  self.arrMission = {}
  local arr = tbData.mission
  Log.Assert(not Table.IsEmpty(arr), "交叉推广:任务数据为空,请检查配置...")
  if not Table.IsEmpty(arr) then
    for i = 1, #arr do
      local tbMd = PromotionMissionData.Create(arr[i])
      self.arrMission[#self.arrMission + 1] = tbMd
    end
  end
  table.sort(self.arrMission, function(a, b)
    return tonumber(a.order) < tonumber(b.order)
  end)
  self:ParseFileName()
  self:_UpdateActivityState()
end

function CrossPromotionModel:_UpdateTaskStatusAfterLogin()
  if Table.IsEmpty(self.arrMission) or not self.m_bActivityOpened then
    return
  end
  for i = 1, #self.arrMission do
    self.arrMission[i]:UpdateTaskStatusAfterLogin()
  end
  self:RefreshActivityButtonInNeed()
end

function CrossPromotionModel:_GetActivityKey()
  if IsNil(self.eventId) then
    return nil
  end
  return "CP_" .. self.eventId
end

function CrossPromotionModel:_GetOpenStatus()
  if PlatformInterface.IsAppInstalled(self.m_judgeInstallKey) then
    return ECPActivityOpenStatus.Close
  end
  return ECPActivityOpenStatus.Open
end

function CrossPromotionModel:_GetServerOpenStatus()
  local activityKey = self:_GetActivityKey()
  if activityKey == nil then
    return ECPActivityOpenStatus.Close
  end
  return GM.MiscModel:Get(activityKey)
end

function CrossPromotionModel:_LogBI()
  local logKey = "cp_log_bi_" .. self.sourceEventId
  if PlayerPrefs.GetString(logKey, ELogBIStatus.NotLog) ~= ELogBIStatus.NotLog then
    return
  end
  local bindAction = ""
  local tbUrlParam = GM.OperManager:GetUrlParam()
  local strTrackName = CSAppsFlyerManager:GetTrackerName()
  if not Table.IsEmpty(tbUrlParam) and not StringUtil.IsNilOrEmpty(strTrackName) then
    bindAction = EBIType.CPBindSourceAction.Both
  elseif not Table.IsEmpty(tbUrlParam) then
    bindAction = EBIType.CPBindSourceAction.URL
  elseif not StringUtil.IsNilOrEmpty(strTrackName) then
    bindAction = EBIType.CPBindSourceAction.TrackerName
  end
  if bindAction ~= "" then
    local logData = {
      sourceEventId = self.sourceEventId,
      sourceGameToken = self.sourceGameToken,
      sourceBind = bindAction,
      url = tbUrlParam,
      trackName = strTrackName
    }
    local dataJson = json.encode(logData)
    GM.BIManager:LogString(dataJson, EBIType.CrossPromotionBindSource)
    PlayerPrefs.SetString(logKey, ELogBIStatus.Log)
  end
end

function CrossPromotionModel:_SetServeArrRewards(arrRwards)
  self.serArrRewards = arrRwards
end

PromotionMissionData = {}
PromotionMissionData.__index = PromotionMissionData

function PromotionMissionData.Create(tbData)
  local tb = setmetatable({}, PromotionMissionData)
  tb:_Init(tbData)
  return tb
end

function PromotionMissionData:_Init(tbData)
  self.id = tbData.id
  self.order = tbData.order
  self.taskType = tbData.task_type
  self.taskParam = tbData.task_param
  self.taskProgress = tbData.task_progress
  self.icon = tbData.icon
  self.title = tbData.title
  self.desc = tbData.desc
  self.rewards = ConfigUtil.GetCurrencyFromArrStr(tbData.reward)
  self.status = tbData.mission_status
  if self.status == "incomplete" then
    self.eProgressStatus = ECPTaskStatus.Doing
  elseif self.status == "completed" then
    self.eProgressStatus = ECPTaskStatus.Done
  elseif self.status == "awarded" then
    self.eProgressStatus = ECPTaskStatus.Awarded
  end
  self.serRewards = {}
end

function PromotionMissionData:HasReward()
  return self.eProgressStatus == ECPTaskStatus.Done
end

function PromotionMissionData:GetTaskRewardKey()
  if GM ~= nil then
    return "op_cp_" .. GM.CrossPromotionModel.eventId .. "_" .. self.id
  end
  return ""
end

function PromotionMissionData:GetProgress()
  return self.taskProgress .. "/" .. self.taskParam
end

function PromotionMissionData:ParseSerReward(tbData)
  if Table.IsEmpty(tbData) then
    return
  end
  self.serveRewardStatus = tbData.status
  local content = json.decode(tbData.content)
  if not Table.IsEmpty(content) then
    self.serRewards = ConfigUtil.GetCurrencyFromArrStr(content.rewards)
  end
end

function PromotionMissionData:UpdateTaskStatus()
  if self.eProgressStatus == ECPTaskStatus.Done and self.serveRewardStatus and self.serveRewardStatus == 1 then
    self.eProgressStatus = ECPTaskStatus.Awarded
  end
end

function PromotionMissionData:UpdateTaskStatusAfterLogin()
  if GM == nil then
    return
  end
  local bAwarded = true
  local rewardKey = self:GetTaskRewardKey()
  local mapRewardInfo = GM.RewardModel:GetRewardInfoMap()
  if mapRewardInfo and mapRewardInfo[rewardKey] and mapRewardInfo[rewardKey].status == ERewardStatus.NotReceived then
    bAwarded = false
  end
  if self.eProgressStatus == ECPTaskStatus.Done and bAwarded then
    self.eProgressStatus = ECPTaskStatus.Awarded
  end
end

function PromotionMissionData:GetRewards()
  if not Table.IsEmpty(self.serRewards) then
    return self.serRewards
  end
  return self.rewards
end
