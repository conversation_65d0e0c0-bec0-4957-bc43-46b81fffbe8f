RevertCookConfirmWindow = setmetatable({}, TwoButtonWindow)
RevertCookConfirmWindow.__index = RevertCookConfirmWindow

function RevertCookConfirmWindow:Init(itemCookCmp)
  TwoButtonWindow.Init(self, "window_revocation_title", "window_revocation_desc", "", "common_button_cancel", nil, nil, true)
  self.m_itemCookCmp = itemCookCmp
  local recipe = itemCookCmp:GetRecipe()
  SpriteUtil.SetImage(self.m_recipeImg, GM.ItemDataModel:GetSpriteName(recipe), true)
  local instrument = itemCookCmp:GetItemModel():GetCode()
  SpriteUtil.SetImage(self.m_instruImg, GM.ItemDataModel:GetSpriteName(instrument), true)
  local cost = GM.ItemDataModel:GetModelConfig(recipe).RevocationPrice or 1
  self.m_propertyBtn:Init(EPropertyType.Gem, cost, function()
    self:OnRevertClicked()
  end)
end

function RevertCookConfirmWindow:OnRevertClicked()
  local closeWin = self.m_itemCookCmp:TryRevertCook()
  if closeWin then
    self:Close()
  end
end
