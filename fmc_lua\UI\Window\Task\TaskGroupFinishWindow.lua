TaskGroupFinishWindow = setmetatable({canCloseByAndroidBack = false, windowMaskAlpha = 0}, BaseWindow)
TaskGroupFinishWindow.__index = TaskGroupFinishWindow

function TaskGroupFinishWindow:Init()
  local sceneView = GM.UIManager:GetOpenedTopViewByType(EViewType.SceneView)
  local taskButton = sceneView:GetHudButton(ESceneViewHudButtonKey.Task)
  local position, scaleX = taskButton:GetBoxPosAndScale()
  local rewards = GM.TaskManager:GetProgressReward()
  GM.TaskManager:ClaimProgressReward()
  local eBoxRewardType = EBoxRewardType.Box
  local title = "claim_task_group_reward"
  GM.UIManager:OpenView(UIPrefabConfigName.BoxRewardWindow, position, scaleX, eBoxRewardType, rewards, title, true, function()
    DelayExecuteFunc(function()
      GM.AudioModel:PlayEffect(AudioFileConfigName.SfxTaskChest)
    end, 1)
  end, function()
    EventDispatcher.DispatchEvent(EEventType.TaskProgressRewardClaimed)
  end)
  self:Close()
end
