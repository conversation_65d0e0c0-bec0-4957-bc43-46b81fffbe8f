ProfilingWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow
}, BaseWindow)
ProfilingWindow.__index = ProfilingWindow
ProfilingWindow.m_customersPause = false
ProfilingWindow.m_rolesPause = false

function ProfilingWindow:Init()
end

function ProfilingWindow:OnClickMipmapOn()
  self.m_graphicsDebugger:Enable(GM.ChapterManager:GetActiveRoomView().gameObject)
end

function ProfilingWindow:OnClickMipmapOff()
  self.m_graphicsDebugger:Disable(GM.ChapterManager:GetActiveRoomView().gameObject)
end

function ProfilingWindow:OnCustomersPause()
  ProfilingWindow.m_customersPause = true
  for _, c in pairs(GM.ChapterManager:GetActiveRoomView().m_customers) do
    c:Pause()
  end
end

function ProfilingWindow:OnCustomersResume()
  ProfilingWindow.m_customersPause = false
  for _, c in pairs(GM.ChapterManager:GetActiveRoomView().m_customers) do
    c:Resume()
  end
end

function ProfilingWindow:OnRolesPause()
  ProfilingWindow.m_rolesPause = true
  for _, r in pairs(GM.ChapterManager:GetActiveRoomView().m_roles) do
    r:Pause()
  end
end

function ProfilingWindow:OnRolesResume()
  ProfilingWindow.m_rolesPause = false
  for _, r in pairs(GM.ChapterManager:GetActiveRoomView().m_roles) do
    r:Resume()
  end
end

function ProfilingWindow:Close()
  BaseWindow.Close(self)
  if ProfilingWindow.m_customersPause then
    self:OnCustomersPause()
  else
    self:OnCustomersResume()
  end
  if ProfilingWindow.m_rolesPause then
    self:OnRolesPause()
  else
    self:OnRolesResume()
  end
end

function ProfilingWindow:OnEnableSceneBatch()
  self.m_graphicsDebugger:BeginSceneBatch(GM.ChapterManager:GetActiveRoomView().gameObject)
end

function ProfilingWindow:OnEnableSceneMipmap()
  self.m_graphicsDebugger:BeginCheckSceneMipmap(GM.ChapterManager:GetActiveRoomView().gameObject)
end
