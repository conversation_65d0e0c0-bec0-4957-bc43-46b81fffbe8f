LollipopNotificationHelper = setmetatable({}, DashActivityNotificationHelper)
LollipopNotificationHelper.__index = LollipopNotificationHelper

function LollipopNotificationHelper.IsSceneExist(strScene)
  if strScene == NotificationScene.LollipopStart or strScene == NotificationScene.LollipopEnd then
    return true
  end
  return false
end

function LollipopNotificationHelper.Generate(strScene)
  local results = {}
  local model = GM.ActivityManager:GetModel(ActivityType.Lollipop)
  local state = model:GetState()
  local strTileKey, strDescKey = GM.NotificationModel:GetTextTileAndDesc(strScene)
  if state == ActivityState.Preparing and strScene == NotificationScene.LollipopStart then
    strTileKey = strTileKey ~= "" and strTileKey or "push_activity_Lollipop_open_title"
    strDescKey = strDescKey ~= "" and strDescKey or "push_activity_Lollipop_open_desc"
    DashActivityNotificationHelper._GenerateDashStartNotification(results, model, NotificationType.<PERSON>nata, strTileKey, strDesc<PERSON>ey)
  elseif state == ActivityState.Started and strScene == NotificationScene.LollipopEnd then
    strTileKey = strTileKey ~= "" and strTileKey or "push_activity_Lollipop_end_title"
    strDescKey = strDescKey ~= "" and strDescKey or "push_activity_Lollipop_end_desc"
    DashActivityNotificationHelper._GenerateDashEndNotification(results, model, NotificationType.Pinata, strTileKey, strDescKey)
  end
  return results
end
