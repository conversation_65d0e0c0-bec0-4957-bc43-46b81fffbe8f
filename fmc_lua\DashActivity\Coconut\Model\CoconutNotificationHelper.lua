CoconutNotificationHelper = setmetatable({}, DashActivityNotificationHelper)
CoconutNotificationHelper.__index = CoconutNotificationHelper

function CoconutNotificationHelper.IsSceneExist(strScene)
  if strScene == NotificationScene.CoconutStart or strScene == NotificationScene.CoconutEnd then
    return true
  end
  return false
end

function CoconutNotificationHelper.Generate(strScene)
  local results = {}
  local model = GM.ActivityManager:GetModel(ActivityType.Coconut)
  local state = model:GetState()
  local strTileKey, strDescKey = GM.NotificationModel:GetTextTileAndDesc(strScene)
  if state == ActivityState.Preparing and strScene == NotificationScene.CoconutStart then
    strTileKey = strTileKey ~= "" and strTileKey or "push_event_coconut_open_title"
    strDescKey = strDescKey ~= "" and strDescKey or "push_event_coconut_open_desc"
    DashActivityNotificationHelper._GenerateDashStartNotification(results, model, NotificationType.Coconut, strTileKey, strDescKey)
  elseif state == ActivityState.Started and strScene == NotificationScene.CoconutEnd then
    strTileKey = strTileKey ~= "" and strTileKey or "push_event_coconut_end_title"
    strDescKey = strDescKey ~= "" and strDescKey or "push_event_coconut_end_desc"
    DashActivityNotificationHelper._GenerateDashEndNotification(results, model, NotificationType.Coconut, strTileKey, strDescKey)
  end
  return results
end
