return {
  {
    ChapterId = "Tapas",
    Id = 1,
    Cost = 856,
    Re<PERSON>s = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secFloor", State = 9},
      {
        Slot = "oldSecFloor",
        State = 100
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 2,
    StartConditions = {1},
    Cost = 805,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secFenceR", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 3,
    StartConditions = {2},
    Cost = 703,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secFenceL", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 4,
    StartConditions = {3},
    Cost = 754,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "oldSecTable",
        State = 100
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 5,
    StartConditions = {4},
    Cost = 549,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secWallL", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 6,
    StartConditions = {5},
    Cost = 549,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldDoorL", State = 100}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 7,
    StartConditions = {6},
    Cost = 652,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "newLeftDoor",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 8,
    StartConditions = {7},
    Cost = 652,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secWallR", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 9,
    StartConditions = {8},
    Cost = 686,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldDoorR", State = 100}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 10,
    StartConditions = {9},
    Cost = 795,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "newRightDoor",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 11,
    StartConditions = {10},
    Cost = 523,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secTableA", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 12,
    StartConditions = {11},
    Cost = 740,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secUtensilA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 13,
    StartConditions = {12},
    Cost = 849,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secUtensilB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 14,
    StartConditions = {13},
    Cost = 740,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secTableB", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 15,
    StartConditions = {14},
    Cost = 632,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "fencePlantL",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 16,
    StartConditions = {15},
    Cost = 795,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "fencePlantR",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 17,
    StartConditions = {16},
    Cost = 685,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "floor", State = 9},
      {Slot = "oldFloor", State = 100}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 18,
    StartConditions = {17},
    Cost = 634,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "pole", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 19,
    StartConditions = {18},
    Cost = 789,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldWin", State = 100}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 20,
    StartConditions = {19},
    Cost = 789,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "oldLeftTable",
        State = 100
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 21,
    StartConditions = {20},
    Cost = 789,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "winA", State = 9},
      {Slot = "winAt", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 22,
    StartConditions = {21},
    Cost = 737,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barTable", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 23,
    StartConditions = {22},
    Cost = 789,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "barPlant", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 24,
    StartConditions = {23},
    Cost = 789,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "winB", State = 9},
      {Slot = "winBt", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 25,
    StartConditions = {24},
    Cost = 761,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "oldRightTable",
        State = 100
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 26,
    StartConditions = {25},
    Cost = 761,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "winC", State = 9},
      {Slot = "winCt", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 27,
    StartConditions = {26},
    Cost = 761,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "winD", State = 9},
      {Slot = "winDt", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 28,
    StartConditions = {27},
    Cost = 761,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftTableA", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 29,
    StartConditions = {28},
    Cost = 807,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftUtensilA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 30,
    StartConditions = {29},
    Cost = 807,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftTableB", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 31,
    StartConditions = {30},
    Cost = 853,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftUtensilB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 32,
    StartConditions = {31},
    Cost = 807,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftTableC", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 33,
    StartConditions = {32},
    Cost = 888,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftUtensilC",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 34,
    StartConditions = {33},
    Cost = 888,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightTableA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 35,
    StartConditions = {34},
    Cost = 783,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightUtensilA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 36,
    StartConditions = {35},
    Cost = 941,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightTableB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 37,
    StartConditions = {36},
    Cost = 888,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightUtensilB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 38,
    StartConditions = {37},
    Cost = 888,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightTableC",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 39,
    StartConditions = {38},
    Cost = 783,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightUtensilC",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 40,
    StartConditions = {39},
    Cost = 731,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "bridge", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 41,
    StartConditions = {40},
    Cost = 678,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "riverPlantL",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 42,
    StartConditions = {41},
    Cost = 770,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "riverPlantR",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 43,
    StartConditions = {42},
    Cost = 935,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "riverFenceL",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 44,
    StartConditions = {43},
    Cost = 880,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "riverFenceR",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 45,
    StartConditions = {44},
    Cost = 880,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "river", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 46,
    StartConditions = {45},
    Cost = 715,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "riverTableL",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 47,
    StartConditions = {46},
    Cost = 880,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "riverTableR",
        State = 9
      }
    }
  },
  {
    ChapterId = "Tapas",
    Id = 48,
    StartConditions = {47},
    Cost = 990,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "brand", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 49,
    StartConditions = {48},
    Cost = 880,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "fountain", State = 9}
    }
  },
  {
    ChapterId = "Tapas",
    Id = 50,
    StartConditions = {49},
    Cost = 990,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "fountainDec",
        State = 9
      }
    }
  }
}
