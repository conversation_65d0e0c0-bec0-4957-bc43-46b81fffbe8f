TestLanguageSettingWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, LanguageSettingWindow)
TestLanguageSettingWindow.__index = TestLanguageSettingWindow

function TestLanguageSettingWindow:OpenConfirmWindow(language)
  GM.UIManager:OpenView(UIPrefabConfigName.TestTwoButtonWindow, "language_change_confirm_window_title", "language_change_confirm_desc", "language_change_confirm_button_yes", "language_change_confirm_button_no", function()
    LocalizationModel:ChangeLanguageWithString(language)
    GM:RestartGame(ERestartType.WithLocalization, EBIProjectType.RestartGameAction.Language)
  end, nil, true)
end
