SpreeActivityOrderCreatorFixed = {}
SpreeActivityOrderCreatorFixed.CurrentIdKey = "CurrentId"
SpreeActivityOrderCreatorFixed.FinishedKey = "Finished"

function SpreeActivityOrderCreatorFixed.Create(orderModel, slot)
  local dbTable = orderModel:GetData()
  assert(dbTable:GetValue(slot, SpreeActivityOrderCreatorFixed.FinishedKey) ~= 0)
  local currentId = dbTable:GetValue(slot, SpreeActivityOrderCreatorFixed.CurrentIdKey)
  local nextId = currentId == nil and SpreeActivityOrderModel.ChainIdOffset * slot + 1 or currentId + 1
  local orderConfig = orderModel:GetOrderConfig()
  local nextConfig = orderConfig[nextId]
  if nextConfig == nil then
    local slotConfig = orderModel:GetSlotConfig()[slot]
    if slotConfig.LoopEntry == nil then
      return nil
    end
    nextConfig = orderConfig[slotConfig.LoopEntry]
  end
  if nextConfig.UnlockOrder ~= nil and not orderModel:IsOrderFinished(nextConfig.UnlockOrder) then
    return nil
  end
  return SpreeActivityOrderCreatorFixed.CreateWithData(orderModel, nextConfig)
end

function SpreeActivityOrderCreatorFixed.DBGetOrder(orderModel, slot)
  local dbTable = orderModel:GetData()
  local currentId = dbTable:GetValue(slot, SpreeActivityOrderCreatorFixed.CurrentIdKey)
  if currentId == nil then
    return nil
  end
  local finished = dbTable:GetValue(slot, SpreeActivityOrderCreatorFixed.FinishedKey)
  if finished == 1 then
    return nil
  end
  local config = orderModel:GetOrderConfig()[currentId]
  return SpreeActivityOrderCreatorFixed.CreateWithData(orderModel, config)
end

function SpreeActivityOrderCreatorFixed.DBAddOrder(orderModel, order)
  local addData = {
    [order:GetSlot()] = {
      [SpreeActivityOrderCreatorFixed.CurrentIdKey] = order:GetId(),
      [SpreeActivityOrderCreatorFixed.FinishedKey] = 0
    }
  }
  orderModel:GetData():BatchSet(addData)
end

function SpreeActivityOrderCreatorFixed.DBRemoveOrder(orderModel, slot)
  orderModel:GetData():Set(slot, SpreeActivityOrderCreatorFixed.FinishedKey, 1)
end

function SpreeActivityOrderCreatorFixed.CreateWithData(orderModel, config)
  local requirements = {
    config.Requirement1,
    config.Requirement2,
    config.Requirement3
  }
  local rewardStrings = {
    config.Reward1,
    config.Reward2
  }
  local rewards = ConfigUtil.GetCurrencyFromArrStr(rewardStrings)
  return SpreeActivityOrder.Create(config.Slot, config.Id, requirements, OrderType.Fixed, rewards, GM.GameModel:GetServerTime())
end

function SpreeActivityOrderCreatorFixed.IsOrderFinished(orderModel, orderId)
  local dbTable = orderModel:GetData()
  local slot = orderId // SpreeActivityOrderModel.ChainIdOffset
  local currentId = dbTable:GetValue(slot, SpreeActivityOrderCreatorFixed.CurrentIdKey) or 0
  if orderId < currentId then
    return true
  elseif orderId > currentId then
    return false
  else
    return dbTable:GetValue(slot, SpreeActivityOrderCreatorFixed.FinishedKey) == 1
  end
end

SpreeActivityOrderCreatorRandom = {}
SpreeActivityOrderCreatorRandom.RequirementKey = "Requirement%d"
SpreeActivityOrderCreatorRandom.StartTimerKey = "StartTimer%d"

function SpreeActivityOrderCreatorRandom.Create(orderModel, slot)
  local slotConfig = orderModel:GetSlotConfig()[slot]
  if slotConfig == nil then
    return nil
  end
  local metaData = orderModel:GetMetaData()
  local startTimerKey = string.format(SpreeActivityOrderCreatorRandom.StartTimerKey, slot)
  local startTimer = metaData:GetValue(startTimerKey, "value")
  local orderPoints = 0
  if startTimer == 0 then
    if orderPoints == 0 then
      metaData:Set(startTimerKey, "value", GM.GameModel:GetServerTime())
    end
  elseif startTimer == nil or GM.GameModel:GetServerTime() - startTimer >= slotConfig.RefreshTime then
    metaData:Set(startTimerKey, "value", 0)
  end
  if orderPoints == 0 then
    return nil
  end
  local availableItems = {}
  for _, config in pairs(orderModel:GetItemConfig()) do
    if Table.Contain(config.Slot, slot) and (config.UnlockOrder == nil or orderModel:IsOrderFinished(config.UnlockOrder)) then
      table.insert(availableItems, {
        Type = config.Type,
        Weight = config.Weight
      })
    end
  end
  if #availableItems == 0 then
    return nil
  end
  local requirementNumber = math.random(1, 2)
  local requirements = {}
  for i = 1, requirementNumber do
    local requirement = Table.ListWeightSelectOne(availableItems).Type
    table.insert(requirements, requirement)
  end
  return SpreeActivityOrderCreatorRandom.CreateWithData(orderModel, slot, requirements)
end

function SpreeActivityOrderCreatorRandom.DBGetOrder(orderModel, slot)
  local dbTable = orderModel:GetData()
  local requirement1Key = string.format(SpreeActivityOrderCreatorRandom.RequirementKey, 1)
  local requirement1 = dbTable:GetValue(slot, requirement1Key)
  if requirement1 == nil then
    return nil
  end
  local requirements = {}
  table.insert(requirements, requirement1)
  local requirement2Key = string.format(SpreeActivityOrderCreatorRandom.RequirementKey, 2)
  local requirement2 = dbTable:GetValue(slot, requirement2Key)
  table.insert(requirements, requirement2)
  return SpreeActivityOrderCreatorRandom.CreateWithData(orderModel, slot, requirements)
end

function SpreeActivityOrderCreatorRandom.DBAddOrder(orderModel, order)
  local requirement1Key = string.format(SpreeActivityOrderCreatorRandom.RequirementKey, 1)
  local requirement2Key = string.format(SpreeActivityOrderCreatorRandom.RequirementKey, 2)
  local addData = {
    [order:GetSlot()] = {
      [requirement1Key] = order:GetRequirements()[1],
      [requirement2Key] = order:GetRequirements()[2]
    }
  }
  orderModel:GetData():BatchSet(addData)
end

function SpreeActivityOrderCreatorRandom.DBRemoveOrder(orderModel, slot)
  orderModel:GetData():Remove(slot)
end

function SpreeActivityOrderCreatorRandom.CreateWithData(orderModel, slot, requirements)
  local itemConfig = orderModel:GetItemConfig()
  local coinCount = 0
  for _, requirement in ipairs(requirements) do
    local config = itemConfig[requirement]
    coinCount = coinCount + config.Coin
  end
  local rewards = {}
  if coinCount ~= 0 then
    table.insert(rewards, {
      [PROPERTY_TYPE] = EPropertyType.Gold,
      [PROPERTY_COUNT] = coinCount
    })
  end
  return SpreeActivityOrder.Create(slot, orderModel:GenerateOrderId(), requirements, OrderType.Random, rewards, GM.GameModel:GetServerTime())
end
