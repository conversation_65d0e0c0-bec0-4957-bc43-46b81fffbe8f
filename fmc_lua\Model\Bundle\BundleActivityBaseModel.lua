BundleActivityBaseModel = setmetatable({
  DataGroupClass = BundleServerConfigDataGroup
}, BundleBaseModel)
BundleActivityBaseModel.__index = BundleActivityBaseModel

function BundleActivityBaseModel:Init(bundleType, virtualDBTable)
  BundleBaseModel.Init(self, bundleType, virtualDBTable)
  self:_LoadGroupVirtualDBTable()
  self:_LoadLocalConfigs()
  self:UpdatePerSecond()
end

function BundleActivityBaseModel:_LoadGroupVirtualDBTable()
  self.m_mapGroupId2DBTable = {}
  for groupId, _ in pairs(self.m_dbTable:GetValues()) do
    self.m_mapGroupId2DBTable[groupId] = VirtualDBTable.Create(self.m_dbTable, groupId)
  end
end

function BundleActivityBaseModel:_LoadServerConfig(arrConfigs)
  self.m_arrConfigs = arrConfigs or Table.Empty
  self.m_bShouldRefreshLocalConfig = true
end

function BundleActivityBaseModel:LateInit()
  BundleBaseModel.LateInit(self)
  self:ClearExpiredDBDatas()
end

function BundleActivityBaseModel:ClearExpiredDBDatas()
  local checkValidFunc = function(groupId)
    for _, serverConfig in ipairs(self.m_arrConfigs or {}) do
      if groupId == serverConfig.groupId then
        return true
      end
    end
    if self:_HasLocalConfig(groupId) then
      return true
    end
    return false
  end
  local arrRemovedId = {}
  for groupId, _ in pairs(self.m_mapGroupId2DBTable) do
    if not checkValidFunc(groupId) then
      table.insert(arrRemovedId, groupId)
    end
  end
  for _, groupId in ipairs(arrRemovedId) do
    self:_DropBundleDBData(groupId)
  end
end

function BundleActivityBaseModel:UpdatePerSecond()
  BundleBaseModel.UpdatePerSecond(self)
  if not Table.IsEmpty(self.m_dataGroups) then
    for _, dataGroup in ipairs(self.m_dataGroups) do
      self:UpdateBundleState(dataGroup)
    end
  end
end

function BundleActivityBaseModel:UpdateBundleState(dataGroup, bTrigger)
  local state = self:_CalculateBundleState(dataGroup)
  local groupId = dataGroup:GetGroupId()
  if self["m_state" .. groupId] ~= state then
    self["m_state" .. groupId] = state
    self:OnBundleStateChanged(dataGroup, bTrigger)
  end
end

function BundleActivityBaseModel:_CalculateBundleState(dataGroup, bTrigger)
  if self:GetRestDuration(dataGroup) >= 0 and self:_IsGroupDataActive(dataGroup) then
    return EBundleState.Opening
  end
  return EBundleState.Close
end

function BundleActivityBaseModel:GetBundleState(dataGroup)
  local groupId = dataGroup:GetGroupId()
  return self["m_state" .. groupId]
end

function BundleActivityBaseModel:OnBundleStateChanged(dataGroup)
  self.m_bShouldRefreshLocalConfig = true
  EventDispatcher.DispatchEvent(EEventType.BundleDataRefreshed, {
    dataGroup = dataGroup,
    bundleType = self.m_bundleType
  })
end

function BundleActivityBaseModel:GetActiveGroupDatas()
  local arrGroupDatas = {}
  for _, dataGroup in ipairs(self.m_dataGroups or {}) do
    if self:GetBundleState(dataGroup) == EBundleState.Opening then
      arrGroupDatas[#arrGroupDatas + 1] = dataGroup
    end
  end
  return arrGroupDatas
end

function BundleActivityBaseModel:GetAllGroupDatas()
  return self.m_dataGroups
end

function BundleActivityBaseModel:GetBundleConfigData(eIAPType)
  local configData
  for _, dataGroup in ipairs(self.m_dataGroups) do
    for _, bundleId in ipairs(dataGroup:GetBundleIds()) do
      configData = dataGroup:GetConfigData(bundleId)
      if configData ~= nil and configData:GetPurchaseId() == eIAPType then
        return configData
      end
    end
  end
end

function BundleActivityBaseModel:GetMapEntryShowConfig()
  return {
    hudKey = self.m_bundleType,
    checkFun = function(dataGroup)
      return self:CanShowMapEntry(dataGroup)
    end
  }
end

function BundleActivityBaseModel:CanShowMapEntry(dataGroup)
  if self:GetBundleState(dataGroup) ~= EBundleState.Opening or not self:CanShowEntry(dataGroup) then
    return false
  end
  local uiDefinition = BundleUIType[dataGroup:GetBundleUIType()]
  if uiDefinition ~= nil and not Table.IsEmpty(uiDefinition.entryInfo) then
    for _, info in ipairs(uiDefinition.entryInfo) do
      if info.scene == EBundleEntryScene.Main then
        return true
      end
    end
    return false
  end
  return true
end

function BundleActivityBaseModel:GetActivityEntryShowConfig()
  return {
    checkFun = function(dataGroup, activityType)
      return self:CanShowActivityEntry(dataGroup, activityType)
    end
  }
end

function BundleActivityBaseModel:CanShowActivityEntry(dataGroup, activityType)
  if self:GetBundleState(dataGroup) ~= EBundleState.Opening or not self:CanShowEntry(dataGroup) then
    return false
  end
  local uiDefinition = BundleUIType[dataGroup:GetBundleUIType()]
  if uiDefinition ~= nil and not Table.IsEmpty(uiDefinition.entryInfo) then
    for _, info in ipairs(uiDefinition.entryInfo) do
      if info.scene == EBundleEntryScene.Activity and info.activity == activityType then
        return true
      end
    end
  end
  return false
end

function BundleActivityBaseModel:CanShowEntry(dataGroup)
  return true
end

function BundleActivityBaseModel:_TryRefreshLocalConfigs()
  if not self.m_bShouldRefreshLocalConfig then
    return
  end
  if not GM.CheckResourcesStageFinished and GameConfig.IsTestMode() then
    Log.Error("[Bundle] 不能在GM.CheckResourcesStageFinished之前调用RefreshLocalConfigs接口, 防止数据被误删")
  end
  self:UpdatePerSecond()
  for _, dataGroup in ipairs(self.m_dataGroups) do
    if self:GetBundleState(dataGroup) ~= EBundleState.Opening then
      self:_RemoveLocalConfig(dataGroup:GetGroupId())
      if self._OnRemoveLocalConfig then
        self:_OnRemoveLocalConfig(dataGroup)
      end
    end
  end
  for _, serverConfig in ipairs(self.m_arrConfigs) do
    if not self:_HasLocalConfig(serverConfig.groupId) then
      self:_AddLocalConfig(serverConfig.groupId, serverConfig)
    end
  end
  self:_LoadLocalConfigs()
  self.m_bShouldRefreshLocalConfig = false
end

function BundleActivityBaseModel:_LoadLocalConfigs()
  if self.DataGroupClass == nil then
    Log.Error("[BundleDataGroupsBaseModel:_LoadLocalConfigs] 没有定义DataGroupClass, bundleType=" .. self.m_bundleType)
  end
  local mapId2Config = self:_GetLocalConfigs()
  self.m_dataGroups = {}
  for _, config in pairs(mapId2Config) do
    local dataGroup = self.DataGroupClass.Create(config)
    self.m_dataGroups[#self.m_dataGroups + 1] = dataGroup
  end
  table.sort(self.m_dataGroups, function(a, b)
    return a:GetPopOrder() < b:GetPopOrder()
  end)
  self:UpdatePerSecond()
end

function BundleActivityBaseModel:OnPurchaseFinished(groupId, bundleId)
  BundleBaseModel.OnPurchaseFinished(self, groupId, bundleId)
  for _, dataGroup in ipairs(self.m_dataGroups) do
    if dataGroup:GetGroupId() == groupId then
      self:_OnPurchaseFinished(dataGroup, bundleId)
      break
    end
  end
  self:UpdatePerSecond()
end

function BundleActivityBaseModel:_OnPurchaseFinished(dataGroup, bundleId)
  self:_RecordBuyNumData(dataGroup)
  self:_FinishAndEnterBuyCD(dataGroup)
end

function BundleActivityBaseModel:_RecordBuyNumData(dataGroup)
  local groupId = dataGroup:GetGroupId()
  local curDay = GM.GameModel:GetServerDay()
  local lastBuyDay = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.LastBuyDay)) or 0
  if lastBuyDay ~= curDay then
    self:_RemoveBundleDBData(groupId, EBundleDBKey.DailyBuyNum)
    self:_SetBundleDBData(groupId, EBundleDBKey.LastBuyDay, tostring(curDay))
  end
  local dailyBuyNum = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.DailyBuyNum)) or 0
  self:_SetBundleDBData(groupId, EBundleDBKey.DailyBuyNum, tostring(dailyBuyNum + 1))
  if dataGroup:GetMaxBuyNum() then
    local maxNum = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.MaxBuyNum)) or 0
    self:_SetBundleDBData(groupId, EBundleDBKey.MaxBuyNum, tostring(maxNum + 1))
  end
end

function BundleActivityBaseModel:_FinishAndEnterBuyCD(dataGroup)
  local groupId = dataGroup:GetGroupId()
  local curTime = GM.GameModel:GetServerTime()
  self:_SetBundleDBData(groupId, EBundleDBKey.TriggerTime, tostring(curTime - dataGroup:GetLastDuration()))
end

function BundleActivityBaseModel:TryTriggerBundle(eTriggerType, triggerArg, bOpenImmediately)
  self:_TryRefreshLocalConfigs()
  for _, dataGroup in ipairs(self.m_dataGroups) do
    local bTriggerd, viewName = self:_TryTriggerBundle(dataGroup, eTriggerType, triggerArg, bOpenImmediately)
    if bTriggerd and viewName then
      return true, viewName
    end
  end
  return false
end

function BundleActivityBaseModel:_IsGroupDataActive(dataGroup)
  if dataGroup == nil then
    return false
  end
  local bundleConditionData = dataGroup:GetConditionData()
  if bundleConditionData and not bundleConditionData:IsGroupDataActive() then
    return false
  end
  return self:_IsGroupDataEligible(dataGroup)
end

function BundleActivityBaseModel:_IsGroupDataEligible(dataGroup)
  local groupId = dataGroup:GetGroupId()
  local curTime = GM.GameModel:GetServerTime()
  if dataGroup:GetBundleEndTime() ~= nil and curTime > dataGroup:GetBundleEndTime() then
    return false
  end
  local triggertime = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.TriggerTime)) or 0
  local weekConfig = dataGroup:GetWeekConfig()
  if curTime > triggertime + dataGroup:GetLastDuration() and weekConfig ~= nil then
    local curWeekDay = TimeUtil.ToDayofWeek(GM.GameModel:GetServerTime())
    if not Table.ListContain(weekConfig, curWeekDay) then
      return false
    end
  end
  local groupId = dataGroup:GetGroupId()
  local curTime = GM.GameModel:GetServerTime()
  local triggertime = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.TriggerTime)) or 0
  local curBuyNum = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.MaxBuyNum)) or 0
  local maxBuyNum = dataGroup:GetMaxBuyNum()
  if maxBuyNum ~= nil and curBuyNum >= maxBuyNum then
    return false
  end
  local buyCD = dataGroup:GetBuyCD()
  if buyCD ~= nil and curTime >= triggertime + dataGroup:GetLastDuration() and curTime < triggertime + dataGroup:GetLastDuration() + buyCD then
    return false
  end
  local maxDailyBuyNum = dataGroup:GetDailyBuyNum()
  if maxDailyBuyNum == 0 then
    return false
  end
  local curDay = GM.GameModel:GetServerDay()
  local lastBuyDay = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.LastBuyDay)) or 0
  local dailyCurBuyNum = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.DailyBuyNum)) or 0
  if lastBuyDay == curDay and maxDailyBuyNum ~= nil and maxDailyBuyNum <= dailyCurBuyNum then
    return false
  end
  return true
end

function BundleActivityBaseModel:IsGroupDataActive(dataGroup)
  return self:_IsGroupDataActive(dataGroup)
end

function BundleActivityBaseModel:_TryTriggerBundle(dataGroup, eTriggerType, triggerArg, bOpenImmediately)
  if dataGroup == nil or not dataGroup:CanTrigger(eTriggerType, triggerArg, self) then
    return false
  end
  if bOpenImmediately == nil then
    bOpenImmediately = true
  end
  if self:_IsGroupDataActive(dataGroup) then
    self:_OnTriggerBundleSuccess(dataGroup, false, eTriggerType)
    if self:_CanTriggerBundleWindow(dataGroup, eTriggerType) then
      local viewName = self:OpenBundleView(dataGroup, bOpenImmediately, false, eTriggerType)
      self:_OnTriggerBundleSuccess(dataGroup, true, eTriggerType)
      return true, viewName
    end
    return true
  end
  return false
end

function BundleActivityBaseModel:_OnTriggerBundleSuccess(dataGroup, bFromWindow, eTriggerType)
  local groupId = dataGroup:GetGroupId()
  local curDay = GM.GameModel:GetServerDay()
  local curTime = GM.GameModel:GetServerTime()
  local triggerTime = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.TriggerTime)) or 0
  if curTime > triggerTime + dataGroup:GetLastDuration() then
    self:_SetBundleDBData(groupId, EBundleDBKey.TriggerTime, tostring(curTime))
    triggerTime = curTime
  end
  local triggerDay = triggerTime // 86400
  local lastShowDay = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.LastShowDay)) or 0
  if bFromWindow or triggerDay > lastShowDay then
    if lastShowDay ~= curDay then
      self:_SetBundleDBData(groupId, EBundleDBKey.LastShowDay, tostring(curDay))
      self:_SetBundleDBData(groupId, EBundleDBKey.DailyShowNum, bFromWindow and "1" or "0")
    elseif dataGroup:GetDailyShowNum() then
      local dailyShowNum = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.DailyShowNum)) or 0
      self:_SetBundleDBData(groupId, EBundleDBKey.DailyShowNum, tostring(dailyShowNum + 1))
    end
    if eTriggerType ~= nil and bFromWindow then
      self:_SetTriggerDailyNum(self:_GetTriggerDailyNum(eTriggerType, dataGroup:GetGroupId(), true) + 1, eTriggerType, dataGroup:GetGroupId())
    end
    if bFromWindow then
      self:_SetBundleDBData(groupId, EBundleDBKey.LastPopTime, tostring(curTime))
    end
  end
  local lastBuyDay = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.LastBuyDay)) or 0
  if lastBuyDay ~= curDay then
    self:_RemoveBundleDBData(groupId, EBundleDBKey.LastBuyDay)
  end
  self:UpdateBundleState(dataGroup, true)
end

function BundleActivityBaseModel:RefreshBundleWindowPopupTime(dataGroup)
  local groupId = dataGroup:GetGroupId()
  self:_SetBundleDBData(groupId, EBundleDBKey.LastPopTime, tostring(GM.GameModel:GetServerTime()))
end

function BundleActivityBaseModel:_CanTriggerBundleWindow(dataGroup, eTriggerType)
  local groupId = dataGroup:GetGroupId()
  local curTime = GM.GameModel:GetServerTime()
  if dataGroup:GetPopCD() then
    local lastPopTime = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.LastPopTime)) or 0
    if curTime < lastPopTime + dataGroup:GetPopCD() then
      return false
    end
  end
  local curDay = GM.GameModel:GetServerDay()
  local lastShowDay = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.LastShowDay)) or 0
  if lastShowDay ~= curDay then
    return true
  end
  local dailyShowNum = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.DailyShowNum)) or 0
  if dataGroup:GetDailyShowNum() and dailyShowNum >= dataGroup:GetDailyShowNum() then
    return false
  end
  if not dataGroup:CanPopup(eTriggerType, self:_GetTriggerDailyNum(eTriggerType, dataGroup:GetGroupId(), true)) then
    return false
  end
  local bundleUIType = dataGroup:GetBundleUIType()
  local window = bundleUIType ~= nil and BundleUIType[bundleUIType] ~= nil and BundleUIType[bundleUIType].window or nil
  if window ~= nil and (GM.UIManager:IsViewExisting(window) or BundlePopupHelper.IsViewExisting(window)) then
    return false
  end
  return true
end

function BundleActivityBaseModel:OpenBundleView(dataGroup, bOpenImmediately, bUserClick, triggerType)
  local bundleUIType = dataGroup:GetBundleUIType()
  if not (bundleUIType and BundleUIType[bundleUIType]) or not BundleUIType[bundleUIType].window then
    if GameConfig.IsTestMode() then
      Log.Error("BundleActivityModel:OpenBundleView no bundleUIType for" .. dataGroup:GetGroupId())
    end
    return nil
  end
  if bOpenImmediately then
    GM.UIManager:OpenView(BundleUIType[bundleUIType].window, self.m_bundleType, dataGroup, bUserClick, triggerType)
  else
    BundlePopupHelper.AddWindowToPopupChainWithoutRepeat(BundleUIType[bundleUIType].window, self.m_bundleType, dataGroup, bUserClick, triggerType)
  end
  return BundleUIType[bundleUIType].window
end

function BundleActivityBaseModel:GetShowBuyLimitNum(dataGroup)
  local dailyLimit = dataGroup:GetDailyBuyNum()
  local maxNum = dataGroup:GetMaxBuyNum()
  local groupId = dataGroup:GetGroupId()
  local boughtNum, limitNum
  local maxBoughtNum = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.MaxBuyNum)) or 0
  if dailyLimit then
    local curDay = GM.GameModel:GetServerDay()
    local lastBuyDay = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.LastBuyDay)) or 0
    if lastBuyDay ~= curDay then
      boughtNum = 0
    else
      boughtNum = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.DailyBuyNum)) or 0
    end
    if not maxNum then
      limitNum = dailyLimit
    else
      limitNum = math.min(dailyLimit, maxNum - maxBoughtNum + boughtNum)
    end
  else
    boughtNum = maxBoughtNum
    limitNum = maxNum
  end
  return boughtNum, limitNum
end

function BundleActivityBaseModel:GetRestDuration(dataGroup)
  local triggerEndTime = self:GetBundleTriggerEndTime(dataGroup)
  local restDuration = triggerEndTime - GM.GameModel:GetServerTime()
  return restDuration
end

function BundleActivityBaseModel:GetBundleTriggerStartTime(dataGroup)
  return self:_GetBundleDBData(dataGroup:GetGroupId(), EBundleDBKey.TriggerTime)
end

function BundleActivityBaseModel:GetBundleTriggerEndTime(dataGroup)
  local bundleConditionData = dataGroup:GetConditionData()
  local triggertime = tonumber(self:_GetBundleDBData(dataGroup:GetGroupId(), EBundleDBKey.TriggerTime)) or 0
  local bundleEndTime = dataGroup:GetBundleEndTime() or math.maxinteger
  local activityEndTime = math.maxinteger
  if bundleConditionData ~= nil and bundleConditionData:GetRecentActivityEndTime() ~= nil then
    activityEndTime = bundleConditionData:GetRecentActivityEndTime()
  end
  return math.min(triggertime + dataGroup:GetLastDuration(), bundleEndTime, activityEndTime)
end

function BundleActivityBaseModel:_GetTriggerDailyNum(eTriggerType, groupId, bRefresh)
  if bRefresh then
    self:TryRefreshTriggerDailyNum(groupId)
  end
  local dbKey = StringUtil.Format(EBundleDBKey.TriggerDailyShowNum, eTriggerType)
  return tonumber(self:_GetBundleDBData(groupId, dbKey)) or 0
end

function BundleActivityBaseModel:_SetTriggerDailyNum(num, eTriggerType, groupId)
  local dbKey = StringUtil.Format(EBundleDBKey.TriggerDailyShowNum, eTriggerType)
  self:_SetBundleDBData(groupId, dbKey, num)
end

function BundleActivityBaseModel:TryRefreshTriggerDailyNum(groupId)
  local lastDay = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.TriggerLastShowDay)) or 0
  local curDay = GM.GameModel:GetServerDay()
  if lastDay < curDay then
    self:_ResetAllTriggerDailyNum(groupId)
    self:_SetBundleDBData(groupId, EBundleDBKey.TriggerLastShowDay, tostring(curDay))
  end
end

function BundleActivityBaseModel:_ResetAllTriggerDailyNum(groupId)
  for _, triggerType in pairs(EBundleTriggerType) do
    if self:_GetTriggerDailyNum(triggerType, groupId) ~= 0 then
      self:_SetTriggerDailyNum(0, triggerType, groupId)
    end
  end
end

function BundleActivityBaseModel:GetTargetPopOrderConfig(triggerType)
  self:_TryRefreshLocalConfigs()
  local popOrderInfo
  for _, dataGroup in ipairs(self.m_dataGroups or {}) do
    popOrderInfo = dataGroup:GetPopOrderConfig(triggerType)
    if popOrderInfo ~= nil then
      return popOrderInfo
    end
  end
end

function BundleActivityBaseModel:GetAllPopOrderConfig(eTriggerType)
  local result = {}
  local popOrderInfo
  for _, dataGroup in ipairs(self.m_dataGroups or {}) do
    popOrderInfo = dataGroup:GetPopOrderConfig(eTriggerType)
    if popOrderInfo ~= nil then
      table.insert(result, popOrderInfo)
    end
  end
  return result
end

function BundleActivityBaseModel:_GetLocalConfigs()
  local mapId2Config = {}
  local strConfig
  for groupId, dbTable in pairs(self.m_mapGroupId2DBTable) do
    strConfig = dbTable:GetValue(EBundleDBKey.LockedConfig, BundleColumnValue)
    if not StringUtil.IsNilOrEmpty(strConfig) then
      mapId2Config[groupId] = json.decode(StringUtil.Replace(strConfig, "@", ","))
    end
  end
  return mapId2Config
end

function BundleActivityBaseModel:_RemoveLocalConfig(groupId)
  self:_RemoveBundleDBData(groupId, EBundleDBKey.LockedConfig)
end

function BundleActivityBaseModel:_AddLocalConfig(groupId, config)
  self:_SetBundleDBData(groupId, EBundleDBKey.LockedConfig, StringUtil.Replace(json.encode(config), ",", "@"))
end

function BundleActivityBaseModel:_HasLocalConfig(groupId)
  return self:_GetBundleDBData(groupId, EBundleDBKey.LockedConfig) ~= nil
end

function BundleActivityBaseModel:_GetBundleDBData(groupId, dbKey)
  local dbTable = self.m_mapGroupId2DBTable[groupId]
  if dbTable == nil then
    return nil
  end
  return dbTable:GetValue(dbKey, BundleColumnValue)
end

function BundleActivityBaseModel:_SetBundleDBData(groupId, dbKey, value)
  local dbTable = self.m_mapGroupId2DBTable[groupId]
  if dbTable == nil then
    dbTable = VirtualDBTable.Create(self.m_dbTable, groupId)
    self.m_mapGroupId2DBTable[groupId] = dbTable
  end
  dbTable:Set(dbKey, BundleColumnValue, value)
end

function BundleActivityBaseModel:_RemoveBundleDBData(groupId, dbKey)
  local dbTable = self.m_mapGroupId2DBTable[groupId]
  if dbTable ~= nil then
    dbTable:Remove(dbKey)
  end
end

function BundleActivityBaseModel:_DropBundleDBData(groupId)
  local dbTable = self.m_mapGroupId2DBTable[groupId]
  if dbTable ~= nil then
    dbTable:Drop()
    self.m_mapGroupId2DBTable[groupId] = nil
  end
end
