BreakEggSuccessWindow = setmetatable({}, BreakEggBaseWindow)
BreakEggSuccessWindow.__index = BreakEggSuccessWindow

function BreakEggSuccessWindow:Init()
  BreakEggBaseWindow.Init(self)
  local completeReward = self.m_activityModel:GetLastCompleteReward()
  if not Table.IsEmpty(completeReward) then
    local img = self.m_activityModel:GetHighImg(completeReward[PROPERTY_TYPE]) or ConfigUtil.GetCurrencyImageName(completeReward)
    self.m_numText.text = "X" .. completeReward[PROPERTY_COUNT]
    self.m_iconImg.gameObject.transform.localScale = Vector3.zero
    self.m_buttonRectTrans.localScale = Vector3.zero
    SpriteUtil.SetImage(self.m_iconImg, img, nil, function()
      self.m_iconImg.gameObject:SetActive(true)
      DOVirtual.DelayedCall(0.4, function()
        self.m_iconImg.gameObject.transform:DOScale(1.0, 0.5):SetEase(Ease.OutBack)
        DOVirtual.DelayedCall(0.4, function()
          self.m_buttonRectTrans:DOScale(1.0, 0.5):SetEase(Ease.OutBack)
        end)
      end)
    end)
  end
  UIUtil.UpdateSortingOrder(self.m_effect_yanhuaGo, self:GetSortingOrder() + 1)
  GM.AudioModel:PlayEffect(AudioFileConfigName.sfxBreakEggFin)
  self.m_arrReward = self.m_activityModel:GetCollectRewards()
  local completeReward = self.m_activityModel:GetLastCompleteReward()
  if not Table.IsEmpty(completeReward) then
    self.m_arrReward[#self.m_arrReward + 1] = completeReward
  end
end

function BreakEggSuccessWindow:OnBack()
  self:Close()
end

function BreakEggSuccessWindow:_OnCollectClicked()
  self:Close()
end

function BreakEggSuccessWindow:Close()
  local callback = function()
    if self.m_activityModel:IsActivityOpen() then
      local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BreakEggMainWindow)
      if mainWindow then
        mainWindow:DisPlayStart()
      end
    end
  end
  GM.UIManager:OpenView(UIPrefabConfigName.BreakEggRewardWIndow, self.m_arrReward, "break_egg_reward_title", false, nil, callback)
  BreakEggBaseWindow.Close(self)
  self.m_activityModel:ResetData()
end
