EnergyBoostSettingWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BaseWindow)
EnergyBoostSettingWindow.__index = EnergyBoostSettingWindow

function EnergyBoostSettingWindow:BeforeOpenCheck()
  return GM.EnergyBoostModel:IsEnergyBoostConfigOn()
end

function EnergyBoostSettingWindow:Init(bAuto)
  self:InitContent()
  self:UpdateContent()
  self:LogWindowAction(EBIType.UIActionType.Open, {
    bAuto and EBIReferType.AutoPopup or EBIReferType.UserClick
  })
  EventDispatcher.AddListener(EEventType.EnergyBoostModeChanged, self, self.OnEnergyBoostModeChanged)
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = true,
    hudKey = ESceneViewHudButtonKey.Energy
  })
end

function EnergyBoostSettingWindow:OnD<PERSON>roy()
  EventDispatcher.RemoveTarget(self)
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = false,
    hudKey = ESceneViewHudButtonKey.Energy
  })
end

function EnergyBoostSettingWindow:InitContent()
  local eConfigBoostType = GM.EnergyBoostModel:GetTriggerBoostType()
  local triggerCount = GM.EnergyBoostModel:GetTriggerCount(eConfigBoostType) or 9999
  local nConfigCostNum = GM.EnergyBoostModel:GetEnergyBoostCostNum(eConfigBoostType)
  self.m_descText.text = GM.GameTextModel:GetText("energy_boost_window_desc_" .. nConfigCostNum, triggerCount)
  self.m_spine:Init()
  self.m_spine:SetAnimation("appear", false)
  self.m_spine:AddAnimation("idle", true)
  for i = 1, 5 do
    self["m_frontSpine" .. i]:Init()
    self["m_backSpine" .. i]:Init()
  end
  self:_CheckShowCountDown()
  local arrFadeInDelay = {
    0,
    0.1,
    0,
    0.06666666666666667,
    0.13333333333333333
  }
  for i = 1, 5 do
    self:_LoadPdImage(i, arrFadeInDelay[i])
  end
end

function EnergyBoostSettingWindow:OnEnergyBoostModeChanged()
  if not GM.EnergyBoostModel:IsEnergyBoostConfigOn() then
    self:Close()
    return
  end
  self:_CheckShowCountDown()
end

function EnergyBoostSettingWindow:OnButtonClick()
  GM.EnergyBoostModel:SwitchUserBoostType()
  local curOn = GM.EnergyBoostModel:GetUserBoostType() >= EEnergyBoostType.None
  GM.AudioModel:PlayEffect(curOn and AudioFileConfigName.sfxEnergyBoostOn or AudioFileConfigName.sfxEnergyBoostOff)
  self:UpdateContent()
end

function EnergyBoostSettingWindow:_CheckShowCountDown()
  self.m_countdownGo:SetActive(GM.EnergyBoostModel:IsTimeLimitedOn())
end

local arrDelayTime = {
  0,
  0.3,
  0.6,
  0.2,
  0.4
}

function EnergyBoostSettingWindow:_ToggleItemSpine(isOn)
  Scheduler.UnscheduleTarget(self)
  for i = 1, 5 do
    local frontSpine = self["m_frontSpine" .. i]
    local backSpine = self["m_backSpine" .. i]
    if isOn then
      local delay = arrDelayTime[i]
      if not self.m_bHasEntered then
        delay = delay + 0.6
      end
      DelayExecuteFuncInView(function()
        frontSpine.gameObject:SetActive(true)
        backSpine.gameObject:SetActive(true)
        frontSpine:SetAnimation("xg", true)
        backSpine:SetAnimation("xg", true)
      end, delay, self, false)
    else
      frontSpine.gameObject:SetActive(false)
      backSpine.gameObject:SetActive(false)
    end
  end
  self.m_bHasEntered = true
end

function EnergyBoostSettingWindow:_LoadPdImage(index, fadeInDelay)
  local imageCmp = self["m_pdImg" .. index]
  local canvasGroupCmp = self["m_canvasGroup" .. index]
  if not imageCmp or not canvasGroupCmp then
    return
  end
  local itemDataModel = GM.ItemDataModel
  local maxUnlockedLevel = 4
  local chain = "pd_" .. index
  local chainMaxLevel = itemDataModel:GetChainMaxLevel(chain)
  for level = maxUnlockedLevel + 1, chainMaxLevel do
    if itemDataModel:IsUnlocked(chain .. "_" .. level) then
      maxUnlockedLevel = level
    else
      break
    end
  end
  local imageKey = chain .. "_" .. maxUnlockedLevel
  SpriteUtil.SetImage(imageCmp, ImageFileConfigName[imageKey], true)
  canvasGroupCmp:DOFade(1, 0.1):SetDelay(fadeInDelay or 0)
end

function EnergyBoostSettingWindow:UpdateContent()
  self.m_centerText.text = "x" .. GM.EnergyBoostModel:GetEnergyBoostCostNum()
  self:_UpdateButton()
  self:_UpdateTip()
  self:_UpdateSpine()
  local s = DOTween.Sequence()
  s:Append(self.m_centerText.transform:DOScale(1.5, 0.1))
  s:Append(self.m_centerText.transform:DOScale(1, 0.1))
  self:_ToggleItemSpine(GM.EnergyBoostModel:GetUserBoostType() > EEnergyBoostType.None)
  self:UpdatePerSecond()
end

function EnergyBoostSettingWindow:_UpdateTip()
  local nCostNum = GM.EnergyBoostModel:GetEnergyBoostCostNum()
  if nCostNum == 1 then
    self.m_tipGo:SetActive(false)
  else
    self.m_tipGo:SetActive(true)
    self.m_tipImg.sprite = self["m_tipSpritex" .. nCostNum]
  end
end

function EnergyBoostSettingWindow:_UpdateButton()
  local nCostNum = GM.EnergyBoostModel:GetEnergyBoostCostNum()
  self.m_buttonx1:SetActive(nCostNum == 1)
  self.m_buttonx2:SetActive(nCostNum == 2)
  self.m_buttonx4:SetActive(nCostNum == 4)
end

function EnergyBoostSettingWindow:_UpdateSpine()
  local nUserCostNum = GM.EnergyBoostModel:GetEnergyBoostCostNum()
  local skinName = tostring(nUserCostNum)
  self.m_spine:SetSkin(skinName)
  for i = 1, 5 do
    local frontSpine = self["m_frontSpine" .. i]
    local backSpine = self["m_backSpine" .. i]
    frontSpine:SetSkin(skinName)
    backSpine:SetSkin(skinName)
  end
end

function EnergyBoostSettingWindow:UpdatePerSecond()
  self.m_countdownText.text = TimeUtil.ParseTimeDescription(GM.EnergyBoostModel:GetTimeLimitedLeftTime(), nil, nil, false)
end

function EnergyBoostSettingWindow:GetToggleRectTrans()
  return self.m_buttonx1.transform
end

function EnergyBoostSettingWindow:GetCloseBtnTrans()
  return self.m_closeBtnGo.transform
end
