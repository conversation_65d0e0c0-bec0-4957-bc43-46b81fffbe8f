ItemLockerView = setmetatable({}, BaseItemViewComponent)
ItemLockerView.__index = ItemLockerView

function ItemLockerView:Init(itemLockerModel, camera)
  self.m_model = itemLockerModel
  self.m_canvas.worldCamera = camera
  self.m_originPos = V3Zero
  self.m_model:HideItems()
  AddHandlerAndRecordMap(self.m_model.event, ItemLockerEventType.Unlock, {
    obj = self,
    method = self._TryUnlock
  })
  AddHandlerAndRecordMap(self.m_model.event, ItemLockerEventType.Tap, {
    obj = self,
    method = self.OnAreaClicked
  })
  if itemLockerModel.width < 3 then
    if itemLockerModel.startPosition:GetX() < 2 then
      UIUtil.SetAnchoredPosition(self.m_popupTextTrans, 70)
    elseif itemLockerModel.startPosition:GetX() >= 6 then
      UIUtil.SetAnchoredPosition(self.m_popupTextTrans, -70)
    end
  end
  self.popupLabel.text = GM.GameTextModel:GetText("hint_board_unlock_day", self.m_model.unlockDay)
  self.m_uiRoot:SetActive(false)
  for k = 0, self.m_effectsGo.transform.childCount - 1 do
    self.m_effectsGo.transform:GetChild(k).gameObject:SetActive(false)
  end
  self.m_effectsGo:SetActive(false)
  self.m_shadowGo:SetActive(false)
  if self.m_animator ~= nil then
    self.m_animator.enabled = false
  end
  self:HidePopup()
  self:_TryUnlock()
end

function ItemLockerView:SetItemView(itemView)
  BaseItemViewComponent.SetItemView(self, itemView)
  self.transform.localPosition = itemView.transform.localPosition
  self.m_originPos = Vector3(0, 0, -itemView.transform.localPosition.z + 7)
  self.m_uiRoot.transform.localPosition = self.m_originPos
end

function ItemLockerView:OnDestroy()
  RemoveAllHandlers(self.m_model.event, self)
  EventDispatcher.RemoveTarget(self)
  if self.m_popTween ~= nil then
    self.m_popTween:Kill()
    self.m_popTween = nil
  end
  if self.m_unlockTween ~= nil then
    self.m_unlockTween:Kill()
    self.m_unlockTween = nil
  end
end

function ItemLockerView:_TryUnlock()
  if not self.m_model.unlocked or self.m_bUnlocked then
    return
  end
  self.m_bUnlocked = true
  self:HidePopup()
  self:OnUnlockClicked()
end

function ItemLockerView:OnAreaClicked()
  if self.m_model.unlocked or self.m_bPoped then
    return
  end
  self:ShowPopup()
end

function ItemLockerView:ShowPopup()
  self.m_bPoped = true
  self.m_uiRoot.transform.localPosition = self.m_originPos + Vector3(0, 0, -1)
  self.m_uiRoot:SetActive(true)
  if self.m_popTween then
    self.m_popTween:Kill()
  end
  local transform = self.m_popupCanvas.transform
  local showTween = DOTween.Sequence()
  showTween:Append(transform:DOScale(1.03, 0.15))
  showTween:Join(self.m_popupCanvas:DOFade(1, 0.15))
  showTween:Append(transform:DOScale(1.0, 0.07))
  showTween:OnComplete(function()
    self.m_popTween = nil
  end)
  self.m_popTween = showTween
  DelayExecuteFuncInView(function()
    self:HidePopup()
  end, 2, self)
end

function ItemLockerView:HidePopup()
  self.m_bPoped = false
  if self.m_popTween then
    self.m_popTween:Kill()
  end
  local transform = self.m_popupCanvas.transform
  local showTween = DOTween.Sequence()
  showTween:Append(transform:DOScale(0, 0.15))
  showTween:Join(self.m_popupCanvas:DOFade(0.0, 0.15))
  showTween:OnComplete(function()
    self.m_uiRoot:SetActive(false)
    self.m_uiRoot.transform.localPosition = self.m_originPos
    self.m_popTween = nil
  end)
  self.m_popTween = showTween
end

function ItemLockerView:OnUnlockClicked()
  self.m_itemView:RemoveComponent(self)
  self.m_effectsGo:SetActive(true)
  self.m_shadowGo:SetActive(true)
  local interval = 1.6
  if self.m_animator ~= nil then
    local spriteRenderers = self.gameObject:GetComponentsInChildren(typeof(SpriteRenderer), true)
    for i = 0, spriteRenderers.Length - 1 do
      spriteRenderers[i].sortingOrder = 20
    end
    self.m_animator.enabled = true
  else
    interval = 0
    self.gameObject:SetActive(false)
  end
  GM.UIManager:SetEventLock(true)
  local tween = DOTween.Sequence()
  tween:AppendInterval(interval)
  tween:AppendCallback(function()
    self.m_model:ShowItems()
    GM.UIManager:SetEventLock(false)
  end)
  tween:AppendInterval(2)
  tween:AppendCallback(function()
    self.m_unlockTween = nil
    self.gameObject:RemoveSelf()
  end)
  self.m_unlockTween = tween
  GM.AudioModel:PlayEffect(AudioFileConfigName.sfxMergeArea)
end

function ItemLockerView:OnDisable()
  if self.m_unlockTween ~= nil then
    self.m_unlockTween:Kill()
    self.m_unlockTween = nil
    self.m_model:ShowItems()
    self.gameObject:RemoveSelf()
  end
end
