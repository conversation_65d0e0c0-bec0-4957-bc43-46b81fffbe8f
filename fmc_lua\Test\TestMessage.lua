TestMessage = {DEFAULT_RETRY = 2}
TestMessage.__index = TestMessage
local Serialization = require("Test.TestInterface")

function TestMessage.ClearData(callback)
  if not GameConfig.IsTestMode() then
    return
  end
  local schema = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema)
  local userid = GM.UserModel:GetUserId()
  local server = NetworkConfig.GetSelectedServer()
  local url = "https://" .. server .. ".global.corp.merge.fun/server/clear_user_data?userid=" .. userid
  if not StringUtil.IsNilOrEmpty(schema) then
    url = url .. "&schema=" .. schema
  end
  GM.UIManager:ShowMask()
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(url, "GET", 8000, 0)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetCallback(function()
    if GM ~= nil then
      GM.UIManager:HideMask()
      if reqCtx.Rcode == ResultCode.Succeeded then
        callback(true, {rcode = 0})
      else
        GM.UIManager:ShowPrompt("清除服务器数据失败！")
      end
    end
  end)
  reqCtx:Send()
end

local Base64 = require("Model.Network.Base64")

function TestMessage.GetServerBackupData(schema, callback)
  if not GameConfig.IsTestMode() then
    callback(false)
  end
  local userid = GM.UserModel:GetUserId()
  local server = NetworkConfig.GetSelectedServer()
  local url = "https://" .. server .. ".global.corp.merge.fun/server/get_backup_data?userid=" .. userid
  if not StringUtil.IsNilOrEmpty(schema) then
    url = url .. "&schema=" .. schema
  end
  GM.UIManager:ShowMask()
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(url, "GET", 8000, 0)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetCallback(function()
    if GM ~= nil then
      GM.UIManager:HideMask()
      if reqCtx.Rcode == ResultCode.Succeeded then
        local arrDesc = json.decode(reqCtx:GetResponseString() or "{}") or {}
        for _, desc in ipairs(arrDesc) do
          arrDesc[_] = Base64.decode(desc)
        end
        callback(true, arrDesc)
      else
        GM.UIManager:ShowPrompt("获取备份数据失败！")
      end
    end
  end)
  reqCtx:Send()
end

function TestMessage.UseBackupData(src_schema, key, callback)
  if not GameConfig.IsTestMode() then
    callback(false)
  end
  local schema = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema)
  local userid = GM.UserModel:GetUserId()
  local server = NetworkConfig.GetSelectedServer()
  local url = "https://" .. server .. ".global.corp.merge.fun/server/use_backup_data?userid=" .. userid .. "&desc=" .. Base64.encodeAndReplace(key)
  if not StringUtil.IsNilOrEmpty(schema) then
    url = url .. "&schema=" .. schema
  end
  if not StringUtil.IsNilOrEmpty(src_schema) then
    url = url .. "&src_schema=" .. src_schema
  end
  GM.UIManager:ShowMask()
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(url, "GET", 8000, 0)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetCallback(function()
    if GM ~= nil then
      GM.UIManager:HideMask()
      if reqCtx.Rcode == ResultCode.Succeeded then
        callback(true, {rcode = 0})
      else
        GM.UIManager:ShowPrompt("应用备份数据失败！")
      end
    end
  end)
  reqCtx:Send()
end

function TestMessage.UploadBackupData(schema, key, callback)
  if not GameConfig.IsTestMode() then
    callback(false)
  end
  local userid = GM.UserModel:GetUserId()
  local server = NetworkConfig.GetSelectedServer()
  local url = "https://" .. server .. ".global.corp.merge.fun/server/update_backup_data?userid=" .. userid .. "&desc=" .. Base64.encodeAndReplace(key)
  if not StringUtil.IsNilOrEmpty(schema) then
    url = url .. "&schema=" .. schema
  end
  GM.UIManager:ShowMask()
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(url, "GET", 8000, 0)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetCallback(function()
    if GM ~= nil then
      GM.UIManager:HideMask()
      if reqCtx.Rcode == ResultCode.Succeeded then
        callback(true, {rcode = 0})
      end
    end
  end)
  reqCtx:Send()
end

function TestMessage.DeleteBackupData(schema, key, callback)
  if not GameConfig.IsTestMode() then
    callback(false)
  end
  local userid = GM.UserModel:GetUserId()
  local server = NetworkConfig.GetSelectedServer()
  local url = "https://" .. server .. ".global.corp.merge.fun/server/delete_backup_data?desc=" .. Base64.encodeAndReplace(key)
  if not StringUtil.IsNilOrEmpty(schema) then
    url = url .. "&schema=" .. schema
  end
  GM.UIManager:ShowMask()
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(url, "GET", 8000, 0)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetCallback(function()
    if GM ~= nil then
      GM.UIManager:HideMask()
      if reqCtx.Rcode == ResultCode.Succeeded then
        callback(true, {rcode = 0})
      end
    end
  end)
  reqCtx:Send()
end

function TestMessage.Serialize(reqCtx, strOpName, tbMsgReq)
  local writer = CSNetLibManager:CreateBufferBlockWriter(reqCtx.ReqBody)
  Serialization.MessageHeader.Serialize(writer, {
    ProtocolMd5 = Serialization.ProtocolMd5,
    MessageId = reqCtx.MessageId,
    Operation = strOpName
  })
  local serializer = Serialization.reqNameMap[strOpName]
  local result = serializer.Serialize(writer, tbMsgReq)
  CSNetLibManager:ReleaseBufferBlockWriter(writer)
  return result
end

function TestMessage.Deserialize(reqCtx, strOpName)
  local reader = CSNetLibManager:CreateBufferBlockReader(reqCtx.RespBody)
  local deserializer = Serialization.respNameMap[strOpName]
  local bRet, body
  bRet, body = deserializer.Deserialize(reader)
  local left = reader:GetLeftLength()
  CSNetLibManager:ReleaseBufferBlockReader(reader)
  if not bRet or left ~= 0 then
    return nil
  end
  return body
end

function TestMessage.SendByHttp(strOpName, tbMsgReq, callback, uTimeout, uRetryCount)
  GM.UIManager:ShowMask()
  local strUrl = NetworkConfig.GetHttpServerUrl(strOpName)
  local reqCtx = CSNetLibManager:CreateApiServerHttpRequest(GM.UserModel:GetUserId(), GM.HttpManager:GetServerTime(), strUrl, uTimeout or 8000, uRetryCount or TestMessage.DEFAULT_RETRY)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader())
  reqCtx:SetRetryHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader(2))
  reqCtx:SetHeader(NetworkConfig.AcceptEncodingKey, "gzip, deflate")
  local strToken = GM.SsoManager:GetToken()
  local session = GM.SsoManager:GetSession()
  reqCtx:SetHeader(NetworkConfig.TokenHeaderKey, strToken)
  if GameConfig.IsTestMode() then
    local schema = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema, "")
    if not StringUtil.IsNilOrEmpty(schema) then
      reqCtx:SetHeader(NetworkConfig.SchemaHeaderKey, schema)
    end
  end
  if not TestMessage.Serialize(reqCtx, strOpName, tbMsgReq) then
    Log.Warning("TestMessage Serialize failed for " .. strOpName)
  end
  local headers, tbMsgResp
  reqCtx:SetCheckResponse(function()
    if GM == nil then
      return nil
    end
    headers = GM.HttpManager:ConvertHeaders(reqCtx.ResponseHeaders)
    if headers["process-time"] == nil then
      return "Missing process-time header"
    end
    if math.floor(reqCtx.Status / 100) == 2 then
      tbMsgResp = TestMessage.Deserialize(reqCtx, strOpName)
      if tbMsgResp == nil then
        return "Failed to deserialize response"
      end
    end
    return nil
  end)
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    GM.UIManager:HideMask()
    if reqCtx.Rcode == ResultCode.Succeeded then
      local strToken = headers.token
      if not StringUtil.IsNilOrEmpty(strToken) then
        GM.SsoManager:SetToken(strToken)
      end
      if callback ~= nil then
        callback(true, tbMsgResp)
      end
      return
    end
    if reqCtx.Rcode == ResultCode.Not2xx then
      if reqCtx.Status == 401 or reqCtx.Status == 403 then
        reqCtx:Retain()
        GM.SsoManager:OnTokenExpired(reqCtx:GetHeader(NetworkConfig.TokenHeaderKey), reqCtx, session)
        return
      end
      if reqCtx.Status == 404 or reqCtx.Status == 502 or reqCtx.Status == 400 and headers["server-time"] ~= nil then
        if headers["server-time"] ~= nil then
          GM.GameModel:RefreshServerTime(tonumber(headers["server-time"]))
          reqCtx.ServerTime = GM.HttpManager:GetServerTime()
        end
        if reqCtx.MaxRetry > 0 then
          reqCtx.MaxRetry = reqCtx.MaxRetry - 1
          reqCtx:Retain()
          reqCtx:Send()
          return
        end
      end
    end
    if ApiMessage.TryFallbackRequest(reqCtx) then
      return
    end
    Log.Warning("Test Message failed, error message is " .. reqCtx.ErrorMsg, LogTag.Network)
    if callback ~= nil then
      callback(false, reqCtx.ErrorMsg)
    end
  end)
  if strToken == "" then
    GM.SsoManager:WaitForToken(reqCtx)
  else
    reqCtx:Send()
  end
end
