GeneralGemConfirmWindow = setmetatable({}, BaseWindow)
GeneralGemConfirmWindow.__index = GeneralGemConfirmWindow

function GeneralGemConfirmWindow:Init(itemModel, titleKey, descKey, cost, confirmCallback, initFunc, closeFunc)
  self.m_titleText.text = GM.GameTextModel:GetText(titleKey)
  self.m_descKey = descKey
  self.m_itemModel = itemModel
  self.m_cost = cost
  self.m_bubbleGo:SetActive(itemModel ~= nil and itemModel:GetComponent(ItemBubble) ~= nil)
  self.m_itemImg.gameObject:SetActive(itemModel ~= nil)
  if itemModel then
    local scale = itemModel:GetComponent(ItemBubble) ~= nil and Vector3(0.75, 0.75, 1) or V3One
    self.m_itemImg.gameObject.transform.localScale = scale
    local spriteName = itemModel:GetSpriteName()
    SpriteUtil.SetImage(self.m_itemImg, spriteName, true)
  else
    UIUtil.AddSizeDelta(self.m_contentRect, 0, -190)
  end
  self:UpdateContent(cost)
  self.m_confirmCallback = confirmCallback
  self.m_closeFunc = closeFunc
  if initFunc then
    initFunc(self)
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_buttonGo.transform)
end

function GeneralGemConfirmWindow:UpdateContent(cost)
  if self.m_cost == 0 then
    return
  end
  self.m_cost = cost
  self.m_greenText.text = cost
  self.m_descText.text = GM.GameTextModel:GetText(self.m_descKey, cost)
end

function GeneralGemConfirmWindow:_OnConfirmClicked()
  if self.m_cost > 0 and self.m_confirmCallback then
    self.m_confirmCallback(self)
  end
  self:OnCloseBtnClick()
end

function GeneralGemConfirmWindow:Close()
  BaseWindow.Close(self)
  if self.m_closeFunc then
    self.m_closeFunc(self)
  end
end
