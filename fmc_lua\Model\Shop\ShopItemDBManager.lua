ShopItemDBManager = {}
ShopItemDBManager.__index = ShopItemDBManager
local DBItemColumnItemCode = "itemCode"
local DBItemColumnLeftCount = "leftCount"
local DBItemColumnCostCount = "costCount"

function ShopItemDBManager:Init()
  self.m_itemDB = GM.DBTableManager:GetTable(EDBTableConfigs.ShopItem)
  self.m_idGenerator = DBIdGenerator.Create()
  self:UpdateShopItemIdMap()
end

function ShopItemDBManager:UpdateShopItemIdMap()
  self.m_mapShopItemIds = {}
  self.m_mapShopItemCode2Id = {}
  local isTestMode = GameConfig.IsTestMode()
  local itemDatas = self.m_itemDB:GetValues()
  for id, data in pairs(itemDatas) do
    local shopType = data.shopType
    if self.m_mapShopItemIds[shopType] == nil then
      self.m_mapShopItemIds[shopType] = {}
      self.m_mapShopItemCode2Id[shopType] = {}
    end
    self.m_mapShopItemIds[shopType][#self.m_mapShopItemIds[shopType] + 1] = id
    local itemCode = data[DBItemColumnItemCode] or data.itemCodeStr
    local leftCount = data[DBItemColumnLeftCount]
    if self.m_mapShopItemCode2Id[shopType][itemCode] and isTestMode then
      Log.Error("商店数据有重复棋子：" .. shopType .. ", " .. itemCode .. ", " .. id)
    else
      self.m_mapShopItemCode2Id[shopType][itemCode] = id
    end
  end
  for _, arrIds in pairs(self.m_mapShopItemIds) do
    table.sort(arrIds)
  end
end

function ShopItemDBManager:HasShopItems(shopType)
  return self.m_mapShopItemIds[shopType] ~= nil and #self.m_mapShopItemIds[shopType] > 0
end

function ShopItemDBManager:GetShopItemData(itemId)
  return self.m_itemDB:GetValues()[itemId]
end

function ShopItemDBManager:GetShopItemIds(shopType)
  return self.m_mapShopItemIds[shopType]
end

function ShopItemDBManager:IsItemInStock(shopType, itemCode)
  local mapShopItemCode2Id = self.m_mapShopItemCode2Id[shopType]
  local itemId = mapShopItemCode2Id and mapShopItemCode2Id[itemCode]
  if not itemId then
    return false
  end
  local shopData = self:GetShopItemData(itemId)
  if not shopData then
    return false
  end
  if shopData[DBItemColumnLeftCount] <= 0 then
    return false
  end
  return true, itemId, shopData[DBItemColumnCostCount]
end

function ShopItemDBManager:GetShopItemDatas(shopType)
  local itemIds = self:GetShopItemIds(shopType)
  if itemIds == nil then
    return nil
  end
  local data = {}
  for _, id in ipairs(itemIds) do
    data[#data + 1] = self:GetShopItemData(id)
  end
  return data
end

function ShopItemDBManager:RemoveShopItemDatas(shopType)
  if self.m_mapShopItemIds[shopType] == nil or #self.m_mapShopItemIds[shopType] == 0 then
    return
  end
  local arrRemove = self.m_mapShopItemIds[shopType]
  for i = 1, #arrRemove do
    self.m_itemDB:Remove(arrRemove[i])
  end
  self:UpdateShopItemIdMap()
end

function ShopItemDBManager:AddShopItemDatas(shopType, arrItemDatas)
  local mapAddDatas = {}
  local id, storeData
  for _, itemData in ipairs(arrItemDatas) do
    id = self.m_idGenerator:Generate()
    itemData.shopType = shopType
    storeData = Table.ShallowCopy(itemData)
    storeData.redTag = nil
    storeData.purpleTag = nil
    mapAddDatas[id] = storeData
    local itemCode = itemData[DBItemColumnItemCode]
    GM.ItemDataModel:SetLocked(itemCode)
  end
  self.m_itemDB:BatchSet(mapAddDatas)
  self:UpdateShopItemIdMap()
end

function ShopItemDBManager:CostLeftCount(eShopType, itemId)
  local leftCount = self.m_itemDB:GetValue(itemId, DBItemColumnLeftCount)
  if leftCount and 0 < leftCount then
    self.m_itemDB:Set(itemId, DBItemColumnLeftCount, leftCount - 1)
    if eShopType == EShopType.DailyDeals then
      local itemCode = self.m_itemDB:GetValue(itemId, DBItemColumnItemCode)
      local currentPrice = self.m_itemDB:GetValue(itemId, DBItemColumnCostCount)
      local nextPrice = GM.ShopDataModel:GetNextDailyDealsPrice(itemCode, currentPrice)
      self.m_itemDB:Set(itemId, DBItemColumnCostCount, nextPrice)
    end
    return true
  end
  return false
end

function ShopItemDBManager:GetData()
  return self.m_itemDB
end
