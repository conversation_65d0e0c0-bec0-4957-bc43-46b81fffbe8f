SceneItemLayerModel = setmetatable({}, BaseInteractiveItemLayerModel)
SceneItemLayerModel.__index = SceneItemLayerModel

function SceneItemLayerModel.Create(boardModel, dbTable, itemManager, fileName)
  local itemLayerModel = setmetatable({}, SceneItemLayerModel)
  itemLayerModel:Init(boardModel, dbTable, itemManager, fileName)
  return itemLayerModel
end

function SceneItemLayerModel:Init(boardModel, dbTable, itemManager, fileName)
  BaseInteractiveItemLayerModel.Init(self, boardModel, dbTable, itemManager)
  self.m_fileName = fileName
end

function SceneItemLayerModel:LoadFileConfig()
  self.m_initCodeMap = require("Data.Config." .. self.m_fileName)
  self.m_lockerArray = require("Data.Config." .. self.m_fileName .. "Locker") or {}
end

function SceneItemLayerModel:OnSyncDataFinished(noItem)
  self:ResetData()
  local isInitBoard = false
  if self.m_dbTable:IsEmpty() and noItem then
    isInitBoard = true
    local position
    for y = 1, self.m_boardModel:GetVerticalTiles() do
      for x = 1, self.m_boardModel:GetHorizontalTiles() do
        position = self.m_boardModel:CreatePosition(x, y)
        local code = self.m_initCodeMap[y][x]
        local item = ItemModelFactory.CreateWithCode(self.m_boardModel, position, code, false)
        if item ~= nil then
          self.m_itemManager:SetItem(item)
          self:SetItem(position, item)
        end
      end
    end
  else
    self:_SyncDBData()
  end
  for _, locker in pairs(self.m_lockerArray) do
    local startPos = self.m_boardModel:CreatePosition(locker.x, locker.y)
    local itemModel = self:GetItem(startPos)
    if itemModel and (isInitBoard or itemModel.locked == 1) then
      ItemModelFactory.CreateItemLockerCmp(itemModel, startPos, locker.width, locker.height, locker.unlockDay)
    end
  end
  if isInitBoard then
    local itemLockerType = ItemLocker
    local itemPaperBoxType = ItemPaperBox
    local itemCobwebType = ItemCobweb
    local itemDataModel = GM.ItemDataModel
    for itemModel, _ in pairs(self:GetAllItems()) do
      if not itemModel:GetComponent(itemLockerType) and not itemModel:GetComponent(itemPaperBoxType) then
        if itemModel:GetComponent(itemCobwebType) then
          itemDataModel:SetLocked(itemModel:GetComponent(itemCobwebType):GetInnerItemCode())
        else
          itemDataModel:SetUnlocked(itemModel:GetType())
        end
      end
    end
  end
end
