RoomBubble = {}
RoomBubble.__index = RoomBubble

function RoomBubble:Awake()
  EventDispatcher.AddListener(EEventType.RoomTouchesUp, self, self.OnPointerUp)
end

function RoomBubble:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function RoomBubble:Update()
  local roomView = GM.ChapterManager:GetActiveRoomView()
  if roomView then
    self.m_contentTrans:SetLocalScaleXY(roomView:GetBubbleScale())
  end
end

function RoomBubble:OnPointerDown()
  self.m_scaleRoot:DOKill()
  self.m_scaleRoot:DOScale(0.85, 0.2)
end

function RoomBubble:OnPointerUp()
  self.m_scaleRoot:DOKill()
  self.m_scaleRoot:DOScale(1, 0.2)
end

function RoomBubble:PlayShowAnimation(animated)
  self.m_scaleRoot:DOKill()
  if animated == false then
    self.m_scaleRoot.localScale = V3One
  else
    self.m_scaleRoot:DOScale(1, 0.4):SetEase(Ease.OutBack)
  end
end

function RoomBubble:PlayHideAnimation(animated)
  self.m_scaleRoot:DOKill()
  if animated == false then
    self.m_scaleRoot.localScale = V3Zero
  else
    self.m_scaleRoot:DOScale(0, 0.4):SetEase(Ease.InBack):SetDelay(0.5)
  end
end

RoomTaskBubble = setmetatable({}, RoomBubble)
RoomTaskBubble.__index = RoomTaskBubble

function RoomTaskBubble:Awake()
  RoomBubble.Awake(self)
  EventDispatcher.AddListener(EEventType.PropertyAcquired, self, self._OnPropertyAcquired)
  EventDispatcher.AddListener(EEventType.PropertyConsumed, self, self._OnPropertyConsumed)
end

function RoomTaskBubble:Init()
  self:UpdateContent()
end

function RoomTaskBubble:_OnPropertyAcquired(message)
  local goldChanged = false
  for _, property in ipairs(message.arrProperties) do
    if property[PROPERTY_TYPE] == EPropertyType.Gold then
      goldChanged = true
      break
    end
  end
  if goldChanged then
    self:UpdateContent()
  end
end

function RoomTaskBubble:_OnPropertyConsumed(message)
  if message.property[PROPERTY_TYPE] == EPropertyType.Gold then
    self:UpdateContent()
  end
end

function RoomTaskBubble:UpdateContent()
  local curGold = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gold)
  local ongoingTaskId = GM.TaskManager:GetOngoingTask()
  if not ongoingTaskId then
    self:UpdatePercentage(0)
    return
  end
  local curChapterName = GM.TaskManager:GetOngoingChapterName()
  local needGold = GM.TaskManager:GetTaskCost(ongoingTaskId, curChapterName)
  local percentage = 1
  if 0 < needGold then
    percentage = curGold / needGold
  end
  self:UpdatePercentage(percentage)
end

local MIN_HEIGHT = 13
local MAX_HEIGHT = 145

function RoomTaskBubble:UpdatePercentage(percentage)
  percentage = math.min(percentage, 1)
  percentage = math.max(percentage, 0)
  local width = self.m_greenSprite.size.x
  local height = MIN_HEIGHT + percentage * (MAX_HEIGHT - MIN_HEIGHT)
  self.m_greenSprite.size = Vector2(width, height)
  percentage = math.floor(percentage * 100)
  self.m_percentageText.text = percentage .. "%"
end

function RoomTaskBubble:OnClicked()
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.TaskWindow) then
    return
  end
  GM.UIManager:OpenView(UIPrefabConfigName.TaskWindow, ETaskWindowRefer.RoomBubble)
end

RoomOrderBubble = setmetatable({}, RoomBubble)
RoomOrderBubble.__index = RoomOrderBubble

function RoomOrderBubble:UpdateContent(itemType)
  SpriteUtil.SetSpriteRenderer(self.m_spriteRenderer, itemType)
  self.m_itemType = itemType
end

function RoomOrderBubble:OnClicked()
  GM.SceneManager:ChangeGameMode(EGameMode.Board)
  GM.BIManager:LogAction(EBIType.ClickOrderBubble, self.m_itemType)
end
