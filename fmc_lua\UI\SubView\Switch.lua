Switch = {}
Switch.__index = Switch

function Switch:Init(funcOnToggle, tag)
  Log.Assert(funcOnToggle, "没有设置 Switch 回调")
  self.m_funcOnToggle = funcOnToggle
  self.m_tag = tag
  self:SetInteractable(true)
end

function Switch:SetOn(bOn, triggerToggleFunc)
  triggerToggleFunc = triggerToggleFunc == nil or triggerToggleFunc
  self.m_flagGo:SetActive(bOn)
  if triggerToggleFunc then
    self:_Callback()
  end
end

function Switch:IsOn()
  return self.m_flagGo.activeSelf
end

function Switch:_OnClick()
  self.m_flagGo:SetActive(not self.m_flagGo.activeSelf)
  self:_Callback()
end

function Switch:_Callback()
  self.m_funcOnToggle(self.m_flagGo.activeSelf, self.m_tag)
end

function Switch:PlaySoundEffect()
  if self.m_strSoundEffectName then
    GM.AudioModel:PlayEffect(self.m_strSoundEffectName)
  end
end

function Switch:SetText(text)
  self.m_textComp.text = text
end

function Switch:SetInteractable(interactable)
  if self.m_switchBtn and self.m_switchBtn.interactable ~= interactable then
    self.m_switchBtn.interactable = interactable
  end
end
