local Step = {
  HighlightShop = "1",
  ClickFree = "2",
  Info = "3"
}
local EStep2TextKey = {
  [Step.HighlightShop] = "tutorial_shop_1",
  [Step.ClickFree] = "tutorial_shop_2",
  [Step.Info] = "tutorial_shop_3"
}
local EStep2TextAnchorPercent = {
  [Step.HighlightShop] = 20,
  [Step.ClickFree] = 70,
  [Step.Info] = 70
}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._OnCloseView)
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self._OnShopRefreshed)
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    return false
  end
  if not GM.ShopModel:HasFreeDailyDeals() then
    return false
  end
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  self:_ExecuteStep1()
  return true
end

function Executer:_OnOpenView(msg)
  if self.m_strOngoingDatas == Step.HighlightShop and msg.name == UIPrefabConfigName.ShopWindow then
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    self:_ExecuteStep2()
  elseif self.m_strOngoingDatas == Step.ClickFree and msg.name == UIPrefabConfigName.RewardWindow then
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    if self.m_shopCell ~= nil then
      if self.m_shopCell:GetDetailButton() ~= nil then
        self.m_shopCell:GetDetailButton().enabled = true
      end
      TutorialHelper.DehighlightForUI(self.m_shopCell.gameObject.transform)
      self.m_shopCell = nil
    end
    if self.m_shopWindow ~= nil then
      self.m_shopWindow:SetScrollEnabled(true)
      self.m_shopWindow:RefreshScrollMask()
      self.m_shopWindow = nil
    end
    TutorialHelper.HideTutorialLayer(self.m_gesture, self.m_arrow)
    self.m_gesture = nil
    self.m_arrow = nil
  end
end

function Executer:_OnCloseView(msg)
  if self.m_strOngoingDatas == Step.ClickFree and msg.name == UIPrefabConfigName.RewardWindow then
    self:_ExecuteStep3()
  end
end

function Executer:_OnShopRefreshed(msg)
  if msg and msg.shopType == EShopType.PassActivityTicket and self.m_strOngoingDatas == Step.ClickFree and self.m_gesture and self.m_shopCell then
    DelayExecuteFuncInView(function()
      if not self.m_gesture or not self.m_shopCell then
        return
      end
      self.m_gesture.transform.localPosition = self.m_shopCell:GetFreeBtnGo().transform.position
      local anchorPercent = self:_GetStep2Percent()
      TutorialHelper.UpdateTextAnchorPercent(anchorPercent)
    end, 0.1, self.m_shopWindow, true)
  end
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.HighlightShop
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  TutorialHelper.HighlightHudButton(ESceneViewHudButtonKey.Shop)
  self.m_arrow = TutorialHelper.AddArrow2HudButton(ESceneViewHudButtonKey.Shop, 135)
  self.m_arrow:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_ExecuteStep2()
  self.m_strOngoingDatas = Step.ClickFree
  TutorialHelper.DehighlightHudButton(ESceneViewHudButtonKey.Shop)
  TutorialHelper.HideTutorialLayer(self.m_gesture, self.m_arrow)
  self.m_gesture = nil
  self.m_arrow = nil
  local window = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.ShopWindow)
  self.m_shopWindow = window
  if self.m_shopWindow ~= nil then
    self.m_shopWindow:LocateShopType(EShopType.DailyDeals)
  end
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
      self:Finish(self.m_gesture, self.m_arrow)
      return
    end
    self.m_shopCell = window:GetDailyFreeGiftCell()
    if self.m_shopCell == nil or self.m_shopCell:GetLeftCount() == 0 then
      self:Finish(self.m_gesture, self.m_arrow)
      return
    end
    if self.m_shopCell:GetDetailButton() ~= nil then
      self.m_shopCell:GetDetailButton().enabled = false
    end
    self.m_shopWindow:SetScrollEnabled(false)
    TutorialHelper.SetMaskAlphaOnce(0.6)
    TutorialHelper.WholeMask()
    local anchorPercent = self:_GetStep2Percent()
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), anchorPercent)
    TutorialHelper.HighlightForUI(self.m_shopCell.gameObject.transform)
    self.m_gesture = TutorialHelper.TapOnCustomRectTrans(self.m_shopCell:GetFreeBtnGo().transform)
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  end, 0.8)
end

function Executer:_GetStep2Percent()
  local anchorPercent = EStep2TextAnchorPercent[self.m_strOngoingDatas]
  return anchorPercent
end

function Executer:_ExecuteStep3()
  self.m_strOngoingDatas = Step.Info
  local callback = function()
    self:Finish(self.m_gesture, self.m_arrow)
  end
  TutorialHelper.SetMaskAlphaOnce(0.6)
  TutorialHelper.WholeMask(callback)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
