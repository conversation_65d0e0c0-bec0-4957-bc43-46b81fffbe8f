EnergyFullNotificationHelper = setmetatable({}, BaseNotificationHelper)
EnergyFullNotificationHelper.__index = EnergyFullNotificationHelper

function EnergyFullNotificationHelper.IsSceneExist(strScene)
  if strScene == NotificationScene.EnergyRefill then
    return true
  end
  return false
end

function EnergyFullNotificationHelper.Generate(strScene)
  if GM.EnergyModel:GetEnergy(EnergyType.Main) >= 50 then
    return {}
  end
  local strTileKey, strDescKey = GM.NotificationModel:GetTextTileAndDesc(strScene)
  strTileKey = strTileKey ~= "" and strTileKey or "push_energy_title"
  strDescKey = strDescKey ~= "" and strDescKey or "push_energy_desc"
  local notification = {
    Type = NotificationType.EnergyFull,
    Title = GM.GameTextModel:GetText(strTileKey),
    Message = GM.GameTextModel:GetText(strDescKey),
    Delay = GM.EnergyModel:GetEnergyFullDuration(EnergyType.Main)
  }
  if notification.Delay == nil or notification.Delay <= 0 then
    return {}
  end
  return {notification}
end
