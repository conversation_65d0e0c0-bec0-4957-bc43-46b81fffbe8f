TutorialBoardView = setmetatable({}, BaseBoardView)
TutorialBoardView.__index = TutorialBoardView

function TutorialBoardView:Init(boardModel)
  BaseBoardView.Init(self, boardModel)
  REGISTER_BOARD_EVENT_HANDLER(self, "CollectItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "MergeItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "SplitItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "TimeSkip")
  TutorialBoardView.s_instance = self
end

function TutorialBoardView.GetInstance()
  return TutorialBoardView.s_instance
end

function TutorialBoardView:ConvertBoardPositionToLocalPosition(boardPosition)
  local endLocalPosition = boardPosition:ToLocalPosition()
  return Vector3(endLocalPosition.x + BaseBoardModel.TileSize / 2, endLocalPosition.y + BaseBoardModel.TileSize / 2, 0)
end

function TutorialBoardView:OnPointerDown(boardPosition)
  local itemModel = self.m_model:GetItem(boardPosition)
  Log.Assert(itemModel ~= nil, "引导棋盘配置的的点击手势下方没有棋子")
  self.m_lastTouchedItem = self:GetItemView(itemModel)
end

function TutorialBoardView:OnDrag(localPosition)
  if not self.m_dragging then
    self:_UpdateItemAffectedEffect(self.m_lastTouchedItem)
  end
  self.m_dragging = true
  local worldPosition = self.transform:TransformPoint(localPosition)
  worldPosition.z = 0
  self.m_lastTouchedItem.transform.position = worldPosition
  self:_TryShowMergeLight(worldPosition)
end

function TutorialBoardView:OnPointerUp(boardPosition)
  self:_ClearMergeLight()
  self:_UpdateItemAffectedEffect()
  local itemModel = self.m_lastTouchedItem:GetModel()
  if self.m_dragging then
    self.m_model:DragItem(itemModel, boardPosition)
  else
    self.m_model:TapItem(itemModel)
  end
  self.m_dragging = false
end

function TutorialBoardView:_UpdateItemAffectedEffect()
  for _, view in pairs(self.m_modelViewMap) do
    if view ~= self.m_lastTouchedItem then
      view:UpdateItemAffectedEffect(self.m_lastTouchedItem and self.m_lastTouchedItem:GetModel() or nil)
    end
  end
end

function TutorialBoardView:ShowGestureTap(localPosition)
  self.m_gestureTap.gameObject:SetActive(true)
  self.m_gestureTap.localPosition = localPosition
end

function TutorialBoardView:HideGestureTap()
  self.m_gestureTap.gameObject:SetActive(false)
end

function TutorialBoardView:ShowGestureDrag(localPosition)
  self.m_gestureDrag.gameObject:SetActive(true)
  self.m_gestureDrag.localPosition = localPosition
end

function TutorialBoardView:HideGestureDrag()
  self.m_gestureDrag.gameObject:SetActive(false)
end

function TutorialBoardView:_OnMergeItem(message)
  local sourceItemView = self:GetItemView(message.Source)
  sourceItemView.toBeRemoved = true
  local targetItemView = self:GetItemView(message.Target)
  targetItemView.toBeRemoved = true
  local newItemView = self:_AddItemView(message.New)
  local targetPosition = targetItemView.transform.localPosition
  local sequence = DOTween.Sequence()
  sequence:Insert(0, sourceItemView.transform:DOLocalMove(targetPosition, 0.1))
  sequence:Insert(0, sourceItemView.transform:DOScale(0.3, 0.1))
  sequence:InsertCallback(0.1, function()
    self:_RemoveItemView(sourceItemView)
  end)
  targetItemView:MergeLightDisappear()
  sequence:Insert(0, targetItemView.transform:DOScale(0.3, 0.1))
  sequence:InsertCallback(0.1, function()
    self:_RemoveItemView(targetItemView)
  end)
  if newItemView ~= nil then
    newItemView.transform.localScale = Vector3.zero
    sequence:InsertCallback(0.1, function()
      newItemView.transform.localScale = 0.3 * V3One
    end)
    sequence:Insert(0.1, newItemView.transform:DOScale(1.3, 0.2))
    sequence:Insert(0.3, newItemView.transform:DOScale(1, 0.1))
  end
end

function TutorialBoardView:_OnSplitItem(message)
  local itemSplit = message.Split:GetComponent(ItemSplit)
  message.Split:SetPosition(message.Split:GetPosition())
  local targetItemView = self:GetItemView(message.Target)
  targetItemView.toBeRemoved = true
  local newItemView1 = self:_AddItemView(message.New1)
  local newItemView2 = self:_AddItemView(message.New2)
  local sequence = DOTween.Sequence()
  targetItemView:MergeLightDisappear()
  sequence:Insert(0, targetItemView.transform:DOScale(0.3, 0.1))
  sequence:InsertCallback(0.1, function()
    self:_RemoveItemView(targetItemView)
  end)
  if newItemView1 ~= nil then
    newItemView1.transform.localScale = Vector3.zero
    sequence:InsertCallback(0.1, function()
      newItemView1.transform.localScale = 0.3 * V3One
    end)
    sequence:Insert(0.1, newItemView1.transform:DOScale(1.3, 0.2))
    sequence:Insert(0.3, newItemView1.transform:DOScale(1, 0.1))
  end
  if newItemView2 ~= nil then
    self:_PlayJumpAnimation(newItemView2, targetItemView.transform.localPosition, newItemView2.transform.localPosition)
  end
  sequence:InsertCallback(0.1, function()
    newItemView1:ShowSpreadLight()
  end)
end

function TutorialBoardView:_OnCollectItem(message)
  local rewards = message.Source:GetComponent(ItemCollectable):GetRewards()
  local itemView = self:GetItemView(message.Source)
  self:_RemoveItemView(itemView)
end

function TutorialBoardView:_OnTimeSkip(message)
  local cPos = message.Item:GetPosition()
  local item, itemView
  for item, _ in pairs(self.m_model:GetAllBoardItems()) do
    if message.Item ~= item then
      itemView = self:GetItemView(item)
      if itemView ~= nil then
        itemView:PlayTimeSkipAnimation(self:_GetTimeSkipDelay(cPos, position))
      end
    end
  end
end

function TutorialBoardView:_GetTimeSkipDelay(cPos, tPos)
  return math.max(math.abs(cPos:GetX() - tPos:GetX()) - 1, math.abs(cPos:GetY() - tPos:GetY()) - 1) * 0.1
end
