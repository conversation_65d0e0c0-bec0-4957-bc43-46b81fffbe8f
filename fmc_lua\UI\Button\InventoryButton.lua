InventoryButton = setmetatable({}, HudGeneralButton)
InventoryButton.__index = InventoryButton

function InventoryButton:Awake()
  HudGeneralButton.Awake(self)
  EventDispatcher.AddListener(EEventType.FunctionOpen, self, self._CheckOpen)
  EventDispatcher.AddListener(EEventType.OpenView, self, self.OnViewOpened)
  EventDispatcher.AddListener(EEventType.CloseView, self, self.OnViewClosed)
  EventDispatcher.AddListener(EEventType.ItemSpreadFailed, self, self.OnItemSpreadFailed)
  EventDispatcher.AddListener(EEventType.ItemStored, self, self._UpdateContent)
  EventDispatcher.AddListener(EEventType.ItemRetrieved, self, self._UpdateContent)
  EventDispatcher.AddListener(EEventType.OnInventoryItemSold, self, self._UpdateContent)
  EventDispatcher.AddListener(EEventType.OnInventoryItemUndoSell, self, self._UpdateContent)
  EventDispatcher.AddListener(EEventType.OnInventoryCapacityUpdate, self, self._UpdateContent)
  EventDispatcher.AddListener(EEventType.OnClaimedOrderGroupReward, self, self._UpdateContent)
  
  function self._PlayContinueEffect()
    self.m_conEffect:SetActive(true)
  end
  
  self.m_bEnabled = true
  self:_UpdateInventoryState(false)
  self:_CheckOpen()
  self:_UpdateContent()
end

function InventoryButton:_CheckOpen()
  self.gameObject:SetActive(GM.OpenFunctionModel:IsFunctionOpen(EFunction.Inventory))
end

function InventoryButton:_UpdateContent()
  local capacity, producerNum = GM.MainBoardModel:GetStoreSlotCount()
  local inStoreCount = GM.MainBoardModel:GetStoredItemCount()
  self.m_capacityText.text = tostring(inStoreCount - producerNum) .. "/" .. tostring(capacity - producerNum)
end

function InventoryButton:OnViewOpened(msg)
  if msg.name == UIPrefabConfigName.InventoryWindow then
    self:_UpdateInventoryState(true)
  end
end

function InventoryButton:OnViewClosed(msg)
  if msg.name == UIPrefabConfigName.InventoryWindow then
    self:_UpdateInventoryState(false)
  end
end

function InventoryButton:OnDestroy()
  HudGeneralButton.OnDestroy(self)
  Scheduler.Unschedule(self._PlayContinueEffect)
end

function InventoryButton:OnClick()
  if not self.m_bEnabled then
    return
  end
  local windowPrefabName = UIPrefabConfigName.InventoryWindow
  local param = self.m_bPlayTutorialAnim
  self.m_bPlayTutorialAnim = nil
  if GM.UIManager:IsViewExisting(windowPrefabName) then
    local topWindow = GM.UIManager:GetOpenedTopViewByType(EViewType.Window)
    if topWindow then
      topWindow:Close()
    end
    return
  end
  GM.UIManager:OpenView(windowPrefabName, param)
end

function InventoryButton:PlayItemInEffect()
  self.m_closeEffect:SetActive(false)
  self.transform:DOScale(1.2, 0.2)
  self.transform:DOScale(1, 0.1):SetDelay(0.2)
  self.m_inEffect:SetActive(true)
end

function InventoryButton:PlayDragStartEffect()
  self:_UpdateIconState(true)
  self.m_openEffect:SetActive(true)
  Scheduler.Schedule(self._PlayContinueEffect, self, nil, 1, 0.34)
end

function InventoryButton:PlayDragEndEffect()
  Scheduler.Unschedule(self._PlayContinueEffect)
  self:_UpdateIconState(false)
  self.m_openEffect:SetActive(false)
  self.m_closeEffect:SetActive(true)
  self.m_conEffect:SetActive(false)
end

function InventoryButton:_UpdateIconState(isOpen)
  self.m_iconNormalGo:SetActive(not isOpen)
  self.m_iconOpenGo:SetActive(isOpen)
  self.m_openEffect2:SetActive(isOpen)
end

function InventoryButton:_UpdateInventoryState(inInventory)
  self.m_iconRootGo:SetActive(true)
  self.m_iconNormalGo:SetActive(not inInventory)
  self.m_iconOpenGo:SetActive(inInventory)
end

function InventoryButton:OnItemSpreadFailed(msg)
  if Table.IsEmpty(msg) then
    return
  end
  if msg.reason == SpreadFailedReason.BoardFull and msg.gameMode ~= EGameMode.ExtraBoard then
    self.m_animator:SetTrigger("Shake")
  end
end

function InventoryButton:SetClickEnabled(bEnable)
  self.m_bEnabled = bEnable
end

function InventoryButton:SetPlayTutorialAnim(bPlayTutorialAnim)
  self.m_bPlayTutorialAnim = bPlayTutorialAnim
end
