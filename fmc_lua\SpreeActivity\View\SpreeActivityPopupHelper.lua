SpreeActivityPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Spree] = true
  }
}, BasePopupHelper)
SpreeActivityPopupHelper.__index = SpreeActivityPopupHelper

function SpreeActivityPopupHelper.Create()
  local helper = setmetatable({}, SpreeActivityPopupHelper)
  helper:Init()
  return helper
end

function SpreeActivityPopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(SpreeActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
end

function SpreeActivityPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function SpreeActivityPopupHelper:CheckPopup()
  for activityType, activityDefinition in pairs(SpreeActivityDefinition) do
    local args = {activityType, true}
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Started then
      if not model:HasWindowOpenedOnce(ActivityState.Started) then
        return activityDefinition.ReadyWindowPrefabName, args
      end
    elseif state == ActivityState.Ended and not model:HasWindowOpenedOnce(ActivityState.Ended) and model:HasWindowOpenedOnce(ActivityState.Started) then
      return activityDefinition.ResultWindowPrefabName, args
    end
  end
  return nil
end
