ReturnUserPopupHelper = setmetatable({}, BasePopupHelper)
ReturnUserPopupHelper.__index = ReturnUserPopupHelper
ReturnUserPopupHelper.canPopScene = {
  [EPopupScene.Main] = true,
  [EPopupScene.Board] = true
}

function ReturnUserPopupHelper.Create()
  local helper = setmetatable({}, ReturnUserPopupHelper)
  helper:Init()
  return helper
end

function ReturnUserPopupHelper:CheckPopup()
  local arrRewrads = GM.ReturnUserModel:TryGetReturnReward()
  if arrRewrads ~= nil then
    return UIPrefabConfigName.ReturnUserRewardWindow, table.pack(arrRewrads)
  end
end
