GameModel = {}
GameModel.__index = GameModel
local LocaleTimeDelta = "LocaleTimeDelta"

function GameModel:Init()
  self:UpdatePerSecond()
  self:_InitLocaleTimeDelta()
  self.m_isCheckingCatalog = false
end

function GameModel:UpdatePerSecond()
  if self.m_iServerTimeDelta ~= nil then
    self.m_iServerTime = DeviceInfo.GetCpuTime() + self.m_iServerTimeDelta
  end
end

function GameModel:HasServerTime()
  return self.m_iServerTimeDelta ~= nil
end

function GameModel:GetServerTime()
  Log.Assert(self.m_iServerTimeDelta ~= nil, "shouldn't get server time before login finished")
  return self.m_iServerTime or 0
end

function GameModel:GetServerDay()
  return self:GetServerTime() // 86400
end

function GameModel:GetLocaleTime()
  return self:GetServerTime() + self.m_iLocaleTimeDelta
end

function GameModel:ServerTime2LocaleTime(serverTime)
  return serverTime + self.m_iLocaleTimeDelta
end

function GameModel:LocaleTime2ServerTime(localeTime)
  return localeTime - self.m_iLocaleTimeDelta
end

function GameModel:SSO(callbackFunc)
  callbackFunc = callbackFunc or function()
  end
  local recordVendorId = GM.UserModel:Get(EUserLocalDataKey.VendorId)
  local recordDeviceId = GM.UserModel:Get(EUserLocalDataKey.DeviceId)
  local vendorIdChanged = not StringUtil.IsNilOrEmpty(recordVendorId) and recordVendorId ~= DeviceInfo.GetVendorID()
  local deviceIdChanged = not StringUtil.IsNilOrEmpty(recordDeviceId) and recordDeviceId ~= DeviceInfo.GetDeviceID()
  if vendorIdChanged or deviceIdChanged then
    if not self.m_deviceInfoUpdated then
      if vendorIdChanged then
        GM.BIManager:LogProject(EBIProjectType.DeviceCopy, "vid: " .. recordVendorId)
      end
      if deviceIdChanged then
        GM.BIManager:LogProject(EBIProjectType.DeviceCopy, "did: " .. recordDeviceId)
      end
      GM.SsoManager:SetToken("")
      GM.SsoManager:Register(function(success, msg)
        if success then
          self.m_deviceInfoUpdated = true
          self:_SSOUpdateInstallId(callbackFunc)
        else
          callbackFunc(false, msg)
        end
      end, true)
    else
      self:_SSOUpdateInstallId(callbackFunc)
    end
  else
    self:_SSOAfterCheckDeviceInfo(callbackFunc)
  end
end

function GameModel:_SSOUpdateInstallId(callbackFunc)
  local newUuid = PlatformInterface.GenerateUUID()
  GM.BIManager:LogProject(EBIProjectType.DeviceCopy, "uuid: " .. newUuid)
  GM.SsoManager:Register(function(success, msg)
    if success then
      GM.UserModel:SetInstallUuid(newUuid)
      GM.BIManager:ResetInstallUuidCache(newUuid)
      self:_SSOAfterCheckDeviceInfo(callbackFunc)
    else
      callbackFunc(false, msg)
    end
  end, true, newUuid)
end

function GameModel:_SSOAfterCheckDeviceInfo(callbackFunc)
  GM.UserModel:Set(EUserLocalDataKey.VendorId, DeviceInfo.GetVendorID())
  GM.UserModel:Set(EUserLocalDataKey.DeviceId, DeviceInfo.GetDeviceID())
  if GM.SsoManager:GetToken() == "" then
    GM.SsoManager:Register(callbackFunc, true)
  elseif GM.UserModel:GetUserId() == 0 then
    GM.SsoManager:SetToken("")
    GM.SsoManager:Register(callbackFunc, true)
  else
    callbackFunc(true)
  end
end

function GameModel:RefreshServerTime(serverTime)
  local localTime = DeviceInfo.GetCpuTime()
  local oldDelta = self.m_iServerTimeDelta or 0
  local curDelta = serverTime - localTime
  if curDelta < oldDelta - 360 then
    Log.Error("服务器时间往回改了，请杀进程或清数据重来。时间差值：" .. tostring(oldDelta - curDelta))
  end
  self.m_iServerTimeDelta = math.max(oldDelta, curDelta)
  CSBIManager.serverTimeDelta = self.m_iServerTimeDelta
  self:UpdatePerSecond()
end

function GameModel:_InitLocaleTimeDelta()
  local strLocaleTimeDelta = PlayerPrefs.GetString(LocaleTimeDelta, "")
  if strLocaleTimeDelta ~= "" then
    self.m_iLocaleTimeDelta = tonumber(strLocaleTimeDelta)
  else
    self:_UpdateLocaleTimeDelta()
  end
  Log.Info("LocaleTimeDelta: " .. self.m_iLocaleTimeDelta)
end

function GameModel:_UpdateLocaleTimeDelta()
  local currentTime = os.time()
  local localeTimeDelta = currentTime - os.time(os.date("!*t", currentTime))
  PlayerPrefs.SetString(LocaleTimeDelta, tostring(localeTimeDelta))
  self.m_iLocaleTimeDelta = localeTimeDelta
end

function GameModel:Login(callbackFunc, retryTimes, isLoading)
  if self.isLoginingService then
    Log.Info("GameModel already logining service, skip")
    if callbackFunc ~= nil then
      callbackFunc(false)
    end
    return
  end
  Log.Info("GameModel:Login")
  callbackFunc = callbackFunc or function()
  end
  local startTime = NetTimeStamp.Create(EBIType.NetworkCheckAction.StartLogin)
  GM.BIManager:LogNet(EBIType.NetworkCheckAction.StartLogin, nil, nil, nil, isLoading)
  local requestId = TimeUtil.GetTimeInSecond()
  local callback = function(bSuccess, tbLoginResp, reqCtx)
    self.isLoginingService = false
    local timeInterval = startTime:EndAndGetDur()
    if bSuccess then
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.LoginSuccess, "id=" .. requestId, reqCtx, timeInterval, isLoading)
      Log.Info("group_name:" .. tbLoginResp.group_name, "ABTest")
      if GM.TestModel then
        GM.TestModel:SetABTestGroupInfo(tbLoginResp.group_name)
      end
      if not StringUtil.IsNilOrEmpty(tbLoginResp.group_name) then
        GM.BIManager:LogABTest(tbLoginResp.group_name)
      end
      self.m_groupName = tbLoginResp.group_name
      GM.SDKHelper:AiHelpUpdateUserInfo()
    else
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.LoginFailed, "id=" .. requestId .. "\n" .. tbLoginResp, reqCtx, timeInterval, isLoading)
    end
    if timeInterval.cpuInS > 300 then
      self:Login(callbackFunc, nil)
      return
    end
    if bSuccess then
      self:RefreshServerTime(math.floor(tbLoginResp.server_time / 1000))
      self.loginFinishTime = self:GetServerTime()
      if tbLoginResp.force_sso == 1 then
        GM.SsoManager:SetToken("")
        GM.SsoManager:Register()
      end
      if self:_TryOpenAccountNoticeWindow(tbLoginResp) then
        callbackFunc(false, {hideNetError = true}, reqCtx)
        return
      end
      local restart = GM.ConfigModel:UpdateServerConfigs(tbLoginResp.config)
      if restart then
        GM:RestartGame(nil, EBIProjectType.RestartGameAction.FileReplaceChanged)
        return
      end
      GM.CDNResourceManager:OnLoginSuccess(tbLoginResp.catalogue, tbLoginResp.catalogue_md5, tbLoginResp.force_download == 1)
      if tbLoginResp.total_pay then
        GM.InAppPurchaseModel:SetTotalRecharge(tbLoginResp.total_pay)
      end
      GM:LoadServerConfig(tbLoginResp)
      GM.BundleManager:UpdateAfterLoginFinished()
      GM.NoticeModel:UpdateAfterLoginFinished(tbLoginResp)
      if tbLoginResp.session then
        GM.HeartBeatManager:SetSession(tbLoginResp.session)
      end
      GM.HeartBeatManager:SetInterval(tbLoginResp.heart_beat_interval, tbLoginResp.heart_beat_interval_idle)
      GM.UpdateHintModel:ParseLogin(tbLoginResp)
      GM.OperManager:Login(tbLoginResp.valid_group_name)
      if isLoading then
        self:CheckCatalogAndRestart(isLoading)
      end
    end
    EventDispatcher.DispatchEvent(EEventType.LoginFinished, {bSuccess = bSuccess, tbResp = tbLoginResp})
    callbackFunc(bSuccess, tbLoginResp, reqCtx)
  end
  self.isLoginingService = true
  ApiMessage.Login(requestId, callback, retryTimes, isLoading)
end

function GameModel:CheckPrivacyPolicy(callback)
  self:GetCloudConfig()
  local showPolicyWindow = function()
    GM.SceneManager:StartPrivacyTick()
    local desc = GM.GameTextModel:GetText("game_start_notice_desc_new", NetworkConfig.GetUserAgreementLink(), NetworkConfig.GetPrivacyPolicyLink(), NetworkConfig.GetChildPrivacyLink())
    GM.UIManager:OpenView(UIPrefabConfigName.PrivacyConfirmWindow, GM.GameTextModel:GetText("game_start_notice_title"), desc, GM.GameTextModel:GetText("game_start_notice_button"), nil, function()
      GM.SceneManager:EndPrivacyTick()
      PlayerPrefs.SetInt(EPlayerPrefKey.PrivacyPolicyAccepted, 1)
      PlatformInterface.RequestATTPermission(function(status)
        Log.Info("iOS idfa permission : " .. tostring(status))
        callback(true)
      end)
    end, false)
  end
  if PlayerPrefs.GetInt(EPlayerPrefKey.PrivacyPolicyAccepted, 0) == 1 then
    callback(true)
  elseif GM.UserModel:GetUserId() ~= 0 then
    PlayerPrefs.SetInt(EPlayerPrefKey.PrivacyPolicyAccepted, 1)
    callback(true)
  else
    showPolicyWindow()
  end
end

function GameModel:GetCloudConfig()
  local requestURL = NetworkConfig.GetCloudConfigUrl()
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(requestURL, "GET", 8000, 0)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetCallback(function()
    if GM ~= nil and reqCtx.Rcode == ResultCode.Succeeded then
      local rawData = reqCtx:GetResponseString()
      local tbData = json.decode(rawData)
      if tbData ~= nil then
        for _, data in ipairs(tbData) do
          if data.name == "compress" then
            NetworkConfig.UpdateCompressMethod(data.value)
          end
        end
      elseif GM.BIManager ~= nil then
        GM.BIManager:LogErrorInfo(EBIType.CloudJsonDecodeError, rawData)
      end
    end
  end)
  reqCtx:Send()
end

function GameModel:OnCheat()
  self:_TryOpenAccountNoticeWindow({force_sync = 2})
end

function GameModel:_TryOpenAccountNoticeWindow(tbLoginResp)
  local endLoginHandle = false
  self.m_accountNotice = nil
  if tbLoginResp.force_sync == 2 then
    GM.HeartBeatManager:Unschedule()
    GM.SyncModel:SetDataInconsistent(true)
    self.m_accountNotice = {
      type = EAccountNotice.Cheat
    }
    endLoginHandle = true
  elseif tbLoginResp.rcode == 2 then
    self.m_accountNotice = {
      type = EAccountNotice.AccountDeleted,
      msg = tbLoginResp.logout_time
    }
    endLoginHandle = true
  elseif tbLoginResp.rcode == 1 then
    self.m_accountNotice = {
      type = EAccountNotice.LowVersion
    }
    GM.SyncModel:ChangeSyncState(ESyncState.VersionError)
  end
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.AccountNoticeWindow) then
    self.m_accountNotice = nil
    return endLoginHandle
  end
  if GM.SceneManager:GetGameMode() ~= EGameMode.Loading or endLoginHandle then
    self:OnSceneViewLoaded()
  end
  return endLoginHandle
end

function GameModel:OnSceneViewLoaded()
  if self.m_accountNotice ~= nil then
    GM.UIManager:OpenView(UIPrefabConfigName.AccountNoticeWindow, self.m_accountNotice)
    self.m_accountNotice = nil
  end
end

function GameModel:GetGroupName()
  return self.m_groupName or ""
end

function GameModel:CheckCatalogAndRestart(isLoading)
  if GM == nil or self.m_isCheckingCatalog then
    return
  end
  local minLevel = GM.ConfigModel:GetGeneralConfByType(EGeneralConfType.HotfixMinLevel) or 5
  if minLevel > GM.LevelModel:GetCurrentLevel() then
    return
  end
  if self.m_restartNextTime then
    self.m_restartNextTime = nil
    self:_HotfixRestart()
    return
  end
  self.m_isCheckingCatalog = true
  AddressableManager.Instance:CheckCatalogWithoutUpdate(function(result)
    self.m_isCheckingCatalog = false
    if result and GM then
      if isLoading and GM.SceneManager:GetGameMode() ~= EGameMode.Loading then
        self.m_restartNextTime = true
        GM.BIManager:LogProject(EBIProjectType.SkipHotfixRestart)
      else
        self:_HotfixRestart()
      end
    end
  end)
end

function GameModel:_HotfixRestart()
  GM.BIManager:LogNet(EBIType.NetworkCheckAction.ValidRestart)
  GM:RestartGame(ERestartType.WithHotfix, EBIProjectType.RestartGameAction.UpdateHint)
end
