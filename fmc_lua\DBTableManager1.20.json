{"finishedTasks": [[{"chapterId": 1, "taskId": 1, "value": 25}, {"chapterId": 1, "taskId": 2, "value": 55}, {"chapterId": 1, "taskId": 3, "value": 49}, {"chapterId": 1, "taskId": 4, "value": 33}, {"chapterId": 1, "taskId": 5, "value": 93}, {"chapterId": 1, "taskId": 6, "value": 80}, {"chapterId": 1, "taskId": 7, "value": 51}, {"chapterId": 1, "taskId": 8, "value": 68}, {"chapterId": 1, "taskId": 9, "value": 38}, {"chapterId": 1, "taskId": 10, "value": 100}], [{"chapterId": 2, "taskId": 1, "value": 135}, {"chapterId": 2, "taskId": 2, "value": 135}, {"chapterId": 2, "taskId": 3, "value": 135}, {"chapterId": 2, "taskId": 4, "value": 135}, {"chapterId": 2, "taskId": 5, "value": 135}, {"chapterId": 2, "taskId": 6, "value": 135}, {"chapterId": 2, "taskId": 7, "value": 149}, {"chapterId": 2, "taskId": 8, "value": 149}, {"chapterId": 2, "taskId": 9, "value": 149}, {"chapterId": 2, "taskId": 10, "value": 149}, {"chapterId": 2, "taskId": 11, "value": 149}, {"chapterId": 2, "taskId": 12, "value": 149}, {"chapterId": 2, "taskId": 13, "value": 149}, {"chapterId": 2, "taskId": 14, "value": 149}, {"chapterId": 2, "taskId": 15, "value": 149}, {"chapterId": 2, "taskId": 16, "value": 149}, {"chapterId": 2, "taskId": 17, "value": 149}, {"chapterId": 2, "taskId": 18, "value": 149}, {"chapterId": 2, "taskId": 19, "value": 149}, {"chapterId": 2, "taskId": 20, "value": 149}, {"chapterId": 2, "taskId": 21, "value": 149}, {"chapterId": 2, "taskId": 22, "value": 149}, {"chapterId": 2, "taskId": 23, "value": 216}, {"chapterId": 2, "taskId": 24, "value": 216}, {"chapterId": 2, "taskId": 25, "value": 216}, {"chapterId": 2, "taskId": 26, "value": 216}, {"chapterId": 2, "taskId": 27, "value": 216}, {"chapterId": 2, "taskId": 28, "value": 216}, {"chapterId": 2, "taskId": 29, "value": 216}, {"chapterId": 2, "taskId": 30, "value": 216}], [{"chapterId": 3, "taskId": 1, "value": 335}, {"chapterId": 3, "taskId": 2, "value": 335}, {"chapterId": 3, "taskId": 3, "value": 335}, {"chapterId": 3, "taskId": 4, "value": 335}, {"chapterId": 3, "taskId": 5, "value": 335}, {"chapterId": 3, "taskId": 6, "value": 352}, {"chapterId": 3, "taskId": 7, "value": 352}, {"chapterId": 3, "taskId": 8, "value": 352}, {"chapterId": 3, "taskId": 9, "value": 355}, {"chapterId": 3, "taskId": 10, "value": 307}, {"chapterId": 3, "taskId": 11, "value": 307}, {"chapterId": 3, "taskId": 12, "value": 177}, {"chapterId": 3, "taskId": 13, "value": 307}, {"chapterId": 3, "taskId": 14, "value": 268}, {"chapterId": 3, "taskId": 15, "value": 361}, {"chapterId": 3, "taskId": 16, "value": 338}, {"chapterId": 3, "taskId": 17, "value": 361}, {"chapterId": 3, "taskId": 18, "value": 269}, {"chapterId": 3, "taskId": 19, "value": 291}, {"chapterId": 3, "taskId": 20, "value": 245}, {"chapterId": 3, "taskId": 21, "value": 285}, {"chapterId": 3, "taskId": 22, "value": 321}, {"chapterId": 3, "taskId": 23, "value": 538}, {"chapterId": 3, "taskId": 24, "value": 249}, {"chapterId": 3, "taskId": 25, "value": 430}, {"chapterId": 3, "taskId": 26, "value": 394}, {"chapterId": 3, "taskId": 27, "value": 357}, {"chapterId": 3, "taskId": 28, "value": 249}, {"chapterId": 3, "taskId": 29, "value": 240}, {"chapterId": 3, "taskId": 30, "value": 643}, {"chapterId": 3, "taskId": 31, "value": 285}, {"chapterId": 3, "taskId": 32, "value": 240}, {"chapterId": 3, "taskId": 33, "value": 598}, {"chapterId": 3, "taskId": 34, "value": 240}, {"chapterId": 3, "taskId": 35, "value": 509}, {"chapterId": 3, "taskId": 36, "value": 374}, {"chapterId": 3, "taskId": 37, "value": 474}, {"chapterId": 3, "taskId": 38, "value": 448}, {"chapterId": 3, "taskId": 39, "value": 367}, {"chapterId": 3, "taskId": 40, "value": 393}, {"chapterId": 3, "taskId": 41, "value": 448}, {"chapterId": 3, "taskId": 42, "value": 367}, {"chapterId": 3, "taskId": 43, "value": 394}, {"chapterId": 3, "taskId": 44, "value": 395}], [{"chapterId": 4, "taskId": 1, "value": 499}, {"chapterId": 4, "taskId": 2, "value": 563}, {"chapterId": 4, "taskId": 3, "value": 531}, {"chapterId": 4, "taskId": 4, "value": 467}, {"chapterId": 4, "taskId": 5, "value": 436}, {"chapterId": 4, "taskId": 6, "value": 467}, {"chapterId": 4, "taskId": 7, "value": 467}, {"chapterId": 4, "taskId": 8, "value": 521}, {"chapterId": 4, "taskId": 9, "value": 571}, {"chapterId": 4, "taskId": 10, "value": 521}, {"chapterId": 4, "taskId": 11, "value": 571}, {"chapterId": 4, "taskId": 12, "value": 521}, {"chapterId": 4, "taskId": 13, "value": 521}, {"chapterId": 4, "taskId": 14, "value": 521}, {"chapterId": 4, "taskId": 15, "value": 595}, {"chapterId": 4, "taskId": 16, "value": 595}, {"chapterId": 4, "taskId": 17, "value": 544}, {"chapterId": 4, "taskId": 18, "value": 696}, {"chapterId": 4, "taskId": 19, "value": 493}, {"chapterId": 4, "taskId": 20, "value": 544}, {"chapterId": 4, "taskId": 21, "value": 592}, {"chapterId": 4, "taskId": 22, "value": 572}, {"chapterId": 4, "taskId": 23, "value": 572}, {"chapterId": 4, "taskId": 24, "value": 615}, {"chapterId": 4, "taskId": 25, "value": 615}, {"chapterId": 4, "taskId": 26, "value": 657}, {"chapterId": 4, "taskId": 27, "value": 699}, {"chapterId": 4, "taskId": 28, "value": 742}, {"chapterId": 4, "taskId": 29, "value": 614}, {"chapterId": 4, "taskId": 30, "value": 724}, {"chapterId": 4, "taskId": 31, "value": 613}, {"chapterId": 4, "taskId": 32, "value": 778}, {"chapterId": 4, "taskId": 33, "value": 724}, {"chapterId": 4, "taskId": 34, "value": 669}, {"chapterId": 4, "taskId": 35, "value": 613}, {"chapterId": 4, "taskId": 36, "value": 613}, {"chapterId": 4, "taskId": 37, "value": 668}, {"chapterId": 4, "taskId": 38, "value": 792}, {"chapterId": 4, "taskId": 39, "value": 737}, {"chapterId": 4, "taskId": 40, "value": 681}, {"chapterId": 4, "taskId": 41, "value": 625}, {"chapterId": 4, "taskId": 42, "value": 681}, {"chapterId": 4, "taskId": 43, "value": 681}, {"chapterId": 4, "taskId": 44, "value": 624}, {"chapterId": 4, "taskId": 45, "value": 680}], [{"chapterId": 5, "taskId": 1, "value": 666}, {"chapterId": 5, "taskId": 2, "value": 593}, {"chapterId": 5, "taskId": 3, "value": 519}, {"chapterId": 5, "taskId": 4, "value": 519}, {"chapterId": 5, "taskId": 5, "value": 593}, {"chapterId": 5, "taskId": 6, "value": 629}, {"chapterId": 5, "taskId": 7, "value": 666}, {"chapterId": 5, "taskId": 8, "value": 594}, {"chapterId": 5, "taskId": 9, "value": 697}, {"chapterId": 5, "taskId": 10, "value": 571}, {"chapterId": 5, "taskId": 11, "value": 571}, {"chapterId": 5, "taskId": 12, "value": 655}, {"chapterId": 5, "taskId": 13, "value": 655}, {"chapterId": 5, "taskId": 14, "value": 655}, {"chapterId": 5, "taskId": 15, "value": 614}, {"chapterId": 5, "taskId": 16, "value": 697}, {"chapterId": 5, "taskId": 17, "value": 633}, {"chapterId": 5, "taskId": 18, "value": 675}, {"chapterId": 5, "taskId": 19, "value": 633}, {"chapterId": 5, "taskId": 20, "value": 759}, {"chapterId": 5, "taskId": 21, "value": 675}, {"chapterId": 5, "taskId": 22, "value": 718}, {"chapterId": 5, "taskId": 23, "value": 633}, {"chapterId": 5, "taskId": 24, "value": 718}, {"chapterId": 5, "taskId": 25, "value": 703}, {"chapterId": 5, "taskId": 26, "value": 850}, {"chapterId": 5, "taskId": 27, "value": 655}, {"chapterId": 5, "taskId": 28, "value": 703}, {"chapterId": 5, "taskId": 29, "value": 557}, {"chapterId": 5, "taskId": 30, "value": 753}, {"chapterId": 5, "taskId": 31, "value": 753}, {"chapterId": 5, "taskId": 32, "value": 801}, {"chapterId": 5, "taskId": 33, "value": 769}, {"chapterId": 5, "taskId": 34, "value": 769}, {"chapterId": 5, "taskId": 35, "value": 769}, {"chapterId": 5, "taskId": 36, "value": 865}, {"chapterId": 5, "taskId": 37, "value": 816}]], "cacheItem": {"1751854106001": {"type": 2, "id": "1751854106001", "cost": "{}", "codeStr": "additem_1"}, "1751854113002": {"type": 2, "id": "1751854113002", "cost": "{}", "codeStr": "eq_1_3"}, "1751854113003": {"type": 2, "id": "1751854113003", "cost": "{}", "codeStr": "greenbox_1"}, "1751854113001": {"type": 2, "id": "1751854113001", "cost": "{}", "codeStr": "pd_7_3"}}, "config": {"key": {"key": "key", "value": {"chapterUpdate": {"config": [{"chapter": 5, "enable": 1, "openDay": 1735178400, "preDay": 1735005600, "dayLevel": 75}, {"chapter": 6, "enable": 1, "openDay": 1736388000, "preDay": 1736215200, "dayLevel": 100}, {"chapter": 7, "enable": 1, "openDay": 1737597600, "preDay": 1737424800, "dayLevel": 125}, {"chapter": 8, "enable": 1, "openDay": 1739412000, "preDay": 1739239200, "dayLevel": 150}, {"chapter": 9, "enable": 1, "openDay": 1740621600, "preDay": 1740448800, "dayLevel": 165}, {"chapter": 9, "enable": 1, "openDay": 1741226400, "preDay": 1741053600, "dayLevel": 180}, {"chapter": 10, "enable": 1, "openDay": 1741831200, "preDay": 1741658400, "dayLevel": 195}, {"chapter": 10, "enable": 1, "openDay": 1742436000, "preDay": 1741831200, "dayLevel": 210}, {"chapter": 11, "enable": 1, "openDay": 1743040800, "preDay": 1742868000, "dayLevel": 225}, {"chapter": 11, "enable": 1, "openDay": 1743645600, "preDay": 1743040800, "dayLevel": 240}, {"chapter": 12, "enable": 1, "openDay": 1744250400, "preDay": 1744077600, "dayLevel": 255}, {"chapter": 12, "enable": 1, "openDay": 1744855200, "preDay": 1744250400, "dayLevel": 270}, {"chapter": 13, "enable": 1, "openDay": 1745460000, "preDay": 1745287200, "dayLevel": 285}, {"chapter": 13, "enable": 1, "openDay": 1746064800, "preDay": 1745460000, "dayLevel": 300}, {"chapter": 14, "enable": 1, "openDay": 1746669600, "preDay": 1746496800, "dayLevel": 315}, {"chapter": 14, "enable": 1, "openDay": 1747274400, "preDay": 1746669600, "dayLevel": 330}, {"chapter": 15, "enable": 1, "openDay": 1747879200, "preDay": 1747706400, "dayLevel": 345}, {"chapter": 15, "enable": 1, "openDay": 1748484000, "preDay": 1747879200, "dayLevel": 360}, {"chapter": 16, "enable": 1, "openDay": 1749088800, "preDay": 1748916000, "dayLevel": 375}, {"chapter": 16, "enable": 1, "openDay": 1749693600, "preDay": 1749088800, "dayLevel": 390}, {"chapter": 17, "enable": 1, "openDay": 1750298400, "preDay": 1750125600, "dayLevel": 405}, {"chapter": 17, "enable": 1, "openDay": 1750903200, "preDay": 1750298400, "dayLevel": 420}, {"chapter": 18, "enable": 1, "openDay": 1751508000, "preDay": 1751335200, "dayLevel": 435}, {"chapter": 18, "enable": 1, "openDay": 1752112800, "preDay": 1751508000, "dayLevel": 450}], "md5": "a39a6f48cf4d0cf5e86049db6851441a"}, "bakeOut": {"config": {"eTime": 1752112800, "id": 100024, "sTime": 1751508600, "bakeout_rank_exchange": [{"cost": 10, "time": 1}, {"cost": 11, "time": 2}, {"cost": 14, "time": 3}, {"cost": 17, "time": 4}, {"cost": 21, "time": 5}, {"cost": 28, "time": 6}, {"cost": 37, "time": 7}, {"cost": 51, "time": 8}, {"cost": 72, "time": 9}, {"cost": 100, "time": 10}], "rTime": 1752113400, "bakeout_rank_reward": [{"start_rank": 1, "end_rank": 1, "rewards": ["gem-25", "energy-100", "skiptime_1-1"]}, {"start_rank": 2, "end_rank": 2, "rewards": ["gem-20", "energy-80", "additem_1-1"]}, {"start_rank": 3, "end_rank": 3, "rewards": ["gem-15", "energy-50"]}, {"start_rank": 4, "end_rank": 6, "rewards": ["gem-10"]}, {"start_rank": 7, "end_rank": 10, "rewards": ["energy-50"]}, {"start_rank": 11, "end_rank": 15, "rewards": ["skipprop-50"]}, {"start_rank": 16, "end_rank": 20, "rewards": ["skipprop-25"]}], "bakeout_rank_parameter": [{"noRegisterTime": 300, "uploadTime": 60, "delayTime": 90, "settlementTime": 300, "maxNum": 20, "retainTime": 601200}], "staticInclude": ["bakeout_rank_reward#1st", "bakeout_rank_exchange#default", "bakeout_rank_parameter#1st"]}, "md5": "8baa3057be53fe4cf0e61a5242abb329"}, "pkRace": {"config": {"include": ["pk_race#pk_race", "order_token_control#pk_race"], "pk_race": [{"playerNumWeight": [{"group_type": "pk", "weight": 10, "child_type": "easy"}, {"group_type": "pk", "weight": 10, "child_type": "normal"}, {"group_type": "pk", "weight": 80, "child_type": "fail"}], "round": 1, "rankReward1": [{"Currency": "energy", "Amount": 30}], "target": 30, "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Currency": "energy", "Amount": 10}]}, {"target": 30, "playerNumWeight": [{"group_type": "pk", "weight": 60, "child_type": "easy"}, {"group_type": "pk", "weight": 30, "child_type": "normal"}, {"group_type": "pk", "weight": 10, "child_type": "hard"}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 2, "roundRewards": [{"Currency": "skipprop", "Amount": 20}], "uiCode": "dig_chest_2", "rankReward1": [{"Currency": "greenbox_1", "Amount": 1}], "rankReward2": [{"Currency": "cbox1_1", "Amount": 1}]}, {"playerNumWeight": [{"group_type": "pk", "weight": 30, "child_type": "easy"}, {"group_type": "pk", "weight": 30, "child_type": "normal"}, {"group_type": "pk", "weight": 30, "child_type": "hard"}], "round": 3, "rankReward1": [{"Currency": "energy", "Amount": 40}], "target": 50, "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Currency": "energy", "Amount": 15}]}, {"target": 50, "playerNumWeight": [{"group_type": "pk", "weight": 20, "child_type": "easy"}, {"group_type": "pk", "weight": 40, "child_type": "normal"}, {"group_type": "pk", "weight": 40, "child_type": "hard"}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 4, "roundRewards": [{"Currency": "additem_1", "Amount": 1}], "uiCode": "dig_chest_2", "rankReward1": [{"Currency": "energy", "Amount": 40}], "rankReward2": [{"Currency": "energy", "Amount": 15}]}, {"playerNumWeight": [{"group_type": "pk", "weight": 20, "child_type": "easy"}, {"group_type": "pk", "weight": 30, "child_type": "normal"}, {"group_type": "pk", "weight": 50, "child_type": "hard"}], "round": 5, "rankReward1": [{"Currency": "energy", "Amount": 50}], "target": 70, "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Currency": "energy", "Amount": 20}]}, {"playerNumWeight": [{"group_type": "pk", "weight": 20, "child_type": "easy"}, {"group_type": "pk", "weight": 30, "child_type": "normal"}, {"group_type": "pk", "weight": 50, "child_type": "hard"}], "round": 6, "rankReward1": [{"Currency": "greenbox_1", "Amount": 1}], "target": 70, "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Currency": "cbox1_1", "Amount": 1}]}, {"target": 100, "playerNumWeight": [{"group_type": "pk", "weight": 20, "child_type": "easy"}, {"group_type": "pk", "weight": 30, "child_type": "normal"}, {"group_type": "pk", "weight": 50, "child_type": "hard"}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 7, "roundRewards": [{"Currency": "skipprop", "Amount": 60}], "uiCode": "dig_chest_2", "rankReward1": [{"Currency": "energy", "Amount": 60}], "rankReward2": [{"Currency": "energy", "Amount": 25}]}, {"playerNumWeight": [{"group_type": "pk", "weight": 20, "child_type": "easy"}, {"group_type": "pk", "weight": 30, "child_type": "normal"}, {"group_type": "pk", "weight": 50, "child_type": "hard"}], "round": 8, "rankReward1": [{"Currency": "energy", "Amount": 80}], "target": 120, "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Currency": "energy", "Amount": 30}]}, {"playerNumWeight": [{"group_type": "pk", "weight": 20, "child_type": "easy"}, {"group_type": "pk", "weight": 30, "child_type": "normal"}, {"group_type": "pk", "weight": 50, "child_type": "hard"}], "round": 9, "rankReward1": [{"Currency": "greenbox2_1", "Amount": 1}], "target": 150, "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Currency": "cbox2_1", "Amount": 1}]}, {"target": 150, "playerNumWeight": [{"group_type": "pk", "weight": 20, "child_type": "easy"}, {"group_type": "pk", "weight": 30, "child_type": "normal"}, {"group_type": "pk", "weight": 50, "child_type": "hard"}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 10, "roundRewards": [{"Currency": "energy", "Amount": 150}], "uiCode": "dig_chest_3", "rankReward1": [{"Currency": "energy", "Amount": 80}], "rankReward2": [{"Currency": "energy", "Amount": 30}]}], "sTime": 1751594400, "rTime": 1752026400, "sLv": 7, "eTime": 1751853600, "id": 118, "order_token_control": [{"ratio": 0.33, "rewardsWeight": [{"Weight": 100, "Currency": "pkRace", "Amount": 1}], "roundType": "down_1"}]}, "md5": "5cf38c6eb494d95d31cacf89d24f7eff"}, "notify": {"config": [{"maxNum": 2, "interval": 86400, "pushtimetype": 0, "scene": ["EnergyRefill", "ComeBack_24", "ComeBack_48", "ItemCooldown"], "id": 1}], "md5": "47c1751e7551193850791d81b02617b6"}, "coinRace": {"config": {"include": ["coin_race#round10", "generalActivityConf#round10", "generalActivityConf#showRound"], "eTime": 1752199200, "generalActivityConf": [{"confType": "divided", "param_int": 22}, {"confType": "coinrace_show_round", "param_int": 1}], "rTime": 1752372000, "sLv": 7, "sTime": 1751853600, "id": 121, "coin_race": [{"playerNumWeight": [{"group_type": "new_suc", "weight": 20, "child_type": "easy"}, {"group_type": "new_suc", "weight": 30, "child_type": "normal"}, {"group_type": "new_suc", "weight": 40, "child_type": "hard"}], "rankReward3": [{"Currency": "skipprop", "Amount": 10}, {"Currency": "energy", "Amount": 10}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "skipprop", "Amount": 20}, {"Currency": "energy", "Amount": 20}], "target": 20, "round": 1, "rankReward2": [{"Currency": "skipprop", "Amount": 15}, {"Currency": "energy", "Amount": 15}]}, {"playerNumWeight": [{"group_type": "new_suc", "weight": 20, "child_type": "easy"}, {"group_type": "new_suc", "weight": 30, "child_type": "normal"}, {"group_type": "new_suc", "weight": 40, "child_type": "hard"}], "rankReward3": [{"Currency": "skipprop", "Amount": 20}, {"Currency": "energy", "Amount": 10}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "skipprop", "Amount": 25}, {"Currency": "energy", "Amount": 30}], "target": 25, "round": 2, "rankReward2": [{"Currency": "skipprop", "Amount": 20}, {"Currency": "energy", "Amount": 20}]}, {"playerNumWeight": [{"group_type": "new_suc", "weight": 20, "child_type": "easy"}, {"group_type": "new_suc", "weight": 30, "child_type": "normal"}, {"group_type": "new_suc", "weight": 40, "child_type": "hard"}], "rankReward3": [{"Currency": "skipprop", "Amount": 20}, {"Currency": "energy", "Amount": 15}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "skipprop", "Amount": 30}, {"Currency": "energy", "Amount": 35}], "target": 30, "round": 3, "rankReward2": [{"Currency": "skipprop", "Amount": 25}, {"Currency": "energy", "Amount": 25}]}, {"playerNumWeight": [{"group_type": "new_suc", "weight": 20, "child_type": "easy"}, {"group_type": "new_suc", "weight": 30, "child_type": "normal"}, {"group_type": "new_suc", "weight": 40, "child_type": "hard"}], "rankReward3": [{"Currency": "skipprop", "Amount": 20}, {"Currency": "energy", "Amount": 20}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "additem_1", "Amount": 1}, {"Currency": "energy", "Amount": 45}], "target": 35, "round": 4, "rankReward2": [{"Currency": "additem_1", "Amount": 1}, {"Currency": "energy", "Amount": 25}]}, {"playerNumWeight": [{"group_type": "new_suc", "weight": 20, "child_type": "easy"}, {"group_type": "new_suc", "weight": 30, "child_type": "normal"}, {"group_type": "new_suc", "weight": 40, "child_type": "hard"}], "rankReward3": [{"Currency": "skipprop", "Amount": 25}, {"Currency": "energy", "Amount": 20}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "gem", "Amount": 5}, {"Currency": "skipprop", "Amount": 40}, {"Currency": "greenbox_1", "Amount": 1}], "target": 40, "round": 5, "rankReward2": [{"Currency": "gem", "Amount": 5}, {"Currency": "greenbox_1", "Amount": 1}]}, {"playerNumWeight": [{"group_type": "new_suc", "weight": 20, "child_type": "easy"}, {"group_type": "new_suc", "weight": 30, "child_type": "normal"}, {"group_type": "new_suc", "weight": 40, "child_type": "hard"}], "rankReward3": [{"Currency": "skipprop", "Amount": 30}, {"Currency": "energy", "Amount": 20}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "skipprop", "Amount": 40}, {"Currency": "energy", "Amount": 45}], "target": 40, "round": 6, "rankReward2": [{"Currency": "skipprop", "Amount": 30}, {"Currency": "energy", "Amount": 30}]}, {"playerNumWeight": [{"group_type": "new_suc", "weight": 20, "child_type": "easy"}, {"group_type": "new_suc", "weight": 30, "child_type": "normal"}, {"group_type": "new_suc", "weight": 40, "child_type": "hard"}], "rankReward3": [{"Currency": "skipprop", "Amount": 30}, {"Currency": "energy", "Amount": 25}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "skipprop", "Amount": 60}, {"Currency": "energy", "Amount": 50}], "target": 50, "round": 7, "rankReward2": [{"Currency": "additem_1", "Amount": 1}, {"Currency": "energy", "Amount": 35}]}, {"playerNumWeight": [{"group_type": "new_suc", "weight": 20, "child_type": "easy"}, {"group_type": "new_suc", "weight": 30, "child_type": "normal"}, {"group_type": "new_suc", "weight": 40, "child_type": "hard"}], "rankReward3": [{"Currency": "skipprop", "Amount": 40}, {"Currency": "energy", "Amount": 25}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "gem", "Amount": 5}, {"Currency": "skipprop", "Amount": 60}, {"Currency": "energy", "Amount": 50}], "target": 55, "round": 8, "rankReward2": [{"Currency": "skipprop", "Amount": 50}, {"Currency": "energy", "Amount": 40}]}, {"playerNumWeight": [{"group_type": "new_suc", "weight": 20, "child_type": "easy"}, {"group_type": "new_suc", "weight": 30, "child_type": "normal"}, {"group_type": "new_suc", "weight": 40, "child_type": "hard"}], "rankReward3": [{"Currency": "skipprop", "Amount": 40}, {"Currency": "energy", "Amount": 30}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "greenbox_1", "Amount": 1}, {"Currency": "skipprop", "Amount": 60}, {"Currency": "energy", "Amount": 50}], "target": 65, "round": 9, "rankReward2": [{"Currency": "skipprop", "Amount": 60}, {"Currency": "energy", "Amount": 45}]}, {"playerNumWeight": [{"group_type": "new_suc", "weight": 20, "child_type": "easy"}, {"group_type": "new_suc", "weight": 30, "child_type": "normal"}, {"group_type": "new_suc", "weight": 40, "child_type": "hard"}], "rankReward3": [{"Currency": "skipprop", "Amount": 60}, {"Currency": "energy", "Amount": 30}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "gem", "Amount": 10}, {"Currency": "skipprop", "Amount": 180}, {"Currency": "energy", "Amount": 50}], "target": 75, "round": 10, "rankReward2": [{"Currency": "skipprop", "Amount": 70}, {"Currency": "energy", "Amount": 50}]}]}, "md5": "9062c4ed8a3a9f1b9b7e9ab68355df09"}, "bp7": {"config": {"include": ["battlePassReward#test1", "battlePassTask#test1", "generalActivityConf#bpClone"], "generalActivityConf": [{"confType": "battlePassClone", "param_int": 2}], "sLv": 7, "battlePassReward": [{"require": 0, "level": 1, "reward": [{"Amount": 30, "Crypt": "pH", "Currency": "energy"}], "golden_reward": [{"Amount": 150, "Crypt": "rMZ", "Currency": "energy"}]}, {"require": 60, "level": 2, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_1_2_2"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}]}, {"require": 90, "level": 3, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_2"}], "golden_reward": [{"Amount": 50, "Crypt": "vH", "Currency": "skipprop"}]}, {"require": 120, "level": 4, "reward": [{"Amount": 30, "Crypt": "pH", "Currency": "skipprop"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "additem_1"}]}, {"require": 150, "level": 5, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_1_1_5"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_3"}]}, {"require": 180, "level": 6, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_2"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_1_1_6"}]}, {"require": 220, "level": 7, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "freebox_1"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}]}, {"require": 260, "level": 8, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "additem_1"}], "golden_reward": [{"Amount": 100, "Crypt": "rHZ", "Currency": "skipprop"}]}, {"require": 280, "level": 9, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_1"}], "golden_reward": [{"Amount": 2, "Crypt": "q", "Currency": "additem_1"}]}, {"require": 340, "level": 10, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "greenbox_1"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "enebox_1"}]}, {"require": 380, "level": 11, "reward": [{"Amount": 50, "Crypt": "vH", "Currency": "skipprop"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_2_3_2"}]}, {"require": 410, "level": 12, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_2_3_1"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_3"}]}, {"require": 460, "level": 13, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_1"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}]}, {"require": 500, "level": 14, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_2"}], "golden_reward": [{"Amount": 100, "Crypt": "rHZ", "Currency": "skipprop"}]}, {"require": 540, "level": 15, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "freebox_1"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_4"}]}, {"require": 580, "level": 16, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "additem_1"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "freebox_1"}]}, {"require": 640, "level": 17, "reward": [{"Amount": 50, "Crypt": "vH", "Currency": "skipprop"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_4"}]}, {"require": 700, "level": 18, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_3_1_7"}]}, {"require": 780, "level": 19, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_3_1_6"}], "golden_reward": [{"Amount": 2, "Crypt": "q", "Currency": "additem_1"}]}, {"require": 850, "level": 20, "reward": [{"Amount": 30, "Crypt": "pH", "Currency": "energy"}], "golden_reward": [{"Amount": 50, "Crypt": "vH", "Currency": "energy"}]}, {"require": 940, "level": 21, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_3"}], "golden_reward": [{"Amount": 10, "Crypt": "rH", "Currency": "gem"}]}, {"require": 1020, "level": 22, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_5"}]}, {"require": 1120, "level": 23, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "enebox_1"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_3_2_5"}]}, {"require": 1220, "level": 24, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "additem_1"}], "golden_reward": [{"Amount": 2, "Crypt": "q", "Currency": "additem_1"}]}, {"require": 1320, "level": 25, "reward": [{"Amount": 50, "Crypt": "vH", "Currency": "skipprop"}], "golden_reward": [{"Amount": 100, "Crypt": "rHZ", "Currency": "skipprop"}]}, {"require": 1430, "level": 26, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_3_2_4"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "enebox_1"}]}, {"require": 1540, "level": 27, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_3"}]}, {"require": 1650, "level": 28, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_4"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_4_2_5"}]}, {"require": 1760, "level": 29, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_4_2_4"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_4"}]}, {"require": 1880, "level": 30, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "additem_1"}], "golden_reward": [{"Amount": 15, "Crypt": "rM", "Currency": "gem"}]}, {"require": 2000, "level": 31, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_3"}, {"Amount": 1, "Crypt": "r", "Currency": "greenbox_1"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "enebox_1"}, {"Amount": 1, "Crypt": "r", "Currency": "greenbox_1"}]}, {"require": 2120, "level": 32, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_4"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_5"}]}, {"require": 2220, "level": 33, "reward": [{"Amount": 50, "Crypt": "vH", "Currency": "skipprop"}], "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_4"}]}, {"require": 2360, "level": 34, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_4"}], "golden_reward": [{"Amount": 100, "Crypt": "rHZ", "Currency": "skipprop"}]}, {"require": 2400, "level": 35, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "enebox_1"}], "golden_reward": [{"Amount": 25, "Crypt": "qM", "Currency": "gem"}]}, {"require": 2480, "level": 36, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "skiptime_1"}], "golden_reward": [{"Amount": 100, "Crypt": "rHZ", "Currency": "energy"}, {"Amount": 1, "Crypt": "r", "Currency": "skiptime_1"}, {"Amount": 1, "Crypt": "r", "Currency": "greenbox_1"}]}], "sTime": 1749607200, "rTime": 1752199200, "battlePassTask": [{"time": 1, "type": "timelimit", "order": 1, "tasks": [{"tokenNum": 40, "type": "customer", "count": 2}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 25, "type": "gem", "count": 5}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}]}, {"time": 2, "type": "timelimit", "order": 2, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 50, "type": "gold", "count": 500}, {"tokenNum": 60, "type": "gold", "count": 600}, {"tokenNum": 70, "type": "gold", "count": 700}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 100, "type": "gold", "count": 1000}, {"tokenNum": 120, "type": "gold", "count": 1200}]}, {"time": 3, "type": "timelimit", "order": 3, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 45, "type": "gold", "count": 450}, {"tokenNum": 50, "type": "gold", "count": 500}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 25, "type": "gem", "count": 5}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}]}, {"time": 4, "type": "timelimit", "order": 4, "tasks": [{"tokenNum": 40, "type": "customer", "count": 2}, {"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 60, "type": "gold", "count": 600}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 110, "type": "gold", "count": 1100}]}, {"time": 5, "type": "timelimit", "order": 5, "tasks": [{"tokenNum": 40, "type": "customer", "count": 2}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 140, "type": "customer", "count": 7}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 60, "type": "gold", "count": 600}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 120, "type": "gold", "count": 1200}]}, {"time": 6, "type": "timelimit", "order": 6, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 140, "type": "customer", "count": 7}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 220, "type": "customer", "count": 11}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}, {"tokenNum": 100, "type": "gem", "count": 20}]}, {"time": 7, "type": "timelimit", "order": 7, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 50, "type": "gold", "count": 500}, {"tokenNum": 60, "type": "gold", "count": 600}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 90, "type": "gold", "count": 900}, {"tokenNum": 110, "type": "gold", "count": 1100}, {"tokenNum": 130, "type": "gold", "count": 1300}]}, {"time": 8, "type": "timelimit", "order": 8, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 50, "type": "gold", "count": 500}, {"tokenNum": 65, "type": "gold", "count": 650}, {"tokenNum": 100, "type": "gold", "count": 1000}, {"tokenNum": 25, "type": "gem", "count": 5}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}]}, {"time": 9, "type": "timelimit", "order": 9, "tasks": [{"tokenNum": 40, "type": "customer", "count": 2}, {"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 140, "type": "customer", "count": 7}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 90, "type": "gold", "count": 900}, {"tokenNum": 120, "type": "gold", "count": 1200}]}, {"time": 10, "type": "timelimit", "order": 10, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 45, "type": "gold", "count": 450}, {"tokenNum": 60, "type": "gold", "count": 600}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 25, "type": "gem", "count": 5}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}]}, {"time": 11, "type": "timelimit", "order": 11, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 90, "type": "gold", "count": 900}, {"tokenNum": 120, "type": "gold", "count": 1200}]}, {"time": 12, "type": "timelimit", "order": 12, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 220, "type": "customer", "count": 11}, {"tokenNum": 60, "type": "gold", "count": 600}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 100, "type": "gold", "count": 1000}]}, {"time": 13, "type": "timelimit", "order": 13, "tasks": [{"tokenNum": 40, "type": "customer", "count": 2}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 140, "type": "customer", "count": 7}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 70, "type": "gold", "count": 700}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 110, "type": "gold", "count": 1100}]}, {"time": 14, "type": "timelimit", "order": 14, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 140, "type": "customer", "count": 7}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 95, "type": "gold", "count": 950}, {"tokenNum": 110, "type": "gold", "count": 1100}]}, {"time": 15, "type": "timelimit", "order": 15, "tasks": [{"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 140, "type": "customer", "count": 7}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 220, "type": "customer", "count": 11}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 90, "type": "gold", "count": 900}, {"tokenNum": 120, "type": "gold", "count": 1200}]}, {"time": 16, "type": "timelimit", "order": 16, "tasks": [{"tokenNum": 40, "type": "customer", "count": 2}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 220, "type": "customer", "count": 11}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 90, "type": "gold", "count": 900}, {"tokenNum": 120, "type": "gold", "count": 1200}]}, {"time": 17, "type": "timelimit", "order": 17, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}, {"tokenNum": 100, "type": "gem", "count": 20}]}, {"time": 18, "type": "timelimit", "order": 18, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 50, "type": "gold", "count": 500}, {"tokenNum": 60, "type": "gold", "count": 600}, {"tokenNum": 85, "type": "gold", "count": 850}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}, {"tokenNum": 100, "type": "gem", "count": 20}]}, {"time": 19, "type": "timelimit", "order": 19, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 60, "type": "gold", "count": 600}, {"tokenNum": 70, "type": "gold", "count": 700}, {"tokenNum": 100, "type": "gold", "count": 1000}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}, {"tokenNum": 100, "type": "gem", "count": 20}]}, {"time": 20, "type": "timelimit", "order": 20, "tasks": [{"tokenNum": 40, "type": "customer", "count": 2}, {"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 140, "type": "customer", "count": 7}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 70, "type": "gold", "count": 700}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 90, "type": "gold", "count": 900}]}, {"time": 21, "type": "timelimit", "order": 21, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 60, "type": "gold", "count": 600}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 100, "type": "gold", "count": 1000}]}, {"time": 22, "type": "timelimit", "order": 22, "tasks": [{"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}, {"tokenNum": 100, "type": "gem", "count": 20}]}, {"time": 23, "type": "timelimit", "order": 23, "tasks": [{"tokenNum": 40, "type": "customer", "count": 2}, {"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 140, "type": "customer", "count": 7}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 70, "type": "gold", "count": 700}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 100, "type": "gold", "count": 1000}]}, {"time": 24, "type": "timelimit", "order": 24, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 45, "type": "gold", "count": 450}, {"tokenNum": 50, "type": "gold", "count": 500}, {"tokenNum": 80, "type": "gold", "count": 600}, {"tokenNum": 70, "type": "gold", "count": 700}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 95, "type": "gold", "count": 950}]}, {"time": 25, "type": "timelimit", "order": 25, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 50, "type": "gold", "count": 500}, {"tokenNum": 65, "type": "gold", "count": 650}, {"tokenNum": 85, "type": "gold", "count": 850}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}, {"tokenNum": 100, "type": "gem", "count": 20}]}, {"time": 26, "type": "timelimit", "order": 26, "tasks": [{"tokenNum": 80, "type": "customer", "count": 4}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 140, "type": "customer", "count": 7}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 220, "type": "customer", "count": 11}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 90, "type": "gold", "count": 900}, {"tokenNum": 100, "type": "gold", "count": 1000}]}, {"time": 27, "type": "timelimit", "order": 27, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 80, "type": "gold", "count": 800}, {"tokenNum": 90, "type": "gold", "count": 900}, {"tokenNum": 100, "type": "gold", "count": 1000}]}, {"time": 28, "type": "timelimit", "order": 28, "tasks": [{"tokenNum": 60, "type": "customer", "count": 3}, {"tokenNum": 100, "type": "customer", "count": 5}, {"tokenNum": 120, "type": "customer", "count": 6}, {"tokenNum": 160, "type": "customer", "count": 8}, {"tokenNum": 180, "type": "customer", "count": 9}, {"tokenNum": 200, "type": "customer", "count": 10}, {"tokenNum": 50, "type": "gem", "count": 10}, {"tokenNum": 75, "type": "gem", "count": 15}, {"tokenNum": 100, "type": "gem", "count": 20}]}, {"time": 0, "type": "cycle", "order": 29, "tasks": [{"tokenNum": 100, "type": "merge", "count": 100}]}], "id": 99, "eTime": 1752026400}, "md5": "e640c564ebec6fa5ac8a49b767eb7310"}, "general_conf": {"config": [{"confType": "balance_gold", "param_int": 1, "id": 1}, {"confType": "hint_inner_dish", "param_int": 1, "id": 3}, {"confType": "level_trans_day", "param_int": 1, "id": 4}, {"confType": "social_bind", "param_int": 1, "id": 5}, {"confType": "skipprop_not_enough", "param_int": 1, "id": 6}, {"confType": "ChangeInventory", "param_int": 1, "id": 7}, {"confType": "ingredient_recipe_hint", "param_int": 1, "id": 8}, {"sLv": 16, "param_int": 200, "id": 9, "confType": "doubleEnergy_trigger"}, {"confType": "cache", "param_int": 1, "id": 10}, {"confType": "new_order_reward_anim", "param_int": 1, "id": 11}, {"confType": "new_order_reward_anim_skip", "param_int": 1, "id": 12}, {"confType": "item_auto_recycle", "id": 14, "param_int_array": [3, 10]}, {"confType": "order_seq_energy_diff", "param_int": 1, "id": 15}, {"confType": "board_cook_bubble", "param_int": 1, "id": 16}, {"confType": "item_delete_corn_husk", "param_int": 1, "id": 17}, {"confType": "order_new_tag", "param_int": 1, "id": 18}, {"confType": "doubleEnergy_trigger_bonus", "param_int_array": [10, 15, 20, 15, 40, 20, 80, 25, 160, 30], "sTime": 1750644000, "id": 21}, {"confType": "facebook", "param_int": 1, "id": 22}], "md5": "4c2af17716a5a7ed5892f6e169226562"}, "extraBoard4": {"config": {"include": ["extraBoardConfig#extraBoard4b", "order_token_control#extraBoardA7", "produce_token_control#extraBoardA7"], "sLv": 8, "extraBoardConfig": [{"maxReward": [{"Currency": "energy", "Amount": 100}], "itemPrefix": "eb4", "maxLevel": 12, "board": "extraBoardA7"}], "order_token_control": [{"rewardsWeight": [{"Amount": 1, "Currency": "eb_1_1", "Weight": 100}], "score_max": 30, "score_min": 15}, {"rewardsWeight": [{"Amount": 1, "Currency": "eb_1_2", "Weight": 100}], "score_max": 60, "score_min": 30}, {"rewardsWeight": [{"Amount": 2, "Currency": "eb_1_2", "Weight": 100}], "score_max": 90, "score_min": 60}, {"rewardsWeight": [{"Amount": 3, "Currency": "eb_1_2", "Weight": 100}], "score_max": 120, "score_min": 90}, {"rewardsWeight": [{"Amount": 2, "Currency": "eb_1_3", "Weight": 100}], "score_max": 180, "score_min": 120}, {"rewardsWeight": [{"Amount": 3, "Currency": "eb_1_3", "Weight": 100}], "score_max": 240, "score_min": 180}, {"rewardsWeight": [{"Amount": 4, "Currency": "eb_1_3", "Weight": 100}], "score_min": 240}], "eTime": 1752458400, "sTime": 1751853600, "rTime": 1752544800, "id": 124, "produce_token_control": [{"rewardsWeight": [{"Amount": 1, "Currency": "eb_1_1", "Weight": 100}], "energyNum": 15}]}, "md5": "003daf5bdcd495cca8a267f0de999cfc"}, "rateUs": {"config": [{"contact": 0, "sLv": 5, "link": "market://details?id=com.cola.game", "id": 2, "task": [{"TaskCount": 30, "ChapterId": 2}, {"TaskCount": 29, "ChapterId": 3}]}], "md5": "cc9b438acb2247f9676ad310935caae2"}, "bundleController": {"config": [{"buyCD": 10080, "specialType": "starter", "dailyShowNum": 2, "duration": 720, "maxBuyNum": 1, "include": ["bundleContent#starter_499", "bundleTrigger#task_finished", "bundleCondition#starter_499"], "bundleContent": [{"bundleId": "starter_499", "price": 4.99, "discountTag": "200%", "payID": "starter_bundle_1", "content": [{"Currency": "gem", "Amount": 240}, {"Currency": "energy", "Amount": 200}, {"Currency": "additem_1", "Amount": 4}, {"Currency": "enebox_1", "Amount": 1}]}], "dailyBuyNum": 1, "groupId": "starter", "bundleCondition": [{"taskFinished": {"TaskCount": 3, "ChapterId": 2}}], "popCD": 360, "is_open": 1, "bundleTrigger": [{"popNum": 1, "trigger": "task_finished", "popOrder": ["starter"]}], "uiCode": "starter", "id": 1, "order": ["starter_499"]}, {"duration": 60, "sLv": 8, "eTime": 1765411200, "popCD": 60, "order": ["order_199"], "include": ["bundleContent#order_199", "bundleTrigger#finish_order_group"], "dailyShowNum": 5, "bundleContent": [{"bundleId": "order_199", "price": 1.99, "discountTag": "120%", "payID": "finish_order_199", "content": [{"Currency": "gem", "Amount": 40}, {"Currency": "energy", "Amount": 100}, {"Currency": "cbox3_1", "Amount": 2}]}], "groupId": "order1", "dailyBuyNum": 2, "buyCD": 30, "sTime": 1741053600, "specialType": "orderGroup", "is_open": 1, "uiCode": "orderGroup", "id": 14, "bundleTrigger": [{"popNum": 1, "trigger": "finish_order_group", "popOrder": ["orderGroup"]}]}, {"duration": 30, "sLv": 8, "eTime": 1765411200, "popCD": 5, "order": ["et1.99", "et3.99", "et5.99"], "buyCD": 30, "dailyShowNum": 10, "specialType": "multiTier", "groupId": "energytier1", "dailyBuyNum": 5, "include": ["bundleContent#et1.99", "bundleContent#et3.99", "bundleContent#et5.99", "bundleTrigger#lack_energy", "generalBundleConf#3.99"], "bundleContent": [{"bundleId": "et1.99", "price": 1.99, "discountTag": "125%", "payID": "energytier_199", "content": [{"Currency": "energy", "Amount": 250}], "originPrice": 2.99}, {"bundleId": "et3.99", "price": 3.99, "discountTag": "130%", "payID": "energytier_399", "content": [{"Currency": "energy", "Amount": 520}], "originPrice": 5.99}, {"bundleId": "et5.99", "price": 5.99, "discountTag": "140%", "payID": "energytier_599", "content": [{"Currency": "energy", "Amount": 820}], "originPrice": 8.99}], "generalBundleConf": [{"confType": "defaultOrder", "param_string": "et3.99"}], "sTime": 1742522400, "is_open": 1, "uiCode": "multiTier", "id": 20, "bundleTrigger": [{"popNum": 1, "trigger": "lack_energy", "popOrder": ["energy"]}]}, {"duration": 360, "sLv": 8, "eTime": 1765411200, "popCD": 60, "order": ["timespeed_199"], "buyCD": 30, "dailyShowNum": 10, "specialType": "cdFill", "groupId": "timespeed_1", "dailyBuyNum": 2, "include": ["bundleContent#timespeed_199", "bundleTrigger#pd_cd_number", "generalBundleConf#pd_cd_number"], "bundleContent": [{"bundleId": "timespeed_199", "payID": "cd_speed_199", "content": [{"Currency": "gem", "Amount": 40}, {"Currency": "energy", "Amount": 80}, {"Currency": "timespeed_1", "Amount": 1}], "price": 1.99}], "generalBundleConf": [{"confType": "pd_cd_number", "param_int": 4}], "sTime": 1749434400, "is_open": 1, "uiCode": "cdFill", "id": 76, "bundleTrigger": [{"popOrder": ["cdFill"], "trigger": "pd_cd_number"}]}, {"bundleContentChain": [{"bundleId": "chain_skip_1", "price": 0, "skin": "1", "step": 1, "content": [{"Currency": "skipprop", "Amount": 5}]}, {"bundleId": "chain_skip_2", "price": 0, "skin": "1", "step": 2, "content": [{"Currency": "skipprop", "Amount": 10}]}, {"bundleId": "chain_skip_3", "price": 0, "skin": "1", "step": 3, "content": [{"Currency": "ene_1", "Amount": 1}]}, {"bundleId": "chain_skip_4", "price": 1.99, "skin": "3", "payID": "chaingift_199", "step": 4, "content": [{"Currency": "gem", "Amount": 30}, {"Currency": "energy", "Amount": 150}]}, {"bundleId": "chain_skip_5", "price": 0, "skin": "1", "step": 5, "content": [{"Currency": "skipprop", "Amount": 15}]}, {"bundleId": "chain_skip_6", "price": 0, "skin": "2", "step": 6, "content": [{"Currency": "ene_2", "Amount": 1}]}, {"bundleId": "chain_skip_7", "price": 2.99, "skin": "3", "payID": "chaingift_299", "step": 7, "content": [{"Currency": "gem", "Amount": 40}, {"Currency": "energy", "Amount": 200}]}, {"bundleId": "chain_skip_8", "price": 0, "skin": "1", "step": 8, "content": [{"Currency": "ene_1", "Amount": 1}]}, {"bundleId": "chain_skip_9", "price": 0, "skin": "2", "step": 9, "content": [{"Currency": "skipprop", "Amount": 30}]}, {"bundleId": "chain_skip_10", "price": 0, "skin": "1", "step": 10, "content": [{"Currency": "gem", "Amount": 10}]}, {"bundleId": "chain_skip_11", "price": 0, "skin": "2", "step": 11, "content": [{"Currency": "ene_3", "Amount": 1}]}, {"bundleId": "chain_skip_12", "price": 3.99, "skin": "3", "payID": "chaingift_399", "step": 12, "content": [{"Currency": "gem", "Amount": 60}, {"Currency": "energy", "Amount": 250}]}, {"bundleId": "chain_skip_13", "price": 0, "skin": "1", "step": 13, "content": [{"Currency": "skipprop", "Amount": 40}]}, {"bundleId": "chain_skip_14", "price": 0, "skin": "2", "step": 14, "content": [{"Currency": "greenbox_1", "Amount": 1}]}, {"bundleId": "chain_skip_15", "price": 0, "skin": "1", "step": 15, "content": [{"Currency": "skipprop", "Amount": 50}]}, {"bundleId": "chain_skip_16", "price": 0, "skin": "2", "step": 16, "content": [{"Currency": "ene_4", "Amount": 1}]}, {"bundleId": "chain_skip_17", "price": 4.99, "skin": "3", "payID": "chaingift_499", "step": 17, "content": [{"Currency": "gem", "Amount": 80}, {"Currency": "energy", "Amount": 300}]}, {"bundleId": "chain_skip_18", "price": 0, "skin": "1", "step": 18, "content": [{"Currency": "skipprop", "Amount": 60}]}, {"bundleId": "chain_skip_19", "price": 0, "skin": "2", "step": 19, "content": [{"Currency": "gem", "Amount": 15}]}, {"bundleId": "chain_skip_20", "price": 0, "skin": "1", "step": 20, "content": [{"Currency": "skipprop", "Amount": 70}]}, {"bundleId": "chain_skip_21", "price": 0, "skin": "2", "step": 21, "content": [{"Currency": "greenbox2_1", "Amount": 1}]}, {"bundleId": "chain_skip_22", "price": 6.99, "skin": "3", "payID": "chaingift_699", "step": 22, "content": [{"Currency": "gem", "Amount": 100}, {"Currency": "energy", "Amount": 450}]}, {"bundleId": "chain_skip_23", "price": 0, "skin": "1", "step": 23, "content": [{"Currency": "skipprop", "Amount": 100}]}, {"bundleId": "chain_skip_24", "price": 0, "skin": "2", "step": 24, "content": [{"Currency": "greenbox2_1", "Amount": 1}]}, {"bundleId": "chain_skip_25", "price": 0, "skin": "1", "step": 25, "content": [{"Currency": "skipprop", "Amount": 150}]}, {"bundleId": "chain_skip_26", "price": 0, "skin": "2", "step": 26, "content": [{"Currency": "gem", "Amount": 20}]}, {"bundleId": "chain_skip_27", "price": 15.99, "skin": "3", "payID": "chaingift_1599", "step": 27, "content": [{"Currency": "gem", "Amount": 240}, {"Currency": "energy", "Amount": 1000}]}, {"bundleId": "chain_skip_28", "price": 0, "skin": "2", "step": 28, "content": [{"Currency": "greenbox2_1", "Amount": 1}]}, {"bundleId": "chain_skip_29", "price": 0, "skin": "1", "step": 29, "content": [{"Currency": "skipprop", "Amount": 200}]}, {"bundleId": "chain_skip_30", "price": 0, "skin": "2", "step": 30, "content": [{"Currency": "additem_3", "Amount": 1}]}, {"bundleId": "chain_skip_31", "price": 0, "skin": "1", "step": 31, "content": [{"Currency": "skipprop", "Amount": 400}]}, {"bundleId": "chain_skip_32", "price": 0, "skin": "2", "step": 32, "content": [{"Currency": "gem", "Amount": 50}]}, {"bundleId": "chain_skip_33", "price": 19.99, "skin": "3", "payID": "chaingift_1999", "step": 33, "content": [{"Currency": "gem", "Amount": 320}, {"Currency": "energy", "Amount": 1200}]}, {"bundleId": "chain_skip_34", "price": 0, "skin": "2", "step": 34, "content": [{"Currency": "skipprop", "Amount": 200}]}, {"bundleId": "chain_skip_35", "price": 0, "skin": "1", "step": 35, "content": [{"Currency": "greenbox2_1", "Amount": 2}]}, {"bundleId": "chain_skip_36", "price": 0, "skin": "2", "step": 36, "content": [{"Currency": "skipprop", "Amount": 400}]}, {"bundleId": "chain_skip_37", "price": 0, "skin": "1", "step": 37, "content": [{"Currency": "additem_3", "Amount": 2}]}, {"bundleId": "chain_skip_38", "price": 0, "skin": "2", "step": 38, "content": [{"Currency": "gem", "Amount": 100}]}], "dailyShowNum": 2, "duration": 4320, "groupId": "chain9", "is_open": 1, "include": ["bundleContentChain#chain_skip", "bundleTrigger#login"], "specialType": "chain", "eTime": 1751853600, "sLv": 8, "sTime": 1751594400, "rTime": 1751940000, "popCD": 5, "uiCode": "chain", "id": 85, "bundleTrigger": [{"trigger": "login"}]}], "md5": "b32b70911bb1e806130a4662545934f6"}, "flambeTime": {"config": {"include": ["generalActivityConf#mode"], "generalActivityConf": [{"confType": "fromOrderGroupConfig", "param_int": 1}, {"confType": "linkInstruSpeed", "param_int": 10}, {"confType": "modeInstruSpeed", "param_int": 10}], "sLv": 7, "eTime": 1802570400, "id": 115, "sTime": 1750644000}, "md5": "b2c26e81d71d7d26b46e3b95e5f9b4a3"}}}}, "shop": {"energyRefreshTime": {"key": "energyRefreshTime", "value": 1751853863}, "eventEnergyBuyCost": {"key": "eventEnergyBuyCost", "value": 10}, "refreshCost": {"key": "refreshCost", "value": 10}, "energyBuyCost": {"key": "energyBuyCost", "value": 10}, "dailyRefreshTime": {"key": "dailyRefreshTime", "value": 1751853863}, "refreshCostResetTime": {"key": "refreshCostResetTime", "value": 1751853863}, "flashRefreshTime": {"key": "flashRefreshTime", "value": 1751854113}}, "newSkin": {}, "weddingDay": {}, "taskMeta": {"OngoingChapterName": {"key": "OngoingChapterName", "value": "Wine"}, "TaskProgress": {"key": "TaskProgress", "value": 5037}, "ProgressId": {"key": "ProgressId", "value": "8"}, "ProgressTaskFinishCount": {"key": "ProgressTaskFinishCount", "value": "3"}, "OngoingChapterId": {"key": "OngoingChapterId", "value": 5}, "OngoingTaskIds": {"key": "OngoingTaskIds", "value": "38"}, "CleanGoldCost": {"key": "CleanGoldCost", "value": "1#19;2#17;3#15;4#15;5#17;6#18;7#19;8#17;9#20;10#16;11#16;12#19;13#19;14#19;15#18;16#20;17#18;18#19;19#18;20#22;21#19;22#21;23#18;24#21;25#20;26#24;27#19;28#20;29#16;30#22;31#22;32#23;33#22;34#22;35#22;36#25;37#23;38#21;39#21;40#19;41#26;42#24;43#22;44#23;45#19;46#26;47#21"}, "ChapterFinishWin": {"key": "ChapterFinishWin", "value": 4}, "TaskCleanGold": {"key": "TaskCleanGold", "value": 744}}, "activity": {"coinRace": {"data": "{\"windowOpened2\":{\"value\":1},\"playerDatas\":{\"value\":\"[{\\\"score\\\":\\\"[{\\\\\\\"score\\\\\\\":160,\\\\\\\"seconds\\\\\\\":51535},{\\\\\\\"score\\\\\\\":284,\\\\\\\"seconds\\\\\\\":51612},{\\\\\\\"score\\\\\\\":454,\\\\\\\"seconds\\\\\\\":51652}]\\\",\\\"id\\\":10000083662,\\\"icon\\\":\\\"head3\\\",\\\"track\\\":5,\\\"group_type\\\":\\\"new_fail\\\",\\\"name\\\":\\\"Patricija Done\\\"},{\\\"score\\\":\\\"[{\\\\\\\"score\\\\\\\":122,\\\\\\\"seconds\\\\\\\":366},{\\\\\\\"score\\\\\\\":333,\\\\\\\"seconds\\\\\\\":576}]\\\",\\\"id\\\":10000083659,\\\"icon\\\":\\\"head1\\\",\\\"track\\\":4,\\\"group_type\\\":\\\"new_fail\\\",\\\"name\\\":\\\"Melis\\\"},{\\\"score\\\":\\\"[{\\\\\\\"score\\\\\\\":143,\\\\\\\"seconds\\\\\\\":133},{\\\\\\\"score\\\\\\\":213,\\\\\\\"seconds\\\\\\\":522},{\\\\\\\"score\\\\\\\":324,\\\\\\\"seconds\\\\\\\":6956},{\\\\\\\"score\\\\\\\":389,\\\\\\\"seconds\\\\\\\":7096},{\\\\\\\"score\\\\\\\":521,\\\\\\\"seconds\\\\\\\":12399},{\\\\\\\"score\\\\\\\":675,\\\\\\\"seconds\\\\\\\":12560}]\\\",\\\"id\\\":10000083665,\\\"icon\\\":\\\"head5\\\",\\\"track\\\":1,\\\"group_type\\\":\\\"new_suc_hard\\\",\\\"name\\\":\\\"Kinga Gorman\\\"},{\\\"score\\\":\\\"[{\\\\\\\"score\\\\\\\":68,\\\\\\\"seconds\\\\\\\":176},{\\\\\\\"score\\\\\\\":125,\\\\\\\"seconds\\\\\\\":276},{\\\\\\\"score\\\\\\\":204,\\\\\\\"seconds\\\\\\\":646},{\\\\\\\"score\\\\\\\":285,\\\\\\\"seconds\\\\\\\":887},{\\\\\\\"score\\\\\\\":379,\\\\\\\"seconds\\\\\\\":957},{\\\\\\\"score\\\\\\\":519,\\\\\\\"seconds\\\\\\\":1059},{\\\\\\\"score\\\\\\\":560,\\\\\\\"seconds\\\\\\\":1069}]\\\",\\\"id\\\":10000083671,\\\"icon\\\":\\\"head4\\\",\\\"track\\\":2,\\\"group_type\\\":\\\"new_suc_hard\\\",\\\"name\\\":\\\"Cillebob\\\"}]\"},\"id\":{\"value\":121},\"entryTime\":{\"value\":1751854133},\"roundRanks\":{\"value\":\"{\\\"1\\\":1}\"},\"round\":{\"value\":2},\"lastScore\":{\"value\":\"{\\\"10000083659\\\":0,\\\"10000083671\\\":0,\\\"10000083665\\\":0,\\\"10000083662\\\":0,\\\"round\\\":2,\\\"1\\\":0}\"}}", "name": "coinRace"}, "bakeOut": {"data": "{\"id\":{\"value\":100024},\"cachedDataMd5\":{\"value\":\"8baa3057be53fe4cf0e61a5242abb329\"},\"cachedData\":{\"value\":\"{\\\"bakeout_rank_parameter\\\":[{\\\"delayTime\\\":90,\\\"retainTime\\\":601200,\\\"noRegisterTime\\\":300,\\\"uploadTime\\\":60,\\\"settlementTime\\\":300,\\\"maxNum\\\":20}],\\\"eTime\\\":1752112800,\\\"id\\\":100024,\\\"staticInclude\\\":[\\\"bakeout_rank_reward#1st\\\",\\\"bakeout_rank_exchange#default\\\",\\\"bakeout_rank_parameter#1st\\\"],\\\"rTime\\\":1752113400,\\\"bakeout_rank_reward\\\":[{\\\"rewards\\\":[\\\"gem-25\\\",\\\"energy-100\\\",\\\"skiptime_1-1\\\"],\\\"start_rank\\\":1,\\\"end_rank\\\":1},{\\\"rewards\\\":[\\\"gem-20\\\",\\\"energy-80\\\",\\\"additem_1-1\\\"],\\\"start_rank\\\":2,\\\"end_rank\\\":2},{\\\"rewards\\\":[\\\"gem-15\\\",\\\"energy-50\\\"],\\\"start_rank\\\":3,\\\"end_rank\\\":3},{\\\"rewards\\\":[\\\"gem-10\\\"],\\\"start_rank\\\":4,\\\"end_rank\\\":6},{\\\"rewards\\\":[\\\"energy-50\\\"],\\\"start_rank\\\":7,\\\"end_rank\\\":10},{\\\"rewards\\\":[\\\"skipprop-50\\\"],\\\"start_rank\\\":11,\\\"end_rank\\\":15},{\\\"rewards\\\":[\\\"skipprop-25\\\"],\\\"start_rank\\\":16,\\\"end_rank\\\":20}],\\\"bakeout_rank_exchange\\\":[{\\\"time\\\":1,\\\"cost\\\":10},{\\\"time\\\":2,\\\"cost\\\":11},{\\\"time\\\":3,\\\"cost\\\":14},{\\\"time\\\":4,\\\"cost\\\":17},{\\\"time\\\":5,\\\"cost\\\":21},{\\\"time\\\":6,\\\"cost\\\":28},{\\\"time\\\":7,\\\"cost\\\":37},{\\\"time\\\":8,\\\"cost\\\":51},{\\\"time\\\":9,\\\"cost\\\":72},{\\\"time\\\":10,\\\"cost\\\":100}],\\\"sTime\\\":1751508600}\"},\"delayedGetRewardTime\":{\"value\":9}}", "name": "bakeOut"}, "album1": {"data": "{\"cacheProperty\":{\"value\":\"{}\"}}", "name": "album1"}, "BP7": {"data": "{\"RewardTaken10_0\":{\"value\":1},\"ProgressEffect_3\":{\"value\":1},\"ProgressEffect_2\":{\"value\":1},\"RewardTaken1_0\":{\"value\":1},\"RewardEffect8_0\":{\"value\":1},\"RewardTaken14_0\":{\"value\":1},\"CycleTaskFinishedCount\":{\"value\":30},\"RewardEffect12_0\":{\"value\":1},\"TimelimitTaskFinished9\":{\"value\":0},\"ProgressEffect_10\":{\"value\":1},\"TimelimitTaskFinishedCount4\":{\"value\":2},\"RewardEffect15_0\":{\"value\":1},\"RewardEffect11_0\":{\"value\":1},\"RewardTaken16_0\":{\"value\":1},\"RewardTaken2_0\":{\"value\":1},\"TimelimitTaskFinishedCount6\":{\"value\":2},\"TimelimitTaskFinished1\":{\"value\":0},\"RewardTaken11_0\":{\"value\":1},\"ProgressEffect_8\":{\"value\":1},\"TimelimitTaskFinishedCount3\":{\"value\":2},\"ProgressEffect_17\":{\"value\":1},\"RewardEffect17_0\":{\"value\":1},\"ProgressEffect_19\":{\"value\":1},\"RewardTaken7_0\":{\"value\":1},\"TimelimitTaskFinishedCount1\":{\"value\":2},\"TimelimitTaskFinishedCount2\":{\"value\":2},\"TimelimitTaskFinished5\":{\"value\":0},\"TimelimitTaskOrder\":{\"value\":27},\"FinTLtask\":{\"value\":35},\"TokenNumber\":{\"value\":7305},\"TimelimitTaskFinished8\":{\"value\":0},\"RewardTaken6_0\":{\"value\":1},\"windowOpened2\":{\"value\":1},\"RewardEffect16_0\":{\"value\":1},\"RewardTaken3_0\":{\"value\":1},\"RewardEffect6_0\":{\"value\":1},\"RewardEffect14_0\":{\"value\":1},\"ProgressEffect_15\":{\"value\":1},\"RewardEffect10_0\":{\"value\":1},\"TimelimitTaskFinished4\":{\"value\":0},\"ProgressEffect_6\":{\"value\":1},\"ProgressEffect_12\":{\"value\":1},\"RewardEffect4_0\":{\"value\":1},\"TimelimitTaskFinishedCount9\":{\"value\":505},\"RewardEffect1_0\":{\"value\":1},\"RewardEffect5_0\":{\"value\":1},\"TimelimitTaskFinished7\":{\"value\":0},\"RewardTaken12_0\":{\"value\":1},\"TimelimitTaskFinishedCount8\":{\"value\":505},\"TimelimitTaskFinished2\":{\"value\":0},\"RewardTaken17_0\":{\"value\":1},\"TimelimitTaskFinishedCount7\":{\"value\":505},\"RewardEffect13_0\":{\"value\":1},\"ProgressEffect_11\":{\"value\":1},\"RewardEffect2_0\":{\"value\":1},\"ProgressEffect_4\":{\"value\":1},\"RewardEffect3_0\":{\"value\":1},\"ProgressEffect_13\":{\"value\":1},\"ProgressEffect_5\":{\"value\":1},\"TimelimitTaskFinished6\":{\"value\":0},\"ProgressEffect_14\":{\"value\":1},\"RewardEffect9_0\":{\"value\":1},\"RewardTaken8_0\":{\"value\":1},\"TimelimitTaskFinished3\":{\"value\":0},\"RewardEffect18_0\":{\"value\":1},\"RewardEffect19_0\":{\"value\":1},\"FinishedLevel\":{\"value\":19},\"RewardTaken9_0\":{\"value\":1},\"ProgressEffect_16\":{\"value\":1},\"TimelimitTaskFinishedCount5\":{\"value\":2},\"ProgressEffect_1\":{\"value\":1},\"RewardTaken4_0\":{\"value\":1},\"RewardEffect7_0\":{\"value\":1},\"CycleTaskIndex\":{\"value\":1},\"ProgressEffect_7\":{\"value\":1},\"ProgressEffect_9\":{\"value\":1},\"windowOpenedBP7MainWindow\":{\"value\":1},\"ProgressEffect_18\":{\"value\":1},\"id\":{\"value\":99}}", "name": "BP7"}, "extraBoard4ItemCache": {"data": "{\"1751854106001\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_2\",\"type\":2},\"1751853863047\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":3},\"1751854403001\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":2},\"1751854449001\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":2},\"1751854428001\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":2},\"1751854097001\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_3\",\"type\":2},\"1751854241001\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":2},\"1751853863048\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":3},\"1751854106002\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_2\",\"type\":2},\"1751853863049\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":3},\"1751854097002\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_3\",\"type\":2},\"1751854256001\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":2},\"1751854005001\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":2},\"1751854340001\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":2},\"1751854383001\":{\"cost\":\"{}\",\"codeStr\":\"eb4_1_1\",\"type\":2}}", "name": "extraBoard4ItemCache"}, "pkRace": {"data": "{\"lastScore\":{\"value\":\"{\\\"10000128796\\\":0,\\\"1\\\":0,\\\"round\\\":3}\"},\"windowOpened3\":{\"value\":1},\"playerDatas\":{\"value\":\"[{\\\"group_type\\\":\\\"pk_easy\\\",\\\"score\\\":\\\"[{\\\\\\\"seconds\\\\\\\":79,\\\\\\\"score\\\\\\\":42.22},{\\\\\\\"seconds\\\\\\\":67462,\\\\\\\"score\\\\\\\":56.06},{\\\\\\\"seconds\\\\\\\":67746,\\\\\\\"score\\\\\\\":95.15},{\\\\\\\"seconds\\\\\\\":67828,\\\\\\\"score\\\\\\\":141.48},{\\\\\\\"seconds\\\\\\\":68010,\\\\\\\"score\\\\\\\":216.37}]\\\",\\\"icon\\\":\\\"head5\\\",\\\"name\\\":\\\"dorytrhy\\\",\\\"id\\\":10000128796}]\"},\"roundRanks\":{\"value\":\"{\\\"2\\\":2,\\\"1\\\":1}\"},\"id\":{\"value\":118},\"entryTime\":{\"value\":1751773783},\"round\":{\"value\":3},\"settled\":{\"value\":true},\"windowOpened2\":{\"value\":1}}", "name": "pkRace"}, "extraBoard4Item": {"data": "{\"1751853863018\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_6\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863005\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863022\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_2_2\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863037\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_3\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863004\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_2_4\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863010\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_5\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_5\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863006\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_2_4\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863009\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863011\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863002\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863012\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863026\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863036\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_2\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863023\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_5\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863027\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_5\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863030\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863044\":{\"shopGemCost\":0,\"codeStr\":\"c#eb4_1_1\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863043\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_2\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863020\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_5\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_3\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863031\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_1\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"c#eb4_1_1\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863001\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_2_4\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863046\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_2\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863033\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_3\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_2\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"c#eb4_1_1\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863035\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_3\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863034\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_3\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863021\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_2_1\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863032\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_3\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_6\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863007\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_2\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863003\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_2_2\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863025\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863019\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863045\":{\"shopGemCost\":0,\"codeStr\":\"c#eb4_1_1\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"*************\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863028\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_5\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863029\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0},\"1751853863008\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb4_1_7\",\"costEnergy\":0,\"bubbleGemCost\":0,\"cookSkipPropCost\":0,\"costEnergyCurDay\":0,\"cookGemCost\":0}}", "name": "extraBoard4Item"}, "extraBoard4": {"data": "{\"extraBoardItemGived\":{\"value\":1},\"windowOpened2\":{\"value\":1},\"ath_o_51160\":{\"value\":\"{\\\"Currency\\\":\\\"eb_1_2\\\"@\\\"Weight\\\":100@\\\"Amount\\\":2}\"},\"ath_o_51140\":{\"value\":\"{\\\"Currency\\\":\\\"eb_1_3\\\"@\\\"Weight\\\":100@\\\"Amount\\\":2}\"},\"ath_o_51130\":{\"value\":\"{\\\"Currency\\\":\\\"eb_1_2\\\"@\\\"Weight\\\":100@\\\"Amount\\\":2}\"},\"ath_o_51150\":{\"value\":\"{\\\"Currency\\\":\\\"eb_1_2\\\"@\\\"Weight\\\":100@\\\"Amount\\\":1}\"},\"ath_o_51190\":{\"value\":\"{\\\"Currency\\\":\\\"eb_1_3\\\"@\\\"Weight\\\":100@\\\"Amount\\\":2}\"},\"ath_o_51180\":{\"value\":\"{\\\"Currency\\\":\\\"eb_1_2\\\"@\\\"Weight\\\":100@\\\"Amount\\\":3}\"},\"ath_o_51170\":{\"value\":\"{\\\"Currency\\\":\\\"eb_1_2\\\"@\\\"Weight\\\":100@\\\"Amount\\\":3}\"},\"ath_ce\":{\"value\":4},\"id\":{\"value\":124}}", "name": "extraBoard4"}, "extraBoard4ItemLayer": {"data": "{\"2_1\":{\"itemId\":\"1751853863002\"},\"3_1\":{\"itemId\":\"1751853863003\"},\"2_8\":{\"itemId\":\"1751853863044\"},\"1_4\":{\"itemId\":\"1751853863019\"},\"2_4\":{\"itemId\":\"1751853863020\"},\"3_4\":{\"itemId\":\"1751853863021\"},\"4_4\":{\"itemId\":\"1751853863022\"},\"5_4\":{\"itemId\":\"1751853863023\"},\"1_5\":{\"itemId\":\"1751853863025\"},\"5_5\":{\"itemId\":\"1751853863029\"},\"4_5\":{\"itemId\":\"1751853863028\"},\"3_5\":{\"itemId\":\"1751853863027\"},\"2_5\":{\"itemId\":\"1751853863026\"},\"1_6\":{\"itemId\":\"1751853863031\"},\"4_6\":{\"itemId\":\"1751853863034\"},\"5_6\":{\"itemId\":\"1751853863035\"},\"2_6\":{\"itemId\":\"1751853863032\"},\"3_6\":{\"itemId\":\"1751853863033\"},\"3_2\":{\"itemId\":\"1751853863009\"},\"2_2\":{\"itemId\":\"1751853863008\"},\"1_2\":{\"itemId\":\"1751853863007\"},\"6_2\":{\"itemId\":\"1751853863012\"},\"5_2\":{\"itemId\":\"1751853863011\"},\"4_2\":{\"itemId\":\"1751853863010\"},\"6_6\":{\"itemId\":\"1751853863036\"},\"6_8\":{\"itemId\":\"1751853863046\"},\"5_8\":{\"itemId\":\"1751853863045\"},\"1_8\":{\"itemId\":\"1751853863043\"},\"6_1\":{\"itemId\":\"1751853863006\"},\"1_1\":{\"itemId\":\"1751853863001\"},\"5_1\":{\"itemId\":\"1751853863005\"},\"6_3\":{\"itemId\":\"1751853863018\"},\"4_1\":{\"itemId\":\"1751853863004\"},\"6_5\":{\"itemId\":\"1751853863030\"},\"1_7\":{\"itemId\":\"1751853863037\"},\"6_4\":{\"itemId\":\"*************\"},\"5_7\":{\"itemId\":\"*************\"},\"6_7\":{\"itemId\":\"*************\"},\"3_7\":{\"itemId\":\"*************\"},\"4_7\":{\"itemId\":\"*************\"},\"1_3\":{\"itemId\":\"*************\"},\"2_7\":{\"itemId\":\"*************\"},\"3_3\":{\"itemId\":\"*************\"},\"2_3\":{\"itemId\":\"*************\"},\"5_3\":{\"itemId\":\"*************\"},\"4_3\":{\"itemId\":\"*************\"}}", "name": "extraBoard4ItemLayer"}}, "userProfile": {}, "localRewards": {}, "account": {"SocialType": {"key": "SocialType", "value": "1"}, "SocialId": {"key": "SocialId", "value": "***************"}, "SocialName": {"key": "SocialName", "value": "<PERSON>"}, "SocialPictureUrl": {"key": "SocialPictureUrl", "value": "https://graph.facebook.com/***************/picture?type=large&access_token=EAAcKmT4gWFsBO3cf0kjmMLCJf71NZCAFzQOHZCETlhBxU2UN6yrYFBYbZAofmtvmkoQ8Alb97XI7S6nbG3fz68ZCTsKdbpklKczMFuCp9wkg3j64yal7lhQ2zsR7vfGdZCUx1jT6qRjQ9WtwA3WY61Pw1tTkb4OoKuZCDhqdaPl1Ku1GrGOrRkkdbZBDO9qlyKmWLvBczefZCZBw6MjZAQ0YeWlPbT5m1ADMcA5z844YFAlGhMbv40PAZDZD"}}, "tutorial": {"task1_1": {"state": 2, "id": "task1_1", "ongoingDatas": ""}, "cook3": {"state": 2, "id": "cook3", "ongoingDatas": ""}, "order10070": {"state": 2, "id": "order10070", "ongoingDatas": ""}, "tutorial_coin_race_first_main_window": {"state": 2, "id": "tutorial_coin_race_first_main_window", "ongoingDatas": ""}, "tutorial_progress": {"state": 2, "id": "tutorial_progress", "ongoingDatas": ""}, "tutorial_energy_boost_quad": {"state": 1, "id": "tutorial_energy_boost_quad", "ongoingDatas": ""}, "toboard": {"state": 2, "id": "toboard", "ongoingDatas": ""}, "tutorial_digactivity_firstdig": {"state": 2, "id": "tutorial_digactivity_firstdig", "ongoingDatas": ""}, "CD_pd_2_6": {"state": 2, "id": "CD_pd_2_6", "ongoingDatas": ""}, "task1_2": {"state": 2, "id": "task1_2", "ongoingDatas": ""}, "order10010": {"state": 2, "id": "order10010", "ongoingDatas": ""}, "tutorial_energy_boost": {"state": 2, "id": "tutorial_energy_boost", "ongoingDatas": ""}, "task1_4": {"state": 2, "id": "task1_4", "ongoingDatas": ""}, "order_item_info": {"state": 2, "id": "order_item_info", "ongoingDatas": ""}, "tutorial_producer_inventory": {"state": 1, "id": "tutorial_producer_inventory", "ongoingDatas": ""}, "order10080": {"state": 2, "id": "order10080", "ongoingDatas": ""}, "clickPD": {"state": 2, "id": "clickPD", "ongoingDatas": ""}, "order_group": {"state": 2, "id": "order_group", "ongoingDatas": ""}, "tutorial_coin_race_entry": {"state": 2, "id": "tutorial_coin_race_entry", "ongoingDatas": ""}, "cook1": {"state": 2, "id": "cook1", "ongoingDatas": ""}, "order10040": {"state": 2, "id": "order10040", "ongoingDatas": ""}, "tutorial_digactivity_seconddig": {"state": 2, "id": "tutorial_digactivity_seconddig", "ongoingDatas": ""}, "bubble": {"state": 2, "id": "bubble", "ongoingDatas": ""}, "tutorial_battlepass": {"state": 2, "id": "tutorial_battlepass", "ongoingDatas": ""}, "tutorial_pk_race_start": {"state": 2, "id": "tutorial_pk_race_start", "ongoingDatas": ""}, "timeline": {"state": 2, "id": "timeline", "ongoingDatas": ""}, "task1_3": {"state": 2, "id": "task1_3", "ongoingDatas": ""}, "tutorial_blind_chest": {"state": 2, "id": "tutorial_blind_chest", "ongoingDatas": ""}, "merge1": {"state": 2, "id": "merge1", "ongoingDatas": ""}, "cd_speed": {"state": 2, "id": "cd_speed", "ongoingDatas": ""}, "weakGesture": {"state": 1, "id": "weakGesture", "ongoingDatas": ""}, "CD_pd_1_7": {"state": 2, "id": "CD_pd_1_7", "ongoingDatas": ""}, "tutorial_extraboard_cobweb_unlock": {"state": 1, "id": "tutorial_extraboard_cobweb_unlock", "ongoingDatas": ""}, "order10030": {"state": 2, "id": "order10030", "ongoingDatas": ""}, "order10050": {"state": 2, "id": "order10050", "ongoingDatas": ""}, "tutorial_coin_race_second_main_window": {"state": 2, "id": "tutorial_coin_race_second_main_window", "ongoingDatas": ""}, "tutorial_coin_race_order": {"state": 2, "id": "tutorial_coin_race_order", "ongoingDatas": ""}, "additem_old_user": {"state": 2, "id": "additem_old_user", "ongoingDatas": ""}, "tutorial_battlepass_loop": {"state": 1, "id": "tutorial_battlepass_loop", "ongoingDatas": ""}, "shop": {"state": 2, "id": "shop", "ongoingDatas": ""}, "tutorial_extraboard_cobweb_merge": {"state": 1, "id": "tutorial_extraboard_cobweb_merge", "ongoingDatas": ""}, "energy": {"state": 2, "id": "energy", "ongoingDatas": ""}, "merge2": {"state": 2, "id": "merge2", "ongoingDatas": ""}, "tutorial_cg": {"state": 2, "id": "tutorial_cg", "ongoingDatas": ""}, "tutorial_extraboard_item_delete": {"state": 1, "id": "tutorial_extraboard_item_delete", "ongoingDatas": ""}, "cook2": {"state": 2, "id": "cook2", "ongoingDatas": ""}, "order10140": {"state": 2, "id": "order10140", "ongoingDatas": ""}, "cache": {"state": 2, "id": "cache", "ongoingDatas": ""}, "additem_+8": {"state": 2, "id": "additem_+8", "ongoingDatas": ""}, "tutorial_extraboard_start": {"state": 2, "id": "tutorial_extraboard_start", "ongoingDatas": ""}, "order10020": {"state": 2, "id": "order10020", "ongoingDatas": ""}}, "item": {"1751854449003": {"materialInfo": "", "choices": "", "costEnergyCurDay": 1, "choiceDate": "", "id": "1751854449003", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_2_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1749892238001": {"materialInfo": "", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1749892238001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_6_5", "cookRecipe": "", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 1, "cookGemCost": 0}, "1751595381001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751595381001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_2_5", "costEnergy": 16, "spreadItemBoxChain": ""}, "1750839471001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1750839471001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_5_1_5", "costEnergy": 10, "spreadItemBoxChain": ""}, "1751854253001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 3, "choiceDate": "", "id": "1751854253001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_1_2_3", "costEnergy": 1, "spreadItemBoxChain": ""}, "1750213727001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1750213727001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "eq_5_3", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854089001": {"materialInfo": "it_4_1_7-1751336294002", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1751854089001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_3_5", "cookRecipe": "ds_juice_6", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 3, "cookGemCost": 0}, "1749541773001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1749541773001", "spreadWeightType": 1, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_2_1_1-1;it_2_1_1-1;it_2_2_1-1;it_2_1_1-1;it_2_1_1-1;it_2_1_1-1;it_2_3_1-1;it_2_1_1-1;it_2_1_1-1;it_2_1_1-1;it_2_2_1-1;it_2_1_1-1;it_2_1_1-1;it_2_3_1-1;it_2_1_1-1;it_2_1_1-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_2_4", "spreadStorageRestNumber": 24, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 24, "cookGemCost": 0}, "1751276995001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751276995001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_4_1_7", "costEnergy": 44, "spreadItemBoxChain": ""}, "1751854446001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1751854446001", "spreadWeightType": 2, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 2, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_2_3_1_1-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadTierUpCount": 0, "spreadInherit": 0, "spreadState": 3, "codeStr": "it_2_3_2", "spreadStorageRestNumber": 1, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 0, "cookGemCost": 0}, "1751595566001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751595566001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_5_1_4", "costEnergy": 5, "spreadItemBoxChain": ""}, "1746585834001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1746585834001", "spreadWeightType": 2, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_7_1_1-4", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_7_5", "spreadStorageRestNumber": 36, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 1176, "cookGemCost": 0}, "1742351309001": {"materialInfo": "it_7_1_4-1751853975001;it_3_2_4-1751854391001", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1742351309001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_4_6", "cookRecipe": "", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 2, "cookGemCost": 0}, "1740484341001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1740484341001", "spreadWeightType": 1, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_2_1_2-1;it_2_1_1-1;it_2_2_1-1;it_2_1_2-1;it_2_1_1-1;it_2_3_1-1;it_2_1_1-1;it_2_1_2-1;it_2_2_1-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_2_6", "spreadStorageRestNumber": 36, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 691, "cookGemCost": 0}, "1751854446002": {"materialInfo": "", "choices": "", "costEnergyCurDay": 2, "choiceDate": "", "id": "1751854446002", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_2_2", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751249288001": {"materialInfo": "it_1_1_9-1751854269001", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1751249288001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 1751869689, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_4_4", "cookRecipe": "ds_friedve_4", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 5, "cookGemCost": 0}, "1751854012001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751854012001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_4_1_4", "costEnergy": 4, "spreadItemBoxChain": ""}, "1751250031001": {"materialInfo": "it_4_1_6-1751336327001", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1751250031001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 1751854390, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_1_7", "cookRecipe": "ds_chopfr_1", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 5, "cookGemCost": 0}, "1751854455002": {"materialInfo": "", "choices": "", "costEnergyCurDay": 1, "choiceDate": "", "id": "1751854455002", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_1_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854014001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751854014001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_4_1_2", "costEnergy": 2, "spreadItemBoxChain": ""}, "1751854260001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 2, "choiceDate": "", "id": "1751854260001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_1_2_2", "costEnergy": 0, "spreadItemBoxChain": ""}, "1749892246001": {"materialInfo": "ds_friedmt_3-1751854307001;ds_friedmt_1-1751854319001", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1749892246001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_1_5", "cookRecipe": "", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 2, "cookGemCost": 0}, "1751854455001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 1, "choiceDate": "", "id": "1751854455001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_3_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751595228001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751595228001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_5_2_2", "costEnergy": 2, "spreadItemBoxChain": ""}, "1751773957003": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751773957003", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_7_2_4", "costEnergy": 8, "spreadItemBoxChain": ""}, "1738287944001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1738287944001", "spreadWeightType": 2, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_6_1_1-6", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_6_5", "spreadStorageRestNumber": 30, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 994, "cookGemCost": 0}, "1751854307001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751854307001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "ds_friedmt_3", "costEnergy": 2, "spreadItemBoxChain": ""}, "1751249238001": {"materialInfo": "it_2_3_2-1751854418001", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1751249238001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_1_4", "cookRecipe": "", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 2, "cookGemCost": 0}, "1730095941001": {"materialInfo": "it_3_1_7-1751854360001", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1730095941001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 1751869689, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_2_5", "cookRecipe": "ds_grillmt_7", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 5, "cookGemCost": 0}, "1749176305001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1749176305001", "spreadWeightType": 1, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_1_4", "spreadStorageRestNumber": 24, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 0, "cookGemCost": 0}, "1751854360001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 8, "choiceDate": "", "id": "1751854360001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_3_1_7", "costEnergy": 35, "spreadItemBoxChain": ""}, "1751854398001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 9, "choiceDate": "", "id": "1751854398001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_3_1_5", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854259002": {"materialInfo": "", "choices": "", "costEnergyCurDay": 1, "choiceDate": "", "id": "1751854259002", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_1_1_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854064001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751854064001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "additem_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854441001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 3, "choiceDate": "", "id": "1751854441001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_3_1_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854418001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1751854418001", "spreadWeightType": 2, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 1, "spreadItemBoxChain": "", "spreadStartTimer": 1751854418, "spreadCodeWeightPairs": "it_2_3_1_1-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadTierUpCount": 0, "spreadInherit": 0, "spreadState": 3, "codeStr": "it_2_3_2", "spreadStorageRestNumber": 0, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 0, "cookGemCost": 0}, "1740574630001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1740574630001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_1_1_1_2", "costEnergy": 21.5, "spreadItemBoxChain": ""}, "1741572578001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1741572578001", "spreadWeightType": 2, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_6_1_1-13", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_6_4", "spreadStorageRestNumber": 24, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 67, "cookGemCost": 0}, "1749729418001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1749729418001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "ds_friedve_1", "costEnergy": 71, "spreadItemBoxChain": ""}, "1750731526001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1750731526001", "spreadWeightType": 1, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_3_1_1-1;it_3_1_2-1;it_3_2_1-1;it_3_1_1-1;it_3_1_2-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_2-1;it_3_1_1-1;it_3_2_1-1;it_3_1_2-1;it_3_1_1-1;it_3_1_2-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_3_5", "spreadStorageRestNumber": 30, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 25, "cookGemCost": 0}, "1751249246001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1751249246001", "spreadWeightType": 1, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_4_2_1-1;it_4_1_1-1;it_4_1_2-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_3-1;it_4_1_1-1;it_4_1_2-1;it_4_2_1-1;it_4_1_1-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_2-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_2-1;it_4_1_1-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_4_6", "spreadStorageRestNumber": 36, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 120, "cookGemCost": 0}, "1749892249001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1749892249001", "spreadWeightType": 2, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_7_1_1-1;it_7_1_2-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_7_5", "spreadStorageRestNumber": 36, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 238, "cookGemCost": 0}, "1744683794001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1744683794001", "spreadWeightType": 1, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_3_1_1-1;it_3_1_2-1;it_3_1_1-1;it_3_1_3-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_2-1;it_3_1_1-1;it_3_2_1-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_3_7", "spreadStorageRestNumber": 42, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 1590, "cookGemCost": 0}, "1751509240001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751509240001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_1_8", "costEnergy": 68, "spreadItemBoxChain": ""}, "1751854453001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 18, "choiceDate": "", "id": "1751854453001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_1_6", "costEnergy": 1, "spreadItemBoxChain": ""}, "1739932709001": {"materialInfo": "", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1739932709001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_3_6", "cookRecipe": "", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 1, "cookGemCost": 0}, "1749608236001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1749608236001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "gem_2", "costEnergy": 0, "spreadItemBoxChain": ""}, "1749872438001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1749872438001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "gem_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854448002": {"materialInfo": "", "choices": "", "costEnergyCurDay": 2, "choiceDate": "", "id": "1751854448002", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_1_2", "costEnergy": 0, "spreadItemBoxChain": ""}, "1743846671001": {"materialInfo": "", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1743846671001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_4_5", "cookRecipe": "", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 1, "cookGemCost": 0}, "1743128763001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1743128763001", "spreadWeightType": 2, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_5_1_1-1;it_5_2_1-3", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_5_5", "spreadStorageRestNumber": 24, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 396, "cookGemCost": 0}, "1751854426002": {"materialInfo": "", "choices": "", "costEnergyCurDay": 2, "choiceDate": "", "id": "1751854426002", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_2_2", "costEnergy": 0, "spreadItemBoxChain": ""}, "1745164158001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1745164158001", "spreadWeightType": 1, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_1_1_3-1;it_1_2_1-1;it_1_1_1-1;it_1_1_2-1;it_1_1_1-1;it_1_2_1-1;it_1_1_3-1;it_1_1_1-1;it_1_1_2-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_2-1;it_1_1_3-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_1_8", "spreadStorageRestNumber": 48, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 1383, "cookGemCost": 0}, "1751854080001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751854080001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "pd_7_3", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751249292001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751249292001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "pd_4_3", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854077001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751854077001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "ene_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751773916001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751773916001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_2_2", "costEnergy": 2, "spreadItemBoxChain": ""}, "1751336294002": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751336294002", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_4_1_7", "costEnergy": 43, "spreadItemBoxChain": ""}, "1751853981002": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751853981002", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_7_2_1", "costEnergy": 1, "spreadItemBoxChain": ""}, "1751336327001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751336327001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_4_1_6", "costEnergy": 23, "spreadItemBoxChain": ""}, "1751853979001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751853979001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_7_2_3", "costEnergy": 4, "spreadItemBoxChain": ""}, "1742869211001": {"materialInfo": "", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1742869211001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_2_6", "cookRecipe": "", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 1, "cookGemCost": 0}, "1751854319001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 1, "choiceDate": "", "id": "1751854319001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "ds_friedmt_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854381002": {"materialInfo": "", "choices": "", "costEnergyCurDay": 6, "choiceDate": "", "id": "1751854381002", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_3_1_4", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854427001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 1, "choiceDate": "", "id": "1751854427001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_2_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1751854434001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 7, "choiceDate": "", "id": "1751854434001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_1_7", "costEnergy": 23, "spreadItemBoxChain": ""}, "1749891834001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1749891834001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "ds_friedve_1", "costEnergy": 51, "spreadItemBoxChain": ""}, "1751854444001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 1, "choiceDate": "", "id": "1751854444001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_1_1", "costEnergy": 0, "spreadItemBoxChain": ""}, "1730096039001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1730096039001", "spreadWeightType": 1, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_2_1_2-1;it_2_1_1-1;it_2_2_1-1;it_2_1_1-1;it_2_3_1-1;it_2_1_2-1;it_2_1_3-1;it_2_1_1-1;it_2_1_1-1;it_2_3_1-1;it_2_1_2-1;it_2_2_1-1;it_2_1_1-1;it_2_1_2-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_2_7", "spreadStorageRestNumber": 42, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 5667, "cookGemCost": 0}, "1751773888002": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751773888002", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_7_2_5", "costEnergy": 16, "spreadItemBoxChain": ""}, "1741837046001": {"materialInfo": "it_4_1_7-1751276995001", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1741837046001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_3_5", "cookRecipe": "ds_juice_6", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 3, "cookGemCost": 0}, "1749698229001": {"materialInfo": "", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1749698229001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_2_4", "cookRecipe": "", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 1, "cookGemCost": 0}, "1751854269001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 16, "choiceDate": "", "id": "1751854269001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_1_1_9", "costEnergy": 109, "spreadItemBoxChain": ""}, "1751595242007": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751595242007", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_5_1_1", "costEnergy": 1, "spreadItemBoxChain": ""}, "1747715534001": {"materialInfo": "", "cookSpeedTime": 0, "cookStartTimer": -1, "choiceDate": "", "id": "1747715534001", "cookSkipPropCost": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "cookLastUpdateTime": 0, "spreadCodeWeightPairs": "", "bubbleGemCost": 0, "codeStr": "eq_5_5", "cookRecipe": "", "shopGemCost": 0, "costEnergy": 0, "choices": "", "cookState": 1, "cookGemCost": 0}, "1751853975001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751853975001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_7_1_4", "costEnergy": 5, "spreadItemBoxChain": ""}, "1751773998001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751773998001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_2_2_4", "costEnergy": 8, "spreadItemBoxChain": ""}, "1734438341001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1734438341001", "spreadWeightType": 1, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_4_1_2-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_2-1;it_4_1_1-1", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_4_6", "spreadStorageRestNumber": 36, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 3794, "cookGemCost": 0}, "1751509193001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 0, "choiceDate": "", "id": "1751509193001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_4_1_5", "costEnergy": 13, "spreadItemBoxChain": ""}, "1740969017001": {"materialInfo": "", "spreadAddItem": 0, "choices": "", "choiceDate": "", "id": "1740969017001", "spreadWeightType": 2, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "costEnergyCurDay": 0, "spreadItemBoxChain": "", "spreadStartTimer": -1, "spreadCodeWeightPairs": "it_5_1_1-1;it_5_2_1-3", "spreadTierUpLevel": 0, "bubbleGemCost": 0, "cookRecipe": "", "spreadState": 3, "spreadTierUpCount": 0, "spreadInherit": 0, "codeStr": "pd_5_6", "spreadStorageRestNumber": 30, "shopGemCost": 0, "costEnergy": 0, "spreadCount": 2076, "cookGemCost": 0}, "1751854391001": {"materialInfo": "", "choices": "", "costEnergyCurDay": 8, "choiceDate": "", "id": "1751854391001", "shopGemCost": 0, "bubbleGemCost": 0, "cookGemCost": 0, "cookSkipPropCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "codeStr": "it_3_2_4", "costEnergy": 0, "spreadItemBoxChain": ""}}, "board": {"6_9": {"itemId": "1734438341001", "id": "6_9"}, "1_3": {"itemId": "1749698229001", "id": "1_3"}, "7_4": {"itemId": "1751854253001", "id": "7_4"}, "3_9": {"itemId": "1746585834001", "id": "3_9"}, "2_9": {"itemId": "1740484341001", "id": "2_9"}, "6_4": {"itemId": "1751854455002", "id": "6_4"}, "3_4": {"itemId": "1751853979001", "id": "3_4"}, "4_4": {"itemId": "1751854444001", "id": "4_4"}, "3_8": {"itemId": "1749892249001", "id": "3_8"}, "2_8": {"itemId": "1751773998001", "id": "2_8"}, "1_8": {"itemId": "1749541773001", "id": "1_8"}, "7_3": {"itemId": "1751854259002", "id": "7_3"}, "6_3": {"itemId": "1751854077001", "id": "6_3"}, "5_3": {"itemId": "1751854449003", "id": "5_3"}, "4_3": {"itemId": "1751854441001", "id": "4_3"}, "3_3": {"itemId": "1751249238001", "id": "3_3"}, "2_7": {"itemId": "1751854434001", "id": "2_7"}, "3_7": {"itemId": "1751773916001", "id": "3_7"}, "2_1": {"itemId": "1742351309001", "id": "2_1"}, "1_1": {"itemId": "1730095941001", "id": "1_1"}, "6_7": {"itemId": "1751854012001", "id": "6_7"}, "4_2": {"itemId": "1750731526001", "id": "4_2"}, "4_7": {"itemId": "1751853981002", "id": "4_7"}, "5_7": {"itemId": "1750839471001", "id": "5_7"}, "1_4": {"itemId": "1747715534001", "id": "1_4"}, "7_1": {"itemId": "1745164158001", "id": "7_1"}, "4_1": {"itemId": "1744683794001", "id": "4_1"}, "3_1": {"itemId": "1751250031001", "id": "3_1"}, "6_1": {"itemId": "1738287944001", "id": "6_1"}, "5_1": {"itemId": "1740969017001", "id": "5_1"}, "6_2": {"itemId": "1741572578001", "id": "6_2"}, "7_9": {"itemId": "1751854381002", "id": "7_9"}, "5_5": {"itemId": "1751854427001", "id": "5_5"}, "7_7": {"itemId": "1751854448002", "id": "7_7"}, "2_4": {"itemId": "1749892238001", "id": "2_4"}, "5_8": {"itemId": "1751595242007", "id": "5_8"}, "5_4": {"itemId": "1751854446001", "id": "5_4"}, "7_8": {"itemId": "1751854398001", "id": "7_8"}, "1_2": {"itemId": "1742869211001", "id": "1_2"}, "2_2": {"itemId": "1743846671001", "id": "2_2"}, "2_3": {"itemId": "1751249288001", "id": "2_3"}, "4_8": {"itemId": "1751854014001", "id": "4_8"}, "4_9": {"itemId": "1751595381001", "id": "4_9"}, "5_2": {"itemId": "1743128763001", "id": "5_2"}, "7_2": {"itemId": "1751854260001", "id": "7_2"}, "1_7": {"itemId": "1751854089001", "id": "1_7"}, "1_5": {"itemId": "1739932709001", "id": "1_5"}, "1_9": {"itemId": "1730096039001", "id": "1_9"}, "2_5": {"itemId": "1751854446002", "id": "2_5"}, "3_5": {"itemId": "1751595566001", "id": "3_5"}, "4_5": {"itemId": "1751773957003", "id": "4_5"}, "5_9": {"itemId": "1751249246001", "id": "5_9"}, "1_6": {"itemId": "1741837046001", "id": "1_6"}, "6_8": {"itemId": "1751773888002", "id": "6_8"}, "3_6": {"itemId": "1751509193001", "id": "3_6"}, "2_6": {"itemId": "1751595228001", "id": "2_6"}, "5_6": {"itemId": "1751854455001", "id": "5_6"}, "4_6": {"itemId": "1751854453001", "id": "4_6"}, "3_2": {"itemId": "1749892246001", "id": "3_2"}, "6_6": {"itemId": "1751854426002", "id": "6_6"}}, "ad": {}, "bundles": {"cdFill": {"data": "{\"timespeed_1\":{\"data\":\"{\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"dailyShowNum\\\\\\\":10@\\\\\\\"duration\\\\\\\":360@\\\\\\\"generalBundleConf\\\\\\\":[{\\\\\\\"param_int\\\\\\\":4@\\\\\\\"confType\\\\\\\":\\\\\\\"pd_cd_number\\\\\\\"}]@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#timespeed_199\\\\\\\"@\\\\\\\"bundleTrigger#pd_cd_number\\\\\\\"@\\\\\\\"generalBundleConf#pd_cd_number\\\\\\\"]@\\\\\\\"eTime\\\\\\\":1765411200@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"buyCD\\\\\\\":30@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"trigger\\\\\\\":\\\\\\\"pd_cd_number\\\\\\\"@\\\\\\\"popOrder\\\\\\\":[\\\\\\\"cdFill\\\\\\\"]}]@\\\\\\\"id\\\\\\\":76@\\\\\\\"uiCode\\\\\\\":\\\\\\\"cdFill\\\\\\\"@\\\\\\\"groupId\\\\\\\":\\\\\\\"timespeed_1\\\\\\\"@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":80}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"timespeed_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"payID\\\\\\\":\\\\\\\"cd_speed_199\\\\\\\"@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"timespeed_199\\\\\\\"}]@\\\\\\\"specialType\\\\\\\":\\\\\\\"cdFill\\\\\\\"@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"popCD\\\\\\\":60@\\\\\\\"sTime\\\\\\\":1749434400@\\\\\\\"dailyBuyNum\\\\\\\":2@\\\\\\\"order\\\\\\\":[\\\\\\\"timespeed_199\\\\\\\"]}\\\"}}\"}}", "name": "cdFill"}, "chain": {"data": "{\"chain9\":{\"data\":\"{\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20275\\\"},\\\"curIndex\\\":{\\\"value\\\":\\\"4\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"dailyShowNum\\\\\\\":2@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"trigger\\\\\\\":\\\\\\\"login\\\\\\\"}]@\\\\\\\"duration\\\\\\\":4320@\\\\\\\"uiCode\\\\\\\":\\\\\\\"chain\\\\\\\"@\\\\\\\"id\\\\\\\":85@\\\\\\\"popCD\\\\\\\":5@\\\\\\\"bundleContentChain\\\\\\\":[{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":1@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":5}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_1\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":2@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":10}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_2\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":3@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_3\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"step\\\\\\\":4@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_199\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":30}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":150}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_4\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":5@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":15}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_5\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":6@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_2\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_6\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"step\\\\\\\":7@\\\\\\\"price\\\\\\\":2.99@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_299\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_7\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":8@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_8\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":9@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":30}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_9\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":10@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":10}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_10\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":11@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_3\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_11\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"step\\\\\\\":12@\\\\\\\"price\\\\\\\":3.99@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_399\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":60}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":250}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_12\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":13@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_13\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":14@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_14\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":15@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":50}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_15\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":16@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_4\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_16\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"step\\\\\\\":17@\\\\\\\"price\\\\\\\":4.99@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_499\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":80}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":300}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_17\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":18@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":60}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_18\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":19@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":15}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_19\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":20@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":70}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_20\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":21@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_21\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"step\\\\\\\":22@\\\\\\\"price\\\\\\\":6.99@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_699\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":450}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_22\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":23@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_23\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":24@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_24\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":25@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":150}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_25\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":26@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":20}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_26\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"step\\\\\\\":27@\\\\\\\"price\\\\\\\":15.99@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_1599\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":240}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":1000}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_27\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":28@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_28\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":29@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_29\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":30@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_3\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_30\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":31@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":400}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_31\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":32@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":50}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_32\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"step\\\\\\\":33@\\\\\\\"price\\\\\\\":19.99@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_1999\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":320}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":1200}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_33\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":34@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_34\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":35@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":2}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_35\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":36@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":400}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_36\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"step\\\\\\\":37@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_3\\\\\\\"@\\\\\\\"Amount\\\\\\\":2}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_37\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"step\\\\\\\":38@\\\\\\\"price\\\\\\\":0@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_38\\\\\\\"}]@\\\\\\\"groupId\\\\\\\":\\\\\\\"chain9\\\\\\\"@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"specialType\\\\\\\":\\\\\\\"chain\\\\\\\"@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContentChain#chain_skip\\\\\\\"@\\\\\\\"bundleTrigger#login\\\\\\\"]@\\\\\\\"rTime\\\\\\\":1751940000@\\\\\\\"sTime\\\\\\\":1751594400@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"eTime\\\\\\\":1751853600}\\\"},\\\"tgDailyShowNum_login\\\":{\\\"value\\\":1},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1751773131\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1751594549\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20275\\\"}}\"}}", "name": "chain"}, "multiTier": {"data": "{\"energytier1\":{\"data\":\"{\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"dailyShowNum\\\\\\\":10@\\\\\\\"duration\\\\\\\":30@\\\\\\\"generalBundleConf\\\\\\\":[{\\\\\\\"param_string\\\\\\\":\\\\\\\"et3.99\\\\\\\"@\\\\\\\"confType\\\\\\\":\\\\\\\"defaultOrder\\\\\\\"}]@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#et1.99\\\\\\\"@\\\\\\\"bundleContent#et3.99\\\\\\\"@\\\\\\\"bundleContent#et5.99\\\\\\\"@\\\\\\\"bundleTrigger#lack_energy\\\\\\\"@\\\\\\\"generalBundleConf#3.99\\\\\\\"]@\\\\\\\"eTime\\\\\\\":1765411200@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"buyCD\\\\\\\":30@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popOrder\\\\\\\":[\\\\\\\"energy\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"lack_energy\\\\\\\"@\\\\\\\"popNum\\\\\\\":1}]@\\\\\\\"id\\\\\\\":20@\\\\\\\"uiCode\\\\\\\":\\\\\\\"multiTier\\\\\\\"@\\\\\\\"groupId\\\\\\\":\\\\\\\"energytier1\\\\\\\"@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"discountTag\\\\\\\":\\\\\\\"125%\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":250}]@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_199\\\\\\\"@\\\\\\\"originPrice\\\\\\\":2.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et1.99\\\\\\\"}@{\\\\\\\"discountTag\\\\\\\":\\\\\\\"130%\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":520}]@\\\\\\\"price\\\\\\\":3.99@\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_399\\\\\\\"@\\\\\\\"originPrice\\\\\\\":5.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et3.99\\\\\\\"}@{\\\\\\\"discountTag\\\\\\\":\\\\\\\"140%\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":820}]@\\\\\\\"price\\\\\\\":5.99@\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_599\\\\\\\"@\\\\\\\"originPrice\\\\\\\":8.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et5.99\\\\\\\"}]@\\\\\\\"specialType\\\\\\\":\\\\\\\"multiTier\\\\\\\"@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"popCD\\\\\\\":5@\\\\\\\"sTime\\\\\\\":1742522400@\\\\\\\"dailyBuyNum\\\\\\\":5@\\\\\\\"order\\\\\\\":[\\\\\\\"et1.99\\\\\\\"@\\\\\\\"et3.99\\\\\\\"@\\\\\\\"et5.99\\\\\\\"]}\\\"},\\\"tgDailyShowNum_lack_energy\\\":{\\\"value\\\":1},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20276\\\"},\\\"missedNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"lastGear\\\":{\\\"value\\\":\\\"0\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1751854456\\\"},\\\"curGearBundleId\\\":{\\\"value\\\":\\\"et3.99\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1751854456\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20276\\\"}}\"}}", "name": "multiTier"}, "starter": {"data": "{\"starter\":{\"data\":\"{\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"tgDailyShowNum_task_finished\\\":{\\\"value\\\":1},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20272\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1751509441\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"dailyShowNum\\\\\\\":2@\\\\\\\"order\\\\\\\":[\\\\\\\"starter_499\\\\\\\"]@\\\\\\\"id\\\\\\\":1@\\\\\\\"duration\\\\\\\":720@\\\\\\\"uiCode\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"dailyBuyNum\\\\\\\":1@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popOrder\\\\\\\":[\\\\\\\"starter\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"task_finished\\\\\\\"@\\\\\\\"popNum\\\\\\\":1}]@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#starter_499\\\\\\\"@\\\\\\\"bundleTrigger#task_finished\\\\\\\"@\\\\\\\"bundleCondition#starter_499\\\\\\\"]@\\\\\\\"groupId\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"discountTag\\\\\\\":\\\\\\\"200%\\\\\\\"@\\\\\\\"price\\\\\\\":4.99@\\\\\\\"payID\\\\\\\":\\\\\\\"starter_bundle_1\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":240}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":4}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"enebox_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"starter_499\\\\\\\"}]@\\\\\\\"specialType\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"popCD\\\\\\\":360@\\\\\\\"bundleCondition\\\\\\\":[{\\\\\\\"taskFinished\\\\\\\":{\\\\\\\"TaskCount\\\\\\\":3@\\\\\\\"ChapterId\\\\\\\":2}}]@\\\\\\\"maxBuyNum\\\\\\\":1@\\\\\\\"buyCD\\\\\\\":10080}\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1751509433\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20272\\\"}}\"}}", "name": "starter"}, "onePlusN": {"data": "{}", "name": "onePlusN"}, "orderGroup": {"data": "{\"order1\":{\"data\":\"{\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20276\\\"},\\\"tgDailyShowNum_finish_order_group\\\":{\\\"value\\\":1},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1751854122\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"dailyShowNum\\\\\\\":5@\\\\\\\"duration\\\\\\\":60@\\\\\\\"dailyBuyNum\\\\\\\":2@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#order_199\\\\\\\"@\\\\\\\"bundleTrigger#finish_order_group\\\\\\\"]@\\\\\\\"eTime\\\\\\\":1765411200@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"buyCD\\\\\\\":30@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popOrder\\\\\\\":[\\\\\\\"orderGroup\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"finish_order_group\\\\\\\"@\\\\\\\"popNum\\\\\\\":1}]@\\\\\\\"id\\\\\\\":14@\\\\\\\"groupId\\\\\\\":\\\\\\\"order1\\\\\\\"@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"discountTag\\\\\\\":\\\\\\\"120%\\\\\\\"@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"payID\\\\\\\":\\\\\\\"finish_order_199\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"cbox3_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":2}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"order_199\\\\\\\"}]@\\\\\\\"popCD\\\\\\\":60@\\\\\\\"uiCode\\\\\\\":\\\\\\\"orderGroup\\\\\\\"@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"sTime\\\\\\\":1741053600@\\\\\\\"specialType\\\\\\\":\\\\\\\"orderGroup\\\\\\\"@\\\\\\\"order\\\\\\\":[\\\\\\\"order_199\\\\\\\"]}\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1751854106\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20276\\\"}}\"}}", "name": "orderGroup"}, "energy": {"data": "{}", "name": "energy"}, "cd": {"data": "{}", "name": "cd"}}, "cdn2": {}, "openFunc": {"discoveries": {"state": 1, "id": "discoveries"}, "inventory": {"state": 1, "id": "inventory"}, "shop": {"state": 1, "id": "shop"}, "bubble": {"state": 1, "id": "bubble"}}, "biSync": {"GameDuration": {"key": "GameDuration", "value": "151466"}, "ConsumedFreeGem": {"key": "ConsumedFreeGem", "value": "294"}, "ConsumedEnergy": {"key": "ConsumedEnergy", "value": "32986"}}, "ItemUnlock": {"ds_mixdrk_7": {"state": 3, "type": "ds_mixdrk_7"}, "gold_2": {"state": 3, "type": "gold_2"}, "it_1_1_2_2": {"state": 3, "type": "it_1_1_2_2"}, "eq_4_6": {"state": 3, "type": "eq_4_6"}, "ds_e1cockt_12": {"state": 3, "type": "ds_e1cockt_12"}, "enebox_1": {"state": 1, "type": "enebox_1"}, "ds_fd_4": {"state": 3, "type": "ds_fd_4"}, "it_4_2_5": {"state": 3, "type": "it_4_2_5"}, "eb4_1_8": {"state": 2, "type": "eb4_1_8"}, "ds_mixdrk_4": {"state": 3, "type": "ds_mixdrk_4"}, "eq_4_4": {"state": 3, "type": "eq_4_4"}, "it_3_1_2": {"state": 3, "type": "it_3_1_2"}, "eb2_2_2": {"state": 2, "type": "eb2_2_2"}, "eq_6_5": {"state": 3, "type": "eq_6_5"}, "ds_e6stewmt_1": {"state": 3, "type": "ds_e6stewmt_1"}, "it_5_1_6": {"state": 3, "type": "it_5_1_6"}, "eb2_1_8": {"state": 2, "type": "eb2_1_8"}, "ds_fd_14": {"state": 3, "type": "ds_fd_14"}, "ds_juice_8": {"state": 3, "type": "ds_juice_8"}, "it_5_1_9": {"state": 3, "type": "it_5_1_9"}, "ds_e1cockt_19": {"state": 3, "type": "ds_e1cockt_19"}, "eb6_1_8": {"state": 2, "type": "eb6_1_8"}, "additem_1_3": {"state": 2, "type": "additem_1_3"}, "it_5_2_1": {"state": 3, "type": "it_5_2_1"}, "greenbox_1": {"state": 2, "type": "greenbox_1"}, "gold_4": {"state": 3, "type": "gold_4"}, "eq_4_5": {"state": 3, "type": "eq_4_5"}, "it_7_1_9": {"state": 2, "type": "it_7_1_9"}, "ds_fd_23": {"state": 3, "type": "ds_fd_23"}, "it_1_2_1_3": {"state": 3, "type": "it_1_2_1_3"}, "it_7_2_4": {"state": 3, "type": "it_7_2_4"}, "eq_3_1": {"state": 3, "type": "eq_3_1"}, "eb4_1_7": {"state": 2, "type": "eb4_1_7"}, "ds_grillmt_8": {"state": 3, "type": "ds_grillmt_8"}, "eq_3_6": {"state": 3, "type": "eq_3_6"}, "pd_1_3": {"state": 3, "type": "pd_1_3"}, "eq_5_5": {"state": 3, "type": "eq_5_5"}, "ds_e4friedmt_6": {"state": 3, "type": "ds_e4friedmt_6"}, "ds_sal_1": {"state": 3, "type": "ds_sal_1"}, "it_3_1_7": {"state": 3, "type": "it_3_1_7"}, "ds_fd_21": {"state": 3, "type": "ds_fd_21"}, "eb6_2_5": {"state": 2, "type": "eb6_2_5"}, "eb1_1_5": {"state": 2, "type": "eb1_1_5"}, "eb6_1_6": {"state": 2, "type": "eb6_1_6"}, "pd_3_3": {"state": 3, "type": "pd_3_3"}, "it_1_2_3": {"state": 3, "type": "it_1_2_3"}, "ds_mixdrk_5": {"state": 3, "type": "ds_mixdrk_5"}, "ds_chopve_2": {"state": 3, "type": "ds_chopve_2"}, "eb2_1_5": {"state": 2, "type": "eb2_1_5"}, "it_5_1_8": {"state": 3, "type": "it_5_1_8"}, "it_1_1_1_4": {"state": 3, "type": "it_1_1_1_4"}, "eq_6_3": {"state": 3, "type": "eq_6_3"}, "eb6_2_2": {"state": 2, "type": "eb6_2_2"}, "it_4_1_6": {"state": 3, "type": "it_4_1_6"}, "it_1_1_1_2": {"state": 3, "type": "it_1_1_1_2"}, "ds_fd_1": {"state": 3, "type": "ds_fd_1"}, "eb2_1_2": {"state": 2, "type": "eb2_1_2"}, "eq_1_6": {"state": 3, "type": "eq_1_6"}, "it_3_2_7": {"state": 3, "type": "it_3_2_7"}, "eb1_1_8": {"state": 2, "type": "eb1_1_8"}, "eq_4_1": {"state": 3, "type": "eq_4_1"}, "eb1_1_9": {"state": 2, "type": "eb1_1_9"}, "ds_e1cockt_14": {"state": 3, "type": "ds_e1cockt_14"}, "ds_friedve_3": {"state": 3, "type": "ds_friedve_3"}, "ds_juice_3": {"state": 3, "type": "ds_juice_3"}, "skipprop_3": {"state": 2, "type": "skipprop_3"}, "it_2_3_1_2": {"state": 3, "type": "it_2_3_1_2"}, "gem_4": {"state": 3, "type": "gem_4"}, "it_7_2_5": {"state": 3, "type": "it_7_2_5"}, "additem_1": {"state": 2, "type": "additem_1"}, "eb2_2_5": {"state": 2, "type": "eb2_2_5"}, "ds_e1icytre_2": {"state": 3, "type": "ds_e1icytre_2"}, "gem_3": {"state": 3, "type": "gem_3"}, "it_3_1_5": {"state": 3, "type": "it_3_1_5"}, "ds_e4sf_15": {"state": 3, "type": "ds_e4sf_15"}, "ds_chopfr_1": {"state": 3, "type": "ds_chopfr_1"}, "ds_e1icytre_1": {"state": 3, "type": "ds_e1icytre_1"}, "ds_friedmt_3": {"state": 3, "type": "ds_friedmt_3"}, "it_3_2_5": {"state": 3, "type": "it_3_2_5"}, "it_1_2_8": {"state": 3, "type": "it_1_2_8"}, "eb6_1_2": {"state": 2, "type": "eb6_1_2"}, "it_5_2_6": {"state": 3, "type": "it_5_2_6"}, "it_5_1_7": {"state": 3, "type": "it_5_1_7"}, "ds_juice_1": {"state": 3, "type": "ds_juice_1"}, "it_6_1_7": {"state": 3, "type": "it_6_1_7"}, "eb6_1_10": {"state": 2, "type": "eb6_1_10"}, "ds_fd_6": {"state": 3, "type": "ds_fd_6"}, "ds_fd_18": {"state": 3, "type": "ds_fd_18"}, "eq_5_3": {"state": 3, "type": "eq_5_3"}, "ds_e1cockt_9": {"state": 3, "type": "ds_e1cockt_9"}, "ds_friedmt_5": {"state": 3, "type": "ds_friedmt_5"}, "eb2_1_7": {"state": 2, "type": "eb2_1_7"}, "pd_1_5": {"state": 3, "type": "pd_1_5"}, "freebox_1": {"state": 2, "type": "freebox_1"}, "it_3_1_3": {"state": 3, "type": "it_3_1_3"}, "ds_juice_6": {"state": 3, "type": "ds_juice_6"}, "ds_e1cockt_16": {"state": 2, "type": "ds_e1cockt_16"}, "eb1_2_4": {"state": 2, "type": "eb1_2_4"}, "it_5_2_8": {"state": 3, "type": "it_5_2_8"}, "pd_4_4": {"state": 3, "type": "pd_4_4"}, "it_2_1_6": {"state": 3, "type": "it_2_1_6"}, "eb6_1_9": {"state": 2, "type": "eb6_1_9"}, "ds_grillmt_9": {"state": 3, "type": "ds_grillmt_9"}, "it_3_1_10": {"state": 3, "type": "it_3_1_10"}, "eb1_1_7": {"state": 2, "type": "eb1_1_7"}, "pd_7_5": {"state": 3, "type": "pd_7_5"}, "it_1_1_1_3": {"state": 3, "type": "it_1_1_1_3"}, "ds_grillsf_6": {"state": 3, "type": "ds_grillsf_6"}, "ds_grillmt_1": {"state": 3, "type": "ds_grillmt_1"}, "eb4_1_5": {"state": 2, "type": "eb4_1_5"}, "it_1_2_5": {"state": 3, "type": "it_1_2_5"}, "it_1_1_2_3": {"state": 3, "type": "it_1_1_2_3"}, "pd_4_6": {"state": 3, "type": "pd_4_6"}, "ds_friedmt_2": {"state": 3, "type": "ds_friedmt_2"}, "ds_juice_4": {"state": 3, "type": "ds_juice_4"}, "ds_grillsf_7": {"state": 3, "type": "ds_grillsf_7"}, "ds_fd_11": {"state": 3, "type": "ds_fd_11"}, "ds_fd_10": {"state": 3, "type": "ds_fd_10"}, "ds_grillve_1": {"state": 3, "type": "ds_grillve_1"}, "eb2_1_1": {"state": 2, "type": "eb2_1_1"}, "eb1_1_2": {"state": 2, "type": "eb1_1_2"}, "it_1_1_5": {"state": 3, "type": "it_1_1_5"}, "ds_friedsf_2": {"state": 3, "type": "ds_friedsf_2"}, "ds_chopve_4": {"state": 3, "type": "ds_chopve_4"}, "eb1_2_1": {"state": 2, "type": "eb1_2_1"}, "it_4_1_1": {"state": 3, "type": "it_4_1_1"}, "pd_1_6": {"state": 3, "type": "pd_1_6"}, "it_7_1_8": {"state": 3, "type": "it_7_1_8"}, "eq_6_2": {"state": 3, "type": "eq_6_2"}, "it_5_1_10": {"state": 3, "type": "it_5_1_10"}, "it_2_3_6": {"state": 3, "type": "it_2_3_6"}, "eq_3_3": {"state": 3, "type": "eq_3_3"}, "it_1_2_1_2": {"state": 3, "type": "it_1_2_1_2"}, "ds_e1cockt_5": {"state": 3, "type": "ds_e1cockt_5"}, "eb4_1_6": {"state": 2, "type": "eb4_1_6"}, "eq_3_4": {"state": 3, "type": "eq_3_4"}, "it_2_1_10": {"state": 3, "type": "it_2_1_10"}, "ds_e1cockt_13": {"state": 3, "type": "ds_e1cockt_13"}, "it_4_2_7": {"state": 3, "type": "it_4_2_7"}, "eb6_1_5": {"state": 2, "type": "eb6_1_5"}, "ds_fd_9": {"state": 3, "type": "ds_fd_9"}, "it_7_2_6": {"state": 3, "type": "it_7_2_6"}, "eb4_1_1": {"state": 2, "type": "eb4_1_1"}, "skipprop_5": {"state": 2, "type": "skipprop_5"}, "pd_2_5": {"state": 3, "type": "pd_2_5"}, "pd_7_2": {"state": 3, "type": "pd_7_2"}, "it_5_1_3": {"state": 3, "type": "it_5_1_3"}, "eb6_1_1": {"state": 2, "type": "eb6_1_1"}, "ds_e6dst_2": {"state": 3, "type": "ds_e6dst_2"}, "it_4_2_6": {"state": 3, "type": "it_4_2_6"}, "ds_fd_2": {"state": 3, "type": "ds_fd_2"}, "it_5_1_4": {"state": 3, "type": "it_5_1_4"}, "eb6_2_4": {"state": 2, "type": "eb6_2_4"}, "ds_grillmt_4": {"state": 3, "type": "ds_grillmt_4"}, "pd_1_1": {"state": 3, "type": "pd_1_1"}, "eq_2_1": {"state": 3, "type": "eq_2_1"}, "eq_5_4": {"state": 3, "type": "eq_5_4"}, "eq_4_3": {"state": 3, "type": "eq_4_3"}, "it_3_2_4": {"state": 3, "type": "it_3_2_4"}, "ds_e3juice_10": {"state": 3, "type": "ds_e3juice_10"}, "eb2_2_1": {"state": 2, "type": "eb2_2_1"}, "ds_juice_2": {"state": 3, "type": "ds_juice_2"}, "it_1_1_9": {"state": 3, "type": "it_1_1_9"}, "ds_grillmt_5": {"state": 3, "type": "ds_grillmt_5"}, "it_2_3_2": {"state": 3, "type": "it_2_3_2"}, "gem_1_1": {"state": 2, "type": "gem_1_1"}, "ds_juice_9": {"state": 3, "type": "ds_juice_9"}, "skipprop_4": {"state": 2, "type": "skipprop_4"}, "pd_4_5": {"state": 3, "type": "pd_4_5"}, "eq_2_6": {"state": 3, "type": "eq_2_6"}, "eb4_1_3": {"state": 2, "type": "eb4_1_3"}, "it_7_1_6": {"state": 3, "type": "it_7_1_6"}, "ds_juice_7": {"state": 3, "type": "ds_juice_7"}, "gold_3": {"state": 3, "type": "gold_3"}, "it_6_1_6": {"state": 3, "type": "it_6_1_6"}, "ene_4": {"state": 3, "type": "ene_4"}, "ds_e5mt_1": {"state": 3, "type": "ds_e5mt_1"}, "eb2_1_4": {"state": 2, "type": "eb2_1_4"}, "skipprop_1": {"state": 2, "type": "skipprop_1"}, "ds_grillsf_1": {"state": 3, "type": "ds_grillsf_1"}, "it_1_2_1_1": {"state": 3, "type": "it_1_2_1_1"}, "it_3_1_1": {"state": 3, "type": "it_3_1_1"}, "pd_2_6": {"state": 3, "type": "pd_2_6"}, "eb4_2_2": {"state": 1, "type": "eb4_2_2"}, "ds_mixdrk_8": {"state": 3, "type": "ds_mixdrk_8"}, "it_4_1_7": {"state": 3, "type": "it_4_1_7"}, "ds_mixdrk_9": {"state": 3, "type": "ds_mixdrk_9"}, "it_1_2_2": {"state": 3, "type": "it_1_2_2"}, "it_4_1_4": {"state": 3, "type": "it_4_1_4"}, "it_6_1_1": {"state": 3, "type": "it_6_1_1"}, "it_5_2_5": {"state": 3, "type": "it_5_2_5"}, "it_4_1_5": {"state": 3, "type": "it_4_1_5"}, "ds_chopfru_1": {"state": 3, "type": "ds_chopfru_1"}, "additem_2": {"state": 2, "type": "additem_2"}, "eb1_2_2": {"state": 2, "type": "eb1_2_2"}, "it_7_1_1": {"state": 3, "type": "it_7_1_1"}, "ds_grillmt_2": {"state": 3, "type": "ds_grillmt_2"}, "it_4_1_8": {"state": 3, "type": "it_4_1_8"}, "pd_4_2": {"state": 3, "type": "pd_4_2"}, "pd_1_4": {"state": 3, "type": "pd_1_4"}, "pd_2_4": {"state": 3, "type": "pd_2_4"}, "ds_fd_5": {"state": 3, "type": "ds_fd_5"}, "additem_1_1": {"state": 2, "type": "additem_1_1"}, "eb1_1_3": {"state": 2, "type": "eb1_1_3"}, "ds_grillmt_12": {"state": 3, "type": "ds_grillmt_12"}, "ds_friedsf_3": {"state": 3, "type": "ds_friedsf_3"}, "it_6_1_3": {"state": 3, "type": "it_6_1_3"}, "it_3_1_9": {"state": 3, "type": "it_3_1_9"}, "ds_fd_13": {"state": 3, "type": "ds_fd_13"}, "ds_e1cockt_8": {"state": 3, "type": "ds_e1cockt_8"}, "gem_2": {"state": 3, "type": "gem_2"}, "eq_1_5": {"state": 3, "type": "eq_1_5"}, "it_2_3_5": {"state": 3, "type": "it_2_3_5"}, "eq_6_4": {"state": 3, "type": "eq_6_4"}, "it_5_1_2": {"state": 3, "type": "it_5_1_2"}, "it_2_1_7": {"state": 3, "type": "it_2_1_7"}, "eb6_1_3": {"state": 2, "type": "eb6_1_3"}, "gold_1": {"state": 3, "type": "gold_1"}, "it_6_1_4": {"state": 3, "type": "it_6_1_4"}, "pd_6_2": {"state": 3, "type": "pd_6_2"}, "it_4_1_3": {"state": 3, "type": "it_4_1_3"}, "it_2_3_1_3": {"state": 3, "type": "it_2_3_1_3"}, "ds_grillmt_7": {"state": 3, "type": "ds_grillmt_7"}, "ds_mixdrk_1": {"state": 3, "type": "ds_mixdrk_1"}, "pd_4_1": {"state": 3, "type": "pd_4_1"}, "it_5_2_3": {"state": 3, "type": "it_5_2_3"}, "ds_e4friedmt_7": {"state": 1, "type": "ds_e4friedmt_7"}, "gem_1": {"state": 3, "type": "gem_1"}, "pd_2_1": {"state": 3, "type": "pd_2_1"}, "it_1_1_2_4": {"state": 3, "type": "it_1_1_2_4"}, "it_2_1_1": {"state": 3, "type": "it_2_1_1"}, "ds_friedsf_1": {"state": 3, "type": "ds_friedsf_1"}, "pd_7_1": {"state": 3, "type": "pd_7_1"}, "ds_grillmt_6": {"state": 3, "type": "ds_grillmt_6"}, "it_2_2_3": {"state": 3, "type": "it_2_2_3"}, "eb1_2_5": {"state": 2, "type": "eb1_2_5"}, "additem_3": {"state": 2, "type": "additem_3"}, "ds_grillmt_3": {"state": 3, "type": "ds_grillmt_3"}, "ds_grillsf_3": {"state": 3, "type": "ds_grillsf_3"}, "it_5_2_2": {"state": 3, "type": "it_5_2_2"}, "it_2_3_3": {"state": 3, "type": "it_2_3_3"}, "it_7_1_5": {"state": 3, "type": "it_7_1_5"}, "it_2_1_3": {"state": 3, "type": "it_2_1_3"}, "eq_6_1": {"state": 3, "type": "eq_6_1"}, "it_1_2_6": {"state": 3, "type": "it_1_2_6"}, "it_4_1_9": {"state": 3, "type": "it_4_1_9"}, "it_4_2_4": {"state": 3, "type": "it_4_2_4"}, "pd_5_3": {"state": 3, "type": "pd_5_3"}, "it_1_2_4": {"state": 3, "type": "it_1_2_4"}, "eb1_1_1": {"state": 2, "type": "eb1_1_1"}, "ds_grillmt_11": {"state": 3, "type": "ds_grillmt_11"}, "ds_e1hotdrk_2": {"state": 2, "type": "ds_e1hotdrk_2"}, "pd_7_4": {"state": 3, "type": "pd_7_4"}, "ene_2": {"state": 3, "type": "ene_2"}, "gold_5": {"state": 3, "type": "gold_5"}, "it_2_2_4": {"state": 3, "type": "it_2_2_4"}, "pd_3_6": {"state": 3, "type": "pd_3_6"}, "pd_2_7": {"state": 3, "type": "pd_2_7"}, "it_7_2_1": {"state": 3, "type": "it_7_2_1"}, "it_4_2_1": {"state": 3, "type": "it_4_2_1"}, "it_2_2_5": {"state": 3, "type": "it_2_2_5"}, "ene_1": {"state": 3, "type": "ene_1"}, "eb6_2_3": {"state": 2, "type": "eb6_2_3"}, "eb6_2_1": {"state": 2, "type": "eb6_2_1"}, "it_3_1_4": {"state": 3, "type": "it_3_1_4"}, "it_5_1_1": {"state": 3, "type": "it_5_1_1"}, "pd_3_7": {"state": 3, "type": "pd_3_7"}, "eq_2_3": {"state": 3, "type": "eq_2_3"}, "eb2_2_4": {"state": 2, "type": "eb2_2_4"}, "eb4_1_2": {"state": 2, "type": "eb4_1_2"}, "it_4_2_3": {"state": 3, "type": "it_4_2_3"}, "ds_chopfs_1": {"state": 3, "type": "ds_chopfs_1"}, "pd_7_3": {"state": 3, "type": "pd_7_3"}, "ds_dst_1": {"state": 3, "type": "ds_dst_1"}, "ds_e1cockt_1": {"state": 3, "type": "ds_e1cockt_1"}, "it_4_1_2": {"state": 3, "type": "it_4_1_2"}, "eb1_1_10": {"state": 2, "type": "eb1_1_10"}, "pd_3_5": {"state": 3, "type": "pd_3_5"}, "ds_fd_8": {"state": 3, "type": "ds_fd_8"}, "ds_fd_7": {"state": 3, "type": "ds_fd_7"}, "ds_grillve_4": {"state": 3, "type": "ds_grillve_4"}, "pd_6_1": {"state": 3, "type": "pd_6_1"}, "pd_6_3": {"state": 3, "type": "pd_6_3"}, "it_5_1_5": {"state": 3, "type": "it_5_1_5"}, "it_1_1_10": {"state": 3, "type": "it_1_1_10"}, "it_3_2_6": {"state": 3, "type": "it_3_2_6"}, "ds_e1cockt_4": {"state": 3, "type": "ds_e1cockt_4"}, "it_1_2_7": {"state": 3, "type": "it_1_2_7"}, "ds_fd_20": {"state": 3, "type": "ds_fd_20"}, "ds_mixdrk_6": {"state": 3, "type": "ds_mixdrk_6"}, "ds_mixdrk_2": {"state": 3, "type": "ds_mixdrk_2"}, "eb2_1_3": {"state": 2, "type": "eb2_1_3"}, "pd_1_7": {"state": 3, "type": "pd_1_7"}, "it_2_3_1_4": {"state": 3, "type": "it_2_3_1_4"}, "ds_e4sf_12": {"state": 3, "type": "ds_e4sf_12"}, "it_2_2_2": {"state": 3, "type": "it_2_2_2"}, "ds_grillsf_5": {"state": 3, "type": "ds_grillsf_5"}, "it_2_3_1": {"state": 3, "type": "it_2_3_1"}, "pd_6_4": {"state": 3, "type": "pd_6_4"}, "ds_fd_17": {"state": 3, "type": "ds_fd_17"}, "eb2_2_3": {"state": 2, "type": "eb2_2_3"}, "eq_2_5": {"state": 3, "type": "eq_2_5"}, "ds_e1hotdrk_1": {"state": 3, "type": "ds_e1hotdrk_1"}, "it_7_2_3": {"state": 3, "type": "it_7_2_3"}, "ds_grillsf_4": {"state": 3, "type": "ds_grillsf_4"}, "ds_grillve_3": {"state": 3, "type": "ds_grillve_3"}, "ds_fd_3": {"state": 3, "type": "ds_fd_3"}, "it_7_1_2": {"state": 3, "type": "it_7_1_2"}, "ds_e3juice_11": {"state": 3, "type": "ds_e3juice_11"}, "it_1_1_2_5": {"state": 3, "type": "it_1_1_2_5"}, "ds_mixdrk_3": {"state": 3, "type": "ds_mixdrk_3"}, "ds_friedsf_4": {"state": 3, "type": "ds_friedsf_4"}, "eq_1_4": {"state": 3, "type": "eq_1_4"}, "it_2_1_2": {"state": 3, "type": "it_2_1_2"}, "ds_fd_19": {"state": 3, "type": "ds_fd_19"}, "ds_fd_16": {"state": 3, "type": "ds_fd_16"}, "ds_e1cockt_10": {"state": 3, "type": "ds_e1cockt_10"}, "eb6_1_7": {"state": 2, "type": "eb6_1_7"}, "it_2_2_6": {"state": 3, "type": "it_2_2_6"}, "it_6_1_5": {"state": 3, "type": "it_6_1_5"}, "ds_chopve_3": {"state": 3, "type": "ds_chopve_3"}, "pd_2_3": {"state": 3, "type": "pd_2_3"}, "ds_e6cockt_7": {"state": 2, "type": "ds_e6cockt_7"}, "cbox1_1": {"state": 2, "type": "cbox1_1"}, "pd_3_4": {"state": 3, "type": "pd_3_4"}, "eq_4_2": {"state": 3, "type": "eq_4_2"}, "pd_1_8": {"state": 3, "type": "pd_1_8"}, "ds_grillve_2": {"state": 3, "type": "ds_grillve_2"}, "ds_friedmt_4": {"state": 3, "type": "ds_friedmt_4"}, "eq_3_2": {"state": 3, "type": "eq_3_2"}, "additem_1_2": {"state": 2, "type": "additem_1_2"}, "eb6_1_4": {"state": 2, "type": "eb6_1_4"}, "it_3_2_1": {"state": 3, "type": "it_3_2_1"}, "it_5_2_7": {"state": 3, "type": "it_5_2_7"}, "ds_e1cockt_6": {"state": 3, "type": "ds_e1cockt_6"}, "pd_5_4": {"state": 3, "type": "pd_5_4"}, "it_6_1_2": {"state": 3, "type": "it_6_1_2"}, "it_4_2_2": {"state": 3, "type": "it_4_2_2"}, "it_1_1_7": {"state": 3, "type": "it_1_1_7"}, "ds_e1cockt_3": {"state": 3, "type": "ds_e1cockt_3"}, "it_7_2_2": {"state": 3, "type": "it_7_2_2"}, "it_4_1_10": {"state": 3, "type": "it_4_1_10"}, "eq_1_3": {"state": 3, "type": "eq_1_3"}, "ds_e4sf_13": {"state": 3, "type": "ds_e4sf_13"}, "it_2_3_1_1": {"state": 3, "type": "it_2_3_1_1"}, "ds_e1cockt_11": {"state": 3, "type": "ds_e1cockt_11"}, "pd_5_5": {"state": 3, "type": "pd_5_5"}, "it_1_1_4": {"state": 3, "type": "it_1_1_4"}, "pd_3_2": {"state": 3, "type": "pd_3_2"}, "ds_grillsf_2": {"state": 3, "type": "ds_grillsf_2"}, "it_2_1_8": {"state": 3, "type": "it_2_1_8"}, "ds_friedve_5": {"state": 3, "type": "ds_friedve_5"}, "skipprop_2": {"state": 2, "type": "skipprop_2"}, "ds_fd_15": {"state": 3, "type": "ds_fd_15"}, "ds_grillmt_10": {"state": 3, "type": "ds_grillmt_10"}, "it_1_1_8": {"state": 3, "type": "it_1_1_8"}, "eq_1_7": {"state": 2, "type": "eq_1_7"}, "it_2_1_5": {"state": 3, "type": "it_2_1_5"}, "ds_e1cockt_2": {"state": 3, "type": "ds_e1cockt_2"}, "it_2_1_4": {"state": 3, "type": "it_2_1_4"}, "pd_2_2": {"state": 3, "type": "pd_2_2"}, "pd_5_6": {"state": 3, "type": "pd_5_6"}, "eb4_2_1": {"state": 2, "type": "eb4_2_1"}, "it_1_1_3": {"state": 3, "type": "it_1_1_3"}, "it_2_2_1": {"state": 3, "type": "it_2_2_1"}, "it_7_1_7": {"state": 3, "type": "it_7_1_7"}, "it_2_3_4": {"state": 3, "type": "it_2_3_4"}, "it_7_1_3": {"state": 3, "type": "it_7_1_3"}, "ds_fd_12": {"state": 3, "type": "ds_fd_12"}, "it_1_1_6": {"state": 3, "type": "it_1_1_6"}, "eq_2_2": {"state": 3, "type": "eq_2_2"}, "eq_3_5": {"state": 3, "type": "eq_3_5"}, "it_2_1_9": {"state": 3, "type": "it_2_1_9"}, "ds_chopve_1": {"state": 3, "type": "ds_chopve_1"}, "eb2_1_10": {"state": 2, "type": "eb2_1_10"}, "ds_e6soup_1": {"state": 3, "type": "ds_e6soup_1"}, "ds_friedve_2": {"state": 3, "type": "ds_friedve_2"}, "eq_2_4": {"state": 3, "type": "eq_2_4"}, "eb1_2_3": {"state": 2, "type": "eb1_2_3"}, "it_3_1_6": {"state": 3, "type": "it_3_1_6"}, "ds_friedve_4": {"state": 3, "type": "ds_friedve_4"}, "it_1_2_1": {"state": 3, "type": "it_1_2_1"}, "it_3_2_3": {"state": 3, "type": "it_3_2_3"}, "pd_6_5": {"state": 3, "type": "pd_6_5"}, "pd_4_3": {"state": 3, "type": "pd_4_3"}, "it_1_1_1": {"state": 3, "type": "it_1_1_1"}, "it_7_1_4": {"state": 3, "type": "it_7_1_4"}, "eb1_1_4": {"state": 2, "type": "eb1_1_4"}, "skiptime_1": {"state": 2, "type": "skiptime_1"}, "pd_3_1": {"state": 3, "type": "pd_3_1"}, "it_3_1_8": {"state": 3, "type": "it_3_1_8"}, "pd_1_2": {"state": 3, "type": "pd_1_2"}, "eb1_1_6": {"state": 2, "type": "eb1_1_6"}, "it_4_2_8": {"state": 3, "type": "it_4_2_8"}, "eb2_1_6": {"state": 2, "type": "eb2_1_6"}, "it_5_2_4": {"state": 3, "type": "it_5_2_4"}, "it_1_1_2_1": {"state": 3, "type": "it_1_1_2_1"}, "it_1_1_2": {"state": 3, "type": "it_1_1_2"}, "eb2_1_9": {"state": 2, "type": "eb2_1_9"}, "ene_5": {"state": 3, "type": "ene_5"}, "it_3_2_2": {"state": 3, "type": "it_3_2_2"}, "it_1_1_1_1": {"state": 3, "type": "it_1_1_1_1"}, "ds_friedve_1": {"state": 3, "type": "ds_friedve_1"}, "cbox2_1": {"state": 2, "type": "cbox2_1"}, "ds_friedmt_1": {"state": 3, "type": "ds_friedmt_1"}, "ene_3": {"state": 3, "type": "ene_3"}, "eb4_1_4": {"state": 2, "type": "eb4_1_4"}}, "slot": {}, "inventory": {"1751509240001": {"itemId": "1751509240001", "codeStr": "it_2_1_8", "storeTime": 9000}, "1749729418001": {"itemId": "1749729418001", "codeStr": "ds_friedve_1", "storeTime": 5000}, "1751249292001": {"itemId": "1751249292001", "codeStr": "pd_4_3", "storeTime": 8000}, "1749608236001": {"itemId": "1749608236001", "codeStr": "gem_2", "storeTime": 4000}, "1749872438001": {"itemId": "1749872438001", "codeStr": "gem_1", "storeTime": 3000}, "1749891834001": {"itemId": "1749891834001", "codeStr": "ds_friedve_1", "storeTime": 6000}, "1751854064001": {"itemId": "1751854064001", "codeStr": "additem_1", "storeTime": 1751854287}, "1749176305001": {"itemId": "1749176305001", "codeStr": "pd_1_4", "storeTime": 2000}, "1750213727001": {"itemId": "1750213727001", "codeStr": "eq_5_3", "storeTime": 7000}, "1740574630001": {"itemId": "1740574630001", "codeStr": "it_1_1_1_2", "storeTime": 1000}, "1751854080001": {"itemId": "1751854080001", "codeStr": "pd_7_3", "storeTime": 10000}}, "survey": {}, "rate": {"1": {"canPopup": 0, "rated": 0, "id": "1", "triggeredConfig": "", "activeConfigId": 0}}, "energy": {"1": {"id": "1", "generateTime": 1751869572, "energyValue": 100}, "2": {"energyValue": 100, "id": "2"}}, "shopItem": {"1751854113002": {"itemCode": "it_5_2_7", "shopType": "FlashSale", "startCount": 5, "costCount": 102, "id": "1751854113002", "costType": "gem", "leftCount": 5}, "1751854113005": {"itemCode": "it_5_2_3", "shopType": "FlashSale", "startCount": 5, "costCount": 9, "id": "1751854113005", "costType": "gem", "leftCount": 5}, "1751854113003": {"itemCode": "it_1_1_8", "shopType": "FlashSale", "startCount": 5, "costCount": 82, "id": "1751854113003", "costType": "gem", "leftCount": 5}, "1751854113006": {"itemCode": "it_3_2_3", "shopType": "FlashSale", "startCount": 5, "costCount": 15, "id": "1751854113006", "costType": "gem", "leftCount": 5}, "1751853863002": {"itemCode": "freebox_1", "shopType": "DailySpecial", "costCount": 0, "id": "1751853863002", "costType": "gem", "leftCount": 0}, "1751854113001": {"itemCode": "it_1_2_5", "shopType": "FlashSale", "startCount": 5, "costCount": 52, "id": "1751854113001", "costType": "gem", "leftCount": 5}, "1751854113004": {"itemCode": "it_7_1_4", "shopType": "FlashSale", "startCount": 5, "costCount": 7, "id": "1751854113004", "costType": "gem", "leftCount": 5}, "1751853863001": {"itemCode": "enebox_1", "shopType": "DailySpecial", "costCount": 20, "id": "1751853863001", "costType": "gem", "leftCount": 1}}, "bundle": {}, "orderMeta": {"CurChapterId": {"key": "CurChapterId", "value": "5"}, "CurOrderGroupId": {"key": "CurOrderGroupId", "value": "17"}, "CurFinishedGroupCount": {"key": "CurFinishedGroupCount", "value": "69"}, "OrderGroupCostPastDayEnergy": {"key": "OrderGroupCostPastDayEnergy", "value": "0"}, "FirstOrder": {"key": "FirstOrder", "value": "1"}, "OrderGroupCostCurDayEnergy": {"key": "OrderGroupCostCurDayEnergy", "value": "0"}, "OrderGroupConsumeEnergy": {"key": "OrderGroupConsumeEnergy", "value": "96"}, "SecondOrder": {"key": "SecondOrder", "value": "1"}, "CurGroupFinishedOrderIds": {"key": "CurGroupFinishedOrderIds", "value": ""}, "TotalFinishedGroupCount": {"key": "TotalFinishedGroupCount", "value": "69"}}, "bundleMeta": {"rush_order_199PurchaseBundleId": {"key": "rush_order_199PurchaseBundleId", "value": "{\"groupId\":\"cd1\"@\"rewards\":[{\"Amount\":40@\"Currency\":\"gem\"@\"Crypt\":\"wH\"}@{\"Amount\":4@\"Currency\":\"additem_1\"@\"Crypt\":\"w\"}@{\"Amount\":50@\"Currency\":\"energy\"@\"Crypt\":\"vH\"}]@\"bundleType\":\"cd\"}"}, "starter_bundle_1PurchaseBundleId": {"key": "starter_bundle_1PurchaseBundleId", "value": "{\"rewards\":[{\"Amount\":240@\"Crypt\":\"qLZ\"@\"Currency\":\"gem\"}@{\"Amount\":200@\"Crypt\":\"qHZ\"@\"Currency\":\"energy\"}@{\"Amount\":4@\"Crypt\":\"w\"@\"Currency\":\"additem_1\"}@{\"Amount\":1@\"Crypt\":\"r\"@\"Currency\":\"enebox_1\"}]@\"groupId\":\"starter\"@\"bundleType\":\"starter\"@\"bundleId\":\"starter_499\"}"}, "energyTriggerRefreshTime": {"key": "energyTriggerRefreshTime", "value": "1733307400"}, "energy_199PurchaseBundleId": {"key": "energy_199PurchaseBundleId", "value": "{\"groupId\":\"energy1\"@\"rewards\":[{\"Currency\":\"gem\"@\"Crypt\":\"wH\"@\"Amount\":40}@{\"Currency\":\"energy\"@\"Crypt\":\"rMZ\"@\"Amount\":150}@{\"Currency\":\"skipprop\"@\"Crypt\":\"rH\"@\"Amount\":10}]@\"bundleType\":\"energy\"}"}}, "cached_requests2": {}, "mainTask": {}, "local": {"DId": {"key": "DId", "value": "fa28c8e34b67dc2df6fec0c182067320"}, "RegisterVersion": {"key": "RegisterVersion", "value": "1.1.0"}, "Authorization": {"key": "Authorization", "value": "Xi0AAAAAAACZH29oAAAAAC1hNmU3ZWR0wz60gZ1aBoYP/Py/QH/q"}, "Icon": {"key": "Icon", "value": "head7"}, "Name": {"key": "Name", "value": "father"}, "DataInconsistent": {"key": "DataInconsistent", "value": "false"}, "LastSyncTime": {"key": "LastSyncTime", "value": "1751869750"}, "UserId": {"key": "UserId", "value": "11614"}, "VId": {"key": "VId", "value": "fa28c8e34b67dc2df6fec0c182067320"}}, "iapOrder": {}, "noticeState": {}, "returnUser": {"rewardexpiredTime": {"key": "rewardexpiredTime", "value": "0"}, "returnReward": {"key": "returnReward", "value": "0"}, "rewardLeaveDay": {"key": "rewardLeaveDay", "value": "0"}}, "misc": {"FlambeTimeFinishTime": {"key": "FlambeTimeFinishTime", "value": 0}, "EQPieceRemove": {"key": "EQPieceRemove", "value": "1"}, "EnergyBoostWindowOpenState": {"key": "EnergyBoostWindowOpenState", "value": "0"}, "FlambeTimeInstruSpeed": {"key": "FlambeTimeInstruSpeed", "value": 10}, "DataBalanceDiff": {"key": "DataBalanceDiff", "value": "eq_2#-8"}, "PDItemSPIndex": {"key": "PDItemSPIndex", "value": "pd_1_4-7;greenbox_1-1;pd_3_4-1;pd_2_6-1;pd_1_5-4;pd_1_6-1;pd_2_4-4;pd_2_5-2"}, "FreeRefillEnergy": {"key": "FreeRefillEnergy", "value": "1"}, "FlambeTimeLinkOrder": {"key": "FlambeTimeLinkOrder", "value": ""}, "FlambeTimeType": {"key": "FlambeTimeType", "value": "link"}, "FlambeTimeInstruChains": {"key": "FlambeTimeInstruChains", "value": ""}, "FlambeTimeInstruSpeedPrice": {"key": "FlambeTimeInstruSpeedPrice", "value": 0}, "EnergyBoostTriggerEndTime": {"key": "EnergyBoostTriggerEndTime", "value": "1751252019"}, "IsFlambeTimeOrderGroup": {"key": "IsFlambeTimeOrderGroup", "value": 1}, "DataBalanceVersion": {"key": "DataBalanceVersion", "value": "1.20"}, "FlambeTimePDChains": {"key": "FlambeTimePDChains", "value": ""}, "EnergyBoostUserOn": {"key": "EnergyBoostUserOn", "value": "1"}, "CheckItemRecycle": {"key": "CheckItemRecycle", "value": "0"}, "InventoryBoughtCap": {"key": "InventoryBoughtCap", "value": "7"}, "ItemTypeDeleteStateit_1_1_2": {"key": "ItemTypeDeleteStateit_1_1_2", "value": 1}}, "skin": {}, "orders": {"51170": {"chapterId": 5, "requirementStr": "ds_mixdrk_4;it_5_2_7", "id": "51170", "groupId": 17, "rewards": "gold-251;additem_1-1", "cleanGoldCount": 0, "avatarId": 5, "type": 1, "createTime": 1751854113}, "51160": {"chapterId": 5, "requirementStr": "it_2_1_7;ds_fd_10", "id": "51160", "groupId": 17, "rewards": "gold-192", "cleanGoldCount": 0, "avatarId": 4, "type": 1, "createTime": 1751854113}, "51180": {"chapterId": 5, "requirementStr": "it_7_2_5;ds_e4friedmt_7", "id": "51180", "groupId": 17, "rewards": "gold-273", "cleanGoldCount": 0, "avatarId": 6, "type": 1, "createTime": 1751854113}, "51140": {"chapterId": 5, "requirementStr": "it_2_1_7;ds_e4sf_13", "id": "51140", "groupId": 17, "rewards": "gold-271;additem_1-1", "cleanGoldCount": 0, "avatarId": 2, "type": 1, "createTime": 1751854113}, "51130": {"chapterId": 5, "requirementStr": "ds_grillmt_7;it_2_2_5", "id": "51130", "groupId": 17, "rewards": "gold-140", "cleanGoldCount": 0, "avatarId": 1, "type": 1, "createTime": 1751854113}, "51190": {"chapterId": 5, "requirementStr": "ds_chopfr_1;ds_friedve_4", "id": "51190", "groupId": 17, "rewards": "gold-270", "cleanGoldCount": 0, "avatarId": 7, "type": 1, "createTime": 1751854113}, "51150": {"chapterId": 5, "requirementStr": "ds_e4sf_12", "id": "51150", "groupId": 17, "rewards": "gold-130", "cleanGoldCount": 0, "avatarId": 3, "type": 1, "createTime": 1751854113}}, "user": {"gem_Give": {"key": "gem_Give", "value": "0"}, "taskprgs_Give": {"key": "taskprgs_Give", "value": "71"}, "gem_Buy": {"key": "gem_Buy", "value": "0"}, "skipprop_Give": {"key": "skipprop_Give", "value": "213"}, "exp_Buy": {"key": "exp_Buy", "value": "0"}, "gold_Buy": {"key": "gold_Buy", "value": "0"}, "skipprop_Buy": {"key": "skipprop_Buy", "value": "0"}, "exp_Give": {"key": "exp_Give", "value": "740"}, "User_Level": {"key": "User_Level", "value": "26"}, "gold_Give": {"key": "gold_Give", "value": "666"}}}