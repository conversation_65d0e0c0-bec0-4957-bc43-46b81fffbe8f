EUserLocalDataKey = {
  UserId = "UserId",
  VendorId = "VId",
  DeviceId = "DId",
  SSOToken = "Authorization",
  RegisterVersion = "RegisterVersion",
  LastSyncTime = "LastSyncTime",
  DataInconsistent = "DataInconsistent",
  Name = "Name",
  Icon = "Icon"
}
EUserSyncDataKey = {UserLevel = "User_Level"}
EMiscKey = {
  InventoryBoughtCap = "InventoryBoughtCap",
  InventoryLevelCap = "InventoryLevelCap",
  FreeRefillEnergy = "FreeRefillEnergy",
  IntroTimelineFinished = "IntroTimelineFinished",
  EnergyBoostUserOn = "EnergyBoostUserOn",
  EnergyBoostQuadUserOn = "EnergyBoostQuadUserOn",
  EnergyBoostWindowOpenState = "EnergyBoostWindowOpenState",
  EnergyBoostTriggerEndTime = "EnergyBoostTriggerEndTime",
  EnergyBoostTriggerType = "EnergyBoostTriggerType",
  PDItemSPIndex = "PDItemSPIndex",
  EQPieceRemove = "EQPieceRemove",
  IsFlambeTimeOrderGroup = "IsFlambeTimeOrderGroup",
  FlambeTimeType = "FlambeTimeType",
  FlambeTimeFinishTime = "FlambeTimeFinishTime",
  FlambeTimePDChains = "FlambeTimePDChains",
  FlambeTimeInstruChains = "FlambeTimeInstruChains",
  FlambeTimeInstruSpeed = "FlambeTimeInstruSpeed",
  FlambeTimeInstruSpeedPrice = "FlambeTimeInstruSpeedPrice",
  FlambeTimeLinkOrder = "FlambeTimeLinkOrder",
  DataBalanceVersion = "DataBalanceVersion",
  DataBalanceDiff = "DataBalanceDiff",
  CheckItemRecycle = "CheckItemRecycle"
}
EPropertyTestKey = {
  ConsumedGold = "test_ConsumedGold",
  GoldAcquireFromBoardCollect = "test_GoldAcquireFBC",
  GemAcquireFromBoardCollect = "test_GemAcquireFBC"
}
EPlayerPrefKey = {
  TableLastSerializationTimePrefix = "LastSerializationTimestamp_",
  TableModificationPrefix = "TableModified_",
  TableHasDataPrefix = "TableHasDataNew_",
  InstallUuid = "InstallUuid",
  UserDataCracked = "UserDataCracked",
  AccountSelectSuccess = "AccountSelectSuccess",
  CrossPromtionWindowOpened = "CrossPromtionWindowOpened",
  MoreGameWindowOpened = "MoreGameWindowOpened",
  BundleChainWindowOpenTime = "BundleChainWindowOpenTime",
  UpdateHintWindowOpenCount = "UpdateHintWindowOpenCount",
  UpdateHintWindowOpenDay = "UpdateHintWindowOpenDay",
  FlambeModeHelpWindowOpened = "FlambeTimeHelpWindowOpened",
  FlambeLinkHelpWindowOpened = "FlambeLinkHelpWindowOpened",
  UserIdForCSharp = "UserIdForCSharp",
  BIMsgId = "BIMsgId",
  RateDisabled = "RateDisabled",
  AdShowingTime = "AdShowingTime",
  EnteredBindEntry = "EnteredBindEntry",
  GetInstallReferrer = "GetInstallReferrer",
  RewardTokenMap = "RewardTokenMap",
  ForceDownloadChapterId = "ForceDownloadChapterId",
  SelectedServer = "SelectedServer",
  ShowItemTestInfo = "ShowItemTestInfo",
  FavoriteItems = "FavoriteItems1",
  FavoriteContentOpenPos = "FavoriteContentOpenPos",
  TestLockServerKey = "TestLockServerKey",
  TestSkipStartVideo = "TestSkipStartVideo",
  TestABGroup = "TestABGroup",
  TestTimelineEditData = "TestTimelineEditData",
  LogFileStorage = "LogFileStorage",
  TestDebugAddress = "TestDebugAddress",
  MuteDownload = "MuteDownload",
  TestShowTextKey = "TestShowTextKey",
  TestUnlockAllTask = "TestUnlockAllTask",
  TestSuperTap = "TestSuperTap",
  TestIgnoreTutorial = "TestIgnoreTutorial",
  TestServerSchema = "TestServerSchema",
  TestAddressableLoadFileFail = "TestAddressableLoadFileFail",
  TestEnableBackwardProgress = "TestEnableBackwardProgress",
  TestCheckRuntimeLanguage = "TestCheckRuntimeLanguage",
  TestAlwaysBubble = "TestAlwaysBubble",
  TestIgnorePopupChain = "TestIgnorePopupChain",
  TestFavoriteItemContent = "TestFavoriteItemContent",
  TestShowAllOrderBubbles = "TestShowAllOrderBubbles",
  TestUnityRestore = "TestUnityRestore",
  TestUnityIAPFail = "TestUnityIAPFail",
  FirstOpenTime = "FirstOpenTime",
  SsoRegisterIdfa = "SsoRegisterIdfa",
  CompressMethod = "CompressMethod",
  BundleStarterOpenTime = "BundleStarterOpenTime",
  BundleDailyOpenTime = "BundleDailyOpenTime",
  GrowthFundOpenCount = "GrowthFundOpenCount",
  BundleCollectGroupInfoTime = "BundleCollectGroupInfoTime",
  BundleLoginRecordTime = "BundleLoginRecordTime",
  PrivacyPolicyAccepted = "PrivacyPolicyAccepted",
  OpenNotification = "OpenNotification",
  OpenHint = "OpenHint",
  OpenVibrate = "OpenVibrate",
  SystemNotiRequsted = "SystemNotiRequsted",
  NotifiWindowPopTimes = "IOSNotifiWindowPopTimes",
  LastNotifiWindowPopTime = "LastIOSNotifiWindowPopTime",
  NotifiStateKey = "NotifiStateKey",
  NotificationClearTimesKey = "IOSNotificationClearTimesKey",
  PushToken = "PushToken",
  PushTokenUserId = "PushTokenUserId",
  AppleSignInRefreshToken = "AppleSignInRefreshToken",
  CoinRaceDailyOpenTime = "CoinRaceDailyOpenTime",
  PkRaceDailyOpenTime = "PkRaceDailyOpenTime",
  LastTimePurchaseTime = "LastTimePurchaseTime"
}
