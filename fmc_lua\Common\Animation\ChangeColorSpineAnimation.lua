ChangeColorSpineAnimation = setmetatable({}, SpineAnimation)
ChangeColorSpineAnimation.__index = ChangeColorSpineAnimation

function ChangeColorSpineAnimation:Init(name)
  if not self[name] then
    Log.Error(name .. " 的头发颜色未配置！")
  end
  self.color = UIUtil.ConvertHexColor2CSColor(self[name] or "543508")
  SpineAnimation.Init(self)
  self.arrSlotName = {}
  for i = 1, 100 do
    if self["slot" .. i] then
      self.arrSlotName[#self.arrSlotName + 1] = self["slot" .. i]
    else
      break
    end
  end
  self:ChangeSlotsColor(self.arrSlotName, self.color)
end

function ChangeColorSpineAnimation:LateUpdate()
  self:ChangeSlotsColor(self.arrSlotName, self.color)
end

function ChangeColorSpineAnimation:ChangeSlotsColor(arrSlotName, color)
  local slot, curColor
  for _, slotName in ipairs(arrSlotName) do
    slot = self:FindSlot(slotName)
    if slot then
      curColor = slot:GetColor()
      slot:SetColor(CSColor(color.r, color.g, color.b, curColor.a))
    else
      Log.Error("Slot " .. slotName .. " 不存在！请策划检查配置！")
    end
  end
end
