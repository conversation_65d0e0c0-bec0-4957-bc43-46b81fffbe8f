ItemRewardBubbleEventType = {Disposed = 1}
ItemRewardBubble = setmetatable({}, BaseItemComponent)
ItemRewardBubble.__index = ItemRewardBubble

function ItemRewardBubble.Create(innerItemCode)
  local itemBubble = setmetatable({}, ItemRewardBubble)
  itemBubble:Init(innerItemCode)
  return itemBubble
end

function ItemRewardBubble:Init(innerItemCode)
  self.m_innerItemCode = innerItemCode
  self.event = PairEvent.Create(self)
end

function ItemRewardBubble:OnBreak()
  self:_Break()
end

function ItemRewardBubble:_Break()
  local boardModel = self.m_itemModel:GetBoardModel()
  boardModel:RemoveItem(self.m_itemModel)
  RewardApi.AcquireRewardsLogic({
    {
      [PROPERTY_TYPE] = self.m_innerItemCode,
      [PROPERTY_COUNT] = 1
    }
  }, EPropertySource.Give, EBIType.BreakRewardBubble, nil, CacheItemType.Stack)
  boardModel.event:Call(BoardEventType.RewardBubbleDisposed, {
    Source = self.m_itemModel,
    New = self.m_innerItemCode
  })
  self.event:Call(ItemRewardBubbleEventType.Disposed)
end

function ItemRewardBubble:GetInnerItemCode()
  return self.m_innerItemCode
end
