local DBKey = {
  CurGearBundleId = "curGearBundleId",
  MissedNum = "missedNum",
  LastGear = "lastGear"
}
local defaultOrderConfType = "defaultOrder"
BundleMultiTierModel = setmetatable({}, BundleActivityBaseModel)
BundleMultiTierModel.__index = BundleMultiTierModel

function BundleMultiTierModel:Init(...)
  BundleActivityBaseModel.Init(self, ...)
  EventDispatcher.AddListener(EEventType.PropertyAcquired, self, self._OnPropertyAcquired)
end

function BundleMultiTierModel:GetShopContainerConfig()
  return {
    shopType = EShopType.MultiTierBundle,
    checkFun = function(dataGroup)
      return self:GetBundleState(dataGroup) == EBundleState.Opening
    end
  }
end

function BundleMultiTierModel:OnBundleStateChanged(dataGroup)
  BundleActivityBaseModel.OnBundleStateChanged(self, dataGroup)
end

function BundleMultiTierModel:BuyBundle(bundleData, callback)
  GM.BundleManager:Buy(self.m_bundleType, bundleData, callback)
end

function BundleMultiTierModel:_OnPurchaseFinished(dataGroup, bundleId)
  BundleActivityBaseModel._OnPurchaseFinished(self, dataGroup, bundleId)
  local groupId = dataGroup:GetGroupId()
  self:SetLastBoughtGear(groupId, self:_GetBundleIndex(dataGroup, bundleId))
  self:SetMissedNum(groupId, 0)
end

function BundleMultiTierModel:GetCurBundleGemByDataGroup(dataGroup)
  local arrBundles = self:GetCurBundleDatas(dataGroup) or {}
  local gemNum
  for _, bundleData in ipairs(arrBundles) do
    local tempGemNum = 0
    local goods = bundleData:GetGoods()
    for _, v in pairs(goods or {}) do
      if v[PROPERTY_TYPE] == EPropertyType.Gem then
        tempGemNum = tempGemNum + v[PROPERTY_COUNT]
      end
    end
    if gemNum == nil then
      gemNum = tempGemNum
    else
      gemNum = math.min(gemNum, tempGemNum)
    end
  end
  return gemNum or 0
end

function BundleMultiTierModel:GetCurBundleDatas(dataGroup)
  local bundleIds = dataGroup:GetBundleIds()
  local bundleDatas = {}
  local curGearIndex = self:_GetCurGearIndex(dataGroup)
  for i, _ in ipairs(bundleIds) do
    if math.abs(i - curGearIndex) <= 1 then
      table.insert(bundleDatas, dataGroup:GetConfigData(bundleIds[i]))
    end
  end
  return bundleDatas
end

function BundleMultiTierModel:OnBundleStateChanged(dataGroup, bTrigger)
  BundleActivityBaseModel.OnBundleStateChanged(self, dataGroup, bTrigger)
  if bTrigger and self:GetBundleState(dataGroup) == EBundleState.Opening then
    self:_AdjustGear(dataGroup)
    local groupId = dataGroup:GetGroupId()
    self:SetMissedNum(groupId, self:GetMissedNum(groupId) + 1)
  end
end

function BundleMultiTierModel:_AdjustGear(dataGroup)
  local groupId = dataGroup:GetGroupId()
  local bundleIds = dataGroup:GetBundleIds()
  local curGearIndex = self:_GetCurGearIndex(dataGroup)
  local lastBoughtGear = self:GetLastBoughtGear(groupId)
  local targetGearIndex = curGearIndex
  if lastBoughtGear ~= 0 and curGearIndex < lastBoughtGear then
    targetGearIndex = targetGearIndex + 1
  elseif lastBoughtGear ~= 0 and curGearIndex > lastBoughtGear then
    targetGearIndex = targetGearIndex - 1
  elseif self:GetMissedNum(groupId) >= 2 then
    targetGearIndex = targetGearIndex - 1
    self:SetMissedNum(groupId, 0)
  end
  if bundleIds[targetGearIndex] then
    self:_SetBundleDBData(groupId, DBKey.CurGearBundleId, bundleIds[targetGearIndex])
  end
  self:SetLastBoughtGear(groupId, 0)
end

function BundleMultiTierModel:SetLastBoughtGear(groupId, gear)
  self:_SetBundleDBData(groupId, DBKey.LastGear, tostring(gear))
end

function BundleMultiTierModel:GetLastBoughtGear(groupId)
  return tonumber(self:_GetBundleDBData(groupId, DBKey.LastGear)) or 0
end

function BundleMultiTierModel:SetMissedNum(groupId, time)
  self:_SetBundleDBData(groupId, DBKey.MissedNum, tostring(time))
end

function BundleMultiTierModel:GetMissedNum(groupId)
  return tonumber(self:_GetBundleDBData(groupId, DBKey.MissedNum)) or 0
end

function BundleMultiTierModel:_OnPropertyAcquired(msg)
  if msg == nil or Table.IsEmpty(msg.arrProperties) then
    return
  end
  for _, property in ipairs(msg.arrProperties) do
    if property[PROPERTY_TYPE] == EPropertyType.Energy and property[PROPERTY_COUNT] > 0 then
      for _, dataGroup in pairs(self.m_dataGroups) do
        self:SetMissedNum(dataGroup:GetGroupId(), 0)
      end
      break
    end
  end
end

function BundleMultiTierModel:_GetBundleIndex(dataGroup, bdId)
  for index, bundleId in ipairs(dataGroup:GetBundleIds()) do
    if bundleId == bdId then
      return index
    end
  end
end

function BundleMultiTierModel:_GetCurGearIndex(dataGroup)
  local groupId = dataGroup:GetGroupId()
  local curGearBundleId = self:_GetBundleDBData(groupId, DBKey.CurGearBundleId)
  if StringUtil.IsNilOrEmpty(curGearBundleId) or self:_GetBundleIndex(dataGroup, curGearBundleId) == nil then
    curGearBundleId = dataGroup:GetGeneralConfig(defaultOrderConfType, EConfigParamType.String)
  end
  local curGearIndex = self:_GetBundleIndex(dataGroup, curGearBundleId) or math.ceil(#dataGroup:GetBundleIds() / 2)
  curGearIndex = math.max(curGearIndex, 2)
  curGearIndex = math.min(curGearIndex, #dataGroup:GetBundleIds() - 1)
  return curGearIndex
end
