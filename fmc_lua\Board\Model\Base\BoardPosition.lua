BoardPosition = {}
BoardPosition.__index = BoardPosition

function BoardPosition:Init(boardModel, x, y)
  self.m_boardModel = boardModel
  self.m_x = x
  self.m_y = y
end

function BoardPosition:IsValid()
  return self.m_x >= 1 and self.m_x <= self.m_boardModel:GetHorizontalTiles() and 1 <= self.m_y and self.m_y <= self.m_boardModel:GetVerticalTiles()
end

function BoardPosition:GetX()
  return self.m_x
end

function BoardPosition:GetY()
  return self.m_y
end

function BoardPosition:ToLocalPosition()
  local x = (self.m_x - 1) * BaseBoardModel.TileSize
  local y = (self.m_boardModel:GetVerticalTiles() - self.m_y) * BaseBoardModel.TileSize
  return Vector2(x, y)
end

function BoardPosition.__add(position, vector)
  local newPosition = setmetatable({}, BoardPosition)
  newPosition:Init(position.m_boardModel, position.m_x + math.tointeger(vector.x), position.m_y + math.tointeger(vector.y))
  return newPosition
end

function BoardPosition.__eq(a, b)
  if getmetatable(a) == BoardPosition and getmetatable(b) == BoardPosition then
    return a.m_boardModel == b.m_boardModel and a.m_x == b.m_x and a.m_y == b.m_y
  else
    return false
  end
end

function BoardPosition.__tostring(position)
  return "(x:" .. tostring(position:GetX()) .. ",y:" .. tostring(position:GetY()) .. ")"
end
