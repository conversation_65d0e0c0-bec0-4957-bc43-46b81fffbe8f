PopupAvatarAnimation = {}
PopupAvatarAnimation.__index = PopupAvatarAnimation

function PopupAvatarAnimation:Init(expression)
  self.m_animation.color = CSColor.white
  self.m_animation.AnimationState:AddAnimation(0, expression .. "_popup", false, 0)
  self.m_animation.AnimationState:AddAnimation(0, expression .. "_idle", true, 0)
  self.m_animation:Update()
end

function PopupAvatarAnimation:OnD<PERSON>roy()
  if self.m_onComplete ~= nil then
    self.m_animation.AnimationState:Complete("-", self.m_onComplete)
  end
end

function PopupAvatarAnimation:StopOnComplete()
  function self.m_onComplete()
    self.m_animation.AnimationState:ClearTracks()
    
    self.m_animation.freeze = true
  end
  
  self.m_animation.AnimationState:Complete("+", self.m_onComplete)
end
