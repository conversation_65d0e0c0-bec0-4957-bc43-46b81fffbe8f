local moreRewardHeight = 686
ExtraBoardActivityRewardRecoverWindow = setmetatable({}, ExtraBoardActivityBaseWindow)
ExtraBoardActivityRewardRecoverWindow.__index = ExtraBoardActivityRewardRecoverWindow

function ExtraBoardActivityRewardRecoverWindow:BeforeOpenCheck()
  local model
  for activityType, _ in pairs(ExtraBoardActivityDefinition) do
    model = GM.ActivityManager:GetModel(activityType)
    if model and model:GetState() == ActivityState.Ended then
      return true
    end
  end
  return false
end

function ExtraBoardActivityRewardRecoverWindow:Init(activityType, rewards)
  ExtraBoardActivityBaseWindow.Init(self, activityType, false)
  if not Table.IsEmpty(rewards) then
    RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.ExtraBoardRewardRecover, EGameMode.Board, CacheItemType.Stack, EGameMode.ExtraBoard)
    if 5 < #rewards then
      UIUtil.SetSizeDelta(self.m_bgRect, nil, moreRewardHeight)
    end
    self.m_scrollRect.enabled = 5 < #rewards
    self.m_rewardContent:Init(rewards, 5)
    self.m_bPlayRewardAnim = true
  end
  local bMaxLevel = self.m_model:IsMaxLevel()
  local prefix = string.lower(activityType)
  local victoryDesc = prefix .. "_victory_reward"
  self.m_titleText.text = GM.GameTextModel:GetText(bMaxLevel and "extraboard_victory_title" or "extraboard_fail_title")
  self.m_descText.text = GM.GameTextModel:GetText(bMaxLevel and victoryDesc or "extraboard_fail_reward")
  UIUtil.SetActive(self.m_victoryGo, bMaxLevel)
  if self.m_failGo ~= nil then
    UIUtil.SetActive(self.m_failGo, not bMaxLevel)
  end
  self:UpdateSortingOrder()
end

function ExtraBoardActivityRewardRecoverWindow:Close()
  ExtraBoardActivityBaseWindow.Close(self)
  if self.m_bPlayRewardAnim then
    self.m_rewardContent:PlayRewardAnimation()
  end
end

function ExtraBoardActivityRewardRecoverWindow:UpdateSortingOrder()
  local sortingOrderLength = 3
  local baseSortingOrder = self:GetSortingOrder()
  self.m_contentCanvas.sortingOrder = baseSortingOrder + sortingOrderLength
  local arrRenderer = self.m_victoryGo:GetComponentsInChildren(typeof(Renderer), true)
  for i = 0, arrRenderer.Length - 1 do
    arrRenderer[i].sortingOrder = (arrRenderer[i].sortingOrder or 0) + baseSortingOrder
  end
end
