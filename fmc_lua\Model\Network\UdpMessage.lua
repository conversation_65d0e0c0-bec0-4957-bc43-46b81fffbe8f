UdpMessage = {MESSAGE_ID_SIZE = 8}
EUdpMessageStatus = {
  Method = 0,
  Path = 1,
  Host = 2,
  Status = 3,
  Desc = 4,
  UserAgent = 5,
  GameClient = 6,
  Authorization = 7,
  UserId = 8,
  Token = 9,
  NeedReplace = 10,
  Forwarded = 11,
  XForwardedFor = 12,
  XClientIp = 13,
  Range = 14,
  AcceptEncoding = 15,
  <PERSON>ie = 16,
  RequestId = 17,
  RespTooLarge = 18,
  MessageId = 19,
  RequestDate = 20,
  ContentLength = 21,
  TransferEncoding = 22,
  ContentEncoding = 23,
  Signature = 24,
  ServerTime = 25,
  SchemaName = 26,
  ContentType = 27,
  Count = 28
}
UdpMessage.__index = UdpMessage
local Serialization = require("Model.Network.Serialization")()

function UdpMessage.SerializeMessageId(writer, messageId)
  return writer:writeMessageId(messageId)
end

function UdpMessage.DeserializeMessageId(reader)
  if reader:GetLeftLength() < UdpMessage.MESSAGE_ID_SIZE then
    return false
  end
  return true, reader:readMessageId()
end

function UdpMessage.SerializeHeader(writer, header)
  return Serialization.VarUInt64.Serialize(writer, header.code) and Serialization.String.Serialize(writer, header.content)
end

function UdpMessage.DeserializeHeader(reader)
  local bRet, code, content
  bRet, code = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  bRet, content = Serialization.String.Deserialize(reader)
  if not bRet then
    return false
  end
  return true, {code = code, content = content}
end

function UdpMessage.SerializeHeaders(writer, headers)
  local arrLength = 0
  if headers ~= nil then
    arrLength = #headers
  end
  if not Serialization.VarUInt64.Serialize(writer, arrLength) then
    return false
  end
  for i = 1, arrLength do
    if not UdpMessage.SerializeHeader(writer, headers[i]) then
      return false
    end
  end
  return true
end

function UdpMessage.DeserializeHeaders(reader)
  local bRet, arrLength, header
  bRet, arrLength = Serialization.VarUInt64.Deserialize(reader)
  if not bRet then
    return false
  end
  local result = {}
  for i = 1, arrLength do
    bRet, header = UdpMessage.DeserializeHeader(reader)
    if not bRet then
      return false
    end
    table.insert(result, header)
  end
  return true, result
end

function UdpMessage.ParseIntUdpHeader(data)
  local reader = BufferReader.Create(data)
  local ret, value
  ret, value = Serialization.VarUInt64.Deserialize(reader)
  if ret then
    return value
  end
  return nil
end

function UdpMessage.FormatIntUdpHeader(value)
  local writer = BufferWriter.Create()
  if Serialization.VarUInt64.Serialize(writer, value) then
    return writer:GetString()
  end
  return nil
end

function UdpMessage.ConvertHeaders(arrHeaders)
  local headers = {}
  local arrLength = 0
  if arrHeaders ~= nil then
    arrLength = #arrHeaders
  end
  local k, v
  for i = 1, arrLength do
    if arrHeaders[i].code == EUdpMessageStatus.Status then
      headers.status = UdpMessage.ParseIntUdpHeader(arrHeaders[i].content)
    elseif arrHeaders[i].code == EUdpMessageStatus.Desc then
      headers.desc = arrHeaders[i].content
    elseif arrHeaders[i].code == EUdpMessageStatus.MessageId then
      headers.messageid = UdpMessage.ParseIntUdpHeader(arrHeaders[i].content)
    elseif arrHeaders[i].code == EUdpMessageStatus.RequestId then
      headers.requestid = arrHeaders[i].content
    elseif arrHeaders[i].code == EUdpMessageStatus.RespTooLarge then
      headers.resptoolarge = UdpMessage.ParseIntUdpHeader(arrHeaders[i].content)
    elseif arrHeaders[i].code == EUdpMessageStatus.ServerTime then
      headers["server-time"] = UdpMessage.ParseIntUdpHeader(arrHeaders[i].content)
    end
  end
  return headers
end
