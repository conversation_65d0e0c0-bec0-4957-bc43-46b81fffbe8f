OrderGroupFinishWindow = setmetatable({canCloseByAndroidBack = false, windowMaskAlpha = 0}, BaseWindow)
OrderGroupFinishWindow.__index = OrderGroupFinishWindow

function OrderGroupFinishWindow:Init()
  local mainBoardView = MainBoardView.GetInstance()
  local orderArea = mainBoardView:GetOrderArea()
  local orderGroupButton = orderArea:GetOrderGroupButton()
  local position, scaleX, eBoxRewardType = orderGroupButton:GetBoxPosAndScale()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local rewards = orderModel:GetCurGroupReward()
  local hasImmediateCD = false
  for _, rew in ipairs(rewards) do
    if RewardApi.IsImmediateEffectItem(rew[PROPERTY_TYPE]) then
      hasImmediateCD = true
      break
    end
  end
  rewards = OrderGroupFinishWindow.TryAddProducerInventoryReward(rewards)
  local title = "claim_order_group_reward"
  local prefabName = UIPrefabConfigName.OrderDayBoxRewardWindow
  local initFunc = function(win)
    if win.m_desc then
      local curDay = orderModel:GetCurOrderDay()
      if curDay == 1 then
        win.m_desc.text = GM.GameTextModel:GetText("order_day_finish_desc", curDay)
      else
        win.m_desc.text = GM.GameTextModel:GetText("order_day_finish_desc1", curDay)
      end
      win.m_ribbonSpine:Init()
      win.m_ribbonSpine:PlayAnimation("appear", nil, false, true)
      win.m_rainbowSpine:Init()
      win.m_rainbowSpine.gameObject:SetActive(false)
      DelayExecuteFuncInView(function()
        win.m_rainbowSpine.gameObject:SetActive(true)
        win.m_rainbowSpine:PlayAnimation("appear", function()
          win.m_rainbowSpine:PlayAnimation("idle", nil, true)
        end, false, true)
        DelayExecuteFuncInView(function()
          win.m_rainbowMask.enabled = false
        end, 1.9, win)
      end, 0.3, win)
      if ScreenFitter.IsWideScreen() or 5 <= #rewards then
        UIUtil.AddAnchoredPosition(win.m_rewardContent.transform, 0, -85)
        UIUtil.AddAnchoredPosition(win.m_boxRoot, 0, -85)
      end
      if ScreenFitter.IsWideScreen() then
        UIUtil.AddAnchoredPosition(win.m_topTrans, 0, 120)
      end
    end
    DelayExecuteFunc(function()
      GM.AudioModel:PlayEffect(AudioFileConfigName.SfxSilverOrderGift)
    end, 0)
  end
  local closeFunc = function()
    if orderModel:HasNextGroup() then
      self.eViewType = EViewType.Other
      self:Close()
      GM.UIManager:OpenView(UIPrefabConfigName.OrderDayRefreshWindow)
    else
      self:Close()
      EventDispatcher.DispatchEvent(EEventType.UpdateOrderGroupButton)
      if orderModel:GetOrdersCount() == 0 then
        EventDispatcher.DispatchEvent(EEventType.UpdateOrderEmpty)
        local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
        if bakeOutModel ~= nil then
          bakeOutModel:OnReadyStart()
        end
      end
    end
    if IsAutoRun() then
      local cookCmps = GM.MainBoardModel:GetAllItemCookCmp()
      for _, cookCmp in ipairs(cookCmps) do
        cookCmp:TryRevertCook()
        cookCmp:OnTap()
        local arrItemCodes = cookCmp:GetCurMaterialsArray()
        for _, code in ipairs(arrItemCodes) do
          cookCmp:PutBackMaterial(code, V3Zero)
        end
      end
      local curDay = orderModel:GetCurOrderDay()
      if GM.TestAutoRunModel.stopDay == curDay then
        GM.TestAutoRunModel:StopAutoRun()
        GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, "Tip", "已自动跑完第" .. curDay .. "天", "OK")
      elseif GM.TestAutoRunModel.finishCurrent then
        GM.TestAutoRunModel:StopAutoRun()
        local itemDataModel = GM.ItemDataModel
        local mainBoardModel = GM.MainBoardModel
        local storedCount = GM.MainBoardModel:GetStoredItemCount()
        local index = 1
        while storedCount >= index do
          local item = mainBoardModel:GetStoredItem(index)
          local itemType = item:GetType()
          if not StringUtil.StartWith(itemType, "pd_") and not StringUtil.StartWith(itemType, "eq_") then
            if mainBoardModel:RetrieveStoredItem(index) then
              storedCount = storedCount - 1
            else
              break
            end
          else
            index = index + 1
          end
        end
        GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, "Tip", "当前订单组已自动跑完成", "OK")
      end
    end
  end
  GM.UIManager:OpenView(prefabName, position, scaleX, eBoxRewardType, rewards, title, true, initFunc, function(rewardWindow)
    orderModel:ClaimGroupReward()
    if rewardWindow.m_ribbonSpine then
      rewardWindow.m_ribbonSpine:PlayAnimation("disappear", nil, false, true)
      rewardWindow.m_rainbowSpine:ThrowCallback()
      rewardWindow.m_rainbowSpine:PlayAnimation("disappear", nil, false, true)
    end
    local delay = 0
    if orderModel:HasNextGroup() and not hasImmediateCD then
      rewardWindow.eViewType = EViewType.Other
      delay = 0.6
    elseif hasImmediateCD then
      if ItemSpeeder.effectedItems == nil or next(ItemSpeeder.effectedItems) == nil then
        delay = 3
      else
        delay = 4.4
      end
    end
    DelayExecuteFuncInView(closeFunc, delay, self)
  end)
end

function OrderGroupFinishWindow.TryAddProducerInventoryReward(rewards)
  local rewards = Table.ShallowCopy(rewards)
  local day = GM.MainBoardModel:GetCurOrderDay()
  local InventoryProducerConfig = GM.MainBoardModel:GetProducerInventoryConfig()
  for i = 1, #InventoryProducerConfig do
    if day == InventoryProducerConfig[i].day then
      local customData = {
        [PROPERTY_TYPE] = ProducerInventoryRewardPrefix .. InventoryProducerConfig[i].type,
        [PROPERTY_COUNT] = 1
      }
      RewardApi.CryptOneReward(customData)
      table.insert(rewards, customData)
    end
  end
  return rewards
end
