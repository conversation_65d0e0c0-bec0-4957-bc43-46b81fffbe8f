ChainBundleButton = setmetatable({}, BundleButton)
ChainBundleButton.__index = ChainBundleButton

function ChainBundleButton:_UpdateExclamation()
  if self.m_model ~= nil and self.m_dataGroup ~= nil then
    local bundleData = self.m_model:GetCurStepBundleConfigData(self.m_dataGroup)
    UIUtil.SetActive(self.m_exclamationGo, bundleData ~= nil and bundleData:IsFree())
  else
    UIUtil.SetActive(self.m_exclamationGo, false)
  end
end
