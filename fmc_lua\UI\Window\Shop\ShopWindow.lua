ShopWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  hideHudAnchorType = {
    EHudAnchorType.All
  },
  disableEffectWhenCloseView = true
}, BaseWindow)
ShopWindow.__index = ShopWindow

function ShopWindow:Init(focusShopType)
  self.m_model = GM.ShopModel
  local contentTransform = self.m_contentLayoutGroup.transform
  self.m_spacingHeight = self.m_contentLayoutGroup.spacing
  local sortingOrder = self:GetSortingOrder()
  local container
  self.m_arrContainer = {}
  local mapShopOrderConfig = GM.ShopModel:GetUnlockedOrderConfigs()
  local seq = Table.GetKeys(mapShopOrderConfig)
  table.sort(seq, function(eShopType1, eShopType2)
    return mapShopOrderConfig[eShopType1].Order < mapShopOrderConfig[eShopType2].Order
  end)
  for _, shopType in ipairs(seq) do
    if shopType == EShopType.Bundles then
      local model, config
      for _, bundleType in ipairs(BundleTypeOrderList) do
        model = GM.BundleManager:GetModel(bundleType)
        if model ~= nil and model.GetShopContainerConfig ~= nil then
          config = model:GetShopContainerConfig()
          for _, dataGroup in ipairs(model:GetAllGroupDatas() or {}) do
            if config.checkFun(dataGroup) then
              container = self.m_containerMap:CreateContainer(config.shopType)
              if container ~= nil then
                container:Init(bundleType, dataGroup)
                if container.UpdateSortingOrder then
                  container:UpdateSortingOrder(sortingOrder)
                end
                self.m_arrContainer[#self.m_arrContainer + 1] = container
                container = nil
              end
            end
          end
        end
      end
    else
      container = self.m_containerMap:CreateContainer(shopType)
      if container ~= nil then
        if container.UpdateSortingOrder then
          container:UpdateSortingOrder(sortingOrder)
        end
        self.m_arrContainer[#self.m_arrContainer + 1] = container
        container = nil
      end
    end
  end
  if focusShopType then
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_contentLayoutGroup.transform)
    self:LocateShopType(focusShopType)
  else
    self:SetScrollEnabled(true)
  end
  self:OnShopRefreshed()
  local adjustSizeOffset = ScreenFitter.GetSceneViewSizeOffset()
  self.m_closeBtnGo.transform:SetLocalPosY(self.m_closeBtnGo.transform.localPosition.y - adjustSizeOffset.x)
  local offsetMax = self.m_scrollView.transform.offsetMax
  offsetMax.y = offsetMax.y - adjustSizeOffset.x - adjustSizeOffset.y
  self.m_scrollView.transform.offsetMax = offsetMax
  self:_ToggleHudHighlight(true)
  EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {
    Show = true,
    AnchorTypes = {
      EHudAnchorType.TopLeft
    }
  })
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self.OnShopRefreshed)
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.OnViewWillClose, self, self._OnViewWillClose)
end

function ShopWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  for _, container in ipairs(self.m_arrContainer) do
    if container.OnClose then
      container:OnClose()
    end
  end
  self:_ToggleHudHighlight(false)
  EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {
    Show = false,
    AnchorTypes = {
      EHudAnchorType.TopLeft
    }
  })
end

function ShopWindow:_ToggleHudHighlight(on)
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = on,
    hudKey = ESceneViewHudButtonKey.Coin
  })
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = on,
    hudKey = ESceneViewHudButtonKey.Gem,
    enableBtn = true
  })
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = on,
    hudKey = ESceneViewHudButtonKey.Energy,
    enableBtn = true
  })
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = on,
    hudKey = ESceneViewHudButtonKey.SkipProp,
    enableBtn = true
  })
end

function ShopWindow:_OnOpenView(msg)
  if msg and msg.name == UIPrefabConfigName.RewardWindow then
    self.m_canvasGroup:DOFade(0, 0.3)
    self:_ToggleHudHighlight(false)
  end
end

function ShopWindow:_OnViewWillClose(msg)
  if msg and msg.name == UIPrefabConfigName.RewardWindow then
    self.m_canvasGroup:DOFade(1, 0.3)
    self:_ToggleHudHighlight(true)
  end
end

function ShopWindow:LocateShopType(shopType)
  shopType = shopType or EShopType.Diamonds
  local focusContainer
  local height = 0
  local targetHeight
  local contentHeight = 0
  for _, container in ipairs(self.m_arrContainer) do
    if container:GetShopType() == shopType and targetHeight == nil then
      targetHeight = height
      focusContainer = container
    end
    contentHeight = container:GetContentHeight()
    if 0 < contentHeight then
      height = height + contentHeight + self.m_spacingHeight
    end
  end
  local maxHeight = math.max(0, height + self.m_contentLayoutGroup.padding.top + self.m_contentLayoutGroup.padding.bottom - self.m_spacingHeight - self.m_scrollView.transform.rect.size.y)
  targetHeight = math.min(maxHeight, targetHeight or 0)
  self.m_contentLayoutGroup.transform:DOAnchorPosY(targetHeight, 0.2):SetEase(Ease.OutQuart):OnComplete(function()
    if not self.m_scrollView:IsNull() then
      self:SetScrollEnabled(true)
    end
    focusContainer:ShowFocusEffect()
  end)
end

function ShopWindow:SetScrollEnabled(enable)
  self.m_scrollView.enabled = enable
end

function ShopWindow:RefreshScrollMask()
  self.m_scrollMask.enabled = false
  self.m_scrollMask.enabled = true
end

function ShopWindow:OnShopRefreshed()
end

function ShopWindow:GetDailyFreeGiftCell()
  for _, container in ipairs(self.m_arrContainer) do
    if container:GetShopType() == EShopType.DailyDeals then
      local cell
      for index = 1, container:GetCellCount() do
        cell = container:GetCell(index)
        if cell:GetData().Price == 0 then
          return cell
        end
      end
    end
  end
end

function ShopWindow:OnRestorePurchase()
  GM.InAppPurchaseModel:RestorePurchase(true)
end
