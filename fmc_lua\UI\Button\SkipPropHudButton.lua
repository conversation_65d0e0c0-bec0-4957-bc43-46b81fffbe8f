SkipPropHudButton = setmetatable({
  HitAudio = AudioFileConfigName.SfxSpeedCard
}, HudPropertyButton)
SkipPropHudButton.__index = SkipPropHudButton

function SkipPropHudButton:Awake()
  self.m_initPos = self.m_skipPropHudTrans.anchoredPosition
  HudPropertyButton.Awake(self)
  HudPropertyButton.Init(self, EPropertyType.SkipProp)
  self.m_skipPropHudTrans.anchoredPosition = self.m_initPos + Vector2(0, 300)
  self.m_bShow = false
end

function SkipPropHudButton:_AddListeners()
  if not HudPropertyButton._AddListeners(self) then
    return
  end
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnChangeGameMode)
  EventDispatcher.AddListener(EEventType.ToggleSkipPropHud, self, self._Toggle)
  EventDispatcher.AddListener(EEventType.OpenView, self, self._Hide)
end

function SkipPropHudButton:OnDestroy()
  HudPropertyButton.OnDestroy(self)
  self:_StopTween()
end

function SkipPropHudButton:OnClicked()
  local shopWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.ShopWindow)
  if shopWindow then
    shopWindow:LocateShopType(EShopType.Diamonds)
    return
  end
  GM.UIManager:OpenView(UIPrefabConfigName.ShopWindow, EShopType.Diamonds)
end

function SkipPropHudButton:_OnCloseView()
  HudPropertyButton._OnCloseView(self)
  if GM.UIManager.allWindowClosed and self.m_needShow then
    self:_Show()
  end
end

function SkipPropHudButton:_Toggle(msg)
  if msg then
    self.m_needShow = true
    self:_Show()
  else
    self.m_needShow = false
    self:_Hide()
  end
end

function SkipPropHudButton:_OnChangeGameMode()
  self:_Toggle(false)
end

function SkipPropHudButton:_Show()
  if self.m_bShow then
    return
  end
  self:_StopTween()
  self.m_bShow = true
  self.m_skipTween = self:_GenShowTween(self.m_skipPropHudTrans)
  self.m_gemTween = self:_GenHideTween(self.m_gemHudTrans)
end

function SkipPropHudButton:_Hide()
  if not self.m_bShow then
    return
  end
  self:_StopTween()
  self.m_bShow = false
  self.m_skipTween = self:_GenHideTween(self.m_skipPropHudTrans)
  self.m_gemTween = self:_GenShowTween(self.m_gemHudTrans)
end

function SkipPropHudButton:_GenShowTween(trans)
  local seq = DOTween.Sequence()
  seq:Append(trans:DOAnchorPosY(self.m_initPos.y, 0.3))
  seq:AppendCallback(function()
    self:_StopTween()
  end)
  return seq
end

function SkipPropHudButton:_GenHideTween(trans)
  local seq = DOTween.Sequence()
  seq:Append(trans:DOAnchorPosY(self.m_initPos.y + 300, 0.3))
  seq:AppendCallback(function()
    self:_StopTween()
  end)
  return seq
end

function SkipPropHudButton:_StopTween()
  if self.m_skipTween ~= nil then
    self.m_skipTween:Kill()
    self.m_skipTween = nil
  end
  if self.m_gemTween ~= nil then
    self.m_gemTween:Kill()
    self.m_gemTween = nil
  end
  self.m_skipPropHudTrans.anchoredPosition = self.m_initPos + Vector2(0, self.m_bShow and 0 or 300)
  self.m_gemHudTrans.anchoredPosition = self.m_initPos + Vector2(0, self.m_bShow and 300 or 0)
end

function SkipPropHudButton:OnPropertyAcquired(...)
  HudPropertyButton.OnPropertyAcquired(self, ...)
  self:SyncToModelValue()
end

function SkipPropHudButton:OnPropertyConsumed(...)
  HudPropertyButton.OnPropertyConsumed(self, ...)
  self:SyncToModelValue()
end
