PassActivityBuyTicketWindow = setmetatable({bNeedSetWindowOpened = false, disableEffectWhenCloseView = true}, PassActivityBaseWindow)
PassActivityBuyTicketWindow.__index = PassActivityBuyTicketWindow

function PassActivityBuyTicketWindow:Init(activityType, ext)
  PassActivityBaseWindow.Init(self, activityType, false, ext)
  self.m_rewardBubble:Init(self.m_model:GetVipRewards(true, true), self:GetSortingOrder() + 8)
  self:OnTicketTipButtonClicked()
  local config = self.m_model:GetLevelConfigs()[1]
  self.m_energyNumberText.text = config.vipRewards[1][PROPERTY_COUNT]
  local price = GM.InAppPurchaseModel:GetLocalizedPrice(self.m_model:GetPassTicketIapType())
  self.m_iapButton:Init(price)
  EventDispatcher.AddListener(self.m_activityDefinition.BuyTicketSuccessEvent, self, self._Continue)
end

function PassActivityBuyTicketWindow:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function PassActivityBuyTicketWindow:OnTicketTipButtonClicked()
  self.m_rewardBubble:ShowBubble({
    bRight = true,
    pos = self.m_bubblePosRect.position
  })
end

function PassActivityBuyTicketWindow:OnMaskClicked()
  self.m_rewardBubble:HideBubble()
end

function PassActivityBuyTicketWindow:OnBuyButtonClicked()
  self.m_model:BuyTicket()
end

function PassActivityBuyTicketWindow:_Continue()
  self:Close()
  PassActivityViewHelper.SetViewChain(true)
  GM.UIManager:OpenView(self.m_activityDefinition.BuyTicketSuccessWindow1PrefabName, self.m_activityType)
end

PassActivityBuyTicketPopupWindow = setmetatable({}, PassActivityBaseWindow)
PassActivityBuyTicketPopupWindow.__index = PassActivityBuyTicketPopupWindow

function PassActivityBuyTicketPopupWindow:Init(activityType)
  PassActivityBaseWindow.Init(self, activityType, false)
  self.m_model:SetBuyTicketPopupWindowOpened()
  local vipRewards = {}
  local levelConfigs = self.m_model:GetLevelConfigs()
  local tokenLevel = self.m_model:GetCurrentLevel()
  for level = 1, tokenLevel do
    for _, reward in ipairs(levelConfigs[level].vipRewards) do
      vipRewards[#vipRewards + 1] = reward
    end
  end
  local arrMergedRewards = RewardApi.GetMergedRewards(vipRewards)
  self.m_rewardContent:Init(arrMergedRewards)
  local price = GM.InAppPurchaseModel:GetLocalizedPrice(self.m_model:GetPassTicketIapType())
  self.m_iapButton:Init(price)
  if #arrMergedRewards <= 3 then
    UIUtil.SetSizeDelta(self.m_contentRect, nil, 1022)
    if self.m_maskImg ~= nil then
      self.m_maskImg.enabled = false
    end
  elseif #arrMergedRewards <= 6 then
    UIUtil.SetSizeDelta(self.m_contentRect, nil, 1310)
    if self.m_maskImg ~= nil then
      self.m_maskImg.enabled = false
    end
  end
  EventDispatcher.AddListener(self.m_activityDefinition.BuyTicketSuccessEvent, self, self._Continue)
end

function PassActivityBuyTicketPopupWindow:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function PassActivityBuyTicketPopupWindow:OnCloseBtnClick()
  if self.m_model:GetState() == ActivityState.Started then
    self:Close()
    PassActivityViewHelper.ContinueViewChain(self.m_activityType)
  else
    local giveUpCallback = function(window)
      window:Close()
      if not self.transform:IsNull() then
        self:Close()
        PassActivityViewHelper.ContinueEndChain(self.m_activityType)
      end
    end
    GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, self.m_activityDefinition.TittleTextKey, "battlepass_lastChance", "battlepass_noButton", "battlepass_buyButton", giveUpCallback, nil, false, nil, nil)
  end
end

function PassActivityBuyTicketPopupWindow:OnBuyButtonClicked()
  self.m_model:BuyTicket()
end

function PassActivityBuyTicketPopupWindow:_Continue()
  self:Close()
  if self.m_model:GetState() == ActivityState.Started then
    GM.UIManager:OpenView(self.m_activityDefinition.BuyTicketSuccessWindow1PrefabName, self.m_activityType)
  else
    PassActivityViewHelper.ContinueEndChain(self.m_activityType)
  end
end

PassActivityBuyTicketSuccessWindow1 = setmetatable({
  bNeedSetWindowOpened = false,
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  disableEffectWhenCloseView = true
}, PassActivityBaseWindow)
PassActivityBuyTicketSuccessWindow1.__index = PassActivityBuyTicketSuccessWindow1

function PassActivityBuyTicketSuccessWindow1:Init(activityType)
  PassActivityBaseWindow.Init(self, activityType, false)
  self.m_titleText.text = GM.GameTextModel:GetText("battlepass_itemActivated")
end

function PassActivityBuyTicketSuccessWindow1:OnCloseBtnClick()
  PassActivityBaseWindow.OnCloseBtnClick(self)
  GM.UIManager:OpenView(self.m_activityDefinition.BuyTicketSuccessWindow2PrefabName, self.m_activityType)
end

PassActivityBuyTicketSuccessWindow2 = setmetatable({bNeedSetWindowOpened = false, disableEffectWhenCloseView = true}, PassActivityBaseWindow)
PassActivityBuyTicketSuccessWindow2.__index = PassActivityBuyTicketSuccessWindow2

function PassActivityBuyTicketSuccessWindow2:Init(activityType)
  PassActivityBaseWindow.Init(self, activityType, false)
  local config = self.m_model:GetLevelConfigs()[1]
  self.m_energyNumberText.text = config.vipRewards[1][PROPERTY_COUNT]
end

function PassActivityBuyTicketSuccessWindow2:OnCloseBtnClick()
  PassActivityBaseWindow.OnCloseBtnClick(self)
  PassActivityViewHelper.ContinueViewChain(self.m_activityType)
end

PassActivityBuyTicketSuccessWindow3 = setmetatable({
  bNeedSetWindowOpened = false,
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  disableEffectWhenCloseView = true
}, PassActivityBaseWindow)
PassActivityBuyTicketSuccessWindow3.__index = PassActivityBuyTicketSuccessWindow3

function PassActivityBuyTicketSuccessWindow3:Init(activityType, bMaxTicket)
  PassActivityBaseWindow.Init(self, activityType, false)
  self.m_titleText.text = GM.GameTextModel:GetText("svip_unlocked")
  self.m_bMaxTicket = bMaxTicket
end

function PassActivityBuyTicketSuccessWindow3:OnCloseBtnClick()
  PassActivityBaseWindow.OnCloseBtnClick(self)
  if self.m_bMaxTicket then
    GM.UIManager:OpenView(self.m_activityDefinition.BuyMaxTicketSuccessWindowPrefabName, self.m_activityType)
  else
    GM.UIManager:OpenView(self.m_activityDefinition.BuyUpTicketSuccessWindowPrefabName, self.m_activityType)
  end
end

PassActivityRewardRecoverWindow = setmetatable({bNeedSetWindowOpened = false, bHideHudImmediately = true}, PassActivityBaseWindow)
PassActivityRewardRecoverWindow.__index = PassActivityRewardRecoverWindow

function PassActivityRewardRecoverWindow:Init(activityType, canTakeRewards)
  PassActivityBaseWindow.Init(self, activityType, false)
  local rewards = {}
  for _, canTakeReward in ipairs(canTakeRewards) do
    if canTakeReward.IsExtra then
      self.m_model:TakeExtraReward(canTakeReward.Index, canTakeReward.Rewards)
    else
      self.m_model:TakeReward(canTakeReward.Level, canTakeReward.IsVip, canTakeReward.Rewards)
    end
    for _, reward in ipairs(canTakeReward.Rewards) do
      rewards[#rewards + 1] = reward
    end
  end
  local arrMergedRewards = RewardApi.GetMergedRewards(rewards)
  self.m_rewardContent:Init(arrMergedRewards)
  if #arrMergedRewards <= 3 then
    UIUtil.SetSizeDelta(self.m_contentRect, nil, 1055)
  elseif #arrMergedRewards <= 6 then
    UIUtil.SetSizeDelta(self.m_contentRect, nil, 1345)
  end
end

function PassActivityRewardRecoverWindow:Close()
  PassActivityBaseWindow.Close(self)
  self.m_rewardContent:PlayRewardAnimation()
end
