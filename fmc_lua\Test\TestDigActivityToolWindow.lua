TestDigActivityToolWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestDigActivityToolWindow.__index = TestDigActivityToolWindow
local folderPath = "./Assets/Scripts/LuaRaw/Data/Config"
local levelConfigPath = folderPath .. "/DigLevelsConfig.lua"
local Dropdown = CS.UnityEngine.UI.Dropdown

function TestDigActivityToolWindow:Init()
  self:UpdateContent()
  self:LoadDigItemConfig()
  self:UpdateDigItemsContent()
end

function TestDigActivityToolWindow:InitBoardView(levelId)
  self.m_curLevelId = levelId
  local levelConfig = self.m_mapId2LevelConfig[levelId]
  self.m_arrItemData = {}
  local digItem, itemConfig
  for _, config in pairs(levelConfig.treasures) do
    itemConfig = self.m_mapId2ItemConfig[config.id]
    if itemConfig ~= nil then
      digItem = DigItemData.Create(itemConfig, config.pos, config.rotate)
      table.insert(self.m_arrItemData, digItem)
    else
      Debug.LogError("[DigActivityModel] 不存在此物品的配置 id:" .. (config.id or ""))
    end
  end
  self.m_digActivityBoardView:Init(levelConfig, self.m_arrItemData, self)
  self:UpdateBoardViewSize()
end

function TestDigActivityToolWindow:UpdateTreasuresConfig()
  if self.m_curLevelId == nil then
    return
  end
  local levelConfig = self.m_mapId2LevelConfig[self.m_curLevelId]
  levelConfig.treasures = {}
  for i = 1, #self.m_arrItemData do
    levelConfig.treasures[i] = {}
    levelConfig.treasures[i].id = self.m_arrItemData[i].id
    levelConfig.treasures[i].rotate = self.m_arrItemData[i].rotate
    levelConfig.treasures[i].pos = self.m_arrItemData[i].pos
  end
  levelConfig.mapScale = self.m_digActivityBoardView:GetCurrentScale()
end

function TestDigActivityToolWindow:UpdateContent()
  self:LoadDigLevelsConfig()
  self:UpdateLevelDropdown()
end

function TestDigActivityToolWindow:ClearEnvironment()
  package.loaded["Data.Config.DigLevelsConfig"] = nil
end

function TestDigActivityToolWindow:LoadDigItemConfig()
  self.m_arrDigItemConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.DigItemConfig) or {}
  self.m_mapId2ItemConfig = {}
  for _, config in ipairs(self.m_arrDigItemConfig) do
    config.imgKey = config.id
    self.m_mapId2ItemConfig[config.id] = config
  end
end

function TestDigActivityToolWindow:LoadDigLevelsConfig()
  self:ClearEnvironment()
  local arrDigLevelConfig = require("Data.Config.DigLevelsConfig")
  self.m_arrDigLevelId = {}
  self.m_mapId2LevelConfig = {}
  for _, config in ipairs(arrDigLevelConfig) do
    self.m_mapId2LevelConfig[config.id] = config
    table.insert(self.m_arrDigLevelId, config.id)
  end
  self:SortDigLevel()
end

function TestDigActivityToolWindow:SortDigLevel()
  if not Table.IsEmpty(self.m_arrDigLevelId) then
    table.sort(self.m_arrDigLevelId, function(a, b)
      return a < b
    end)
  end
end

function TestDigActivityToolWindow:CheckItemData()
  if self.m_curLevelId == nil then
    return false
  end
  local levelConfig = self.m_mapId2LevelConfig[self.m_curLevelId]
  local width = levelConfig.width
  local height = levelConfig.height
  local matrix = {}
  for index, itemData in ipairs(self.m_arrItemData) do
    for _, pos in ipairs(itemData:GetOccupancyGrids(true)) do
      if pos.x <= 0 or 0 >= pos.y or width < pos.x or height < pos.y then
        GM.UIManager:ShowPrompt("保存失败，存在宝物超出棋盘范围")
        return false
      end
      matrix[pos.y] = matrix[pos.y] or {}
      if matrix[pos.y][pos.x] == nil then
        matrix[pos.y][pos.x] = true
      else
        GM.UIManager:ShowPrompt("保存失败，宝物之间存在重叠")
        return false
      end
    end
  end
  return true
end

function TestDigActivityToolWindow:ExportDigLevelsConfig(bUpdateConfig)
  if bUpdateConfig then
    if not self:CheckItemData() then
      return
    end
    self:UpdateTreasuresConfig()
  end
  local file = io.open(levelConfigPath, "w+")
  if file == nil then
    Directory.CreateDirectory(folderPath)
    file = io.open(levelConfigPath, "w+")
  end
  local contentStr = ""
  contentStr = contentStr .. [[
return
{
]]
  for _, id in ipairs(self.m_arrDigLevelId) do
    local levelConfig = self.m_mapId2LevelConfig[id]
    contentStr = contentStr .. "\t{\n"
    contentStr = contentStr .. "\t\tid = \"" .. id .. "\",\n"
    contentStr = contentStr .. "\t\twidth = " .. levelConfig.width .. ",\n"
    contentStr = contentStr .. "\t\theight = " .. levelConfig.height .. ",\n"
    contentStr = contentStr .. "\t\tmapScale = " .. levelConfig.mapScale .. ",\n"
    contentStr = contentStr .. "\t\ttreasures = {\n"
    for _, treasure in ipairs(levelConfig.treasures) do
      contentStr = contentStr .. "\t\t\t{\n"
      contentStr = contentStr .. "\t\t\t\tid = \"" .. treasure.id .. "\",\n"
      contentStr = contentStr .. "\t\t\t\tpos = {x = " .. treasure.pos.x .. ", y = " .. treasure.pos.y .. "},\n"
      contentStr = contentStr .. "\t\t\t\trotate = " .. treasure.rotate .. ",\n"
      contentStr = contentStr .. "\t\t\t},\n"
    end
    contentStr = contentStr .. "\t\t},\n"
    contentStr = contentStr .. "\t},\n"
  end
  contentStr = contentStr .. "}"
  file:write(contentStr)
  file:close()
  GM.UIManager:ShowPrompt("文件保存成功")
end

function TestDigActivityToolWindow:UpdateLevelDropdown()
  local arrDropData = {}
  for _, id in pairs(self.m_arrDigLevelId) do
    table.insert(arrDropData, Dropdown.OptionData(id))
  end
  self.m_levelDropdown.options = arrDropData
end

function TestDigActivityToolWindow:UpdateDigItemsContent()
  UIUtil.SetActive(self.m_testDigItemGo, false)
  local go
  for _, itemConfig in pairs(self.m_arrDigItemConfig or {}) do
    go = GameObject.Instantiate(self.m_testDigItemGo, self.m_testDigItemGo.transform.parent)
    go:GetLuaTable():Init(itemConfig, self)
    UIUtil.SetActive(go, true)
  end
end

function TestDigActivityToolWindow:OnCreateLevelBtnClicked()
  local strId = self.m_levelIdInputField.text
  local strSize = self.m_mapSizeInputField.text
  local arrSize = StringUtil.Split(strSize, ",")
  if self.m_mapId2LevelConfig[strId] ~= nil then
    GM.UIManager:ShowPrompt("关卡id: " .. strId .. "已经存在, 不允许重复创建")
  elseif not StringUtil.IsNilOrEmpty(strId) and not StringUtil.IsNilOrEmpty(strSize) and #arrSize == 2 then
    local levelConfig = {
      id = strId,
      width = tonumber(arrSize[1]),
      height = tonumber(arrSize[2]),
      mapScale = 1,
      treasures = {}
    }
    table.insert(self.m_arrDigLevelId, strId)
    self.m_mapId2LevelConfig[strId] = levelConfig
    self:ExportDigLevelsConfig()
    self:UpdateLevelDropdown()
    self:InitBoardView(strId)
    self:SortDigLevel()
  else
    GM.UIManager:ShowPrompt("输入不合法, 关卡id和宽高不能为空, 宽高的格式形如7,9")
  end
end

function TestDigActivityToolWindow:OnDeleteLevelBtnClicked()
  local strId = self.m_levelIdInputField.text
  if not StringUtil.IsNilOrEmpty(strId) and self.m_mapId2LevelConfig[strId] ~= nil then
    Table.ListRemove(self.m_arrDigLevelId, strId)
    self.m_mapId2LevelConfig[strId] = nil
    self:ExportDigLevelsConfig()
    self:UpdateLevelDropdown()
    if strId == self.m_curLevelId then
      self.m_curLevelId = nil
    end
  end
end

function TestDigActivityToolWindow:OnImportBtnClicked()
  if Table.IsEmpty(self.m_arrDigLevelId) then
    GM.UIManager:ShowPrompt("无可导入的关卡, 请先创建关卡")
    return
  end
  local levelId = self.m_levelDropdown.options[self.m_levelDropdown.value].text
  self:InitBoardView(levelId)
  self.m_mapScaleInputField.text = ""
end

function TestDigActivityToolWindow:OnExportBtnClicked()
  if self.m_curLevelId == nil then
    GM.UIManager:ShowPrompt("没有正在编辑的关卡，请先进行导入")
    return
  end
  self:ExportDigLevelsConfig(true)
end

function TestDigActivityToolWindow:OnMapScaleEndEdit()
  if self.m_curLevelId == nil then
    return
  end
  local str = self.m_mapScaleInputField.text
  if not StringUtil.IsNilOrEmpty(str) then
    self.m_digActivityBoardView:SetMapScale(tonumber(str))
  end
end

function TestDigActivityToolWindow:OnTestDigItemCellClicked(digItemConfig)
  if self.m_curLevelId == nil then
    GM.UIManager:ShowPrompt("没有正在编辑的关卡, 请先导入或进行创建")
    return
  end
  self.m_digActivityBoardView:InsertDigItem(DigItemData.Create(digItemConfig, {x = 1, y = 1}, 0))
end

function TestDigActivityToolWindow:UpdateBoardViewSize()
  local maxSizeDelta = self.m_boardViewRootRect.sizeDelta
  local width, height = self.m_digActivityBoardView:GetRealBoardFrameSize()
  local frameSize = 150
  height = height + 200 + frameSize
  width = width + frameSize
  local wScale = math.min(maxSizeDelta.x / width, 1)
  local hScale = math.min(maxSizeDelta.y / height, 1)
  local targetScale = math.min(wScale, hScale)
  UIUtil.SetLocalScale(self.m_boardViewRootRect, targetScale, targetScale)
end

TestDigActivityBoardView = setmetatable({}, DigActivityBoardView)
TestDigActivityBoardView.__index = TestDigActivityBoardView

function TestDigActivityBoardView:Init(levelConfig, arrDigItemData, window)
  self.m_activityDefinition = DigActivityDefinition[ActivityType.TreasureDig]
  self.m_levelConfig = levelConfig
  self.m_arrDigItemData = arrDigItemData
  self.m_window = window
  self:ClearLastBoardView()
  self:SetMapScale()
  self:SetGridLayout(self.m_tileGridLayout, self.m_activityDefinition.GridSize, self.m_levelConfig.width, self.m_activityDefinition.SpacingOffsetX, self.m_activityDefinition.SpacingOffsetY)
  self:SetGridLayout(self.m_digGridLayout, self.m_activityDefinition.GridSize, self.m_levelConfig.width, self.m_activityDefinition.SpacingOffsetX, self.m_activityDefinition.SpacingOffsetY)
  self:UpdateContent()
end

function TestDigActivityBoardView:SetMapScale(scale)
  local targetScale = scale or self.m_levelConfig.mapScale or 1
  UIUtil.SetLocalScale(self.m_scaleRootRect, targetScale, targetScale, 1)
  self.m_curScale = targetScale
  UIUtil.SetSizeDelta(self.m_boardFrameRootRect, self:GetRealBoardFrameSize())
end

function TestDigActivityBoardView:GetRealBoardFrameSize()
  local gridSize = self.m_activityDefinition.GridSize
  local offsetX = self.m_activityDefinition.SpacingOffsetX or 0
  local offsetY = self.m_activityDefinition.SpacingOffsetY or 0
  local resultX = self.m_levelConfig.width * (gridSize + offsetX) * self.m_curScale
  local resultY = self.m_levelConfig.height * (gridSize + offsetY) * self.m_curScale
  return resultX, resultY
end

function TestDigActivityBoardView:GetCurrentScale()
  return self.m_curScale
end

function TestDigActivityBoardView:UpdateItems()
  local arrDigItemData = self.m_arrDigItemData
  local digItemGo
  for i = 1, #arrDigItemData do
    if self.m_arrDigItems[i] == nil then
      digItemGo = GameObject.Instantiate(self.m_digItemGo, self.m_digItemGo.transform.parent)
      self.m_arrDigItems[i] = digItemGo:GetLuaTable()
    end
    self.m_arrDigItems[i]:Init(arrDigItemData[i], self)
    UIUtil.SetActive(self.m_arrDigItems[i].gameObject, true)
  end
end

function TestDigActivityBoardView:InsertDigItem(digItemData)
  local width, height = self:GetMapSize()
  if width < digItemData.width or height < digItemData.height then
    GM.UIManager:ShowPrompt("导入失败，宝物尺寸超过棋盘范围")
    return
  end
  table.insert(self.m_arrDigItemData, digItemData)
  self:UpdateContent()
end

function TestDigActivityBoardView:UpdateTiles()
  local tileImg1 = self.m_activityDefinition.TileDarkImageName
  local tileImg2 = self.m_activityDefinition.TileLightImageName
  local tileGo, imgKey, index
  for y = 1, self.m_levelConfig.height do
    for x = 1, self.m_levelConfig.width do
      index = self:XY2Index(x, y)
      if self.m_arrTiles[index] == nil then
        self.m_arrTiles[index] = GameObject.Instantiate(self.m_tileGo, self.m_tileGo.transform.parent)
      end
      tileGo = self.m_arrTiles[index]
      imgKey = (x + y) % 2 == 0 and tileImg1 or tileImg2
      SpriteUtil.SetImage(tileGo:GetComponent(typeof(Image)), imgKey)
      UIUtil.SetActive(tileGo, true)
    end
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_tileGridLayout.transform)
end

function TestDigActivityBoardView:IsValidGrid(x, y)
  for index, digItemData in ipairs(self.m_arrDigItemData or {}) do
    for _, pos in ipairs(digItemData:GetOccupancyGrids(true)) do
      if x == pos.x and y == pos.y then
        return true
      end
    end
  end
  return false
end

function TestDigActivityBoardView:UpdateDigGrids()
  local digGridGo, index
  for y = 1, self.m_levelConfig.height do
    for x = 1, self.m_levelConfig.width do
      index = self:XY2Index(x, y)
      if self.m_arrDigGrids[index] == nil then
        digGridGo = GameObject.Instantiate(self.m_digGridGo, self.m_digGridLayout.transform)
        UIUtil.SetActive(digGridGo, true)
        self.m_arrDigGrids[index] = digGridGo:GetLuaTable()
        self.m_arrDigGrids[index]:InitForTest()
      end
    end
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_digGridLayout.transform)
end

function TestDigActivityBoardView:UpdateContent()
  self:UpdateTiles()
  self:UpdateDigGrids()
  self:UpdateItems()
end

function TestDigActivityBoardView:GetMapSize()
  return self.m_levelConfig.width, self.m_levelConfig.height
end

function TestDigActivityBoardView:RemoveDigItem(digItemData)
  Table.ListRemove(self.m_arrDigItemData, digItemData)
  self:ClearLastBoardView()
  self:UpdateContent()
end

function TestDigActivityBoardView:GetNearlyGridPos(worldPosition, width, height)
  local minDis = math.maxinteger
  local minIndex, dis, x, y
  local maxW = self.m_levelConfig.width
  local maxH = self.m_levelConfig.height
  for index, tileGo in ipairs(self.m_arrTiles) do
    x, y = self:Index2XY(index)
    if maxW >= x + width - 1 and maxH >= y + height - 1 then
      dis = Vector3.Distance(tileGo.transform.position, worldPosition)
      if minDis > dis then
        minDis = dis
        minIndex = index
      end
    end
  end
  return self:Index2XY(minIndex)
end

function TestDigActivityBoardView:UpdateTilesSprite(pos, width, height)
  self:UpdateTiles()
  local arrGridPos = DigItemData.GetOccupancyGrids({
    width = width,
    height = height,
    pos = pos
  })
  local tileGo
  for _, gridPos in ipairs(arrGridPos) do
    tileGo = self.m_arrTiles[self:XY2Index(gridPos.x, gridPos.y)]
    if tileGo ~= nil then
      SpriteUtil.SetImage(tileGo:GetComponent(typeof(Image)), ImageFileConfigName.game_done_bg)
    end
  end
end

TestDigItemCell = {}
TestDigItemCell.__index = TestDigItemCell

function TestDigItemCell:Init(digItemConfig, window)
  self.m_digItemConfig = digItemConfig
  self.m_window = window
  local num = math.max(digItemConfig.width, digItemConfig.height)
  SpriteUtil.SetImage(self.m_iconImg, digItemConfig.imgKey, true)
  UIUtil.SetLocalScale(self.m_iconImg.transform, 1 / num, 1 / num, 1)
  self.m_sizeText.text = digItemConfig.width .. "x" .. digItemConfig.height
end

function TestDigItemCell:OnBtnClicked()
  if self.m_window ~= nil then
    self.m_window:OnTestDigItemCellClicked(self.m_digItemConfig)
  end
end

TestDigItem = {}
TestDigItem.__index = TestDigItem

function TestDigItem:Init(digItemData, boardView)
  if digItemData == nil or boardView == nil then
    Debug.LogError("DigItemData is nil, please check!")
    return
  end
  self.m_digItemData = digItemData
  self.m_boardView = boardView
  self:UpdateContent()
  
  function self.m_eventTrigger.OnLuaDrag(eventData)
    self:_OnDrag(eventData)
  end
  
  function self.m_eventTrigger.OnLuaPointerUp(eventData)
    self:_OnPointerUp(eventData)
  end
  
  function self.m_eventTrigger.OnLuaPointerDown(eventData)
    self:_OnPointerDown(eventData)
  end
end

function TestDigItem:UpdateContent()
  local digItemData = self.m_digItemData
  local boardView = self.m_boardView
  SpriteUtil.SetImage(self.m_itemImg, digItemData.imgKey, true)
  local gridSize = boardView:GetActivityDefinition().GridSize
  local totalWidth = gridSize * digItemData.width
  local totalHeight = gridSize * digItemData.height
  UIUtil.SetSizeDelta(self.gameObject.transform, totalWidth, totalHeight)
  self.m_itemImg.transform.localRotation = Quaternion.Euler(Vector3(0, 0, digItemData.rotate or 0))
  local anchoredPosition = boardView:GetGridAnchoredPos(digItemData.pos.x, digItemData.pos.y)
  local targetPosX = anchoredPosition.x + (totalWidth / 2 - gridSize / 2)
  local targetPosY = anchoredPosition.y - (totalHeight / 2 - gridSize / 2)
  UIUtil.SetAnchoredPosition(self.gameObject.transform, targetPosX, targetPosY, 0)
  self.m_deltaPosX = -(totalWidth / 2 - gridSize / 2)
  self.m_deltaPosY = totalHeight / 2 - gridSize / 2
  self.m_leftUpRect.anchoredPosition = Vector2(gridSize / 2, -gridSize / 2)
end

function TestDigItem:OnRotateBtnClicked()
  local originRotate = self.m_digItemData.rotate
  self.m_digItemData:UpdateRotate((self.m_digItemData.rotate + 90) % 360)
  local width, height = self.m_boardView:GetMapSize()
  for _, pos in ipairs(self.m_digItemData:GetOccupancyGrids(true)) do
    if pos.x <= 0 or 0 >= pos.y or width < pos.x or height < pos.y then
      self.m_digItemData:UpdateRotate(originRotate)
      GM.UIManager:ShowPrompt("旋转后超出边界，无法在此旋转")
      return
    end
  end
  self.m_boardView:UpdateContent()
  self:UpdateContent()
  self.transform:SetAsLastSibling()
end

function TestDigItem:OnRemoveBtnClicked()
  self.m_boardView:RemoveDigItem(self.m_digItemData)
end

local DirOffset = {
  UP = {x = 0, y = -1},
  Down = {x = 0, y = 1},
  Left = {x = -1, y = 0},
  Right = {x = 1, y = 0}
}

function TestDigItem:UpdateDirButtonState()
  local pos = self.m_digItemData.pos
  local width, height = self.m_boardView:GetMapSize()
  width = width - self.m_digItemData.width + 1
  height = height - self.m_digItemData.height + 1
  local mapDir2BtnGo = {
    UP = self.m_upBtnGo,
    Down = self.m_downBtnGo,
    Left = self.m_leftBtnGo,
    Right = self.m_rightBtnGo
  }
  local bShow, x, y
  for dir, offset in pairs(DirOffset) do
    x = pos.x + offset.x
    y = pos.y + offset.y
    bShow = 1 <= x and width >= x and 1 <= y and height >= y
    UIUtil.SetActive(mapDir2BtnGo[dir], bShow)
  end
end

function TestDigItem:OnUpBtnClicked()
  self:MoveToDir(DirOffset.UP)
end

function TestDigItem:OnDownBtnClicked()
  self:MoveToDir(DirOffset.Down)
end

function TestDigItem:OnLeftBtnClicked()
  self:MoveToDir(DirOffset.Left)
end

function TestDigItem:OnRightBtnClicked()
  self:MoveToDir(DirOffset.Right)
end

function TestDigItem:MoveToDir(dirOffset)
  local originPos = self.m_digItemData.pos
  self.m_digItemData:UpdatePos({
    x = originPos.x + dirOffset.x,
    y = originPos.y + dirOffset.y
  })
  self:UpdateContent()
  self.transform:SetAsLastSibling()
  self.m_boardView:UpdateContent()
end

function TestDigItem:_OnPointerDown(eventData)
  self.m_bStartDrag = true
  self.m_bChangeSibling = false
  self.transform:SetAsLastSibling()
  self.m_curX = nil
  self.m_curY = nil
end

function TestDigItem:_OnDrag(eventData)
  if not self.m_bStartDrag then
    return
  end
  local screenPos = eventData.position
  local worldPosition = PositionUtil.UICameraScreen2World(screenPos)
  local localPosition = self.transform.parent:InverseTransformPoint(worldPosition)
  localPosition.z = 0
  self.transform.localPosition = localPosition
  local x, y
  x, y = self.m_boardView:GetNearlyGridPos(self.m_leftUpRect.position, self.m_digItemData.width, self.m_digItemData.height)
  self.m_boardView:UpdateTilesSprite({x = x, y = y}, self.m_digItemData.width, self.m_digItemData.height)
  self.m_curX = x
  self.m_curY = y
end

function TestDigItem:_OnPointerUp(eventData)
  if self.m_curX == nil or self.m_curY == nil then
    return
  end
  self.m_bStartDrag = false
  self.m_digItemData:UpdatePos({
    x = self.m_curX,
    y = self.m_curY
  })
  self:UpdateContent()
  self.m_boardView:UpdateContent()
  self.m_curX = nil
  self.m_curY = nil
end
