BakeOutPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
BakeOutPopupHelper.__index = BakeOutPopupHelper

function BakeOutPopupHelper.Create()
  local helper = setmetatable({}, BakeOutPopupHelper)
  helper:Init()
  return helper
end

function BakeOutPopupHelper:Init()
  BasePopupHelper.Init(self)
  self.m_arrPopupList = {}
  self.m_model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  AddHandlerAndRecordMap(self.m_model.event, BakeOutEventType.NeedOpenWindow, {
    obj = self,
    method = self._OnNeedOpenWindow
  })
end

function BakeOutPopupHelper:OnDestroy()
  RemoveAllHandlers(self.m_model.event, self)
end

function BakeOutPopupHelper:_OnNeedOpenWindow()
  self:SetNeedCheckPopup(true)
end

function BakeOutPopupHelper:CheckPopup()
  local list = self.m_model:GetWaitingOpenWindowDatas() or Table.Empty
  local openViewName, parameters
  while openViewName == nil and 0 < #list do
    openViewName, parameters = list[1].name, list[1].params
    table.remove(list, 1)
  end
  if 0 < #list then
    self:SetNeedCheckPopup(true)
  end
  return openViewName, parameters
end
