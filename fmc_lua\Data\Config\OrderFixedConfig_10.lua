return {
  {
    Id = "100010",
    GroupId = 1,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "100020",
    GroupId = 1,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_15", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100030",
    GroupId = 1,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6dims_20",
      Count = 1
    }
  },
  {
    Id = "100040",
    GroupId = 1,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "100050",
    GroupId = 1,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100060",
    GroupId = 1,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100070",
    GroupId = 1,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "100080",
    GroupId = 2,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6rice_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_5",
      Count = 1
    }
  },
  {
    Id = "100090",
    GroupId = 2,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100100",
    GroupId = 2,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "100110",
    GroupId = 2,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    }
  },
  {
    Id = "100120",
    GroupId = 2,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4dims_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100130",
    GroupId = 2,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100140",
    GroupId = 2,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "100150",
    GroupId = 3,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e1dims_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100160",
    GroupId = 3,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_3",
      Count = 1
    }
  },
  {
    Id = "100170",
    GroupId = 3,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100180",
    GroupId = 3,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6sf_22",
      Count = 1
    }
  },
  {
    Id = "100190",
    GroupId = 3,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_e4sf_14", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100200",
    GroupId = 3,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_9e6soup_9",
      Count = 1
    }
  },
  {
    Id = "100210",
    GroupId = 3,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_14", Count = 1}
  },
  {
    Id = "100220",
    GroupId = 4,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_6e6preingre_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "100230",
    GroupId = 4,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "100240",
    GroupId = 4,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100250",
    GroupId = 4,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dims_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_10",
      Count = 1
    }
  },
  {
    Id = "100260",
    GroupId = 4,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_9e5saus_25",
      Count = 1
    }
  },
  {
    Id = "100270",
    GroupId = 4,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100280",
    GroupId = 4,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "100290",
    GroupId = 5,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "100300",
    GroupId = 5,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "ds_fd_16", Count = 1}
  },
  {
    Id = "100310",
    GroupId = 5,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e1mdrk_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100320",
    GroupId = 5,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "100330",
    GroupId = 5,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2flb_5",
      Count = 1
    }
  },
  {
    Id = "100340",
    GroupId = 5,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6rice_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100350",
    GroupId = 5,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100360",
    GroupId = 6,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "100370",
    GroupId = 6,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_e4sf_12", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4dims_6",
      Count = 1
    }
  },
  {
    Id = "100380",
    GroupId = 6,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100390",
    GroupId = 6,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    }
  },
  {
    Id = "100400",
    GroupId = 6,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dims_16",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100410",
    GroupId = 6,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100420",
    GroupId = 6,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_5",
      Count = 1
    }
  },
  {
    Id = "100430",
    GroupId = 7,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "100440",
    GroupId = 7,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dims_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "100450",
    GroupId = 7,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100460",
    GroupId = 7,
    ChapterId = 10,
    Requirement_1 = {Type = "it_4_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_10dims_15",
      Count = 1
    }
  },
  {
    Id = "100470",
    GroupId = 7,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_10",
      Count = 1
    }
  },
  {
    Id = "100480",
    GroupId = 7,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100490",
    GroupId = 7,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "100500",
    GroupId = 8,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100510",
    GroupId = 8,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "100520",
    GroupId = 8,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillsf_2",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100530",
    GroupId = 8,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "100540",
    GroupId = 8,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_10dims_18",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100550",
    GroupId = 8,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "100560",
    GroupId = 8,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_10dims_7",
      Count = 1
    }
  },
  {
    Id = "100570",
    GroupId = 9,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100580",
    GroupId = 9,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6dst_9",
      Count = 1
    }
  },
  {
    Id = "100590",
    GroupId = 9,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4dims_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100600",
    GroupId = 9,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "100610",
    GroupId = 9,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_fd_16", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "100620",
    GroupId = 9,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_fd_18", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100630",
    GroupId = 9,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "100640",
    GroupId = 10,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "100650",
    GroupId = 10,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dst_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100660",
    GroupId = 10,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6dst_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100670",
    GroupId = 10,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    }
  },
  {
    Id = "100680",
    GroupId = 10,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e6soup_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6rice_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100690",
    GroupId = 10,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "100700",
    GroupId = 10,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "100710",
    GroupId = 11,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_1",
      Count = 1
    }
  },
  {
    Id = "100720",
    GroupId = 11,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dims_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100730",
    GroupId = 11,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100740",
    GroupId = 11,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100750",
    GroupId = 11,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6dims_20",
      Count = 1
    }
  },
  {
    Id = "100760",
    GroupId = 11,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "100770",
    GroupId = 11,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "100780",
    GroupId = 12,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "100790",
    GroupId = 12,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4dims_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100800",
    GroupId = 12,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6dst_9",
      Count = 1
    }
  },
  {
    Id = "100810",
    GroupId = 12,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e5dims_12",
      Count = 1
    }
  },
  {
    Id = "100820",
    GroupId = 12,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100830",
    GroupId = 12,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4friedmt_8",
      Count = 1
    }
  },
  {
    Id = "100840",
    GroupId = 12,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dst_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100850",
    GroupId = 13,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "100860",
    GroupId = 13,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10dims_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100870",
    GroupId = 13,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6dst_11",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "100880",
    GroupId = 13,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "100890",
    GroupId = 13,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10dims_14",
      Count = 1
    }
  },
  {
    Id = "100900",
    GroupId = 13,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100910",
    GroupId = 13,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_9e6assort_5",
      Count = 1
    }
  },
  {
    Id = "100920",
    GroupId = 14,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "100930",
    GroupId = 14,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100940",
    GroupId = 14,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6rice_11",
      Count = 1
    }
  },
  {
    Id = "100950",
    GroupId = 14,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6rice_9",
      Count = 1
    }
  },
  {
    Id = "100960",
    GroupId = 14,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "100970",
    GroupId = 14,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e6stewmt_1",
      Count = 1
    }
  },
  {
    Id = "100980",
    GroupId = 14,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "100990",
    GroupId = 15,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "101000",
    GroupId = 15,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10dims_22",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101010",
    GroupId = 15,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "101020",
    GroupId = 15,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_10dims_24",
      Count = 1
    }
  },
  {
    Id = "101030",
    GroupId = 15,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "101040",
    GroupId = 15,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101050",
    GroupId = 15,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101060",
    GroupId = 16,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_fd_15", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101070",
    GroupId = 16,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6nibble_8",
      Count = 1
    }
  },
  {
    Id = "101080",
    GroupId = 16,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6rice_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101090",
    GroupId = 16,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "101100",
    GroupId = 16,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10dst_10",
      Count = 1
    }
  },
  {
    Id = "101110",
    GroupId = 16,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6dst_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101120",
    GroupId = 16,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4dims_5",
      Count = 1
    }
  },
  {
    Id = "101130",
    GroupId = 17,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "101140",
    GroupId = 17,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6dst_11",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101150",
    GroupId = 17,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    }
  },
  {
    Id = "101160",
    GroupId = 17,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "101170",
    GroupId = 17,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101180",
    GroupId = 17,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_10e5dims_13",
      Count = 1
    }
  },
  {
    Id = "101190",
    GroupId = 17,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dims_23",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6dst_9",
      Count = 1
    }
  },
  {
    Id = "101200",
    GroupId = 18,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_mixdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10dims_15",
      Count = 1
    }
  },
  {
    Id = "101210",
    GroupId = 18,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dims_25",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101220",
    GroupId = 18,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101230",
    GroupId = 18,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "101240",
    GroupId = 18,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "101250",
    GroupId = 18,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101260",
    GroupId = 18,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillmt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "101270",
    GroupId = 19,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "101280",
    GroupId = 19,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dims_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101290",
    GroupId = 19,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101300",
    GroupId = 19,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4dims_19",
      Count = 1
    }
  },
  {
    Id = "101310",
    GroupId = 19,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101320",
    GroupId = 19,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    }
  },
  {
    Id = "101330",
    GroupId = 19,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6rice_11",
      Count = 1
    }
  },
  {
    Id = "101340",
    GroupId = 20,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101350",
    GroupId = 20,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_8e6nibble_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6rice_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101360",
    GroupId = 20,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "101370",
    GroupId = 20,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "101380",
    GroupId = 20,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101390",
    GroupId = 20,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "101400",
    GroupId = 20,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e1mdrk_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e5dims_17",
      Count = 1
    }
  },
  {
    Id = "101410",
    GroupId = 21,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "101420",
    GroupId = 21,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dst_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6rice_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101430",
    GroupId = 21,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_chopfru_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101440",
    GroupId = 21,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_16",
      Count = 1
    }
  },
  {
    Id = "101450",
    GroupId = 21,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6dims_27",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101460",
    GroupId = 21,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "101470",
    GroupId = 21,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10dims_14",
      Count = 1
    }
  },
  {
    Id = "101480",
    GroupId = 22,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101490",
    GroupId = 22,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_8e6nibble_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101500",
    GroupId = 22,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e4sf_25",
      Count = 1
    }
  },
  {
    Id = "101510",
    GroupId = 22,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "101520",
    GroupId = 22,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101530",
    GroupId = 22,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "101540",
    GroupId = 22,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "101550",
    GroupId = 23,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6soup_9",
      Count = 1
    }
  },
  {
    Id = "101560",
    GroupId = 23,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4dims_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101570",
    GroupId = 23,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6dst_9",
      Count = 1
    }
  },
  {
    Id = "101580",
    GroupId = 23,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6rice_11",
      Count = 1
    }
  },
  {
    Id = "101590",
    GroupId = 23,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6dims_27",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101600",
    GroupId = 23,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dst_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101610",
    GroupId = 23,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    }
  },
  {
    Id = "101620",
    GroupId = 24,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "101630",
    GroupId = 24,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6rice_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101640",
    GroupId = 24,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10dims_14",
      Count = 1
    }
  },
  {
    Id = "101650",
    GroupId = 24,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6dst_8",
      Count = 1
    }
  },
  {
    Id = "101660",
    GroupId = 24,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101670",
    GroupId = 24,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4dims_3",
      Count = 1
    }
  },
  {
    Id = "101680",
    GroupId = 24,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101690",
    GroupId = 25,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101700",
    GroupId = 25,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10dims_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101710",
    GroupId = 25,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "101720",
    GroupId = 25,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_fd_16", Count = 1}
  },
  {
    Id = "101730",
    GroupId = 25,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101740",
    GroupId = 25,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "101750",
    GroupId = 25,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10dims_8",
      Count = 1
    }
  },
  {
    Id = "101760",
    GroupId = 26,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101770",
    GroupId = 26,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_10dims_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101780",
    GroupId = 26,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e1mdrk_13",
      Count = 1
    }
  },
  {
    Id = "101790",
    GroupId = 26,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    }
  },
  {
    Id = "101800",
    GroupId = 26,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101810",
    GroupId = 26,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4dims_2",
      Count = 1
    }
  },
  {
    Id = "101820",
    GroupId = 26,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "101830",
    GroupId = 27,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "101840",
    GroupId = 27,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e1mdrk_11",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101850",
    GroupId = 27,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4sf_25",
      Count = 1
    }
  },
  {
    Id = "101860",
    GroupId = 27,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_4", Count = 1}
  },
  {
    Id = "101870",
    GroupId = 27,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101880",
    GroupId = 27,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "101890",
    GroupId = 27,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10dims_14",
      Count = 1
    }
  },
  {
    Id = "101900",
    GroupId = 28,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "101910",
    GroupId = 28,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_10e5dims_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101920",
    GroupId = 28,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "101930",
    GroupId = 28,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "101940",
    GroupId = 28,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6dst_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e1mdrk_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101950",
    GroupId = 28,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    }
  },
  {
    Id = "101960",
    GroupId = 28,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_e4sf_12", Count = 1},
    Requirement_2 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "101970",
    GroupId = 29,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_9e6assort_5",
      Count = 1
    }
  },
  {
    Id = "101980",
    GroupId = 29,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e4dims_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "101990",
    GroupId = 29,
    ChapterId = 10,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "102000",
    GroupId = 29,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "102010",
    GroupId = 29,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_chopve_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4dims_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "102020",
    GroupId = 29,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_10dst_10",
      Count = 1
    }
  },
  {
    Id = "102030",
    GroupId = 29,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_grillmt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6rice_11",
      Count = 1
    }
  },
  {
    Id = "102040",
    GroupId = 30,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "102050",
    GroupId = 30,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10dims_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "102060",
    GroupId = 30,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6dst_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "102070",
    GroupId = 30,
    ChapterId = 10,
    Requirement_1 = {Type = "ds_fd_14", Count = 1},
    Requirement_2 = {
      Type = "ds_9e6nibble_6",
      Count = 1
    }
  },
  {
    Id = "102080",
    GroupId = 30,
    ChapterId = 10,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_10dims_23",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "102090",
    GroupId = 30,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "102100",
    GroupId = 30,
    ChapterId = 10,
    Requirement_1 = {
      Type = "ds_10e6dst_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e6stewmt_1",
      Count = 1
    }
  }
}
