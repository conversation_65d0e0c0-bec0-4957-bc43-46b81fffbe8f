local EBorder = {
  Up = 1,
  Down = 2,
  Left = 3,
  Right = 4,
  Height = 5
}
TestMapBorderToolView = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow
}, BaseView)
TestMapBorderToolView.__index = TestMapBorderToolView

function TestMapBorderToolView:Init()
  self.m_mapCam, self.m_input = GM.ModeViewController:GetChapterCam()
  self.m_mapCamTrans = self.m_mapCam.gameObject.transform
  self.m_leftLuaTable:Init(self.m_mapCamTrans, self.m_input, true)
  self.m_rightLuaTable:Init(self.m_mapCamTrans, self.m_input, false)
end

function TestMapBorderToolView:Update()
  if not self.m_mapCamTrans then
    return
  end
  local pos = self.m_mapCamTrans.localPosition
  self.m_cameraPosText.text = "(" .. math.floor(pos.x) .. "," .. math.floor(pos.y) .. "," .. math.floor(pos.z) .. ")"
end

function TestMapBorderToolView:OnOkClicked()
  self.m_leftLuaTable:Apply()
  self.m_rightLuaTable:Apply()
end

function TestMapBorderToolView:OnExportClicked()
end

TestMapBorderPanel = {}
TestMapBorderPanel.__index = TestMapBorderPanel

function TestMapBorderPanel:Init(mapCamTrans, input, isNear)
  local trans = self.gameObject.transform
  local prefix = isNear and "max" or "min"
  trans:GetChild(0).gameObject:GetLuaTable():Init("高度" .. (isNear and "minY" or "maxY"), mapCamTrans, input, isNear and "minY" or "maxY", EBorder.Height)
  trans:GetChild(1).gameObject:GetLuaTable():Init("上边界" .. prefix .. "F", mapCamTrans, input, prefix .. "F", EBorder.Up)
  trans:GetChild(2).gameObject:GetLuaTable():Init("下边界" .. prefix .. "B", mapCamTrans, input, prefix .. "B", EBorder.Down)
  trans:GetChild(3).gameObject:GetLuaTable():Init("左边界" .. prefix .. "L", mapCamTrans, input, prefix .. "L", EBorder.Left)
  trans:GetChild(4).gameObject:GetLuaTable():Init("右边界" .. prefix .. "R", mapCamTrans, input, prefix .. "R", EBorder.Right)
end

function TestMapBorderPanel:Apply()
  local trans = self.gameObject.transform
  for i = 0, 4 do
    trans:GetChild(i).gameObject:GetLuaTable():Apply()
  end
end

TestMapBorderCell = {}
TestMapBorderCell.__index = TestMapBorderCell

function TestMapBorderCell:Init(name, mapCamTrans, mapInput, configKey, eBorder)
  self.m_TextText.text = name
  self.m_mapCam = mapCamTrans
  self.m_mapInput = mapInput
  self.m_InputFieldInpu.text = MapInput.m_borders[configKey]
  self.m_orgValue = MapInput.m_borders[configKey]
  self.m_configKey = configKey
  self.m_border = eBorder
end

function TestMapBorderCell:OnGetClicked()
  if self.m_border == EBorder.Up or self.m_border == EBorder.Down then
    self.m_InputFieldInpu.text = math.floor(self.m_mapCam.localPosition.z)
  elseif self.m_border == EBorder.Left or self.m_border == EBorder.Right then
    self.m_InputFieldInpu.text = math.floor(self.m_mapCam.localPosition.x)
  else
    self.m_InputFieldInpu.text = math.floor(self.m_mapCam.localPosition.y)
  end
end

function TestMapBorderCell:OnResetClicked()
  self.m_InputFieldInpu.text = self.m_orgValue
end

function TestMapBorderCell:GetValue()
  return self.m_InputFieldInpu.text
end

function TestMapBorderCell:Apply()
  local value = tonumber(self.m_InputFieldInpu.text)
  if not value then
    GM.UIManager:ShowPrompt("输入错误")
  end
  MapInput.m_borders[self.m_configKey] = value
  self.m_mapInput:_UpdateBorders()
end
