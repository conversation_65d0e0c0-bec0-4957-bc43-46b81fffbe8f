ItemCollectable = setmetatable({}, BaseItemComponent)
ItemCollectable.__index = ItemCollectable

function ItemCollectable.Create(rewardsConfig)
  local itemCollectable = setmetatable({}, ItemCollectable)
  itemCollectable:Init(rewardsConfig)
  return itemCollectable
end

function ItemCollectable:Init(rewardsConfig)
  self.m_rewards = rewardsConfig
end

function ItemCollectable:OnTap()
  local boardModel = self.m_itemModel:GetBoardModel()
  if self.m_rewards[1][PROPERTY_TYPE] ~= LollipopModel.TokenType and self.m_rewards[1][PROPERTY_TYPE] ~= CoconutModel.TokenType and self.m_rewards[1][PROPERTY_TYPE] ~= PinataModel.TokenType then
    GM.PropertyDataManager:Acquire(self.m_rewards, EPropertySource.Give, EBIType.ItemCollect, self:GetGameMode())
  end
  local boardModel = self.m_itemModel:GetBoardModel()
  boardModel:RemoveItem(self.m_itemModel)
  boardModel.event:Call(BoardEventType.CollectItem, {
    Source = self.m_itemModel
  })
  EventDispatcher.DispatchEvent(EEventType.BoardCollect, {
    Rewards = self.m_rewards
  })
  local goldCount
  for _, v in pairs(self.m_rewards) do
    if v[PROPERTY_TYPE] == EPropertyType.Gold then
      goldCount = v[PROPERTY_COUNT]
      EventDispatcher.DispatchEvent(EEventType.CollectGold, goldCount)
    end
  end
  if self.m_itemModel:GetType() == ItemType.gold_5 or self.m_itemModel:GetType() == ItemType.ene_5 then
    GM.OperBIManager:TrackEvent(EOperBIEventType.collect_item)
  end
  PlatformInterface.Vibrate(EVibrationType.Light)
end

function ItemCollectable:GetRewards()
  return self.m_rewards
end
