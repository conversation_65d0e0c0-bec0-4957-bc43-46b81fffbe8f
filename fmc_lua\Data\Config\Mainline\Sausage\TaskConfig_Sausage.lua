return {
  {
    ChapterId = "Sausage",
    Id = 1,
    Cost = 770,
    <PERSON><PERSON>s = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trash", State = 100}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 2,
    StartConditions = {1},
    Cost = 770,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldStove", State = 100}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 3,
    StartConditions = {2},
    Cost = 823,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secFloor", State = 9},
      {
        Slot = "oldSecFloor",
        State = 100
      }
    }
  },
  {
    ChapterId = "Sausage",
    Id = 4,
    StartConditions = {3},
    Cost = 928,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secWall", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 5,
    StartConditions = {4},
    Cost = 664,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stoveWall", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 6,
    StartConditions = {5},
    Cost = 875,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "newStove", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 7,
    StartConditions = {6},
    Cost = 770,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stoveLeft", State = 1}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 8,
    StartConditions = {7},
    Cost = 801,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stoveLeft", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 9,
    StartConditions = {8},
    Cost = 910,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oven", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 10,
    StartConditions = {9},
    Cost = 910,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "grill", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 11,
    StartConditions = {10},
    Cost = 910,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "shelf", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 12,
    StartConditions = {11},
    Cost = 692,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "freezer", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 13,
    StartConditions = {12},
    Cost = 801,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secTable", State = 1}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 14,
    StartConditions = {13},
    Cost = 856,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secTable", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 15,
    StartConditions = {14},
    Cost = 818,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashA", State = 100}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 16,
    StartConditions = {15},
    Cost = 764,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashB", State = 100}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 17,
    StartConditions = {16},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstFloor", State = 9},
      {
        Slot = "oldFirstFloor",
        State = 100
      }
    }
  },
  {
    ChapterId = "Sausage",
    Id = 18,
    StartConditions = {17},
    Cost = 980,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stair", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 19,
    StartConditions = {18},
    Cost = 980,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "innerWall", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 20,
    StartConditions = {19},
    Cost = 818,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftWall", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 21,
    StartConditions = {20},
    Cost = 926,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "bar", State = 1}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 22,
    StartConditions = {21},
    Cost = 920,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "bar", State = 2}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 23,
    StartConditions = {22},
    Cost = 920,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "bar", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 24,
    StartConditions = {23},
    Cost = 863,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldStage", State = 100}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 25,
    StartConditions = {24},
    Cost = 978,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stageWall", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 26,
    StartConditions = {25},
    Cost = 978,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 1}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 27,
    StartConditions = {26},
    Cost = 920,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 28,
    StartConditions = {27},
    Cost = 863,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midChairA", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 29,
    StartConditions = {28},
    Cost = 1020,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "midUtensilA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Sausage",
    Id = 30,
    StartConditions = {29},
    Cost = 953,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midChairB", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 31,
    StartConditions = {30},
    Cost = 1020,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "midUtensilB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Sausage",
    Id = 32,
    StartConditions = {31},
    Cost = 953,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outFloor", State = 9},
      {
        Slot = "oldOutFloor",
        State = 100
      }
    }
  },
  {
    ChapterId = "Sausage",
    Id = 33,
    StartConditions = {32},
    Cost = 886,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashC", State = 100}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 34,
    StartConditions = {33},
    Cost = 886,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftPlant", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 35,
    StartConditions = {34},
    Cost = 1020,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftTableA", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 36,
    StartConditions = {35},
    Cost = 1020,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftTableB", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 37,
    StartConditions = {36},
    Cost = 949,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "umbrella", State = 9},
      {Slot = "umbrellaB", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 38,
    StartConditions = {37},
    Cost = 949,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftLampA", State = 9},
      {Slot = "leftLampAb", State = 9},
      {Slot = "leftLampAc", State = 9},
      {Slot = "leftLampAd", State = 9},
      {Slot = "leftLampAe", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 39,
    StartConditions = {38},
    Cost = 1012,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftLampB", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 40,
    StartConditions = {39},
    Cost = 1138,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftFence", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 41,
    StartConditions = {40},
    Cost = 949,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashD", State = 100}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 42,
    StartConditions = {41},
    Cost = 1075,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldPlantR", State = 100}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 43,
    StartConditions = {42},
    Cost = 1075,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightWall", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 44,
    StartConditions = {43},
    Cost = 1012,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightHouse", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 45,
    StartConditions = {44},
    Cost = 892,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "oldRightFence",
        State = 100
      }
    }
  },
  {
    ChapterId = "Sausage",
    Id = 46,
    StartConditions = {45},
    Cost = 1106,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightTableA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Sausage",
    Id = 47,
    StartConditions = {46},
    Cost = 1034,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightLamp", State = 9},
      {Slot = "rightLampB", State = 9},
      {Slot = "rightLampC", State = 9},
      {Slot = "rightLampD", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 48,
    StartConditions = {47},
    Cost = 1106,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightTableB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Sausage",
    Id = 49,
    StartConditions = {48},
    Cost = 963,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightPlant", State = 9}
    }
  },
  {
    ChapterId = "Sausage",
    Id = 50,
    StartConditions = {49},
    Cost = 1106,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightTableC",
        State = 9
      }
    }
  },
  {
    ChapterId = "Sausage",
    Id = 51,
    StartConditions = {50},
    Cost = 1106,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightTableD",
        State = 9
      }
    }
  },
  {
    ChapterId = "Sausage",
    Id = 52,
    StartConditions = {51},
    Cost = 1248,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 16}
    },
    SlotState = {
      {Slot = "rightFence", State = 9}
    }
  }
}
