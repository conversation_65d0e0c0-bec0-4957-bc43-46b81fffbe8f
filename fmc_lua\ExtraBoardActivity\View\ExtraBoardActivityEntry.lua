ExtraBoardActivityEntry = setmetatable({}, HudPropertyButton)
ExtraBoardActivityEntry.__index = ExtraBoardActivityEntry

function ExtraBoardActivityEntry:Awake()
  self.m_originScale = self.transform.localScale
  self.m_bDisplay = self.gameObject.activeSelf
  HudPropertyButton.Awake(self)
  self:OnGameModeChanged()
  self:_AddListeners()
end

function ExtraBoardActivityEntry:Init(model)
  self.m_model = model
  self.m_activityType = self.m_model:GetType()
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  self.m_boardModel = model:GetBoardModel()
  HudPropertyButton.Init(self)
  self:UpdatePerSecond()
  if self.gameObject.activeInHierarchy then
    self:_AddListeners()
  end
end

function ExtraBoardActivityEntry:GetBoardModel()
  return self.m_model and self.m_model:GetBoardModel()
end

function ExtraBoardActivityEntry:GetPropertyNum()
  return self:GetBoardModel() and self:GetBoardModel():GetCachedItemCount() or 0
end

function ExtraBoardActivityEntry:_AddListeners()
  if self.m_activityDefinition ~= nil then
    EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self.SyncToModelValue, true)
  end
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnGameModeChanged, true)
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self.OnPopCachedItem, true)
end

function ExtraBoardActivityEntry:OnPopCachedItem(msg)
  if msg ~= nil and msg.GameMode == self.m_boardModel:GetGameMode() then
    self:SyncToModelValue()
  end
end

function ExtraBoardActivityEntry:UpdatePerSecond()
  if self.m_model == nil or self.m_model:GetNextStateTime() == nil then
    return
  end
  local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
  self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
end

function ExtraBoardActivityEntry:OnBtnClicked()
  if not self.m_model:HasWindowOpenedOnce(ActivityState.Started) then
    GM.UIManager:OpenView(self.m_activityDefinition.ReadyWindowPrefabName, self.m_activityType, true)
  else
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, true)
  end
end

function ExtraBoardActivityEntry:UpdateValueText()
  if self.m_exclamationText then
    self.m_exclamationText.text = math.floor(self.m_value + 0.5)
  end
  UIUtil.SetActive(self.m_exclamationGo, self.m_value > 0)
end

function ExtraBoardActivityEntry:OnGameModeChanged()
  if HudGeneralButton._NeedDisplay(self) then
    if not self.m_bDisplay then
      self.m_bDisplay = true
      self.transform.localScale = self.m_originScale
    end
  elseif self.m_bDisplay then
    self.m_bDisplay = false
    self.transform.localScale = V3Zero
  end
  self:SyncToModelValue()
end
