local Step = {
  HighlightRewards = "1",
  HighlightTasks = "2",
  HighlightTicket = "3",
  ClickActivate = "4"
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:_GetTutorialText(index)
  local key = string.format(self.m_activityDefinition.TutorialTextKey, index)
  return GM.GameTextModel:GetText(key)
end

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  for _, activityDefinition in pairs(PassActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnPassActivityStateChanged)
  end
end

function Executer:_OnOpenView(message)
  for activityType, activityDefinition in pairs(PassActivityDefinition) do
    if message.name == activityDefinition.MainWindowPrefabName then
      local activityModel = GM.ActivityManager:GetModel(activityType)
      if activityModel:HasTicket() then
        self:_Finish()
      else
        self.m_activityDefinition = activityDefinition
        self.m_activityModel = activityModel
        self:_StepHighlightRewards()
        activityModel:SetWindowOpened(activityDefinition.MainWindowPrefabName)
      end
      return
    end
  end
  if self.m_activityDefinition == nil then
    return
  end
  if message.name == self.m_activityDefinition.NewTimelimitTaskWindowPrefabName or message.name == self.m_activityDefinition.BuyTicketWindowPrefabName or message.name == self.m_activityDefinition.BuyMaxTicketWindowPrefabName or message.name == self.m_activityDefinition.BuyUpTicketWindowPrefabName then
    self:_Finish()
  end
end

function Executer:_OnPassActivityStateChanged()
  if self.m_activityDefinition ~= nil then
    self:_Finish()
  end
end

function Executer:_StepHighlightRewards()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.HighlightRewards
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  GM.UIManager:SetEventLock(true)
  DOVirtual.DelayedCall(1, function()
    GM.UIManager:SetEventLock(false)
    if GM.UIManager:IsViewOpen(self.m_activityDefinition.FinishTaskWindowPrefabName) or GM.UIManager:IsViewOpen(self.m_activityDefinition.NewTimelimitTaskWindowPrefabName) then
      self:_Finish()
      return
    end
    local callback = function()
      self:_StepHighlightTasks()
    end
    local mainWindow = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
    local center, size = mainWindow:GetSecondRewardArea()
    TutorialHelper.UpdateMask(center, size, callback, false)
    local anchorPercent = 50
    TutorialHelper.ShowDialog(self:_GetTutorialText(2), anchorPercent)
  end)
end

function Executer:_StepHighlightTasks()
  self.m_strOngoingDatas = Step.HighlightTasks
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.HideTutorialLayer()
  local callback = function()
    self:_StepHighlightTicket()
  end
  local mainWindow = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
  local center, size = mainWindow:GetTaskArea()
  TutorialHelper.UpdateMask(center, size, callback, false)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText("battlepass_tutorial_3"), 60)
end

function Executer:_StepHighlightTicket()
  self.m_strOngoingDatas = Step.HighlightTicket
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.HideTutorialLayer()
  local callback = function()
    self:_StepClickActivate()
  end
  local mainWindow = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
  if mainWindow.ScrollToFront ~= nil and mainWindow.GetVipTicketTransform ~= nil and mainWindow:GetVipTicketTransform() ~= nil then
    mainWindow:ScrollToFront()
    TutorialHelper.WholeMask(callback)
    DelayExecuteFuncInView(function()
      if GM.TutorialModel:IsTutorialFinished(self.m_tutorialId) then
        return
      end
      self.m_vipTicketRectTrans = mainWindow:GetVipTicketTransform()
      TutorialHelper.HighlightForUI(self.m_vipTicketRectTrans, true)
      TutorialHelper.ShowDialog(GM.GameTextModel:GetText("battlepass_tutorial_4"), 30)
    end, 0.5, mainWindow, true)
  end
end

function Executer:_StepClickActivate()
  if not self.m_activityModel or self.m_activityModel:HasTicket() then
    self:_Finish()
    return
  end
  self.m_strOngoingDatas = Step.ClickActivate
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  if self.m_vipTicketRectTrans ~= nil then
    TutorialHelper.DehighlightForUI(self.m_vipTicketRectTrans)
    self.m_vipTicketRectTrans.gameObject:SetActive(false)
    self.m_vipTicketRectTrans.gameObject:SetActive(true)
  end
  TutorialHelper.HideTutorialLayer()
  TutorialHelper.WholeMask()
  local mainWindow = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
  mainWindow:SetScrollLock(true)
  TutorialHelper.HighlightForUI(mainWindow:GetTicketButtonTransform())
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(mainWindow:GetTicketButtonTransform())
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText("battlepass_tutorial_5"), 50)
end

function Executer:_Finish()
  if self.m_activityDefinition ~= nil then
    local mainWindow = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
    if mainWindow ~= nil then
      TutorialHelper.DehighlightForUI(mainWindow:GetTicketButtonTransform())
      if self.m_vipTicketRectTrans ~= nil then
        TutorialHelper.DehighlightForUI(self.m_vipTicketRectTrans)
      end
      mainWindow:SetScrollLock(false)
    end
  end
  self:Finish(self.m_gesture)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
