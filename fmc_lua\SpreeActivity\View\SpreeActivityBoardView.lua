SpreeActivityBoardView = setmetatable({}, BaseSceneBoardView)
SpreeActivityBoardView.__index = SpreeActivityBoardView

function SpreeActivityBoardView:Init(activityType)
  self.m_activityDefinition = SpreeActivityDefinition[activityType]
  local activityModel = GM.ActivityManager:GetModel(activityType)
  self.Tile1Sprite = self.m_activityDefinition.TileBackground1Name
  self.Tile2Sprite = self.m_activityDefinition.TileBackground2Name
  self.CompleteTileColor = self.m_activityDefinition.CompleteTileColor or self.CompleteTileColor
  BaseSceneBoardView.Init(self, activityModel:GetBoardModel())
  self.m_orderArea:Init(activityType, self)
  SpreeActivityBoardView.s_instance = self
  self.m_activityModel = activityModel
end

function SpreeActivityBoardView.GetInstance()
  return SpreeActivityBoardView.s_instance
end

function SpreeActivityBoardView:OnDestroy()
  BaseSceneBoardView.OnDestroy(self)
  SpreeActivityBoardView.s_instance = nil
end

function SpreeActivityBoardView:_UpdateTile()
  local horizontalTiles = self.m_model:GetHorizontalTiles()
  local verticalTiles = self.m_model:GetVerticalTiles()
  for position, tileSprite in pairs(self.m_tileMap) do
    local tileSpriteName
    if position:GetX() == 1 and position:GetY() == 1 then
      tileSpriteName = self.m_activityDefinition.TileBackgroundCorner1Name
    elseif position:GetX() == horizontalTiles and position:GetY() == 1 then
      tileSpriteName = self.m_activityDefinition.TileBackgroundCorner2Name
    elseif position:GetX() == 1 and position:GetY() == verticalTiles then
      tileSpriteName = self.m_activityDefinition.TileBackgroundCorner3Name
    elseif position:GetX() == horizontalTiles and position:GetY() == verticalTiles then
      tileSpriteName = self.m_activityDefinition.TileBackgroundCorner4Name
    end
    if tileSpriteName == nil then
      tileSpriteName = (position:GetX() + position:GetY()) % 2 == 0 and self.Tile1Sprite or self.Tile2Sprite
    end
    SpriteUtil.SetSpriteRenderer(tileSprite, tileSpriteName)
  end
end
