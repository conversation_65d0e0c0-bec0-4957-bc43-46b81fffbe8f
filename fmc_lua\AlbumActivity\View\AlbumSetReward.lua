AblumSetReward = {}
AblumSetReward.__index = AblumSetReward

function AblumSetReward:Init(formattedReward, model)
  self.m_model = model
  self.m_reward = formattedReward
  self.m_numText.transform.gameObject:SetActive(false)
  SpriteUtil.SetImage(self.m_setIconImg, formattedReward[PROPERTY_TYPE])
end

function AblumSetReward:UpdateContent(formattedReward)
  if self.m_reward == nil or self.m_reward[PROPERTY_TYPE] ~= formattedReward[PROPERTY_TYPE] or self.m_reward[PROPERTY_COUNT] ~= formattedReward[PROPERTY_COUNT] then
    self.m_reward = formattedReward
    SpriteUtil.SetImage(self.m_setIconImg, formattedReward[PROPERTY_TYPE])
  end
end

function AblumSetReward:SetRoation(el)
  self.m_setIconImg.gameObject.transform.localRotation = el
end

function AblumSetReward:ShowCount()
  local count = self.m_reward[PROPERTY_COUNT]
  self.m_numText.transform.gameObject:SetActive(true)
  self.m_numText.text = "+" .. count
end

function AblumSetReward:_OnTip()
  self.m_cardTipLuaTable:Show(self.m_reward[PROPERTY_TYPE], self.m_model)
end

function AblumSetReward:_OnInfoClicked()
  UIHelper.OpenAlbumProbability()
end
