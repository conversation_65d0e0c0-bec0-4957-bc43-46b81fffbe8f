MutiLoopAnimationPlayer = {}
MutiLoopAnimationPlayer.__index = MutiLoopAnimationPlayer

function MutiLoopAnimationPlayer:OnEnable()
  Scheduler.Schedule(self.TickPlay, self, tonumber(self.m_duration), nil, 0)
end

function MutiLoopAnimationPlayer:OnDisable()
  Scheduler.UnscheduleTarget(self)
end

function MutiLoopAnimationPlayer:TickPlay()
  for i = 1, tonumber(self.m_aniCount) do
    self["m_ani_" .. i]:SetTrigger(self["m_aniName_" .. i])
  end
end
