DataBalanceWindow = setmetatable({
  canCloseByAndroidBack = false,
  canClickWindowMask = false,
  canCloseByChangeGameMode = false
}, BaseWindow)
DataBalanceWindow.__index = DataBalanceWindow

function DataBalanceWindow:Init()
  local addCodeCount = GM.DataBalanceModel:Balance()
  if next(addCodeCount) == nil then
    UIUtil.SetSizeDelta(self.m_contentTrans, nil, 650)
    UIUtil.AddAnchoredPosition(self.m_descTrans, 0, -30)
    self.m_itemsGo:SetActive(false)
    return
  end
  local obj, cell
  for item, count in pairs(addCodeCount) do
    obj = Object.Instantiate(self.m_itemCellPrefab, self.m_itemCellRootTrans)
    obj:SetActive(true)
    cell = obj:GetLuaTable()
    cell:Init(item, count)
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_itemCellRootTrans)
  local height = self.m_itemCellRootTrans.sizeDelta.y
  if height < 520 then
    local delta = 520 - height
    UIUtil.AddSizeDelta(self.m_itemsTrans, nil, -delta)
    UIUtil.AddSizeDelta(self.m_contentTrans, nil, -delta)
    self.m_scrollRect.enabled = false
  end
end

DataBalanceCell = {}
DataBalanceCell.__index = DataBalanceCell

function DataBalanceCell:Init(item, count)
  local image = GM.ItemDataModel:GetSpriteName(item)
  SpriteUtil.SetImage(self.m_iconImg, image, true)
  self.m_countText.text = "x" .. count
end
