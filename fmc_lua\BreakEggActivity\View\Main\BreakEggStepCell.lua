BreakEggStepCell = {}
BreakEggStepCell.__index = BreakEggStepCell

function BreakEggStepCell:Init(step)
  self.m_step = step
  self.m_backgroundImage = self.transform:GetComponent(typeof(Image))
  self.m_text.text = step
  self.m_text2.text = step
  self.m_arrLineColor = {
    "30588F",
    "8B1C54",
    "6D34C6"
  }
  local idx = (self.m_step - 1) // 10 + 1
  local color = self.m_arrLineColor[idx] or self.m_arrLineColor[1]
  self.m_text.color = UIUtil.ConvertHexColor2CSColor(color)
  local bgSprite = self["m_bg" .. idx] or self.m_bg1
  self.m_backgroundImage.sprite = bgSprite
end

function BreakEggStepCell:UpdateContent(currentStep)
  self.m_frameGo:SetActive(self.m_step == currentStep)
  self.m_text.gameObject:SetActive(self.m_step == currentStep)
  self.m_text2.gameObject:SetActive(self.m_step ~= currentStep)
end
