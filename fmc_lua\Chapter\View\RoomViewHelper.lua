RoomViewHelper = {}
RoomViewHelper.__index = RoomViewHelper

function RoomViewHelper.PlayPlaceAnimation(go, strAnimationConfig, callback)
  callback = callback or function()
  end
  if go == nil then
    Log.Error("逻辑错误，没有播放动画的载体GameObject！")
    callback()
    return
  end
  local rootTrans = go.transform
  local childCount = rootTrans.childCount
  local seats = go:GetLuaTable()
  local hasSeats = seats and seats.GetSeats
  if hasSeats then
    Log.Assert(seats:GetRoot() == rootTrans:GetChild(childCount - 1), "座位节点必须是最后一个节点！")
  end
  local defaultAnim = {
    trans = rootTrans,
    strAnimationConfig = strAnimationConfig,
    delay = 0
  }
  local animArray
  local stringSplit = StringUtil.Split
  local configArray = stringSplit(strAnimationConfig, ",")
  if #configArray <= 1 then
    animArray = {defaultAnim}
  else
    animArray = {}
    local index, delay, anim, trans, animTypeConfig, animDur
    for i = 1, #configArray, 3 do
      if configArray[i] == nil or configArray[i + 1] == nil or configArray[i + 2] == nil then
        Log.Error(strAnimationConfig .. " 动画配置错误，请资源策划检查！")
        break
      end
      index = tonumber(configArray[i])
      delay = tonumber(configArray[i + 1])
      animTypeConfig = stringSplit(configArray[i + 2], "-")
      anim = animTypeConfig[1]
      animDur = animTypeConfig[2]
      trans = childCount >= index and rootTrans:GetChild(index - 1)
      if not trans then
        Log.Error(strAnimationConfig .. " 配置错误，" .. index .. " 节点不存在，请资源策划检查！")
        break
      end
      animArray[#animArray + 1] = {
        trans = trans,
        eAnimType = anim,
        delay = delay,
        animDur = animDur
      }
    end
    if hasSeats and #animArray ~= childCount - 1 or not hasSeats and #animArray ~= childCount then
      Log.Error(strAnimationConfig .. " 配置错误，与节点数不符，请资源策划检查！")
    end
    if #animArray == 0 then
      animArray = {defaultAnim}
    end
  end
  local count = #animArray
  local func = function()
    count = count - 1
    if count <= 0 then
      callback()
    end
  end
  local oneAnim
  for i = 1, #animArray do
    oneAnim = animArray[i]
    RoomViewHelper._PlayOneTransPlaceAnimation(oneAnim.trans, oneAnim.eAnimType, oneAnim.animDur, oneAnim.delay, func)
  end
end

function RoomViewHelper._PlayOneTransPlaceAnimation(trans, eAnimType, animDur, delay, callback)
  if string.find(eAnimType, EPlaceAnimType.Lay) == 1 then
    local sprite = trans:GetComponentInChildren(typeof(SpriteRenderer))
    if not sprite then
      Log.Error("配置播放 lay 动画，但是没找到 SpriteRenderer 节点！")
      callback()
      return
    end
    trans.gameObject:SetActive(false)
    local seq = DOTween.Sequence()
    seq:AppendInterval(delay)
    seq:AppendCallback(GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.LayEffectMask), trans, Vector3.zero, function(go)
      trans.gameObject:SetActive(true)
      local layEffectMask = go:GetLuaTable()
      layEffectMask:Init(sprite, eAnimType, callback)
    end))
    return
  end
  local originScale = trans.localScale
  local originPos = trans.localPosition
  local seq = DOTween.Sequence()
  if 0 < delay then
    seq:AppendInterval(delay)
  end
  if eAnimType == EPlaceAnimType.Rise then
    trans.localScale = Vector3(0, 0, originScale.z * 1)
    seq:Append(trans:DOScale(Vector3(originScale.x * 1.1, originScale.y * 1.1, originScale.z * 1), 0.4))
    local sprite = trans:GetComponentInChildren(typeof(SpriteRenderer))
    if sprite then
      local height = sprite.localBounds.size.y
      trans.localPosition = Vector3(originPos.x, originPos.y - height / 2, originPos.z)
      local heightDelta = height * 0.1 / 2
      seq:Join(trans:DOLocalMoveY(originPos.y + heightDelta, 0.4))
    else
      Log.Error("配置播放 rise 动画，但是没找到 SpriteRenderer 节点！")
    end
    seq:Append(trans:DOScale(originScale, 0.2))
    seq:Join(trans:DOLocalMoveY(originPos.y, 0.2))
  elseif eAnimType == EPlaceAnimType.Drop then
    trans.localPosition = originPos + Vector3(0, 100, 0)
    trans.localScale = V3Zero
    seq:Append(trans:DOScale(originScale, 0))
    seq:Append(trans:DOLocalMoveY(originPos.y, 0.3):SetEase(Ease.Linear))
    seq:Append(trans:DOScaleY(originScale.y * 0.8, 0.1))
    seq:Append(trans:DOScaleY(originScale.y * 1.1, 0.1))
    seq:Append(trans:DOScaleY(originScale.y * 0.9, 0.1))
    seq:Append(trans:DOScaleY(originScale.y, 0.1))
  elseif eAnimType == EPlaceAnimType.Scale then
    trans.localScale = Vector3(0, 0, originScale.z * 1)
    seq:Append(trans:DOScale(Vector3(originScale.x * 1.1, originScale.y * 1.1, originScale.z * 1), 0.4))
    seq:Append(trans:DOScale(originScale, 0.2))
  elseif string.find(eAnimType, EPlaceAnimType.Stretch) == 1 then
    if string.find(eAnimType, "X") then
      trans.localScale = Vector3(0, originScale.y * 1, originScale.z * 1)
      seq:Append(trans:DOScaleX(originScale.x * 1.1, 0.4))
      seq:Append(trans:DOScaleX(originScale.x, 0.2))
    elseif string.find(eAnimType, "Y") then
      trans.localScale = Vector3(originScale.x * 1, 0, originScale.z * 1)
      seq:Append(trans:DOScaleY(originScale.y * 1.1, 0.4))
      seq:Append(trans:DOScaleY(originScale.y, 0.2))
    end
  elseif eAnimType == EPlaceAnimType.Disappear then
    seq:Append(trans:DOScale(Vector3(originScale.x * 1.2, originScale.y * 1.2, originScale.z * 1), 0.18):SetEase(Ease.OutCirc))
    seq:AppendInterval(0.12)
    seq:Append(trans:DOScale(Vector3.zero, 0.16):SetEase(Ease.InCirc))
  elseif eAnimType == EPlaceAnimType.Disappear2 then
    local openGo, closeGo
    local desOff = Vector3(80, 230, 0)
    local innerSeq = DOTween.Sequence()
    innerSeq:AppendInterval(0.2)
    innerSeq:AppendCallback(function()
      GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.effect_yichu_yanwu_close), trans.parent, desOff, function(go)
        closeGo = go
        closeGo.transform.localScale = Vector3(100, 100, 1)
      end)
    end)
    seq:AppendCallback(function()
      GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.effect_yichu_yanwu_open), trans.parent, Vector3.zero, function(go)
        openGo = go
        openGo.transform.localScale = Vector3(100, 100, 1)
      end)
    end)
    seq:Append(trans:DOLocalMoveY(originPos.y + 80, 0.2))
    seq:Append(trans:DOLocalMoveY(originPos.y + 10, 0.1))
    seq:Append(trans:DOLocalJump(desOff, 3, 1, 0.4))
    seq:Join(trans:DOScale(Vector3(originScale.x * 0.3, originScale.y * 0.3, originScale.z * 1), 0.4):SetEase(Ease.Linear))
    seq:Join(innerSeq)
    seq:Append(trans:DOScale(Vector3.zero, 0))
    seq:AppendInterval(0.5)
    seq:AppendCallback(function()
      trans.localPosition = originPos
      openGo:RemoveSelf()
      closeGo:RemoveSelf()
    end)
  elseif eAnimType == EPlaceAnimType.Show then
    if delay == 0 then
      trans.gameObject:SetActive(true)
    else
      trans.gameObject:SetActive(false)
      seq:AppendCallback(function()
        trans.gameObject:SetActive(true)
      end)
    end
  elseif eAnimType == EPlaceAnimType.Hide then
    if delay == 0 then
      trans.gameObject:SetActive(false)
    else
      trans.gameObject:SetActive(true)
      seq:AppendCallback(function()
        trans.gameObject:SetActive(false)
      end)
    end
  elseif eAnimType == EPlaceAnimType.FadeIn or eAnimType == EPlaceAnimType.FadeOut then
    local spriteRenderers = trans:GetComponentsInChildren(typeof(SpriteRenderer), true)
    if 0 >= spriteRenderers.Length then
      Log.Error("配置播放 fade 动画，但是没找到 SpriteRenderer 节点！")
      callback()
      return
    end
    local startOpacity = eAnimType == EPlaceAnimType.FadeIn and 0 or 1
    local endOpacity = eAnimType == EPlaceAnimType.FadeIn and 1 or 0
    local startColor = CSColor(1, 1, 1, startOpacity)
    spriteRenderers[0].color = startColor
    seq:Append(spriteRenderers[0]:DOFade(endOpacity, animDur or 0.4))
    for i = 1, spriteRenderers.Length - 1 do
      spriteRenderers[i].color = startColor
      seq:Join(spriteRenderers[i]:DOFade(endOpacity, animDur or 0.4))
    end
  else
    Log.Error("动画类型错误 " .. tostring(eAnimType) .. "，请策划检查配置！")
  end
  seq:AppendCallback(callback)
end
