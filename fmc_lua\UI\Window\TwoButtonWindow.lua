TwoButtonWindow = setmetatable({canCloseByAndroidBack = false, needBlurEffect = false}, BaseWindow)
TwoButtonWindow.__index = TwoButtonWindow

function TwoButtonWindow:Init(titleKey, descKey, redButton<PERSON>ey, greenButton<PERSON>ey, redButtonCallback, greenButtonCallback, bWithCloseBtn, initFunc)
  local titleText = self:_GetText(titleKey)
  local descText = self:_GetText(descKey)
  local redButtonText = self:_GetText(redButtonKey)
  local greenButtonText = self:_GetText(greenButtonKey)
  self.m_redButtonKey = redButtonKey
  self:_InitWithText(titleText, descText, redButtonText, greenButtonText, redButtonCallback, greenButtonCallback, bWithCloseBtn, initFunc)
end

function TwoButtonWindow:_InitWithText(titleText, descText, redButtonText, greenButtonText, redButtonCallback, greenButtonCallback, bWithCloseBtn, initFunc)
  self.m_titleText.text = titleText
  self.m_descText.text = descText
  self.m_redText.text = redButtonText
  self.m_greenText.text = greenButtonText
  self:_AdjustSize()
  self.m_redButtonCallback = redButtonCallback
  self.m_greenButtonCallback = greenButtonCallback
  self:SetCloseBtnActive(bWithCloseBtn)
  self.canCloseByAndroidBack = bWithCloseBtn
  if initFunc then
    initFunc(self)
  end
end

function TwoButtonWindow:_AdjustSize()
  local preferredHeight = self.m_descText.preferredHeight
  local defaultTextSizeDelta = self.m_descText.transform.sizeDelta
  if preferredHeight > defaultTextSizeDelta.y then
    local defaultSizeDelta = self.m_contentRect.sizeDelta
    defaultSizeDelta.y = defaultSizeDelta.y + preferredHeight - defaultTextSizeDelta.y
    self.m_contentRect.sizeDelta = defaultSizeDelta
    defaultTextSizeDelta.y = preferredHeight
    self.m_descText.transform.sizeDelta = defaultTextSizeDelta
  end
end

function TwoButtonWindow:_GetText(key)
  if not StringUtil.IsNilOrEmpty(key) then
    return GM.GameTextModel:GetText(key)
  end
end

function TwoButtonWindow:OnRedClick()
  if self.m_redButtonCallback then
    self.m_redButtonCallback(self)
  else
    self:Close()
  end
end

function TwoButtonWindow:OnGreenClick()
  if self.m_greenButtonCallback then
    self.m_greenButtonCallback(self)
  else
    self:Close()
  end
end

function TwoButtonWindow:SetCountDownTimer(timer)
  self.m_countDownTimer = timer + 1
  self.m_redButtonTb:SetEnabled(false)
  self:UpdatePerSecond()
end

function TwoButtonWindow:UpdatePerSecond()
  if self.m_countDownTimer == nil then
    return
  end
  self.m_countDownTimer = self.m_countDownTimer - 1
  local text = GM.GameTextModel:GetText(self.m_redButtonKey)
  if self.m_countDownTimer == 0 then
    self.m_redButtonTb:SetEnabled(true)
    self.m_redButtonTb:SetText(text)
    self.m_countDownTimer = nil
  else
    self.m_redButtonTb:SetText(text .. " (" .. self.m_countDownTimer .. "s)")
  end
end
