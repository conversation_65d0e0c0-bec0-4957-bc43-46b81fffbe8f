local Step = {HighlightCobweb = "1"}
local EStep2TextKey = {
  [Step.HighlightCobweb] = "extraboardweb_guide_1"
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.ExtraBoardCobwebUnlock, self, self._TryStartTutorial)
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnViewOpen)
  for _, v in pairs(ExtraBoardActivityDefinition) do
    EventDispatcher.AddListener(v.StateChangedEvent, self, self._OnActivityStateChanged)
  end
end

function Executer:_OnActivityStateChanged()
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    self:Finish(self.m_gesture)
  end
end

function Executer:_OnViewOpen(msg)
  for _, definition in pairs(ExtraBoardActivityDefinition) do
    if msg.name == definition.MainWindowPrefabName then
      self:_TryStartTutorial()
    end
  end
end

function Executer:_TryStartTutorial()
  local activeModel = ExtraBoardActivityModel.GetActiveModel()
  local boardModel = activeModel and activeModel:GetBoardModel()
  if boardModel ~= nil and GM.UIManager:GetOpenedViewByName(activeModel:GetActivityDefinition().MainWindowPrefabName) ~= nil and boardModel:IsCobwebOpen() and boardModel:HasFinishedAllMainCobweb() and StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    self.m_activityModel = activeModel
    self.m_activityDefinition = activeModel:GetActivityDefinition()
    self:_ExecuteStep1()
  end
end

function Executer:_ExecuteStep1()
  self.m_strOngoingDatas = Step.HighlightCobweb
  self:SetStrongTutorial(true)
  GM.TutorialModel:SetTutorialFinished(self:GetTutorialId())
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local window = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
    local cobwebBgRect = window and window:GetCobwebBgRect()
    if window == nil or cobwebBgRect == nil then
      self:Finish()
      return
    end
    TutorialHelper.UpdateMask(cobwebBgRect.position, cobwebBgRect.sizeDelta, function()
      self:Finish()
    end, false)
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), 55)
  end, 0.5)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
