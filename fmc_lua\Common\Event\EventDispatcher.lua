EventDispatcher = {}
EventDispatcher._listeners = {}
EventDispatcher._WaitingDispatchNewTarget = {}
EventDispatcher._ArrCachedDispatchingEvent = {}

function EventDispatcher.AddListener(eventType, target, listener, override)
  if not target or not listener then
    Log.Error((target == nil and "target is nil" or "listener is nil") .. " for event " .. eventType)
  end
  if not EventDispatcher._listeners[eventType] then
    EventDispatcher._listeners[eventType] = setmetatable({}, {__mode = "k"})
  end
  local wrapper = function(target, message)
    if target and target.gameObject and target.gameObject:IsNull() then
      EventDispatcher.RemoveListener(eventType, target)
      return
    end
    listener(target, message)
  end
  if EventDispatcher._listeners[eventType][target] then
    if override then
      Log.Traceback(eventType .. " 事件监听重复注册，将覆盖已有的回调！")
    else
      Log.Error(eventType .. " 事件监听重复注册，将覆盖已有的回调！")
    end
  end
  EventDispatcher._listeners[eventType][target] = wrapper
  if EventDispatcher.DispatchingEventType == eventType then
    EventDispatcher._WaitingDispatchNewTarget[target] = true
  end
end

function EventDispatcher.AddActiveListener(eventType, target, listener, override)
  Log.Assert(target.gameObject ~= nil, "只能对具有gameObject的实例进行监听")
  local wrapper = function(target, message)
    if target.gameObject.activeInHierarchy then
      listener(target, message)
    end
  end
  EventDispatcher.AddListener(eventType, target, wrapper, override)
end

function EventDispatcher.RemoveListener(eventType, target)
  Log.Assert(target, "EventDispatcher.RemoveListener")
  if EventDispatcher._listeners[eventType] and EventDispatcher._listeners[eventType][target] then
    EventDispatcher._listeners[eventType][target] = nil
  else
    Log.Warning("no matching listener to remove :" .. tostring(eventType))
  end
end

function EventDispatcher.RemoveTarget(target)
  for _, listeners in pairs(EventDispatcher._listeners) do
    if listeners[target] then
      listeners[target] = nil
    end
  end
end

local mapWaitingDispatchTarget = {}

function EventDispatcher.DispatchEvent(eventType, message)
  if EventDispatcher.DispatchingEventType ~= nil then
    EventDispatcher._ArrCachedDispatchingEvent[#EventDispatcher._ArrCachedDispatchingEvent + 1] = {event = eventType, message = message}
    return
  end
  local listeners = EventDispatcher._listeners[eventType]
  if listeners then
    EventDispatcher.DispatchingEventType = eventType
    for target, _ in pairs(listeners) do
      mapWaitingDispatchTarget[target] = true
    end
    for target, _ in pairs(mapWaitingDispatchTarget) do
      SafeCall(listeners[target], nil, target, message)
      mapWaitingDispatchTarget[target] = nil
    end
    for target, _ in pairs(EventDispatcher._WaitingDispatchNewTarget) do
      if listeners[target] then
        SafeCall(listeners[target], nil, target, message)
      end
      EventDispatcher._WaitingDispatchNewTarget[target] = nil
    end
    EventDispatcher.DispatchingEventType = nil
  end
  if #EventDispatcher._ArrCachedDispatchingEvent > 0 then
    local dispatchingData = table.remove(EventDispatcher._ArrCachedDispatchingEvent, 1)
    EventDispatcher.DispatchEvent(dispatchingData.event, dispatchingData.message)
  end
end

function EventDispatcher.Clear()
  if GameConfig.IsTestMode() and not Table.IsEmpty(EventDispatcher._listeners) then
    for eventType, targets in pairs(EventDispatcher._listeners) do
      if not Table.IsEmpty(targets) then
        for target, func in pairs(targets) do
          if target and not target.m_bClosed and target.RemoveEventListener then
            target:RemoveEventListener()
          end
          if target and target.gameObject and not target.gameObject:IsNull() and target.OnDestroy then
            target:OnDestroy()
          end
        end
      end
    end
    local output = {}
    for eventType, targets in pairs(EventDispatcher._listeners) do
      if not Table.IsEmpty(targets) then
        for target, func in pairs(targets) do
          if func then
            local info = debug.getinfo(func)
            local strInfo = info.short_src
            local objName = target and (target.name or target.m_name)
            if objName then
              strInfo = objName .. ", " .. strInfo
            end
            if output[eventType] == nil then
              output[eventType] = {strInfo}
            elseif not Table.Contain(output[eventType], strInfo) then
              table.insert(output[eventType], strInfo)
            end
          end
        end
      end
    end
    if not Table.IsEmpty(output) then
      for eventType, allSrc in pairs(output) do
        for _, src in pairs(allSrc) do
          Log.Error("EventDispatcher. listener not removed. type:" .. eventType .. ", file:" .. src)
        end
      end
    end
  end
  EventDispatcher._listeners = {}
end
