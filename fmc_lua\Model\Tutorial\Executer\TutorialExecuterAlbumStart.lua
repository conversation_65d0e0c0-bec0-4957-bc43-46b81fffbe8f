local Step = {
  ClickButton = "1",
  HighlightReward = "2",
  ClickHelp = "3"
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.AlbumStartButtonClick, self, self._OnBtnClicked)
  for _, activityDefinition in pairs(AlbumActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChange)
  end
end

function Executer:_OnOpenView(msg)
  for _, activityDefinition in pairs(AlbumActivityDefinition) do
    if msg.name == activityDefinition.StartWindowPrefabName then
      local window = GM.UIManager:GetOpenedViewByName(activityDefinition.StartWindowPrefabName)
      if window ~= nil then
        self.m_activityDefinition = activityDefinition
        self.m_startWindow = window
        self:_ExecuteStep1()
      end
    elseif msg.name == activityDefinition.MainWindowPrefabName then
      local window = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
      if window ~= nil then
        self.m_mainWindow = window
        self:_ExecuteStep2()
      end
    elseif msg.name == activityDefinition.HelpWindowPrefabName and self.m_strOngoingDatas == Step.ClickHelp then
      if self.m_helpRect ~= nil and not self.m_helpRect:IsNull() then
        TutorialHelper.DehighlightForUI(self.m_helpRect)
        self.m_helpRect = nil
      end
      self:Finish(self.m_gesture)
    end
  end
end

function Executer:_OnStateChange()
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    self:Finish(self.m_gesture)
  end
end

function Executer:_OnBtnClicked()
  if self.m_strOngoingDatas == Step.ClickButton then
    if self.m_btnRect ~= nil and not self.m_btnRect:IsNull() then
      TutorialHelper.DehighlightForUI(self.m_btnRect)
      self.m_btnRect = nil
    end
    TutorialHelper.HideGesture(self.m_gesture)
    TutorialHelper.HideMask()
    TutorialHelper.HideDialog()
  end
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickButton
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local window = self.m_startWindow
    if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
      return
    end
    local btnRectTrans = window:GetButtonRect()
    self.m_btnRect = btnRectTrans
    TutorialHelper.WholeMask()
    TutorialHelper.HighlightForUI(btnRectTrans)
    self.m_gesture = TutorialHelper.TapOnCustomRectTrans(btnRectTrans)
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText("album_guide1_1"), 50)
    self.m_btnRect = btnRectTrans
  end, 0.2)
end

function Executer:_ExecuteStep2()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.HighlightReward
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local window = self.m_mainWindow
    if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
      return
    end
    local rewardAreaRect = window:GetRewardAreaRect()
    TutorialHelper.UpdateMask(rewardAreaRect.position, rewardAreaRect.sizeDelta, function()
      self:_ExecuteStep3()
    end, false)
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText("album_guide1_2"), 60)
  end, 0.2)
end

function Executer:_ExecuteStep3()
  local window = self.m_mainWindow
  if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
    self:Finish(self.m_gesture)
  end
  self.m_strOngoingDatas = Step.ClickHelp
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local helpRect = self.m_mainWindow:GetHelpBtnTransf()
  if helpRect == nil then
    self:Finish(self.m_gesture)
  end
  self.m_helpRect = helpRect
  TutorialHelper.WholeMask()
  TutorialHelper.HighlightForUI(helpRect)
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(helpRect)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText("album_guide1_3"), 40)
end

function Executer:Finish(gesture, arrow)
  if self.m_btnRect ~= nil and not self.m_btnRect:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_btnRect)
    self.m_btnRect = nil
  end
  if self.m_helpRect ~= nil and not self.m_helpRect:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_helpRect)
    self.m_helpRect = nil
  end
  TutorialExecuter.Finish(self, gesture, arrow)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
