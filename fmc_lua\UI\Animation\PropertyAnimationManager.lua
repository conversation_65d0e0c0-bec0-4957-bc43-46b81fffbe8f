local PROPERTY_COUNT = PROPERTY_COUNT
local PROPERTY_TYPE = PROPERTY_TYPE
local OTHER_TYPES = "Others"
local ImageType = typeof(Image)
local ParticleSystemType = typeof(ParticleSystem)
local V3Zero = V3Zero
local COLLECT_ANIMATION_INTERVAL = 0.3
local SINGLE_FLY_INTERVAL = 0.05
local SINGLE_FLY_INTERVAL_FAST = 0.03
local MAX_FLY_COUNT = 15
PropertyAnimationManager = {
  uiLockFlyingCount = 0,
  mapTween = {}
}
PropertyAnimationManager.__index = PropertyAnimationManager

function PropertyAnimationManager._GetHudButtonTargetKey(propertyType)
  local mainBoardHudKey = SceneViewHud.GetMainBoardHighlightHudKey()
  if propertyType == EPropertyType.Gem then
    return ESceneViewHudButtonKey.Gem
  elseif propertyType == EPropertyType.Gold then
    return ESceneViewHudButtonKey.Coin
  elseif propertyType == EPropertyType.Experience then
    return ESceneViewHudButtonKey.Day
  elseif propertyType == EPropertyType.TaskProgress then
    return ESceneViewHudButtonKey.Task
  elseif propertyType == EPropertyType.EnergyBoost then
    return ESceneViewHudButtonKey.EnergyBoost
  elseif EnergyModel.PropertyType2EnergyType(propertyType) ~= nil or EnergyModel.PropertyType2InfiniteEnergyType(propertyType) ~= nil then
    return ESceneViewHudButtonKey.Energy
  elseif propertyType == EPropertyType.SkipProp then
    if GM.SceneManager:GetGameMode() == EGameMode.Board then
      return ESceneViewHudButtonKey.Inventory
    elseif GM.SceneManager:GetGameMode() == EGameMode.Main then
      return mainBoardHudKey
    end
  elseif GM.ItemDataModel:IsItemExist(propertyType) and GM.SceneManager:GetGameMode() == EGameMode.Main then
    return mainBoardHudKey
  elseif AlbumActivityModel.IsAlbumPackReward(propertyType) and GM.SceneManager:GetGameMode() == EGameMode.Main then
    return mainBoardHudKey
  elseif propertyType == "day" then
    return ESceneViewHudButtonKey.Day
  elseif StringUtil.StartWith(propertyType, ProducerInventoryRewardPrefix) then
    return ESceneViewHudButtonKey.Inventory
  else
    return nil
  end
end

function PropertyAnimationManager.GetButtonTarget(propertyType, flyCount)
  if propertyType == EPropertyType.SkipProp and GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.InventoryWindow) ~= nil then
    return GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.InventoryWindow):GetSkipPropBtn()
  end
  local target
  if propertyType == EPropertyType.BakeOutToken then
    target = BakeOutModel.GetButtonTarget(propertyType)
  end
  if target == nil then
    target = ExtraBoardActivityModel.GetButtonTarget(propertyType)
  end
  local eSceneViewHudButtonKey = PropertyAnimationManager._GetHudButtonTargetKey(propertyType)
  if eSceneViewHudButtonKey ~= nil and target == nil then
    local baseSceneView = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BaseSceneView)
    target = baseSceneView:GetHudButton(eSceneViewHudButtonKey)
  end
  if target == nil then
    target = CoinRaceModel.GetButtonTarget(CoinRaceActivityDefinition, propertyType)
  end
  if target == nil then
    target = PassActivityModel.GetButtonTarget(propertyType)
  end
  if target == nil then
    target = DigActivityModel.GetButtonTarget(propertyType)
  end
  if target == nil then
    target = ProgressActivityModel.GetButtonTarget(ProgressActivityDefinition, propertyType)
  end
  if target == nil then
    target = BlindChestModel.GetButtonTarget(propertyType)
  end
  if target == nil then
    target = PkRaceModel.GetButtonTarget(PkRaceDefinition, propertyType)
  end
  if target == nil then
    target = AlbumActivityModel.GetButtonTarget(propertyType, flyCount)
  end
  return target
end

function PropertyAnimationManager.AddFlyingCount()
  PropertyAnimationManager.uiLockFlyingCount = PropertyAnimationManager.uiLockFlyingCount + 1
end

function PropertyAnimationManager.RemoveFlyingCount()
  PropertyAnimationManager.uiLockFlyingCount = PropertyAnimationManager.uiLockFlyingCount - 1
  if PropertyAnimationManager.uiLockFlyingCount == 0 then
    EventDispatcher.DispatchEvent(EEventType.FlyingElementClear)
  end
end

function PropertyAnimationManager:Awake()
  EventDispatcher.AddListener(EEventType.PlayCollectAnimation, self, self.PlayCollectAnimation)
  EventDispatcher.AddListener(EEventType.PlayPropertyIncreaseAnimation, self, self.PlayPropertyIncreaseAnimation)
  EventDispatcher.AddListener(EEventType.PlayPropertyDecreaseAnimation, self, self.PlayPropertyDecreaseAnimation)
  EventDispatcher.AddListener(EEventType.PlayConsumeAnimation, self, self.PlayConsumeAnimation)
  self.m_mapPropertyFlyCount = {
    [EPropertyType.Gold] = {
      count = {
        2,
        6,
        12,
        25,
        60,
        87,
        math.maxinteger
      },
      flyCount = {
        2,
        5,
        7,
        8,
        9,
        12,
        15
      },
      fastFlyCount = 10
    },
    [EPropertyType.Energy] = {
      count = {
        1,
        2,
        3,
        12,
        30,
        50,
        math.maxinteger
      },
      flyCount = {
        1,
        2,
        3,
        5,
        7,
        8,
        9
      }
    },
    [EPropertyType.Gem] = {
      count = {
        1,
        2,
        6,
        12,
        25,
        math.maxinteger
      },
      flyCount = {
        1,
        2,
        5,
        7,
        8,
        9
      }
    },
    [EPropertyType.TaskProgress] = {
      count = {
        1,
        2,
        6,
        12,
        25,
        math.maxinteger
      },
      flyCount = {
        1,
        2,
        5,
        7,
        8,
        9
      }
    },
    [EPropertyType.EnergyBoost] = {
      count = {
        1,
        2,
        6,
        12,
        25,
        math.maxinteger
      },
      flyCount = {
        1,
        2,
        5,
        7,
        8,
        9
      }
    },
    [EPropertyType.Experience] = {
      count = {
        5,
        15,
        30,
        math.maxinteger
      },
      flyCount = {
        3,
        5,
        7,
        8
      }
    },
    [EPropertyType.SkipProp] = {
      count = {
        4,
        12,
        30,
        50,
        math.maxinteger
      },
      flyCount = {
        3,
        5,
        7,
        8,
        9
      }
    },
    [EPropertyType.CoinRaceToken] = {
      count = {
        1,
        3,
        7,
        15,
        math.maxinteger
      },
      flyCount = {
        1,
        3,
        6,
        8,
        9
      }
    },
    [EPropertyType.PkRaceToken] = {
      count = {
        1,
        3,
        7,
        15,
        math.maxinteger
      },
      flyCount = {
        1,
        3,
        6,
        8,
        9
      }
    },
    [EPropertyType.BakeOutToken] = {
      count = {
        1,
        3,
        7,
        15,
        math.maxinteger
      },
      flyCount = {
        1,
        3,
        6,
        8,
        9
      }
    },
    [EPropertyType.JokerCommonCard] = {
      count = {
        1,
        2,
        3,
        4,
        5,
        math.maxinteger
      },
      flyCount = {
        1,
        2,
        3,
        4,
        5,
        6
      }
    }
  }
  if GM.ConfigModel:UseNewOrderRewardAnimation() then
    self.m_mapPropertyFlyCount[EPropertyType.Gold] = {
      count = {
        2,
        6,
        12,
        25,
        60,
        87,
        math.maxinteger
      },
      flyCount = {
        2,
        3,
        4,
        5,
        6,
        7,
        8
      },
      fastFlyCount = 5
    }
  end
  for _, define in pairs(PassActivityDefinition) do
    if define.ActivityTokenPropertyType then
      self.m_mapPropertyFlyCount[define.ActivityTokenPropertyType] = {
        count = {
          1,
          3,
          7,
          15,
          math.maxinteger
        },
        flyCount = {
          1,
          3,
          6,
          8,
          9
        }
      }
    end
    if define.TicketPropertyType then
      self.m_mapPropertyFlyCount[define.TicketPropertyType] = {
        count = {
          1,
          math.maxinteger
        },
        flyCount = {1}
      }
    end
    if define.PlusTicketPropertyType then
      self.m_mapPropertyFlyCount[define.PlusTicketPropertyType] = {
        count = {
          1,
          math.maxinteger
        },
        flyCount = {1}
      }
    end
  end
  for _, define in pairs(DigActivityDefinition) do
    if define.ActivityTokenPropertyType then
      self.m_mapPropertyFlyCount[define.ActivityTokenPropertyType] = {
        count = {
          1,
          3,
          7,
          15,
          math.maxinteger
        },
        flyCount = {
          1,
          3,
          6,
          8,
          9
        }
      }
    end
  end
  for _, activityDefinition in pairs(ProgressActivityDefinition) do
    self.m_mapPropertyFlyCount[activityDefinition.ActivityTokenPropertyType] = {
      count = {
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        math.maxinteger
      },
      flyCount = {
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        9
      }
    }
  end
  for _, activityDefinition in pairs(BlindChestDefinition) do
    self.m_mapPropertyFlyCount[activityDefinition.ActivityTokenPropertyType] = {
      count = {
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        math.maxinteger
      },
      flyCount = {
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        9
      }
    }
  end
  self.m_mapPrefabConfig = {
    [EPropertyType.Gold] = GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.effect_jb_1),
    [EPropertyType.Gem] = GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.effect_zs_11),
    [EPropertyType.Energy] = GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.effect_tili_1),
    [EPropertyType.EnergyBoost] = GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.FlyElementWithTail),
    [EPropertyType.Experience] = GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.effect_jy_1),
    day = GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.FlyOrderDay),
    pdInventoryItem = GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.InventoryFlyItem)
  }
  self.m_mapflyElementPool = {}
  for k, _ in pairs(self.m_mapPrefabConfig) do
    self.m_mapflyElementPool[k] = {}
  end
  self.m_mapflyElementPool[OTHER_TYPES] = {}
  local mapPreloadPrefabNames = {
    [EPropertyType.Gold] = UIPrefabConfigName.effect_jb_1,
    [EPropertyType.Gem] = UIPrefabConfigName.effect_zs_11,
    [EPropertyType.Energy] = UIPrefabConfigName.effect_tili_1,
    [EPropertyType.Experience] = UIPrefabConfigName.effect_jy_1,
    [EPropertyType.EnergyBoost] = UIPrefabConfigName.FlyElementWithTail,
    [OTHER_TYPES] = UIPrefabConfigName.FlyElement
  }
  for ePropertyType, name in ipairs(mapPreloadPrefabNames) do
    local config = GM.DataResource.UIPrefabConfig:GetConfig(name)
    GM.ResourceLoader:LoadPrefab(config, self.transform, V3Zero, function(gameObject)
      gameObject:SetActive(false)
      self:_RecycleFlyElement(ePropertyType, gameObject)
    end)
  end
end

function PropertyAnimationManager:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

local emptyData = {}

function PropertyAnimationManager:PlayCollectAnimation(message)
  local arrProperties = message.arrProperties
  local uiWorldPos = message.uiWorldPos
  local customDatas = message.customData or emptyData
  for i = 1, #arrProperties do
    local interval = COLLECT_ANIMATION_INTERVAL * (i - 1)
    self:_PlayCollectAnimation(arrProperties[i], uiWorldPos, interval, customDatas[i])
  end
end

function PropertyAnimationManager:_PlayCollectAnimation(property, uiWorldPos, delay, customData)
  customData = customData or emptyData
  local type = property[PROPERTY_TYPE]
  local startLocalPos = uiWorldPos ~= nil and self.gameObject.transform:InverseTransformPoint(uiWorldPos) or V3Zero
  startLocalPos.z = 0
  local flyCount = customData.flyCount or self:_GetFlyCount(property)
  local targetButton = customData.targetButton or self.GetButtonTarget(type, flyCount)
  local countDt = property[PROPERTY_COUNT] / flyCount
  local singleInterval = customData.singleFlyInterval or self:_GetSingleFlyInterval(property)
  local targetWorldPos = customData.endPos
  if RewardApi.IsImmediateEffectItem(type) then
    targetWorldPos = V3Zero
    targetButton = nil
  end
  if targetWorldPos == nil and targetButton ~= nil then
    if targetButton:IsInBoardView() then
      local boardView = BoardViewHelper.GetActiveView()
      if boardView then
        targetWorldPos = PositionUtil.UICameraScreen2World(boardView:ConvertWorldPositionToScreenPosition(targetButton:GetScaleTrans().position))
      else
        targetWorldPos = targetButton:GetFlyTargetPosition()
      end
    elseif targetButton:GetFlyTargetPosition() then
      targetWorldPos = targetButton:GetFlyTargetPosition()
    elseif targetButton.GetScaleTrans then
      targetWorldPos = targetButton:GetScaleTrans().position or targetButton.position
    end
  end
  targetWorldPos = targetWorldPos or V3Zero
  local targetLocalPos = self.gameObject.transform:InverseTransformPoint(targetWorldPos)
  targetLocalPos.z = 0
  delay = (delay or 0) + (customData.flyDelay or 0)
  local playFlyAnimation = function(gameObject, interval)
    local realLocalPos = targetLocalPos
    if customData.toCacheRoot or targetButton ~= nil and targetButton:IsInBoardView() then
      local priority = customData.toCacheRoot and EOrderAreaButtonPriority.BoardCacheRoot or EOrderAreaButtonPriority.Other
      realLocalPos = MainOrderArea.CalculateRealFlyTargetPos(targetLocalPos, priority)
    end
    if self:CanPlayPropertyFlyAnim(type) then
      self:_PlayPropertySingleFlyAnimation(type, gameObject, startLocalPos, realLocalPos, targetButton, countDt, delay, interval, customData)
    else
      local tbFlyEle = gameObject:GetLuaTable()
      if tbFlyEle ~= nil and self.m_mapPrefabConfig[type] == nil then
        tbFlyEle:Init(customData.spriteKey, customData.floatLabel, true)
      end
      self:_PlayElementSingleFlyAnimation(type, gameObject, startLocalPos, realLocalPos, targetButton, delay + interval, customData)
    end
  end
  for i = 1, flyCount do
    if customData.eventLock then
      GM.UIManager:SetEventLock(true)
    end
    self.AddFlyingCount()
    local interval = (i - 1) * singleInterval
    local flyElement = self:_GetCachedFlyElement(type)
    if flyElement then
      playFlyAnimation(flyElement, interval)
    else
      GM.ResourceLoader:LoadPrefab(self:_GetPrefabConfig(type), self.gameObject.transform, startLocalPos, function(go)
        go:SetActive(false)
        go.name = self:_GetPoolType(type)
        playFlyAnimation(go, interval)
      end)
    end
  end
end

function PropertyAnimationManager:CanPlayPropertyFlyAnim(type)
  return self.m_mapPropertyFlyCount[type] ~= nil or StringUtil.StartWith(type, ProducerInventoryRewardPrefix) or ExtraBoardActivityModel.IsExtraBoardActivityItem(type)
end

function PropertyAnimationManager:_ToggleMainBoardButton(toShow, hudKey)
  local arrHudAnchorTypes = {
    EHudAnchorType.BottomRight
  }
  if toShow then
    TutorialHelper.HighlightHudButton(hudKey, true, arrHudAnchorTypes)
  else
    TutorialHelper.DehighlightHudButton(hudKey, arrHudAnchorTypes)
  end
  EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {
    Show = toShow == true,
    AnchorTypes = arrHudAnchorTypes
  })
end

local backLength = 100

function PropertyAnimationManager:_PlayElementSingleFlyAnimation(type, go, startPos, endPos, btn, delay, customData)
  local eMainBoardHudKey
  if btn == self.m_sceneViewHud:GetHudButton(ESceneViewHudButtonKey.MainBoard) then
    eMainBoardHudKey = ESceneViewHudButtonKey.MainBoard
  elseif btn == self.m_sceneViewHud:GetHudButton(ESceneViewHudButtonKey.MainBoardHighlight) then
    eMainBoardHudKey = ESceneViewHudButtonKey.MainBoardHighlight
  end
  if eMainBoardHudKey ~= nil then
    self:_ToggleMainBoardButton(true, eMainBoardHudKey)
  end
  go:SetActive(true)
  local transform = go.transform
  if customData.inverseOrder then
    transform:SetAsFirstSibling()
  else
    transform:SetAsLastSibling()
  end
  transform.localPosition = startPos
  transform.localScale = customData.spriteScale and V3One * customData.spriteScale or V3One * 1.3
  local backOffset = (startPos - endPos).normalized * backLength
  backOffset.z = 0
  if customData.toCacheRoot then
    delay = delay + 0.2
  end
  local sequence = DOTween.Sequence()
  sequence:AppendInterval(delay)
  if customData.toCacheRoot or customData.simpleCacheRoot then
    local ratioScale = BoardCacheRoot.GetCacheElementScale()
    sequence:Append(transform:DOScale(0.7 * ratioScale, 0.1))
    sequence:Append(transform:DOScale(1 * ratioScale, 0.1))
    sequence:Append(transform:DOLocalJump(endPos, 60, 1, 0.5):SetEase(Ease.OutQuad))
    sequence:Insert(delay + 0.2, transform:DOScale(1.5 * ratioScale, 0.3))
    sequence:Insert(delay + 0.5, transform:DOScale(1 * ratioScale, 0.2):SetEase(Ease.OutQuad))
  else
    sequence:Append(transform:DOLocalMove(startPos + backOffset, 0.3):SetEase(Ease.OutCubic))
    sequence:Append(transform:DOLocalMove(endPos, 0.5):SetEase(Ease.InQuad))
    sequence:Join(transform:DOScale(customData.endScale or 1, 0.5):SetEase(Ease.InQuad))
  end
  sequence:OnComplete(function()
    self.mapTween[sequence] = nil
    if RewardApi.IsImmediateEffectItem(type) then
      GM.MainBoardModel.event:Call(BoardEventType.TimeSkip, {
        EffectedItems = ItemSpeeder.effectedItems,
        ItemCode = type
      })
      ItemSpeeder.effectedItems = nil
    end
    if customData.eventLock then
      GM.UIManager:SetEventLock(false)
    end
    self.RemoveFlyingCount()
    if eMainBoardHudKey then
      DelayExecuteFuncInView(function()
        self:_ToggleMainBoardButton(false, eMainBoardHudKey)
      end, 0.5, self)
    end
    if customData.hitAudio then
      GM.AudioModel:PlayEffect(customData.hitAudio)
    end
    go:SetActive(false)
    transform.localScale = V3One
    self:_RecycleFlyElement(type, go)
    if btn ~= nil then
      btn:IconScaleAnimation(true, type)
      if btn.UpdateTextAnimation then
        btn:UpdateTextAnimation(type, 1, customData.alwaysInMiddle)
      end
      if btn.InvokeFlyEndCallback then
        btn:InvokeFlyEndCallback()
      end
    end
  end)
  self._AddTween(sequence)
end

function PropertyAnimationManager:_TogglePropertyButton(ePropertyHudButtonKey, toShow)
  local arrHudAnchorTypes = ePropertyHudButtonKey == ESceneViewHudButtonKey.MainBoard and {
    EHudAnchorType.BottomRight
  } or {
    EHudAnchorType.TopLeft
  }
  if toShow then
    TutorialHelper.HighlightHudButton(ePropertyHudButtonKey, true, arrHudAnchorTypes)
  else
    TutorialHelper.DehighlightHudButton(ePropertyHudButtonKey, arrHudAnchorTypes)
  end
  EventDispatcher.DispatchEvent(EEventType.UpdateSceneViewHud, {
    Show = toShow == true,
    AnchorTypes = arrHudAnchorTypes
  })
end

function PropertyAnimationManager:_PlayPropertySingleFlyAnimation(type, go, startPos, endPos, btn, count, delay, interval, customData, isConsume)
  local particleSystem, particleChild
  if StringUtil.StartWith(type, ProducerInventoryRewardPrefix) then
    go:GetLuaTable():Init(EPropertySprite[type])
  else
    local tbFlyEle = go:GetLuaTable()
    if tbFlyEle ~= nil then
      tbFlyEle:Init(EPropertySprite[type], customData.floatLabel)
      go:SetActive(false)
    end
    if self.m_mapPrefabConfig[type] then
      particleSystem = go:GetComponent(ParticleSystemType)
      if particleSystem and not particleSystem:IsNull() then
        particleSystem:Play()
        local trans = particleSystem.transform
        particleChild = trans.childCount > 0 and trans:GetChild(0) or nil
        if particleChild then
          particleChild.gameObject:SetActive(false)
        end
      else
        particleSystem = nil
      end
    end
  end
  local transform = go.transform
  transform.localPosition = startPos
  transform.localScale = V3Zero
  local updateButtonTextFunc = function()
    if btn ~= nil then
      btn:IconScaleAnimation(not isConsume, type)
      if btn.UpdateTextAnimation ~= nil then
        btn:UpdateTextAnimation(type, count, customData.alwaysInMiddle)
      end
      if btn.InvokeFlyEndCallback then
        btn:InvokeFlyEndCallback()
      end
    end
  end
  local eHudButtonKey = self._GetHudButtonTargetKey(type)
  local toHudBtn = eHudButtonKey and self.m_sceneViewHud:GetHudButton(eHudButtonKey) == btn
  if toHudBtn then
    self:_TogglePropertyButton(eHudButtonKey, true)
  end
  local sequence = DOTween.Sequence()
  sequence:AppendInterval(delay)
  if not isConsume then
    sequence:AppendInterval(interval)
  end
  sequence:AppendCallback(function()
    if isConsume then
      updateButtonTextFunc()
      transform.localScale = Vector3(1.2, 1.2, 1)
    end
    if not go:IsNull() then
      go:SetActive(true)
    end
    if particleSystem ~= nil then
      particleSystem:Play()
    end
  end)
  local flyFromRight = false
  local floatDt = 0.5
  if not isConsume and not customData.noDiffusion then
    local posOffset
    if customData.floatUp then
      posOffset = Vector3(0, 150, 0)
    elseif customData.floatLarge then
      posOffset = self:_GetLargeRandomPosOffset()
    else
      posOffset = self:_GetPropertyRandomPosOffset()
    end
    flyFromRight = 0 < posOffset.x
    startPos = startPos + posOffset
    sequence:Append(transform:DOLocalMove(startPos, floatDt):SetEase(Ease.OutSine))
  end
  sequence:Join(transform:DOScale(customData.flyStartScale or 1, floatDt):SetEase(Ease.OutSine))
  sequence:AppendInterval(customData.flyPause or 0)
  local v2StartPos = Vector2(startPos.x, startPos.y)
  local v2EndPos = Vector2(endPos.x - 10, endPos.y)
  local midPos = (v2StartPos + v2EndPos) / 2
  local verticalDirection = Vector2(v2EndPos.y - v2StartPos.y, v2StartPos.x - v2EndPos.x)
  local length = math.sqrt(verticalDirection.x ^ 2 + verticalDirection.y ^ 2)
  verticalDirection = Vector2(verticalDirection.x / length, verticalDirection.y / length)
  local midShift = verticalDirection * math.random(isConsume and 170 or 70)
  local config = {
    control1 = midPos + midShift * (flyFromRight and 1 or -1),
    control2 = midPos + midShift * (flyFromRight and 1 or -1),
    to = v2EndPos,
    from = v2StartPos,
    posType = BezierPosType.Local,
    easeType = BezierEaseType.In
  }
  local bezier = go:GetComponent(typeof(CS.BezierMove2D))
  if bezier == nil or bezier:IsNull() then
    Log.Error(tostring(type) .. " 的 prefab 没有挂载 BezierMove2D 脚本")
  end
  sequence:AppendCallback(function()
    if particleChild then
      particleChild.gameObject:SetActive(true)
      local trail = go:GetComponentInChildren(typeof(TrailRenderer))
      if trail and not trail:IsNull() then
        trail:Clear()
      end
    end
    bezier:MoveTo(config, 0.25 + interval)
  end)
  sequence:Append(transform:DOScale(Vector3.one * 1.3, (0.25 + interval) * 0.5):SetEase(Ease.OutSine))
  sequence:Append(transform:DOScale(customData.endScale or Vector3.one, (0.25 + interval) * 0.5):SetEase(Ease.InSine))
  sequence:OnComplete(function()
    self.mapTween[sequence] = nil
    if customData.eventLock then
      GM.UIManager:SetEventLock(false)
    end
    self.RemoveFlyingCount()
    if toHudBtn then
      self:_TogglePropertyButton(eHudButtonKey, false)
    end
    if customData.hitAudio then
      GM.AudioModel:PlayEffect(customData.hitAudio)
    elseif type == EPropertyType.SkipProp then
      GM.AudioModel:PlayEffect(AudioFileConfigName.SfxSpeedCard)
    end
    if not go:IsNull() then
      go:SetActive(false)
      self:_RecycleFlyElement(type, go)
      if not isConsume then
        updateButtonTextFunc()
      end
      if particleSystem then
        particleSystem:Stop()
      end
    end
  end)
  self._AddTween(sequence)
end

function PropertyAnimationManager._AddTween(t)
  PropertyAnimationManager.mapTween[t] = true
end

function PropertyAnimationManager.FinishAllTweens()
  local arrTweens = Table.GetKeys(PropertyAnimationManager.mapTween)
  for _, tween in ipairs(arrTweens) do
    tween:Complete(true)
  end
end

function PropertyAnimationManager:PlayConsumeAnimation(message)
  local property = message.property
  local type = property[PROPERTY_TYPE]
  local uiWorldPos = message.uiWorldPos
  local customData = message.customData or emptyData
  local startButton = customData.startButton or self.GetButtonTarget(type)
  local flyCount = 4
  local countDt = -property[PROPERTY_COUNT] / flyCount
  local startWorldPos = startButton.iconTrans.position
  local startLocalPos = self.gameObject.transform:InverseTransformPoint(startWorldPos)
  local playFlyAnimation = function(gameObject, delay, interval)
    self:_PlayPropertySingleFlyAnimation(type, gameObject, startLocalPos, uiWorldPos, startButton, countDt, delay, interval, customData, true)
  end
  for i = 1, flyCount do
    if customData.eventLock then
      GM.UIManager:SetEventLock(true)
    end
    self.AddFlyingCount()
    local delay = (i - 1) * 0.1
    local interval = (i - 1) * 0.08
    local flyElement = self:_GetCachedFlyElement(type)
    if flyElement then
      playFlyAnimation(flyElement, delay, interval)
    else
      GM.ResourceLoader:LoadPrefab(self:_GetPrefabConfig(type), self.gameObject.transform, startLocalPos, function(go)
        go:SetActive(false)
        go.name = self:_GetPoolType(type)
        playFlyAnimation(go, delay, interval)
      end)
    end
  end
end

function PropertyAnimationManager:_GetFlyCount(property)
  local flyCountData = self.m_mapPropertyFlyCount[property[PROPERTY_TYPE]]
  if flyCountData ~= nil then
    local count = property[PROPERTY_COUNT]
    for i, v in ipairs(flyCountData.count) do
      if v >= count then
        return flyCountData.flyCount[i]
      end
    end
  end
  return math.min(MAX_FLY_COUNT, property[PROPERTY_COUNT])
end

function PropertyAnimationManager:_GetSingleFlyInterval(property)
  local flyCountData = self.m_mapPropertyFlyCount[property[PROPERTY_TYPE]]
  if flyCountData ~= nil and flyCountData.fastFlyCount ~= nil then
    return self:_GetFlyCount(property) >= flyCountData.fastFlyCount and SINGLE_FLY_INTERVAL_FAST or SINGLE_FLY_INTERVAL
  end
  return SINGLE_FLY_INTERVAL
end

function PropertyAnimationManager:_GetPropertyRandomPosOffset()
  return Vector3(math.random(10000) * 200 / 10000 - 100, math.random(10000) * 100 / 10000 - 100, 0)
end

function PropertyAnimationManager:_GetLargeRandomPosOffset()
  local randomX
  if math.random(2) == 1 then
    randomX = math.random(10000) * 20 / 10000 - 100
  else
    randomX = math.random(10000) * 20 / 10000 + 80
  end
  local randomY = math.random(10000) * 20 / 10000 - 100
  return Vector3(randomX, randomY, 0)
end

function PropertyAnimationManager:PlayPropertyIncreaseAnimation(message)
  local property = message.property
  local button = self.GetButtonTarget(property[PROPERTY_TYPE])
  button:IconScaleAnimation(false, property[PROPERTY_TYPE])
  button:UpdateTextAnimation(property[PROPERTY_TYPE], property[PROPERTY_COUNT], message.customData.alwaysInMiddle)
end

function PropertyAnimationManager:PlayPropertyDecreaseAnimation(message)
  local property = message.property
  local button = self.GetButtonTarget(property[PROPERTY_TYPE])
  if button ~= nil then
    if message.ext ~= nil and message.ext.noAnim then
      button:SyncToModelValue()
    else
      button:UpdateTextAnimation(property[PROPERTY_TYPE], -property[PROPERTY_COUNT])
    end
  end
end

function PropertyAnimationManager.PlayFlyElementAnimation(itemType, moveDuration, fromPosition, targetPosition, fromScale, targetScale, bLockEvent, withTail, sortingOrder, onStart, callback, ease)
  if bLockEvent then
    GM.UIManager:SetEventLock(true)
  end
  PropertyAnimationManager.AddFlyingCount()
  local prefabName = withTail and UIPrefabConfigName.FlyElementWithTail or UIPrefabConfigName.FlyElement
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(prefabName), GM.UIManager:GetCanvasRoot(), fromPosition, function(go)
    if not go:IsNull() then
      if onStart then
        onStart()
      end
      if sortingOrder then
        local canvas = go:AddComponent(typeof(CS.UnityEngine.Canvas))
        canvas.overrideSorting = true
        canvas.sortingOrder = sortingOrder
      end
      local spriteName = GM.ItemDataModel:GetSpriteName(itemType)
      local flyTb = go:GetLuaTable()
      flyTb:Init(spriteName, nil, true)
      local transform = go.transform
      UIUtil.SetLocalPosition(transform, nil, nil, 0)
      transform.localScale = V3One * (fromScale or 0)
      local sequence = DOTween.Sequence()
      sequence:Insert(0, transform:DOMove(targetPosition, moveDuration):SetEase(ease or Ease.InQuad))
      sequence:Insert(0, transform:DOScale(1.8, moveDuration * 0.7))
      sequence:Insert(moveDuration * 0.7, transform:DOScale(targetScale or 1, moveDuration * 0.3))
      sequence:AppendCallback(function()
        if bLockEvent then
          GM.UIManager:SetEventLock(false)
        end
        AddressableLoader.Destroy(go)
        PropertyAnimationManager.RemoveFlyingCount()
        PlatformInterface.Vibrate(EVibrationType.Light)
        if callback then
          callback()
        end
      end)
    elseif bLockEvent then
      GM.UIManager:SetEventLock(false)
      if callback then
        callback()
      end
    end
  end)
end

function PropertyAnimationManager:_GetPrefabConfig(type)
  if StringUtil.StartWith(type, ProducerInventoryRewardPrefix) then
    return self.m_mapPrefabConfig.pdInventoryItem
  elseif self.m_mapPrefabConfig[type] ~= nil then
    return self.m_mapPrefabConfig[type]
  else
    return GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.FlyElement)
  end
end

function PropertyAnimationManager:_GetCachedFlyElement(type)
  type = self:_GetPoolType(type)
  local count = #self.m_mapflyElementPool[type]
  if 0 < count then
    local element = self.m_mapflyElementPool[type][count]
    self.m_mapflyElementPool[type][count] = nil
    return element
  end
end

function PropertyAnimationManager:_GetCachedImage()
  local count = #self.m_cachedImagePool
  if 0 < count then
    local image = self.m_cachedImagePool[count]
    self.m_cachedImagePool[count] = nil
    return image
  end
end

function PropertyAnimationManager:_GetPoolType(type)
  if StringUtil.StartWith(type, ProducerInventoryRewardPrefix) then
    return "pdInventoryItem"
  end
  return self.m_mapflyElementPool[type] ~= nil and type or OTHER_TYPES
end

function PropertyAnimationManager:_RecycleFlyElement(type, obj)
  type = self:_GetPoolType(type)
  self.m_mapflyElementPool[type][#self.m_mapflyElementPool[type] + 1] = obj
end
