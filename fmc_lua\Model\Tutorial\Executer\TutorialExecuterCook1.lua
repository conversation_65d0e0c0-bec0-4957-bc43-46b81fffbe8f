local Step = {
  ClickCache = "1",
  Merge = "2",
  HighlightOrder = "3"
}
local EStep2TextKey = {
  [Step.ClickCache] = "tutorial_equipment_1",
  [Step.Merge] = "tutorial_equipment_2",
  [Step.HighlightOrder] = "tutorial_equipment_3"
}
local EStep2TextAnchorPercent = {
  [Step.ClickCache] = 40,
  [Step.HighlightOrder] = 40
}
local itemId = "eq_1_3"
local mergedItemId = "eq_1_4"
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self._OnPopCachedItem)
  EventDispatcher.AddListener(EEventType.ItemMerged, self, self._OnItemMerged)
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnViewOpened)
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  if (StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) or self.m_strOngoingDatas == Step.ClickCache) and self:_TryExecuteStep1() then
    return true
  elseif (StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) or self.m_strOngoingDatas == Step.Merge) and self:_TryExecuteStep2() then
    return true
  elseif (StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) or self.m_strOngoingDatas == Step.HighlightOrder) and self:_TryExecuteStep3() then
    return true
  end
end

function Executer:_OnPopCachedItem(msg)
  if self.m_strOngoingDatas == Step.ClickCache and self.m_gesture and msg.GameMode == EGameMode.Board then
    TutorialHelper.DehighlightCacheRoot()
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    if not self:_TryExecuteStep2() then
      self:Finish(self.m_gesture)
    end
  end
end

function Executer:_OnItemMerged()
  if self.m_strOngoingDatas == Step.Merge and self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
    self:LogTutorialStepFinish(self.m_strOngoingDatas)
    if not self:_TryExecuteStep3() then
      self:Finish(self.m_gesture)
    end
  end
end

function Executer:_OnViewOpened(msg)
  if self.m_strOngoingDatas == Step.HighlightOrder and (msg.name == UIPrefabConfigName.ItemDetailWindow or msg.name == UIPrefabConfigName.ItemDishDetailWindow) and self.m_orderCell then
    GM.UIManager:SetEventLock(true)
    DelayExecuteFunc(function()
      GM.UIManager:SetEventLock(false)
      GM.UIManager:OpenView(UIPrefabConfigName.HowToCookWindow)
    end, 0.5)
    TutorialHelper.DehighlightOrder(self.m_orderCell)
    self:Finish(self.m_gesture)
  end
end

function Executer:_TryExecuteStep1()
  local topCachedItemType = GM.MainBoardModel:GetCachedItem(1)
  if topCachedItemType == nil or topCachedItemType ~= itemId then
    return false
  end
  local boardView = BoardViewHelper.GetActiveView()
  if boardView:GetOrderArea():GetBoardCacheRoot():GetDisplayItem() ~= topCachedItemType or boardView:GetOrderArea():GetBoardCacheRoot():IsPlayingAnimation() or not boardView:GetOrderArea():GetBoardCacheRoot():IsShowing() then
    return false
  end
  local isBoardFullofItems = GM.MainBoardModel:FindEmptyPositionInValidOrder() == nil
  if isBoardFullofItems then
    return false
  end
  self:_ExecuteStep1()
  return true
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickCache
  self:_SaveOngoingDatas()
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  local cacheRoot = TutorialHelper.HighlightCacheRoot()
  self.m_gesture = TutorialHelper.TapCustomPos(cacheRoot:GetFlyTargetPosition())
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_TryExecuteStep2()
  local items1 = TutorialHelper.GetItems(itemId)
  local items2 = TutorialHelper.GetItems("c#" .. itemId)
  if 0 < #items1 and 0 < #items2 then
    self:_ExecuteStep2(items1[1], items2[1])
    return true
  end
end

function Executer:_ExecuteStep2(item1, item2)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.Merge
  self:_SaveOngoingDatas()
  local from = item1:GetPosition()
  local to = item2:GetPosition()
  GM.TutorialModel:SetForceSourceBoardPosition(from)
  GM.TutorialModel:SetForceTargetBoardPosition(to)
  self.m_gesture = TutorialHelper.DragOnItems(from, to)
  if not self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
    return
  end
  TutorialHelper.MaskOnItemBoard(from, to)
  TutorialHelper.ShowDialogWithBoardMaskArea(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), from, to)
end

function Executer:_TryExecuteStep3()
  local order = self:_GetValidFixedOrder()
  if order == nil then
    return
  end
  local mergedItems = TutorialHelper.GetItems(mergedItemId)
  if #mergedItems <= 0 then
    return
  end
  self:_ExecuteStep3(order)
  return true
end

function Executer:_ExecuteStep3(order)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.HighlightOrder
  self:_SaveOngoingDatas()
  local orderCell = TutorialHelper.HighlightOrder(order)
  self.m_orderCell = orderCell
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(self.m_orderCell:GetIcon(1).transform)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_GetValidFixedOrder()
  local orders = GM.MainBoardModel:GetOrders()
  for _, order in pairs(orders) do
    if order:GetType() == OrderType.Fixed and order:GetState() ~= OrderState.CanDeliver and GM.ItemDataModel:IsDishes(order:GetRequirements()[1]) then
      local boardView = MainBoardView.GetInstance()
      if boardView ~= nil then
        local orderArea = boardView:GetOrderArea()
        local orderCell = orderArea:GetCell(order)
        if not orderArea:IsPlayingOrderAnimation() and orderCell ~= nil then
          return order
        end
      end
    end
  end
  return nil
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
