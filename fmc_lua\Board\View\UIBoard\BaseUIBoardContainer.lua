BaseUIBoardContainer = {}
BaseUIBoardContainer.__index = BaseUIBoardContainer
BaseUIBoardContainer.ItemBoardName = "ItemBoard"
BaseUIBoardContainer.CacheRootName = "BoardCacheRoot"

function BaseUIBoardContainer:Init(boardView, boardCamera)
  self.m_boardView = boardView
  self.m_boardCamera = boardCamera
  local eventTrigger = self.gameObject:GetComponent(typeof(CS.LuaEventTrigger))
  
  function eventTrigger.OnLuaPointerDown(eventData)
    self:_OnPointerDown(eventData)
  end
  
  function eventTrigger.OnLuaDrag(eventData)
    self:_OnDrag(eventData)
  end
  
  function eventTrigger.OnLuaPointerUp(eventData)
    self:_OnPointerUp(eventData)
  end
end

function BaseUIBoardContainer:ConvertScreenPositionToWorldPosition(position)
  local transform = self.m_targetTrans or self.transform
  local _, localPosition = RectTransformUtility.ScreenPointToLocalPointInRectangle(transform, UIUtil.ToVector2(position), GM.UIManager.camera)
  local viewportPosition = UIUtil.ToVector3(Rect.PointToNormalized(transform.rect, localPosition))
  return self.m_boardCamera:ViewportToWorldPoint(viewportPosition)
end

function BaseUIBoardContainer:ConvertWorldPositionToScreenPosition(position)
  local transform = self.m_targetTrans or self.transform
  local normalizedPosition = UIUtil.ToVector2(self.m_boardCamera:WorldToViewportPoint(position))
  local localPosition = UIUtil.ToVector3(Rect.NormalizedToPoint(transform.rect, normalizedPosition))
  local uiWorldPosition = transform:TransformPoint(localPosition)
  return PositionUtil.UICameraWorld2Screen(uiWorldPosition)
end

function BaseUIBoardContainer:ConvertWorldPositionToUIWorldPosition(position)
  local transform = self.m_targetTrans or self.transform
  local normalizedPosition = UIUtil.ToVector2(self.m_boardCamera:WorldToViewportPoint(position))
  local localPosition = UIUtil.ToVector3(Rect.NormalizedToPoint(transform.rect, normalizedPosition))
  local uiWorldPosition = transform:TransformPoint(localPosition)
  return uiWorldPosition
end

function BaseUIBoardContainer:ConvertUIWorldPositionToWorldPosition(position)
  local transform = self.m_targetTrans or self.transform
  local localPosition = UIUtil.ToVector2(transform:InverseTransformPoint(position))
  local viewportPosition = UIUtil.ToVector3(Rect.PointToNormalized(transform.rect, localPosition))
  local worldPosition = self.m_boardCamera:ViewportToWorldPoint(viewportPosition)
  return worldPosition
end

function BaseUIBoardContainer:_OnPointerDown(eventData)
  local worldPosition = self:ConvertScreenPositionToWorldPosition(eventData.position)
  self.m_collider = CS.UnityEngine.Physics2D.OverlapPoint(Vector2(worldPosition.x, worldPosition.y))
  if self.m_collider == nil then
    return
  end
  if self.m_collider.gameObject.name == BaseUIBoardContainer.ItemBoardName then
    self.m_boardView:OnPointerDown(eventData)
  elseif self.m_collider.gameObject.name == BaseUIBoardContainer.CacheRootName then
    if self.m_cacheRootScheduler then
      Scheduler.Unschedule(self.m_cacheRootScheduler, self)
      self.m_cacheRootScheduler = nil
    end
    
    function self.m_cacheRootScheduler()
      local cacheRoot = self.m_boardView:GetCacheRoot()
      if cacheRoot and cacheRoot.gameObject and not cacheRoot.gameObject:IsNull() and cacheRoot.gameObject.activeSelf then
        cacheRoot:OnClicked()
      else
        Scheduler.Unschedule(self.m_cacheRootScheduler, self)
        self.m_cacheRootScheduler = nil
      end
    end
    
    EventDispatcher.DispatchEvent(EEventType.HuntPointerDown)
    Scheduler.Schedule(self.m_cacheRootScheduler, self, 0.2, nil, 0.5)
  end
end

function BaseUIBoardContainer:_OnDrag(eventData)
  if self.m_collider ~= nil and self.m_collider.gameObject.name == BaseUIBoardContainer.ItemBoardName then
    self.m_boardView:OnDrag(eventData)
  end
end

function BaseUIBoardContainer:_OnPointerUp(eventData)
  if self.m_collider == nil then
    return
  end
  if self.m_collider.gameObject.name == BaseUIBoardContainer.ItemBoardName then
    self.m_boardView:OnPointerUp(eventData)
  elseif self.m_collider.gameObject.name == BaseUIBoardContainer.CacheRootName then
    local cacheRoot = self.m_boardView:GetCacheRoot()
    if not UIUtil.IsEmptyComponent(cacheRoot) then
      cacheRoot:OnClicked()
    end
  end
  if self.m_cacheRootScheduler then
    Scheduler.Unschedule(self.m_cacheRootScheduler, self)
    self.m_cacheRootScheduler = nil
  end
end
