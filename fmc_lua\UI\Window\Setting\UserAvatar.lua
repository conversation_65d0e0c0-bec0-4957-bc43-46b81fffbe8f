UserAvatar = {}
UserAvatar.__index = UserAvatar
EAvatarFrame = {
  Normal = ImageFileConfigName.head_frame,
  Highlight = ImageFileConfigName.head_frame2,
  PkRace = ImageFileConfigName.head_frame_pkrace
}

function UserAvatar:SetAvatar(frame, avatarIcon)
  if self.m_avatarTrans == nil then
    self.m_avatarTrans = self.m_avatarImg.gameObject.transform
  end
  self:UpdateFrame(frame)
  local isBuildIn = GM.UserProfileModel:IsBuildInAvatar(avatarIcon)
  if isBuildIn then
    self.m_bIgnoreCB = true
    SpriteUtil.SetImage(self.m_avatarImg, avatarIcon)
    return
  end
  if StringUtil.IsNilOrEmpty(avatarIcon) then
    self.m_bIgnoreCB = true
    SpriteUtil.SetImage(self.m_avatarImg, ImageFileConfigName.avatar_default)
    return
  end
  self.m_bIgnoreCB = false
  if self.m_downloadCallback == nil then
    function self.m_downloadCallback(bSuccess, sprite)
      if bSuccess and not self.m_avatarImg:IsNull() then
        if self.m_bIgnoreCB then
          return
        end
        self.m_avatarImg.enabled = true
        self.m_avatarImg.sprite = sprite
      end
    end
  end
  if self.m_loadDefaultCallback == nil then
    function self.m_loadDefaultCallback(sprite)
      if self.m_bIgnoreCB then
        return
      end
      if not self.m_avatarImg:IsNull() then
        self.m_avatarImg.enabled = true
        self.m_avatarImg.sprite = sprite
      end
    end
  end
  self.m_avatarImg.enabled = false
  LoadUrlImage:LoadSprite(avatarIcon, self.m_downloadCallback, true, ImageFileConfigName.avatar_default, self.m_loadDefaultCallback)
end

function UserAvatar:UpdateFrame(frame)
  SpriteUtil.SetImage(self.m_frameImg, frame)
end
