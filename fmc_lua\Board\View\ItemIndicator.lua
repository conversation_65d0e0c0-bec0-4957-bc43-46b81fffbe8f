ItemIndicator = {}
ItemIndicator.__index = ItemIndicator
local ItemIndicatorLocalZ = 650
local TimePerFrame = 0.016666666666666666

function ItemIndicator:OnDestroy()
  if self.m_showTween then
    self.m_showTween:Kill()
    self.m_showTween = nil
  end
  if self.m_loopTween then
    self.m_loopTween:Kill()
    self.m_loopTween = nil
  end
end

function ItemIndicator:UpdateIndicator(selectedItemModel, playAnimation)
  if self.gameObject.activeSelf ~= (selectedItemModel ~= nil) then
    self.gameObject:SetActive(selectedItemModel ~= nil)
  end
  if selectedItemModel == nil then
    return
  end
  local localPosition = selectedItemModel:GetPosition():ToLocalPosition()
  self.transform.localPosition = Vector3(localPosition.x + BaseBoardModel.TileSize / 2, localPosition.y + BaseBoardModel.TileSize / 2, ItemIndicatorLocalZ)
  self:_UpdateIndicator(selectedItemModel, playAnimation)
end

function ItemIndicator:UpdateIndicatorWithoutBoard(selectedItemView, playAnimation)
  if self.gameObject.activeSelf ~= (selectedItemView ~= nil) then
    self.gameObject:SetActive(selectedItemView ~= nil)
  end
  if selectedItemView == nil then
    return
  end
  self.transform.position = selectedItemView.transform.position
  UIUtil.SetLocalPosition(self.transform, nil, nil, ItemIndicatorLocalZ)
  self:_UpdateIndicator(selectedItemView:GetModel(), playAnimation)
end

function ItemIndicator:_UpdateIndicator(itemModel, playAnimation)
  local hasMergedType
  local itemCobweb = itemModel:GetComponent(ItemCobweb)
  if itemCobweb == nil then
    hasMergedType = itemModel:GetMergedType() ~= nil
  else
    hasMergedType = not itemCobweb:IsInnerItemMaxLevel()
  end
  self.m_mergeGo:SetActive(hasMergedType)
  self.m_noMergeGo:SetActive(not hasMergedType)
  if playAnimation then
    self:_TryToInitTweenOrigin()
    self.m_loopTween:Pause()
    self.m_showTween:Pause()
    self.gameObject.transform.localScale = Vector3(0.3, 0.3, 0.3)
    local transparentColor = CSColor(1, 1, 1, 0)
    self.m_mergeSprite.color = transparentColor
    self.m_noMergeSprite.color = transparentColor
    self.m_showTween:Restart()
  end
end

function ItemIndicator:_TryToInitTweenOrigin()
  if self.m_showTween ~= nil then
    return
  end
  local transform = self.gameObject.transform
  local loopDuration = 70 * TimePerFrame
  local loopTween = DOTween.Sequence():SetLoops(-1)
  loopTween:Append(transform:DOScale(1.03, loopDuration))
  loopTween:Join(self.m_mergeSprite:DOColor(CSColor(0.92, 0.92, 0.92, 1), loopDuration))
  loopTween:Append(transform:DOScale(1, loopDuration))
  loopTween:Join(self.m_mergeSprite:DOColor(CSColor(1, 1, 1, 1), loopDuration))
  loopTween:SetAutoKill(false)
  self.m_loopTween = loopTween
  self.m_loopTween:Pause()
  local showTween = DOTween.Sequence()
  showTween:Append(transform:DOScale(1.15, 8 * TimePerFrame))
  showTween:Append(transform:DOScale(1, 7 * TimePerFrame))
  showTween:AppendInterval(10 * TimePerFrame)
  showTween:Insert(0, self.m_mergeSprite:DOFade(1, 10 * TimePerFrame))
  showTween:Insert(0, self.m_noMergeSprite:DOFade(1, 10 * TimePerFrame))
  showTween:OnComplete(function()
    self.m_loopTween:Restart()
  end)
  showTween:SetAutoKill(false)
  self.m_showTween = showTween
  self.m_showTween:Pause()
end
