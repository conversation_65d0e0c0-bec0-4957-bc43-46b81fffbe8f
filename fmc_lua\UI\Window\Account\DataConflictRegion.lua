local CompareFlag = {
  Equal = 1,
  Greater = 2,
  Less = 3
}
DataConflictRegion = {}
DataConflictRegion.__index = DataConflictRegion

function DataConflictRegion:Init(conflictWindow, index, data, referenceData)
  self.m_conflictWindow = conflictWindow
  self.m_index = index
  self.m_data = data
  self.m_referenceData = referenceData
  self:_SetNumber(self.m_gemProgressText, "Gems")
  self:_SetNumber(self.m_coinProgressText, "Coins")
  self:_SetNumber(self.m_dayProgressText, "Day", "order_day_button")
  self:_SetDisplayTask()
  if not data.Timestamp or data.Timestamp == 0 then
    self.m_timestampText.text = GM.GameTextModel:GetText("progress_save_time_local")
  else
    self.m_timestampText.text = GM.GameTextModel:GetText("progress_save_time_cloud", TimeUtil.ParseTimeDescription(data.Timestamp, 2, false, true))
  end
end

function DataConflictRegion:_SetNumber(text, key, valueKey)
  local value = self.m_data[key]
  local compareFlag = CompareFlag.Equal
  if self.m_referenceData ~= nil then
    local referenceValue = self.m_referenceData[key]
    if value ~= referenceValue then
      if value > referenceValue then
        compareFlag = CompareFlag.Greater
      else
        compareFlag = CompareFlag.Less
      end
    end
  end
  if valueKey then
    value = GM.GameTextModel:GetText(valueKey, value)
  end
  self:_SetText(key, text, value, compareFlag)
end

function DataConflictRegion:_SetDisplayTask()
  local nCurrentChapterId = self.m_data.CurrentChapter
  local firstOngoingTaskId = (StringUtil.SplitToNum(self.m_data.CurrentTasks, TASK_ID_SEPARATOR) or {})[1]
  local compareFlag = CompareFlag.Equal
  if self.m_referenceData ~= nil then
    local nReferenceChapterId = self.m_referenceData.CurrentChapter
    local referenceFirstOngoingTaskId = (StringUtil.SplitToNum(self.m_referenceData.CurrentTasks, TASK_ID_SEPARATOR) or {})[1]
    if nReferenceChapterId ~= nCurrentChapterId or referenceFirstOngoingTaskId ~= firstOngoingTaskId then
      if self:_TaskGreater(nCurrentChapterId, firstOngoingTaskId, nReferenceChapterId, referenceFirstOngoingTaskId) then
        compareFlag = CompareFlag.Greater
      else
        compareFlag = CompareFlag.Less
      end
    end
  end
  if not firstOngoingTaskId then
    nCurrentChapterId = nCurrentChapterId + 1
    firstOngoingTaskId = 0
  end
  local value = tostring(nCurrentChapterId) .. "-" .. tostring(firstOngoingTaskId)
  self:_SetText("Task", self.m_taskProgressText, value, compareFlag)
end

function DataConflictRegion:_TaskGreater(chapterId, taskId, referenceChapterId, referenceTaskId)
  if chapterId ~= referenceChapterId then
    return referenceChapterId < chapterId
  end
  if not taskId then
    return true
  elseif not referenceTaskId then
    return false
  end
  return referenceTaskId < taskId
end

function DataConflictRegion:_SetText(key, text, value, compareFlag)
  text.text = value
  if compareFlag == CompareFlag.Greater then
    text.color = UIUtil.ConvertHexColor2CSColor("49B124")
  elseif compareFlag == CompareFlag.Less then
    text.color = UIUtil.ConvertHexColor2CSColor("E8254F")
  end
end

function DataConflictRegion:OnButtonClicked()
  self.m_conflictWindow:OnRegionSelected(self.m_index)
end

function DataConflictRegion:SetSelected(selected)
  self.m_buttonGo:SetActive(not selected)
  self.m_selectedGo:SetActive(selected)
end
