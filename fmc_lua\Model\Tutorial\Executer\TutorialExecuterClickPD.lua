local Step = {Click = "1"}
local textKey = "tutorial_click_transform_1"
local itemId = "it_1_1_6"
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.ItemReplaced, self, self._OnItemSpread)
  EventDispatcher.AddListener(EEventType.ItemSpreadFailed, self, self._OnItemSpread)
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnItemSpread)
  local item = TutorialHelper.GetItems(itemId)
  if 0 < #item then
    self.m_targetItem = item[1]
  else
    self:Finish()
  end
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return false
  end
  local itemSpread = self.m_targetItem:GetComponent(ItemSpread)
  if itemSpread:GetStorageRestNumber() > 0 then
    local boardView = MainBoardView.GetInstance()
    if boardView ~= nil then
      boardView:UpdateSelectedItem(self.m_targetItem)
    end
    self:_ExecuteStep1(self.m_targetItem)
    return true
  end
end

function Executer:_ExecuteStep1(item)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.Click
  self:_SaveOngoingDatas()
  local tapPos = item:GetPosition()
  TutorialHelper.MaskOnItemBoard(tapPos, tapPos)
  TutorialHelper.ShowDialogWithBoardMaskArea(GM.GameTextModel:GetText(textKey), tapPos, tapPos)
  self.m_gesture = TutorialHelper.TapOnBoard(tapPos)
  if not self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish()
    return
  end
  GM.TutorialModel:SetForceSourceBoardPosition(tapPos)
  GM.TutorialModel:SetForceTargetBoardPosition(tapPos)
end

function Executer:_OnItemSpread()
  if self.m_strOngoingDatas == Step.Click and self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
  end
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
