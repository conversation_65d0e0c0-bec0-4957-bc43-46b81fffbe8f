FlambeTimeWindow = setmetatable({}, TransparentBaseWindow)
FlambeTimeWindow.__index = FlambeTimeWindow

function FlambeTimeWindow:Init(removeCellFunc, tryFillNewOrderFunc, onBeforeInit, onAfterInit)
  local removeCellUIPos, tempNormalizedPosition
  if onBeforeInit then
    removeCellUIPos, tempNormalizedPosition = onBeforeInit()
  end
  local isLink = GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link
  self.m_linkGo:SetActive(isLink)
  self.m_modeGo:SetActive(not isLink)
  self.m_clockText = isLink and self.m_clockTextLink or self.m_clockTextMode
  self.m_titleTrans = isLink and self.m_titleTransLink or self.m_titleTransMode
  self.m_titleText = isLink and self.m_titleTextLink or self.m_titleTextMode
  self.m_clockCanvas = isLink and self.m_clockCanvasLink or self.m_clockCanvasMode
  self.m_fireGo = isLink and self.m_fireGoLink or self.m_fireGoMode
  self.m_flyEffectPrefab = isLink and self.m_flyEffectPrefabLink or self.m_flyEffectPrefabMode
  self.m_clockText.text = TimeUtil.ParseTimeDescription(FlambeTimeDuration, 2)
  self.m_fireGo:SetActive(false)
  UIUtil.SetAnchoredPosition(self.m_titleTrans, 0, -62)
  UIUtil.SetAlpha(self.m_titleText, 0)
  self.m_clockCanvas.alpha = 0
  if IsAutoRun() then
    DOVirtual.DelayedCall(0.2, function()
      self:_CloseWindowAndDispatchEvent()
      if removeCellFunc then
        removeCellFunc()
      end
      if tryFillNewOrderFunc then
        tryFillNewOrderFunc()
      end
    end)
    return
  end
  if isLink then
    if removeCellUIPos ~= nil then
      self:_LinkOrderAnim(removeCellUIPos, removeCellFunc, tryFillNewOrderFunc)
    else
      self:_LinkFirstAnim()
    end
  else
    self:_ModeAnim()
  end
  if onAfterInit then
    onAfterInit(tempNormalizedPosition)
  end
end

function FlambeTimeWindow:_ModeAnim()
  self.m_fireGo:SetActive(true)
  local seq = DOTween.Sequence()
  seq:Insert(0, self.m_titleTrans:DOAnchorPosY(48, 0.3))
  seq:Insert(0.3, self.m_titleTrans:DOAnchorPosY(37, 0.15))
  seq:Insert(0, self.m_titleText:DOFade(1, 0.5))
  seq:Insert(0.3, self.m_clockCanvas:DOFade(1, 0.2))
  seq:Insert(2, self.m_contentRootRect:DOScale(0, 0.3))
  seq:Insert(2, self.m_contentRootCanvas:DOFade(0, 0.3))
  seq:InsertCallback(2.2, function()
    local targetFlyDuration = self:_FlyToItemsToFireTogether(V2Zero)
    local s = DOTween.Sequence()
    s:AppendInterval(targetFlyDuration)
    s:AppendCallback(function()
      self:_CloseWindowAndDispatchEvent()
    end)
  end)
end

function FlambeTimeWindow:_LinkFirstAnim()
  self.m_fireGo:SetActive(true)
  local clockTrans = self.m_clockCanvas.transform
  UIUtil.SetAnchoredPosition(self.m_titleTrans, -100, 37)
  UIUtil.SetAnchoredPosition(clockTrans, -100, 0)
  local seq = DOTween.Sequence()
  seq:Insert(0, self.m_titleTrans:DOAnchorPosX(50, 0.3))
  seq:Insert(0.3, self.m_titleTrans:DOAnchorPosX(0, 0.15))
  seq:Insert(0, clockTrans:DOAnchorPosX(50, 0.3))
  seq:Insert(0.3, clockTrans:DOAnchorPosX(0, 0.15))
  seq:Insert(0, self.m_titleText:DOFade(1, 0.3))
  seq:Insert(0, self.m_clockCanvas:DOFade(1, 0.3))
  seq:InsertCallback(1, function()
    self:_CenterLinkOrder()
  end)
  seq:Insert(1.5, self.m_titleText:DOFade(0, 0.3))
  seq:Insert(1.5, self.m_clockCanvas:DOFade(0, 0.3))
  seq:InsertCallback(1.7, function()
    local newFiredCell, newFiredCellUIPos = self:_GetNewFiredOrderCellAndUIPos()
    if not newFiredCell then
      self:_CloseWindowAndDispatchEvent()
      return
    end
    self.m_linkOrderFireTrans.localScale = V3One
    local s = DOTween.Sequence()
    s:Insert(0, self.m_linkOrderFireTrans:DOJump(newFiredCellUIPos, 150, 1, 0.3):SetEase(Ease.InOutCubic))
    s:InsertCallback(0.3, function()
      newFiredCell:TryShowFire()
      local targetEffectGo = Object.Instantiate(self.m_targetEffect, self.m_scaleRootTrans)
      targetEffectGo.transform.localPosition = newFiredCellUIPos
      local targetFlyDuration = self:_FlyToItemsToFireSeparately()
      local innerSeq = DOTween.Sequence()
      innerSeq:AppendInterval(targetFlyDuration)
      innerSeq:AppendCallback(function()
        self:_CloseWindowAndDispatchEvent()
      end)
    end)
  end)
end

function FlambeTimeWindow:_LinkOrderAnim(removeCellUIPos, removeCellFunc, tryFillNewOrderFunc)
  local startFlyDuration = self:_FlyFromCurFiredItems2Pos(Vector2(removeCellUIPos.x, removeCellUIPos.y))
  self.m_linkOrderFireTrans.localPosition = Vector3(removeCellUIPos.x, removeCellUIPos.y, 0)
  local seq = DOTween.Sequence()
  seq:Insert(0.25, self.m_linkOrderFireTrans:DOScale(1, 0.3))
  seq:Insert(startFlyDuration + 0.4, self.m_linkOrderFireTrans:DOAnchorPosY(removeCellUIPos.y + 220, 0.3))
  seq:InsertCallback(startFlyDuration + 0.4, function()
    removeCellFunc()
    self:_CenterLinkOrder()
  end)
  seq:InsertCallback(startFlyDuration + 0.7, function()
    local newFiredCell, newFiredCellUIPos = self:_GetNewFiredOrderCellAndUIPos()
    if not newFiredCell then
      self:_CloseWindowAndDispatchEvent()
      return
    end
    local s = DOTween.Sequence()
    s:Insert(0, self.m_linkOrderFireTrans:DOJump(newFiredCellUIPos, 150, 1, 0.3):SetEase(Ease.InOutCubic))
    s:InsertCallback(0.3, function()
      newFiredCell:TryShowFire()
      local targetEffectGo = Object.Instantiate(self.m_targetEffect, self.m_scaleRootTrans)
      targetEffectGo.transform.localPosition = newFiredCellUIPos
      local targetFlyDuration = self:_FlyToItemsToFireSeparately()
      local innerSeq = DOTween.Sequence()
      innerSeq:AppendInterval(targetFlyDuration)
      innerSeq:AppendCallback(function()
        self:_CloseWindowAndDispatchEvent()
        tryFillNewOrderFunc()
      end)
    end)
  end)
end

function FlambeTimeWindow:_GetItemsUIWorldPosition(items)
  local arrUIWorldPos = {}
  local boardView = MainBoardView.GetInstance()
  local screenPos, uiPos
  for _, item in ipairs(items) do
    screenPos = boardView:ConvertBoardPositionToScreenPosition(item:GetPosition())
    uiPos = PositionUtil.UICameraScreen2World(screenPos)
    arrUIWorldPos[#arrUIWorldPos + 1] = uiPos
  end
  return arrUIWorldPos
end

function FlambeTimeWindow:_GetCurFiredItems(chains)
  if not chains or not next(chains) then
    return {}
  end
  local items = GM.MainBoardModel:FilterItems(function(itemModel)
    if itemModel:GetComponent(ItemSpread) ~= nil or itemModel:GetComponent(ItemCook) ~= nil then
      local chain = GM.ItemDataModel:GetChainId(itemModel:GetCode())
      if Table.ListContain(chains, chain) then
        return true
      end
    end
    return false
  end)
  return items
end

function FlambeTimeWindow:_FlyFromCurFiredItems2Pos(endPos)
  local startItemsChains = GM.FlambeTimeModel:GetAndResetCurFiredItemChains()
  local startItems = self:_GetCurFiredItems(startItemsChains)
  local arrStartPos = self:_GetItemsUIWorldPosition(startItems)
  if #arrStartPos == 0 then
    return 0
  end
  local flyDuration = 0.3
  local s = DOTween.Sequence()
  for _, uiPos in ipairs(arrStartPos) do
    self:_Fly(Vector2(uiPos.x, uiPos.y), endPos, flyDuration, s)
  end
  local spread, cook
  for _, item in ipairs(startItems) do
    spread = item:GetComponent(ItemSpread)
    if spread then
      spread:HideFire()
    end
    cook = item:GetComponent(ItemCook)
    if cook then
      cook:HideFire()
    end
  end
  return flyDuration
end

function FlambeTimeWindow:_FlyToItemsToFireTogether(fromPos)
  local targetItems = GM.FlambeTimeModel:GetInBoardFiredItems()
  local arrTargetPos = self:_GetItemsUIWorldPosition(targetItems)
  if #arrTargetPos == 0 then
    return 0
  end
  local flyDuration = 0.3
  local s = DOTween.Sequence()
  for _, uiPos in ipairs(arrTargetPos) do
    self:_Fly(fromPos, Vector2(uiPos.x, uiPos.y), flyDuration, s)
  end
  return flyDuration
end

function FlambeTimeWindow:_FlyToItemsToFireSeparately()
  local targetItems = GM.FlambeTimeModel:GetInBoardFiredItems(true)
  local arrTargetPos = self:_GetItemsUIWorldPosition(targetItems)
  if #arrTargetPos == 0 then
    return 0
  end
  local boardView = MainBoardView.GetInstance()
  local nextFlyDuration = 0.3
  self.m_linkOrderFireTrans.localScale = V3Zero
  UIUtil.AddAnchoredPosition(self.m_linkOrderFireTrans, 0, -80)
  local totalTime = 0.2
  local s = DOTween.Sequence()
  s:Insert(totalTime, self.m_linkOrderFireTrans:DOScale(1, 0.4))
  s:Insert(totalTime, self.m_linkOrderFireTrans:DOAnchorPosY(self.m_linkOrderFireTrans.localPosition.y + 300, 0.4):SetEase(Ease.OutSine))
  totalTime = totalTime + 0.4
  local item, itemView, spread, cook, targetEffectGo
  for i, uiPos in ipairs(arrTargetPos) do
    s:Insert(totalTime, self.m_linkOrderFireTrans:DOJump(uiPos, 150, 1, nextFlyDuration):SetEase(Ease.InOutCubic))
    s:InsertCallback(totalTime + nextFlyDuration, function()
      item = targetItems[i]
      itemView = boardView:GetItemView(item)
      if itemView then
        itemView:PlayHitAnimation()
      end
      spread = item:GetComponent(ItemSpread)
      if spread then
        spread:TryShowFire()
      end
      cook = item:GetComponent(ItemCook)
      if cook then
        cook:TryShowFire()
      end
      targetEffectGo = Object.Instantiate(self.m_targetEffect, self.m_scaleRootTrans)
      targetEffectGo.transform.localPosition = Vector3(uiPos.x, uiPos.y, 0)
    end)
    totalTime = totalTime + nextFlyDuration + 0.05
    nextFlyDuration = nextFlyDuration * 0.8
  end
  return totalTime
end

function FlambeTimeWindow:_Fly(startPos, endPos, flyDuration, seq, delay)
  delay = delay or 0
  local flyEffectGo = Object.Instantiate(self.m_flyEffectPrefab, self.m_scaleRootTrans)
  flyEffectGo.transform.localPosition = Vector3(startPos.x, startPos.y, 0)
  local v2StartPos = startPos
  local v2EndPos = endPos
  local midPos = (v2StartPos + v2EndPos) / 2
  local verticalDirection = Vector2(v2EndPos.y - v2StartPos.y, v2StartPos.x - v2EndPos.x)
  local length = math.sqrt(verticalDirection.x ^ 2 + verticalDirection.y ^ 2)
  verticalDirection = Vector2(verticalDirection.x / length, verticalDirection.y / length)
  local midShift = verticalDirection * math.random(170)
  local flyFromRight = 0 < v2EndPos.x
  local config = {
    control1 = midPos + midShift * (flyFromRight and 1 or -1),
    control2 = midPos + midShift * (flyFromRight and 1 or -1),
    to = v2EndPos,
    from = v2StartPos,
    posType = BezierPosType.Local,
    easeType = BezierEaseType.Linear
  }
  local bezier = flyEffectGo:GetComponent(typeof(CS.BezierMove2D))
  seq:InsertCallback(delay, function()
    bezier:MoveTo(config, flyDuration)
  end)
  seq:InsertCallback(delay + flyDuration + 0.1, function()
    flyEffectGo:RemoveSelf()
  end)
end

function FlambeTimeWindow:_CenterLinkOrder()
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    local orderCell = orderArea:GetCellById(GM.FlambeTimeModel:GetLinkOrderId())
    if orderCell ~= nil then
      orderArea:ScrollToRectTransformVisible(orderCell.transform, true)
    end
  end
end

function FlambeTimeWindow:_GetNewFiredOrderCellAndUIPos()
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    local orderCell = orderArea:GetCellById(GM.FlambeTimeModel:GetLinkOrderId())
    if orderCell ~= nil then
      local newFiredCellWorldPos = orderCell.transform.position
      local newFiredCellScreenPos = boardView:ConvertWorldPositionToScreenPosition(newFiredCellWorldPos)
      local newFiredCellUIPos = PositionUtil.UICameraScreen2World(newFiredCellScreenPos)
      return orderCell, newFiredCellUIPos
    end
  end
end

function FlambeTimeWindow:_CloseWindowAndDispatchEvent()
  self:Close()
  EventDispatcher.DispatchEvent(EEventType.FlambeTimeChanged)
  local isLink = GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link
  if not isLink and PlayerPrefs.GetInt(EPlayerPrefKey.FlambeModeHelpWindowOpened, 0) == 0 and not IsAutoRun() then
    GM.UIManager:OpenView(UIPrefabConfigName.FlambeModeHelpWindow)
  end
  if isLink and PlayerPrefs.GetInt(EPlayerPrefKey.FlambeLinkHelpWindowOpened, 0) == 0 and not IsAutoRun() then
    GM.UIManager:OpenView(UIPrefabConfigName.FlambeLinkHelpWindow)
  end
end

FlambeModeHelpWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  canClickWindowMask = true,
  disableEffectWhenCloseView = true
}, BaseWindow)
FlambeModeHelpWindow.__index = FlambeModeHelpWindow

function FlambeModeHelpWindow:Init()
  PlayerPrefs.SetInt(EPlayerPrefKey.FlambeModeHelpWindowOpened, 1)
  self.m_clockText.text = TimeUtil.ParseTimeDescription(FlambeTimeDuration, 2)
end

FlambeLinkHelpWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  canClickWindowMask = true,
  disableEffectWhenCloseView = true
}, BaseWindow)
FlambeLinkHelpWindow.__index = FlambeLinkHelpWindow

function FlambeLinkHelpWindow:Init()
  PlayerPrefs.SetInt(EPlayerPrefKey.FlambeLinkHelpWindowOpened, 1)
  self.m_clockText.text = TimeUtil.ParseTimeDescription(FlambeTimeDuration, 2)
end
