AccountStatusWindow = setmetatable({canCloseByChangeGameMode = false}, BaseWindow)
AccountStatusWindow.__index = AccountStatusWindow

function AccountStatusWindow:Init(titleTextKey, descriptionTextKey, buttonTextKey, success, isConnected, buttonCallback)
  self.m_titleText.text = GM.GameTextModel:GetText(titleTextKey)
  self.m_descriptionText.text = GM.GameTextModel:GetText(descriptionTextKey)
  self.m_buttonText.text = GM.GameTextModel:GetText(buttonTextKey)
  self.m_unsuccessGo:SetActive(not success)
  self.m_connectedGo:SetActive(success and isConnected)
  self.m_disconnectedGo:SetActive(success and not isConnected)
  self.m_buttonCallback = buttonCallback
end

function AccountStatusWindow:OnButtonClicked()
  self:Close()
  if self.m_buttonCallback ~= nil then
    self.m_buttonCallback()
  end
end

function AccountStatusWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  GM.AccountManager:GiveBindRewardIfCould()
end
