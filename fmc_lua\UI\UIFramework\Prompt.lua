Prompt = {}
Prompt.__index = Prompt
local TEST_COLOR = CSColor(1.0, 0.39215686274509803, 1.0)

function Prompt:Init(id, text, customStartPosition, stayDuration, callback, bTMP, forTest)
  UIUtil.SetLocalScale(self.transform, 1, 0, 1)
  UIUtil.SetAlpha(self.m_normalText, 0)
  self.m_id = id
  bTMP = bTMP or false
  UIUtil.SetActive(self.m_tmpText.gameObject, bTMP)
  UIUtil.SetActive(self.m_normalText.gameObject, not bTMP)
  local textCmp = bTMP and self.m_tmpText or self.m_normalText
  textCmp.text = text
  self.m_testImg.enabled = forTest == true
  local canvasWidth = self.m_canvas.transform.rect.width
  self.m_canvasGroup.alpha = 0
  UIUtil.SetSizeDelta(self.m_canvasGroup.transform, canvasWidth, self.m_canvasGroup.transform.sizeDelta.y)
  local preferredWidth = self.m_normalText.preferredWidth
  local minX = (preferredWidth - canvasWidth) / 2 + 10
  local maxX = -minX
  local startAnchoredPosition
  if customStartPosition == nil then
    startAnchoredPosition = Vector2.zero
  else
    local x = customStartPosition.x
    x = math.max(x, minX)
    x = math.min(x, maxX)
    startAnchoredPosition = Vector2(x, customStartPosition.y)
  end
  self.transform.anchoredPosition = startAnchoredPosition
  local seq = DOTween.Sequence()
  seq:Append(self.m_normalText:DOFade(1, 0.2))
  seq:Join(self.m_canvasGroup:DOFade(1, 0.2))
  seq:Join(self.transform:DOAnchorPosY(startAnchoredPosition.y + 75, 0.3))
  seq:Join(self.transform:DOScaleY(1.2, 0.3))
  seq:Append(self.transform:DOAnchorPosY(startAnchoredPosition.y + 30, 0.1))
  seq:Join(self.transform:DOScaleY(1, 0.1))
  seq:Append(self.transform:DOAnchorPosY(startAnchoredPosition.y + 45, 0.1))
  seq:AppendInterval(stayDuration or 1)
  seq:Append(self.m_normalText:DOFade(0, 0.15))
  seq:Join(self.m_canvasGroup:DOFade(0, 0.15))
  seq.onComplete = callback
  self.m_tween = seq
  self:_TrySpeedUpTween()
  EventDispatcher.AddListener(EEventType.ShowPrompt, self, self._TrySpeedUpTween)
end

function Prompt:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  if self.m_tween then
    self.m_tween:Kill()
    self.m_tween = nil
  end
end

function Prompt:SetSortingOrder(order)
  self.m_canvas.sortingOrder = order
end

function Prompt:_TrySpeedUpTween()
  if self.m_id == GM.UIManager:GetCurrentPromptId() then
    return
  end
  if self.m_tween then
    self.m_tween.timeScale = 10
  end
end

BoardBoostPrompt = {}
BoardBoostPrompt.__index = BoardBoostPrompt

function BoardBoostPrompt:Init(count, startAnchoredPosition)
  self.m_text.text = "+ " .. count
  self.transform.anchoredPosition = startAnchoredPosition
  local stayDuration = 0.5
  local seq = DOTween.Sequence()
  seq:Append(self.m_canvasGroup:DOFade(1, 0.2))
  seq:Join(self.transform:DOAnchorPosY(startAnchoredPosition.y + 75, 0.3))
  seq:Join(self.transform:DOScaleY(1.2, 0.3))
  seq:Append(self.transform:DOAnchorPosY(startAnchoredPosition.y + 30, 0.1))
  seq:Join(self.transform:DOScaleY(1, 0.1))
  seq:Append(self.transform:DOAnchorPosY(startAnchoredPosition.y + 45, 0.1))
  seq:AppendInterval(stayDuration)
  seq:Append(self.m_canvasGroup:DOFade(0, 0.15))
  seq:OnComplete(function()
    self.gameObject:RemoveSelf()
  end)
end
