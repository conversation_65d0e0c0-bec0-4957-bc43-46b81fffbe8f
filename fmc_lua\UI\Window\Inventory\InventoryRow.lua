InventoryRow = {}
InventoryRow.__index = InventoryRow

function InventoryRow:Init(window)
  self.window = window
  self.m_itemCellGos = {}
  self.m_itemCells = {}
  self.m_mapItemCellPool = {}
  self.m_mapItemCellPool[0] = {}
  for _, code in pairs(EInventoryCellCode) do
    self.m_mapItemCellPool[code] = {}
  end
  self.m_itemCellCodes = {}
  self.m_boardSprite.sortingOrder = InventoryWindow.itemViewSortingOrderNormal
  self.m_bInited = true
end

function InventoryRow:SetCell(column, cellCode, param)
  local itemObject, cacheGo
  if self.m_itemCellGos[column] ~= nil then
    if self.m_itemCellCodes[column] == cellCode then
      cacheGo = self.m_itemCellGos[column]
    elseif self.m_mapItemCellPool[self.m_itemCellCodes[column]] ~= nil then
      UIUtil.SetActive(self.m_itemCellGos[column], false)
      table.insert(self.m_mapItemCellPool[self.m_itemCellCodes[column]], self.m_itemCellGos[column])
    end
  end
  if cacheGo == nil and not Table.IsEmpty(self.m_mapItemCellPool[cellCode]) then
    cacheGo = self.m_mapItemCellPool[cellCode][1]
    table.remove(self.m_mapItemCellPool[cellCode], 1)
    UIUtil.SetActive(cacheGo, true)
  end
  if cellCode == EInventoryCellCode.Empty then
    itemObject = cacheGo or Object.Instantiate(self.m_emptyCellPrefab, self.m_content)
  elseif cellCode == EInventoryCellCode.NewSlot then
    itemObject = cacheGo or Object.Instantiate(self.m_newSlotCellPrefab, self.m_content)
    itemObject:GetLuaTable():Init()
  elseif cellCode == EInventoryCellCode.Locked then
    itemObject = cacheGo or Object.Instantiate(self.m_lockedSlotCellPrefab, self.m_content)
  elseif cellCode == EInventoryCellCode.ProducerEmpty then
    itemObject = cacheGo or Object.Instantiate(self.m_producerEmptyCellPrefab, self.m_content)
    itemObject:GetLuaTable():Init(param)
  elseif cellCode == EInventoryCellCode.ProducerLocked then
    itemObject = cacheGo or Object.Instantiate(self.m_producerLockedCellPrefab, self.m_content)
    itemObject:GetLuaTable():Init(param)
  elseif cellCode == EInventoryCellCode.Item then
    itemObject = cacheGo or Object.Instantiate(self.m_cellPrefab, self.m_content)
    itemObject:GetLuaTable():Init(self.window, param.itemModel, param.index, param.bNewAdded)
  else
    Log.Error("[InventoryRow] 无效的cellCode, 请检查:" .. (cellCode or "nil"))
  end
  if itemObject ~= nil then
    self:_SetPosition(itemObject.transform, column)
  end
  self.m_itemCellGos[column] = itemObject
  self.m_itemCells[column] = itemObject and itemObject:GetLuaTable() or nil
  self.m_itemCellCodes[column] = cellCode
end

function InventoryRow:ResetPos(column)
  if self.m_itemCells[column] then
    self:_SetPosition(self.m_itemCells[column].transform, column)
  end
end

local TILE_SIZE = 166

function InventoryRow:_SetPosition(transform, column)
  transform.anchoredPosition = Vector2(TILE_SIZE * 0.5 + 25 + TILE_SIZE * (column - 1), 116)
end

function InventoryRow:GetCell(column)
  return self.m_itemCells[column]
end

function InventoryRow:UpdateOrderCheck(codeStateMap)
  for _, itemCell in pairs(self.m_itemCells) do
    if itemCell.UpdateOrderCheck then
      itemCell:UpdateOrderCheck(codeStateMap)
    end
  end
end

function InventoryRow:HasInited()
  return self.m_bInited
end
