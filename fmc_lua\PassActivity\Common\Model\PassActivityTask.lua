EPassActivityTaskType = {
  Timelimit = "timelimit",
  Cycle = "cycle",
  Bonus = "bonus",
  VIP = "vip"
}
EPassActivityTaskOrder = {
  [EPassActivityTaskType.VIP] = 0,
  [EPassActivityTaskType.Timelimit] = 100,
  [EPassActivityTaskType.Bonus] = 1000,
  [EPassActivityTaskType.Cycle] = 10000
}
EPassActivityTargetType = {
  Customer = "customer",
  Merge = "merge",
  Gold = "gold",
  UseGem = "gem",
  Energy = "energy",
  Day = "day",
  Dish = "dish"
}
PassActivityTask = {}
PassActivityTask.__index = PassActivityTask

function PassActivityTask.Create(model, type, order, index, group, target, count, reward)
  local tb = setmetatable({}, PassActivityTask)
  tb.m_model = model
  tb.m_configTarget = target
  tb.m_configCount = count
  tb.Type = type
  tb.Order = order
  tb.Index = index
  tb.Reward = reward
  tb.Group = group
  return tb
end

function PassActivityTask:GetFinalTarget()
  local realTarget = self.m_model:GetRealItemTarget(self.Type, self.Order, self.Index)
  return realTarget or self.m_configTarget
end

function PassActivityTask:GetFinalCount()
  local realCount = self.m_model:GetRealTargetCount(self.Type, self.Order, self.Index)
  return realCount or self.m_configCount
end

function PassActivityTask:GetExtraCount()
  return self.m_model:GetFinishedCount(self) % self:GetFinalCount()
end

function PassActivityTask:GetAccomplishTimes()
  return self.m_model:GetFinishedCount(self) // self:GetFinalCount()
end

function PassActivityTask:GenerateFinalContent()
  if self.m_configTarget == EPassActivityTargetType.Day then
    local curDay = GM.MainBoardModel:GetCurOrderDay()
    local orderModel = GM.MainBoardModel:GetOrderModel()
    if orderModel:HasClaimedGroupReward() then
      curDay = curDay + 1
    end
    local maxDay = GM.ChapterDataModel:GetMaxCanUnlockDay()
    if self.m_configCount > maxDay - curDay + 1 then
      self.m_model:SetRealItemTarget(self.Type, self.Order, self.Index, EPassActivityTargetType.Customer)
      self.m_model:SetRealTargetCount(self.Type, self.Order, self.Index, self.m_configCount * 7)
    end
  end
end

function PassActivityTask:GetActivityModel()
  return self.m_model
end
