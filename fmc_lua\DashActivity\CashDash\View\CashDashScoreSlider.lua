CashDashScoreSlider = setmetatable({}, DashActivityScoreSlider)
CashDashScoreSlider.__index = CashDashScoreSlider

function CashDashScoreSlider:Init(activityType)
  DashActivityScoreSlider.Init(self, activityType)
  EventDispatcher.AddListener(EEventType.BakeOutStateChanged, self, self._UpdateTokenIcon)
  EventDispatcher.AddListener(EEventType.BakeOutModeChanged, self, self._UpdateTokenIcon)
  self:_UpdateTokenIcon()
  if self.m_cashDashScoreSliderCanv then
    self.m_cashDashScoreSliderCanv.alpha = 1
  end
end

function CashDashScoreSlider:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function CashDashScoreSlider:_UpdateTokenIcon()
  local showIconGroup = false
  local iconName
  local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  if bakeOutModel ~= nil and bakeOutModel:CanAcquireToken() then
    showIconGroup = true
    iconName = ImageFileConfigName.icon_bo_token2
  end
  if self.m_showIconGroup ~= showIconGroup then
    self.m_showIconGroup = showIconGroup
    if self.m_showIconGroup then
      self.m_iconGroupGo:SetActive(true)
      self.m_normalIconGo:SetActive(false)
      SpriteUtil.SetImage(self.m_tokenImage, iconName, true)
    else
      self.m_tokenImage.sprite = nil
      self.m_tokenImage.enabled = false
      self.m_iconGroupGo:SetActive(false)
      self.m_normalIconGo:SetActive(true)
    end
  end
end

function CashDashScoreSlider:PlayFadeOut()
  if self.m_cashDashScoreSliderCanv then
    self.m_cashDashScoreSliderCanv:DOFade(0, 0.3)
  end
end

function CashDashScoreSlider:PlayFadeIn()
  if self.m_cashDashScoreSliderCanv then
    self.m_cashDashScoreSliderCanv:DOFade(1, 0.3)
  end
end

function CashDashScoreSlider:PlayScaleIn()
  self.transform.localScale = Vector3.one * 0.4
  self.m_cashDashScoreSliderCanv.alpha = 1
  self.transform:DOScale(1, 0.5):SetEase(Ease.OutBack)
end
