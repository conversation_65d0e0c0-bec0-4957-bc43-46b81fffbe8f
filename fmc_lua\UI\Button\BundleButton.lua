BundleButton = setmetatable({}, HudGeneralButton)
BundleButton.__index = BundleButton

function BundleButton:Awake()
  HudGeneralButton.Awake(self)
  self:_UpdateExclamation()
end

function BundleButton:On<PERSON><PERSON>roy()
  HudGeneralButton.On<PERSON><PERSON>roy(self)
  EventDispatcher.RemoveTarget(self)
end

function BundleButton:Init(bundleType, dataGroup)
  self.m_bundleType = bundleType
  self.m_model = GM.BundleManager:GetModel(bundleType)
  self.m_dataGroup = dataGroup
  self:UpdateContent()
end

function BundleButton:UpdatePerSecond()
  if self.m_model ~= nil and self.m_dataGroup ~= nil then
    local restDuration = self.m_model:GetRestDuration(self.m_dataGroup)
    if 0 < restDuration then
      self.m_countdownText.text = TimeUtil.ParseTimeDescription(restDuration)
    else
    end
  end
  self:_UpdateExclamation()
end

function BundleButton:UpdateContent()
  if self.m_dataGroup == nil then
    return
  end
  local model = self.m_model
  if self.m_uiCode ~= self.m_dataGroup:GetBundleUIType() then
    self.m_uiCode = self.m_dataGroup:GetBundleUIType()
    if BundleUIType[self.m_uiCode] == nil and GameConfig.IsTestMode() then
      GM.UIManager:ShowTestPrompt("礼包UICode " .. self.m_uiCode .. " 不存在")
    end
    self.m_uiStyle = BundleUIType[self.m_uiCode]
    if self.m_uiStyle.icon ~= nil then
      self.m_iconImg.enabled = false
      SpriteUtil.SetImage(self.m_iconImg, self.m_uiStyle.icon, true)
    end
  end
  self:UpdatePerSecond()
end

function BundleButton:_UpdateExclamation()
  UIUtil.SetActive(self.m_exclamationGo, false)
end

function BundleButton:OnClicked()
  if self.m_model ~= nil then
    self.m_model:OpenBundleView(self.m_dataGroup, true, true)
  end
end

function BundleButton:GetTriggerStartTime()
  if self.m_dataGroup == nil or self.m_model == nil then
    return GM.GameModel:GetServerTime()
  end
  return self.m_model:GetBundleTriggerStartTime(self.m_dataGroup)
end
