ProgressActivityBaseWindow = setmetatable({}, BaseWindow)
ProgressActivityBaseWindow.__index = ProgressActivityBaseWindow

function ProgressActivityBaseWindow:Init(eActivityType, bAutoPop)
  self.m_activityType = eActivityType
  self.m_model = GM.ActivityManager:GetModel(eActivityType)
  self.m_definition = self.m_model:GetDefinition()
  self.m_nextStateTime = self.m_model:GetNextStateTime()
  EventDispatcher.AddListener(ProgressActivityDefinition[eActivityType].StateChangedEvent, self, self.OnStateChangedEvent)
  self:LogWindowAction(EBIType.UIActionType.Open, bAutoPop and EBIReferType.AutoPopup or EBIReferType.UserClick)
  if IsAutoRun() then
    DOVirtual.DelayedCall(0.2, function()
      self:Close()
    end)
  end
end

function ProgressActivityBaseWindow:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function ProgressActivityBaseWindow:OnStateChangedEvent()
  self:Close()
end
