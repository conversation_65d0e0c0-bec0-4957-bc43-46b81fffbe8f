EmptyRewardType = "empty"
EActTokenAcquireType = {
  Merge = "merge",
  CostEnergy = "costenergy",
  ProducerClick = "producerClick",
  FinishOrder = "order",
  ShopItem = "shopItem"
}
ERoundType = {
  Up = "up",
  Down = "down",
  Round = "round",
  Down_1 = "down_1"
}
EFlyElementLabelStyle = {
  Default = {
    textColor = CSColor(1, 0.9882352941176471, 0.5137254901960784, 1),
    size = 57,
    outlineColor = CSColor(0.5490196078431373, 0.17647058823529413, 0.08627450980392157, 1),
    shadowColor = CSColor(1, 0.5019607843137255, 0.16862745098039217, 1),
    outlineDis = Vector2(2, 2),
    shadowDis = Vector2(0, -3)
  }
}
for k, v in pairs(EFlyElementLabelStyle) do
  v.__index = v
end
local DefaultViewData = {noDelayTime = true, floatUp = true}
DefaultViewData.__index = DefaultViewData
ActivityTokenHelper = {}
ActivityTokenHelper.__index = ActivityTokenHelper
local DB_KEY_COST_ENERGY = "ath_ce"
local DB_KEY_CLICK_PRODUCER = "ath_cp"
local DB_KEY_RANDOM_ENERGY = "ath_re"
local DB_KEY_ORDER_REWARD_PREFIX = "ath_o_"

function ActivityTokenHelper.Create(model, dbTable, flyLabelStyle, viewDataParam)
  local helper = setmetatable({}, ActivityTokenHelper)
  helper:Init(model, dbTable, flyLabelStyle, viewDataParam)
  return helper
end

function ActivityTokenHelper:Init(model, dbTable, flyLabelStyle, viewDataParam)
  self.m_model = model
  self.m_dbTable = dbTable
  self:_AddEventListener()
  self.m_bIsValid = false
  self.m_labelStyle = flyLabelStyle
  self.m_viewDataParam = setmetatable(viewDataParam or {}, DefaultViewData)
  self.m_viewDataParam.__index = self.m_viewDataParam
  if not self.m_model.CanAddOrderReward or self.m_model.CanAddOrderReward == BaseActivityModel.CanAddOrderReward then
    function self.m_model.CanAddOrderReward(model, orderType)
      return self:CanAddOrderReward(orderType)
    end
  end
  if not self.m_model.GetOrderExtraReward or self.m_model.GetOrderExtraReward == BaseActivityModel.GetOrderExtraReward then
    function self.m_model.GetOrderExtraReward(model, orderScore, coinNum, orderId)
      return self:GetOrderExtraReward(orderScore, orderId)
    end
  end
end

function ActivityTokenHelper:LoadConfig(config)
  if not Table.IsEmpty(config.produce_token_control) then
    self.m_costEnergyCfg = config.produce_token_control[1]
    if self.m_costEnergyCfg.clickNum and self.m_costEnergyCfg.energyNum then
      Log.Error("produce_token_control 中 clickNum 和 energyNum 不应同时配置")
    end
  end
  self.m_arrMergeCfgs = config.merge_token_control or {}
  self.m_arrOrderCfgs = config.order_token_control or {}
  self.m_arrShopItemCfgs = config.shopItem_token_control or {}
  self.m_bIsValid = self.m_costEnergyCfg ~= nil or not Table.IsEmpty(self.m_arrMergeCfgs) or not Table.IsEmpty(self.m_arrOrderCfgs) or not Table.IsEmpty(self.m_arrShopItemCfgs)
  self:CheckScoreConfig(self.m_arrMergeCfgs)
  self:CheckScoreConfig(self.m_arrOrderCfgs)
  self:CheckScoreConfig(self.m_arrShopItemCfgs)
end

function ActivityTokenHelper:IsValid()
  return self.m_bIsValid
end

function ActivityTokenHelper:_AddEventListener()
  EventDispatcher.AddListener(EEventType.CosumeEnergy, self, self._OnEnergyCosumed)
  EventDispatcher.AddListener(EEventType.ProducerItemClick, self, self._OnProducerItemClicked)
  EventDispatcher.AddListener(EEventType.ItemMerged, self, self._OnItemMerge)
  EventDispatcher.AddListener(EEventType.OrderFinished, self, self._OnOrderFinished)
  EventDispatcher.AddListener(EEventType.ShopBuyItemSuccess, self, self._OnShopBuyItem)
  EventDispatcher.AddListener(EEventType.BuyBubble, self, self._OnBuyItem)
  EventDispatcher.AddListener(EEventType.BuyCobweb, self, self._OnBuyItem)
end

function ActivityTokenHelper:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function ActivityTokenHelper:_OnItemMerge(msg)
  if Table.IsEmpty(self.m_arrMergeCfgs) or not self.m_model:CanAddScore() then
    return
  end
  local score = GM.ItemDataModel:GetItemScore(msg.New:GetCode())
  if score <= 0 then
    return
  end
  local reward
  for _, v in pairs(self.m_arrMergeCfgs) do
    if (v.score_min == nil or score >= v.score_min) and (v.score_max == nil or score < v.score_max) then
      reward = ActivityTokenHelper.GetAddReward(v, score)
      break
    end
  end
  if reward ~= nil then
    local boardView = MainBoardView.GetInstance()
    if boardView ~= nil then
      local uiWorldPosition = PositionUtil.UICameraScreen2World(boardView:ConvertBoardPositionToScreenPosition(msg.New:GetPosition()))
      self:PlayAcquireAnim(uiWorldPosition, reward)
    end
    RewardApi.AcquireRewardsLogic({reward}, EPropertySource.Give, EBIType.MergeItem, nil, CacheItemType.Stack)
  end
end

function ActivityTokenHelper:_OnEnergyCosumed(msg)
  if self.m_costEnergyCfg == nil or self.m_costEnergyCfg.energyNum == nil or not self.m_model:CanAddScore() then
    return
  end
  local num = msg.num
  local originCost = self.m_dbTable:GetValue(DB_KEY_COST_ENERGY, DB_VALUE_KEY)
  originCost = tonumber(originCost) or 0
  num = originCost + num
  local energyReward
  local energyNum = self.m_costEnergyCfg.energyNum
  if energyNum ~= nil and num >= energyNum then
    energyReward = ActivityTokenHelper.GetAddReward(self.m_costEnergyCfg, nil, true)
    if energyReward ~= nil then
      energyReward[PROPERTY_COUNT] = energyReward[PROPERTY_COUNT] * (num // energyNum)
    end
    num = num % energyNum
  end
  self.m_dbTable:Set(DB_KEY_COST_ENERGY, DB_VALUE_KEY, num)
  if energyReward ~= nil then
    local boardView = MainBoardView.GetInstance()
    if boardView ~= nil then
      local uiWorldPosition = PositionUtil.UICameraScreen2World(boardView:ConvertBoardPositionToScreenPosition(msg.pos))
      self:PlayAcquireAnim(uiWorldPosition, energyReward)
    end
    RewardApi.CryptOneReward(energyReward)
    RewardApi.AcquireRewardsLogic({energyReward}, EPropertySource.Give, EBIType.CostEnergy, nil, CacheItemType.Stack)
  end
end

function ActivityTokenHelper:_OnProducerItemClicked(msg)
  if self.m_costEnergyCfg == nil or self.m_costEnergyCfg.clickNum == nil or not self.m_model:CanAddScore() then
    return
  end
  local originClickNum = tonumber(self.m_dbTable:GetValue(DB_KEY_CLICK_PRODUCER, DB_VALUE_KEY)) or 0
  local curClickNum = originClickNum + 1
  local clickNum = self.m_costEnergyCfg.clickNum
  local reward
  if clickNum ~= nil and curClickNum >= clickNum then
    reward = ActivityTokenHelper.GetAddReward(self.m_costEnergyCfg, nil, true)
    if reward ~= nil then
      reward[PROPERTY_COUNT] = reward[PROPERTY_COUNT] * (curClickNum // clickNum)
    end
    curClickNum = curClickNum % clickNum
  end
  self.m_dbTable:Set(DB_KEY_CLICK_PRODUCER, DB_VALUE_KEY, curClickNum)
  if reward ~= nil then
    local boardView = MainBoardView.GetInstance()
    if boardView ~= nil then
      local uiWorldPosition = PositionUtil.UICameraScreen2World(boardView:ConvertBoardPositionToScreenPosition(msg.pos))
      self:PlayAcquireAnim(uiWorldPosition, reward)
    end
    RewardApi.CryptOneReward(reward)
    RewardApi.AcquireRewardsLogic({reward}, EPropertySource.Give, EBIType.ClickProducerItem, nil, CacheItemType.Stack)
  end
end

function ActivityTokenHelper:_OnShopBuyItem(msg)
  if not self.m_arrShopItemCfgs or self.m_arrShopItemCfgs[1] == nil or not self.m_model:CanAddScore() then
    return
  end
  local score = GM.ItemDataModel:GetItemScore(msg.shopData.itemCode)
  if msg.shopData.costType ~= EPropertyType.Gem or score == nil or score == 0 then
    return
  end
  local reward
  for _, v in ipairs(self.m_arrShopItemCfgs) do
    if (v.score_min == nil or score >= v.score_min) and (v.score_max == nil or score < v.score_max) then
      reward = ActivityTokenHelper.GetAddReward(v, score)
      break
    end
  end
  if reward ~= nil then
    RewardApi.AcquireRewardsLogic({reward}, EPropertySource.Give, EBIType.ShopItem, nil, CacheItemType.Stack)
    DelayExecuteFunc(function()
      self:PlayAcquireAnim(msg.uiPos or Vector3(0, 100, 0), reward)
    end, 0.1)
  end
end

function ActivityTokenHelper:_OnBuyItem(msg)
  if Table.IsEmpty(self.m_arrShopItemCfgs) or not self.m_model:CanAddScore() then
    return
  end
  local itemModel = msg.source
  local score = GM.ItemDataModel:GetItemScore(msg.newItemCode)
  if score <= 0 then
    return
  end
  local reward
  for _, v in ipairs(self.m_arrShopItemCfgs) do
    if (v.score_min == nil or score >= v.score_min) and (v.score_max == nil or score < v.score_max) then
      reward = ActivityTokenHelper.GetAddReward(v, score)
      break
    end
  end
  if reward ~= nil then
    local boardView = MainBoardView.GetInstance()
    if boardView ~= nil then
      local uiWorldPosition = PositionUtil.UICameraScreen2World(boardView:ConvertBoardPositionToScreenPosition(itemModel:GetPosition()))
      uiWorldPosition.z = 0
      self:PlayAcquireAnim(uiWorldPosition, reward)
    end
    RewardApi.AcquireRewardsLogic({reward}, EPropertySource.Give, EBIType.BuyBubble, nil, CacheItemType.Stack)
  end
end

function ActivityTokenHelper:PlayAcquireAnim(uiWorldPosition, reward)
  uiWorldPosition.z = 0
  local viewData = setmetatable({
    arrWorldPos = {uiWorldPosition}
  }, self.m_viewDataParam)
  if self.m_labelStyle then
    viewData.flyCount = 1
    viewData.floatLabel = setmetatable({
      text = "+" .. reward[PROPERTY_COUNT]
    }, self.m_labelStyle)
  end
  RewardApi.AcquireRewardsInView({reward}, viewData)
end

function ActivityTokenHelper:CanAddOrderReward(orderType)
  if not self.m_arrOrderCfgs or self.m_arrOrderCfgs[1] == nil or not self.m_model:CanAddScore() then
    return false
  end
  return true
end

function ActivityTokenHelper:GetOrderExtraReward(orderScore, orderId)
  local dbID = DB_KEY_ORDER_REWARD_PREFIX .. orderId
  local rewardStr = self.m_dbTable:GetValue(dbID, DB_VALUE_KEY)
  if not StringUtil.IsNilOrEmpty(rewardStr) then
    return json.decode(StringUtil.Replace(rewardStr, "@", ","))
  end
  local reward
  for _, v in pairs(self.m_arrOrderCfgs) do
    if (v.score_min == nil or orderScore >= v.score_min) and (v.score_max == nil or orderScore < v.score_max) then
      reward = ActivityTokenHelper.GetAddReward(v, orderScore, true)
      if reward ~= nil then
        self.m_dbTable:Set(dbID, DB_VALUE_KEY, StringUtil.Replace(json.encode(reward), ",", "@"))
        return reward
      end
    end
  end
  return
end

function ActivityTokenHelper:_OnOrderFinished(msg)
  local id = msg.order:GetId()
  self.m_dbTable:Remove(DB_KEY_ORDER_REWARD_PREFIX .. id)
end

function ActivityTokenHelper.GetAddReward(config, score, bWithoutCrypt)
  local reward = Table.ListWeightSelectOne(config.rewardsWeight)
  if reward[PROPERTY_TYPE] == EmptyRewardType then
    return nil
  end
  reward = ActivityTokenHelper.TryUseRatioReward(reward, config, score)
  if not bWithoutCrypt then
    RewardApi.CryptOneReward(reward)
  end
  return reward
end

function ActivityTokenHelper.TryUseRatioReward(reward, config, score)
  reward = Table.ShallowCopy(reward)
  if config.ratio ~= nil and score ~= nil then
    local roundType = config.roundType
    local targetNum = reward[PROPERTY_COUNT] * config.ratio * score
    if roundType == ERoundType.Up then
      targetNum = math.ceil(targetNum)
    elseif roundType == ERoundType.Down then
      targetNum = math.floor(targetNum)
    elseif roundType == ERoundType.Down_1 then
      targetNum = math.max(math.floor(targetNum), 1)
    else
      targetNum = math.floor(targetNum + 0.5)
    end
    if targetNum <= 0 then
      return nil
    end
    reward[PROPERTY_COUNT] = targetNum
  end
  return reward
end

function ActivityTokenHelper.GetRatioRewards(config, score)
  local rewards = {}
  for _, reward in ipairs(config.rewardsWeight) do
    rewards[#rewards + 1] = ActivityTokenHelper.TryUseRatioReward(reward, config, score)
  end
  return rewards
end

function ActivityTokenHelper:IsAcquireTypeValid(type)
  if type == EActTokenAcquireType.CostEnergy then
    return self.m_costEnergyCfg ~= nil and self.m_costEnergyCfg.energyNum ~= nil
  elseif type == EActTokenAcquireType.ProducerClick then
    return self.m_costEnergyCfg ~= nil and self.m_costEnergyCfg.clickNum ~= nil
  elseif type == EActTokenAcquireType.FinishOrder then
    return not Table.IsEmpty(self.m_arrOrderCfgs)
  elseif type == EActTokenAcquireType.Merge then
    return not Table.IsEmpty(self.m_arrMergeCfgs)
  elseif type == EActTokenAcquireType.ShopItem then
    return not Table.IsEmpty(self.m_arrShopItemCfgs)
  end
end

function ActivityTokenHelper:CheckScoreConfig(arrConfig)
  if Table.IsEmpty(arrConfig) or not GameConfig.IsTestMode() then
    return
  end
  local tmpConfig = Table.ShallowCopy(arrConfig)
  table.sort(tmpConfig, function(a, b)
    local minA = a.score_min or math.mininteger
    local minB = b.score_min or math.mininteger
    return minA < minB
  end)
  local getMinMaxFunc = function(config)
    return config.score_min or math.mininteger, config.score_max or math.maxinteger
  end
  local scoreMin, scoreMax, lastMax
  for _, config in ipairs(tmpConfig) do
    scoreMin, scoreMax = getMinMaxFunc(config)
    if scoreMin > scoreMax then
      Log.Error("[ActivityTokenHelper] 配置出错, score_min的值大于score_max的值，请检查！")
    end
    if lastMax ~= nil and math.abs(lastMax - scoreMin) > 0.001 then
      Log.Error("[ActivityTokenHelper] 配置出错, score的总区间不连续请检查！")
    end
    lastMax = scoreMax
  end
end
