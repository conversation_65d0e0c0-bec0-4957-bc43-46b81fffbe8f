BakeOutResultWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BaseWindow)
BakeOutResultWindow.__index = BakeOutResultWindow

function BakeOutResultWindow:BeforeOpenCheck()
  local model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  return model ~= nil and model:GetFinalRankInfos() ~= nil
end

function BakeOutResultWindow:Init(userClick)
  if GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    GM.SceneManager:ChangeGameMode(EGameMode.Main)
  end
  self.m_model = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  self.m_model:SetWindowOpened()
  local finalRankDatas = self.m_model:GetFinalRankInfos()
  local ownRankData = self.m_model:GetOwnDataFromRanksData(finalRankDatas)
  if ownRankData ~= nil then
    self.m_podium:UpdateContent(ownRankData)
    self.m_resultText.text = GM.GameTextModel:GetText("bakeout_result", ownRankData.rank, #finalRankDatas or 0, GM.UserProfileModel:GetName())
  end
  local arrWindows = self.m_model:GetWaitingOpenWindowDatas()
  local index = 1
  while index <= #arrWindows do
    if arrWindows[index].name == UIPrefabConfigName.BakeOutResultWindow then
      table.remove(arrWindows, index)
    else
      index = index + 1
    end
  end
  self:LogWindowAction(EBIType.UIActionType.Open, {
    userClick and EBIReferType.UserClick or EBIReferType.AutoPopup
  }, self.m_model:GetId())
  self.m_bInited = true
  self.m_role:Init()
  self.m_role:PlayEnterAnimation()
end

function BakeOutResultWindow:OnCloseView()
  if self.m_bClosing then
    return
  end
  self.m_bClosing = true
  self.m_model:AcquireReward(function()
    BaseWindow.OnCloseView(self)
    self.m_model:TryUpdateHint()
  end)
end
