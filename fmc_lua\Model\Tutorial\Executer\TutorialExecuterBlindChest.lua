local Step = {
  HighlightKey = "1",
  HighlightChest = "2",
  HighlightTopRewards = "3"
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.BlindChestClickSlot, self, self._StepHighlightTopRewards)
  for _, activityDefinition in pairs(BlindChestDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnBlindChestStateChanged)
  end
end

function Executer:_OnOpenView(message)
  for activityType, activityDefinition in pairs(BlindChestDefinition) do
    if message.name == activityDefinition.MainWindowPrefabName then
      self.m_activityModel = GM.ActivityManager:GetModel(activityType)
      self.m_mainWindow = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
      self.m_activityDefinition = activityDefinition
      self.m_activityType = activityType
      self:_StepHighlightKey()
    end
  end
end

function Executer:_OnBlindChestStateChanged(message)
  if self.m_activityModel == nil then
    return
  end
  self:_Finish()
end

function Executer:_StepHighlightKey()
  self:SetStrongTutorial(true)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  self.m_strOngoingDatas = Step.HighlightKey
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local callback = function()
      if self.m_keyNode ~= nil and not self.m_keyNode:IsNull() then
        TutorialHelper.DehighlightForUI(self.m_keyNode)
        self.m_keyNode = nil
      end
      self:_StepHighlightChest()
    end
    TutorialHelper.WholeMask(callback)
    local keyNode = self.m_mainWindow:GetKeyButtonTrans()
    TutorialHelper.HighlightForUI(keyNode, true)
    self.m_keyNode = keyNode
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(self.m_activityType .. "_guide_1"), 50)
  end, 1.5)
end

function Executer:_StepHighlightChest()
  self.m_strOngoingDatas = Step.HighlightChest
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.HideTutorialLayer()
  local slotTrans = self.m_mainWindow:GetSlotTouchArea()
  TutorialHelper.WholeMask()
  TutorialHelper.HighlightForUI(slotTrans)
  self.m_slotTrans = slotTrans
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(slotTrans)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  local text = GM.GameTextModel:GetText(self.m_activityType .. "_guide_2")
  TutorialHelper.ShowDialog(text, 70)
end

function Executer:_StepHighlightTopRewards()
  if self.m_slotTrans ~= nil and not self.m_slotTrans:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_slotTrans)
    self.m_slotTrans = nil
  end
  TutorialHelper.HideTutorialLayer(self.m_gesture)
  self.m_gesture = nil
  self.m_strOngoingDatas = Step.HighlightTopRewards
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local callback = function()
      self.m_mainWindow:PopupFinalRewardTip()
      self:_Finish()
    end
    local topRewardNode = self.m_mainWindow:GetRewardGroup()
    TutorialHelper.WholeMask(callback)
    TutorialHelper.HighlightForUI(topRewardNode, true)
    self.m_topRewardTrans = topRewardNode
    local text = GM.GameTextModel:GetText(self.m_activityType .. "_guide_3")
    TutorialHelper.ShowDialog(text, 50)
  end, 1.5)
end

function Executer:_Finish()
  if self.m_keyNode ~= nil and not self.m_keyNode:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_keyNode)
    self.m_keyNode = nil
  end
  if self.m_slotTrans ~= nil and not self.m_slotTrans:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_slotTrans)
    self.m_slotTrans = nil
  end
  if self.m_topRewardTrans ~= nil and not self.m_topRewardTrans:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_topRewardTrans)
    self.m_topRewardTrans = nil
  end
  self:Finish(self.m_gesture)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
