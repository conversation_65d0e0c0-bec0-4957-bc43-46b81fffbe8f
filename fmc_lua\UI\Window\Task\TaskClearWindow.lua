TaskClearWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
TaskClearWindow.__index = TaskClearWindow

function TaskClearWindow:Init()
  local desc
  local preTipTime = GM.ChapterDataModel:GetPreTipTime()
  if preTipTime == nil then
    desc = GM.GameTextModel:GetText("no_task_tip")
  else
    desc = GM.GameTextModel:GetText("no_task_pretip", preTipTime)
  end
  self.m_desc.text = desc
end

function TaskClearWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  if bakeOutModel ~= nil then
    bakeOutModel:OnReadyStart()
  end
end
