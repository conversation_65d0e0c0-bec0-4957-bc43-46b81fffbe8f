SurveyModel = {}
SurveyModel.__index = SurveyModel
ESurveyDataKey = {
  Id = "Id",
  DataDate = "DataDate",
  PopCount = "PopCount",
  PopTime = "PopTime"
}
EQuestionType = {
  SingleChoice = "SingleChoice",
  MultipleChoice = "MultipleChoice",
  MultipleChoiceFillBlank = "MultipleChoiceFillBlank",
  FillBlank = "FillBlank",
  PictureFillBlank = "PictureFillBlank",
  PictureSingleChoice = "PictureSingleChoice",
  PictureMultipeChoice = "PictureMultipleChoice",
  PictureSingleChoiceFillBlank = "PictureSingleChoiceFillBlank",
  PictureMultipChoiceFillBlank = "PictureMultipChoiceFillBlank"
}
EAnswerKey = {AnserId = "answer_id", Vmemo = "vmemo"}
SurveyNeedOpen = 1

function SurveyModel:Init()
  self.m_dbKVTable = GM.DBTableManager:GetTable(EDBTableConfigs.Survey)
  ModelHelper.DefineProperties(SurveyModel, ESurveyDataKey)
  self.event = PairEvent.Create(self)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnChangeGameModeFinished)
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self.OnLoginFinished)
end

function SurveyModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function SurveyModel:OnChangeGameModeFinished()
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    self:TryPopWindow()
  end
end

function SurveyModel:OnLoginFinished()
  if self.m_serverConfig == nil then
    return
  end
  if self:_CanPopNecessary() then
    self:_RequestQuestionCtx()
  end
end

function SurveyModel:GetData()
  return self.m_dbKVTable
end

function SurveyModel:LoadServerConfig()
  if GM.UIManager and (GM.UIManager:IsViewExisting(UIPrefabConfigName.SurveyTipWindow) or GM.UIManager:IsViewExisting(UIPrefabConfigName.SurveyWindow)) then
    return
  end
  self.m_serverConfig = nil
  self.questionConfig = nil
  local configs = GM.ConfigModel:GetServerConfig(ServerConfigKey.Survey)
  if configs and 0 < #configs then
    self.m_serverConfig = configs[1]
  else
    return
  end
  self.rewards = ConfigUtil.GetCurrencyFromArrStr(self.m_serverConfig.reward)
  local date = TimeUtil.ToDate(GM.GameModel:GetServerTime(), ETimeFormat.YMD)
  local cacheId = self:GetIdInNumber()
  if cacheId ~= self.m_serverConfig.id or date ~= self:GetDataDate() then
    local content = {
      [ESurveyDataKey.Id] = {
        value = tostring(self.m_serverConfig.id)
      },
      [ESurveyDataKey.DataDate] = {value = date},
      [ESurveyDataKey.PopCount] = {value = "0"}
    }
    self.m_dbKVTable:BatchSet(content)
  end
end

function SurveyModel:GetRewardKey()
  return ServerConfigKey.Survey .. "_" .. tostring(self.m_serverConfig.id)
end

function SurveyModel:TryPopWindow()
  if not self.m_serverConfig or not self.questionConfig then
    return
  end
  if not GM.UIManager then
    return
  end
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.SurveyTipWindow) or GM.UIManager:IsViewExisting(UIPrefabConfigName.SurveyWindow) then
    return
  end
  if not self:_CanPopNecessary() then
    return
  end
  self.curQuestionData = self.questionConfig.questions[1]
  self.nextQuestionIds = {}
  self.arrUserAnswers = {}
  self:_UpdateLastFlag()
  self.event:Call(SurveyNeedOpen)
  local content = {
    [ESurveyDataKey.PopCount] = {
      value = tostring(self:GetPopCountInNumber() + 1)
    },
    [ESurveyDataKey.PopTime] = {
      value = tostring(GM.GameModel:GetServerTime())
    }
  }
  self.m_dbKVTable:BatchSet(content)
end

function SurveyModel:GetBIData(withAnswer)
  local tbData = {}
  tbData.survey_id = self.m_serverConfig.id
  if withAnswer then
    tbData.question_list = self.arrUserAnswers
  end
  return json.encode(tbData)
end

function SurveyModel:StoreAnswerAndStepForward(answers, nextQIds)
  self:_StoreAnswer(answers, nextQIds)
  if #self.nextQuestionIds > 0 then
    self.curQuestionData = self:_GetQuestionDataById(self.nextQuestionIds[1])
    table.remove(self.nextQuestionIds, 1)
  else
    self.curQuestionData = self:_GetNextVisibleQuestionData(self.curQuestionData.id)
  end
  self:_UpdateLastFlag()
end

function SurveyModel:StoreAnswerAndSubmit(answers, nextQIds, callback)
  self:_StoreAnswer(answers, nextQIds)
  GM.RewardModel:ReceiveReward(self:GetRewardKey(), function(eRewardRespStatus)
    if eRewardRespStatus == ERewardRespStatus.Success then
      RewardApi.AcquireRewards(self.rewards, EPropertySource.Give, EBIType.Survey)
      self.questionConfig = nil
    end
    callback(eRewardRespStatus)
  end)
end

function SurveyModel:_UpdateLastFlag()
  if self.curQuestionData.isLast ~= nil then
    self.isLastQuestion = self.curQuestionData.isLast
  else
    self.isLastQuestion = self.curQuestionData == self.questionConfig.questions[#self.questionConfig.questions]
  end
end

function SurveyModel:_StoreAnswer(answers, nextQIds)
  local curQData = self.curQuestionData
  Table.ListAppend(self.nextQuestionIds, nextQIds)
  local answerData = {}
  answerData.question_id = curQData.id
  if answers[EAnswerKey.AnserId] then
    answerData[EAnswerKey.AnserId] = answers[EAnswerKey.AnserId]
  end
  if answers[EAnswerKey.Vmemo] then
    answerData[EAnswerKey.Vmemo] = answers[EAnswerKey.Vmemo]
  end
  answerData.question_type = curQData.attribute
  answerData.question_rule = curQData.rule
  self.arrUserAnswers[#self.arrUserAnswers + 1] = answerData
end

function SurveyModel:_GetNextVisibleQuestionData(curQId)
  local data
  local started = false
  for i = 1, #self.questionConfig.questions do
    data = self.questionConfig.questions[i]
    if data.id == curQId then
      started = true
    elseif started and data.isVisible then
      return data
    end
  end
end

function SurveyModel:_GetQuestionDataById(id)
  local data
  for i = 1, #self.questionConfig.questions do
    data = self.questionConfig.questions[i]
    if data.id == id then
      return data
    end
  end
end

function SurveyModel:_CanPopNecessary()
  if GM.RewardModel:HasReceivedReward(self:GetRewardKey()) then
    return false
  end
  if self:GetPopCountInNumber() >= self.m_serverConfig.maxNum then
    return false
  end
  local lastPopTime = self:GetPopTimeInNumber()
  local curTime = GM.GameModel:GetServerTime()
  if curTime - lastPopTime < self.m_serverConfig.interval then
    return false
  end
  return true
end

function SurveyModel:_RequestQuestionCtx()
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(self.m_serverConfig.link, "GET", 8000, 0)
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    if reqCtx.Rcode == ResultCode.Succeeded and reqCtx.RespBody.Bytes > 0 then
      local question = json.decode(reqCtx:GetResponseString()) or {}
      if question.questions ~= nil and 0 < #question.questions then
        self.questionConfig = question
        if GM.SceneManager:GetGameMode() == EGameMode.Main then
          self:TryPopWindow()
        end
      end
    else
      self.m_serverConfig = nil
    end
  end)
  reqCtx:Send()
end

function SurveyModel:_LoadQuestionCtx()
  GM.ResourceLoader:LoadLatestFile(GM.DataResource.TextAssetConfig:GetConfig(TextAssetConfigName.question), function(data)
    self.questionConfig = json.decode(data)
  end)
end
