PassActivityDefinition = {}
local CreateActivityDefinition = function(activityType, prefix, overrideTable)
  local BaseTable = {
    Prefix = prefix,
    EntryButtonKey = ESceneViewHudButtonKey[prefix],
    ActivityDataTableName = VirtualDBTableName[prefix],
    ActivityTokenPropertyType = EPropertyType[prefix .. "ScoreToken"],
    TicketPropertyType = EPropertyType[prefix .. "Ticket"],
    PlusTicketPropertyType = EPropertyType[prefix .. "PlusTicket"],
    StateChangedEvent = prefix .. "StateChanged",
    ShouldRefreshTimelimitTasksEvent = prefix .. "ShouldRefreshTimelimitTasks",
    RefreshTimelimitTasksEvent = prefix .. "RefreshTimelimitTasks",
    TaskProgressChangedEvent = prefix .. "TaskProgressChanged",
    CanFinishTaskNumberChangedEvent = prefix .. "CanFinishTaskNumberChanged",
    CanTakeRewardNumberChangedEvent = prefix .. "CanTakeRewardNumberChanged",
    BuyTicketSuccessEvent = prefix .. "BuyTicketSuccess",
    BuyMaxTicketSuccessEvent = prefix .. "BuyMaxTicketSuccess",
    BuyUpTicketSuccessEvent = prefix .. "BuyUpTicketSuccess",
    TakeRewardBIType = EBIType[prefix .. "TakeReward"],
    TakeExtraRewardBIType = EBIType[prefix .. "TakeExtraReward"],
    MapEntryPrefabName = UIPrefabConfigName[prefix .. "Entry"],
    BoardEntryPrefabName = UIPrefabConfigName[prefix .. "ActivityBoardBubble"],
    ShopCellPrefabName = UIPrefabConfigName[prefix .. "ShopCell"],
    MainWindowPrefabName = UIPrefabConfigName[prefix .. "MainWindow"],
    ReadyWindowPrefabName = UIPrefabConfigName[prefix .. "ReadyWindow"],
    EndWindowPrefabName = UIPrefabConfigName[prefix .. "EndWindow"],
    NewTimelimitTaskWindowPrefabName = UIPrefabConfigName[prefix .. "NewTimelimitTaskWindow"],
    FinishTaskWindowPrefabName = UIPrefabConfigName[prefix .. "FinishTaskWindow"],
    TimelimitNoticeBubblePrefabName = UIPrefabConfigName[prefix .. "TimelimitNoticeBubble"],
    CycleNoticeBubblePrefabName = UIPrefabConfigName[prefix .. "CycleNoticeBubble"],
    BuyTicketWindowPrefabName = UIPrefabConfigName[prefix .. "BuyTicketWindow"],
    BuyTicketSuccessWindow1PrefabName = UIPrefabConfigName.PassActivityBuyTicketSuccessWindow1,
    BuyTicketSuccessWindow2PrefabName = UIPrefabConfigName[prefix .. "BuyTicketSuccessWindow2"],
    BuyTicketPopupWindowPrefabName = UIPrefabConfigName[prefix .. "BuyTicketPopupWindow"],
    BuyMaxTicketWindowPrefabName = UIPrefabConfigName.HasConfig(prefix .. "BuyMaxTicketWindow") and UIPrefabConfigName[prefix .. "BuyMaxTicketWindow"] or nil,
    BuyUpTicketWindowPrefabName = UIPrefabConfigName.HasConfig(prefix .. "BuyUpTicketWindow") and UIPrefabConfigName[prefix .. "BuyUpTicketWindow"] or nil,
    BuyUpTicketSuccessWindowPrefabName = UIPrefabConfigName.HasConfig(prefix .. "BuyUpTicketSuccessWindow") and UIPrefabConfigName[prefix .. "BuyUpTicketSuccessWindow"] or nil,
    BuyMaxTicketSuccessWindowPrefabName = UIPrefabConfigName.HasConfig(prefix .. "BuyMaxTicketSuccessWindow") and UIPrefabConfigName[prefix .. "BuyMaxTicketSuccessWindow"] or nil,
    RewardRecoverWindowPrefabName = UIPrefabConfigName[prefix .. "RewardRecoverWindow"],
    TokenWindowPrefabName = UIPrefabConfigName[prefix .. "TokenWindow"],
    RewardGroupTopPadding = 104,
    RewardCellHeight = 241,
    ExtraRewardBoxBottomPadding = 600,
    PreviousProgressTextColor = "FFFFFF",
    PreviousProgressTextOutlineColor = "00000073",
    TokenIconName = ImageFileConfigName[activityType .. "_battle_pass_token"],
    LockedUnpayTextKey = "battlepass_redesc_locked_unpay",
    TutorialTextKey = activityType .. "_tutorial_%d",
    TittleTextKey = activityType .. "_title",
    ResourceLabels = {
      AddressableLabel.BPCommon,
      AddressableLabel[prefix]
    },
    TutorialStartCondition = ETutorialStartCondition.PassStart
  }
  BaseTable.__index = BaseTable
  PassActivityDefinition[activityType] = setmetatable(overrideTable, BaseTable)
  PassActivityDefinition[activityType].__index = PassActivityDefinition[activityType]
end
CreateActivityDefinition(ActivityType.BP1, "BP1", {})
CreateActivityDefinition(ActivityType.BP2, "BP2", {
  TutorialTextKey = ActivityType.BP1 .. "_tutorial_%d",
  TittleTextKey = ActivityType.BP1 .. "_title"
})
CreateActivityDefinition(ActivityType.BP3, "BP3", {})
CreateActivityDefinition(ActivityType.BP4, "BP4", {
  TutorialTextKey = ActivityType.BP3 .. "_tutorial_%d",
  TittleTextKey = ActivityType.BP3 .. "_title"
})
CreateActivityDefinition(ActivityType.BP5, "BP5", {})
CreateActivityDefinition(ActivityType.BP6, "BP6", {})
CreateActivityDefinition(ActivityType.BP7, "BP7", {})
CreateActivityDefinition(ActivityType.BP8, "BP8", {})
