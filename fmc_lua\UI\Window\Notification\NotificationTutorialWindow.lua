NotificationTutorialWindow = setmetatable({}, BaseWindow)
NotificationTutorialWindow.__index = NotificationTutorialWindow

function NotificationTutorialWindow:Init()
  EventDispatcher.AddListener(EEventType.ApplicationWillEnterForeground, self, self.ApplicationWillEnterForeground)
end

function NotificationTutorialWindow:ApplicationWillEnterForeground()
  if PlatformInterface.IsNotificationsEnabled() then
    self:Close()
  end
end

function NotificationTutorialWindow:OnTurnOnButtonClicked()
  GM.NotificationModel:TryOpenNotification(true)
end

function NotificationTutorialWindow:OnCloseBtnClick()
  self:Close()
end
