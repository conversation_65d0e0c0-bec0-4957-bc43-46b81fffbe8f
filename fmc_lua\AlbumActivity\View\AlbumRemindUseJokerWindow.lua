AlbumRemindUseJokerWindow = setmetatable({}, AlbumActivityBaseWindow)
AlbumRemindUseJokerWindow.__index = AlbumRemindUseJokerWindow

function AlbumRemindUseJokerWindow:BeforeOpenCheck()
  local model = AlbumActivityModel.GetActiveModel()
  if model ~= nil then
    return true
  end
  return false
end

function AlbumRemindUseJokerWindow:Init(activityType, pWin)
  AlbumActivityBaseWindow.Init(self, activityType)
  self.m_parentWin = pWin
  local jokerGetTime = self.m_model:GetMinJokerCardTime()
  local nextStateTime = self.m_model:GetNextStateTime()
  self.m_jokerEndTime = jokerGetTime + Sec2Day
  self.m_jokerEndTime = math.min(self.m_jokerEndTime, nextStateTime)
  self.m_jokerCount = self.m_model:GetJokerCount()
  self.m_descText.text = GM.GameTextModel:GetText("joker_card_waiting_notice", self.m_jokerCount)
  self:UpdatePerSecond()
end

function AlbumRemindUseJokerWindow:UpdatePerSecond()
  if self.m_jokerEndTime then
    local delta = math.max(0, self.m_jokerEndTime - GM.GameModel:GetServerTime())
    if delta == 0 then
      if self.m_closeBtnGo.activeSelf then
        self.m_countdownText.text = GM.GameTextModel:GetText("joker_card_time_expired")
        self.m_descText.text = GM.GameTextModel:GetText("joker_card_expired_notice", self.m_jokerCount)
        self.m_closeBtnGo:SetActive(false)
        self.canCloseByAndroidBack = false
      end
    else
      local str = TimeUtil.ParseTimeDescription(delta, 2, false, false)
      self.m_countdownText.text = GM.GameTextModel:GetText("joker_card_timing", str)
    end
  end
end

function AlbumRemindUseJokerWindow:OnCloseFinish()
  if self.m_openPrefabName ~= nil then
    AlbumActivityModel.UseNewJokerCard = false
    if not GM.UIManager:IsViewExisting(self.m_activityDefinition.MainWindowPrefabName) then
      GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, false, function()
        GM.UIManager:OpenView(self.m_openPrefabName, self.m_activityType, false, nil, self.m_jokerEndTime)
      end)
    else
      GM.UIManager:OpenView(self.m_openPrefabName, self.m_activityType, false, nil, self.m_jokerEndTime)
    end
  end
  BaseWindow.OnCloseFinish(self)
end

function AlbumRemindUseJokerWindow:OnBtnClicked()
  self.m_openPrefabName = UIPrefabConfigName.AlbumJokerExchangeWindow
  self:Close()
end
