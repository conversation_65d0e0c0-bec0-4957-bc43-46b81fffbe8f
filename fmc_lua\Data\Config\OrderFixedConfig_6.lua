return {
  {
    Id = "60010",
    GroupId = 1,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "60020",
    GroupId = 1,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "it_2_1_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60030",
    GroupId = 1,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e1preingre_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "60040",
    GroupId = 1,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "60050",
    GroupId = 1,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60060",
    GroupId = 1,
    ChapterId = 6,
    Requirement_1 = {Type = "ds_e5mt_2", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_5",
      Count = 1
    }
  },
  {
    Id = "60070",
    GroupId = 1,
    ChapterId = 6,
    Requirement_1 = {Type = "ds_fd_10", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "60080",
    GroupId = 2,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "60090",
    GroupId = 2,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60100",
    GroupId = 2,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_18",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "60110",
    GroupId = 2,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "60120",
    GroupId = 2,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_13",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60130",
    GroupId = 2,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "it_5_2_7", Count = 1}
  },
  {
    Id = "60140",
    GroupId = 2,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e1sala_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "60150",
    GroupId = 3,
    ChapterId = 6,
    Requirement_1 = {Type = "it_4_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60160",
    GroupId = 3,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "60170",
    GroupId = 3,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_23",
      Count = 1
    }
  },
  {
    Id = "60180",
    GroupId = 3,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "60190",
    GroupId = 3,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60200",
    GroupId = 3,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e6dst_5",
      Count = 1
    }
  },
  {
    Id = "60210",
    GroupId = 3,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e5mt_2", Count = 1}
  },
  {
    Id = "60220",
    GroupId = 4,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_4", Count = 1}
  },
  {
    Id = "60230",
    GroupId = 4,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_14",
      Count = 1
    }
  },
  {
    Id = "60240",
    GroupId = 4,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60250",
    GroupId = 4,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e6preingre_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_8", Count = 1}
  },
  {
    Id = "60260",
    GroupId = 4,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "60270",
    GroupId = 4,
    ChapterId = 6,
    Requirement_1 = {Type = "it_4_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60280",
    GroupId = 4,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_19",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    }
  },
  {
    Id = "60290",
    GroupId = 5,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "60300",
    GroupId = 5,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_27",
      Count = 1
    },
    Requirement_2 = {Type = "it_5_2_7", Count = 1}
  },
  {
    Id = "60310",
    GroupId = 5,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60320",
    GroupId = 5,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "60330",
    GroupId = 5,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_14",
      Count = 1
    }
  },
  {
    Id = "60340",
    GroupId = 5,
    ChapterId = 6,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_6e6rice_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60350",
    GroupId = 5,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "it_a6_1_6", Count = 1}
  },
  {
    Id = "60360",
    GroupId = 6,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {Type = "it_a6_1_5", Count = 1}
  },
  {
    Id = "60370",
    GroupId = 6,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "60380",
    GroupId = 6,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60390",
    GroupId = 6,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_17",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "60400",
    GroupId = 6,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "60410",
    GroupId = 6,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60420",
    GroupId = 6,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "60430",
    GroupId = 7,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "ds_fd_10", Count = 1}
  },
  {
    Id = "60440",
    GroupId = 7,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "60450",
    GroupId = 7,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {Type = "it_a6_1_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60460",
    GroupId = 7,
    ChapterId = 6,
    Requirement_1 = {Type = "it_4_2_4", Count = 1},
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "60470",
    GroupId = 7,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "60480",
    GroupId = 7,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_7",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60490",
    GroupId = 7,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_14",
      Count = 1
    }
  },
  {
    Id = "60500",
    GroupId = 8,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    }
  },
  {
    Id = "60510",
    GroupId = 8,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "60520",
    GroupId = 8,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e3preingre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60530",
    GroupId = 8,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "60540",
    GroupId = 8,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60550",
    GroupId = 8,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4flb_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "60560",
    GroupId = 8,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_18",
      Count = 1
    }
  },
  {
    Id = "60570",
    GroupId = 9,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "60580",
    GroupId = 9,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_8",
      Count = 1
    }
  },
  {
    Id = "60590",
    GroupId = 9,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_chopfru_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60600",
    GroupId = 9,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_23",
      Count = 1
    }
  },
  {
    Id = "60610",
    GroupId = 9,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "60620",
    GroupId = 9,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e2assort_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60630",
    GroupId = 9,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "60640",
    GroupId = 10,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "60650",
    GroupId = 10,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_6e1icytre_5",
      Count = 1
    }
  },
  {
    Id = "60660",
    GroupId = 10,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60670",
    GroupId = 10,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_1",
      Count = 1
    }
  },
  {
    Id = "60680",
    GroupId = 10,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60690",
    GroupId = 10,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "60700",
    GroupId = 10,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "ds_fd_19", Count = 1}
  },
  {
    Id = "60710",
    GroupId = 11,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "it_4_2_3", Count = 1}
  },
  {
    Id = "60720",
    GroupId = 11,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60730",
    GroupId = 11,
    ChapterId = 6,
    Requirement_1 = {Type = "it_6_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "60740",
    GroupId = 11,
    ChapterId = 6,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60750",
    GroupId = 11,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "60760",
    GroupId = 11,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4stewmt_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "60770",
    GroupId = 11,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "60780",
    GroupId = 12,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "60790",
    GroupId = 12,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_4",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60800",
    GroupId = 12,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    }
  },
  {
    Id = "60810",
    GroupId = 12,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "60820",
    GroupId = 12,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60830",
    GroupId = 12,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e1icytre_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "60840",
    GroupId = 12,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "60850",
    GroupId = 13,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "60860",
    GroupId = 13,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60870",
    GroupId = 13,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_14",
      Count = 1
    }
  },
  {
    Id = "60880",
    GroupId = 13,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "60890",
    GroupId = 13,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_20",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_7", Count = 1}
  },
  {
    Id = "60900",
    GroupId = 13,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {Type = "ds_fd_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60910",
    GroupId = 13,
    ChapterId = 6,
    Requirement_1 = {Type = "it_5_2_7", Count = 1},
    Requirement_2 = {Type = "it_a6_1_7", Count = 1}
  },
  {
    Id = "60920",
    GroupId = 14,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "60930",
    GroupId = 14,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e6dst_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60940",
    GroupId = 14,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_4",
      Count = 1
    }
  },
  {
    Id = "60950",
    GroupId = 14,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_11",
      Count = 1
    }
  },
  {
    Id = "60960",
    GroupId = 14,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "60970",
    GroupId = 14,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e1dst_4",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_6", Count = 1}
  },
  {
    Id = "60980",
    GroupId = 14,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    }
  },
  {
    Id = "60990",
    GroupId = 15,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "61000",
    GroupId = 15,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61010",
    GroupId = 15,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "61020",
    GroupId = 15,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_6",
      Count = 1
    }
  },
  {
    Id = "61030",
    GroupId = 15,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "61040",
    GroupId = 15,
    ChapterId = 6,
    Requirement_1 = {Type = "it_6_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61050",
    GroupId = 15,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_26",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_4", Count = 1}
  },
  {
    Id = "61060",
    GroupId = 16,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "61070",
    GroupId = 16,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4assort_3",
      Count = 1
    }
  },
  {
    Id = "61080",
    GroupId = 16,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61090",
    GroupId = 16,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_4", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_23",
      Count = 1
    }
  },
  {
    Id = "61100",
    GroupId = 16,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_21",
      Count = 1
    }
  },
  {
    Id = "61110",
    GroupId = 16,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61120",
    GroupId = 16,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_18",
      Count = 1
    }
  },
  {
    Id = "61130",
    GroupId = 17,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "61140",
    GroupId = 17,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61150",
    GroupId = 17,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_10",
      Count = 1
    }
  },
  {
    Id = "61160",
    GroupId = 17,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e1icytre_5",
      Count = 1
    }
  },
  {
    Id = "61170",
    GroupId = 17,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61180",
    GroupId = 17,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4friedmt_9",
      Count = 1
    }
  },
  {
    Id = "61190",
    GroupId = 17,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "61200",
    GroupId = 18,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "61210",
    GroupId = 18,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {Type = "it_5_2_7", Count = 1}
  },
  {
    Id = "61220",
    GroupId = 18,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_18", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61230",
    GroupId = 18,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_24",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "61240",
    GroupId = 18,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "61250",
    GroupId = 18,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e2mt_14",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61260",
    GroupId = 18,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "61270",
    GroupId = 19,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    }
  },
  {
    Id = "61280",
    GroupId = 19,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_2",
      Count = 1
    }
  },
  {
    Id = "61290",
    GroupId = 19,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61300",
    GroupId = 19,
    ChapterId = 6,
    Requirement_1 = {Type = "it_1_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_23",
      Count = 1
    }
  },
  {
    Id = "61310",
    GroupId = 19,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61320",
    GroupId = 19,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_3_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "61330",
    GroupId = 19,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_6e5dst_6",
      Count = 1
    }
  },
  {
    Id = "61340",
    GroupId = 20,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "61350",
    GroupId = 20,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_16",
      Count = 1
    },
    Requirement_2 = {Type = "ds_juice_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61360",
    GroupId = 20,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_12",
      Count = 1
    }
  },
  {
    Id = "61370",
    GroupId = 20,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_18",
      Count = 1
    }
  },
  {
    Id = "61380",
    GroupId = 20,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_23",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61390",
    GroupId = 20,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "61400",
    GroupId = 20,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e5pasta_30",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_2_5", Count = 1}
  },
  {
    Id = "61410",
    GroupId = 21,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "61420",
    GroupId = 21,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4friedmt_8",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_8", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61430",
    GroupId = 21,
    ChapterId = 6,
    Requirement_1 = {Type = "ds_fd_15", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "61440",
    GroupId = 21,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_23",
      Count = 1
    }
  },
  {
    Id = "61450",
    GroupId = 21,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61460",
    GroupId = 21,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e1icytre_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "61470",
    GroupId = 21,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "61480",
    GroupId = 22,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "61490",
    GroupId = 22,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e1assort_2",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_4", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61500",
    GroupId = 22,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {Type = "it_6_1_8", Count = 1}
  },
  {
    Id = "61510",
    GroupId = 22,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_14",
      Count = 1
    }
  },
  {
    Id = "61520",
    GroupId = 22,
    ChapterId = 6,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61530",
    GroupId = 22,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "61540",
    GroupId = 22,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    }
  },
  {
    Id = "61550",
    GroupId = 23,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_3",
      Count = 1
    }
  },
  {
    Id = "61560",
    GroupId = 23,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61570",
    GroupId = 23,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "61580",
    GroupId = 23,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillve_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_23",
      Count = 1
    }
  },
  {
    Id = "61590",
    GroupId = 23,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61600",
    GroupId = 23,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "61610",
    GroupId = 23,
    ChapterId = 6,
    Requirement_1 = {Type = "ds_e6sf_18", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "61620",
    GroupId = 24,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_3",
      Count = 1
    }
  },
  {
    Id = "61630",
    GroupId = 24,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e4pasta_28",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61640",
    GroupId = 24,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "61650",
    GroupId = 24,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_6e6rice_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_3",
      Count = 1
    }
  },
  {
    Id = "61660",
    GroupId = 24,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61670",
    GroupId = 24,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "61680",
    GroupId = 24,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_2", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_18",
      Count = 1
    }
  },
  {
    Id = "61690",
    GroupId = 25,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "61700",
    GroupId = 25,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_mixdrk_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e4pasta_22",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61710",
    GroupId = 25,
    ChapterId = 6,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_23",
      Count = 1
    }
  },
  {
    Id = "61720",
    GroupId = 25,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "61730",
    GroupId = 25,
    ChapterId = 6,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "61740",
    GroupId = 25,
    ChapterId = 6,
    Requirement_1 = {Type = "it_7_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4pasta_29",
      Count = 1
    }
  },
  {
    Id = "61750",
    GroupId = 25,
    ChapterId = 6,
    Requirement_1 = {
      Type = "ds_mixdrk_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  }
}
