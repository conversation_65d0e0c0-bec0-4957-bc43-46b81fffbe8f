OrderStateHelper = {}

function OrderStateHelper.TryFillOrderRequirement(boardModel, requirement, needCount, includeStore)
  if GM.ItemDataModel:IsDisposableInstrument(requirement) then
    includeStore = includeStore == nil and true or includeStore
    local items = boardModel:FilterItemsByType(requirement, function(item)
      return item:IsEmptyInstrument()
    end, needCount, includeStore)
    local existCount = #items
    if needCount <= existCount then
      return true
    else
      return false, existCount
    end
  else
    return true
  end
end
