return {
  {
    Type = "eb1_1_1",
    MergedType = "eb1_1_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb1_1_2",
    MergedType = "eb1_1_3",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb1_1_3",
    MergedType = "eb1_1_4",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb1_1_4",
    MergedType = "eb1_1_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb1_1_5",
    MergedType = "eb1_1_6",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb1_1_6",
    MergedType = "eb1_1_7",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "ene_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb1_1_7",
    MergedType = "eb1_1_8",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb1_1_8",
    MergedType = "eb1_1_9",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {
        Code = "additem_1_1",
        Weight = 1
      },
      {Code = "eb1_2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb1_1_9",
    MergedType = "eb1_1_10",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "ene_1", Weight = 1},
      {Code = "rb#cbox1_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb1_1_10",
    MergedType = "eb1_1_11",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "ene_2", Weight = 1},
      {Code = "rb#cbox2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb1_1_11",
    MergedType = "eb1_1_12",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "ene_3", Weight = 1},
      {Code = "rb#cbox3_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb1_1_12",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "rb#cbox3_1", Weight = 1},
      {
        Code = "rb#greenbox_1",
        Weight = 1
      },
      {
        Code = "rb#greenbox2_1",
        Weight = 1
      }
    },
    Cd = 1,
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    DropsTotal = 3
  },
  {
    Type = "eb1_2_1",
    MergedType = "eb1_2_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb1_1_8"}
  },
  {
    Type = "eb1_2_2",
    MergedType = "eb1_2_3",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb1_1_8"},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb1_2_3",
    MergedType = "eb1_2_4",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb1_1_8"},
    GeneratedItems = {
      {Code = "ene_2", Weight = 1},
      {Code = "gem_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb1_2_4",
    MergedType = "eb1_2_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb1_1_8"},
    GeneratedItems = {
      {Code = "ene_3", Weight = 1},
      {Code = "gem_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb1_2_5",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb1_1_8"},
    GeneratedItems = {
      {Code = "rb#cbox1_1", Weight = 1},
      {
        Code = "rb#greenbox_1",
        Weight = 1
      },
      {Code = "gem_2", Weight = 1}
    },
    Cd = 1,
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    DropsTotal = 3
  },
  {
    Type = "eb2_1_1",
    MergedType = "eb2_1_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb2_1_2",
    MergedType = "eb2_1_3",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb2_1_3",
    MergedType = "eb2_1_4",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb2_1_4",
    MergedType = "eb2_1_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb2_1_5",
    MergedType = "eb2_1_6",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb2_1_6",
    MergedType = "eb2_1_7",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb2_1_7",
    MergedType = "eb2_1_8",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_3", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb2_1_8",
    MergedType = "eb2_1_9",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_3", Weight = 1},
      {
        Code = "additem_1_1",
        Weight = 1
      },
      {Code = "eb2_2_1", Weight = 1}
    },
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    SpreadAuto = 2
  },
  {
    Type = "eb2_1_9",
    MergedType = "eb2_1_10",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1},
      {Code = "rb#cbox1_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb2_1_10",
    MergedType = "eb2_1_11",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1},
      {Code = "rb#cbox2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb2_1_11",
    MergedType = "eb2_1_12",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1", Weight = 1},
      {Code = "rb#cbox2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb2_1_12",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "rb#cbox3_1", Weight = 1},
      {
        Code = "rb#greenbox_1",
        Weight = 1
      },
      {
        Code = "rb#greenbox2_1",
        Weight = 1
      }
    },
    Cd = 1,
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    DropsTotal = 3
  },
  {
    Type = "eb2_2_1",
    MergedType = "eb2_2_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb2_1_8"}
  },
  {
    Type = "eb2_2_2",
    MergedType = "eb2_2_3",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb2_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 1},
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb2_2_3",
    MergedType = "eb2_2_4",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb2_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 1},
      {Code = "skipprop_3", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb2_2_4",
    MergedType = "eb2_2_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb2_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 1},
      {Code = "skipprop_3", Weight = 2}
    },
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    SpreadAuto = 2
  },
  {
    Type = "eb2_2_5",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb2_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 4},
      {Code = "ene_2", Weight = 6}
    },
    Cd = 1,
    InitialNumber = 10,
    Frequency = 1,
    Capacity = 10,
    DropsTotal = 10
  },
  {
    Type = "eb3_1_1",
    MergedType = "eb3_1_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb3_1_2",
    MergedType = "eb3_1_3",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb3_1_3",
    MergedType = "eb3_1_4",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb3_1_4",
    MergedType = "eb3_1_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb3_1_5",
    MergedType = "eb3_1_6",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb3_1_6",
    MergedType = "eb3_1_7",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb3_1_7",
    MergedType = "eb3_1_8",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb3_1_8",
    MergedType = "eb3_1_9",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {
        Code = "additem_1_1",
        Weight = 1
      },
      {Code = "eb3_2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb3_1_9",
    MergedType = "eb3_1_10",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1},
      {Code = "rb#cbox1_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb3_1_10",
    MergedType = "eb3_1_11",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1},
      {Code = "rb#cbox2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb3_1_11",
    MergedType = "eb3_1_12",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1", Weight = 1},
      {Code = "rb#cbox3_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb3_1_12",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "rb#cbox3_1", Weight = 1},
      {
        Code = "rb#greenbox_1",
        Weight = 1
      },
      {
        Code = "rb#greenbox2_1",
        Weight = 1
      }
    },
    Cd = 1,
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    DropsTotal = 3
  },
  {
    Type = "eb3_2_1",
    MergedType = "eb3_2_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb3_1_8"}
  },
  {
    Type = "eb3_2_2",
    MergedType = "eb3_2_3",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb3_1_8"},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb3_2_3",
    MergedType = "eb3_2_4",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb3_1_8"},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1},
      {Code = "ene_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb3_2_4",
    MergedType = "eb3_2_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb3_1_8"},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1},
      {Code = "ene_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb3_2_5",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb3_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 5},
      {Code = "ene_2", Weight = 2},
      {Code = "ene_3", Weight = 1}
    },
    Cd = 1,
    InitialNumber = 8,
    Frequency = 1,
    Capacity = 8,
    DropsTotal = 8
  },
  {
    Type = "eb4_1_1",
    MergedType = "eb4_1_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb4_1_2",
    MergedType = "eb4_1_3",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb4_1_3",
    MergedType = "eb4_1_4",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb4_1_4",
    MergedType = "eb4_1_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb4_1_5",
    MergedType = "eb4_1_6",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb4_1_6",
    MergedType = "eb4_1_7",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb4_1_7",
    MergedType = "eb4_1_8",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_3", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb4_1_8",
    MergedType = "eb4_1_9",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_3", Weight = 1},
      {
        Code = "additem_1_1",
        Weight = 1
      },
      {Code = "eb4_2_1", Weight = 1}
    },
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    SpreadAuto = 2
  },
  {
    Type = "eb4_1_9",
    MergedType = "eb4_1_10",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1},
      {Code = "rb#cbox1_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb4_1_10",
    MergedType = "eb4_1_11",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1},
      {Code = "rb#cbox2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb4_1_11",
    MergedType = "eb4_1_12",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1", Weight = 1},
      {Code = "rb#cbox2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb4_1_12",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "rb#cbox3_1", Weight = 1},
      {
        Code = "rb#greenbox_1",
        Weight = 1
      },
      {
        Code = "rb#greenbox2_1",
        Weight = 1
      }
    },
    Cd = 1,
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    DropsTotal = 3
  },
  {
    Type = "eb4_2_1",
    MergedType = "eb4_2_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb4_1_8"}
  },
  {
    Type = "eb4_2_2",
    MergedType = "eb4_2_3",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb4_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 1},
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb4_2_3",
    MergedType = "eb4_2_4",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb4_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 1},
      {Code = "skipprop_3", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb4_2_4",
    MergedType = "eb4_2_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb4_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 1},
      {Code = "skipprop_3", Weight = 2}
    },
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    SpreadAuto = 2
  },
  {
    Type = "eb4_2_5",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb4_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 4},
      {Code = "ene_2", Weight = 6}
    },
    Cd = 1,
    InitialNumber = 10,
    Frequency = 1,
    Capacity = 10,
    DropsTotal = 10
  },
  {
    Type = "eb5_1_1",
    MergedType = "eb5_1_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb5_1_2",
    MergedType = "eb5_1_3",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb5_1_3",
    MergedType = "eb5_1_4",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb5_1_4",
    MergedType = "eb5_1_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb5_1_5",
    MergedType = "eb5_1_6",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb5_1_6",
    MergedType = "eb5_1_7",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb5_1_7",
    MergedType = "eb5_1_8",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb5_1_8",
    MergedType = "eb5_1_9",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {
        Code = "additem_1_1",
        Weight = 1
      },
      {Code = "eb5_2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb5_1_9",
    MergedType = "eb5_1_10",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1},
      {Code = "rb#cbox1_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb5_1_10",
    MergedType = "eb5_1_11",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1},
      {Code = "rb#cbox2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb5_1_11",
    MergedType = "eb5_1_12",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1", Weight = 1},
      {Code = "rb#cbox3_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb5_1_12",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "rb#cbox3_1", Weight = 1},
      {
        Code = "rb#greenbox_1",
        Weight = 1
      },
      {
        Code = "rb#greenbox2_1",
        Weight = 1
      }
    },
    Cd = 1,
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    DropsTotal = 3
  },
  {
    Type = "eb5_2_1",
    MergedType = "eb5_2_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb5_1_8"}
  },
  {
    Type = "eb5_2_2",
    MergedType = "eb5_2_3",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb5_1_8"},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb5_2_3",
    MergedType = "eb5_2_4",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb5_1_8"},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1},
      {Code = "ene_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb5_2_4",
    MergedType = "eb5_2_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb5_1_8"},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1},
      {Code = "ene_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb5_2_5",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb5_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 5},
      {Code = "ene_2", Weight = 2},
      {Code = "ene_3", Weight = 1}
    },
    Cd = 1,
    InitialNumber = 8,
    Frequency = 1,
    Capacity = 8,
    DropsTotal = 8
  },
  {
    Type = "eb6_1_1",
    MergedType = "eb6_1_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb6_1_2",
    MergedType = "eb6_1_3",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb6_1_3",
    MergedType = "eb6_1_4",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb6_1_4",
    MergedType = "eb6_1_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb6_1_5",
    MergedType = "eb6_1_6",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb6_1_6",
    MergedType = "eb6_1_7",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb6_1_7",
    MergedType = "eb6_1_8",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_3", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb6_1_8",
    MergedType = "eb6_1_9",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_3", Weight = 1},
      {
        Code = "additem_1_1",
        Weight = 1
      },
      {Code = "eb6_2_1", Weight = 1}
    },
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    SpreadAuto = 2
  },
  {
    Type = "eb6_1_9",
    MergedType = "eb6_1_10",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1", Weight = 1},
      {Code = "rb#cbox1_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb6_1_10",
    MergedType = "eb6_1_11",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1", Weight = 1},
      {Code = "rb#cbox2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb6_1_11",
    MergedType = "eb6_1_12",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1", Weight = 1},
      {Code = "rb#cbox3_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb6_1_12",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "rb#cbox3_1", Weight = 1},
      {
        Code = "rb#greenbox_1",
        Weight = 1
      },
      {
        Code = "rb#greenbox2_1",
        Weight = 1
      }
    },
    Cd = 1,
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    DropsTotal = 3
  },
  {
    Type = "eb6_2_1",
    MergedType = "eb6_2_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb6_1_8"}
  },
  {
    Type = "eb6_2_2",
    MergedType = "eb6_2_3",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb6_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 1},
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb6_2_3",
    MergedType = "eb6_2_4",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb6_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 1},
      {Code = "skipprop_3", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb6_2_4",
    MergedType = "eb6_2_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb6_1_8"},
    GeneratedItems = {
      {Code = "ene_2", Weight = 1},
      {Code = "skipprop_3", Weight = 2}
    },
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    SpreadAuto = 2
  },
  {
    Type = "eb6_2_5",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb6_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 2},
      {Code = "ene_2", Weight = 6},
      {Code = "ene_3", Weight = 1}
    },
    Cd = 1,
    InitialNumber = 9,
    Frequency = 1,
    Capacity = 9,
    DropsTotal = 9
  },
  {
    Type = "eb7_1_1",
    MergedType = "eb7_1_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb7_1_2",
    MergedType = "eb7_1_3",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb7_1_3",
    MergedType = "eb7_1_4",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1}
  },
  {
    Type = "eb7_1_4",
    MergedType = "eb7_1_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb7_1_5",
    MergedType = "eb7_1_6",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb7_1_6",
    MergedType = "eb7_1_7",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb7_1_7",
    MergedType = "eb7_1_8",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb7_1_8",
    MergedType = "eb7_1_9",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {
        Code = "additem_1_1",
        Weight = 1
      },
      {Code = "eb7_2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb7_1_9",
    MergedType = "eb7_1_10",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1},
      {Code = "rb#cbox1_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb7_1_10",
    MergedType = "eb7_1_11",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1_1", Weight = 1},
      {Code = "rb#cbox2_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb7_1_11",
    MergedType = "eb7_1_12",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "gem_1", Weight = 1},
      {Code = "rb#cbox3_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb7_1_12",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    GeneratedItems = {
      {Code = "rb#cbox3_1", Weight = 1},
      {
        Code = "rb#greenbox_1",
        Weight = 1
      },
      {
        Code = "rb#greenbox2_1",
        Weight = 1
      }
    },
    Cd = 1,
    InitialNumber = 3,
    Frequency = 1,
    Capacity = 3,
    DropsTotal = 3
  },
  {
    Type = "eb7_2_1",
    MergedType = "eb7_2_2",
    Category = {1},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb7_1_8"}
  },
  {
    Type = "eb7_2_2",
    MergedType = "eb7_2_3",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb7_1_8"},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1}
    },
    InitialNumber = 1,
    Frequency = 1,
    Capacity = 1,
    SpreadAuto = 2
  },
  {
    Type = "eb7_2_3",
    MergedType = "eb7_2_4",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb7_1_8"},
    GeneratedItems = {
      {Code = "skipprop_1", Weight = 1},
      {Code = "ene_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb7_2_4",
    MergedType = "eb7_2_5",
    Category = {1, 8},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb7_1_8"},
    GeneratedItems = {
      {Code = "skipprop_2", Weight = 1},
      {Code = "ene_1", Weight = 1}
    },
    InitialNumber = 2,
    Frequency = 1,
    Capacity = 2,
    SpreadAuto = 2
  },
  {
    Type = "eb7_2_5",
    Category = {1, 3},
    SellCurrency = {Currency = "delete", Amount = 1},
    Generators = {"eb7_1_8"},
    GeneratedItems = {
      {Code = "ene_1", Weight = 5},
      {Code = "ene_2", Weight = 2},
      {Code = "ene_3", Weight = 1}
    },
    Cd = 1,
    InitialNumber = 8,
    Frequency = 1,
    Capacity = 8,
    DropsTotal = 8
  }
}
