SlotAnimatorState = {}
SlotAnimatorState.__index = SlotAnimatorState

function SlotAnimatorState:PlayAnimation(callback)
  Log.Assert(not self.m_callback, "不允许重复播放 Slot Animator")
  self.m_callback = callback
  self.m_animator:SetTrigger("Change")
end

function SlotAnimatorState:OnAnimationFinish()
  Log.Assert(self.m_callback, "Slot Animator 未设置回调")
  if self.m_callback then
    local callback = self.m_callback
    self.m_callback = nil
    callback()
  end
end
