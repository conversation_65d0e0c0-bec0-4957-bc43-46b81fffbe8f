ItemModelFactory = {}

function ItemModelFactory.CreateWithData(boardModel, data)
  if data.codeStr == nil or type(data.codeStr) ~= "string" then
    Log.Error("ItemModelFactory.CreateWithData codeStr is " .. tostring(data.codeStr) .. "code is " .. tostring(data.code))
  end
  local item = ItemModelFactory.CreateWithCode(boardModel, nil, data.codeStr, false)
  if item == nil then
    return
  end
  item:FromSerialization(data)
  return item
end

local codePrefixToCreator

function ItemModelFactory.CreateWithCode(boardModel, position, code, needUnlockType)
  if codePrefixToCreator == nil then
    codePrefixToCreator = {
      [ItemCodePrefix.PaperBox] = ItemModelFactory._CreatePaperBox,
      [ItemCodePrefix.Cobweb] = ItemModelFactory._CreateCobweb,
      [ItemCodePrefix.Bubble] = ItemModelFactory._CreateBubble,
      [ItemCodePrefix.RewardBubble] = ItemModelFactory._CreateRewardBubble
    }
  end
  for prefix, creator in pairs(codePrefixToCreator) do
    if StringUtil.StartWith(code, prefix) then
      local innerCode = string.sub(code, string.len(prefix) + 1)
      return creator(boardModel, position, innerCode, code, needUnlockType)
    end
  end
  return ItemModelFactory.CreateWithType(boardModel, position, code, needUnlockType)
end

function ItemModelFactory._CreatePaperBox(boardModel, position, innerCode, originalCode)
  local itemConfig = GM.ItemDataModel:GetModelConfig(ItemType.PaperBox)
  local itemModel = ItemModel.Create(boardModel, position, itemConfig, originalCode)
  local itemPaperBox = ItemPaperBox.Create(innerCode)
  itemModel:AddComponent(itemPaperBox)
  return itemModel
end

function ItemModelFactory._CreateCobweb(boardModel, position, innerCode, originalCode, needUnlockType)
  if needUnlockType then
    GM.ItemDataModel:SetLocked(innerCode)
  end
  local itemConfig = GM.ItemDataModel:GetModelConfig(ItemType.Cobweb)
  local itemModel = ItemModel.Create(boardModel, position, itemConfig, originalCode)
  local itemCobweb = ItemCobweb.Create(innerCode)
  itemModel:AddComponent(itemCobweb)
  return itemModel
end

function ItemModelFactory._CreateBubble(boardModel, position, innerCode, originalCode)
  local itemConfig = GM.ItemDataModel:GetModelConfig(ItemType.Bubble)
  local itemModel = ItemModel.Create(boardModel, position, itemConfig, originalCode)
  local itemBubble = ItemBubble.Create(innerCode)
  itemModel:AddComponent(itemBubble)
  return itemModel
end

function ItemModelFactory._CreateRewardBubble(boardModel, position, innerCode, originalCode)
  local itemConfig = GM.ItemDataModel:GetModelConfig(ItemType.RewardBubble)
  local itemModel = ItemModel.Create(boardModel, position, itemConfig, originalCode)
  local itemBubble = ItemRewardBubble.Create(innerCode)
  itemModel:AddComponent(itemBubble)
  return itemModel
end

function ItemModelFactory.CreateWithType(boardModel, position, type, needUnlockType)
  if needUnlockType then
    GM.ItemDataModel:SetUnlocked(type)
  end
  local itemConfig = GM.ItemDataModel:GetModelConfig(type)
  if not itemConfig then
    return
  end
  local itemModel = ItemModel.Create(boardModel, position, itemConfig, type)
  if itemConfig.TapeItems ~= nil or itemConfig.GeneratedItems ~= nil then
    local itemSpread = ItemSpread.Create(itemConfig, needUnlockType)
    itemModel:AddComponent(itemSpread)
  end
  if itemConfig.Transform ~= nil then
    local itemTransform = ItemTransform.Create(itemConfig)
    itemModel:AddComponent(itemTransform)
  end
  if itemConfig.CollectRewards ~= nil then
    local itemCollectable = ItemCollectable.Create(itemConfig.CollectRewards)
    itemModel:AddComponent(itemCollectable)
  end
  if itemConfig.Recipes ~= nil then
    local itemCook = ItemCook.Create(itemConfig)
    itemModel:AddComponent(itemCook)
  end
  if itemConfig.BoosterType ~= nil then
    if itemConfig.BoosterType == "SkipTime" or itemConfig.BoosterType == "NewDay" then
      local itemSpeeder = ItemSpeeder.Create(itemConfig)
      itemModel:AddComponent(itemSpeeder)
    elseif itemConfig.BoosterType == "Split" then
      local itemSplit = ItemSplit.Create()
      itemModel:AddComponent(itemSplit)
    else
      local itemBooster = ItemBooster.Create(itemConfig)
      itemModel:AddComponent(itemBooster)
    end
  end
  if itemConfig.Choices ~= nil then
    local itemChoose = ItemChoose.Create(itemConfig.Choices)
    itemModel:AddComponent(itemChoose)
  end
  return itemModel
end

function ItemModelFactory.CreateItemLockerCmp(itemModel, startPosition, width, height, unlockDay)
  local itemLocker = ItemLocker.Create(startPosition, width, height, unlockDay)
  itemModel:AddComponent(itemLocker)
end

function ItemModelFactory.GetCodeAndWeightPairs(configArray)
  local ret = {}
  for _, str in ipairs(configArray) do
    local pairs = StringUtil.Split(str, "-")
    table.insert(ret, {
      Code = pairs[1],
      Weight = tonumber(pairs[2])
    })
  end
  return ret
end

function ItemModelFactory.GetInnerType(code)
  local index = StringUtil.rFindChar(code, "#")
  if index then
    return string.sub(code, index + 1)
  else
    return code
  end
end

function ItemModelFactory.GetCodePrefixTargetType(code)
  if ItemModelFactory.s_codePrefixItemTypeMap == nil then
    ItemModelFactory.s_codePrefixItemTypeMap = {
      [ItemCodePrefix.PaperBox] = ItemType.PaperBox,
      [ItemCodePrefix.Cobweb] = ItemType.Cobweb,
      [ItemCodePrefix.Bubble] = ItemType.Bubble,
      [ItemCodePrefix.RewardBubble] = ItemType.RewardBubble
    }
  end
  for prefix, type in pairs(ItemModelFactory.s_codePrefixItemTypeMap) do
    if StringUtil.StartWith(code, prefix) then
      return type
    end
  end
end
