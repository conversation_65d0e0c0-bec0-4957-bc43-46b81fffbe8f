SpineAnimation = {}
SpineAnimation.__index = SpineAnimation
local NOT_EXIST_CACHE = "nil"

function SpineAnimation:Init()
  if self.initialized then
    return
  end
  self.skeleton = self.skeletonAnimation.Skeleton
  Log.Assert(self.skeleton, self.transform.parent.gameObject.name .. " 的骨骼动画组件有问题。")
  self.skeletonData = self.skeleton.Data
  self.m_mapCachePartSkin = {}
  self.m_mapCacheSlot = {}
  self.m_skeletonState = self.skeletonAnimation.state or self.skeletonAnimation.AnimationState
  
  function self.m_onAnimComplete(entry)
    self:_OnAnimComplete(entry)
  end
  
  self.m_skeletonState:Complete("+", self.m_onAnimComplete)
  self.initialized = true
end

function SpineAnimation:OnDestroy()
  if not self.initialized then
    return
  end
  self.m_skeletonState:Complete("-", self.m_onAnimComplete)
end

function SpineAnimation:PlayAnimation(animName, callback, loop, ignoreLoopStart)
  local success = true
  if loop then
    Log.Assert(not callback, "循环动画不应该设置回调")
  end
  self:SetCompleteCallback(callback, animName)
  if loop then
    local preAnim = animName .. "_start"
    if not ignoreLoopStart and self:IsValidAnimName(preAnim) then
      success = self:SetAnimation(preAnim, false) and success
      success = self:AddAnimation(animName, true) and success
    else
      success = self:SetAnimation(animName, true) and success
    end
  else
    success = self:SetAnimation(animName, false) and success
  end
  return success
end

function SpineAnimation:_OnAnimComplete(entry)
  if self.callback and (self.callbackAnimName == nil or tostring(entry.Animation.Name) == self.callbackAnimName) then
    local cb = self.callback
    self.callback = nil
    self.callbackAnimName = nil
    cb()
  end
end

function SpineAnimation:ThrowCallback()
  self.callback = nil
  self.callbackAnimName = nil
end

function SpineAnimation:SpeedUp()
  local prevSpeed = self.m_skeletonState.TimeScale
  self.m_skeletonState.TimeScale = 1000000
  self:UpdateImediately()
  self.m_skeletonState.TimeScale = prevSpeed
end

function SpineAnimation:SetTimeScale(timeScale)
  self.m_skeletonState.TimeScale = timeScale
end

function SpineAnimation:SetCompleteCallback(callback, animName)
  self:Init()
  if self.callback then
    local cb = self.callback
    self.callback = nil
    cb()
    Log.Error("之前带有callback的动画尚未播完！")
  end
  self.callback = callback
  if self.callback then
    self.callbackAnimName = animName
  end
end

function SpineAnimation:SetAnimation(animName, loop)
  self.m_bLooping = loop
  local stillAnimName = animName .. "_still"
  local hasStill = self:IsValidAnimName(stillAnimName)
  animName = hasStill and stillAnimName or animName
  local valid = self:IsValidAnimName(animName)
  if not valid then
    Log.Error("动画 " .. animName .. " 不存在！请策划检查配置！")
    self.callbackAnimName = nil
    self:_OnAnimComplete()
    return false
  end
  self.m_skeletonState:ClearTracks()
  self.skeleton:SetToSetupPose()
  self.m_skeletonState:SetAnimation(0, animName, loop)
  self.skeletonAnimation.enabled = true
  self:UpdateImediately()
  self.skeletonAnimation.enabled = not hasStill
  return true
end

function SpineAnimation:AddAnimation(animName, loop)
  self.m_bLooping = loop
  local valid = self:IsValidAnimName(animName)
  if not valid then
    Log.Error("动画 " .. animName .. " 不存在！请策划检查配置！")
    return false
  end
  self.m_skeletonState:AddAnimation(0, animName, loop, 0)
  return true
end

function SpineAnimation:UpdateImediately()
  self.skeletonAnimation:Update(0)
  self.skeletonAnimation:LateUpdate(0)
end

function SpineAnimation:IsValidAnimName(animName)
  local anim = self.skeletonData:FindAnimation(animName)
  if anim == nil then
    return false
  end
  return true
end

function SpineAnimation:HasSetInitAnim()
  local initAnim = self.skeletonAnimation.AnimationName
  return not StringUtil.IsNilOrEmpty(initAnim) and self:IsValidAnimName(initAnim)
end

function SpineAnimation:GetCurrentRunningAnimName()
  local entry = self.m_skeletonState:GetCurrent(0)
  if not entry or not entry.Animation then
    return ""
  end
  return entry.Animation.Name
end

function SpineAnimation:IsLooping()
  return self.m_bLooping == true
end

function SpineAnimation:FindSkin(skinName, logError)
  if self.m_mapCachePartSkin[skinName] then
    if self.m_mapCachePartSkin[skinName] == NOT_EXIST_CACHE then
      return nil
    end
    return self.m_mapCachePartSkin[skinName]
  end
  local skin = self.skeletonData:FindSkin(skinName)
  if not skin and logError ~= false then
    Log.Error("皮肤 " .. skinName .. " 不存在！请策划检查配置！")
  end
  self.m_mapCachePartSkin[skinName] = skin ~= nil and skin or NOT_EXIST_CACHE
  return skin
end

function SpineAnimation:InitSkin(skinName)
  self.skeletonAnimation.initialSkinName = skinName
  self.skeletonAnimation:Initialize(true)
end

function SpineAnimation:SetSkin(skinName)
  self.skeleton:SetSkin(skinName)
end

function SpineAnimation:FindSlot(slotName, logError)
  if self.m_mapCacheSlot[slotName] then
    if self.m_mapCacheSlot[slotName] == NOT_EXIST_CACHE then
      return nil
    end
    return self.m_mapCacheSlot[slotName]
  end
  local slot = self.skeleton:FindSlot(slotName)
  if not slot and logError ~= false then
    Log.Error("Slot " .. slotName .. " 不存在！请策划检查配置！")
  end
  self.m_mapCacheSlot[slotName] = slot ~= nil and slot or NOT_EXIST_CACHE
  return slot
end

function SpineAnimation:SetOpacity(opacity)
  if self.m_fadeTween then
    self.m_fadeTween:Kill()
    self.m_fadeTween = nil
  end
  self.skeleton:SetColor(CSColor(1, 1, 1, opacity))
  local curValue = self.skeletonAnimation.enabled
  self.skeletonAnimation.enabled = true
  self:UpdateImediately()
  self.skeletonAnimation.enabled = curValue
end

function SpineAnimation:DOFade(opacity, duration)
  if self.m_fadeTween then
    self.m_fadeTween:Kill()
    self.m_fadeTween = nil
  end
  local curValue = self.skeletonAnimation.enabled
  self.skeletonAnimation.enabled = true
  self.m_fadeTween = DOVirtual.Float(self.skeleton:GetColor().a, opacity, duration, function(x)
    self.skeleton:SetColor(CSColor(1, 1, 1, x))
  end):OnComplete(function()
    self.skeletonAnimation.enabled = curValue
  end)
end
