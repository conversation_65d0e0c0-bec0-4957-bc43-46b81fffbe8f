TestAlbumInfoWindow = setmetatable({}, BaseWindow)
TestAlbumInfoWindow.__index = TestAlbumInfoWindow
local ChangeCount = 1
local ForceRemove = false

function TestAlbumInfoWindow:Init()
  self.m_model = AlbumActivityModel.GetActiveModel()
  if self.m_model == nil then
    GM.UIManager:ShowPrompt("没有正在开启的集卡活动")
    return
  end
  local albumConfig = self.m_model:GetAlbumConfig()
  self.m_arrCardSet = {}
  for _, setConfig in ipairs(self.m_model:GetCardSets()) do
    local obj = GameObject.Instantiate(self.m_setGo, self.m_content)
    obj:SetActive(true)
    local set = obj:GetLuaTable()
    set:Init(self.m_model, self, albumConfig, setConfig)
    table.insert(self.m_arrCardSet, set)
  end
  self:UpdateContent()
  self.m_changeCountInput.text = ChangeCount
  self.m_forceRemoveToggle.isOn = ForceRemove
  EventDispatcher.AddListener(self.m_model:GetActivityDefinition().StateChangedEvent, self, self.Close)
end

function TestAlbumInfoWindow:Fold()
  for _, set in ipairs(self.m_arrCardSet) do
    set:ShowCards(false)
  end
end

function TestAlbumInfoWindow:UnFold()
  for _, set in ipairs(self.m_arrCardSet) do
    set:ShowCards(true)
  end
end

function TestAlbumInfoWindow:UpdateContent()
  local totalCards = 0
  local totalSurplusCards = 0
  for cardId, cardInfo in pairs(self.m_model.m_mapCards) do
    local totalCard = self.m_model:GetCardCount(cardId)
    totalCards = totalCards + totalCard
    totalSurplusCards = totalSurplusCards + (self.m_model:GetCardCount(cardId, true) - (0 < totalCard and 1 or 0))
  end
  local curCard, maxCard = self.m_model:GetAlbumCollectProgress()
  local starInfo = "("
  for i = 1, self.m_model.m_maxCardStar do
    starInfo = starInfo .. i .. "*" .. #self.m_model.m_SurplusCardList[i] .. (i < self.m_model.m_maxCardStar and "+" or ")")
  end
  self.m_descText.text = "进度：" .. tostring(curCard) .. "/" .. tostring(maxCard) .. "\n总卡牌数：" .. tostring(totalCards) .. "\n剩余待消耗卡牌数：" .. tostring(totalSurplusCards) .. "\n总星星数：" .. tostring(self.m_model:GetSurplusCardStar()) .. starInfo
end

function TestAlbumInfoWindow:ChangeCount()
  ChangeCount = tonumber(self.m_changeCountInput.text)
end

function TestAlbumInfoWindow:ForceRemoveToggleChanged()
  ForceRemove = self.m_forceRemoveToggle.isOn
end

TestAlbumInfoSet = {}
TestAlbumInfoSet.__index = TestAlbumInfoSet

function TestAlbumInfoSet:Init(model, window, albumConfig, setConfig)
  self.m_model = model
  self.m_window = window
  self.m_albumConfig = albumConfig
  self.m_config = setConfig
  self.m_bInitCards = false
  self:ShowCards(false)
  self:_Init()
end

function TestAlbumInfoSet:OnBtnClicked()
  self:ShowCards(not self.m_content.gameObject.activeSelf)
end

function TestAlbumInfoSet:ShowCards(show)
  UIUtil.SetActive(self.m_content.gameObject, show)
  if show and not self.m_bInitCards then
    local cards = self.m_config.cardGroup
    for i, v in ipairs(cards) do
      local obj = GameObject.Instantiate(self.m_cardGo, self.m_content)
      obj:SetActive(true)
      local card = obj:GetLuaTable()
      card:Init(self.m_model, self, v)
    end
    self.m_bInitCards = true
  end
  self:_Init()
end

function TestAlbumInfoSet:_Init()
  local curSetCard, maxSetCard = self.m_model:GetSetCollectProgress(self.m_config.setId)
  self.m_setText.text = (self.m_content.gameObject.activeSelf and "v" or ">") .. "[" .. self.m_config.setId .. "]" .. GM.GameTextModel:GetText(self.m_config.setId .. "_name") .. "    " .. tostring(curSetCard) .. "/" .. tostring(maxSetCard)
end

function TestAlbumInfoSet:UpdateContent()
  self:_Init()
  self.m_window:UpdateContent()
end

TestAlbumInfoCard = {}
TestAlbumInfoCard.__index = TestAlbumInfoCard

function TestAlbumInfoCard:Init(model, set, cardId)
  self.m_model = model
  self.m_set = set
  self.m_cardId = cardId
  SpriteUtil.SetImage(self.m_image, ImageFileConfigName[cardId])
  local name = self.m_model:GetCardName(cardId)
  self.m_titleText.text = GM.GameTextModel:GetText(name) .. "(" .. self.m_model:GetCardInfo(self.m_cardId).star .. "星)"
  self:UpdateCount()
end

function TestAlbumInfoCard:UpdateCount()
  self.m_countText.text = "当前：" .. tostring(self.m_model:GetCardCount(self.m_cardId, true)) .. "\n总共：" .. tostring(self.m_model:GetCardCount(self.m_cardId, false))
  self.m_set:UpdateContent()
end

function TestAlbumInfoCard:Add()
  for i = 1, ChangeCount or 1 do
    local setId, bAlbum = self.m_model:AcquireCard(self.m_cardId)
    self.m_model:GetEvent():Call(AlbumActivityModel.EventKey.GetNewCard)
    if setId ~= nil or bAlbum ~= nil then
      do
        local setList = {setId}
        local rewards = {}
        if bAlbum then
          local finishRewards = self.m_model:GetAlbumConfig().reward
          for _, reward in pairs(finishRewards) do
            rewards[reward[PROPERTY_TYPE]] = true
          end
        end
        if setList ~= nil then
          for _, setId in pairs(setList) do
            local setRewardConfig = self.m_model:GetCardSetRewards(setId)
            for _, reward in pairs(setRewardConfig) do
              rewards[reward[PROPERTY_TYPE]] = true
            end
          end
        end
        local callback
        
        function callback()
          if self.m_model:GetState() ~= ActivityState.Started then
            return
          end
          if not Table.IsEmpty(setList) then
            GM.UIManager:OpenView(self.m_model:GetActivityDefinition().TakeSetRewardPrefabName, self.m_model:GetType(), setList, callback)
            return
          end
          if bAlbum == true then
            GM.UIManager:OpenView(self.m_model:GetActivityDefinition().TakeFinishRewardPrefabName, self.m_model:GetType())
            return
          end
        end
        
        callback()
      end
    end
  end
  self:UpdateCount()
end

function TestAlbumInfoCard:Minus()
  local count = ChangeCount or 1
  local remainCard = self.m_model:GetCardCount(self.m_cardId, true)
  if not ForceRemove and count >= remainCard then
    GM.UIManager:ShowPrompt("没有那么多卡啦,剔除到剩一张")
    count = remainCard - 1
  else
    count = math.min(count, remainCard)
  end
  self.m_model.m_dbTable:Set(self.m_cardId, "value", self.m_model:GetCardCount(self.m_cardId) - count)
  self:UpdateCount()
  self.m_model:GetEvent():Call(AlbumActivityModel.EventKey.GetNewCard)
end
