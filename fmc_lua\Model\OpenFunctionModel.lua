EFunction = {
  Bubble = "bubble",
  Discoveries = "discoveries",
  Inventory = "inventory",
  Shop = "shop"
}
OpenFunctionModel = {}
OpenFunctionModel.__index = OpenFunctionModel

function OpenFunctionModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.OpenFunc)
  EventDispatcher.AddListener(EEventType.LevelUp, self, self._CheckOpen)
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self._CheckOpen)
  EventDispatcher.AddListener(EEventType.NewChapterUnlocked, self, self._CheckOpen)
end

function OpenFunctionModel:LoadFileConfig()
  self.m_mapFunctionConfig = {}
  self.m_mapFunctionConfigParam = {}
  local config = GM.ConfigModel:GetLocalConfig(LocalConfigKey.FunctionEnable)
  for _, data in ipairs(config) do
    self.m_mapFunctionConfig[data.name] = tonumber(data.enable)
    self.m_mapFunctionConfigParam[data.name] = data.param
  end
end

function OpenFunctionModel:LateInit()
  self:_CheckOpen()
end

function OpenFunctionModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function OpenFunctionModel:_IsFunctionEnable(key)
  return self.m_mapFunctionConfig[key] == 1
end

function OpenFunctionModel:IsFunctionOpen(key)
  if not self:_IsFunctionEnable(key) then
    return false
  end
  return self.m_dbTable:GetValue(key, "state")
end

function OpenFunctionModel:_CheckOpen()
  for k, v in pairs(EFunction) do
    if self:_IsFunctionEnable(v) and not self.m_dbTable:GetValue(v, "state") then
      local config = self.m_mapFunctionConfigParam[v]
      local satisfied = false
      if config.Type == "openTask" and GM.TaskManager:IsTaskFinished(config.Chapter, config.Task) then
        satisfied = true
      elseif config.Type == "openRoom" and GM.TaskManager:GetOngoingChapterId() >= config.Chapter then
        satisfied = true
      elseif config.Type == "level" and GM.LevelModel:GetCurrentLevel() >= config.Level then
        satisfied = true
      end
      if satisfied then
        self.m_dbTable:Set(v, "state", 1)
        EventDispatcher.DispatchEvent(EEventType.FunctionOpen, {openFunc = v})
      end
    end
  end
end
