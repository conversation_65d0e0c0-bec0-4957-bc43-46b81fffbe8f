DecorationSlotView = setmetatable({}, BaseSlotView)
DecorationSlotView.__index = DecorationSlotView

function DecorationSlotView:Init(roomModel, slotId, parentView)
  BaseSlotView.Init(self, roomModel, slotId)
  self.m_loadRoot = parentView.loadRoot
end

function DecorationSlotView:PlayAnimator(state, callback)
  local prefabName = self:_GetPrefabName(state)
  if prefabName == nil then
    self:_AllPlayAnimator(self.m_go, function()
      self:SetState(state)
      if callback then
        callback()
      end
    end)
    return
  end
  self:SetState(state, function()
    self:_AllPlayAnimator(self.m_go, callback)
  end)
end

function DecorationSlotView:_AllPlayAnimator(go, allCallback, check)
  if not go then
    return
  end
  local arrAnimatorStates = {}
  local luaComponents = go:GetComponentsInChildren(typeof(LuaComponent), true)
  for i = 0, luaComponents.Length - 1 do
    local animatorState = luaComponents[i]:GetLuaTable()
    arrAnimatorStates[#arrAnimatorStates + 1] = animatorState
  end
  local leftCount = #arrAnimatorStates
  Log.Assert(0 < leftCount or check == false, tostring(self.m_prefabName) .. "没有挂载 SlotAnimatorState 动画组件！")
  for _, animatorState in ipairs(arrAnimatorStates) do
    local callback = function()
      leftCount = leftCount - 1
      if leftCount == 0 and allCallback then
        allCallback()
      end
    end
    animatorState:PlayAnimation(callback)
  end
end

function DecorationSlotView:PlaySpineAnimation(state, animationName, callback)
  local prefabName = self:_GetPrefabName(state)
  if prefabName == nil then
    self:_AllPlaySpineAnimation(self.m_go, animationName, false, nil, function()
      self:SetState(state)
      if callback then
        callback()
      end
    end)
    return
  end
  self:SetState(state, function()
    self:_SetAnimatorsEnable(true)
    self:_AllPlaySpineAnimation(self.m_go, animationName, false, function(spineGo)
      self:_SetAnimatorsEnable(false)
      self:_TryPlayIdleSpineAnimation(spineGo)
    end, callback)
  end)
end

function DecorationSlotView:_AllPlaySpineAnimation(go, animationName, loop, singleCallback, allCallback, check)
  if not go then
    return
  end
  Log.Assert(not loop or singleCallback == nil and allCallback == nil, tostring(self.m_prefabName) .. "循环动画不允许有回调！")
  local arrSpineAnimations = {}
  local luaComponents = go:GetComponentsInChildren(typeof(LuaComponent), true)
  for i = 0, luaComponents.Length - 1 do
    local spineAnimation = luaComponents[i]:GetLuaTable()
    if getmetatable(spineAnimation) == SpineAnimation then
      spineAnimation:Init()
      arrSpineAnimations[#arrSpineAnimations + 1] = spineAnimation
    end
  end
  local leftCount = #arrSpineAnimations
  Log.Assert(0 < leftCount or check == false, tostring(self.m_prefabName) .. "没有挂载 SpineAnimation 动画组件！")
  for _, spineAnimation in ipairs(arrSpineAnimations) do
    local callback
    if not loop then
      function callback()
        leftCount = leftCount - 1
        
        if singleCallback then
          singleCallback(spineAnimation.gameObject)
        end
        if leftCount == 0 and allCallback then
          allCallback()
        end
      end
    end
    local success = spineAnimation:PlayAnimation(animationName, callback, loop)
    if not success then
      Log.Error(self.m_prefabName .. " 播放动画（部分）失败：" .. animationName)
    end
  end
end

function DecorationSlotView:_TryPlayIdleSpineAnimation(go, check)
  local idleAnimName = (self.curViewState or 0) .. "_loop"
  self:_AllPlaySpineAnimation(go, idleAnimName, true, nil, nil, check)
end

function DecorationSlotView:SpeedUpSpine(state)
  if not self.m_go then
    self:SetState(state)
    return
  end
  local luaComponents = self.m_go:GetComponentsInChildren(typeof(LuaComponent), true)
  for i = 0, luaComponents.Length - 1 do
    local spineAnimation = luaComponents[i]:GetLuaTable()
    spineAnimation:SpeedUp()
    spineAnimation:ThrowCallback()
  end
  self:SetState(state)
end

function DecorationSlotView:SetState(state, callback, ignoreCheck)
  if self.curViewState == state then
    self.m_stateCallback = nil
  end
  self:_OnUpdateViewFinish()
  self.curViewState = state
  self.m_stateCallback = callback
  self:_UpdateView(ignoreCheck)
end

function DecorationSlotView:_UpdateView(ignoreCheck)
  local prefabName = self:_GetPrefabName(self.curViewState)
  if prefabName and prefabName == self.m_prefabName then
    self:_OnUpdateViewFinish()
    return
  end
  self.m_prefabName = prefabName
  if self.m_prefabName == nil then
    self:_ChangeVisibleGo(nil, ignoreCheck)
    self:_OnUpdateViewFinish()
    return
  end
  self:_LoadPrefabByPrefabName(self.m_prefabName, ignoreCheck)
end

function DecorationSlotView:_OnUpdateViewFinish()
  if self.m_stateCallback then
    local cb = self.m_stateCallback
    self.m_stateCallback = nil
    cb()
  end
end

function DecorationSlotView:_LoadPrefabByPrefabName(prefabName, ignoreCheck)
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName[prefabName]), self.m_loadRoot, Vector3.zero, function(go)
    go.transform:SetParent(self.gameObject.transform, false)
    if prefabName ~= self.m_prefabName then
      Log.Info("prefabName:" .. tostring(prefabName) .. ", self.m_prefabName:" .. tostring(self.m_prefabName))
      go:SetActive(false)
      self:_OnUpdateViewFinish()
      go:RemoveSelf()
      return
    end
    self:_ChangeVisibleGo(go, ignoreCheck)
    self:_OnUpdateViewFinish()
  end)
end

function DecorationSlotView:_ChangeVisibleGo(go, ignoreCheck)
  if go == self.m_go then
    return
  end
  if self.m_go ~= nil then
    if ignoreCheck ~= true and self.m_arrSeats and self.m_arrSeats[1] then
      Log.Error("不允许对可以坐顾客的椅子进行再建造！" .. self:GetNamePrefix())
    end
    self.m_go:RemoveSelf()
  end
  self.m_go = go
  if go then
    self:_TryPlayIdleSpineAnimation(go, false)
  end
  self:_TryParseSeats(go)
end

function DecorationSlotView:_TryParseSeats(go)
  self.m_arrSeats = {}
  if not go then
    return
  end
  local roomSeats = go:GetLuaTable()
  if not roomSeats or not roomSeats.GetSeats then
    return
  end
  self.m_arrSeats = roomSeats:GetSeats()
end

function DecorationSlotView:GetSeats()
  return self.m_arrSeats
end

function DecorationSlotView:HasSeats()
  return self.m_arrSeats and self.m_arrSeats[1] ~= nil
end

function DecorationSlotView:_GetPrefabName(state)
  if state == EMPTY_VIEW_STATE then
    return nil
  end
  local prefix = self:GetNamePrefix()
  local prefabName
  if state == nil then
    prefabName = prefix .. DEFAULT_VIEW_STATE
    if not ScenePrefabConfigName.HasConfig(prefabName) then
      return nil
    end
  else
    prefabName = prefix .. state
    if not ScenePrefabConfigName.HasConfig(prefabName) then
      Log.Error("Prefab " .. prefabName .. " 不存在！请策划检查配置！")
      return nil
    end
  end
  return prefabName
end

function DecorationSlotView:ChangeRendererSortingOrder(delta, go)
  go = go or self.m_go
  if not go then
    return
  end
  local renderers = go:GetComponentsInChildren(typeof(Renderer), true)
  for i = 0, renderers.Length - 1 do
    renderers[i].sortingOrder = renderers[i].sortingOrder + delta
  end
  return go
end

function DecorationSlotView:PlayPlaceAnimation(strAnimationConfig, newState, callback)
  if string.find(strAnimationConfig, EPlaceAnimType.Disappear) or string.find(strAnimationConfig, EPlaceAnimType.Hide) or string.find(strAnimationConfig, EPlaceAnimType.FadeOut) then
    RoomViewHelper.PlayPlaceAnimation(self.m_go, strAnimationConfig, callback)
  else
    self:SetState(newState, function()
      RoomViewHelper.PlayPlaceAnimation(self.m_go, strAnimationConfig, callback)
    end)
  end
end

function DecorationSlotView:StartCovering()
  if self:HasSeats() then
    Log.Error("有椅子的槽位不能 Cover！")
    return
  end
  local go = self.m_go
  if not go or go:IsNull() then
    return
  end
  local spriteRenderers = go:GetComponentsInChildren(typeof(SpriteRenderer), true)
  for i = 0, spriteRenderers.Length - 1 do
    spriteRenderers[i]:DOFade(0, 0.5)
  end
  local csArraySpineAnimation = self.gameObject:GetAllLuaTable("SpineAnimation")
  for i = 0, csArraySpineAnimation.Length - 1 do
    csArraySpineAnimation[i]:DOFade(0, 0.5)
  end
end

function DecorationSlotView:FinishCovering()
  local go = self.m_go
  if not go or go:IsNull() then
    return
  end
  local spriteRenderers = go:GetComponentsInChildren(typeof(SpriteRenderer), true)
  for i = 0, spriteRenderers.Length - 1 do
    spriteRenderers[i]:DOFade(1, 0.5)
  end
  local csArraySpineAnimation = self.gameObject:GetAllLuaTable("SpineAnimation")
  for i = 0, csArraySpineAnimation.Length - 1 do
    csArraySpineAnimation[i]:DOFade(1, 0.5)
  end
end

function DecorationSlotView:_SetAnimatorsEnable(enable)
  local animators = self.m_go:GetComponentsInChildren(typeof(Animator), true)
  for i = 0, animators.Length - 1 do
    animators[i].enabled = enable
  end
end
