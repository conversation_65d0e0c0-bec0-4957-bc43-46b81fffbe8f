NetworkConfig = {
  ClientHeaderKey = ProjectConfig.NET_CLIENT_HEADER_KEY,
  TokenHeaderKey = "Authorization",
  SchemaHeaderKey = "Schema-Name",
  AcceptEncodingKey = "Accept-Encoding",
  _strClientHeader = ProjectConfig.GenerateClientHeader(GameConfig.GetCurrentVersion()),
  CompressMethod = PlayerPrefs.GetString(EPlayerPrefKey.CompressMethod, "deflate"),
  SignHeaderkey = "Signature",
  ContentTypeKey = "Content-Type",
  OfflineFallbackIp = "*************"
}
NetworkConfig.__index = NetworkConfig

function NetworkConfig.UpdateCompressMethod(method)
  NetworkConfig.CompressMethod = method
  PlayerPrefs.SetString(EPlayerPrefKey.CompressMethod, method)
end

function NetworkConfig.GetSelectedServer()
  return PlayerPrefs.GetString(EPlayerPrefKey.SelectedServer, "cola-test-01")
end

function NetworkConfig.SetSelectedServer(server)
  PlayerPrefs.SetString(EPlayerPrefKey.SelectedServer, server)
end

function NetworkConfig.GetHttpServerUrl(strOpname)
  local uid = "&userid=" .. GM.UserModel:GetDisplayUserId()
  local strPrefix = string.sub(strOpname, 1, 2)
  if strPrefix == "BL" then
    return CSNetLibManager:GetUrl(strPrefix) .. "/cola?op=" .. strOpname .. uid
  elseif strPrefix == "RS" then
    return CSNetLibManager:GetUrl(strPrefix) .. "/redis?op=" .. strOpname .. uid
  elseif strPrefix == "SO" then
    return CSNetLibManager:GetUrl(strPrefix) .. "/oper_sso?op=" .. strOpname .. uid
  elseif strPrefix == "AC" then
    return CSNetLibManager:GetUrl(strPrefix) .. "/" .. GameConfig.PROJECT_ID .. "/client/select/" .. (GameConfig.IsTestMode() and "nuforcim_test" or "nuforcim") .. "?op=" .. strOpname .. uid
  elseif strPrefix == "DA" then
    return CSNetLibManager:GetUrl(strPrefix) .. "/dot"
  end
  Log.Assert(false, "NetworkConfig.GetHttpServerUrl")
end

function NetworkConfig.GetUdpServerUrl(strOpname)
  return CSNetLibManager:GetUrl("BL-UDP")
end

function NetworkConfig.GetOneSDKBIUrl()
  if NetworkConfig._OneSDKBIUrl == nil then
    NetworkConfig._OneSDKBIUrl = CSNetLibManager:GetOneSDKBIUrl()
  end
  return NetworkConfig._OneSDKBIUrl
end

function NetworkConfig.GetOperUrl()
  return CSNetLibManager:GetUrl("OP")
end

function NetworkConfig.GetCdnFileUrl(strHash)
  if GameConfig.IsTestMode() then
    return CSNetLibManager:GetUrl("S3") .. "/cola_hotfix/cdn/" .. strHash
  else
    return CSNetLibManager:GetUrl("S3") .. "/archive/" .. strHash
  end
end

function NetworkConfig.GetCloudConfigUrl()
  if GameConfig.IsTestMode() then
    return CSNetLibManager:GetUrl("S3") .. "/cola_hotfix/abroad_cloud/offlineAgreeControl/agreeControl_v2.json"
  else
    return CSNetLibManager:GetUrl("S3") .. "/cloud/config/agreeControl_v2.json"
  end
end

function NetworkConfig.GetClientHeader(appValue)
  local strHeader = NetworkConfig._strClientHeader
  return string.format(strHeader .. ";app(%d)", appValue ~= nil and appValue or 0)
end

function NetworkConfig.IsTestServer4()
  if not GameConfig.IsTestMode() then
    return false
  end
  return NetworkConfig.GetSelectedServer() == "cola-test-04"
end

function NetworkConfig.GetUserAgreementLink()
  return "https://www.microfun.com/userAgreementEN.html"
end

function NetworkConfig.GetChildPrivacyLink()
  return "https://www.microfun.com/childrenPrivacyPolicyEN.html"
end

function NetworkConfig.GetPrivacyPolicyLink()
  return "https://www.microfun.com/privacy_EN.html"
end

function NetworkConfig.GetProbabilityDisclosureLink()
  return "https://cdn.mergecola.com/page/cola_cards.html"
end
