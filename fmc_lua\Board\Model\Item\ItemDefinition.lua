ItemType = {
  pd_1_1 = "pd_1_1",
  pd_1_2 = "pd_1_2",
  pd_1_3 = "pd_1_3",
  pd_1_4 = "pd_1_4",
  pd_1_5 = "pd_1_5",
  pd_1_6 = "pd_1_6",
  pd_1_7 = "pd_1_7",
  pd_1_8 = "pd_1_8",
  pd_1_9 = "pd_1_9",
  pd_1_10 = "pd_1_10",
  it_1_2_1 = "it_1_2_1",
  it_1_2_2 = "it_1_2_2",
  it_1_2_3 = "it_1_2_3",
  it_1_2_4 = "it_1_2_4",
  it_1_2_5 = "it_1_2_5",
  pd_2_1 = "pd_2_1",
  pd_2_2 = "pd_2_2",
  pd_2_3 = "pd_2_3",
  pd_2_4 = "pd_2_4",
  pd_2_5 = "pd_2_5",
  pd_2_6 = "pd_2_6",
  pd_2_7 = "pd_2_7",
  pd_2_8 = "pd_2_8",
  pd_2_9 = "pd_2_9",
  it_2_2_1 = "it_2_2_1",
  it_2_2_2 = "it_2_2_2",
  it_2_2_3 = "it_2_2_3",
  it_2_2_4 = "it_2_2_4",
  it_2_2_5 = "it_2_2_5",
  pd_3_1 = "pd_3_1",
  pd_3_2 = "pd_3_2",
  pd_3_3 = "pd_3_3",
  pd_3_4 = "pd_3_4",
  pd_3_5 = "pd_3_5",
  pd_3_6 = "pd_3_6",
  pd_3_7 = "pd_3_7",
  pd_3_8 = "pd_3_8",
  pd_3_9 = "pd_3_9",
  pd_3_10 = "pd_3_10",
  pd_5_1 = "pd_5_1",
  pd_5_2 = "pd_5_2",
  pd_5_3 = "pd_5_3",
  pd_5_4 = "pd_5_4",
  pd_5_5 = "pd_5_5",
  pd_5_6 = "pd_5_6",
  pd_5_7 = "pd_5_7",
  pd_5_8 = "pd_5_8",
  pd_5_9 = "pd_5_9",
  pd_5_10 = "pd_5_10",
  pd_6_1 = "pd_6_1",
  pd_6_2 = "pd_6_2",
  pd_6_3 = "pd_6_3",
  pd_6_4 = "pd_6_4",
  pd_6_5 = "pd_6_5",
  pd_6_6 = "pd_6_6",
  pd_6_7 = "pd_6_7",
  pd_6_8 = "pd_6_8",
  pd_6_9 = "pd_6_9",
  pd_6_10 = "pd_6_10",
  pd_6_11 = "pd_6_11",
  gold_1 = "gold_1",
  gold_2 = "gold_2",
  gold_3 = "gold_3",
  gold_4 = "gold_4",
  gold_5 = "gold_5",
  gem_1 = "gem_1",
  gem_2 = "gem_2",
  gem_3 = "gem_3",
  gem_4 = "gem_4",
  ene_1 = "ene_1",
  ene_2 = "ene_2",
  ene_3 = "ene_3",
  ene_4 = "ene_4",
  ene_5 = "ene_5",
  skipprop_1 = "skipprop_1",
  skipprop_2 = "skipprop_2",
  skipprop_3 = "skipprop_3",
  skipprop_4 = "skipprop_4",
  skipprop_5 = "skipprop_5",
  PaperBox = "box_1",
  Cobweb = "cobweb_1",
  Bubble = "bubble_1",
  RewardBubble = "rb_1",
  tutbox_1 = "tutbox_1",
  tutbox_booster1_1 = "tutbox_booster1_1",
  Lollipop01 = "12801",
  Lollipop02 = "12802",
  Lollipop03 = "12803",
  Lollipop04 = "12804",
  Lollipop05 = "12805",
  level_up_piece_1 = "level_up_piece_1",
  scissors_1 = "scissors_1",
  time_skip_1 = "time_skip_1",
  time_skip_2 = "time_skip_2",
  time_skip_3 = "time_skip_3",
  time_skip_4 = "time_skip_4",
  time_skip_5 = "time_skip_5",
  pinata_1 = "pinata_1",
  pinata_2 = "pinata_2",
  pinata_3 = "pinata_3",
  pinata_4 = "pinata_4"
}
ItemCodePrefix = {
  PaperBox = "pb#",
  Cobweb = "c#",
  Bubble = "b#",
  RewardBubble = "rb#",
  ExtraBoardCommon = "eb_"
}
ItemChain = {
  Lollipop = "128",
  Balloon = "ma_balloon",
  Pinata = "pinata",
  it_1_1 = "it_1_1",
  it_2_1 = "it_2_1",
  OperationBoard = "eq_1",
  Grill = "eq_2",
  Blender = "eq_3",
  Pan = "eq_4",
  Oven = "eq_5",
  Pot = "eq_6",
  Bowl = "it_2_3_1",
  AddItem = "additem"
}
CLEAN_ITEM_CODE = "clean"
EItemCategory = {
  DisposableGenerator = 3,
  Instrument = 4,
  Dish = 5,
  RandomBox = 6,
  Box = 7,
  NoRecycleableSpreadItem = 8
}
local nameDefinition = {}
local prefix = "item_"
local suffix = "_name"
for k, v in pairs(ItemType) do
  nameDefinition[v] = prefix .. v .. suffix
end
ItemNameDefinition = {}

function ItemNameDefinition.GetName(itemType)
  local key = nameDefinition[itemType]
  if key == nil then
    local name = prefix .. itemType .. suffix
    nameDefinition[itemType] = name
    return name
  end
  return key
end
