local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer
local EGestureType = {
  ClickDiscoveries = 1,
  EnterBoard = 2,
  ToMap = 3,
  TapCacheWhenLackOfEq = 4
}

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._UpdateGesture)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._UpdateGesture)
  EventDispatcher.AddListener(EEventType.EventLockClear, self, self._UpdateGesture)
  EventDispatcher.AddListener(EEventType.TimelineComplete, self, self._UpdateGesture)
  EventDispatcher.AddListener(EEventType.TutorialFinished, self, self._UpdateGesture)
  EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self._UpdateGesture)
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self._UpdateGesture)
  EventDispatcher.AddListener(EEventType.UpdateSceneViewHud, self, self._UpdateGesture)
  EventDispatcher.AddListener(EEventType.OrderStateChanged, self, self._UpdateGesture)
  EventDispatcher.AddListener(EEventType.NewContentReleased, self, self._UpdateGesture)
  EventDispatcher.AddListener(EEventType.OrderItemCookPopTip, self, self._OnOrderItemCookPopTip)
  EventDispatcher.AddListener(EEventType.HideTapCacheWeakTutorial, self, self._OnHideTapCacheWeakTutorial)
  self:_UpdateGesture()
  
  function self.m_showGesture()
    if self.m_eGestureType == EGestureType.ClickDiscoveries then
      self.m_gesture = TutorialHelper.TapOnHudButton(ESceneViewHudButtonKey.Discoveries, Vector3(-8, 3, 0))
    elseif self.m_eGestureType == EGestureType.ToMap then
      self.m_arrow = TutorialHelper.AddArrow2HudButton(ESceneViewHudButtonKey.Map, 0)
    elseif self.m_eGestureType == EGestureType.EnterBoard then
      self.m_arrow = TutorialHelper.AddArrow2HudButton(ESceneViewHudButtonKey.MainBoard, 0)
    elseif self.m_eGestureType == EGestureType.TapCacheWhenLackOfEq then
      local boardView = MainBoardView:GetInstance()
      local orderArea = boardView:GetOrderArea()
      orderArea:ScrollToRectTransformVisible(boardView:GetOrderArea():GetBoardCacheRoot().transform, true)
      orderArea:GetBoardCacheRoot():ShowHandTapEffect(true)
      self.m_bCacheRootGesture = true
      boardView:ToggleBoardPrompt(false)
      boardView:TryStartBoardPrompt()
    end
  end
end

function Executer:_OnOrderItemCookPopTip(msg)
  self.m_bShouldTapCacheWhenLackOfEq = false
  if GM.ConfigModel:CachePinEqPdToTop() and msg and msg.tip and msg.tipType == EItemPopTipType.MatPopWithInstruLack and StringUtil.StartWith(msg.itemCode, "eq_") and not GM.MainBoardModel:IsBoardFull() and GM.MainBoardModel:GetCachedItemCount() > 0 then
    local chainId = GM.ItemDataModel:GetChainId(msg.itemCode)
    local arrChain = GM.ItemDataModel:GetChain(chainId)
    for _, id in ipairs(arrChain) do
      if 0 < GM.MainBoardModel:GetCachedCountByCode(id) then
        self.m_bShouldTapCacheWhenLackOfEq = true
        break
      end
    end
  end
  self:_UpdateGesture()
end

function Executer:_OnHideTapCacheWeakTutorial()
  self.m_bShouldTapCacheWhenLackOfEq = false
  self:_UpdateGesture()
end

function Executer:_UpdateGesture()
  local showGesture = self:_CanShowGesture()
  if showGesture then
    self:_ShowGesture()
  else
    self:_HideGesture()
  end
end

function Executer:_CanShowGesture()
  if not GM.UIManager.allWindowClosed or GM.UIManager:IsEventLock() or GM.UIManager:IsMaskVisible() or GM.TimelineManager:IsPlayingTimeline() or GM.TutorialModel:HasAnyStrongTutorialOngoing() or GM.ChapterManager.curActiveChapterName ~= GM.TaskManager:GetOngoingChapterName() or GM.SceneManager.isChanging or GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BaseSceneView) == nil then
    return false
  end
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    return not GM.TaskManager:CanFinishOngoingTask()
  elseif GM.SceneManager:GetGameMode() == EGameMode.Board then
    if GM.TaskManager:IsOngoingChapterProgressFinished() and not GM.TaskManager:IsLastChapter() then
      return true
    end
    if self.m_bShouldTapCacheWhenLackOfEq then
      return true
    end
  end
  return false
end

function Executer:_DetermineGestureType()
  local eGestureType = EGestureType.EnterBoard
  if GM.SceneManager:GetGameMode() == EGameMode.Board and self.m_bShouldTapCacheWhenLackOfEq then
    eGestureType = EGestureType.TapCacheWhenLackOfEq
  elseif GM.SceneManager:GetGameMode() == EGameMode.Main and GM.OpenFunctionModel:IsFunctionOpen(EFunction.Discoveries) and GM.ItemDataModel:GetDiscoveriesCanRewardCount() > 0 then
    eGestureType = EGestureType.ClickDiscoveries
  elseif GM.SceneManager:GetGameMode() == EGameMode.Board and GM.TaskManager:IsOngoingChapterProgressFinished() and not GM.TaskManager:IsLastChapter() then
    eGestureType = EGestureType.ToMap
  end
  return eGestureType
end

function Executer:_ShowGesture()
  local eGestureType = self:_DetermineGestureType()
  if eGestureType == self.m_eGestureType then
    return
  end
  self:_HideGesture()
  self.m_eGestureType = eGestureType
  local delay = 3
  if self.m_eGestureType == EGestureType.ClickDiscoveries or 3 > GM.LevelModel:GetCurrentLevel() then
    delay = 1
  elseif self.m_eGestureType == EGestureType.TapCacheWhenLackOfEq then
    delay = 0
  end
  Scheduler.Schedule(self.m_showGesture, self, 0, 1, delay)
end

function Executer:_HideGesture()
  if self.m_gesture ~= nil then
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
  end
  if self.m_arrow ~= nil then
    TutorialHelper.HideArrow(self.m_arrow)
    self.m_arrow = nil
  end
  if self.m_bCacheRootGesture then
    local boardView = MainBoardView:GetInstance()
    local orderArea = boardView:GetOrderArea()
    orderArea:GetBoardCacheRoot():ShowHandTapEffect(false)
    self.m_bCacheRootGesture = nil
    boardView:ToggleBoardPrompt(true)
    boardView:TryStartBoardPrompt()
  end
  self.m_eGestureType = nil
  Scheduler.Unschedule(self.m_showGesture)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
