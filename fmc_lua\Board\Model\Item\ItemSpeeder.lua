ItemSpeeder = setmetatable({}, BaseItemComponent)
ItemSpeeder.__index = ItemSpeeder

function ItemSpeeder.Create(itemConfig)
  local cmp = setmetatable({}, ItemSpeeder)
  cmp:Init(itemConfig)
  return cmp
end

function ItemSpeeder:Init(itemConfig)
  self.m_effect = itemConfig.Effect
end

function ItemSpeeder:OnTap()
  local boardModel = self.m_itemModel:GetBoardModel()
  local items = ItemSpeeder.TakeEffect(boardModel, self.m_effect, self.m_itemModel:GetCode())
  boardModel:RemoveItem(self.m_itemModel)
  boardModel.event:Call(BoardEventType.TimeSkip, {
    Item = self.m_itemModel,
    EffectedItems = items
  })
  ItemSpeeder.effectedItems = nil
  EventDispatcher.DispatchEvent(EEventType.ItemSpeederClicked)
end

function ItemSpeeder.TakeEffect(boardModel, effect, sourceCode)
  local items = ItemSpeeder.GetEffectedItems(boardModel)
  ItemSpeeder.effectedItems = items
  for _, item in ipairs(items) do
    local itemSpread = item:GetComponent(ItemSpread)
    itemSpread:SkipTimeFromSpeeder(effect, sourceCode)
  end
  return items
end

function ItemSpeeder.GetEffectedItems(boardModel)
  local filter = function(itemModel)
    local itemSpread = itemModel:GetComponent(ItemSpread)
    return itemSpread ~= nil and itemSpread:CanSpeederEffect()
  end
  return boardModel:FilterItems(filter)
end
