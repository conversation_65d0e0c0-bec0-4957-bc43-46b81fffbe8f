TextAssetConfigName = {
  AlbumCardConfig = "AlbumCardConfig",
  BoardModelConfig = "BoardModelConfig",
  BoardModelConfigLocker = "BoardModelConfigLocker",
  BoardPromptConfig = "BoardPromptConfig",
  BubbleConfig = "BubbleConfig",
  ChapterConfig = "ChapterConfig",
  CleanOrderConfig = "CleanOrderConfig",
  DigItemConfig = "DigItemConfig",
  DigLevelsConfig = "DigLevelsConfig",
  ExtraBoardActivityBoardConfig = "ExtraBoardActivityBoardConfig",
  ExtraBoardCobwebConfig = "ExtraBoardCobwebConfig",
  ExtraBoardItemModelConfig = "ExtraBoardItemModelConfig",
  ExtraBoardItemModelConfig_cwenergy = "ExtraBoardItemModelConfig_cwenergy",
  ExtraBoardItemModelConfig_guidebook = "ExtraBoardItemModelConfig_guidebook",
  FunctionEnableConfig = "FunctionEnableConfig",
  InventorySlotConfig = "InventorySlotConfig",
  ItemModelConfig = "ItemModelConfig",
  ItemModelConfig_box = "ItemModelConfig_box",
  ItemPropertyConfig = "ItemPropertyConfig",
  ItemViewConfig = "ItemViewConfig",
  LevelConfig = "LevelConfig",
  MainBoardBgConfig = "MainBoardBgConfig",
  NotificationTextConfig = "NotificationTextConfig",
  OrderAvatarConfig = "OrderAvatarConfig",
  OrderFixedConfig_1 = "OrderFixedConfig_1",
  OrderFixedConfig_2 = "OrderFixedConfig_2",
  OrderFixedConfig_3 = "OrderFixedConfig_3",
  OrderFixedConfig_4 = "OrderFixedConfig_4",
  OrderFixedConfig_5 = "OrderFixedConfig_5",
  OrderFixedConfig_6 = "OrderFixedConfig_6",
  OrderFixedConfig_7 = "OrderFixedConfig_7",
  OrderFixedConfig_8 = "OrderFixedConfig_8",
  OrderFixedConfig_9 = "OrderFixedConfig_9",
  OrderFixedConfig_10 = "OrderFixedConfig_10",
  OrderFixedConfig_11 = "OrderFixedConfig_11",
  OrderFixedConfig_12 = "OrderFixedConfig_12",
  OrderFixedConfig_13 = "OrderFixedConfig_13",
  OrderFixedConfig_14 = "OrderFixedConfig_14",
  OrderFixedConfig_15 = "OrderFixedConfig_15",
  OrderFixedConfig_16 = "OrderFixedConfig_16",
  OrderFixedConfig_17 = "OrderFixedConfig_17",
  OrderFixedConfig_18 = "OrderFixedConfig_18",
  OrderGroupConfig_1 = "OrderGroupConfig_1",
  OrderGroupConfig_2 = "OrderGroupConfig_2",
  OrderGroupConfig_2_album = "OrderGroupConfig_2_album",
  OrderGroupConfig_3 = "OrderGroupConfig_3",
  OrderGroupConfig_3_album = "OrderGroupConfig_3_album",
  OrderGroupConfig_4 = "OrderGroupConfig_4",
  OrderGroupConfig_4_album = "OrderGroupConfig_4_album",
  OrderGroupConfig_5 = "OrderGroupConfig_5",
  OrderGroupConfig_5_album = "OrderGroupConfig_5_album",
  OrderGroupConfig_6 = "OrderGroupConfig_6",
  OrderGroupConfig_6_album = "OrderGroupConfig_6_album",
  OrderGroupConfig_7 = "OrderGroupConfig_7",
  OrderGroupConfig_7_album = "OrderGroupConfig_7_album",
  OrderGroupConfig_8 = "OrderGroupConfig_8",
  OrderGroupConfig_8_album = "OrderGroupConfig_8_album",
  OrderGroupConfig_9 = "OrderGroupConfig_9",
  OrderGroupConfig_9_album = "OrderGroupConfig_9_album",
  OrderGroupConfig_10 = "OrderGroupConfig_10",
  OrderGroupConfig_10_album = "OrderGroupConfig_10_album",
  OrderGroupConfig_11 = "OrderGroupConfig_11",
  OrderGroupConfig_11_album = "OrderGroupConfig_11_album",
  OrderGroupConfig_12 = "OrderGroupConfig_12",
  OrderGroupConfig_12_album = "OrderGroupConfig_12_album",
  OrderGroupConfig_13 = "OrderGroupConfig_13",
  OrderGroupConfig_13_album = "OrderGroupConfig_13_album",
  OrderGroupConfig_14 = "OrderGroupConfig_14",
  OrderGroupConfig_14_album = "OrderGroupConfig_14_album",
  OrderGroupConfig_15 = "OrderGroupConfig_15",
  OrderGroupConfig_15_album = "OrderGroupConfig_15_album",
  OrderGroupConfig_16 = "OrderGroupConfig_16",
  OrderGroupConfig_16_album = "OrderGroupConfig_16_album",
  OrderGroupConfig_17 = "OrderGroupConfig_17",
  OrderGroupConfig_17_album = "OrderGroupConfig_17_album",
  OrderGroupConfig_18 = "OrderGroupConfig_18",
  OrderGroupConfig_18_album = "OrderGroupConfig_18_album",
  OrderHigherGroupConfig = "OrderHigherGroupConfig",
  PDItemSPConfig = "PDItemSPConfig",
  question = "question",
  sensitiveWord = "sensitiveWord",
  ShopDailyDealsConfig = "ShopDailyDealsConfig",
  ShopDailyDealsConfig_additem = "ShopDailyDealsConfig_additem",
  ShopDailyDealsConfig_leveldown = "ShopDailyDealsConfig_leveldown",
  ShopDailyDealsConfig_tag = "ShopDailyDealsConfig_tag",
  ShopFlashSaleConfig = "ShopFlashSaleConfig",
  ShopIAPConfig = "ShopIAPConfig",
  ShopIAPConfig_tag = "ShopIAPConfig_tag",
  ShopOrderConfig = "ShopOrderConfig",
  SystemConfig = "SystemConfig",
  TaskConfig_Bakery = "TaskConfig_Bakery",
  TaskConfig_BBQ = "TaskConfig_BBQ",
  TaskConfig_DimSum = "TaskConfig_DimSum",
  TaskConfig_Hanbi = "TaskConfig_Hanbi",
  TaskConfig_Market = "TaskConfig_Market",
  TaskConfig_Morocco = "TaskConfig_Morocco",
  TaskConfig_Nice = "TaskConfig_Nice",
  TaskConfig_Orleans = "TaskConfig_Orleans",
  TaskConfig_Ottoman = "TaskConfig_Ottoman",
  TaskConfig_Pasta = "TaskConfig_Pasta",
  TaskConfig_Sausage = "TaskConfig_Sausage",
  TaskConfig_Seafood = "TaskConfig_Seafood",
  TaskConfig_Sushi = "TaskConfig_Sushi",
  TaskConfig_Tacos = "TaskConfig_Tacos",
  TaskConfig_Tapas = "TaskConfig_Tapas",
  TaskConfig_Thailand = "TaskConfig_Thailand",
  TaskConfig_Vietnam = "TaskConfig_Vietnam",
  TaskConfig_Wine = "TaskConfig_Wine",
  TaskGroupConfig = "TaskGroupConfig"
}
setmetatable(TextAssetConfigName, {
  __index = function(_, key)
    Log.Error("TextAssetConfigName try to index a nil key: " .. tostring(key))
    return nil
  end
})

function TextAssetConfigName.HasConfig(name)
  if name == nil then
    return false
  end
  return rawget(TextAssetConfigName, name) ~= nil
end
