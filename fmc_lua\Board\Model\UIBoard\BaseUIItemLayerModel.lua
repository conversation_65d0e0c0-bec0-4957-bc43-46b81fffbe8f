BaseUIItemLayerModel = setmetatable({}, BaseInteractiveItemLayerModel)
BaseUIItemLayerModel.__index = BaseUIItemLayerModel

function BaseUIItemLayerModel.Create(boardModel, dbTable, itemManager, initCodeMap)
  local itemLayerModel = setmetatable({}, BaseUIItemLayerModel)
  itemLayerModel:Init(boardModel, dbTable, itemManager, initCodeMap)
  return itemLayerModel
end

function BaseUIItemLayerModel:Init(boardModel, dbTable, itemManager, initCodeMap)
  BaseInteractiveItemLayerModel.Init(self, boardModel, dbTable, itemManager)
  if not Table.IsEmpty(initCodeMap) and self.m_dbTable:IsEmpty() and self.m_itemManager:IsEmpty() then
    self:_InitBoardItem(initCodeMap)
  else
    self:_SyncDBData()
  end
end

function BaseUIItemLayerModel:_InitBoardItem(initCodeMap)
  if Table.IsEmpty(initCodeMap) then
    return
  end
  local position
  for y = 1, self.m_boardModel:GetVerticalTiles() do
    for x = 1, self.m_boardModel:GetHorizontalTiles() do
      position = self.m_boardModel:CreatePosition(x, y)
      local code = initCodeMap[position:GetY()][position:GetX()]
      if not StringUtil.IsNilOrEmpty(code) then
        local item = ItemModelFactory.CreateWithCode(self.m_boardModel, position, code, true)
        if item ~= nil then
          self.m_itemManager:SetItem(item)
          self:SetItem(position, item)
        end
      end
    end
  end
end
