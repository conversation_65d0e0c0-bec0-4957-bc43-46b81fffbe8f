PkRaceModel = setmetatable({
  FixedPlayerNumber = 1,
  EntryOpName = "BLPkRaceEntry",
  EntryGapTime = 1800,
  ShowEntryMask = false
}, BaseRaceActivityModel)
PkRaceModel.__index = PkRaceModel
PkRaceModel.RoundConfigKey = "pk_race"
PkRaceModel.PlayerConfigKey = "race_player"

function PkRaceModel:Init(activityType, virtualDBTable)
  self.m_tokenHelper = ActivityTokenHelper.Create(self, virtualDBTable)
  BaseRaceActivityModel.Init(self, activityType, virtualDBTable)
end

function PkRaceModel:Destroy()
  BaseRaceActivityModel.Destroy(self)
  self.m_tokenHelper:Destroy()
end

function PkRaceModel:GetActivityDefinitionByType(activityType)
  return PkRaceDefinition[activityType]
end

function PkRaceModel:GetBoardEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.PkRace,
    entryPrefabName = self.m_activityDefinition.BoardEntryPrefabName,
    checkFun = function()
      return self:CanShowBoardEntry()
    end
  }
end

function PkRaceModel:GetMapEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.PkRace,
    entryPrefabName = self.m_activityDefinition.EntryPrefabName,
    hudKey = self.m_activityDefinition.EntryButtonKey,
    checkFun = function()
      return self:IsActivityOpen()
    end
  }
end

function PkRaceModel:AcquireActivityToken(score)
  if 0 < score then
    self:_AddMyScore(score, "order")
  end
end

function PkRaceModel:GetActivityTokenNumber()
  return self:GetMyScore()
end

function PkRaceModel:IsActivityOpen()
  return BaseRaceActivityModel.IsActivityOpen(self) and self:GetState() == ActivityState.Started
end

function PkRaceModel:CanShowBoardEntry()
  return self:GetState() == ActivityState.Started and self:IsActivityOpen() and self:IsInRace()
end

function PkRaceModel:GetScoreRatio()
  if not Table.IsEmpty(self.m_speedCurveConfig) then
    return 1
  end
  return self.m_scoreRatio or 1
end

function PkRaceModel:_LoadOtherServerConfig(config)
  BaseRaceActivityModel._LoadOtherServerConfig(self, config)
  local arrConfig = Table.DeepCopy(config[PkRaceModel.RoundConfigKey], true)
  table.sort(arrConfig, function(a, b)
    return a.round < b.round
  end)
  self.m_config.roundConfigs = arrConfig
  local orderTokenConfigs = config.order_token_control
  self.m_scoreRatio = nil
  if orderTokenConfigs ~= nil and orderTokenConfigs[1] ~= nil then
    self.m_scoreRatio = orderTokenConfigs[1].ratio
    if orderTokenConfigs[1].score_min ~= nil or orderTokenConfigs[1].score_max ~= nil then
      Log.Error("order_token_control表配置有误, 1v1活动不允许根据难度分进行分层")
    end
  end
  if not Table.IsEmpty(orderTokenConfigs) and 1 < #orderTokenConfigs then
    Log.Error("order_token_control表配置有误, 1v1活动不允许有多条配置")
  end
  self.m_tokenHelper:LoadConfig(config)
  local newPlayerConfigs = config[PkRaceModel.PlayerConfigKey]
  if not Table.IsEmpty(newPlayerConfigs) then
    self.m_speedCurveConfig = {}
    local arrCurve, curveConfig, playerConfig
    for _, config in ipairs(newPlayerConfigs) do
      if self.m_speedCurveConfig[config.round] == nil then
        self.m_speedCurveConfig[config.round] = {}
      end
      arrCurve = {}
      for i = 1, 6 do
        curveConfig = config["speed_curve" .. i]
        if curveConfig ~= nil then
          table.insert(arrCurve, curveConfig)
          if GameConfig.IsTestMode() then
            local rCode = self:CheckSpeedCurveValid(curveConfig, config.delta, config.success == 1)
            if rCode == 1 then
              Log.Error("曲线积分区间存在重叠的情况, 请检查配置{ 轮次:" .. config.round .. ", 组:" .. config.group .. ", 曲线序号:" .. tostring(i))
            elseif rCode == 2 then
              Log.Error("曲线的时间不是递增的, 请检查配置{ 轮次:" .. config.round .. ", 组:" .. config.group .. ", 曲线序号:" .. tostring(i))
            end
          end
        end
      end
      playerConfig = Table.ShallowCopy(config)
      playerConfig.arrCurve = arrCurve
      self.m_speedCurveConfig[config.round][config.group] = playerConfig
    end
  end
  self.EntryOpName = not Table.IsEmpty(newPlayerConfigs) and "BLPkRace1Entry" or "BLPkRaceEntry"
  self:CheckConfigValid()
end

function PkRaceModel:CheckConfigValid()
  if not GameConfig.IsTestMode() then
    return
  end
  for _, roundConfig in ipairs(self.m_config.roundConfigs) do
    local total = 0
    local mapGroupType = {}
    local mapRealGroupName = {}
    if not roundConfig.playerNum then
      Log.Error("playerNum 为空！")
    else
      for _, playerNumConfig in ipairs(roundConfig.playerNum) do
        total = total + playerNumConfig.group_num
        mapGroupType[playerNumConfig.group_type] = true
      end
      if total ~= self.FixedPlayerNumber then
        Log.Error("playerNum 假人数量不符要求！")
      end
    end
    local mapGroupWeight = {}
    if roundConfig.playerNumWeight then
      for _, weightConfig in ipairs(roundConfig.playerNumWeight) do
        mapGroupWeight[weightConfig.group_type] = true
        if not mapGroupType[weightConfig.group_type] then
          Log.Error("playerNumWeight 含有不存在的 groupType！")
        else
          mapRealGroupName[weightConfig.group_type .. "_" .. weightConfig.child_type] = true
        end
      end
    end
    for groupType, _ in pairs(mapGroupType) do
      if mapGroupWeight[groupType] == nil then
        mapRealGroupName[groupType] = true
      end
    end
    if not Table.IsEmpty(self.m_speedCurveConfig) then
      local speedRoundConfig = self.m_speedCurveConfig[roundConfig.round]
      if speedRoundConfig == nil then
        Log.Error("race_player表中缺少第" .. roundConfig.round .. "轮的配置")
      else
        local groupConfig
        for groupName, _ in pairs(mapRealGroupName) do
          groupConfig = speedRoundConfig[groupName]
          if groupConfig == nil then
            Log.Error("race_player表中第" .. roundConfig.round .. "轮缺少" .. groupName .. "组的配置")
          elseif Table.IsEmpty(groupConfig.arrCurve) then
            Log.Error("race_player表中第" .. roundConfig.round .. "轮, " .. groupName .. "组的配置缺少速度曲线")
          end
        end
      end
    end
  end
end

function PkRaceModel:CheckSpeedCurveValid(curve, delta, bSuccess)
  local lastMaxPer = 0
  local lastTimePer = 0
  local minPer, tmp1, tmp2
  for _, curveInfo in ipairs(curve) do
    minPer = curveInfo.TokenPercent - delta
    if minPer - lastMaxPer < -1.0E-4 then
      return 1
    end
    if lastTimePer > curveInfo.TimePercent or bSuccess and curveInfo.TimePercent == 1 then
      return 2
    end
    lastMaxPer = curveInfo.TokenPercent + delta
    lastTimePer = curveInfo.TimePercent
  end
  return 0
end

function PkRaceModel:_CreateRobotPlayers(robots)
  local targetScore = self:GetTargetScore()
  local playerData
  self.m_arrPlayerData = {}
  if Table.IsEmpty(self.m_speedCurveConfig) then
    for index, value in ipairs(robots) do
      playerData = RacePlayerData.Create(self, value, targetScore, 0)
      self.m_arrPlayerData[#self.m_arrPlayerData + 1] = playerData
    end
  else
    local randomIndex
    for index, value in ipairs(robots) do
      value.score, randomIndex = self:_GenerateScoreCurve(value.group_type, targetScore, value.second)
      playerData = PkRacePlayerData.Create(self, value, targetScore, 0)
      self.m_arrPlayerData[#self.m_arrPlayerData + 1] = playerData
      if GameConfig.IsTestMode() and randomIndex ~= nil then
        Log.Debug("[匹配信息] 轮次:" .. self:GetCurrentRound() .. "组:" .. value.group_type .. ", 曲线序号:" .. randomIndex .. ", 积分信息:" .. json.encode(value.score), "PkRacePlayer")
      end
    end
  end
end

function PkRaceModel:_GenerateScoreCurve(group, targetScore, targetTime)
  local round = self:GetCurrentRound()
  local config = self.m_speedCurveConfig[round] and self.m_speedCurveConfig[round][group]
  if config == nil then
    Log.Error("[race_player] 没有对应的轮次或者组别，请检查: " .. (group or "nil"))
    return {
      {seconds = 0, score = 0}
    }
  end
  local arrCurve = config.arrCurve
  if Table.IsEmpty(arrCurve) then
    Log.Error("[race_player] 没有可用的曲线, 请检查配置")
    return {
      {seconds = 0, score = 0}
    }
  end
  local delta = config.delta
  local result = {}
  local curveConfig, randomIndex = Table.ListRandomSelectOne(arrCurve)
  local bSuccess = config.success == 1
  local lastScore = 0
  local lastSecond = 0
  local second, score
  for _, curveInfo in ipairs(curveConfig) do
    second = math.max(math.floor(targetTime * curveInfo.TimePercent), 5)
    score = math.floor(targetScore * self:GenerateRandomFloat(curveInfo.TokenPercent - delta, curveInfo.TokenPercent + delta))
    score = math.min(bSuccess and targetScore or targetScore - 1, math.max(score, lastScore))
    table.insert(result, {seconds = second, score = score})
    lastScore = score
  end
  if bSuccess then
    table.insert(result, {seconds = targetTime, score = targetScore})
  end
  return result, randomIndex
end

function PkRaceModel:GenerateRandomFloat(min, max)
  return min + (max - min) * math.random()
end

function PkRaceModel:GetOneCompetitorData()
  local arrPlayerData = self:GetAllPlayerData()
  for _, playerData in ipairs(arrPlayerData or {}) do
    if not playerData:IsMySelf() then
      return playerData
    end
  end
end

function PkRaceModel:GetCurrentRoundReward()
  local curRound = self:GetCurrentRound()
  local roundConfig = self.m_config.roundConfigs[curRound]
  if not roundConfig then
    return {}
  end
  local rewards = {
    roundConfig.rankReward1,
    roundConfig.rankReward2
  }
  return rewards
end

function PkRaceModel:GetRoundProgressReward(round)
  local roundConfig = self.m_config.roundConfigs[round]
  if roundConfig ~= nil then
    return roundConfig.roundRewards
  end
end

function PkRaceModel:GetRoundProgressRewardConfigs()
  local arrConfig = {}
  local maxRound = 0
  for round, config in ipairs(self.m_config.roundConfigs) do
    if not Table.IsEmpty(config.roundRewards) then
      table.insert(arrConfig, {
        round = round,
        roundRewards = config.roundRewards,
        uiCode = config.uiCode
      })
      maxRound = math.max(maxRound, round)
    end
  end
  return arrConfig, maxRound
end

function PkRaceModel:TryClaimReward()
  local bSuccess, rewards = BaseRaceActivityModel.TryClaimReward(self)
  local progressRewards
  if bSuccess then
    progressRewards = self:GetRoundProgressReward(self:GetCurrentRound())
    RewardApi.CryptRewards(progressRewards)
    RewardApi.AcquireRewardsLogic(progressRewards, EPropertySource.Give, self.m_activityDefinition.RoundRewardBIType, EGameMode.Board, CacheItemType.Stack)
  end
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
  return bSuccess, rewards, progressRewards
end

function PkRaceModel:TryOpenMainWindow(bEntry)
  if not self:HasNetwork() then
    self:OnNetError(function()
      self:TryOpenMainWindow()
    end)
    return
  end
  GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_type, false, bEntry)
end
