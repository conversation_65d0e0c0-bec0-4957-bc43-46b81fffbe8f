return {
  {
    ChapterId = "Nice",
    Id = 1,
    SlotState = {
      {Slot = "floorOut", State = 9},
      {
        Slot = "floorOutOld",
        State = 100
      }
    },
    Cost = 740,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 2,
    StartConditions = {1},
    SlotState = {
      {
        Slot = "rightCoverOld",
        State = 100
      }
    },
    Cost = 786,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 3,
    StartConditions = {2},
    SlotState = {
      {
        Slot = "rightOutWall",
        State = 9
      }
    },
    Cost = 833,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 4,
    StartConditions = {3},
    SlotState = {
      {Slot = "rightCover", State = 9}
    },
    Cost = 786,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 5,
    StartConditions = {4},
    SlotState = {
      {Slot = "rightPlant", State = 9}
    },
    Cost = 694,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 6,
    StartConditions = {5},
    SlotState = {
      {Slot = "rightTable", State = 9}
    },
    Cost = 694,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 7,
    StartConditions = {6},
    SlotState = {
      {Slot = "rightChair", State = 9}
    },
    Cost = 648,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 8,
    StartConditions = {7},
    SlotState = {
      {
        Slot = "rightUtensil",
        State = 9
      }
    },
    Cost = 740,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 9,
    StartConditions = {8},
    SlotState = {
      {
        Slot = "leftOutWall",
        State = 9
      }
    },
    Cost = 935,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 10,
    StartConditions = {9},
    SlotState = {
      {Slot = "leftTable", State = 9}
    },
    Cost = 715,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 11,
    StartConditions = {10},
    SlotState = {
      {Slot = "leftChair", State = 9}
    },
    Cost = 770,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 12,
    StartConditions = {11},
    SlotState = {
      {
        Slot = "leftUtensil",
        State = 9
      }
    },
    Cost = 825,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 13,
    StartConditions = {12},
    SlotState = {
      {Slot = "leftCover", State = 9}
    },
    Cost = 660,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 14,
    StartConditions = {13},
    SlotState = {
      {
        Slot = "trashCounter",
        State = 100
      }
    },
    Cost = 660,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 15,
    StartConditions = {14},
    SlotState = {
      {Slot = "floorIn", State = 9},
      {Slot = "floorInOld", State = 100}
    },
    Cost = 880,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 16,
    StartConditions = {15},
    SlotState = {
      {
        Slot = "counterOldA",
        State = 100
      }
    },
    Cost = 715,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 17,
    StartConditions = {16},
    SlotState = {
      {
        Slot = "fenceLeftOld",
        State = 100
      }
    },
    Cost = 849,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 18,
    StartConditions = {17},
    SlotState = {
      {
        Slot = "fenceRightOld",
        State = 100
      }
    },
    Cost = 849,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 19,
    StartConditions = {18},
    SlotState = {
      {
        Slot = "leftInnerWall",
        State = 9
      }
    },
    Cost = 793,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 20,
    StartConditions = {19},
    SlotState = {
      {
        Slot = "rightInnerWall",
        State = 9
      }
    },
    Cost = 793,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 21,
    StartConditions = {20},
    SlotState = {
      {Slot = "balcony", State = 9}
    },
    Cost = 737,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 22,
    StartConditions = {21},
    SlotState = {
      {Slot = "roof", State = 9}
    },
    Cost = 793,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 23,
    StartConditions = {22},
    SlotState = {
      {
        Slot = "counterOldB",
        State = 100
      }
    },
    Cost = 737,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 24,
    StartConditions = {23},
    SlotState = {
      {Slot = "counter", State = 1}
    },
    Cost = 849,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 25,
    StartConditions = {24},
    SlotState = {
      {Slot = "counter", State = 2}
    },
    Cost = 824,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 26,
    StartConditions = {25},
    SlotState = {
      {Slot = "counter", State = 3}
    },
    Cost = 824,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 27,
    StartConditions = {26},
    SlotState = {
      {Slot = "counter", State = 9}
    },
    Cost = 882,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 28,
    StartConditions = {27},
    SlotState = {
      {
        Slot = "counterLight",
        State = 9
      }
    },
    Cost = 765,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 29,
    StartConditions = {28},
    SlotState = {
      {Slot = "longTable", State = 1}
    },
    Cost = 824,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 30,
    StartConditions = {29},
    SlotState = {
      {Slot = "longTable", State = 2}
    },
    Cost = 765,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 31,
    StartConditions = {30},
    SlotState = {
      {Slot = "longTable", State = 3}
    },
    Cost = 824,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 32,
    StartConditions = {31},
    SlotState = {
      {Slot = "longTable", State = 9}
    },
    Cost = 882,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 33,
    StartConditions = {32},
    SlotState = {
      {
        Slot = "screenTable",
        State = 9
      }
    },
    Cost = 882,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 34,
    StartConditions = {33},
    SlotState = {
      {
        Slot = "screenChair",
        State = 9
      }
    },
    Cost = 799,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 35,
    StartConditions = {34},
    SlotState = {
      {
        Slot = "screenUtensil",
        State = 9
      }
    },
    Cost = 860,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 36,
    StartConditions = {35},
    SlotState = {
      {Slot = "pairChair", State = 1}
    },
    Cost = 799,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 37,
    StartConditions = {36},
    SlotState = {
      {Slot = "pairChair", State = 2}
    },
    Cost = 799,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 38,
    StartConditions = {37},
    SlotState = {
      {Slot = "pairChair", State = 9}
    },
    Cost = 860,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 39,
    StartConditions = {38},
    SlotState = {
      {Slot = "trashBake", State = 100}
    },
    Cost = 921,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 40,
    StartConditions = {39},
    SlotState = {
      {
        Slot = "kitCabinetOld",
        State = 9
      }
    },
    Cost = 921,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 41,
    StartConditions = {40},
    SlotState = {
      {
        Slot = "kitCounterOld",
        State = 100
      }
    },
    Cost = 983,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 42,
    StartConditions = {41},
    SlotState = {
      {Slot = "kitCounter", State = 1}
    },
    Cost = 799,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 43,
    StartConditions = {42},
    SlotState = {
      {Slot = "kitCounter", State = 2}
    },
    Cost = 723,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 44,
    StartConditions = {43},
    SlotState = {
      {Slot = "kitCounter", State = 3}
    },
    Cost = 890,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 45,
    StartConditions = {44},
    SlotState = {
      {Slot = "kitCounter", State = 9}
    },
    Cost = 834,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 46,
    StartConditions = {45},
    SlotState = {
      {Slot = "kitCabinet", State = 9}
    },
    Cost = 1001,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 47,
    StartConditions = {46},
    SlotState = {
      {Slot = "kitWashing", State = 9}
    },
    Cost = 890,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 48,
    StartConditions = {47},
    SlotState = {
      {Slot = "breadCart", State = 9}
    },
    Cost = 834,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 49,
    StartConditions = {48},
    SlotState = {
      {Slot = "fenceRight", State = 9}
    },
    Cost = 1001,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 50,
    StartConditions = {49},
    SlotState = {
      {Slot = "fenceLeft", State = 9}
    },
    Cost = 946,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 51,
    StartConditions = {50},
    SlotState = {
      {
        Slot = "breadCounter",
        State = 1
      }
    },
    Cost = 890,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 52,
    StartConditions = {51},
    SlotState = {
      {
        Slot = "breadCounter",
        State = 9
      }
    },
    Cost = 916,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 53,
    StartConditions = {52},
    SlotState = {
      {Slot = "breadTable", State = 9}
    },
    Cost = 980,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 54,
    StartConditions = {53},
    SlotState = {
      {Slot = "secTable", State = 9}
    },
    Cost = 916,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 55,
    StartConditions = {54},
    SlotState = {
      {Slot = "secUtensil", State = 9}
    },
    Cost = 851,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 56,
    StartConditions = {55},
    SlotState = {
      {
        Slot = "balconyTable",
        State = 9
      }
    },
    Cost = 980,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 57,
    StartConditions = {56},
    SlotState = {
      {Slot = "turntable", State = 9}
    },
    Cost = 916,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 58,
    StartConditions = {57},
    SlotState = {
      {Slot = "bookcase", State = 1}
    },
    Cost = 980,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 59,
    StartConditions = {58},
    SlotState = {
      {Slot = "bookcase", State = 2}
    },
    Cost = 916,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  },
  {
    ChapterId = "Nice",
    Id = 60,
    StartConditions = {59},
    SlotState = {
      {Slot = "bookcase", State = 9}
    },
    Cost = 916,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    }
  }
}
