ScrollBackToOrderPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true
  },
  canIgnorePopup = false
}, BasePopupHelper)
ScrollBackToOrderPopupHelper.__index = ScrollBackToOrderPopupHelper

function ScrollBackToOrderPopupHelper.Create()
  local helper = setmetatable({}, ScrollBackToOrderPopupHelper)
  helper:Init()
  return helper
end

function ScrollBackToOrderPopupHelper:Init()
  BasePopupHelper.Init(self)
  EventDispatcher.AddListener(EEventType.OnViewWillClose, self, self._OnViewWillClose)
  EventDispatcher.AddListener(EEventType.FlambeTimePopup, self, self._OnFlambeTimePopup)
end

function ScrollBackToOrderPopupHelper:_OnFlambeTimePopup()
  self.m_bCanPop = false
end

function ScrollBackToOrderPopupHelper:_OnViewWillClose(msg)
  if not GM.ConfigModel:CanNewOrderRewardAnimationBack() then
    return
  end
  if msg.name == UIPrefabConfigName.ScrollBackToOrderWindow or msg.name == UIPrefabConfigName.FlambeTimeWindow then
    self.m_bCanPop = false
    return
  end
  if msg.name ~= UIPrefabConfigName.OrderFinishRewardWindow then
    return
  end
  if GM.UIManager:IsEventLockUntilNextPopup() and GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link then
    return
  end
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.FlambeTimeWindow) then
    self.m_bCanPop = false
    return
  end
  local mainOrderModel = GM.MainBoardModel:GetOrderModel()
  if mainOrderModel:GetOrdersCount() <= 0 then
    return
  end
  self:SetNeedCheckPopup(true)
  self.m_bCanPop = true
end

function ScrollBackToOrderPopupHelper:CheckPopup()
  if self.m_bCanPop then
    self.m_bCanPop = nil
    return UIPrefabConfigName.ScrollBackToOrderWindow
  end
end
