local Step = {ClickCache = "1", Merge = "2"}
local EStep2TextKey = {
  [Step.ClickCache] = "tutorial_+8_1",
  [Step.Merge] = "tutorial_+8_2"
}
local EStep2TextAnchorPercent = {
  [Step.ClickCache] = 40
}
local itemId = "additem_1"
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self.OnPopCachedItem)
  EventDispatcher.AddListener(EEventType.ItemAffected, self, self.OnItemAffected)
end

function Executer:TryStartTutorial()
  if self:_CanExecuteStep1() then
    self:_ExecuteStep1()
    return true
  end
end

function Executer:_Finish()
  self:Finish(self.m_gesture)
  self.m_model:FinishTutorial(ETutorialId.AddItemOldUser)
end

function Executer:OnPopCachedItem(msg)
  if self.m_strOngoingDatas == Step.ClickCache and self.m_gesture and msg.GameMode == EGameMode.Board then
    TutorialHelper.DehighlightCacheRoot()
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
    self:_ExecuteStep2()
  end
end

function Executer:OnItemAffected()
  if self.m_strOngoingDatas == Step.Merge and self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:_Finish()
  end
end

function Executer:_CanExecuteStep1()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return false
  end
  if GM.MainBoardModel:GetCachedCountByCode(itemId) <= 0 then
    return false
  end
  local boardView = BoardViewHelper.GetActiveView()
  if boardView:GetOrderArea():GetBoardCacheRoot():IsPlayingAnimation() or not boardView:GetOrderArea():GetBoardCacheRoot():IsShowing() then
    return false
  end
  local isBoardFullofItems = GM.MainBoardModel:FindEmptyPositionInValidOrder() == nil
  if isBoardFullofItems then
    self:_Finish()
    return false
  end
  local maxLevel, curLevel, targetItem, itemCode, spreadCmp
  for item, _ in pairs(GM.MainBoardModel:GetAllBoardItems()) do
    itemCode = item:GetCode()
    spreadCmp = item:GetComponent(ItemSpread)
    if spreadCmp ~= nil and spreadCmp:CanTutorialCD() and GM.MainBoardModel:CanItemMove(item) and GM.ItemDataModel:GetChainId(itemCode) == "pd_2" then
      curLevel = GM.ItemDataModel:GetChainLevel(itemCode)
      if maxLevel == nil or maxLevel < curLevel then
        maxLevel = curLevel
        targetItem = item
      end
    end
  end
  if not targetItem then
    self:_Finish()
    return false
  end
  self.m_targetItem = targetItem
  return true
end

function Executer:_ExecuteStep1()
  GM.MainBoardModel:TryPinOneItemCodeToCacheTop(itemId)
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  self.m_model:FinishTutorial(ETutorialId.AddItemOldUser)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickCache
  self:_SaveOngoingDatas()
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  local cacheRoot = TutorialHelper.HighlightCacheRoot()
  self.m_gesture = TutorialHelper.TapCustomPos(cacheRoot:GetFlyTargetPosition())
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_ExecuteStep2()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.Merge
  self:_SaveOngoingDatas()
  local items = TutorialHelper.GetItems(itemId)
  if #items <= 0 then
    self:_Finish()
    return
  end
  local from = items[1]:GetPosition()
  local to = self.m_targetItem:GetPosition()
  GM.TutorialModel:SetForceSourceBoardPosition(from)
  GM.TutorialModel:SetForceTargetBoardPosition(to)
  self.m_gesture = TutorialHelper.DragOnItems(from, to)
  if not self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:_Finish()
    return
  end
  TutorialHelper.MaskOnItemBoard(from, to)
  TutorialHelper.ShowDialogWithBoardMaskArea(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), from, to)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
