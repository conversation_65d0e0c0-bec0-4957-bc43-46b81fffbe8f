SpreeActivityOrderModel = setmetatable({}, SlotOrderModel)
SpreeActivityOrderModel.__index = SpreeActivityOrderModel
SpreeActivityOrderModel.FixedSlots = {1, 2}
SpreeActivityOrderModel.RandomSlots = {3, 4}
SpreeActivityOrderModel.SlotCount = 4
SpreeActivityOrderModel.ChainIdOffset = 1000

function SpreeActivityOrderModel.Create(activityType, dbMetaTable, dbTable, boardModel)
  local orderModel = setmetatable({}, SpreeActivityOrderModel)
  orderModel:Init(activityType, dbMetaTable, dbTable, boardModel)
  return orderModel
end

function SpreeActivityOrderModel:Init(activityType, dbMetaTable, dbTable, boardModel)
  SlotOrderModel.Init(self, boardModel, dbMetaTable, dbTable, SpreeActivityOrderModel.SlotCount)
  self.m_activityDefinition = SpreeActivityDefinition[activityType]
  self.m_activityModel = GM.ActivityManager:GetModel(activityType)
end

function SpreeActivityOrderModel:LoadFileConfig()
  self.m_slotConfig = {}
  local fileName = self.m_activityDefinition.OrderSlotConfigFileName or "SpreeOrderSlotConfig_" .. self.m_activityDefinition.ConfigSuffix
  local slotConfigs = require("Data.Config." .. fileName)
  for _, slotConfig in ipairs(slotConfigs) do
    self.m_slotConfig[slotConfig.Slot] = slotConfig
  end
  self.m_orderConfig = {}
  fileName = self.m_activityDefinition.OrderConfigFileName or "SpreeOrderConfig_" .. self.m_activityDefinition.ConfigSuffix
  local orderConfigs = Table.DeepCopy(require("Data.Config." .. fileName), true)
  for _, orderConfig in ipairs(orderConfigs) do
    self.m_orderConfig[orderConfig.Id] = orderConfig
    if orderConfig.BankReward then
      local rewards = ConfigUtil.GetCurrencyFromArrStr(orderConfig.BankReward, true)
      if not Table.IsEmpty(rewards) then
        orderConfig.BankReward = rewards
      end
    end
  end
  local needItemConfig = false
  for _, slot in ipairs(SpreeActivityOrderModel.RandomSlots) do
    if self.m_slotConfig[slot] ~= nil then
      needItemConfig = true
      break
    end
  end
  if needItemConfig then
    self.m_itemConfig = {}
    fileName = self.m_activityDefinition.OrderItemConfigFileName or "SpreeOrderItemConfig_" .. self.m_activityDefinition.ConfigSuffix
    local orderItemConfigs = require("Data.Config." .. fileName)
    for _, orderItemConfig in ipairs(orderItemConfigs) do
      self.m_itemConfig[orderItemConfig.Type] = orderItemConfig
    end
  end
end

function SpreeActivityOrderModel:_DBGetOrders()
  local orders = {}
  for _, slot in ipairs(SpreeActivityOrderModel.FixedSlots) do
    orders[slot] = SpreeActivityOrderCreatorFixed.DBGetOrder(self, slot)
  end
  for _, slot in ipairs(SpreeActivityOrderModel.RandomSlots) do
    orders[slot] = SpreeActivityOrderCreatorRandom.DBGetOrder(self, slot)
  end
  return orders
end

function SpreeActivityOrderModel:_DBAddOrder(order)
  if Table.Contain(SpreeActivityOrderModel.FixedSlots, order:GetSlot()) then
    SpreeActivityOrderCreatorFixed.DBAddOrder(self, order)
  else
    SpreeActivityOrderCreatorRandom.DBAddOrder(self, order)
  end
end

function SpreeActivityOrderModel:_DBRemoveOrder(slot)
  if Table.Contain(SpreeActivityOrderModel.FixedSlots, slot) then
    SpreeActivityOrderCreatorFixed.DBRemoveOrder(self, slot)
  else
    SpreeActivityOrderCreatorRandom.DBRemoveOrder(self, slot)
  end
end

function SpreeActivityOrderModel:_TryCreateOrder(slot)
  if Table.Contain(SpreeActivityOrderModel.FixedSlots, slot) then
    return SpreeActivityOrderCreatorFixed.Create(self, slot)
  else
    local hasFixedSlot = false
    for _, slot in ipairs(SpreeActivityOrderModel.FixedSlots) do
      if self.m_orders[slot] ~= nil then
        hasFixedSlot = true
        break
      end
    end
    if not hasFixedSlot then
      return nil
    end
    return SpreeActivityOrderCreatorRandom.Create(self, slot)
  end
end

function SpreeActivityOrderModel:IsOrderFinished(orderId)
  local slot = orderId // SpreeActivityOrderModel.ChainIdOffset
  local order = self.m_orders[slot]
  if order ~= nil and order:GetId() == orderId then
    return order:GetState() == OrderState.Finished
  end
  return SpreeActivityOrderCreatorFixed.IsOrderFinished(self, orderId)
end

function SpreeActivityOrderModel:GetOrderPiggyBankReward(orderId)
  local config = self.m_orderConfig[orderId]
  Log.Assert(config ~= nil, "副本订单Id不存在：[" .. (orderId or "nil") .. "]")
  return config.BankReward
end

function SpreeActivityOrderModel:GetActivityModel()
  return self.m_activityModel
end

function SpreeActivityOrderModel:GetOrderConfig()
  return self.m_orderConfig
end

function SpreeActivityOrderModel:GetSlotConfig()
  return self.m_slotConfig
end

function SpreeActivityOrderModel:GetItemConfig()
  return self.m_itemConfig
end
