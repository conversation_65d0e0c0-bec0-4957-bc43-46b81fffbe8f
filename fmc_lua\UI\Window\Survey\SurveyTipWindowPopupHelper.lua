SurveyTipWindowPopupHelper = setmetatable({}, BasePopupHelper)
SurveyTipWindowPopupHelper.__index = SurveyTipWindowPopupHelper

function SurveyTipWindowPopupHelper.Create()
  local helper = setmetatable({}, SurveyTipWindowPopupHelper)
  helper:Init()
  return helper
end

function SurveyTipWindowPopupHelper:Init()
  BasePopupHelper.Init(self)
  self.canPopWindow = false
  AddHandlerAndRecordMap(GM.SurveyModel.event, SurveyNeedOpen, {
    obj = self,
    method = self._OnNeedOpenWindow
  })
end

function SurveyTipWindowPopupHelper:_OnNeedOpenWindow()
  self:SetNeedCheckPopup(true)
  self.canPopWindow = true
end

function SurveyTipWindowPopupHelper:CheckPopup()
  if not self.canPopWindow or GM.SurveyModel.questionConfig == nil then
    return
  end
  self.canPopWindow = false
  return UIPrefabConfigName.SurveyTipWindow, {
    GM.SurveyModel.rewards,
    GM.SurveyModel.questionConfig.title,
    GM.SurveyModel.questionConfig.desc
  }
end
