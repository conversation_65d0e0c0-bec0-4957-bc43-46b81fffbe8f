local nStart = 0
local autoIncrease = function()
  nStart = nStart + 1
  return nStart
end
NotificationType = {
  ComeBack = autoIncrease(),
  EnergyFull = autoIncrease(),
  CashDash = autoIncrease(),
  Lollipop = autoIncrease(),
  Coconut = autoIncrease(),
  Pinata = autoIncrease(),
  ItemSpreadRecovery = autoIncrease()
}
NotificationType.MinPriority = nStart
NotificationType.MaxPriority = 1
NotificationScene = {
  EnergyRefill = "EnergyRefill",
  ItemCooldown = "ItemCooldown",
  CashDashStart = "CashDashStart",
  CashDashEnd = "CashDashEnd",
  ComeBack = "ComeBack",
  LollipopStart = "LollipopStart",
  LollipopEnd = "LollipopEnd",
  CoconutStart = "CoconutStart",
  CoconutEnd = "CoconutEnd",
  PinataStart = "PinataStart",
  PinataEnd = "PinataEnd"
}
ENotiSceneDescKey = {
  EnergyEmpty = "notification_desc_energy",
  CashDash = "notification_desc_cash_dash",
  ItemCoolDown = "notification_desc_item_cooldown"
}
NotificationModel = {}
NotificationModel.__index = NotificationModel
NotificationModel.NotifyInterval = 86400
NotificationModel.MaxNumberPerInterval = 4

function NotificationModel:Init()
  self:UnregisterAll()
end

function NotificationModel:LoadServerConfig()
  self.m_config = GM.ConfigModel:GetServerConfig(ServerConfigKey.Notify)
  self:_UpdateAuthRequestTimesInfo()
end

function NotificationModel:LoadFileConfig()
  self.m_TextKeyConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.NotificationText)
end

function NotificationModel:_UpdateAuthRequestTimesInfo()
  local nLocalTimesKey = PlayerPrefs.GetInt(EPlayerPrefKey.NotificationClearTimesKey, 0)
  local nServerTimesKey = GM.ConfigModel:GetGeneralConfByType(EGeneralConfType.NotifyRemindTimes)
  if nServerTimesKey ~= nil and nServerTimesKey ~= nLocalTimesKey then
    PlayerPrefs.SetInt(EPlayerPrefKey.NotificationClearTimesKey, nServerTimesKey)
    PlayerPrefs.SetInt(EPlayerPrefKey.NotifiWindowPopTimes, 0)
  end
end

function NotificationModel:LateInit()
  self.m_inited = true
  self.m_helpers = {
    ComeBackNotificationHelper,
    EnergyFullNotificationHelper,
    ItemSpreadRecoveryNotificationHelper,
    CashDashNotificationHelper,
    LollipopNotificationHelper,
    CoconutNotificationHelper,
    PinataNotificationHelper
  }
  Log.Info("Platform Notification " .. (PlatformInterface.IsNotificationsEnabled() and "enabled" or "not enabled"), "NotificationModel")
end

function NotificationModel:LogStateBI()
  local lastTime = PlayerPrefs.GetInt(EPlayerPrefKey.NotifiStateKey, 0)
  local serTime = GM.GameModel:GetServerTime()
  if serTime // NotificationModel.NotifyInterval > lastTime // NotificationModel.NotifyInterval then
    local gameSw = PlayerPrefs.GetInt(EPlayerPrefKey.OpenNotification, 1)
    local systemSw = PlatformInterface.IsNotificationsEnabled() and 1 or 0
    PlayerPrefs.SetInt(EPlayerPrefKey.NotifiStateKey, serTime)
    GM.BIManager:LogAction(EBIType.NotificationState, {gameSw = gameSw, systemSw = systemSw})
  end
end

function NotificationModel:RegisterAll()
  if not self.m_inited then
    return
  end
  if self.m_config == nil or PlayerPrefs.GetInt(EPlayerPrefKey.OpenNotification, 1) == 0 or not PlatformInterface.IsNotificationsEnabled() then
    return
  end
  self:UnregisterAll()
  for _, oOneCfg in pairs(self.m_config) do
    local notificationList = self:_GenerateList(oOneCfg)
    local intervaleMap = self._SplitListByInterval(notificationList, oOneCfg)
    for _, list in pairs(intervaleMap) do
      local selectedList = self._FilterList(list, oOneCfg)
      for _, notification in ipairs(selectedList) do
        self:_Schedule(notification)
      end
    end
  end
end

function NotificationModel:UnregisterAll()
  CSNotificationManager:CancelAllNotifi()
end

function NotificationModel:_GenerateList(oConfig)
  local results = {}
  for _, strScene in pairs(oConfig.scene) do
    for _, helper in ipairs(self.m_helpers) do
      if helper.IsSceneExist(strScene) then
        Table.ListAppend(results, helper.Generate(strScene))
      end
    end
  end
  if oConfig.pushtimetype == 1 and type(oConfig.pushtime) == "table" and oConfig.pushtime[1] ~= nil and oConfig.pushtime[2] ~= nil then
    self:_CalculateLocalTime(results, oConfig.pushtime)
  end
  table.sort(results, function(a, b)
    return a.Delay < b.Delay
  end)
  return results
end

function NotificationModel:_CalculateLocalTime(results, pushtime)
  local nLeftHour = pushtime[1]
  local nRightHour = pushtime[2]
  local llLocalTime = os.time()
  local GetZeroTime = function(llCurTime)
    local date_table = os.date("*t", llCurTime)
    date_table.hour = 0
    date_table.min = 0
    date_table.sec = 0
    return os.time(date_table)
  end
  for _, pushInfo in pairs(results) do
    local llPushTime = llLocalTime + pushInfo.Delay
    local strHour = os.date("%H", llPushTime)
    local hour = tonumber(strHour)
    local llCurZeroTime = GetZeroTime(llPushTime)
    local llNextZeroTime = llCurZeroTime + 86400
    if nLeftHour > hour then
      pushInfo.Delay = llCurZeroTime + nLeftHour * 60 * 60 - llLocalTime
    end
    if nRightHour < hour then
      pushInfo.Delay = llNextZeroTime + nLeftHour * 60 * 60 - llLocalTime
    end
  end
end

function NotificationModel._SplitListByInterval(list, oOneConfig)
  local results = {}
  for _, notification in ipairs(list) do
    local index = notification.Delay // oOneConfig.interval
    if results[index] == nil then
      results[index] = {}
    end
    table.insert(results[index], notification)
  end
  return results
end

function NotificationModel._FilterList(list, oOneConfig)
  local number = oOneConfig.maxNum
  if number >= #list then
    return list
  end
  local results = {}
  for type = NotificationType.MaxPriority, NotificationType.MinPriority do
    if number == 0 then
      break
    end
    for index, notification in ipairs(list) do
      if notification.Type == type then
        table.insert(results, notification)
        table.remove(list, index)
        number = number - 1
        break
      end
    end
  end
  while number ~= 0 and #list ~= 0 do
    table.insert(results, list[1])
    table.remove(list, 1)
    number = number - 1
  end
  return results
end

function NotificationModel:_Schedule(notification)
  CSNotificationManager:ScheduleNotifi(notification.Title, notification.Message, notification.Delay)
end

function NotificationModel:RequestAuthorization()
  PlayerPrefs.SetInt(EPlayerPrefKey.SystemNotiRequsted, 1)
  CSNotificationManager:RequestAuthorization(function(text)
    Log.Info("RequestAuthorization response " .. text, "NotificationModel")
    local granted = string.sub(text, 1, 1) == "1"
    local token = string.sub(text, 3)
    if granted and DeviceInfo.IsSystemIOS() and GM ~= nil then
      GM.PushTokenModel:UpdatePushToken(token)
    end
  end)
end

function NotificationModel:TryOpenNotification(direct2Setting)
  PlayerPrefs.SetInt(EPlayerPrefKey.OpenNotification, 1)
  if not PlatformInterface.IsNotificationsEnabled() then
    if not self:IsSystemNotiRequsted() then
      self:RequestAuthorization()
    elseif direct2Setting then
      PlatformInterface.OpenAppSettings()
    else
      GM.UIManager:OpenView(UIPrefabConfigName.NotificationTutorialWindow)
    end
  else
    GM.UIManager:ShowPromptWithKey("notification_on_hint")
  end
end

function NotificationModel:IsSystemNotiRequsted()
  return PlayerPrefs.GetInt(EPlayerPrefKey.SystemNotiRequsted, 0) == 1
end

function NotificationModel:TryOpenNotificationWindow(eNotiSceneDescKey)
  if IsAutoRun() then
    return false
  end
  if PlatformInterface.IsNotificationsEnabled() then
    return
  end
  if PlayerPrefs.GetInt(EPlayerPrefKey.OpenNotification, 1) == 0 then
    return
  end
  if eNotiSceneDescKey == ENotiSceneDescKey.EnergyEmpty then
    local energy = GM.EnergyModel:GetEnergy(EnergyType.Main)
    if energy >= ItemSpread.GetCostEnergyNum() then
      return
    end
  end
  local serTime = GM.GameModel:GetServerTime()
  local lastTime = PlayerPrefs.GetInt(EPlayerPrefKey.LastNotifiWindowPopTime, 0)
  if serTime - lastTime < 259200 then
    return
  end
  local popTimes = PlayerPrefs.GetInt(EPlayerPrefKey.NotifiWindowPopTimes, 0)
  if 3 <= popTimes then
    return
  end
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.NotificationOpenWindow) then
    return
  end
  GM.UIManager:OpenViewWhenIdle(UIPrefabConfigName.NotificationOpenWindow, eNotiSceneDescKey)
end

function NotificationModel:GetTextTileAndDesc(strScene)
  if self.m_TextKeyConfig == nil then
    Log.Assert(false, "没有配置Push功能的文案文件")
    return "", ""
  end
  if self.m_TextKeyConfig[strScene] ~= nil then
    return self.m_TextKeyConfig[strScene].TitleKey, self.m_TextKeyConfig[strScene].DescKey
  end
  for strKey, oOneCfg in pairs(self.m_TextKeyConfig) do
    if oOneCfg.IsMatch == 1 then
      local bFind = string.find(strScene, strKey)
      if bFind then
        local strTileKey = oOneCfg.TitleKey == nil and "" or oOneCfg.TitleKey
        local strDescKey = oOneCfg.DescKey == nil and "" or oOneCfg.DescKey
        return strTileKey, strDescKey
      end
    end
  end
  return "", ""
end
