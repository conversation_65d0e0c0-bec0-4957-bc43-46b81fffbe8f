BuyEnergyDiscountWindow = setmetatable({}, BaseWindow)
BuyEnergyDiscountWindow.__index = BuyEnergyDiscountWindow

function BuyEnergyDiscountWindow:BeforeOpenCheck()
  return GM.ShopModel:IsEnergyBuyInDiscount()
end

function BuyEnergyDiscountWindow:Init(popupForLackingEnergy)
  self.m_shopModel = GM.ShopModel
  self.m_numText.text = "+" .. tostring(GM.ShopDataModel:GetBuyEnergyCount())
  self.m_energyCount = GM.ShopDataModel:GetBuyEnergyCount()
  self.m_lastTime = self.m_shopModel:GetEnergyDiscountLastTime()
  self:UpdatePerSecond()
  self:UpdateContent(true)
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self.OnShopRefreshed)
  UIUtil.UpdateSortingOrder(self.m_effectGo, self:GetSortingOrder() + 1)
  if popupForLackingEnergy then
    local disableTouchDuration = GM.ConfigModel:GetAntiMisTouchDuration()
    if disableTouchDuration ~= nil and 0 < disableTouchDuration then
      self.m_bDisableTouch = true
      DelayExecuteFuncInView(function()
        self.m_bDisableTouch = nil
      end, disableTouchDuration, self)
    end
  end
  local energyData = self.m_shopModel:GetEnergyDatas(EnergyType.Main)[1]
  self:LogWindowAction(EBIType.UIActionType.Open, popupForLackingEnergy and EBIReferType.ClickEle or EBIReferType.UserClick, {
    gem = energyData.originalCostNum,
    discount = self:_GetOffDiscount(energyData)
  })
end

function BuyEnergyDiscountWindow:OnShopRefreshed()
  if not GM.ShopModel:IsEnergyBuyInDiscount() then
    self:Close()
  end
end

function BuyEnergyDiscountWindow:UpdateContent(former)
  if self.m_callback == nil then
    function self.m_callback()
      if self.m_bDisableTouch then
        return
      end
      self.m_bBuySuccess = self.m_shopModel:BuyEnergy(EnergyType.Main)
      self:Close()
    end
  end
  local energyData = self.m_shopModel:GetEnergyDatas(EnergyType.Main)[1]
  self.m_formerPriceText.text = energyData.originalCostNum
  self.m_propertyButton:Init(EPropertyType.Gem, former and energyData.originalCostNum or energyData.costNum, self.m_callback)
  self.m_discountView:UpdateOffDiscount(self:_GetOffDiscount(energyData))
end

function BuyEnergyDiscountWindow:_GetOffDiscount(energyData)
  return math.floor(100 - 100 * energyData.costNum / energyData.originalCostNum)
end

function BuyEnergyDiscountWindow:TriggerChangeCostEvent()
  self:UpdateContent()
end

function BuyEnergyDiscountWindow:UpdatePerSecond()
  if self.m_lastTime ~= nil then
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(math.max(0, self.m_lastTime - GM.GameModel:GetServerTime()), 2, false, false)
  end
end

function BuyEnergyDiscountWindow:OnCloseFinish()
  BaseWindow.OnCloseFinish(self)
  if self.m_bBuySuccess then
    RewardApi.AcquireRewardsInView({
      {
        [PROPERTY_TYPE] = EPropertyType.Energy,
        [PROPERTY_COUNT] = self.m_energyCount or 0
      }
    }, {eventLock = false})
  elseif self.m_bBuySuccess == false then
    GM.ShopModel:OnLackOfGem(self.m_shopModel:GetEnergyDatas(EnergyType.Main)[1].costNum - GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem))
  end
end
