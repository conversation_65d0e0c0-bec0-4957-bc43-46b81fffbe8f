BoardModelHelper = {}
BoardModelHelper.__index = BoardModelHelper

function BoardModelHelper.GetActiveModel()
  for activityType, activityDefinition in pairs(ExtraBoardActivityDefinition) do
    local adventureActivityMainWindow = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
    if adventureActivityMainWindow ~= nil then
      return GM.ActivityManager:GetModel(activityType):GetBoardModel()
    end
  end
  local currentMode = GM.SceneManager:GetGameMode()
  return BoardModelHelper.GetModelByGameMode(currentMode)
end

function BoardModelHelper.GetModelByGameMode(gameMode)
  if gameMode == nil or gameMode == EGameMode.Board then
    return GM.MainBoardModel
  elseif gameMode == EGameMode.ExtraBoard then
    local activityModel = ExtraBoardActivityModel.GetActiveModel()
    return activityModel:GetBoardModel()
  else
    local activityModel = BoardModelHelper.GetActivityModelByGameMode(gameMode)
    if activityModel ~= nil then
      return activityModel:GetBoardModel()
    else
      Log.Error("获取不到对应model, " .. tostring(gameMode or nil))
      return nil
    end
  end
end

function BoardModelHelper.GetActivityModelByGameMode(gameMode)
  if gameMode == EGameMode.Main or gameMode == EGameMode.Board then
    return nil
  end
  for activityType, activityDefinition in pairs(SpreeActivityDefinition) do
    if gameMode == activityDefinition.GameMode then
      local activityModel = GM.ActivityManager:GetModel(activityType)
      Log.Assert(activityModel ~= nil, "no activity model")
      return activityModel
    end
  end
  return nil
end

function BoardModelHelper.GetActiveActivityModelEventId()
  local activityModel = BoardModelHelper.GetActivityModelByGameMode(GM.SceneManager:GetGameMode())
  if activityModel then
    return activityModel:GetId()
  end
end
