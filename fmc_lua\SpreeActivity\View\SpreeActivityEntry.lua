SpreeActivityEntry = setmetatable({}, HudGeneralButton)
SpreeActivityEntry.__index = SpreeActivityEntry

function SpreeActivityEntry:Init(activityType)
  self.m_activityType = activityType
  self.m_activityDefinition = SpreeActivityDefinition[activityType]
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_IconImg.gameObject:SetActive(false)
  SpriteUtil.SetImage(self.m_IconImg, self.m_activityDefinition.EntryIconName, nil, function()
    self.m_IconImg.gameObject:SetActive(true)
  end)
  self:UpdatePerSecond()
end

function SpreeActivityEntry:UpdatePerSecond()
  if self.m_model ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function SpreeActivityEntry:OnClicked()
  local state = self.m_model:GetState()
  if state == ActivityState.Started then
    GM.UIManager:OpenView(self.m_activityDefinition.LevelWindowPrefabName, self.m_activityType, true)
  end
end
