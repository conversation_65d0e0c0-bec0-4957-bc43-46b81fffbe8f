FlyElement = {}
FlyElement.__index = FlyElement

function FlyElement:Init(sprite<PERSON>ey, floatLabel, forItem)
  self:SetImageSprite(spriteKey, forItem)
  self:SetPropertyLabel(floatLabel)
end

function FlyElement:OnEnable()
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.transform)
end

local FLY_ELEMENT_SIZE = Vector.Create(80, 80)

function FlyElement:SetImageSprite(spriteKey, forItem)
  self.m_image.enabled = false
  if spriteKey ~= nil then
    local setNativeSize = forItem or not GM.ConfigModel:UseNewOrderRewardAnimation()
    if not setNativeSize then
      UIUtil.SetSizeDelta(self.m_image.transform, FLY_ELEMENT_SIZE.x, FLY_ELEMENT_SIZE.y)
    end
    SpriteUtil.SetImage(self.m_image, spriteKey, setNativeSize, function()
      if not self.m_image:IsNull() then
        self.m_image.enabled = true
      end
    end)
  end
end

function FlyElement:SetPropertyLabel(floatLabel)
  if floatLabel then
    self.m_label.text = floatLabel.text
    self.m_shadow.effectColor = floatLabel.shadowColor
    self.m_shadow.effectDistance = floatLabel.shadowDis
    self.m_outline.effectColor = floatLabel.outlineColor
    self.m_outline.effectDistance = floatLabel.outlineDis
    self.m_label.color = floatLabel.textColor
    self.m_label.fontSize = floatLabel.size
    UIUtil.SetActive(self.m_label.gameObject, true)
  else
    UIUtil.SetActive(self.m_label.gameObject, false)
  end
end

FloatLabel = {}
FloatLabel.__index = FloatLabel

function FloatLabel:OnEnable()
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.transform)
end
