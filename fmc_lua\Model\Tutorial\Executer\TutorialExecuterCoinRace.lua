local Step = {ClickEntry = "1"}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._OnCloseView)
  EventDispatcher.AddListener(EEventType.CoinRaceSignUpClicked, self, self._OnSignUpClicked)
  for _, activityDefinition in pairs(CoinRaceActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnCoinRaceStateChange)
  end
end

function Executer:_OnOpenView(msg)
  for _, activityDefinition in pairs(CoinRaceActivityDefinition) do
    if msg.name == activityDefinition.NoticeWindowPrefabName and activityDefinition.TutorialEntryId == self:GetTutorialId() then
      local window = GM.UIManager:GetOpenedViewByName(activityDefinition.NoticeWindowPrefabName)
      if window and window:IsEntry() then
        self.m_activityDefinition = activityDefinition
        self.m_window = window
        self:_ExecuteStep1()
      end
    end
  end
end

function Executer:_OnCloseView(msg)
  for _, activityDefinition in pairs(CoinRaceActivityDefinition) do
    if msg.name == activityDefinition.NoticeWindowPrefabName and self.m_strOngoingDatas == Step.ClickEntry then
      self:Finish(self.m_gesture)
    end
  end
end

function Executer:_OnCoinRaceStateChange(msg)
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) and (not msg or not msg.isEnterCompetition) then
    self:Finish(self.m_gesture)
  end
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickEntry
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local window = self.m_window
    if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
      return
    end
    local btnRectTrans = window:GetButtonTransform()
    self.m_btnRect = btnRectTrans
    TutorialHelper.WholeMask()
    TutorialHelper.HighlightForUI(btnRectTrans)
    self.m_gesture = TutorialHelper.TapOnCustomRectTrans(btnRectTrans)
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(self.m_activityDefinition.StartTutorial1TextKey), 50)
    self.m_btnRect = btnRectTrans
  end, 0.2)
end

function Executer:_OnSignUpClicked()
  if self.m_btnRect ~= nil and not self.m_btnRect:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_btnRect)
    self.m_btnRect = nil
  end
end

function Executer:Finish(gesture, arrow)
  if self.m_btnRect ~= nil and not self.m_btnRect:IsNull() then
    TutorialHelper.DehighlightForUI(self.m_btnRect)
    self.m_btnRect = nil
  end
  TutorialExecuter.Finish(self, gesture, arrow)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
