return {
  {
    Id = "70010",
    GroupId = 1,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e4tapas_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "70020",
    GroupId = 1,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1icytre_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70030",
    GroupId = 1,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_10",
      Count = 1
    }
  },
  {
    Id = "70040",
    GroupId = 1,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e4tapas_2",
      Count = 1
    }
  },
  {
    Id = "70050",
    GroupId = 1,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e4tapas_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70060",
    GroupId = 1,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_12", Count = 1}
  },
  {
    Id = "70070",
    GroupId = 1,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_13",
      Count = 1
    }
  },
  {
    Id = "70080",
    GroupId = 2,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2tapas_4",
      Count = 1
    }
  },
  {
    Id = "70090",
    GroupId = 2,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70100",
    GroupId = 2,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "70110",
    GroupId = 2,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "ds_fd_16", Count = 1}
  },
  {
    Id = "70120",
    GroupId = 2,
    ChapterId = 7,
    Requirement_1 = {Type = "it_4_2_5", Count = 1},
    Requirement_2 = {Type = "it_a7_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70130",
    GroupId = 2,
    ChapterId = 7,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_7e4tapas_5",
      Count = 1
    }
  },
  {
    Id = "70140",
    GroupId = 2,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "70150",
    GroupId = 3,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e1cockt_21",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70160",
    GroupId = 3,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e1tapas_19",
      Count = 1
    }
  },
  {
    Id = "70170",
    GroupId = 3,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6assort_4",
      Count = 1
    }
  },
  {
    Id = "70180",
    GroupId = 3,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "70190",
    GroupId = 3,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70200",
    GroupId = 3,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "70210",
    GroupId = 3,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "70220",
    GroupId = 4,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e1semi_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_2",
      Count = 1
    }
  },
  {
    Id = "70230",
    GroupId = 4,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "70240",
    GroupId = 4,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70250",
    GroupId = 4,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "70260",
    GroupId = 4,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_7e5mt_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "70270",
    GroupId = 4,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "it_4_2_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70280",
    GroupId = 4,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_6e2mt_14",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "70290",
    GroupId = 5,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "70300",
    GroupId = 5,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e3soup_2",
      Count = 1
    }
  },
  {
    Id = "70310",
    GroupId = 5,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70320",
    GroupId = 5,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_15",
      Count = 1
    }
  },
  {
    Id = "70330",
    GroupId = 5,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "70340",
    GroupId = 5,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "ds_7e5mt_3", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70350",
    GroupId = 5,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "70360",
    GroupId = 6,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e6cockt_7",
      Count = 1
    }
  },
  {
    Id = "70370",
    GroupId = 6,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e4semi_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_10",
      Count = 1
    }
  },
  {
    Id = "70380",
    GroupId = 6,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70390",
    GroupId = 6,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "70400",
    GroupId = 6,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    }
  },
  {
    Id = "70410",
    GroupId = 6,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e6tapas_15",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70420",
    GroupId = 6,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "70430",
    GroupId = 7,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e2tapas_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_15",
      Count = 1
    }
  },
  {
    Id = "70440",
    GroupId = 7,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfru_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_13",
      Count = 1
    }
  },
  {
    Id = "70450",
    GroupId = 7,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70460",
    GroupId = 7,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "70470",
    GroupId = 7,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "70480",
    GroupId = 7,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e1cockt_22",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70490",
    GroupId = 7,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "70500",
    GroupId = 8,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "70510",
    GroupId = 8,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_6",
      Count = 1
    }
  },
  {
    Id = "70520",
    GroupId = 8,
    ChapterId = 7,
    Requirement_1 = {Type = "it_1_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70530",
    GroupId = 8,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_12",
      Count = 1
    }
  },
  {
    Id = "70540",
    GroupId = 8,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70550",
    GroupId = 8,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_24",
      Count = 1
    }
  },
  {
    Id = "70560",
    GroupId = 8,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e1tapas_11",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "70570",
    GroupId = 9,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_3",
      Count = 1
    }
  },
  {
    Id = "70580",
    GroupId = 9,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e1semi_8",
      Count = 1
    }
  },
  {
    Id = "70590",
    GroupId = 9,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_fd_19", Count = 1},
    Requirement_2 = {Type = "it_2_1_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70600",
    GroupId = 9,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e4tapas_22",
      Count = 1
    }
  },
  {
    Id = "70610",
    GroupId = 9,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "70620",
    GroupId = 9,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e1tapas_28",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70630",
    GroupId = 9,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "70640",
    GroupId = 10,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_e4sf_14", Count = 1}
  },
  {
    Id = "70650",
    GroupId = 10,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "70660",
    GroupId = 10,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70670",
    GroupId = 10,
    ChapterId = 7,
    Requirement_1 = {Type = "it_4_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_19",
      Count = 1
    }
  },
  {
    Id = "70680",
    GroupId = 10,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70690",
    GroupId = 10,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "70700",
    GroupId = 10,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_6e2flb_5",
      Count = 1
    }
  },
  {
    Id = "70710",
    GroupId = 11,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "70720",
    GroupId = 11,
    ChapterId = 7,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70730",
    GroupId = 11,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "70740",
    GroupId = 11,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70750",
    GroupId = 11,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "70760",
    GroupId = 11,
    ChapterId = 7,
    Requirement_1 = {Type = "it_a7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "70770",
    GroupId = 11,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e4tapas_30",
      Count = 1
    }
  },
  {
    Id = "70780",
    GroupId = 12,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "70790",
    GroupId = 12,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70800",
    GroupId = 12,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_16",
      Count = 1
    }
  },
  {
    Id = "70810",
    GroupId = 12,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "70820",
    GroupId = 12,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e4tapas_27",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70830",
    GroupId = 12,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "70840",
    GroupId = 12,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e4tapas_22",
      Count = 1
    }
  },
  {
    Id = "70850",
    GroupId = 13,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "70860",
    GroupId = 13,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e4tapas_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70870",
    GroupId = 13,
    ChapterId = 7,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "70880",
    GroupId = 13,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_13",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "70890",
    GroupId = 13,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_21",
      Count = 1
    }
  },
  {
    Id = "70900",
    GroupId = 13,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70910",
    GroupId = 13,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2tapas_7",
      Count = 1
    }
  },
  {
    Id = "70920",
    GroupId = 14,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "70930",
    GroupId = 14,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70940",
    GroupId = 14,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_8",
      Count = 1
    }
  },
  {
    Id = "70950",
    GroupId = 14,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "70960",
    GroupId = 14,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "70970",
    GroupId = 14,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "70980",
    GroupId = 14,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e3soup_2",
      Count = 1
    }
  },
  {
    Id = "70990",
    GroupId = 15,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_20",
      Count = 1
    }
  },
  {
    Id = "71000",
    GroupId = 15,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_mixdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71010",
    GroupId = 15,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_fd_20", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "71020",
    GroupId = 15,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_12",
      Count = 1
    }
  },
  {
    Id = "71030",
    GroupId = 15,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "71040",
    GroupId = 15,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71050",
    GroupId = 15,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_7e5mt_4", Count = 1}
  },
  {
    Id = "71060",
    GroupId = 16,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfru_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "71070",
    GroupId = 16,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "71080",
    GroupId = 16,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71090",
    GroupId = 16,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_8",
      Count = 1
    }
  },
  {
    Id = "71100",
    GroupId = 16,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "71110",
    GroupId = 16,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e4tapas_26",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71120",
    GroupId = 16,
    ChapterId = 7,
    Requirement_1 = {Type = "it_4_2_6", Count = 1},
    Requirement_2 = {Type = "it_a7_2_7", Count = 1}
  },
  {
    Id = "71130",
    GroupId = 17,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e1preingre_1",
      Count = 1
    }
  },
  {
    Id = "71140",
    GroupId = 17,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e4semi_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71150",
    GroupId = 17,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "71160",
    GroupId = 17,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    }
  },
  {
    Id = "71170",
    GroupId = 17,
    ChapterId = 7,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71180",
    GroupId = 17,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "71190",
    GroupId = 17,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e4tapas_5",
      Count = 1
    }
  },
  {
    Id = "71200",
    GroupId = 18,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "71210",
    GroupId = 18,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_10",
      Count = 1
    }
  },
  {
    Id = "71220",
    GroupId = 18,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e6tapas_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71230",
    GroupId = 18,
    ChapterId = 7,
    Requirement_1 = {Type = "clean", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "71240",
    GroupId = 18,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2tapas_7",
      Count = 1
    }
  },
  {
    Id = "71250",
    GroupId = 18,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71260",
    GroupId = 18,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e6tapas_16",
      Count = 1
    },
    Requirement_2 = {Type = "it_1_2_4", Count = 1}
  },
  {
    Id = "71270",
    GroupId = 19,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e4tapas_22",
      Count = 1
    }
  },
  {
    Id = "71280",
    GroupId = 19,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_7e5mt_6", Count = 1}
  },
  {
    Id = "71290",
    GroupId = 19,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e6soup_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71300",
    GroupId = 19,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "71310",
    GroupId = 19,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71320",
    GroupId = 19,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "71330",
    GroupId = 19,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "71340",
    GroupId = 20,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "71350",
    GroupId = 20,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71360",
    GroupId = 20,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_15",
      Count = 1
    }
  },
  {
    Id = "71370",
    GroupId = 20,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "it_4_2_4", Count = 1}
  },
  {
    Id = "71380",
    GroupId = 20,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71390",
    GroupId = 20,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e1cockt_23",
      Count = 1
    }
  },
  {
    Id = "71400",
    GroupId = 20,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfru_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "71410",
    GroupId = 21,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_1", Count = 1},
    Requirement_2 = {Type = "ds_7e5mt_6", Count = 1}
  },
  {
    Id = "71420",
    GroupId = 21,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_7e4tapas_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71430",
    GroupId = 21,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e4tapas_22",
      Count = 1
    }
  },
  {
    Id = "71440",
    GroupId = 21,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "71450",
    GroupId = 21,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_7e4tapas_29",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71460",
    GroupId = 21,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "71470",
    GroupId = 21,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "71480",
    GroupId = 22,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "71490",
    GroupId = 22,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e4tapas_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71500",
    GroupId = 22,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "71510",
    GroupId = 22,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "71520",
    GroupId = 22,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "it_4_2_4", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71530",
    GroupId = 22,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e1tapas_11",
      Count = 1
    }
  },
  {
    Id = "71540",
    GroupId = 22,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2tapas_7",
      Count = 1
    }
  },
  {
    Id = "71550",
    GroupId = 23,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {Type = "it_a7_2_8", Count = 1}
  },
  {
    Id = "71560",
    GroupId = 23,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71570",
    GroupId = 23,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "71580",
    GroupId = 23,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e6tapas_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "71590",
    GroupId = 23,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_23",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71600",
    GroupId = 23,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "71610",
    GroupId = 23,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "71620",
    GroupId = 24,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "71630",
    GroupId = 24,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_fd_8", Count = 1},
    Requirement_2 = {Type = "ds_7e5mt_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71640",
    GroupId = 24,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e4tapas_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "71650",
    GroupId = 24,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e1tapas_11",
      Count = 1
    }
  },
  {
    Id = "71660",
    GroupId = 24,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71670",
    GroupId = 24,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6soup_4",
      Count = 1
    }
  },
  {
    Id = "71680",
    GroupId = 24,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {Type = "it_4_2_5", Count = 1}
  },
  {
    Id = "71690",
    GroupId = 25,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "71700",
    GroupId = 25,
    ChapterId = 7,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71710",
    GroupId = 25,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "71720",
    GroupId = 25,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "71730",
    GroupId = 25,
    ChapterId = 7,
    Requirement_1 = {Type = "ds_e4sf_14", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "71740",
    GroupId = 25,
    ChapterId = 7,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_11",
      Count = 1
    }
  },
  {
    Id = "71750",
    GroupId = 25,
    ChapterId = 7,
    Requirement_1 = {
      Type = "ds_7e5sf_19",
      Count = 1
    }
  }
}
