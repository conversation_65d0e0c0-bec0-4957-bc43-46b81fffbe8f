SettingButton = setmetatable({}, HudGeneralButton)
SettingButton.__index = SettingButton

function SettingButton:Awake()
  HudGeneralButton.Awake(self)
  self:_UpdateRedPoint()
  EventDispatcher.AddListener(EEventType.RefreshSettingStrongTip, self, self._UpdateRedPoint)
end

function SettingButton:_UpdateRedPoint()
  self.m_exclamationGo:SetActive(GM.SDKHelper:HasUnreadCSMsg() or GM.MoreGameModel:IsStrongTip() or GM.AccountManager:ShowBindStrongTip())
end

function SettingButton:OnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.SettingWindow)
end
