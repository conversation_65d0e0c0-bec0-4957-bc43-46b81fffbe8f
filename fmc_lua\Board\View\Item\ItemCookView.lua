ItemCookView = setmetatable({}, BaseItemViewComponent)
ItemCookView.__index = ItemCookView

function ItemCookView:Init(itemCookModel)
  self.m_model = itemCookModel
  AddHandlerAndRecordMap(self.m_model.event, ItemCookEventType.StateChanged, {
    obj = self,
    method = self._OnStateChanged
  })
  AddHandlerAndRecordMap(self.m_model.event, ItemCookEventType.SpeedUp, {
    obj = self,
    method = self._OnSpeedUp
  })
  AddHandlerAndRecordMap(self.m_model.event, ItemCookEventType.UpdateFire, {
    obj = self,
    method = self._UpdateFlambeFire
  })
  AddHandlerAndRecordMap(self.m_model.event, ItemCookEventType.HideFire, {
    obj = self,
    method = self._HideFire
  })
  self.m_cookingSpine:Init()
end

function ItemCookView:SetItemView(itemView)
  BaseItemViewComponent.SetItemView(self, itemView)
  if itemView:GetSpine() ~= nil then
    self:OnSpineLoaded(itemView:GetSpine())
  end
  self:_UpdateView()
  self:_UpdateFlambeFire()
end

function ItemCookView:OnDestroy()
  RemoveAllHandlers(self.m_model.event, self)
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
  if self.m_seq ~= nil then
    self.m_seq:Kill()
    self.m_seq = nil
  end
  if self.m_sliderTween ~= nil then
    self.m_sliderTween:Kill()
    self.m_sliderTween = nil
  end
end

function ItemCookView:OnSpineLoaded(spine)
  self.m_spine = spine
  self:_UpdateView()
end

function ItemCookView:_OnStateChanged()
  if self.m_bIsSpeeding then
    return
  end
  self:_UpdateView()
end

function ItemCookView:_OnSpeedUp()
  if self.m_bIsSpeeding then
    return
  end
  self.m_bIsSpeeding = true
  self.m_cookingTrans.gameObject:SetActive(false)
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.speedCookEffect), self.m_effectsTrans, Vector3.zero, function(go)
    go.transform.localScale = Vector3(36, 36, 1)
    go.transform.localPosition = Vector3(-46, 44, 0)
    go.transform:SetLocalRotation(0, 0, -180)
    DelayExecuteFuncInView(function()
      go:RemoveSelf()
      self.m_bIsSpeeding = false
      self:_UpdateView()
    end, 1, self)
  end)
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.speedCookParticle), self.m_effectsTrans, Vector3.zero, function(go)
    go.transform.localScale = Vector3(100, 100, 100)
    go.transform:SetLocalRotation(180, 0, 0)
  end)
end

function ItemCookView:_UpdateView()
  local oldState = self.m_curState
  local state = self.m_model:GetState()
  self.m_curState = state
  local changed = oldState ~= state
  local canShowTip = state == EItemCookState.Prepare or state == EItemCookState.CanCook
  local canShowCooking = true
  if state == EItemCookState.Cooked then
    canShowCooking = false
  elseif canShowTip then
    canShowCooking = self.m_itemView.inBoard
  end
  self.m_cookingTrans.gameObject:SetActive(canShowCooking)
  self.m_cookedTrans.gameObject:SetActive(state == EItemCookState.Cooked)
  self.m_tipTrans.gameObject:SetActive(canShowTip)
  if canShowTip then
    self.m_tipTrans:SetLocalPosX(canShowCooking and 48 or -48)
  end
  self.m_sliderGo:SetActive(self.m_itemView.inBoard and state == EItemCookState.Cooking)
  self.m_effectsTrans.gameObject:SetActive(self.m_itemView.inBoard)
  if self.m_seq ~= nil then
    self.m_seq:Kill()
    self.m_seq = nil
  end
  ItemCookView.ResetTrans(self.m_cookedTrans)
  ItemCookView.ResetTrans(self.m_tipTrans)
  self.m_cookedEffect:SetActive(false)
  if state == EItemCookState.Empty then
    if changed and oldState == EItemCookState.Cooked then
      GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.takeOutEffect), self.m_effectsTrans, Vector3.zero, function(go)
        go.transform.localScale = Vector3(120, 120, 120)
      end)
      self:_UpdateFlambeFire()
    end
    if self.m_spine then
      self.m_spine:PlayIdleAnimation()
    end
    self.m_cookingSpine:PlayAnimation(self.m_itemView.inBoard and "breath" or "idle", nil, true)
  elseif state == EItemCookState.Prepare or state == EItemCookState.CanCook then
    if changed and oldState == EItemCookState.Cooking then
      GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.revertCookEffect), self.m_effectsTrans, Vector3.zero, function(go)
        DelayExecuteFuncInView(function()
          go:RemoveSelf()
        end, 1.2, self)
        DelayExecuteFuncInView(function()
          self:_UpdateView()
        end, 1, self)
      end)
    else
      self.m_seq = ItemCookView.Shake(self.m_tipTrans)
      if self.m_spine then
        self.m_spine:PlayIdleAnimation()
      end
      self.m_cookingSpine:PlayAnimation(self.m_itemView.inBoard and "breath" or "idle", nil, true)
    end
  elseif state == EItemCookState.Cooked then
    self.m_cookedEffect:SetActive(true)
    self.m_seq = ItemCookView.Shake(self.m_cookedTrans)
    if self.m_spine then
      local cookedIdleAnim = "idle_cooked"
      if not self.m_spine:HasAnimation(cookedIdleAnim) and not self.m_spine:HasAnimation(cookedIdleAnim .. "_still") then
        cookedIdleAnim = nil
      end
      if changed then
        self.m_spine:PlayAnimThenIdle(self:GetCookAnimName("end"), cookedIdleAnim)
        self:_UpdateFlambeFire()
      else
        self.m_spine:PlayIdleAnimation(cookedIdleAnim)
      end
    end
  elseif state == EItemCookState.Cooking then
    if changed then
      GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.startCookEffect), self.m_effectsTrans, Vector3.zero, function(go)
        go.transform.localScale = Vector3(100, 100, 100)
      end)
    end
    if self.m_spine then
      self.m_spine:PlayLoopAnimation(self:GetCookAnimName())
    end
    self.m_cookingSpine:PlayAnimation("cook", nil, true)
  end
  if state == EItemCookState.Cooking then
    self.m_sliderCmp.value = self.m_model:GetTimerAmount()
    self:UpdatePerSecond()
  end
end

function ItemCookView:UpdatePerSecond()
  if self.m_model:GetState() ~= EItemCookState.Cooking then
    return
  end
  if self.m_sliderCmp.value < self.m_model:GetTimerAmount() then
    self.m_sliderCmp.value = self.m_model:GetTimerAmount()
  end
  if self.m_sliderTween ~= nil then
    self.m_sliderTween:Kill()
  end
  local s = DOTween.Sequence()
  s:Append(self.m_sliderCmp:DOValue(self.m_model:GetNextTimerAmount(), 1):SetEase(Ease.Linear))
  self.m_sliderTween = s
end

function ItemCookView:GetCookAnimName(suffix)
  local origin, special = GM.ItemDataModel:GetCookAnimationName(self.m_model:GetRecipe(), suffix)
  if not self.m_spine then
    return origin
  end
  if self.m_spine:HasAnimation(special) then
    return special
  end
  return origin
end

function ItemCookView.Shake(trans)
  local seq = DOTween.Sequence():SetLoops(-1)
  seq:Append(trans:DOScale(1.25, 0.3):SetLoops(2, LoopType.Yoyo))
  seq:AppendInterval(0.3)
  return seq
end

function ItemCookView.ResetTrans(trans)
  trans.localScale = V3One
  trans:SetLocalRotation(V3Zero)
end

function ItemCookView:_UpdateFlambeFire()
  local showFire = self.m_model:IsFlambeTime() and self.m_itemView and self.m_itemView.inBoard and self.m_curState ~= EItemCookState.Cooked
  if self.m_fireGo.activeSelf and not showFire then
    self.m_fireGo:SetActive(false)
  elseif not self.m_fireGo.activeSelf and showFire then
    self.m_fireGo:SetActive(true)
    local isLink = GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link
    self.m_linkFireGo:SetActive(isLink)
    self.m_modeFireGo:SetActive(not isLink)
    self.m_fireGo.transform.localScale = V3Zero
    self.m_fireGo.transform:DOScale(1, 0.2)
  end
end

function ItemCookView:_HideFire()
  self.m_fireGo:SetActive(false)
end
