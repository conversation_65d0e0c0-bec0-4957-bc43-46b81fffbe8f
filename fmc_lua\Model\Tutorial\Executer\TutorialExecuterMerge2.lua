local Step = {Merge = "1"}
local EStep2TextKey = {
  [Step.Merge] = "tutorial_merge_pd_1"
}
local itemId = "pd_1_4"
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.ItemMerged, self, self.OnItemMerged)
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  local items1 = TutorialHelper.GetItems(itemId)
  local items2 = TutorialHelper.GetItems("c#" .. itemId)
  if 0 < #items1 and 0 < #items2 then
    self:_ExecuteStep1(items1[1], items2[1])
    return true
  end
end

function Executer:OnItemMerged()
  if self.m_strOngoingDatas == Step.Merge and self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
  end
end

function Executer:_ExecuteStep1(item1, item2)
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.Merge
  self:_SaveOngoingDatas()
  local from = item1:GetPosition()
  local to = item2:GetPosition()
  GM.TutorialModel:SetForceSourceBoardPosition(from)
  GM.TutorialModel:SetForceTargetBoardPosition(to)
  self.m_gesture = TutorialHelper.DragOnItems(from, to)
  if not self.m_gesture then
    GM.TutorialModel:ClearTempDatas()
    self:Finish(self.m_gesture)
    return
  end
  TutorialHelper.MaskOnItemBoard(from, to)
  TutorialHelper.ShowDialogWithBoardMaskArea(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), from, to)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
