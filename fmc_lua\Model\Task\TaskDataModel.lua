TaskDataModel = {}
TaskDataModel.__index = TaskDataModel

function TaskDataModel:Init()
  self.m_mapChapterTaskData = {}
  self.m_mapChapterTaskStarters = {}
  self.m_mapChapterPreIds = {}
  self.m_mapChapterNextIds = {}
  self.m_mapChapterInitialIds = {}
end

function TaskDataModel:LoadFileConfig()
  self.m_arrTaskGroupConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.TaskGroup)
end

function TaskDataModel:LateInit()
  self:_LoadConfig(GM.TaskManager:GetOngoingChapterName())
end

function TaskDataModel:_LoadConfig(chapterName)
  if self.m_mapChapterTaskData[chapterName] then
    return
  end
  local configs = require("Data.Config.Mainline." .. tostring(chapterName) .. ".TaskConfig_" .. tostring(chapterName))
  if not configs then
    Log.Error(tostring(chapterName) .. " 改造任务配置缺失。")
    return {}
  end
  local chapterId = GM.ChapterDataModel:GetChapterIdByName(chapterName)
  local mapTaskData = {}
  local arrTaskStarters = {}
  local taskData
  for i = 1, #configs do
    taskData = TaskData.Create(configs[i], chapterId, chapterName)
    mapTaskData[taskData.Id] = taskData
    arrTaskStarters[i] = TaskStarter.Create(taskData)
  end
  self.m_mapChapterTaskData[chapterName] = mapTaskData
  self.m_mapChapterTaskStarters[chapterName] = arrTaskStarters
  self:_ParseTaskChain(chapterName)
end

function TaskDataModel:_ParseTaskChain(chapterName)
  local mapNextTaskIds = {}
  local mapPreTaskIds = {}
  local arrInitialTaskIds = {}
  local arrTaskStarters = self.m_mapChapterTaskStarters[chapterName]
  for _, starter in ipairs(arrTaskStarters) do
    local taskId = starter:GetTaskId()
    mapPreTaskIds[taskId] = {}
    local condition = starter:GetCondition()
    if condition == nil then
      arrInitialTaskIds[#arrInitialTaskIds + 1] = taskId
    else
      for _, preTaskId in ipairs(condition.arrPreTaskIds) do
        mapNextTaskIds[preTaskId] = mapNextTaskIds[preTaskId] or {}
        mapNextTaskIds[preTaskId][#mapNextTaskIds[preTaskId] + 1] = taskId
        mapPreTaskIds[taskId][#mapPreTaskIds[taskId] + 1] = preTaskId
      end
    end
  end
  self.m_mapChapterPreIds[chapterName] = mapPreTaskIds
  self.m_mapChapterNextIds[chapterName] = mapNextTaskIds
  self.m_mapChapterInitialIds[chapterName] = arrInitialTaskIds
end

function TaskDataModel:GetTaskDatasByChapter(chapterName)
  local mapTaskData = self.m_mapChapterTaskData[chapterName]
  if not mapTaskData then
    self:_LoadConfig(chapterName)
    mapTaskData = self.m_mapChapterTaskData[chapterName]
  end
  return mapTaskData
end

function TaskDataModel:GetTaskData(chapterName, taskId)
  local mapTaskData = self:GetTaskDatasByChapter(chapterName)
  local taskData = mapTaskData[taskId]
  if not taskData then
    Log.Error("没有找到对应的任务配置 taskId:" .. tostring(taskId))
  end
  return taskData or {}
end

function TaskDataModel:GetChapterTaskCount(chapterName)
  local mapTaskData = self:GetTaskDatasByChapter(chapterName)
  return Table.GetMapSize(mapTaskData)
end

function TaskDataModel:GetTaskStartersByChapter(chapterName)
  local arrTaskStarters = self.m_mapChapterTaskStarters[chapterName]
  if not arrTaskStarters then
    self:_LoadConfig(chapterName)
    arrTaskStarters = self.m_mapChapterTaskStarters[chapterName]
  end
  return arrTaskStarters
end

function TaskDataModel:GetTaskPreIdsByChapter(chapterName)
  local mapPreIds = self.m_mapChapterPreIds[chapterName]
  if not mapPreIds then
    self:_LoadConfig(chapterName)
    mapPreIds = self.m_mapChapterPreIds[chapterName]
  end
  return mapPreIds
end

function TaskDataModel:GetTaskNextIdsByChapter(chapterName)
  local mapPreIds = self.m_mapChapterNextIds[chapterName]
  if not mapPreIds then
    self:_LoadConfig(chapterName)
    mapPreIds = self.m_mapChapterNextIds[chapterName]
  end
  return mapPreIds
end

function TaskDataModel:GetTaskInitialIdsByChapter(chapterName)
  local mapInitialTaskIds = self.m_mapChapterInitialIds[chapterName]
  if not mapInitialTaskIds then
    self:_LoadConfig(chapterName)
    mapInitialTaskIds = self.m_mapChapterInitialIds[chapterName]
  end
  return mapInitialTaskIds
end

function TaskDataModel:GetTaskGroupCount()
  return #self.m_arrTaskGroupConfig
end

function TaskDataModel:GetTaskGroupNeedNum(groupId)
  local config = self.m_arrTaskGroupConfig[groupId]
  if not config then
    return 0
  end
  return config.Num
end

function TaskDataModel:GetTaskGroupRewards(groupId)
  local config = self.m_arrTaskGroupConfig[groupId]
  if not config then
    Log.Error("TaskGroupConfig 不存在：" .. tostring(groupId))
    return {}
  end
  return config.Rewards
end
