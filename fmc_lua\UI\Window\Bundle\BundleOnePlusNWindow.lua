BundleOnePlusNWindow = setmetatable({disableEffectWhenCloseView = true}, BundleBaseWindow)
BundleOnePlusNWindow.__index = BundleOnePlusNWindow

function BundleOnePlusNWindow:UpdatePerSecond()
  if not self.m_bundleEndTime then
    self.m_bundleEndTime = self.m_model:GetBundleTriggerEndTime(self.m_dataGroup)
  end
  local restDuration = self.m_bundleEndTime - GM.GameModel:GetServerTime()
  if self.m_countDownText then
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(restDuration, 2, false, false)
  end
  if restDuration <= 0 then
    UIUtil.SetActive(self.m_countDownGo, false)
    return
  end
end

function BundleOnePlusNWindow:OnBundleDataRefreshed(msg)
  if self.m_model ~= nil and self.m_dataGroup ~= nil and self.m_model:HasPurchaseFinished(self.m_dataGroup) then
    DelayExecuteFuncInView(function()
      self:Close()
    end, 1, self)
    return
  end
  BundleBaseWindow.OnBundleDataRefreshed(self, msg)
end

function BundleOnePlusNWindow:OnDestroy()
  BundleBaseWindow.OnDestroy(self)
  if self.m_seq ~= nil then
    self.m_seq:Kill()
    self.m_seq = nil
  end
  GM.UIManager:RemoveAllEventLocks(self)
end

function BundleOnePlusNWindow:Init(...)
  BundleBaseWindow.Init(self, ...)
  self.m_model = GM.BundleManager:GetModel(self.m_dataGroup:GetBundleType())
  local uiType = self.m_dataGroup:GetBundleUIType()
  self.m_uiStyle = BundleUIType[uiType]
  UIUtil.SetActive(self.m_cellOrigin1, false)
  UIUtil.SetActive(self.m_cellOrigin2, false)
  local NCount = self.m_model:GetNCount(self.m_dataGroup)
  local cellPrefab = NCount == 1 and self.m_cellOrigin1 or self.m_cellOrigin2
  self.m_layoutGroup.spacing = NCount == 1 and -90 or 0
  self.m_descText.text = GM.GameTextModel:GetText(self.m_uiStyle.descText, NCount)
  self.m_cells = {}
  local cell
  for index = 1, NCount + 1 do
    cell = GameObject.Instantiate(cellPrefab, self.m_nodeRootRect):GetLuaTable()
    local curData = self.m_dataGroup:GetConfigDataByIndex(index)
    if curData ~= nil then
      cell.gameObject:SetActive(true)
      cell:Init(self, self.m_dataGroup, index, NCount)
    end
    self.m_cells[index] = cell
  end
  if self.m_headerSpine then
    self.m_headerSpine:Init()
    self.m_headerSpine:SetAnimation("appear" .. NCount, false)
    self.m_headerSpine:AddAnimation("idle" .. NCount, true)
  end
  self:UpdatePerSecond()
  EventDispatcher.AddListener(EEventType.BundleIAPRestoreSuccess, self, self.OnBundleIAPRestoreSuccess)
end

function BundleOnePlusNWindow:OnBundleIAPRestoreSuccess(groupId)
  if self.m_dataGroup ~= nil and self.m_dataGroup:GetGroupId() == groupId then
    self:Close()
  end
end

function BundleOnePlusNWindow:GetPurchaseIds()
  if self.m_dataGroup ~= nil then
    return self.m_dataGroup:GetBundleIds()
  end
end

function BundleOnePlusNWindow:PlayUnlockAnimation()
  GM.UIManager:SetEventLock(true, self)
  local sequence = DOTween.Sequence()
  for i = 1, self.m_model:GetNCount(self.m_dataGroup) do
    local cell = self.m_cells[i + 1]
    local transform = cell:GetLockGo().transform
    sequence:AppendInterval(0.3)
    sequence:AppendCallback(function()
      cell:PlayUnlockSpine()
    end)
    sequence:AppendInterval(0.3)
    sequence:AppendCallback(function()
      cell:PlayUnlockEffect()
    end)
    sequence:Append(transform:DOShakeAnchorPos(0.4, 4, 20, 90, true))
    sequence:Append(transform:DOLocalMoveY(transform.localPosition.y + 30, 0.1):SetEase(Ease.OutQuart))
    sequence:Append(transform:DOLocalMoveY(transform.localPosition.y - 1000, 0.45):SetEase(Ease.InQuart))
    sequence:AppendCallback(function()
      cell:SetUnlocked()
    end)
  end
  sequence:OnComplete(function()
    GM.UIManager:SetEventLock(false, self)
    self.m_seq = nil
  end)
  self.m_seq = sequence
end

function BundleOnePlusNWindow:OnCloseView()
  BundleBaseWindow.OnCloseView(self)
  if self.m_dataGroup ~= nil and self.m_model ~= nil and self.m_dataGroup:IsDisposable() then
    self.m_model:TryRecoverFreeRewards(self.m_dataGroup, true)
  end
  for _, cell in pairs(self.m_cells) do
    cell:OnCloseView()
  end
end

BundleOnePlusNCell = {}
BundleOnePlusNCell.__index = BundleOnePlusNCell

function BundleOnePlusNCell:Init(window, dataGroup, index, nCount)
  self.m_window = window
  self.m_model = GM.BundleManager:GetModel(dataGroup:GetBundleType())
  self.m_dataGroup = dataGroup
  self.m_index = index
  self.m_uiStyle = BundleUIType[self.m_dataGroup:GetBundleUIType()]
  self.m_lockPosition = self.m_lockGo.transform.localPosition
  self:UpdateContent(self.m_dataGroup:GetConfigDataByIndex(index))
  self.m_frameEffect.transform:SetLocalScaleX(nCount == 1 and 1.2 or 1)
  self.m_frameEffect.gameObject:SetActive(false)
  self.m_lockSpine:Init()
  if not self.m_model:IsIndexClaimed(self.m_dataGroup, self.m_index) then
    self.m_addGo.transform:SetLocalScale(0)
    DelayExecuteFuncInView(function()
      self:PlayScaleAnimation()
      DelayExecuteFuncInView(function()
        self.m_frameEffect.gameObject:SetActive(true)
        self:PlayAddAnimation()
      end, 0.27, self, false)
    end, 0.3, self, false)
  end
end

function BundleOnePlusNCell:OnDestroy()
  self:_ClearTween()
  Scheduler.UnscheduleTarget(self)
end

function BundleOnePlusNCell:OnCloseView()
  Scheduler.UnscheduleTarget(self)
  self.m_frameEffect.gameObject:SetActive(false)
end

function BundleOnePlusNCell:_ClearTween()
  if self.m_tween then
    self.m_tween:Kill()
    self.m_tween = nil
  end
  if self.m_addTween then
    self.m_addTween:Kill()
    self.m_addTween = nil
  end
end

function BundleOnePlusNCell:UpdateContent(data)
  self.m_data = data
  UIUtil.SetActive(self.m_addGo, self.m_index > 1)
  self:_UpdateRewards()
  self:_UpdateButton()
end

function BundleOnePlusNCell:_UpdateButton()
  local bClaimed = self.m_model:IsIndexClaimed(self.m_dataGroup, self.m_index)
  local isFree = self.m_data:GetPurchaseId() == nil
  UIUtil.SetActive(self.m_freeButtonGo, not bClaimed and isFree)
  UIUtil.SetActive(self.m_IAPButtonGo, not bClaimed and not isFree)
  UIUtil.SetActive(self.m_claimedButtonGo, bClaimed)
  UIUtil.SetActive(self.m_claimedMaskGo, bClaimed)
  if not isFree then
    self.m_IAPText.text = GM.InAppPurchaseModel:GetLocalizedPrice(self.m_data:GetPurchaseId())
  end
  self.m_bUnlocked = self:_IsLogicUnlocked()
  self:UpdateLockState()
end

function BundleOnePlusNCell:_UpdateRewards()
  self.m_rewardContent:Init(self.m_data:GetGoods())
end

function BundleOnePlusNCell:SetUnlocked()
  self.m_bUnlocked = true
  self:UpdateLockState()
  self:PlayScaleAnimation()
  DelayExecuteFuncInView(function()
    self.m_frameEffect:Play()
  end, 0.27, self, false)
end

function BundleOnePlusNCell:UpdateLockState()
  local unlock = self:IsInCurStep()
  local bClaimed = self.m_model:IsIndexClaimed(self.m_dataGroup, self.m_index)
  UIUtil.SetActive(self.m_lockGo, not bClaimed and not unlock)
  self.m_lockGo.transform.localPosition = self.m_lockPosition
end

function BundleOnePlusNCell:PlayUnlockSpine()
  self.m_lockSpine:SetAnimation("0_open_1", false)
end

function BundleOnePlusNCell:PlayUnlockEffect()
  self.m_unlockEffectGo:SetActive(true)
end

function BundleOnePlusNCell:PlayScaleAnimation()
  self:_ClearTween()
  local s = DOTween:Sequence()
  s:Append(self.m_bgTrans:DOScale(1.1, 0.2))
  s:Append(self.m_bgTrans:DOScale(1, 0.3333333333333333))
  s:AppendCallback(function()
    self.m_tween = nil
  end)
  self.m_tween = s
end

function BundleOnePlusNCell:PlayAddAnimation()
  local s = DOTween.Sequence()
  s:Append(self.m_addGo.transform:DOScale(1, 0.2):SetEase(Ease.OutBack))
  s:AppendCallback(function()
    self.m_addTween = nil
  end)
  self.m_addTween = s
end

function BundleOnePlusNCell:_IsLogicUnlocked()
  if self.m_index > 1 and not self.m_model:IsIndexClaimed(self.m_dataGroup, 1) then
    return false
  end
  return not self.m_model:IsIndexClaimed(self.m_dataGroup, self.m_index)
end

function BundleOnePlusNCell:IsInCurStep()
  if not self.m_bUnlocked then
    return false
  end
  return self:_IsLogicUnlocked()
end

function BundleOnePlusNCell:OnBtnClicked()
  if self:IsInCurStep() then
    self.m_model:BuyBundle(self.m_dataGroup, self.m_index, function(rewards)
      self:_UpdateButton()
      if self.m_data:GetPurchaseId() == nil then
        self.m_rewardContent:PlayRewardAnimation()
      else
        GM.UIManager:OpenView(UIPrefabConfigName.BundleRewardWindow, rewards, self.m_uiStyle.titleText, true, nil, function()
          if self.gameObject:IsNull() or self.m_window.gameObject:IsNull() then
            return
          end
          self.m_window:PlayUnlockAnimation()
        end)
      end
    end)
  else
    GM.UIManager:ShowPromptWithKey("one_plus_bundle_unlock_note")
  end
end

function BundleOnePlusNCell:GetLockGo()
  return self.m_lockGo
end
