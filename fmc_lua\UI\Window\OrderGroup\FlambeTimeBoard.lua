FlambeTimeBoard = {}
FlambeTimeBoard.__index = FlambeTimeBoard

function FlambeTimeBoard:Awake()
  EventDispatcher.AddListener(EEventType.FlambeTimeChanged, self, self.UpdateContent)
  self:UpdateContent()
end

function FlambeTimeBoard:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function FlambeTimeBoard:UpdateContent()
  if not GM.FlambeTimeModel:IsFlambeTimeOrderGroup() then
    self.m_modeContentGo:SetActive(false)
    self.m_linkContentGo:SetActive(false)
    if self.m_helpGo then
      self.m_helpGo:SetActive(false)
    end
    return
  end
  local isLink = GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link
  self.m_modeContentGo:SetActive(not isLink)
  self.m_linkContentGo:SetActive(isLink)
  if self.m_helpGo then
    self.m_helpGo:SetActive(true)
  end
  local remainDur = GM.FlambeTimeModel:GetFlambeTimeRemainDur()
  self.m_modeFireGo:SetActive(remainDur == nil)
  self.m_linkFireGo:SetActive(remainDur == nil)
  self.m_modeTimeGo:SetActive(remainDur ~= nil)
  self.m_linkTimeGo:SetActive(remainDur ~= nil)
  if isLink then
    self.m_curLinkOrderId = GM.FlambeTimeModel:GetLinkOrderId()
  end
  self:UpdateFiredCount()
  self:UpdatePerSecond()
end

function FlambeTimeBoard:UpdateFiredCount()
  if GM.FlambeTimeModel:GetFlambeTimeType() ~= EFlambeTimeType.mode then
    return
  end
  local count = GM.FlambeTimeModel:GetFinishedOrderCount()
  for i = 1, FlambeTimeModeTriggerCount do
    self["m_firedImg" .. i]:SetActive(i <= count)
  end
end

function FlambeTimeBoard:UpdatePerSecond()
  local remainDur = GM.FlambeTimeModel:GetFlambeTimeRemainDur()
  if not remainDur then
    return
  end
  if GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link and self.m_curLinkOrderId ~= GM.FlambeTimeModel:GetLinkOrderId() then
    return
  end
  local timeTxt = TimeUtil.ParseTimeDescription(remainDur, 2)
  self.m_modeTimeText.text = timeTxt
  self.m_linkTimeText.text = timeTxt
end

function FlambeTimeBoard:OnHelpClicked()
  local isLink = GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link
  GM.UIManager:OpenView(isLink and UIPrefabConfigName.FlambeLinkHelpWindow or UIPrefabConfigName.FlambeModeHelpWindow)
end
