BakeOutUIAnimation = setmetatable({}, BaseOrderAnimation)
BakeOutUIAnimation.__index = BakeOutUIAnimation

function BakeOutUIAnimation:Init()
  BaseOrderAnimation.Init(self, self.gameObject.name)
end

function BakeOutUIAnimation:PlayEnterAnimation()
  self:SetTransparent(false)
  self:_SetAnimation("appear", false)
  self:_AddRepeatAnimation("appear")
  self.m_animation:Update(0)
end

function BakeOutUIAnimation:_SetAnimation(shortName, isRepeat)
  local animationName = self:_GetAnimationName(shortName, isRepeat)
  self.m_animation.AnimationState:SetAnimation(0, animationName, isRepeat)
end
