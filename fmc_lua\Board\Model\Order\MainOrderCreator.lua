MainOrderCreatorFixed = {}

function MainOrderCreatorFixed.Create(orderModel, arrNextConfigs)
  if not arrNextConfigs or arrNextConfigs[1] == nil then
    return nil
  end
  local mapFixedRequirements, randomCount = MainOrderCreatorFixed._ClassifyRequirements(arrNextConfigs)
  local arrAvailableCleanItems
  if 0 < randomCount then
    arrAvailableCleanItems = MainOrderCreatorFixed._FindAvailableCleanItems(mapFixedRequirements)
  end
  local arrNewOrders = {}
  local avatarIds = orderModel:GetAvatarIds(#arrNextConfigs, OrderType.Fixed)
  for i, nextConfig in ipairs(arrNextConfigs) do
    local newOrder = MainOrderCreatorFixed._Create(orderModel, arrAvailableCleanItems, nextConfig, avatarIds[i])
    arrNewOrders[#arrNewOrders + 1] = newOrder
  end
  Log.Assert(arrNewOrders[1], "主线订单生成失败！")
  return arrNewOrders
end

function MainOrderCreatorFixed._ClassifyRequirements(arrNextConfigs)
  local randomCount = 0
  local mapFixedRequirements = {}
  for _, nextConfig in ipairs(arrNextConfigs) do
    if nextConfig.Requirement_1.Type ~= CLEAN_ITEM_CODE then
      mapFixedRequirements[nextConfig.Requirement_1.Type] = true
    else
      randomCount = randomCount + 1
    end
    if nextConfig.Requirement_2 then
      if nextConfig.Requirement_2.Type ~= CLEAN_ITEM_CODE then
        mapFixedRequirements[nextConfig.Requirement_2.Type] = true
      else
        randomCount = randomCount + 1
      end
    end
  end
  return mapFixedRequirements, randomCount
end

function MainOrderCreatorFixed._FindAvailableCleanItems(mapFixedRequirements)
  local mapFixedRelaventItems = {}
  local itemDataModel = GM.ItemDataModel
  local mapRelaventItems
  for fixedRequirement, _ in pairs(mapFixedRequirements) do
    _, mapRelaventItems = itemDataModel:GetItemSplits(fixedRequirement)
    for relaventItem, _ in pairs(mapRelaventItems) do
      mapFixedRelaventItems[relaventItem] = true
    end
  end
  local mapAvailableCleanItems = {}
  local arrAvailableTypes = {}
  local arrNotInConfigUnrelaventTypes = {}
  local mainOrderModel = GM.MainBoardModel:GetOrderModel()
  local mapCleanOrderConfig = mainOrderModel.dataModel:GetCleanOrderConfigMap()
  local codeCountMap = GM.MainBoardModel:GetCodeCountMap(true, false, true)
  for itemType, count in pairs(codeCountMap) do
    if not mapFixedRelaventItems[itemType] and mapCleanOrderConfig[itemType] then
      mapAvailableCleanItems[itemType] = count
      arrAvailableTypes[#arrAvailableTypes + 1] = itemType
    else
    end
  end
  table.sort(arrAvailableTypes, function(type1, type2)
    return itemDataModel:GetItemScore(type1) > itemDataModel:GetItemScore(type2)
  end)
  local arrUncookableDs = {}
  local arrAvailableCleanItems = {}
  for _, itemType in ipairs(arrAvailableTypes) do
    local ds = mapCleanOrderConfig[itemType].DS
    if ds then
      local instru = GM.ItemDataModel:GetDishUnlockedInstru(ds)
      if not GM.ItemDataModel:IsUnlocked(instru) then
        arrUncookableDs[#arrUncookableDs + 1] = ds
        ds = nil
      end
    end
    local finalType = ds and ds or itemType
    for i = 1, mapAvailableCleanItems[itemType] do
      arrAvailableCleanItems[#arrAvailableCleanItems + 1] = finalType
    end
  end
  if GameConfig.IsTestMode() then
    TestAccumulatedLogInfo.Start()
    TestAccumulatedLogInfo.TestPrintCleanOrderCreation(arrNotInConfigUnrelaventTypes, mapAvailableCleanItems, arrAvailableTypes, arrUncookableDs, arrAvailableCleanItems)
    TestAccumulatedLogInfo.LogNow()
  end
  return arrAvailableCleanItems
end

function MainOrderCreatorFixed._Create(orderModel, arrAvailableCleanItems, nextConfig, avatarId)
  local arrRequirements = {}
  local arrCleanRequirements = {}
  MainOrderCreatorFixed._ParseRequirement(nextConfig.Id, arrRequirements, arrCleanRequirements, arrAvailableCleanItems, nextConfig.Requirement_1)
  MainOrderCreatorFixed._ParseRequirement(nextConfig.Id, arrRequirements, arrCleanRequirements, arrAvailableCleanItems, nextConfig.Requirement_2)
  if arrRequirements[1] == nil then
    Log.Info("主线订单无棋子，已去除。")
    return nil
  end
  local arrRewards = {}
  local goldCount = MainOrderCreatorFixed.GetGoldRewardCount(arrRequirements)
  if 0 < goldCount then
    arrRewards[#arrRewards + 1] = {
      [PROPERTY_TYPE] = EPropertyType.Gold,
      [PROPERTY_COUNT] = goldCount
    }
  end
  local cleanGoldCount = MainOrderCreatorFixed.GetGoldRewardCount(arrCleanRequirements)
  if nextConfig.Rewards then
    Table.ListAppend(arrRewards, Table.ShallowCopyArray(nextConfig.Rewards))
  end
  RewardApi.CryptRewards(arrRewards)
  return MainOrder.Create(orderModel, nextConfig.Id, nextConfig.GroupId or 0, nextConfig.ChapterId, avatarId, arrRequirements, OrderType.Fixed, GM.GameModel:GetServerTime(), arrRewards, cleanGoldCount)
end

function MainOrderCreatorFixed._ParseRequirement(orderId, arrRequirements, arrCleanRequirements, arrAvailableCleanItems, requirementConfig)
  if not requirementConfig then
    return
  end
  local itemType = requirementConfig.Type
  local isCleanItem = itemType == CLEAN_ITEM_CODE
  if isCleanItem then
    Log.Assert(requirementConfig.Count == 1, "清场棋子 Count 只能是1  orderId:" .. orderId)
    local info = {id = orderId, list = arrAvailableCleanItems}
    GM.BIManager:LogAction(EBIType.GenerateCleanTask, info)
    itemType = arrAvailableCleanItems[1]
    if not itemType then
      Log.Info("主线清场订单棋子不够！")
      return
    else
      table.remove(arrAvailableCleanItems, 1)
    end
  end
  for i = 1, requirementConfig.Count do
    arrRequirements[#arrRequirements + 1] = itemType
    if isCleanItem then
      arrCleanRequirements[#arrCleanRequirements + 1] = itemType
    end
  end
end

function MainOrderCreatorFixed.GetGoldRewardCountByConfig(config)
  local total = 0
  if config.Requirement_1.Type ~= CLEAN_ITEM_CODE then
    local itemModelConfig = GM.ItemDataModel:GetModelConfig(config.Requirement_1.Type, true)
    if itemModelConfig and itemModelConfig.Reward then
      total = total + itemModelConfig.Reward * config.Requirement_1.Count
    end
  end
  if config.Requirement_2 and config.Requirement_2.Type ~= CLEAN_ITEM_CODE then
    local itemModelConfig = GM.ItemDataModel:GetModelConfig(config.Requirement_2.Type, true)
    if itemModelConfig and itemModelConfig.Reward then
      total = total + itemModelConfig.Reward * config.Requirement_2.Count
    end
  end
  return total
end

function MainOrderCreatorFixed.GetGoldRewardCount(arrRequirements)
  local total = 0
  local itemModelConfig
  local itemDataModel = GM.ItemDataModel
  for _, requirement in ipairs(arrRequirements) do
    itemModelConfig = itemDataModel:GetModelConfig(requirement, true)
    if itemModelConfig and itemModelConfig.Reward then
      total = total + itemModelConfig.Reward
    end
  end
  return total
end
