BreakEggReviveWindow = setmetatable({canCloseByAndroidBack = false}, BreakEggBaseWindow)
BreakEggReviveWindow.__index = BreakEggReviveWindow

function BreakEggReviveWindow:Init()
  BreakEggBaseWindow.Init(self)
  local reviveCt = self.m_activityModel:GetRevivePrice()
  self.m_priceText.text = reviveCt
  self.m_hudRectTrans.localScale = Vector3.zero
  self.m_buttonRectTrans.localScale = Vector3.zero
  self.m_bombBgRectTrans.localScale = Vector3.zero
  DOVirtual.DelayedCall(0.5, function()
    self.m_bombBgRectTrans:DOScale(1.8, 0.5):SetEase(Ease.OutBack)
    DOVirtual.DelayedCall(0.4, function()
      self.m_hudRectTrans:DOScale(1.0, 0.5):SetEase(Ease.OutBack)
      self.m_buttonRectTrans:DOScale(1.0, 0.5):SetEase(Ease.OutBack)
    end)
  end)
  UIUtil.UpdateSortingOrder(self.m_boomParticle, self:GetSortingOrder() + 1)
  self.m_boomParticle:Stop()
  EventDispatcher.AddListener(EEventType.PropertyAcquired, self, self.OnPropertyChanged)
  EventDispatcher.AddListener(EEventType.PropertyConsumed, self, self.OnPropertyChanged)
end

function BreakEggReviveWindow:OnPropertyChanged()
  if self.m_gemHudButton then
    self.m_gemHudButton:SyncToModelValue()
  end
  if self.m_energyHudButton then
    self.m_energyHudButton:SyncToModelValue()
  end
end

function BreakEggReviveWindow:OnDestroy()
  BreakEggBaseWindow.OnDestroy(self)
  if self.m_activityModel ~= nil then
    RemoveAllHandlers(self.m_activityModel.event, self)
  end
end

function BreakEggReviveWindow:_OnLeaveClicked()
  local round = self.m_activityModel:GetBreakEggRound()
  local step = self.m_activityModel:GetCurrentStep()
  self.m_activityModel:LogActivity(EBIType.BreakEggRevive, round .. "," .. step .. ",0")
  self.m_activityModel:ResetData()
  GM.UIManager:SetEventLock(true, self)
  self:_Hide()
  
  function self.m_onCompleteClose()
    self.m_bombSkel.AnimationState:Complete("-", self.m_onCompleteClose)
    GM.UIManager:SetEventLock(false, self)
  end
  
  self.m_bombSkel.AnimationState:SetAnimation(0, "bomb_fail", false)
  self.m_bombSkel.AnimationState:Complete("+", self.m_onCompleteClose)
  DOVirtual.DelayedCall(2.2, function()
    self.m_boomParticle:Play()
    DOVirtual.DelayedCall(1.1, function()
      local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BreakEggMainWindow)
      if mainWindow then
        mainWindow:DisPlayStart()
      end
      self:Close()
    end)
  end)
end

function BreakEggReviveWindow:_Hide()
  self.m_hudRectTrans:DOScale(0, 0.2)
  self.m_buttonRectTrans:DOScale(0, 0.2)
  self.m_descRectTrans:DOScale(0, 0.2)
  self.m_breakEggRewardAreaRectTrans:DOScale(0, 0.2)
end

function BreakEggReviveWindow:_OnReviveClicked()
  local gemCost = self.m_activityModel:GetRevivePrice()
  if GM.PropertyDataManager:Consume(EPropertyType.Gem, gemCost, EBIType.BreakEggRevive, false, EBIConsumerType.BreakEgg) then
    self.m_activityModel:ReviveContinue()
    local round = self.m_activityModel:GetBreakEggRound()
    local step = self.m_activityModel:GetCurrentStep()
    self.m_activityModel:LogActivity(EBIType.BreakEggRevive, round .. "," .. step .. ",1," .. gemCost)
    self:Close()
    local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BreakEggMainWindow)
    mainWindow:OnReviveSuccess()
  else
    GM.ShopModel:OnLackOfGem(gemCost - GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem))
  end
end
