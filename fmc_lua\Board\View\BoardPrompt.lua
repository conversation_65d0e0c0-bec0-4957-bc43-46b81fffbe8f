BoardPromptType = {
  MergeItems = "mergeItems",
  TapSpreadItem = "tapSpreadItem",
  FinishOrder = "finishOrder",
  FinishTask = "finishTask",
  TapCacheItems = "tapCacheItems",
  LastOrder = "lastOrder",
  Collect = "collect",
  TapRewardBubble = "tapRewardBubble"
}
BoardPrompt = {}
BoardPrompt.__index = BoardPrompt

function BoardPrompt.sortFunc(item1, item2)
  local level1 = GM.ItemDataModel:GetChainLevel(item1:GetCode())
  local level2 = GM.ItemDataModel:GetChainLevel(item2:GetCode())
  return level1 > level2
end

function BoardPrompt:Init(type)
  self.m_type = type
end

function BoardPrompt:GetType()
  return self.m_type
end

function BoardPrompt:CanStart(boardView)
  return false
end

local maxInt = math.maxinteger

function BoardPrompt:IsOpen(promptConfig)
  if IsAutoRun() then
    return true
  end
  if PlayerPrefs.GetInt(EPlayerPrefKey.OpenHint, 1) == 0 and self.m_type ~= BoardPromptType.LastOrder then
    return false
  end
  local conditionConfig = promptConfig[self.m_type]
  if conditionConfig == nil then
    return true
  end
  local closeLevel = conditionConfig.CloseLevel or maxInt
  return closeLevel > GM.LevelModel:GetCurrentLevel()
end

function BoardPrompt:_Start(boardView)
end

function BoardPrompt:Start(boardView)
  self:_Start(boardView)
end

function BoardPrompt:AutoDo(boardView)
end

function BoardPrompt:StartStep2(boardView)
  return false
end

function BoardPrompt:Stop(boardView)
  self:_Stop(boardView)
end

function BoardPrompt:_Stop(boardView)
end

BoardPromptMergeItems = setmetatable({}, BoardPrompt)
BoardPromptMergeItems.__index = BoardPromptMergeItems

function BoardPromptMergeItems.Create()
  local prompt = setmetatable({}, BoardPromptMergeItems)
  prompt:Init(BoardPromptType.MergeItems)
  return prompt
end

function BoardPromptMergeItems:CanStart(boardView)
  local boardModel = boardView:GetModel()
  self.m_itemModels = boardModel:FindMergePair(boardView:GetPromptIgnoreItems())
  return self.m_itemModels ~= nil
end

function BoardPromptMergeItems:_Start(boardView)
  local firstItemView = boardView:GetItemView(self.m_itemModels[1])
  local secondItemView = boardView:GetItemView(self.m_itemModels[2])
  if firstItemView ~= nil and secondItemView ~= nil then
    firstItemView:PlayPromptAnimation(secondItemView.transform.position, self.m_itemModels[2])
    secondItemView:PlayPromptAnimation(firstItemView.transform.position, self.m_itemModels[1])
  end
end

function BoardPromptMergeItems:AutoDo(boardView)
  self:_Stop(boardView)
  local boardModel = boardView:GetModel()
  if boardModel:CanItemMove(self.m_itemModels[1]) then
    boardModel:DragItem(self.m_itemModels[1], self.m_itemModels[2]:GetPosition())
  else
    boardModel:DragItem(self.m_itemModels[2], self.m_itemModels[1]:GetPosition())
  end
end

function BoardPromptMergeItems:_Stop(boardView)
  local firstItemView = boardView:GetItemView(self.m_itemModels[1])
  local secondItemView = boardView:GetItemView(self.m_itemModels[2])
  if firstItemView ~= nil and secondItemView ~= nil then
    firstItemView:StopPromptAnimation()
    secondItemView:StopPromptAnimation()
  end
end

BoardPromptTapSpreadItem = setmetatable({}, BoardPrompt)
BoardPromptTapSpreadItem.__index = BoardPromptTapSpreadItem

function BoardPromptTapSpreadItem.Create()
  local prompt = setmetatable({}, BoardPromptTapSpreadItem)
  prompt:Init(BoardPromptType.TapSpreadItem)
  return prompt
end

function BoardPromptTapSpreadItem:_CanStart(boardView)
  local boardModel = boardView:GetModel()
  local filterDirect = true
  local filter = function(itemModel)
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and itemSpread:GetState() == ItemSpreadState.Opened and itemSpread:GetStorageRestNumber() ~= 0 then
      local generators = GM.ItemDataModel:GetModelConfig(itemSpread:GetItemModel():GetCode()).GeneratedItems
      for _, gen in pairs(generators) do
        if boardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(gen.Code, filterDirect) then
          return true
        end
      end
    end
  end
  self.m_itemModels = boardView:GetModel():FilterItems(filter)
  if #self.m_itemModels == 0 then
    filterDirect = false
    self.m_itemModels = boardView:GetModel():FilterItems(filter)
  end
  return #self.m_itemModels ~= 0
end

function BoardPromptTapSpreadItem:CanStart(boardView)
  if IsAutoRun() then
    return false
  end
  local boardModel = boardView:GetModel()
  if boardModel:IsBoardFull() then
    return false
  end
  return self:_CanStart(boardView)
end

function BoardPromptTapSpreadItem:_Start(boardView)
  Table.ListRandomReorder(self.m_itemModels)
  table.sort(self.m_itemModels, self.sortFunc)
  local itemView = boardView:GetItemView(self.m_itemModels[1])
  boardView:ShowHandTapEffect(itemView.transform.position)
end

function BoardPromptTapSpreadItem:_Stop(boardView)
  boardView:HideHandTapEffect()
end

BoardPromptFinishOrder = setmetatable({}, BoardPrompt)
BoardPromptFinishOrder.__index = BoardPromptFinishOrder

function BoardPromptFinishOrder.Create()
  local prompt = setmetatable({}, BoardPromptFinishOrder)
  prompt:Init(BoardPromptType.FinishOrder)
  return prompt
end

function BoardPromptFinishOrder:CanStart(boardView)
  self.m_cell = nil
  local index
  local cells = boardView:GetOrderArea():GetCells()
  for _, cell in pairs(cells) do
    if cell:GetOrderViewState() == OrderState.CanDeliver and (self.m_cell == nil or index > cell.transform:GetSiblingIndex()) then
      self.m_cell = cell
      index = cell.transform:GetSiblingIndex()
    end
  end
  return self.m_cell ~= nil
end

function BoardPromptFinishOrder:_Start(boardView)
  boardView:GetOrderArea():ScrollToRectTransformVisible(self.m_cell.transform, true)
  self.m_cell:SetHandEffectActive(true)
end

function BoardPromptFinishOrder:AutoDo(boardView)
  self:_Stop(boardView)
  self.m_cell:OnGoButtonClicked()
  if BoardPromptTestSpreadItem then
    BoardPromptTestSpreadItem.CurrentOrder = nil
  end
end

function BoardPromptFinishOrder:_Stop(boardView)
  self.m_cell:SetHandEffectActive(false)
end

BoardPromptFinishTask = setmetatable({}, BoardPrompt)
BoardPromptFinishTask.__index = BoardPromptFinishTask

function BoardPromptFinishTask.Create()
  local prompt = setmetatable({}, BoardPromptFinishTask)
  prompt:Init(BoardPromptType.FinishTask)
  return prompt
end

function BoardPromptFinishTask:CanStart(boardView)
  return GM.TaskManager:CanFinishOngoingTask()
end

function BoardPromptFinishTask:_Start(boardView)
  boardView:GetOrderArea():ScrollToRectTransformVisible(boardView:GetOrderArea():GetTaskBubble().transform, true)
  boardView:GetOrderArea():GetTaskBubble():SetHandEffectActive(true)
end

function BoardPromptFinishTask:AutoDo(boardView)
  self:_Stop(boardView)
  boardView:GetOrderArea():GetTaskBubble():OnClick()
end

function BoardPromptFinishTask:_Stop(boardView)
  boardView:GetOrderArea():GetTaskBubble():SetHandEffectActive(false)
end

BoardPromptTapCacheItems = setmetatable({}, BoardPrompt)
BoardPromptTapCacheItems.__index = BoardPromptTapCacheItems

function BoardPromptTapCacheItems.Create()
  local prompt = setmetatable({}, BoardPromptTapCacheItems)
  prompt:Init(BoardPromptType.TapCacheItems)
  return prompt
end

function BoardPromptTapCacheItems:CanStart(boardView)
  return not boardView:GetModel():IsBoardFull() and boardView:GetModel():GetCachedItemCount() > 0
end

function BoardPromptTapCacheItems:_Start(boardView)
  boardView:GetOrderArea():ScrollToRectTransformVisible(boardView:GetOrderArea():GetBoardCacheRoot().transform, true)
  boardView:GetOrderArea():GetBoardCacheRoot():ShowHandTapEffect(true)
end

function BoardPromptTapCacheItems:AutoDo(boardView)
  self:_Stop(boardView)
  boardView:GetOrderArea():GetBoardCacheRoot():OnClicked()
end

function BoardPromptTapCacheItems:_Stop(boardView)
  boardView:GetOrderArea():GetBoardCacheRoot():ShowHandTapEffect(false)
end

BoardPromptLastOrder = setmetatable({}, BoardPrompt)
BoardPromptLastOrder.__index = BoardPromptLastOrder

function BoardPromptLastOrder.Create()
  local prompt = setmetatable({}, BoardPromptLastOrder)
  prompt:Init(BoardPromptType.LastOrder)
  return prompt
end

function BoardPromptLastOrder:CanStart(boardView)
  local can, item1, item2, targetDish = BoardPromptLastOrder.CanPromptLastOrder(boardView)
  if can then
    self.m_targetDish = targetDish
    self.m_itemModels = {item1, item2}
    if item1 ~= nil and item2 ~= nil and item2:GetComponent(ItemCobweb) == nil and item2:GetComponent(ItemCook) == nil then
      local pos1 = item1:GetPosition()
      local pos2 = item2:GetPosition()
      if pos1:GetX() > pos2:GetX() or pos1:GetX() == pos2:GetX() and pos1:GetY() > pos2:GetY() then
        self.m_itemModels = {item2, item1}
      end
    end
  end
  return can
end

function BoardPromptLastOrder.CanPromptLastOrder(boardView)
  local isAutoRun = IsAutoRun()
  if not isAutoRun and GM.LevelModel:GetCurrentLevel() >= 5 then
    return false
  end
  local boardModel = boardView:GetModel()
  local orders = boardModel:GetOrders()
  orders = Table.GetValueList(orders)
  if not isAutoRun and #orders ~= 1 then
    return false
  end
  if isAutoRun then
    for _, o in ipairs(orders) do
      local can, itemModel1, itemModel2, targetDish = BoardPromptLastOrder._CanPromptLastOrder(boardView, o)
      if can then
        return can, itemModel1, itemModel2, targetDish
      end
    end
    return false
  else
    return BoardPromptLastOrder._CanPromptLastOrder(boardView, orders[1])
  end
end

function BoardPromptLastOrder._CanPromptLastOrder(boardView, order)
  if order:GetState() == OrderState.CanDeliver then
    return false
  end
  local boardModel = boardView:GetModel()
  if not boardView:GetModel():IsBoardFull() and boardView:GetModel():GetCachedItemCount() > 0 then
    return true
  end
  local cookCmps = boardModel:GetAllItemCookCmp(true, false)
  local requirements = order:GetRequirements()
  local requirementFillStates = order:GetRequirementFillStates()
  local unfilled = {}
  for index, requirement in ipairs(requirements) do
    if requirementFillStates[index] == ERequirementFillState.Cooked then
      for _, cookCmp in ipairs(cookCmps) do
        if cookCmp:GetState() == EItemCookState.Cooked and cookCmp:GetRecipe() == requirement and not boardModel:IsBoardFull() then
          return true, cookCmp:GetItemModel()
        end
      end
    elseif requirementFillStates[index] == ERequirementFillState.Init then
      unfilled[requirement] = true
    end
  end
  local filterItemCode
  local filter = function(itemModel)
    return itemModel:GetCode() == filterItemCode
  end
  local isAutoRun = IsAutoRun()
  local itemDataModel = GM.ItemDataModel
  if isAutoRun then
    local nestedUnfilled = {}
    for itemCode, _ in pairs(unfilled) do
      if itemDataModel:IsDishes(itemCode) then
        local mapUnfilled = boardModel:GetUnfilledDishMaterials(itemCode, not GM.TestAutoRunModel.useInventory)
        for dishMaterial, _ in pairs(mapUnfilled) do
          nestedUnfilled[dishMaterial] = true
        end
      end
    end
    for dishMaterial, _ in pairs(nestedUnfilled) do
      unfilled[dishMaterial] = true
    end
  end
  for requirement, _ in pairs(unfilled) do
    if itemDataModel:IsDishes(requirement) then
      for _, cookCmp in ipairs(cookCmps) do
        local canCook, lackMaterials = cookCmp:CanCook(requirement)
        if canCook and 0 < #lackMaterials and (not isAutoRun or order:CanUseCookCmp(cookCmp)) then
          for _, lackMat in ipairs(lackMaterials) do
            filterItemCode = lackMat
            local items = boardView:GetModel():FilterItems(filter)
            if 0 < #items then
              if isAutoRun then
                order:TryLockCookCmp(cookCmp)
              end
              return true, Table.ListRandomSelectOne(items), cookCmp:GetItemModel(), requirement
            end
          end
        end
      end
    end
  end
  if isAutoRun then
    return false
  end
  local includeCobwebMergePair = boardModel:FindMergePair(boardView:GetPromptIgnoreItems())
  local excludeCobwebMergePair = boardModel:FindMergePair(boardView:GetPromptIgnoreItems(), true)
  if includeCobwebMergePair ~= nil and boardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(includeCobwebMergePair[1]:GetMergedType(), true, true) then
    return true, includeCobwebMergePair[1], includeCobwebMergePair[2]
  elseif excludeCobwebMergePair ~= nil and boardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(excludeCobwebMergePair[1]:GetMergedType(), true, true) then
    return true, excludeCobwebMergePair[1], excludeCobwebMergePair[2]
  end
  local filterDirect = true
  
  function filter(itemModel)
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and itemSpread:GetState() == ItemSpreadState.Opened and itemSpread:GetStorageRestNumber() ~= 0 then
      local generators = itemDataModel:GetModelConfig(itemSpread:GetItemModel():GetCode()).GeneratedItems
      for _, gen in pairs(generators) do
        if boardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(gen.Code, filterDirect, true) then
          return true
        end
      end
    end
  end
  
  local generatorItems = boardView:GetModel():FilterItems(filter)
  if 0 < #generatorItems and not boardModel:IsBoardFull() then
    Table.ListRandomReorder(generatorItems)
    table.sort(generatorItems, BoardPrompt.sortFunc)
    return true, generatorItems[1]
  end
  if includeCobwebMergePair ~= nil and boardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(includeCobwebMergePair[1]:GetMergedType(), false, true) then
    return true, includeCobwebMergePair[1], includeCobwebMergePair[2]
  elseif excludeCobwebMergePair ~= nil and boardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(excludeCobwebMergePair[1]:GetMergedType(), false, true) then
    return true, excludeCobwebMergePair[1], excludeCobwebMergePair[2]
  end
  filterDirect = false
  generatorItems = boardView:GetModel():FilterItems(filter)
  if 0 < #generatorItems and not boardModel:IsBoardFull() then
    Table.ListRandomReorder(generatorItems)
    table.sort(generatorItems, BoardPrompt.sortFunc)
    return true, generatorItems[1]
  end
  return false
end

function BoardPromptLastOrder:_Start(boardView)
  if #self.m_itemModels == 0 then
    boardView:GetOrderArea():ScrollToRectTransformVisible(boardView:GetOrderArea():GetBoardCacheRoot().transform, true)
    boardView:GetOrderArea():GetBoardCacheRoot():ShowHandTapEffect(true)
  elseif #self.m_itemModels == 1 then
    local itemView = boardView:GetItemView(self.m_itemModels[1])
    boardView:ShowHandTapEffect(itemView.transform.position)
  else
    local firstItemView = boardView:GetItemView(self.m_itemModels[1])
    local secondItemView = boardView:GetItemView(self.m_itemModels[2])
    if firstItemView ~= nil and secondItemView ~= nil then
      boardView:ShowHandDragEffect(firstItemView.transform.position, secondItemView.transform.position)
      firstItemView:PlayPromptAnimation(secondItemView.transform.position, self.m_itemModels[2])
      secondItemView:PlayPromptAnimation(firstItemView.transform.position, self.m_itemModels[1])
    end
  end
end

function BoardPromptLastOrder:AutoDo(boardView)
  self:_Stop(boardView)
  if #self.m_itemModels == 0 then
    boardView:GetOrderArea():GetBoardCacheRoot():OnClicked()
  elseif #self.m_itemModels == 1 then
    local itemCook = self.m_itemModels[1]:GetComponent(ItemCook)
    if itemCook then
      itemCook:OnTap()
      return
    end
    local itemSpread = self.m_itemModels[1]:GetComponent(ItemSpread)
    if itemSpread then
      itemSpread:OnTap()
    end
  else
    local firstItemView = boardView:GetItemView(self.m_itemModels[1])
    local secondItemView = boardView:GetItemView(self.m_itemModels[2])
    if firstItemView ~= nil and secondItemView ~= nil then
      local boardModel = boardView:GetModel()
      boardModel:DragItem(self.m_itemModels[1], self.m_itemModels[2]:GetPosition())
      local itemCook = self.m_itemModels[2]:GetComponent(ItemCook)
      if self.m_targetDish and itemCook then
        local canCook, lackedMaterials = itemCook:CanCook(self.m_targetDish)
        if canCook and not lackedMaterials[1] then
          itemCook:StartCook()
          itemCook:OnSpeedUp(true)
        end
      end
    end
  end
end

function BoardPromptLastOrder:_Stop(boardView)
  if #self.m_itemModels == 0 then
    boardView:GetOrderArea():GetBoardCacheRoot():ShowHandTapEffect(false)
  elseif #self.m_itemModels == 1 then
    boardView:HideHandTapEffect()
  else
    boardView:HideHandDragEffect()
    local firstItemView = boardView:GetItemView(self.m_itemModels[1])
    local secondItemView = boardView:GetItemView(self.m_itemModels[2])
    if firstItemView ~= nil and secondItemView ~= nil then
      firstItemView:StopPromptAnimation()
      secondItemView:StopPromptAnimation()
    end
  end
end

BoardPromptCollect = setmetatable({}, BoardPrompt)
BoardPromptCollect.__index = BoardPromptCollect

function BoardPromptCollect.Create()
  local prompt = setmetatable({}, BoardPromptCollect)
  prompt:Init(BoardPromptType.Collect)
  return prompt
end

function BoardPromptCollect:CanStart(boardView)
  local boardModel = boardView:GetModel()
  local filter = function(itemModel)
    if itemModel:GetComponent(ItemCollectable) ~= nil then
      local itemType = itemModel:GetType()
      local chainId = GM.ItemDataModel:GetChainId(itemType)
      local maxLevel = GM.ItemDataModel:GetChainMaxLevel(chainId)
      local curLevel = GM.ItemDataModel:GetChainLevel(itemType)
      if maxLevel ~= nil and curLevel ~= nil then
        return curLevel == maxLevel
      end
    end
    return false
  end
  self.m_itemModels = boardView:GetModel():FilterItems(filter)
  return #self.m_itemModels ~= 0
end

function BoardPromptCollect:_Start(boardView)
  self.m_itemModel = Table.ListRandomSelectOne(self.m_itemModels)
  local itemView = boardView:GetItemView(self.m_itemModel)
  boardView:ShowHandTapEffect(itemView.transform.position)
end

function BoardPromptCollect:AutoDo(boardView)
  self:_Stop(boardView)
  boardView:GetModel():TapItem(self.m_itemModel)
end

function BoardPromptCollect:_Stop(boardView)
  boardView:HideHandTapEffect()
end
