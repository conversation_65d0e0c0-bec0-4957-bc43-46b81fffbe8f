ItemSpreadRecoveryNotificationHelper = setmetatable({}, BaseNotificationHelper)
ItemSpreadRecoveryNotificationHelper.__index = ItemSpreadRecoveryNotificationHelper

function ItemSpreadRecoveryNotificationHelper.IsSceneExist(strScene)
  if strScene == NotificationScene.ItemCooldown then
    return true
  end
  return false
end

function ItemSpreadRecoveryNotificationHelper.Generate(strScene)
  local results = {}
  local strTileKey, strDescKey = GM.NotificationModel:GetTextTileAndDesc(strScene)
  strTileKey = strTileKey ~= "" and strTileKey or "push_cooldown_title"
  strDescKey = strDescKey ~= "" and strDescKey or "push_cooldown_desc"
  for itemModel, _ in pairs(GM.MainBoardModel:GetAllBoardItems()) do
    local itemSpread = itemModel and itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and itemSpread:GetStorageRestNumber() == 0 and itemSpread:GetState() ~= ItemSpreadState.OpenFinish then
      local startTimer = itemSpread:GetStartTimer()
      local delay = startTimer + itemSpread:GetTimerDuration() - GM.GameModel:GetServerTime()
      if 1200 <= delay then
        local itemName = GM.GameTextModel:GetText(ItemNameDefinition.GetName(itemModel:GetType()))
        table.insert(results, {
          Type = NotificationType.ItemSpreadRecovery,
          Title = GM.GameTextModel:GetText(strTileKey),
          Message = GM.GameTextModel:GetText(strDescKey, itemName),
          Delay = delay
        })
      end
    end
  end
  return results
end
