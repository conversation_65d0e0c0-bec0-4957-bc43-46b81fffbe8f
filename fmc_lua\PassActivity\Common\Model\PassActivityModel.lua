PassActivityEventType = {StateChanged = 1}
PassActivityModel = setmetatable({
  eModelType = ActivityModelType.Pass
}, BaseActivityModel)
PassActivityModel.__index = PassActivityModel
PassActivityModel.TimelimitTaskOrderKey = "TimelimitTaskOrder"
PassActivityModel.TimelimitTaskFinishedCountKey = "TimelimitTaskFinishedCount%d"
PassActivityModel.TimelimitTaskFinishedKey = "TimelimitTaskFinished%d"
PassActivityModel.BonusTaskFinishedCountKey = "BonusTaskFinishedCountKey%d"
PassActivityModel.BonusTaskFinishedKey = "BonusTaskFinishedKey%d"
PassActivityModel.CycleTaskIndexKey = "CycleTaskIndex"
PassActivityModel.CycleTaskFinishedCountKey = "CycleTaskFinishedCount"
PassActivityModel.TokenNumberKey = "TokenNumber"
PassActivityModel.TicketKey = "Ticket"
PassActivityModel.MaxTicketKey = "MaxTicket"
PassActivityModel.RewardTakenKey = "RewardTaken%d_%d"
PassActivityModel.SettleKey = "Settle"
PassActivityModel.LastBuyTicketPopupWindowDayKey = "LastBuyTicketPopupWindowDay"
PassActivityModel.BuyTicketPopupWindowOpenedStateEnded = "BuyTicketPopupWindowOpenedStateEnded"
PassActivityModel.FinishedLevelKey = "FinishedLevel"
PassActivityModel.RewardEffectKey = "RewardEffect%d_%d"
PassActivityModel.ProgressEffectKey = "ProgressEffect_%d"
PassActivityModel.RealItemTargetKey = "RealItemTarget_%d_%d"
PassActivityModel.RealBonusItemTargetKey = "RealBonusItemTarget_%d_%d"
PassActivityModel.RealTargetCountKey = "RealTargetCount_%d_%d"
PassActivityModel.RealBonusTargetCountKey = "RealBonusTargetCount_%d_%d"
PassActivityModel.RealActivityTargetCountKey = "RATC_%d_%d"
PassActivityModel.ExtraRewardTakenKey = "ExtraRewardTaken"
PassActivityModel.VIPTaskFinishedCountKey = "VIPTaskFinishedCountKey%d"
PassActivityModel.VIPTaskFinishedKey = "VIPTaskFinished%d"
PassActivityModel.RealVIPItemTargetKey = "RealVIPItemTarget_%d_%d"
PassActivityModel.RealVIPTargetCountKey = "RealVIPTargetCount_%d_%d"
PassActivityModel.FinishTimelimitTask = "FinTLtask"
PassActivityModel.BuyTicketPopLevelsKey = "BPBuyTicketPopLevels"
PassActivityModel.BuyTicketPopCountDown = "bp_vip_countdown"
PassActivityModel.BuyTicketPopMinLevel = "bp_pop_minlevel"
PassActivityModel.MaxTicketTokenCountKey = "bp_svip_token"
PassActivityModel.MaxTicketTokenRatioKey = "bp_svip_multipe"
PassActivityModel.NewIapTypeServer = "battlePassClone"
EBPTicketState = {
  None = 1,
  Upgrade = 2,
  Purchase = 3
}
local EBPTicketType = {
  Normal = 1,
  Max = 2,
  Up = 3
}
local EBPIAPSeries = {
  Default = 1,
  BackUp = 2,
  BackUp2 = 3
}
local arrIAPType = {
  [EBPIAPSeries.Default] = {
    [EBPTicketType.Normal] = EIAPType.passticket,
    [EBPTicketType.Max] = EIAPType.maxpassticket,
    [EBPTicketType.Up] = EIAPType.uppassticket
  },
  [EBPIAPSeries.BackUp] = {
    [EBPTicketType.Normal] = EIAPType.passticket2,
    [EBPTicketType.Max] = EIAPType.maxpassticket2,
    [EBPTicketType.Up] = EIAPType.uppassticket2
  },
  [EBPIAPSeries.BackUp2] = {
    [EBPTicketType.Normal] = EIAPType.passticket3,
    [EBPTicketType.Max] = EIAPType.maxpassticket3,
    [EBPTicketType.Up] = EIAPType.uppassticket3
  }
}

function PassActivityModel:Init(activityType, virtualDBTable)
  self.m_activityDefinition = PassActivityDefinition[activityType]
  self.m_bHasTakeAllRewards = false
  BaseActivityModel.Init(self, activityType, virtualDBTable)
  EventDispatcher.AddListener(EEventType.ItemMerged, self, self._OnItemMerged)
  EventDispatcher.AddListener(EEventType.OrderFinished, self, self._OnOrderFinished)
  EventDispatcher.AddListener(EEventType.PropertyConsumed, self, self._OnPropertyConsumed)
  EventDispatcher.AddListener(EEventType.CosumeEnergy, self, self._OnEnergyConsumed)
  EventDispatcher.AddListener(EEventType.OnClaimedOrderGroupReward, self, self._OnClaimedOrderGroupReward)
  EventDispatcher.AddListener(EEventType.TakeOutDishItem, self, self._OnTaskOutDishItem)
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self._OnLoginFinished)
end

function PassActivityModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function PassActivityModel:GetBoardEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.PassActivity,
    entryPrefabName = self.m_activityDefinition.BoardEntryPrefabName,
    checkFun = function()
      return self:GetState() == ActivityState.Started
    end
  }
end

function PassActivityModel:GetMapEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.PassActivity,
    entryPrefabName = self.m_activityDefinition.MapEntryPrefabName or UIPrefabConfigName.PassActivityEntry,
    hudKey = self.m_activityDefinition.EntryButtonKey,
    checkFun = function()
      return self:GetState() == ActivityState.Started
    end
  }
end

function PassActivityModel:IsActivityOpen()
  return self:GetState() == ActivityState.Started
end

function PassActivityModel:GetResourceLabels()
  return self.m_activityDefinition.ResourceLabels
end

function PassActivityModel:_LoadOtherServerConfig(config)
  self.m_config.levelConfigs = {}
  Log.Assert(config.battlePassReward, "BP 缺失 battlePassReward 配置！")
  for _, lConfig in ipairs(config.battlePassReward) do
    local item = {}
    item.require = lConfig.require
    item.rewards = lConfig.reward
    item.vipRewards = lConfig.golden_reward
    RewardApi.CryptRewards(item.rewards, true)
    RewardApi.CryptRewards(item.vipRewards, true)
    self.m_config.levelConfigs[lConfig.level] = item
  end
  if GameConfig.IsTestMode() and config.battlePassReward and config.battlePassReward[1] and config.battlePassReward[1].golden_reward and config.battlePassReward[1].golden_reward[1] and config.battlePassReward[1].golden_reward[1][PROPERTY_TYPE] ~= EPropertyType.Energy then
    Log.Error("BP 金券第一个奖励应配置为体力！")
  end
  self.m_config.extraLevelConfigs = {}
  if config.battlePassExtraReward then
    local extraLevelCount = #config.battlePassExtraReward
    for _, config in ipairs(config.battlePassExtraReward) do
      self.m_config.extraLevelConfigs[config.id] = config
      RewardApi.CryptRewards(config.reward, true)
    end
    for i = 1, extraLevelCount do
      if self.m_config.extraLevelConfigs[i] == nil then
        Log.Error("BP battlePassExtraReward 配置错误")
      end
    end
  end
  local maxTimelimitOrder = 0
  self.m_config.timelimitTasks = {}
  self.m_config.bonusTasks = {}
  self.m_config.vipTask = {}
  self.m_config.cycleTasks = {}
  Log.Assert(config.battlePassTask ~= nil or config.battlePassTaskNew ~= nil, "BP 缺失 Task 配置！")
  if not Table.IsEmpty(config.battlePassTaskNew) then
    for _, tbConfig in ipairs(config.battlePassTaskNew) do
      self.m_bUseTaskGroup = true
      local taskType = tbConfig.type
      local taskOrder = tbConfig.order
      if tbConfig.type == EPassActivityTaskType.Timelimit then
        local arrTasks = {}
        local taskIndex = 1
        local maxGroupIndex = 0
        for i = 1, 3 do
          for index, config in ipairs(tbConfig["group" .. i] or {}) do
            arrTasks[#arrTasks + 1] = self:_ParseTask(config, taskType, taskOrder, taskIndex, i)
            taskIndex = taskIndex + 1
            maxGroupIndex = i
          end
        end
        if GameConfig.IsTestMode() then
          for i = 1, maxGroupIndex do
            Log.Assert(tbConfig["group" .. i] ~= nil, "battlePassTaskNew group的配置必须连续")
          end
        end
        local taskSet = {
          time = tbConfig.time,
          tasks = arrTasks
        }
        self.m_config.timelimitTasks[tbConfig.order] = taskSet
        maxTimelimitOrder = math.max(maxTimelimitOrder, tbConfig.order)
      elseif tbConfig.type == EPassActivityTaskType.Cycle then
        local arrTasks = {}
        for index, config in ipairs(tbConfig.tasks or {}) do
          arrTasks[#arrTasks + 1] = self:_ParseTask(config, taskType, taskOrder, index)
        end
        self.m_config.cycleTasks = arrTasks
      end
    end
  else
    for _, tbConfig in ipairs(config.battlePassTask) do
      local taskType = tbConfig.type
      local taskOrder = tbConfig.order
      local arrTasks = {}
      for index, config in ipairs(tbConfig.tasks or {}) do
        arrTasks[#arrTasks + 1] = self:_ParseTask(config, taskType, taskOrder, index)
      end
      if tbConfig.type == EPassActivityTaskType.Timelimit then
        local taskSet = {
          time = tbConfig.time,
          tasks = arrTasks
        }
        self.m_config.timelimitTasks[tbConfig.order] = taskSet
        maxTimelimitOrder = math.max(maxTimelimitOrder, tbConfig.order)
      elseif tbConfig.type == EPassActivityTaskType.Cycle then
        self.m_config.cycleTasks = arrTasks
      end
    end
  end
  local bpDurationInDays = (config.eTime - config.sTime) / Sec2Day
  Log.Assert(maxTimelimitOrder >= bpDurationInDays, "活动持续时间内缺失完整的 battlePassTask 配置！")
  for order = 1, maxTimelimitOrder do
    Log.Assert(self.m_config.timelimitTasks[order], "battlePassTask 缺失第 " .. order .. " 天的限时任务配置！")
  end
  self.m_config.mapBuyTicketPopLevels = {}
  local arrPopConfig = self:GetGeneralConfig(PassActivityModel.BuyTicketPopLevelsKey, EConfigParamType.IntArray) or {}
  for _, level in ipairs(arrPopConfig) do
    self.m_config.mapBuyTicketPopLevels[level] = true
  end
  self.m_config.mapBuyTicketPopDays = {}
  local arrPopDayConfig = self:GetGeneralConfig(PassActivityModel.BuyTicketPopCountDown, EConfigParamType.IntArray) or {}
  for _, day in ipairs(arrPopDayConfig) do
    self.m_config.mapBuyTicketPopDays[day] = true
  end
  self.m_popMinLevel = self:GetGeneralConfig(PassActivityModel.BuyTicketPopMinLevel, EConfigParamType.Int) or 2
  local strTokenCount = self:GetGeneralConfig(PassActivityModel.MaxTicketTokenCountKey, EConfigParamType.Int)
  local strTokenRatio = self:GetGeneralConfig(PassActivityModel.MaxTicketTokenRatioKey, EConfigParamType.Float)
  if strTokenCount ~= nil and strTokenRatio ~= nil then
    self.m_config.maxTicketTokenCount = tonumber(strTokenCount)
    self.m_config.maxTicketTokenRatio = tonumber(strTokenRatio)
  end
  self.m_eIapSeries = EBPIAPSeries.Default
  local iapConfig = tonumber(self:GetGeneralConfig(PassActivityModel.NewIapTypeServer, EConfigParamType.Int))
  if iapConfig == 3 then
    self.m_eIapSeries = EBPIAPSeries.BackUp2
  elseif iapConfig then
    self.m_eIapSeries = EBPIAPSeries.BackUp
  end
  self:_CheckIfTakeAllRewards()
end

function PassActivityModel:OnCheckResourcesFinished()
  self:_OnLoginFinished()
end

function PassActivityModel:_OnLoginFinished()
  if not GM.CheckResourcesStageFinished then
    return
  end
  if self:GetState() == ActivityState.Started then
    self:_UpdateShouldRefreshTimelimitTasks()
  end
end

function PassActivityModel:UpdatePerSecond()
  BaseActivityModel.UpdatePerSecond(self)
  if self:GetState() ~= ActivityState.Started then
    return
  end
  self:_UpdateShouldRefreshTimelimitTasks()
end

function PassActivityModel:_CalculateState()
  if self.m_config == nil then
    return ActivityState.Released, -1
  end
  local serverTime = GM.GameModel:GetServerTime()
  if serverTime < self.m_config.sTime then
    return ActivityState.Preparing, self.m_config.sTime
  end
  if serverTime < self.m_config.eTime and not self:IsSettled() and not self:CanSettleBeforeEnd() then
    return ActivityState.Started, self.m_config.eTime
  end
  if self.m_config.rTime and serverTime < self.m_config.rTime then
    return ActivityState.Ended, self.m_config.rTime
  end
  return ActivityState.Released, -1
end

function PassActivityModel:_OnStateChanged()
  if self:GetState() == ActivityState.Started then
    if self.m_dbTable:GetValue(PassActivityModel.CycleTaskIndexKey, "value") == nil then
      self:_RefreshCycleTask()
      self:RefreshTimelimitTasks()
    end
  elseif self:GetState() == ActivityState.Ended then
    self:DoSettle()
  end
  self.event:Call(PassActivityEventType.StateChanged)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
end

function PassActivityModel:_DropData()
  BaseActivityModel._DropData(self)
  self.m_bSettled = false
  self.m_bHasTakeAllRewards = false
  self.m_shouldRefreshTimelimitTasks = false
end

function PassActivityModel:GetLevelConfigs()
  return self.m_config.levelConfigs
end

function PassActivityModel:GetLevelConfigIncludeExtra(level)
  local normalLevelCount = #self.m_config.levelConfigs
  if level <= normalLevelCount then
    return self.m_config.levelConfigs[level]
  elseif self:HasExtraLevels() then
    local extraLevelCount = #self.m_config.extraLevelConfigs
    local index = (level - normalLevelCount) % extraLevelCount
    index = index == 0 and extraLevelCount or index
    return self.m_config.extraLevelConfigs[index]
  end
end

function PassActivityModel:HasExtraLevels()
  return #self.m_config.extraLevelConfigs > 0
end

function PassActivityModel:CanOpenBuyTicketPopupWindow()
  if self:HasTicket() then
    return false
  end
  if self:GetState() == ActivityState.Started then
    local tokenLevel = self:GetCurrentLevel()
    local remainDay = (self.m_config.eTime - GM.GameModel:GetServerTime()) // Sec2Day + 1
    if not self.m_config.mapBuyTicketPopLevels[tokenLevel] and (not self.m_config.mapBuyTicketPopDays[remainDay] or self:GetCurrentLevel() < self.m_popMinLevel) then
      return false
    end
    local lastDay = self.m_dbTable:GetValue(PassActivityModel.LastBuyTicketPopupWindowDayKey, "value") or -1
    local currentDay = (GM.GameModel:GetServerTime() - self.m_config.sTime) // Sec2Day
    return lastDay < currentDay
  elseif self:GetState() == ActivityState.Ended then
    return self.m_dbTable:GetValue(PassActivityModel.BuyTicketPopupWindowOpenedStateEnded, "value") ~= 1
  end
  return false
end

function PassActivityModel:SetBuyTicketPopupWindowOpened()
  if self:GetState() == ActivityState.Started then
    local day = (GM.GameModel:GetServerTime() - self.m_config.sTime) // Sec2Day
    self.m_dbTable:Set(PassActivityModel.LastBuyTicketPopupWindowDayKey, "value", day)
  else
    self.m_dbTable:Set(PassActivityModel.BuyTicketPopupWindowOpenedStateEnded, "value", 1)
  end
end

function PassActivityModel:OnCollectGold(count)
  self:_OnTaskTargetReached(EPassActivityTargetType.Gold, count)
end

function PassActivityModel:_OnPropertyConsumed(message)
  if message.property and message.property[PROPERTY_TYPE] == EPropertyType.Gem then
    local count = message.property[PROPERTY_COUNT]
    self:_OnTaskTargetReached(EPassActivityTargetType.UseGem, count)
  end
end

function PassActivityModel:_OnEnergyConsumed(msg)
  if msg ~= nil and msg.num ~= nil and msg.num > 0 then
    self:_OnTaskTargetReached(EPassActivityTargetType.Energy, msg.num)
  end
end

function PassActivityModel:_OnItemMerged(message)
  if message and message.GameMode ~= nil and message.GameMode == EGameMode.ExtraBoard then
    return
  end
  self:_OnTaskTargetReached(message.New:GetCode(), 1)
  self:_OnTaskTargetReached(EPassActivityTargetType.Merge, 1)
end

function PassActivityModel:_OnOrderFinished(message)
  if message and message.isActivity then
    return
  end
  if message.order and message.order:GetType() == OrderType.LuckyStar then
    return
  end
  self:_OnTaskTargetReached(EPassActivityTargetType.Customer, 1)
end

function PassActivityModel:_OnClaimedOrderGroupReward()
  self:_OnTaskTargetReached(EPassActivityTargetType.Day, 1)
end

function PassActivityModel:_OnTaskOutDishItem(itemCode)
  self:_OnTaskTargetReached(EPassActivityTargetType.Dish, 1)
end

function PassActivityModel:_OnTaskTargetReached(target, delta)
  if self:GetState() ~= ActivityState.Started then
    return
  end
  if delta <= 0 then
    return
  end
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board and GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    return
  end
  if not self:HasExtraLevels() and self:GetCurrentLevel() >= self:GetMaxLevel() then
    return
  end
  local tasks, timelimitTaskOrder = self:GetActiveTasks()
  for _, task in ipairs(tasks) do
    if task:GetFinalTarget() == target then
      self:_AddFinishedCount(task, delta)
    end
  end
end

function PassActivityModel:IsTaskGroupOpen()
  return self.m_bUseTaskGroup
end

function PassActivityModel:_GetTimeLimitTaskConfig(timelimitTaskOrder)
  return self.m_config and self.m_config.timelimitTasks[timelimitTaskOrder] or {
    time = 0,
    tasks = {}
  }
end

function PassActivityModel:_GetTimeLimitGroupTasks(order)
  local arrTasks = self:_GetTimeLimitTaskConfig(order).tasks
  local mapGroup2Task = {}
  for _, task in ipairs(arrTasks) do
    if mapGroup2Task[task.Group] == nil then
      mapGroup2Task[task.Group] = {}
    end
    table.insert(mapGroup2Task[task.Group], task)
  end
  return mapGroup2Task
end

function PassActivityModel:_GetBonusTaskConfig(timelimitTaskOrder)
  return self.m_config and self.m_config.bonusTasks[timelimitTaskOrder] or {
    time = 0,
    tasks = {}
  }
end

function PassActivityModel:_UpdateShouldRefreshTimelimitTasks()
  local shouldRefresh = GM.GameModel:GetServerTime() >= self:GetTaskRefreshTime()
  if shouldRefresh and not self:HasExtraLevels() and self:GetCurrentLevel() >= self:GetMaxLevel() then
    shouldRefresh = false
  end
  if not self.m_shouldRefreshTimelimitTasks and shouldRefresh then
    self.m_shouldRefreshTimelimitTasks = true
    EventDispatcher.DispatchEvent(self.m_activityDefinition.ShouldRefreshTimelimitTasksEvent)
  end
end

function PassActivityModel:ShouldRefreshTimelimitTasks()
  return self.m_shouldRefreshTimelimitTasks
end

function PassActivityModel:GetActiveTasks()
  local activeTasks = {}
  local timelimitTaskOrder = self:GetTimelimitTaskOrder()
  local tasks = self:_GetTimeLimitTaskConfig(timelimitTaskOrder).tasks
  for _, task in ipairs(tasks) do
    if not self:_IsTimelimitTaskFinished(task) then
      table.insert(activeTasks, task)
    end
  end
  local bonusTasks = self:_GetBonusTaskConfig(timelimitTaskOrder).tasks
  for _, task in ipairs(bonusTasks) do
    if not self:_IsTimelimitTaskFinished(task) then
      table.insert(activeTasks, task)
    end
  end
  local cycleTaskIndex = self.m_dbTable:GetValue(PassActivityModel.CycleTaskIndexKey, "value")
  local cycleTask = self.m_config.cycleTasks[cycleTaskIndex]
  if cycleTask then
    table.insert(activeTasks, cycleTask)
  end
  local vipTask = self.m_config.vipTask[timelimitTaskOrder]
  if vipTask and not self:_IsTimelimitTaskFinished(vipTask) then
    table.insert(activeTasks, vipTask)
  end
  return activeTasks, timelimitTaskOrder
end

function PassActivityModel:GetCurDayAllTasks()
  local activeTasks = {}
  local finishedTasks = {}
  local timelimitTaskOrder = self:GetTimelimitTaskOrder()
  local tasks = self:_GetTimeLimitTaskConfig(timelimitTaskOrder).tasks
  for _, task in ipairs(tasks) do
    if not self:_IsTimelimitTaskFinished(task) then
      table.insert(activeTasks, task)
    else
      table.insert(finishedTasks, task)
    end
  end
  local bonusTasks = self:_GetBonusTaskConfig(timelimitTaskOrder).tasks
  for _, task in ipairs(bonusTasks) do
    if not self:_IsTimelimitTaskFinished(task) then
      table.insert(activeTasks, task)
    else
      table.insert(finishedTasks, task)
    end
  end
  local cycleTaskIndex = self.m_dbTable:GetValue(PassActivityModel.CycleTaskIndexKey, "value")
  local cycleTask = self.m_config.cycleTasks[cycleTaskIndex]
  if cycleTask then
    table.insert(activeTasks, cycleTask)
  end
  local vipTask = self.m_config.vipTask[timelimitTaskOrder]
  if vipTask then
    if not self:_IsTimelimitTaskFinished(vipTask) then
      table.insert(activeTasks, vipTask)
    else
      table.insert(finishedTasks, vipTask)
    end
  end
  return activeTasks, finishedTasks
end

function PassActivityModel:GetCurDayTaskProgress()
  local timelimitTaskOrder = self:GetTimelimitTaskOrder()
  local tasks = self:_GetTimeLimitTaskConfig(timelimitTaskOrder).tasks or {}
  local bonusTasks = self:_GetBonusTaskConfig(timelimitTaskOrder).tasks or {}
  local totalProgress = #tasks + #bonusTasks
  local curProgress = 0
  for _, task in ipairs(tasks) do
    if self:_IsTimelimitTaskFinished(task) then
      curProgress = curProgress + 1
    end
  end
  for _, task in ipairs(bonusTasks) do
    if self:_IsTimelimitTaskFinished(task) then
      curProgress = curProgress + 1
    end
  end
  local vipTask = self.m_config.vipTask[timelimitTaskOrder]
  if vipTask and self:HasTicket() then
    totalProgress = totalProgress + 1
    if self:_IsTimelimitTaskFinished(vipTask) then
      curProgress = curProgress + 1
    end
  end
  return curProgress, totalProgress
end

function PassActivityModel:GetShowingTaskByGroup()
  local arrTasks = {}
  local timelimitTaskOrder = self:GetTimelimitTaskOrder()
  local mapGroup2Tasks = self:_GetTimeLimitGroupTasks(timelimitTaskOrder)
  for group, tasks in ipairs(mapGroup2Tasks) do
    for _, task in ipairs(tasks) do
      if not self:_IsTimelimitTaskFinished(task) then
        table.insert(arrTasks, task)
        break
      end
    end
  end
  local bonusTasks = self:_GetBonusTaskConfig(timelimitTaskOrder).tasks
  for _, task in ipairs(bonusTasks) do
    if not self:_IsTimelimitTaskFinished(task) then
      table.insert(arrTasks, task)
    end
  end
  local cycleTaskIndex = self.m_dbTable:GetValue(PassActivityModel.CycleTaskIndexKey, "value")
  local cycleTask = self.m_config.cycleTasks[cycleTaskIndex]
  if cycleTask then
    table.insert(arrTasks, cycleTask)
  end
  local vipTask = self.m_config.vipTask[timelimitTaskOrder]
  if vipTask and not self:_IsTimelimitTaskFinished(vipTask) then
    table.insert(arrTasks, vipTask)
  end
  return arrTasks
end

function PassActivityModel:GetFinishedCount(task, noExcess)
  local finishedCountKey = self:_GetFinishedCountKey(task.Type, task.Index)
  local count = self.m_dbTable:GetValue(finishedCountKey, "value") or 0
  local target = task:GetFinalCount()
  if noExcess then
    if count % target ~= 0 then
      count = count % target
    elseif target <= count then
      count = target
    end
  end
  return count
end

function PassActivityModel:_AddFinishedCount(task, delta)
  if task.Type == EPassActivityTaskType.Bonus and not self:IsCurrentTimelimitTasksFinished() then
    return
  end
  local oldCount = self:GetFinishedCount(task)
  local targetCount = task:GetFinalCount()
  local newCount = oldCount + delta
  if task.Type ~= EPassActivityTaskType.Cycle then
    if oldCount >= targetCount then
      return
    end
    if targetCount <= newCount then
      newCount = targetCount
      if task.Type == EPassActivityTaskType.Timelimit then
        local num = tonumber(self.m_dbTable:GetValue(PassActivityModel.FinishTimelimitTask, "value")) or 0
        self.m_dbTable:Set(PassActivityModel.FinishTimelimitTask, "value", num + 1)
        self:_OnTaskTargetReached(EPassActivityTargetType.TimeLimitedTask, 1)
      elseif task.Type == EPassActivityTaskType.VIP then
        local timelimitTaskOrder = self:GetTimelimitTaskOrder()
        GM.BIManager:LogAction(EBIType.BattlePassVIPTaskProgress, {
          id = "vip" .. timelimitTaskOrder .. "_" .. task.Index,
          cost = task:GetFinalTarget() .. "_" .. task:GetFinalCount(),
          is_vip = self:HasTicket() and 1 or 0
        })
      end
    end
  end
  local finishedCountKey = self:_GetFinishedCountKey(task.Type, task.Index)
  self.m_dbTable:Set(finishedCountKey, "value", newCount)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.TaskProgressChangedEvent, {Task = task})
  if targetCount <= newCount then
    EventDispatcher.DispatchEvent(self.m_activityDefinition.CanFinishTaskNumberChangedEvent)
  end
end

function PassActivityModel:GetTimelimitTaskOrder()
  return self.m_dbTable:GetValue(PassActivityModel.TimelimitTaskOrderKey, "value")
end

function PassActivityModel:IsCurrentTimelimitTasksFinished()
  local timelimitTaskOrder = self:GetTimelimitTaskOrder()
  local tasks = self:_GetTimeLimitTaskConfig(timelimitTaskOrder).tasks
  for _, task in ipairs(tasks) do
    if not self:_IsTimelimitTaskFinished(task) then
      return false
    end
  end
  return 0 < #tasks
end

function PassActivityModel:GetTaskRefreshTime()
  local timelimitTaskOrder = self:GetTimelimitTaskOrder()
  local taskSet = self:_GetTimeLimitTaskConfig(timelimitTaskOrder)
  return (self.m_config and self.m_config.sTime or 0) + taskSet.time * Sec2Day
end

function PassActivityModel:GetCanFinishTasks(bIgnoreGroup)
  local arrCanFinishTasks = {}
  local timelimitTaskOrder = self:GetTimelimitTaskOrder()
  if self:HasTicket() then
    local vipTask = self.m_config.vipTask[timelimitTaskOrder]
    if vipTask and not self:_IsTimelimitTaskFinished(vipTask) and self:GetFinishedCount(vipTask) >= vipTask:GetFinalCount() then
      table.insert(arrCanFinishTasks, vipTask)
    end
  end
  if self.m_bUseTaskGroup then
    local mapGroup2Tasks = self:_GetTimeLimitGroupTasks(timelimitTaskOrder)
    for group, arrTasks in ipairs(mapGroup2Tasks) do
      for _, task in ipairs(arrTasks) do
        if not self:_IsTimelimitTaskFinished(task) then
          if self:GetFinishedCount(task) >= task:GetFinalCount() then
            table.insert(arrCanFinishTasks, task)
          else
            break
          end
          if not bIgnoreGroup then
            break
          end
        end
      end
    end
  else
    local tasks = self:_GetTimeLimitTaskConfig(timelimitTaskOrder).tasks
    for _, task in ipairs(tasks) do
      if not self:_IsTimelimitTaskFinished(task) and self:GetFinishedCount(task) >= task:GetFinalCount() then
        table.insert(arrCanFinishTasks, task)
      end
    end
  end
  local bonusTasks = self:_GetBonusTaskConfig(timelimitTaskOrder).tasks
  for _, task in ipairs(bonusTasks) do
    if not self:_IsTimelimitTaskFinished(task) and self:GetFinishedCount(task) >= task:GetFinalCount() then
      table.insert(arrCanFinishTasks, task)
    end
  end
  local cycleTaskIndex = self.m_dbTable:GetValue(PassActivityModel.CycleTaskIndexKey, "value")
  local task = self.m_config.cycleTasks[cycleTaskIndex]
  if task and self:GetFinishedCount(task) >= task:GetFinalCount() then
    table.insert(arrCanFinishTasks, task)
  end
  return arrCanFinishTasks
end

function PassActivityModel:GetCanFinishTasksCount()
  local arrCanFinishTasks = self:GetCanFinishTasks()
  local count = 0
  for _, task in ipairs(arrCanFinishTasks) do
    if task.Type == EPassActivityTaskType.Cycle then
      count = count + task:GetAccomplishTimes()
    else
      count = count + 1
    end
  end
  return count
end

function PassActivityModel:FinishTasks(tasks)
  for _, task in ipairs(tasks) do
    self:_FinishTask(task)
  end
  EventDispatcher.DispatchEvent(self.m_activityDefinition.CanFinishTaskNumberChangedEvent)
end

function PassActivityModel:_FinishTask(task)
  local taskId
  local accomplishTimes = 1
  if task.Type == EPassActivityTaskType.Timelimit or task.Type == EPassActivityTaskType.Bonus or task.Type == EPassActivityTaskType.VIP then
    self:_SetTimelimitTaskFinished(task)
    local timelimitTaskOrder = self:GetTimelimitTaskOrder()
    if task.Type == EPassActivityTaskType.VIP then
      taskId = "vip"
    elseif task.Type == EPassActivityTaskType.Bonus then
      taskId = "bonus"
    else
      taskId = ""
    end
    taskId = taskId .. timelimitTaskOrder .. "_" .. task.Index
  elseif task.Type == EPassActivityTaskType.Cycle then
    accomplishTimes = task:GetAccomplishTimes()
    self:_RefreshCycleTask(task)
    taskId = tostring(task.Index)
  end
  local ratio = 1
  if self:CanBuyMaxTicket() and self:GetTicketState() == EBPTicketState.Purchase then
    ratio = self:GetMaxTicketTokenRatio()
  end
  for i = 1, accomplishTimes do
    self:AcquireToken(math.ceil(task.Reward * ratio))
    GM.BIManager:LogPassActivityTask(taskId, task, ratio)
  end
end

function PassActivityModel:RefreshTimelimitTasks()
  local maxValidOrder = 0
  local serverTime = GM.GameModel:GetServerTime()
  for order, taskSet in ipairs(self.m_config.timelimitTasks) do
    local startTime = self.m_config.sTime + (taskSet.time - 1) * Sec2Day
    if serverTime >= startTime then
      maxValidOrder = order
    end
  end
  local newTasks = {}
  local vipTask = self.m_config.vipTask[maxValidOrder]
  if vipTask then
    local index = 1
    local finishedCountKey = self:_GetFinishedCountKey(EPassActivityTaskType.VIP, index)
    self.m_dbTable:Set(finishedCountKey, "value", 0)
    local finishedKey = string.format(PassActivityModel.VIPTaskFinishedKey, index)
    self.m_dbTable:Set(finishedKey, "value", 0)
    vipTask:GenerateFinalContent()
    table.insert(newTasks, vipTask)
  end
  local newTimelimitTasks = self:_GetTimeLimitTaskConfig(maxValidOrder).tasks
  self.m_dbTable:Set(PassActivityModel.TimelimitTaskOrderKey, "value", maxValidOrder)
  for index = 1, #newTimelimitTasks do
    local finishedCountKey = self:_GetFinishedCountKey(EPassActivityTaskType.Timelimit, index)
    self.m_dbTable:Set(finishedCountKey, "value", 0)
    local finishedKey = string.format(PassActivityModel.TimelimitTaskFinishedKey, index)
    self.m_dbTable:Set(finishedKey, "value", 0)
    newTimelimitTasks[index]:GenerateFinalContent()
  end
  Table.ListAppend(newTasks, newTimelimitTasks)
  local bonusTasks = self:_GetBonusTaskConfig(maxValidOrder).tasks
  for index = 1, #bonusTasks do
    local finishedCountKey = self:_GetFinishedCountKey(EPassActivityTaskType.Bonus, index)
    self.m_dbTable:Set(finishedCountKey, "value", 0)
    local finishedKey = string.format(PassActivityModel.BonusTaskFinishedKey, index)
    self.m_dbTable:Set(finishedKey, "value", 0)
    bonusTasks[index]:GenerateFinalContent()
  end
  Table.ListAppend(newTasks, bonusTasks)
  self.m_shouldRefreshTimelimitTasks = false
  EventDispatcher.DispatchEvent(self.m_activityDefinition.RefreshTimelimitTasksEvent)
  return newTasks
end

function PassActivityModel:_RefreshCycleTask(lastCycleTask)
  if Table.IsEmpty(self.m_config.cycleTasks) then
    return
  end
  local oldIndex = self.m_dbTable:GetValue(PassActivityModel.CycleTaskIndexKey, "value") or 0
  local newIndex = oldIndex + 1
  if newIndex > #self.m_config.cycleTasks then
    newIndex = 1
  end
  local inheritedValue = 0
  if oldIndex == newIndex and lastCycleTask then
    inheritedValue = lastCycleTask:GetExtraCount() or 0
  end
  self.m_dbTable:Set(PassActivityModel.CycleTaskIndexKey, "value", newIndex)
  self.m_dbTable:Set(PassActivityModel.CycleTaskFinishedCountKey, "value", inheritedValue)
  self.m_config.cycleTasks[newIndex]:GenerateFinalContent()
end

function PassActivityModel:_GetRealItemTargetKey(type)
  local key = PassActivityModel.RealItemTargetKey
  if type == EPassActivityTaskType.Bonus then
    key = PassActivityModel.RealBonusItemTargetKey
  elseif type == EPassActivityTaskType.VIP then
    key = PassActivityModel.RealVIPItemTargetKey
  end
  return key
end

function PassActivityModel:SetRealItemTarget(taskType, order, index, itemName)
  local key = self:_GetRealItemTargetKey(taskType)
  local dataKey = string.format(key, order, index)
  return self.m_dbTable:Set(dataKey, "value", itemName)
end

function PassActivityModel:GetRealItemTarget(taskType, order, index)
  local key = self:_GetRealItemTargetKey(taskType)
  local dataKey = string.format(key, order, index)
  return self.m_dbTable:GetValue(dataKey, "value")
end

function PassActivityModel:_GetRealTargetCountKey(type)
  if type == EPassActivityTaskType.Bonus then
    return PassActivityModel.RealBonusTargetCountKey
  elseif type == EPassActivityTaskType.VIP then
    return PassActivityModel.RealVIPTargetCountKey
  end
  return PassActivityModel.RealTargetCountKey
end

function PassActivityModel:SetRealTargetCount(taskType, order, index, count)
  local key = self:_GetRealTargetCountKey(taskType)
  local dataKey = string.format(key, order, index)
  return self.m_dbTable:Set(dataKey, "value", count)
end

function PassActivityModel:GetRealTargetCount(taskType, order, index)
  local key = self:_GetRealTargetCountKey(taskType)
  local dataKey = string.format(key, order, index)
  return self.m_dbTable:GetValue(dataKey, "value")
end

function PassActivityModel:_GetFinishedCountKey(type, index)
  if type == EPassActivityTaskType.Timelimit then
    return string.format(PassActivityModel.TimelimitTaskFinishedCountKey, index)
  elseif type == EPassActivityTaskType.Bonus then
    return string.format(PassActivityModel.BonusTaskFinishedCountKey, index)
  elseif type == EPassActivityTaskType.VIP then
    return string.format(PassActivityModel.VIPTaskFinishedCountKey, index)
  elseif type == EPassActivityTaskType.Cycle then
    return PassActivityModel.CycleTaskFinishedCountKey
  end
end

function PassActivityModel:_IsTimelimitTaskFinished(taskConfig)
  local format = PassActivityModel.TimelimitTaskFinishedKey
  if taskConfig.Type == EPassActivityTaskType.Bonus then
    format = PassActivityModel.BonusTaskFinishedKey
  elseif taskConfig.Type == EPassActivityTaskType.VIP then
    format = PassActivityModel.VIPTaskFinishedKey
  end
  local finishedKey = string.format(format, taskConfig.Index)
  return self.m_dbTable:GetValue(finishedKey, "value") == 1
end

function PassActivityModel:_SetTimelimitTaskFinished(taskConfig)
  local format = PassActivityModel.TimelimitTaskFinishedKey
  if taskConfig.Type == EPassActivityTaskType.Bonus then
    format = PassActivityModel.BonusTaskFinishedKey
  elseif taskConfig.Type == EPassActivityTaskType.VIP then
    format = PassActivityModel.VIPTaskFinishedKey
  end
  local finishedKey = string.format(format, taskConfig.Index)
  self.m_dbTable:Set(finishedKey, "value", 1)
end

function PassActivityModel:_ParseTask(taskData, type, order, index, group)
  return PassActivityTask.Create(self, type, order, index, group, taskData.type, taskData.count, taskData.tokenNum)
end

function PassActivityModel:CheckActTaskValid(taskType, num)
  if taskType == ETaskType.GetActToken then
    return self:GetState() == ActivityState.Started and (self:HasExtraLevels() or self:GetTokenNumber() + num <= self:GetAllLevelTokenNumber())
  end
  return false
end

function PassActivityModel:GetAllLevelTokenNumber()
  local totalNum = 0
  for level, config in ipairs(self.m_config.levelConfigs or {}) do
    totalNum = totalNum + config.require
  end
  return totalNum
end

function PassActivityModel:GetTokenNumber()
  return self.m_dbTable:GetValue(PassActivityModel.TokenNumberKey, "value") or 0
end

function PassActivityModel:GetActivityTokenNumber()
  return self:GetTokenNumber()
end

function PassActivityModel:AcquireToken(count, biType)
  local currentTokenNumber = self:GetTokenNumber()
  self.m_dbTable:Set(PassActivityModel.TokenNumberKey, "value", currentTokenNumber + count)
  local finishedLevel = self.m_dbTable:GetValue(PassActivityModel.FinishedLevelKey, "value") or 1
  local currentTokenLevel = self:GetCurrentLevel(true)
  if finishedLevel < currentTokenLevel then
    local maxLevel = self:GetMaxLevel()
    if finishedLevel < maxLevel then
      self:LogActivity(EBIType.ActivityRankUp, math.min(maxLevel, currentTokenLevel))
    end
    if currentTokenLevel > maxLevel then
      self:LogActivity(EBIType.BattlePassExtraRankUp, currentTokenLevel - maxLevel)
    end
  end
  self.m_dbTable:Set(PassActivityModel.FinishedLevelKey, "value", currentTokenLevel)
  biType = biType or EBIType.ActivityAddScore
  self:LogActivity(biType, count)
  EventDispatcher.DispatchEvent(EEventType.AcquireActivityToken, {
    num = count,
    activityType = self.m_type
  })
end

function PassActivityModel:AcquireActivityToken(count)
  self:AcquireToken(count)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.CanTakeRewardNumberChangedEvent)
end

function PassActivityModel:GetLevel(tokenNumber, bIncludeExtra)
  local tokenLevel = 0
  for level, config in ipairs(self.m_config.levelConfigs) do
    if tokenNumber >= config.require then
      tokenLevel = level
      tokenNumber = tokenNumber - config.require
    else
      break
    end
  end
  if bIncludeExtra and tokenLevel == self:GetMaxLevel() then
    local extraLevelConfigs = self.m_config.extraLevelConfigs
    local extraLevelConfigsCount = #extraLevelConfigs
    if 0 < extraLevelConfigsCount then
      local index = 1
      while 0 < tokenNumber do
        local requireCount = extraLevelConfigs[index].require
        if tokenNumber >= requireCount then
          tokenNumber = tokenNumber - requireCount
          tokenLevel = tokenLevel + 1
        else
          break
        end
        index = extraLevelConfigsCount <= index and 1 or index + 1
      end
    end
  end
  return tokenLevel, tokenNumber
end

function PassActivityModel:GetCurrentLevel(bIncludeExtra)
  return self:GetLevel(self:GetTokenNumber(), bIncludeExtra)
end

function PassActivityModel:GetMaxLevel()
  return #self.m_config.levelConfigs
end

function PassActivityModel:CanBuyMaxTicket()
  return self.m_config.maxTicketTokenCount ~= nil and self.m_config.maxTicketTokenRatio ~= nil
end

function PassActivityModel:GetTicketState()
  if self:HasMaxTicket() and self:HasTicket() then
    return EBPTicketState.Purchase
  elseif self:HasTicket() and not self:HasMaxTicket() then
    return EBPTicketState.Upgrade
  elseif not self:HasTicket() and not self:HasMaxTicket() then
    return EBPTicketState.None
  end
  Log.Error("[PassActivityModel] Ticket State Error, please check!")
end

function PassActivityModel:HasMaxTicket()
  return self.m_dbTable:GetValue(PassActivityModel.MaxTicketKey, "value") == 1
end

function PassActivityModel:GetMaxTicketTokenNumber()
  return self.m_config.maxTicketTokenCount
end

function PassActivityModel:GetMaxTicketTokenRatio()
  return self.m_config.maxTicketTokenRatio
end

function PassActivityModel:GetPassTicketIapType()
  local mapIapType = arrIAPType[self.m_eIapSeries]
  return mapIapType[EBPTicketType.Normal] or EIAPType.passticket
end

function PassActivityModel:GetUpPassTicketIapType()
  local mapIapType = arrIAPType[self.m_eIapSeries]
  return mapIapType[EBPTicketType.Up] or EIAPType.uppassticket
end

function PassActivityModel:GetMaxPassTicektIapType()
  local mapIapType = arrIAPType[self.m_eIapSeries]
  return mapIapType[EBPTicketType.Max] or EIAPType.maxpassticket
end

function PassActivityModel:BuyMaxTicket()
  if not self:CanBuyMaxTicket() or self:GetTicketState() ~= EBPTicketState.None then
    return
  end
  local purchaseCallback = function()
    self:_OnBuyMaxTicketSuccess()
  end
  GM.InAppPurchaseModel:StartPurchase(self:GetMaxPassTicektIapType(), purchaseCallback, EBIType.ShopBuy)
end

function PassActivityModel:_OnBuyMaxTicketSuccess()
  self.m_dbTable:Set(PassActivityModel.TicketKey, "value", 1)
  self.m_dbTable:Set(PassActivityModel.MaxTicketKey, "value", 1)
  self:AcquireToken(self.m_config.maxTicketTokenCount, EBIType.BPVIPAcquireToken)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.CanTakeRewardNumberChangedEvent)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.BuyMaxTicketSuccessEvent)
end

function PassActivityModel:BuyUpTicket()
  if not self:CanBuyMaxTicket() or self:GetTicketState() ~= EBPTicketState.Upgrade then
    return
  end
  local purchaseCallback = function()
    self:_OnBuyUpTicketSuccess()
  end
  GM.InAppPurchaseModel:StartPurchase(self:GetUpPassTicketIapType(), purchaseCallback, EBIType.ShopBuy)
end

function PassActivityModel:_OnBuyUpTicketSuccess()
  self.m_dbTable:Set(PassActivityModel.MaxTicketKey, "value", 1)
  self:AcquireToken(self.m_config.maxTicketTokenCount, EBIType.BPVIPAcquireToken)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.CanTakeRewardNumberChangedEvent)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.BuyUpTicketSuccessEvent)
end

function PassActivityModel:GetBuyTicketWindowByCurState()
  if self:GetTicketState() == EBPTicketState.None then
    return self.m_activityDefinition.BuyMaxTicketWindowPrefabName
  elseif self:GetTicketState() == EBPTicketState.Upgrade then
    return self.m_activityDefinition.BuyUpTicketWindowPrefabName
  end
end

function PassActivityModel.IsVIPTicket(propertyType)
  for _, activityDefinition in pairs(PassActivityDefinition) do
    if propertyType == activityDefinition.TicketPropertyType or propertyType == activityDefinition.PlusTicketPropertyType then
      return true
    end
  end
  return false
end

function PassActivityModel:TryAcquireVIPTicket(propertyType)
  if self:GetState() == ActivityState.Released then
    return
  end
  local bAcquired = false
  if propertyType == self.m_activityDefinition.TicketPropertyType and not self:HasTicket() then
    bAcquired = true
    self:_OnBuyTicketSuccess()
  elseif propertyType == self.m_activityDefinition.PlusTicketPropertyType and not self:HasMaxTicket() then
    bAcquired = true
    self:_OnBuyMaxTicketSuccess()
  end
  if bAcquired and self:GetState() == ActivityState.Ended then
    local canTakeRewards = self:GetCanTakeRewards()
    if #canTakeRewards ~= 0 then
      GM.UIManager:OpenViewWhenIdle(self.m_activityDefinition.RewardRecoverWindowPrefabName, self.m_type, canTakeRewards)
    end
  end
  return bAcquired
end

function PassActivityModel:GetVipRewards(bIngnoreLevel, bIngnoreFirstEnergy)
  local rewards = {}
  local levelConfigs = self:GetLevelConfigs()
  local tokenLevel = bIngnoreLevel and self:GetMaxLevel() or self:GetCurrentLevel()
  for level = 1, tokenLevel do
    for _, reward in ipairs(levelConfigs[level].vipRewards) do
      local rewardType = reward[PROPERTY_TYPE]
      if not (level == 1 and bIngnoreFirstEnergy) or rewardType ~= EPropertyType.Energy then
        if rewards[rewardType] == nil then
          rewards[rewardType] = 0
        end
        rewards[rewardType] = rewards[rewardType] + reward[PROPERTY_COUNT]
      end
    end
  end
  local formattedRewards = {}
  for type, count in pairs(rewards) do
    table.insert(formattedRewards, {
      [PROPERTY_TYPE] = type,
      [PROPERTY_COUNT] = count
    })
  end
  return formattedRewards
end

function PassActivityModel:HasTicket()
  return self.m_dbTable:GetValue(PassActivityModel.TicketKey, "value") == 1
end

function PassActivityModel:BuyTicket()
  if self:HasTicket() then
    self:_OnBuyTicketSuccess()
    return
  end
  local purchaseCallback = function()
    self:_OnBuyTicketSuccess()
  end
  GM.InAppPurchaseModel:StartPurchase(self:GetPassTicketIapType(), purchaseCallback, EBIType.ShopBuy)
end

function PassActivityModel:RestoreIapRewards(iapType)
  local rewards
  if iapType == self:GetPassTicketIapType() and not self:HasTicket() then
    self:_OnBuyTicketSuccess()
    rewards = self:_GetVipShowRewards(false)
  elseif iapType == self:GetMaxPassTicektIapType() and self:GetTicketState() == EBPTicketState.None then
    self:_OnBuyMaxTicketSuccess()
    rewards = self:_GetVipShowRewards(true)
  elseif iapType == self:GetUpPassTicketIapType() and self:GetTicketState() == EBPTicketState.Upgrade then
    self:_OnBuyUpTicketSuccess()
    rewards = self:_GetVipShowRewards(true)
  end
  if rewards then
    return true
  else
    return false
  end
end

function PassActivityModel:_GetVipShowRewards(bhasToken)
  if bhasToken then
    return {
      {
        [PROPERTY_TYPE] = self.m_activityDefinition.PlusTicketPropertyType,
        [PROPERTY_COUNT] = 1
      },
      {
        [PROPERTY_TYPE] = self.m_activityDefinition.ActivityTokenPropertyType,
        [PROPERTY_COUNT] = self.m_config.maxTicketTokenCount
      }
    }
  else
    return {
      {
        [PROPERTY_TYPE] = self.m_activityDefinition.TicketPropertyType,
        [PROPERTY_COUNT] = 1
      }
    }
  end
end

function PassActivityModel:_OnBuyTicketSuccess()
  self.m_dbTable:Set(PassActivityModel.TicketKey, "value", 1)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.CanTakeRewardNumberChangedEvent)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.BuyTicketSuccessEvent)
end

function PassActivityModel:GetCanTakeRewards()
  local rewards = {}
  local currentTokenLevel = self:GetCurrentLevel()
  local hasTicket = self:HasTicket()
  for level, config in ipairs(self.m_config and self.m_config.levelConfigs or {}) do
    if level > currentTokenLevel then
      break
    end
    if not self:IsRewardTaken(level, false) then
      table.insert(rewards, {
        Level = level,
        IsVip = false,
        Rewards = config.rewards
      })
    end
    if hasTicket and not self:IsRewardTaken(level, true) then
      table.insert(rewards, {
        Level = level,
        IsVip = true,
        Rewards = config.vipRewards
      })
    end
  end
  Table.ListAppend(rewards, self:GetCanTakeExtraRewards())
  return rewards
end

function PassActivityModel:GetCanTakeExtraRewards()
  if not self:HasMaxTicket() then
    return {}
  end
  return self:_GetCanTakeExtraRewards()
end

function PassActivityModel:_GetCanTakeExtraRewards()
  local currentTokenLevelWithExtra = self:GetCurrentLevel(true)
  local currentTokenLevel = math.min(currentTokenLevelWithExtra, self:GetMaxLevel())
  local extraLevelsToReward = currentTokenLevelWithExtra - currentTokenLevel
  local rewards = {}
  local extraRewardTakenIndex = self.m_dbTable:GetValue(PassActivityModel.ExtraRewardTakenKey, "value") or 0
  local configCount = #self.m_config.extraLevelConfigs
  for index = extraRewardTakenIndex + 1, extraLevelsToReward do
    local realIndex = index % configCount
    realIndex = realIndex == 0 and configCount or realIndex
    table.insert(rewards, {
      IsExtra = true,
      Index = index,
      Rewards = self.m_config.extraLevelConfigs[realIndex].reward
    })
  end
  return rewards
end

function PassActivityModel:GetCanTakeExtraRewardNum()
  return #self:_GetCanTakeExtraRewards()
end

function PassActivityModel:GetNextExtraRewardConfig()
  if not self:HasExtraLevels() then
    return nil
  end
  local extraRewardTakenIndex = self.m_dbTable:GetValue(PassActivityModel.ExtraRewardTakenKey, "value") or 0
  local configCount = #self.m_config.extraLevelConfigs
  extraRewardTakenIndex = (extraRewardTakenIndex + 1) % configCount
  extraRewardTakenIndex = extraRewardTakenIndex == 0 and configCount or extraRewardTakenIndex
  return self.m_config.extraLevelConfigs[extraRewardTakenIndex]
end

function PassActivityModel:GetAllExtraRewards()
  local rewards = {}
  if not self:HasExtraLevels() then
    return
  end
  for _, config in ipairs(self.m_config.extraLevelConfigs) do
    for _, reward in ipairs(config.reward) do
      rewards[#rewards + 1] = reward
    end
  end
  return rewards
end

function PassActivityModel:IsRewardTaken(level, isVip)
  local rewardTakenKey = string.format(PassActivityModel.RewardTakenKey, level, isVip and 1 or 0)
  return self.m_dbTable:GetValue(rewardTakenKey, "value") == 1
end

function PassActivityModel:TakeReward(level, isVip, rewards)
  local rewardTakenKey = string.format(PassActivityModel.RewardTakenKey, level, isVip and 1 or 0)
  if self.m_dbTable:GetValue(rewardTakenKey, "value") == 1 then
    return
  end
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, self.m_activityDefinition.TakeRewardBIType, EGameMode.Board, CacheItemType.Stack)
  self.m_dbTable:Set(rewardTakenKey, "value", 1)
  local action = {
    level = level,
    vip = isVip and 1 or 0
  }
  local actionString = GM.BIManager:TableToString(action)
  self:LogActivity(EBIType.ActivityGetRewards, actionString)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.CanTakeRewardNumberChangedEvent)
  self:_CheckIfTakeAllRewards()
end

function PassActivityModel:TakeExtraReward(index, rewards)
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, self.m_activityDefinition.TakeExtraRewardBIType, EGameMode.Board, CacheItemType.Stack)
  local extraRewardTakenIndex = self.m_dbTable:GetValue(PassActivityModel.ExtraRewardTakenKey, "value") or 0
  extraRewardTakenIndex = math.max(extraRewardTakenIndex, index)
  self.m_dbTable:Set(PassActivityModel.ExtraRewardTakenKey, "value", extraRewardTakenIndex)
  local action = {index = extraRewardTakenIndex}
  local actionString = GM.BIManager:TableToString(action)
  self:LogActivity(EBIType.ActivityGetRewards, actionString)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.CanTakeRewardNumberChangedEvent)
end

function PassActivityModel:_CheckIfTakeAllRewards()
  self.m_bHasTakeAllRewards = false
  if self:HasExtraLevels() then
    return
  end
  for level = 1, self:GetMaxLevel() do
    if not self:IsRewardTaken(level, false) or not self:IsRewardTaken(level, true) then
      return
    end
  end
  self.m_bHasTakeAllRewards = true
end

function PassActivityModel:CanSettleBeforeEnd()
  return self.m_bHasTakeAllRewards and GM.UIManager.allWindowClosed and PropertyAnimationManager.uiLockFlyingCount <= 0 and not GM.UIManager:IsEventLock()
end

function PassActivityModel:IsSettled()
  if self.m_bSettled == nil then
    self.m_bSettled = self.m_dbTable:GetValue(PassActivityModel.SettleKey, "value") or false
  end
  return self.m_bSettled
end

function PassActivityModel:DoSettle()
  self.m_bSettled = true
  self.m_dbTable:Set(PassActivityModel.SettleKey, "value", true)
end

function PassActivityModel:CanShowNoticeBubble(task)
  if task.Group == nil then
    return true
  end
  local mapGroup2Task = self:_GetTimeLimitGroupTasks(self:GetTimelimitTaskOrder())
  for _, v in ipairs(mapGroup2Task[task.Group] or {}) do
    if v.Index < task.Index and not self:_IsTimelimitTaskFinished(v) then
      return false
    end
  end
  return true
end

function PassActivityModel:HasShownRewardEffect(level, isVip)
  local rewardEffectKey = string.format(PassActivityModel.RewardEffectKey, level, isVip and 1 or 0)
  return self.m_dbTable:GetValue(rewardEffectKey, "value") == 1
end

function PassActivityModel:SetShownRewardEffect(level, isVip)
  local rewardEffectKey = string.format(PassActivityModel.RewardEffectKey, level, isVip and 1 or 0)
  self.m_dbTable:Set(rewardEffectKey, "value", 1)
end

function PassActivityModel:HasShownProgressEffect(level)
  local progressEffectKey = string.format(PassActivityModel.ProgressEffectKey, level)
  return self.m_dbTable:GetValue(progressEffectKey, "value") == 1
end

function PassActivityModel:SetShownProgressEffect(level)
  local progressEffectKey = string.format(PassActivityModel.ProgressEffectKey, level)
  self.m_dbTable:Set(progressEffectKey, "value", 1)
end

function PassActivityModel.GetButtonTarget(propertyType)
  for activityType, activityDefinition in pairs(PassActivityDefinition) do
    if propertyType == activityDefinition.ActivityTokenPropertyType or propertyType == activityDefinition.TicketPropertyType or propertyType == activityDefinition.PlusTicketPropertyType then
      if GM.SceneManager:GetGameMode() == EGameMode.Board then
        local boardView = MainBoardView.GetInstance()
        if boardView ~= nil then
          local orderArea = boardView:GetOrderArea()
          if orderArea ~= nil then
            return orderArea:GetIconAreaByActivityType(activityType)
          end
        end
      elseif GM.SceneManager:GetGameMode() == EGameMode.Main then
        return TutorialHelper.GetHudButton(activityDefinition.EntryButtonKey)
      end
    end
  end
  return nil
end
