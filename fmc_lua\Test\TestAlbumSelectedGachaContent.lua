TestAlbumSelectedGachaContent = {}
TestAlbumSelectedGachaContent.__index = TestAlbumSelectedGachaContent

function TestAlbumSelectedGachaContent.GetInstance()
  return TestAlbumSelectedGachaContent.s_instance
end

function TestAlbumSelectedGachaContent:Init()
  TestAlbumSelectedGachaContent.s_instance = self
  
  function self.m_eventTrigger.OnLuaPointerDown(eventData)
    self:_OnPointerDown(eventData)
  end
  
  function self.m_eventTrigger.OnLuaDrag(eventData)
    self:_OnDrag(eventData)
  end
  
  self.m_cells = {}
end

function TestAlbumSelectedGachaContent:OnDestroy()
  TestAlbumSelectedGachaContent.s_instance = nil
end

function TestAlbumSelectedGachaContent:_OnPointerDown(eventData)
  self.m_localPosition = self.transform.localPosition
  self.m_startPosition = PositionUtil.UICameraScreen2World(eventData.position)
end

function TestAlbumSelectedGachaContent:_OnDrag(eventData)
  local position = PositionUtil.UICameraScreen2World(eventData.position)
  self.transform.localPosition = self.m_localPosition + position - self.m_startPosition
end

function TestAlbumSelectedGachaContent:Close()
  self.gameObject:RemoveSelf()
end

function TestAlbumSelectedGachaContent:Add()
  local cell = Object.Instantiate(self.m_cellPrefab, self.m_groupTransform):GetLuaTable()
  table.insert(self.m_cells, cell)
end

function TestAlbumSelectedGachaContent:Delete(cell)
  Table.ListRemove(self.m_cells, cell)
  cell.gameObject:RemoveSelf()
end

function TestAlbumSelectedGachaContent:GetValues()
  local values = {}
  for _, cell in ipairs(self.m_cells) do
    table.insert(values, cell:GetValue())
  end
  return values
end
