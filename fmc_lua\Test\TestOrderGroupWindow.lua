TestOrderGroupWindow = setmetatable({}, BaseWindow)
TestOrderGroupWindow.__index = TestOrderGroupWindow

function TestOrderGroupWindow:Init()
  self:UpdateContent()
end

function TestOrderGroupWindow:UpdateContent()
  if self.m_cells then
    for i = 1, #self.m_cells do
      self.m_cells[i].gameObject:RemoveSelf()
    end
  end
  self.arrOrderGroupData = GM.TestModel:GetTestOrderGroups()
  self.m_cells = {}
  for i = 1, #self.arrOrderGroupData do
    local cell = Object.Instantiate(self.m_prefab, self.m_content)
    local cellTb = cell:GetLuaTable()
    cellTb:Init(self, self.arrOrderGroupData[i])
    self.m_cells[#self.m_cells + 1] = cellTb
  end
end

function TestOrderGroupWindow:OnClickAddNew()
  local newData = {
    orderGroupId = #self.arrOrderGroupData + 1,
    arrOrderData = {}
  }
  for i = 1, 7 do
    newData.arrOrderData[i] = {
      {},
      {}
    }
  end
  self.arrOrderGroupData[#self.arrOrderGroupData + 1] = newData
  self:UpdateContent()
end

function TestOrderGroupWindow:OnClickAddCopy()
  self:OnClickAddNew()
  local newData = self.arrOrderGroupData[#self.arrOrderGroupData]
  local arrOrderData = newData.arrOrderData
  local mainOrderModel = GM.MainBoardModel:GetOrderModel()
  local mapOrders = mainOrderModel:GetOrders()
  local orderIndex = 1
  for _, order in pairs(mapOrders) do
    local requireIndex = 1
    local countMap, arrOrder = order:GetUniqueRequirements()
    for itemType, count in pairs(countMap) do
      arrOrderData[orderIndex][requireIndex] = {Type = itemType, Count = count}
      requireIndex = requireIndex + 1
    end
    orderIndex = orderIndex + 1
  end
  self:UpdateContent()
end

function TestOrderGroupWindow:OnClickDeploy()
  if #self.m_cells == 1 and GM.TestModel:DeployOrderGroups({
    self.m_cells[1].orderGroupData
  }) then
    self:Close()
    return
  end
  local arrSelectedOrderGroupData = {}
  for _, cell in ipairs(self.m_cells) do
    if cell.selected then
      arrSelectedOrderGroupData[#arrSelectedOrderGroupData + 1] = cell.orderGroupData
    end
  end
  if arrSelectedOrderGroupData[1] == nil then
    GM.UIManager:ShowTestPrompt("当前未选择订单组！")
    return
  end
  if GM.TestModel:DeployOrderGroups(arrSelectedOrderGroupData) then
    self:Close()
  end
end

function TestOrderGroupWindow:OnClickUndeploy()
  if GM.TestModel:UndeployOrderGroups() then
    self:Close()
  end
end
