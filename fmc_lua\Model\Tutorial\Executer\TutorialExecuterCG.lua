local Step = {PlayVedio = "1"}
local Executer = setmetatable({AlwaysStrongTutorial = true, AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  if GM.LevelModel:GetCurrentLevel() > 1 then
    self:Finish(self.m_gesture, self.m_arrow)
    return
  end
  EventDispatcher.AddListener(EEventType.EnterMainScene, self, self.OnEnterMainScene)
  EventDispatcher.AddListener(EEventType.InitVideoFinished, self, self.OnInitVideoFinished)
end

function Executer:OnEnterMainScene(msg)
  if GM.TutorialModel:IsTutorialOnGoing(ETutorialId.Timeline) or GM.TutorialModel:IsTutorialFinished(ETutorialId.Timeline) or GM.ConfigModel:GetCGPath() == nil then
    self:Finish()
    return
  end
  GM.TimelineLayer:PlayStartVideo()
end

function Executer:OnInitVideoFinished()
  self:Finish()
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
