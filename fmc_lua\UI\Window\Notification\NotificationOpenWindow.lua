NotificationOpenWindow = setmetatable({}, BaseWindow)
NotificationOpenWindow.__index = NotificationOpenWindow

function NotificationOpenWindow:BeforeOpenCheck(descKey)
  if descKey ~= ENotiSceneDescKey.EnergyEmpty then
    return true
  end
  return GM.PropertyDataManager:GetPropertyNum(EPropertyType.Energy) < 10
end

function NotificationOpenWindow:Init(descKey)
  self.m_descKey = descKey
  PlayerPrefs.SetInt(EPlayerPrefKey.LastNotifiWindowPopTime, GM.GameModel:GetServerTime())
  local times = PlayerPrefs.GetInt(EPlayerPrefKey.NotifiWindowPopTimes, 0)
  PlayerPrefs.SetInt(EPlayerPrefKey.NotifiWindowPopTimes, times + 1)
  self.m_descriptionText.text = GM.GameTextModel:GetText(descKey)
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.AutoPopup
  })
  EventDispatcher.AddListener(EEventType.ApplicationWillEnterForeground, self, self.ApplicationWillEnterForeground)
end

function NotificationOpenWindow:ApplicationWillEnterForeground()
  if PlatformInterface.IsNotificationsEnabled() then
    self:Close()
  end
end

function NotificationOpenWindow:OnRequestAuthorization()
  self:LogWindowAction(EBIType.UIActionType.Click)
  GM.NotificationModel:TryOpenNotification(true)
end

function NotificationOpenWindow:OnLateButtonClicked()
  self:LogWindowAction(EBIType.UIActionType.Close)
  self:Close()
end
