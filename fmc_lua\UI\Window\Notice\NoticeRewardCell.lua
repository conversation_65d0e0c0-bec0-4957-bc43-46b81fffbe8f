NoticeRewardCell = {}
NoticeRewardCell.__index = NoticeRewardCell

function NoticeRewardCell:Init(formattedReward)
  local rewardType = formattedReward[PROPERTY_TYPE]
  local spriteName, setNativeSize = RewardApi.GetRewardIconNameAndIsSetNativeSize(rewardType)
  SpriteUtil.SetImage(self.m_iconImage, spriteName, setNativeSize)
  self.m_text.text = "x" .. formattedReward[PROPERTY_COUNT]
  local scale = 1
  if GM.PropertyDataManager:IsPropertyType(rewardType) then
    scale = 0.8
  end
  self.m_iconImage.transform:SetLocalScaleXY(scale)
end
