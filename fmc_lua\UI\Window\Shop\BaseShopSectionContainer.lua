BaseShopSectionContainer = setmetatable({}, BaseShopContainer)
BaseShopSectionContainer.__index = BaseShopSectionContainer

function BaseShopSectionContainer:Init(shopType)
  self.m_shopType = shopType
  self:_UpdateContent(true)
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self._OnShopRefreshed)
end

function BaseShopSectionContainer:GetContentHeight()
  return self.gameObject.activeSelf and self.transform.sizeDelta.y or 0
end

function BaseShopSectionContainer:_OnShopRefreshed(message)
  if message ~= nil and message.shopType == self.m_shopType then
    self:_UpdateContent()
  end
end

function BaseShopSectionContainer:_UpdateContent(flushAnimation)
  local cellData = self:_GetCellData()
  local cellDataEmpty = #cellData == 0
  self.gameObject:SetActive(not cellDataEmpty)
  if cellDataEmpty then
    return
  end
  self.m_sectionView:UpdateCells(cellData, flushAnimation)
end
