NoticeState = {
  Unread = 1,
  FollowLink = 2,
  GetRewards = 3,
  Read = 4
}
NoticeModel = {}
NoticeModel.__index = NoticeModel
NoticeModel.CustomerServiceRewardPrefix = "cs_"

function NoticeModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.NoticeState)
  self.m_hasNewNotice = false
  self.m_textMap = {}
end

function NoticeModel:GetData()
  return self.m_dbTable
end

function NoticeModel:FromSyncData(dataArr)
  self.m_dbTable:FromArr(dataArr)
end

function NoticeModel:HasUnreadSystemNotice()
  local serverTime = GM.GameModel:GetServerTime()
  for _, notice in ipairs(self.m_notices) do
    if serverTime <= notice.DisappearTime and self:GetNoticeState(notice) ~= NoticeState.Read then
      return true
    end
  end
  return false
end

function NoticeModel:CheckNewNotice(callback)
  local funcCallback = function(result, response)
    GM.UIManager:HideMask()
    if callback then
      callback(result)
    end
  end
  GM.UIManager:ShowMask()
  GM.GameModel:Login(funcCallback, 1)
end

function NoticeModel:OnSceneViewLoaded()
  self.m_inited = true
  self:UpdateAfterLoginFinished(nil)
end

function NoticeModel:UpdateAfterLoginFinished(loginResponse)
  if loginResponse ~= nil then
    for _, id in ipairs(loginResponse.received_bulletin_ids) do
      self.m_dbTable:Set(tostring(id), "state", NoticeState.Read)
    end
  end
  self.m_notices = {}
  if not self.m_inited then
    return
  end
  self:_LoadServerConfig()
  self:_UpdateCustomerServiceRewards()
  EventDispatcher.DispatchEvent(EEventType.UpdateNotice)
end

function NoticeModel:_LoadServerConfig()
  local texts = GM.ConfigModel:GetServerConfig(ServerConfigKey.BulletinLanguage)
  if texts == nil then
    return
  end
  for _, text in ipairs(texts) do
    self.m_textMap[text.key] = text.value
  end
  local bulletinConfigs = GM.ConfigModel:GetServerConfig(ServerConfigKey.Bulletin)
  if bulletinConfigs == nil then
    return
  end
  for _, config in ipairs(bulletinConfigs) do
    local title = self.m_textMap[config.titleRes]
    local content = self.m_textMap[config.contentRes]
    local notice = self:_CreateNotice(tostring(config.id), title, content, config.link, config.rewards, config.sTime, config.eTime, config.eventId, config.orderNumber)
    table.insert(self.m_notices, notice)
  end
end

function NoticeModel:_UpdateCustomerServiceRewards()
  for key, rewardInfo in pairs(GM.RewardModel:GetRewardInfoMap()) do
    if StringUtil.StartWith(key, NoticeModel.CustomerServiceRewardPrefix) then
      local releaseTimeString = string.sub(key, string.len(NoticeModel.CustomerServiceRewardPrefix) + 1)
      local releaseTime = tonumber(releaseTimeString)
      local notice = self:_CreateNotice(key, rewardInfo.content.title, rewardInfo.content.desc, nil, rewardInfo.content.rewards, releaseTime, rewardInfo.content.expire_time, rewardInfo.content.eventId, 10000)
      table.insert(self.m_notices, notice)
      if rewardInfo.status == 1 then
        self.m_dbTable:Set(key, "state", NoticeState.Read)
      end
    end
  end
end

function NoticeModel:HasNewNotice()
  return self.m_hasNewNotice
end

function NoticeModel:ResetHasNewNotice()
  self.m_hasNewNotice = false
end

function NoticeModel:GetNotices()
  return self.m_notices
end

function NoticeModel:_CreateNotice(id, title, content, link, rewards, releaseTime, disappearTime, eventId, orderNumber)
  if eventId ~= nil then
    local activityModel = GM.ActivityManager:GetModel(eventId)
    if activityModel == nil or activityModel:GetState() ~= ActivityState.Started then
      return nil
    end
    disappearTime = math.min(disappearTime, activityModel:GetNextStateTime())
  end
  if self.m_dbTable:GetValue(id, "state") == nil then
    self.m_hasNewNotice = true
    self.m_dbTable:Set(id, "state", NoticeState.Unread)
  end
  if rewards and IsString(rewards[1]) then
    rewards = ConfigUtil.GetCurrencyFromArrStr(rewards)
  else
    RewardApi.CryptRewards(rewards, true)
  end
  return {
    Id = id,
    Title = title,
    Content = content,
    Link = link,
    Rewards = rewards,
    ReleaseTime = releaseTime,
    DisappearTime = disappearTime,
    EventId = eventId,
    OrderNumber = orderNumber
  }
end

function NoticeModel:GetNoticeState(notice)
  local state = self.m_dbTable:GetValue(notice.Id, "state")
  Log.Assert(state ~= nil, "公告模型实现错误")
  return state
end

function NoticeModel:_TrySetNoticeState(notice, state)
  local currentState = self:GetNoticeState(notice)
  if state <= currentState then
    return
  end
  self.m_dbTable:Set(notice.Id, "state", state)
end

function NoticeModel:OnNoticeOpen(notice)
  if notice.Rewards ~= nil then
    if notice.Link ~= nil then
      self:_TrySetNoticeState(notice, NoticeState.FollowLink)
    else
      self:_TrySetNoticeState(notice, NoticeState.GetRewards)
    end
  else
    self:_TrySetNoticeState(notice, NoticeState.Read)
  end
end

function NoticeModel:OnNoticeFollowLink(notice)
  if notice.Rewards ~= nil then
    self:_TrySetNoticeState(notice, NoticeState.GetRewards)
    return
  end
  self:_TrySetNoticeState(notice, NoticeState.Read)
end

function NoticeModel:_OnNoticeGetRewards(notice)
  self:_TrySetNoticeState(notice, NoticeState.Read)
end

function NoticeModel:AcquireRewards(notice, callback)
  local isCustomerServiceNotice = StringUtil.StartWith(notice.Id, NoticeModel.CustomerServiceRewardPrefix)
  local callbackWrapper = function(result)
    if result ~= ERewardRespStatus.RespFail then
      self:_OnNoticeGetRewards(notice)
    end
    if result == ERewardRespStatus.Success then
      if GM.GameModel:GetServerTime() > notice.DisappearTime then
        result = ERewardRespStatus.Expired
      else
        local scene = isCustomerServiceNotice and EBIType.CustomerServiceReward or EBIType.NoticeReward
        local targetMode = self:_GetGameModeByEventId(notice.EventId)
        RewardApi.AcquireRewardsLogic(notice.Rewards, EPropertySource.Give, EBIType.NoticeReward, targetMode, CacheItemType.Stack)
      end
    end
    callback(result)
  end
  local rewardId = isCustomerServiceNotice and notice.Id or "mail_" .. notice.Id
  GM.RewardModel:ReceiveReward(rewardId, callbackWrapper)
end

function NoticeModel:_GetGameModeByEventId(activityType)
  local activityDefinition = SpreeActivityDefinition[activityType]
  if activityDefinition ~= nil then
    return activityDefinition.GameMode
  else
    return EGameMode.Board
  end
end
