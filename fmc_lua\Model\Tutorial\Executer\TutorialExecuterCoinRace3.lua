local Step = {HighlightEndLine = "1"}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  for _, activityDefinition in pairs(CoinRaceActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnCoinRaceStateChange)
  end
end

function Executer:_OnOpenView(msg)
  for activityType, activityDefinition in pairs(CoinRaceActivityDefinition) do
    if msg.name == activityDefinition.MainWindowPrefabName and activityDefinition.TutorialSecondRoundId == self:GetTutorialId() then
      local model = GM.ActivityManager:GetModel(activityType)
      if model and model:GetCurrentRound() == 2 then
        self.m_activityDefinition = activityDefinition
        self.m_mainWindow = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
        self:_ExecuteStep1()
      end
    end
  end
end

function Executer:_OnCoinRaceStateChange(msg)
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) and (not msg or not msg.isEnterCompetition) then
    self:Finish()
  end
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.HighlightEndLine
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  local window = self.m_mainWindow
  if window == nil or window.gameObject == nil or window.gameObject:IsNull() then
    self:Finish()
    return
  end
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local transf = window:GetEndLineTransf()
    TutorialHelper.UpdateMask(transf.position, transf.sizeDelta, function()
      self:Finish()
    end, false)
    local percent = 50
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText("coin_race_round2_tutorial_1"), percent)
  end, 0.4)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
