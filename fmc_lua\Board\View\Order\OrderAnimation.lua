OrderAnimation = setmetatable({}, BaseOrderAnimation)
OrderAnimation.__index = OrderAnimation

function OrderAnimation:Awake()
  if self.m_state then
    self:OnOrderStateChanged()
  end
end

function OrderAnimation:OnDestroy()
  Scheduler.UnscheduleTarget(self)
end

function OrderAnimation:Init(order)
  BaseOrderAnimation.Init(self, order:GetAvatarName())
  self.m_order = order
end

function OrderAnimation:OnOrderStateChanged(state)
  local newState = state or self.m_order:GetState()
  if self.m_state == newState then
    if GM.ConfigModel:IsOrderSequenceByEnergyDiff() then
      local compareInfo = self.m_order:GetOrderCompareInfo()
      if self.m_compareInfo ~= nil then
        local old = self.m_compareInfo.filledCount
        local new = compareInfo.filledCount
        if old < new then
          self:SetAnimation("finished", {
            OrderState.Init,
            OrderState.PartiallyFinished
          })
        elseif old > new then
          self:SetAnimation("appear", {})
        end
      end
      self.m_compareInfo = compareInfo
    end
    return
  elseif GM.ConfigModel:IsOrderSequenceByEnergyDiff() then
    local compareInfo = self.m_order:GetOrderCompareInfo()
    self.m_compareInfo = compareInfo
  end
  if GM.ConfigModel:IsOrderSequenceByEnergyDiff() then
    if newState == OrderState.Init then
      self:SetAnimation("appear", {})
    elseif newState == OrderState.CanDeliver or newState == OrderState.PartiallyFinished then
      self:SetAnimation("finished", {
        OrderState.Init,
        OrderState.PartiallyFinished
      })
    end
    self.m_state = newState
    return
  end
  if newState == OrderState.Init or newState == OrderState.PartiallyFinished then
    self:SetAnimation("appear", {})
  elseif newState == OrderState.CanDeliver then
    self:SetAnimation("finished", {
      OrderState.Init,
      OrderState.PartiallyFinished
    })
  end
  self.m_state = newState
end

function OrderAnimation:PlayEnterAnimation()
  self.m_state = nil
  self:OnOrderStateChanged(OrderState.Init)
  DelayExecuteFuncInView(function()
    self:OnOrderStateChanged()
  end, 1, self)
end

function OrderAnimation:PlayLeaveAnimation()
  self:_SetAnimation("leave", false)
end
