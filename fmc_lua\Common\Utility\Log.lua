if not ProjectConfig.IS_LOG_ENABLED then
  function error()
  end
  
  function assert()
  end
  
  function print()
  end
end
Log = {}
LogLevel = {
  Error = "Error",
  Warning = "Warning",
  Info = "Info",
  Debug = "Debug",
  Verbose = "Verbose"
}
LogTag = {
  Default = "Default",
  Network = "Network",
  Audio = "Audio",
  Purchase = "Purchase",
  Ad = "ad"
}

function Log.Error(msg)
  Log._Log(LogLevel.Error, nil, msg, 3)
end

function Log.Warning(msg)
  Log._Log(LogLevel.Warning, nil, msg)
end

function Log.Info(msg, logTag)
  logTag = logTag or LogTag.Default
  Log._Log(LogLevel.Info, logTag, msg)
end

function Log.Debug(msg, logTag)
  logTag = logTag or LogTag.Default
  Log._Log(LogLevel.Debug, logTag, msg)
end

function Log.Verbose(msg, logTag)
  logTag = logTag or LogTag.Default
  Log._Log(LogLevel.Verbose, logTag, msg)
end

function Log.Assert(condition, msg)
  if msg == nil then
    error("Log.Assert without msg", 2)
  end
  if not condition then
    Log._Log(LogLevel.Error, nil, msg, 3)
  end
end

function Log.Encode(data)
  if type(data) == "userdata" then
    return "[" .. tostring(data) .. "]"
  elseif type(data) == "table" then
    local tb = {}
    for k, v in pairs(data) do
      tb[tostring(k)] = Log.Encode(v)
    end
    return json.encode(tb)
  else
    return tostring(data)
  end
end

local _replaceTraceback = GameConfig.IsTestMode() and CS.StringHelper.ReplaceTraceBack or function(msg)
  return msg
end

function Log._Log(logLevel, logTag, msg, errorLevel)
  if logLevel ~= LogLevel.Error and logLevel ~= LogLevel.Warning and not logTag then
    return
  end
  msg = msg or ""
  if not logTag then
    msg = logLevel .. ": " .. (msg or "")
  else
    msg = logLevel .. "_" .. logTag .. ": " .. (msg or "")
  end
  if logLevel == LogLevel.Error then
    if StringUtil.IsNilOrEmpty(msg) then
      error("Log.Error without msg", 2)
    end
    print(msg .. "\n" .. debug.traceback())
    Debug.LogError(_replaceTraceback("LuaException: " .. msg) .. "\n" .. _replaceTraceback(debug.traceback()))
  else
    if logLevel == LogLevel.Warning then
      Debug.LogWarning(_replaceTraceback("LuaWarning: " .. msg) .. "\n" .. _replaceTraceback(debug.traceback()))
    end
    print(msg)
  end
end

function Log.Traceback(...)
  print((...), debug.traceback())
end

local toStringEx = function(value)
  if value ~= nil and type(value) == "string" then
    return "\"" .. value .. "\""
  end
  return tostring(value)
end

function Log.PrintTable(tab, tag)
  if not GameConfig.IsTestMode() then
    return
  end
  local str = {}
  local tabRecord = {}
  
  local function printTableImp(tab, tabRecord, space)
    local ret = ""
    if tab == nil then
      return "nil"
    end
    if rawget(tabRecord, tab) ~= nil then
      return rawget(tabRecord, tab) .. "\n"
    else
      rawset(tabRecord, tab, tostring(tab))
    end
    local nextSpace = space .. "   "
    if type(tab) == "table" then
      ret = ret .. tostring(tab)
      ret = ret .. "\n" .. space .. "{\n"
      for k, v in pairs(tab) do
        ret = ret .. nextSpace .. "[" .. toStringEx(k) .. "]" .. " = " .. printTableImp(v, tabRecord, nextSpace)
      end
      ret = ret .. space .. "}"
    else
      ret = ret .. toStringEx(tab)
    end
    return ret .. "\n"
  end
  
  local str = printTableImp(tab, tabRecord, "")
  tabRecord = nil
  tag = tag or ""
  print(tag .. str)
end
