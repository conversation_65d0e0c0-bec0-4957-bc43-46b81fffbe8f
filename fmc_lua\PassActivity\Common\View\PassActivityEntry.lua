PassActivityEntry = setmetatable({}, HudGeneralButton)
PassActivityEntry.__index = PassActivityEntry

function PassActivityEntry:Init(model)
  self.m_model = model
  self.m_activityType = model:GetType()
  self.m_activityDefinition = PassActivityDefinition[self.m_activityType]
  EventDispatcher.AddListener(self.m_activityDefinition.CanFinishTaskNumberChangedEvent, self, self._UpdateRedDot)
  EventDispatcher.AddListener(self.m_activityDefinition.CanTakeRewardNumberChangedEvent, self, self._UpdateRedDot)
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self._UpdateRedDot)
  self:UpdatePerSecond()
  self:_UpdateRedDot()
end

function PassActivityEntry:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function PassActivityEntry:UpdatePerSecond()
  if self.m_model ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function PassActivityEntry:_UpdateRedDot()
  if self.m_model:GetState() == ActivityState.Released then
    return
  end
  local messageNumber = self.m_model:GetCanFinishTasksCount() + #self.m_model:GetCanTakeRewards()
  if messageNumber == 0 then
    self.m_redDotGo:SetActive(false)
  else
    self.m_redDotGo:SetActive(true)
    self.m_redDotText.text = messageNumber
  end
end

function PassActivityEntry:OnClicked()
  if not GM.UIManager:IsViewExisting(self.m_activityDefinition.MainWindowPrefabName) then
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType)
  end
end

PassActivityShopCell = {}
PassActivityShopCell.__index = PassActivityShopCell
PassActivityShopCell.Height = 640

function PassActivityShopCell:Init(activityType)
  self.m_activityType = activityType
end

function PassActivityShopCell:OnButtonClicked()
  GM.UIManager:CloseView(UIPrefabConfigName.ShopWindow)
  PassActivityViewHelper.OpenBuyTicketWindow(self.m_activityType, "shop")
end

PassActivityBoardBubble = {}
PassActivityBoardBubble.__index = PassActivityBoardBubble

function PassActivityBoardBubble:Awake()
  self:_AddListeners()
end

function PassActivityBoardBubble:Init(model, orderArea)
  self.m_model = model
  self.m_activityType = model:GetType()
  self.m_activityDefinition = PassActivityDefinition[self.m_activityType]
  self.m_iconArea:SetInBoardView()
  if self.gameObject.activeInHierarchy then
    self:_AddListeners()
  end
  self:UpdatePerSecond()
  self:_UpdateContent()
end

function PassActivityBoardBubble:_AddListeners()
  if self.m_activityDefinition == nil or self.m_model == nil then
    return
  end
  EventDispatcher.AddListener(self.m_activityDefinition.CanFinishTaskNumberChangedEvent, self, self._UpdateContent, true)
  EventDispatcher.AddListener(self.m_activityDefinition.CanTakeRewardNumberChangedEvent, self, self._UpdateContent, true)
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self._UpdateContent, true)
  EventDispatcher.AddListener(self.m_activityDefinition.RefreshTimelimitTasksEvent, self, self._UpdateContent, true)
end

function PassActivityBoardBubble:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function PassActivityBoardBubble:OnEnable()
  self:UpdatePerSecond()
end

function PassActivityBoardBubble:UpdatePerSecond()
  if self.m_model ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function PassActivityBoardBubble:_UpdateContent()
  self:_UpdateRedDot()
  self:_UpdateProgressSlider()
end

function PassActivityBoardBubble:_UpdateRedDot()
  if self.m_model:GetState() == ActivityState.Released then
    return
  end
  local messageNumber = self.m_model:GetCanFinishTasksCount() + #self.m_model:GetCanTakeRewards()
  if messageNumber == 0 then
    self.m_redDotGo:SetActive(false)
  else
    self.m_redDotGo:SetActive(true)
    self.m_redDotText.text = messageNumber
  end
end

function PassActivityBoardBubble:_UpdateProgressSlider()
  if self.m_model == nil or self.m_model:GetState() == ActivityState.Released or self.m_slider == nil or self.m_sliderNumText == nil or self.m_maxLevelDescGo == nil then
    return
  end
  local levelConfigs = self.m_model:GetLevelConfigs() or {}
  local currentTokenLevel = self.m_model:GetCurrentLevel() or 0
  local bFinished = currentTokenLevel >= #levelConfigs and not self.m_model:HasExtraLevels()
  UIUtil.SetActive(self.m_maxLevelDescGo, bFinished)
  UIUtil.SetActive(self.m_sliderNumText.gameObject, not bFinished)
  local cur, total = self.m_model:GetCurDayTaskProgress()
  self.m_slider.gameObject:SetActive(true)
  if not bFinished then
    if cur <= total then
      self.m_sliderNumText.text = cur .. "/" .. total
      self.m_slider.value = cur / total
    end
  else
    self.m_slider.value = 1
  end
end

function PassActivityBoardBubble:OnClicked()
  if not GM.UIManager:IsViewExisting(self.m_activityDefinition.MainWindowPrefabName) then
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType)
  end
end
