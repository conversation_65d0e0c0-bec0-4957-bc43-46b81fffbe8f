DataBalancePopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true
  },
  canIgnorePopup = false
}, BasePopupHelper)
DataBalancePopupHelper.__index = DataBalancePopupHelper

function DataBalancePopupHelper.Create()
  local helper = setmetatable({}, DataBalancePopupHelper)
  helper:Init()
  return helper
end

function DataBalancePopupHelper:CheckPopup()
  if GM.DataBalanceModel:NeedBalance() then
    return UIPrefabConfigName.DataBalanceWindow
  end
end
