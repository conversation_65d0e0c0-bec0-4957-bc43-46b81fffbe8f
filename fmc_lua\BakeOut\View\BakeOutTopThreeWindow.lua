BakeOutTopThreeWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BaseWindow)
BakeOutTopThreeWindow.__index = BakeOutTopThreeWindow

function BakeOutTopThreeWindow:Init(arrRankDatas, bakeoutId)
  for i = 1, 3 do
    self["m_podium" .. i]:UpdateContent(arrRankDatas and arrRankDatas[i] or nil)
  end
  self.m_arrRankDatas = arrRankDatas
  self.m_bakeoutId = bakeoutId
end

function BakeOutTopThreeWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  GM.UIManager:OpenView(UIPrefabConfigName.BakeOutMainWindow, self.m_arrRankDatas, nil, self.m_bakeoutId)
end
