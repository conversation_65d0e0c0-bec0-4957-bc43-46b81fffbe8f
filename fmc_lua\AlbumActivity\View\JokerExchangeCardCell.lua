JokerExchangeCardCell = {}
JokerExchangeCardCell.__index = JokerExchangeCardCell

function JokerExchangeCardCell:Init(cardId, model, bShowNoneCard, bOnlyOne, wnd, selectCallback)
  self.m_cardId = cardId
  self.m_model = model
  self.m_bShowNoneCard = bShowNoneCard
  self.m_selectcallback = selectCallback
  self.m_exchangeWindow = wnd
  if self.m_exchangeWindow ~= nil then
    UIUtil.UpdateSortingOrder(self.m_selectBgGo, self.m_exchangeWindow:GetSortingOrder() + 1)
  end
  local num = self.m_model:GetCardCount(cardId, true)
  self.m_cardLuaTable:Init(cardId, model, num < 1, true, num, true, false)
  local isGoldJoker = self.m_model:IsGoldenJokerCardInUse()
  local bGoldCard = self.m_model:IsGoldCard(cardId)
  local bLocked = bGoldCard and not isGoldJoker
  UIUtil.SetActive(self.m_lockGo, bLocked)
  self.m_bLocked = bLocked
  UIUtil.SetActive(self.m_completeGo, bOnlyOne and not bLocked and num < 1)
  if bShowNoneCard then
    self.gameObject:SetActive(num <= 0)
  end
  local bSelect = self.m_exchangeWindow ~= nil and self.m_exchangeWindow:IsSelectCard(self.m_cardId)
  self.m_bSelect = bSelect
  self:UpdateSelectUI(bSelect)
  if not self.m_bInit and self.m_exchangeWindow ~= nil and self.m_selectcallback ~= nil then
    self.m_bInit = true
    EventDispatcher.AddListener(EEventType.JokerUpdateExchangeSelect, self, self._OnUpdataSelect)
  end
end

function JokerExchangeCardCell:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function JokerExchangeCardCell:UpdateSelectState()
  if not self.gameObject.activeSelf or self.m_exchangeWindow == nil then
    return
  end
  if self.m_bSelect and not self.m_exchangeWindow:IsSelectCard(self.m_cardId) then
    self:UpdateSelectUI(false)
  end
end

function JokerExchangeCardCell:OnButtonClicked()
  if self.m_exchangeWindow == nil or self.m_selectcallback == nil then
    return
  end
  if self.m_bLocked then
    GM.UIManager:ShowPromptWithKey("golden_card_lock_tip")
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxColdingClick)
    return
  end
  self.m_bSelect = not self.m_bSelect
  self:UpdateSelectUI(self.m_bSelect)
  if self.m_bSelect then
    self.m_selectcallback(self.m_cardId)
  else
    self.m_selectcallback(nil)
  end
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxButtonClick)
end

function JokerExchangeCardCell:_OnUpdataSelect(event)
  if event.cardId == self.m_cardId then
    return
  end
  local isSelect = self.m_exchangeWindow:IsSelectCard(self.m_cardId)
  self.m_bSelect = isSelect
  self:UpdateSelectUI(self.m_bSelect)
end

function JokerExchangeCardCell:UpdateSelectUI(state)
  self.m_selectBgGo:SetActive(state)
  self.m_selectAnimator:SetBool("select", state)
end
