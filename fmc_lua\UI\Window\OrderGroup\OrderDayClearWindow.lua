OrderDayClearWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
OrderDayClearWindow.__index = OrderDayClearWindow

function OrderDayClearWindow:Init()
  local finishAllTasks = not GM.TaskManager:CanFinishOngoingTask()
  if finishAllTasks then
    local desc
    local preTipTime = GM.ChapterDataModel:GetPreTipTime()
    if preTipTime == nil then
      desc = GM.GameTextModel:GetText("no_day_tip")
    else
      desc = GM.GameTextModel:GetText("no_day_pretip", preTipTime)
    end
    self.m_descText.text = desc
    self.m_comingGo:SetActive(true)
    self.m_taskGo:SetActive(false)
  else
    self.m_descText.text = GM.GameTextModel:GetText("hint_board_no_order_desc")
    self.m_comingGo:SetActive(false)
    self.m_taskGo:SetActive(true)
  end
  if self.m_rainbowSpine then
    self.m_rainbowSpine:Init()
    self.m_rainbowSpine:PlayAnimation("appear", function()
      self.m_rainbowSpine:PlayAnimation("idle", nil, true)
    end, false, true)
  end
end

function OrderDayClearWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  if self.m_rainbowSpine then
    self.m_rainbowSpine:ThrowCallback()
    self.m_rainbowSpine:PlayAnimation("disappear", nil, false, true)
  end
end
