ItemTypeDeleteWindow = setmetatable({}, BaseWindow)
ItemTypeDeleteWindow.__index = ItemTypeDeleteWindow

function ItemTypeDeleteWindow:Init(rewardCount, mapItems)
  self.rewardCount = rewardCount
  if rewardCount == 0 then
    return
  end
  local keys = Table.GetKeys(mapItems)
  table.sort(keys)
  local obj, cell, count
  for _, item in ipairs(keys) do
    count = mapItems[item]
    obj = Object.Instantiate(self.m_itemCellPrefab, self.m_itemCellRootTrans)
    obj:SetActive(true)
    cell = obj:GetLuaTable()
    cell:Init(item, count)
  end
  self.m_rewardCountTxt.text = "x" .. rewardCount
  if #keys <= 4 then
    UIUtil.AddSizeDelta(self.m_scrollViewTrans, nil, -100)
    UIUtil.AddSizeDelta(self.m_topContentTrans, nil, -100)
    UIUtil.AddSizeDelta(self.m_contentTrans, nil, -100)
    self.m_scrollRect.enabled = false
  end
end

function ItemTypeDeleteWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  if self.rewardCount > 0 then
    local rewards = {
      [PROPERTY_TYPE] = EPropertyType.Energy,
      [PROPERTY_COUNT] = self.rewardCount
    }
    RewardApi.AcquireRewardsInView({rewards})
  end
end

ItemTypeDeletedItem = {}
ItemTypeDeletedItem.__index = ItemTypeDeletedItem

function ItemTypeDeletedItem:Awake()
  self.m_go1:SetActive(true)
  self.m_go2:SetActive(false)
  local seq = DOTween.Sequence()
  self.seq = seq
  seq:InsertCallback(1, function()
    self.m_go1:SetActive(true)
    self.m_go2:SetActive(false)
    UIUtil.SetAlpha(self.m_go1ArrowImg, 1)
    UIUtil.SetAlpha(self.m_go1ItemImg, 1)
  end)
  seq:Insert(1, self.m_go1ArrowImg:DOFade(0, 1.15))
  seq:Insert(1, self.m_go1ItemImg:DOFade(0, 1.15))
  seq:InsertCallback(2, function()
    self.m_go1:SetActive(false)
    self.m_go2:SetActive(true)
  end)
  seq:SetLoops(-1)
end

function ItemTypeDeletedItem:OnDestroy()
  if self.seq then
    self.seq:Kill()
    self.seq = nil
  end
end
