SurveyWindow = setmetatable({canCloseByAndroidBack = false, canClickWindowMask = false}, BaseWindow)
SurveyWindow.__index = SurveyWindow

function SurveyWindow:Init()
  self.m_startTime = TimeUtil.GetTimeInMS()
  self:_ShowNextQuestion()
end

function SurveyWindow:_ShowNextQuestion()
  self.m_btnText.text = GM.GameTextModel:GetText(GM.SurveyModel.isLastQuestion and "question_end_button" or "question_next_button")
  local prefab = UIPrefabConfigName.SurveySingleChoice
  local qType = GM.SurveyModel.curQuestionData.type
  if qType == EQuestionType.MultipleChoice then
    prefab = UIPrefabConfigName.SurveyMultiChoice
  elseif qType == EQuestionType.MultipleChoiceFillBlank then
    prefab = UIPrefabConfigName.SurveyMultiChoiceFillBlank
  elseif qType == EQuestionType.FillBlank then
    prefab = UIPrefabConfigName.SurveyFillBlank
  elseif qType == EQuestionType.PictureFillBlank then
    prefab = UIPrefabConfigName.SurveyFillBlankImg
  elseif qType == EQuestionType.PictureSingleChoice then
    prefab = UIPrefabConfigName.SurveySingleChoiceImg
  elseif qType == EQuestionType.PictureMultipeChoice then
    prefab = UIPrefabConfigName.SurveyMultiChoiceImg
  elseif qType == EQuestionType.PictureSingleChoiceFillBlank then
    prefab = UIPrefabConfigName.SurveySingleChoiceFillBlankImg
  elseif qType == EQuestionType.PictureMultipChoiceFillBlank then
    prefab = UIPrefabConfigName.SurveyMultiChoiceFillBlankImg
  end
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(prefab), self.m_containerTrans, Vector3(10000, 0, 0), function(go)
    if self.m_contentGo then
      self.m_contentGo:RemoveSelf()
      self.m_contentGo = nil
      self.m_contentCmp = nil
    end
    self.m_contentGo = go
    self.m_contentGo.transform.localPosition = Vector3.zero
    self.m_contentCmp = self.m_contentGo:GetLuaTable()
    self.m_contentCmp:Init(GM.SurveyModel.curQuestionData)
  end)
end

function SurveyWindow:OnNextClicked()
  if not self:CanNextStep() then
    return
  end
  local answers, nextQIds = self.m_contentCmp:GetUserAnswer()
  if GM.SurveyModel.isLastQuestion then
    GM.SurveyModel:StoreAnswerAndSubmit(answers, nextQIds, function(eRewardRespStatus)
      if eRewardRespStatus == ERewardRespStatus.Success then
        self:Close()
        GM.BIManager:LogSurvey(200, TimeUtil.GetTimeInMS() - self.m_startTime, GM.SurveyModel:GetBIData(true))
      elseif eRewardRespStatus == ERewardRespStatus.HasReceived then
        self:Close()
        GM.UIManager:ShowPromptWithKey("question_claim_tips")
        GM.BIManager:LogSurvey(200, TimeUtil.GetTimeInMS() - self.m_startTime, GM.SurveyModel:GetBIData(true))
      else
        GM.UIManager:ShowPromptWithKey("bad_network_window_desc")
      end
    end)
  else
    GM.SurveyModel:StoreAnswerAndStepForward(answers, nextQIds)
    self:_ShowNextQuestion()
  end
end

function SurveyWindow:CanNextStep()
  if not self.m_contentCmp then
    return false
  end
  return self.m_contentCmp:CanNextStep()
end
