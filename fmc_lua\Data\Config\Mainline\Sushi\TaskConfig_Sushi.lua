return {
  {
    ChapterId = "Sushi",
    Id = 1,
    Cost = 796,
    <PERSON><PERSON>s = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "insideFloor",
        State = 9
      },
      {
        Slot = "oldInsideFloor",
        State = 100
      }
    }
  },
  {
    ChapterId = "Sushi",
    Id = 2,
    StartConditions = {1},
    Cost = 739,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldBoxDoor", State = 100}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 3,
    StartConditions = {2},
    Cost = 683,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldBoxA", State = 100}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 4,
    StartConditions = {3},
    Cost = 683,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldBoxB", State = 100}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 5,
    StartConditions = {4},
    Cost = 683,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldBoxWall", State = 100}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 6,
    StartConditions = {5},
    Cost = 796,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "boxWall", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 7,
    StartConditions = {6},
    Cost = 570,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "boxFloor", State = 9},
      {
        Slot = "oldBoxFloor",
        State = 100
      }
    }
  },
  {
    ChapterId = "Sushi",
    Id = 8,
    StartConditions = {7},
    Cost = 570,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "boxTableB", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 9,
    StartConditions = {8},
    Cost = 680,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "boxUtensilB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Sushi",
    Id = 10,
    StartConditions = {9},
    Cost = 616,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "boxTableA", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 11,
    StartConditions = {10},
    Cost = 744,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "boxUtensilA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Sushi",
    Id = 12,
    StartConditions = {11},
    Cost = 680,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldScreen", State = 100}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 13,
    StartConditions = {12},
    Cost = 808,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "backWall", State = 1}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 14,
    StartConditions = {13},
    Cost = 744,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "backWall", State = 2}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 15,
    StartConditions = {14},
    Cost = 744,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "backWall", State = 3}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 16,
    StartConditions = {15},
    Cost = 744,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "backWall", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 17,
    StartConditions = {16},
    Cost = 542,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldRockR", State = 100}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 18,
    StartConditions = {17},
    Cost = 708,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldWallR", State = 100}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 19,
    StartConditions = {18},
    Cost = 819,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "tableR", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 20,
    StartConditions = {19},
    Cost = 764,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "utensilR", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 21,
    StartConditions = {20},
    Cost = 819,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "wallR", State = 1}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 22,
    StartConditions = {21},
    Cost = 764,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldDoor", State = 100}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 23,
    StartConditions = {22},
    Cost = 819,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "wallR", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 24,
    StartConditions = {23},
    Cost = 764,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "oldMidTable",
        State = 100
      }
    }
  },
  {
    ChapterId = "Sushi",
    Id = 25,
    StartConditions = {24},
    Cost = 753,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midTable", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 26,
    StartConditions = {25},
    Cost = 753,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midUtensil", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 27,
    StartConditions = {26},
    Cost = 812,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midFood", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 28,
    StartConditions = {27},
    Cost = 812,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "midDec", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 29,
    StartConditions = {28},
    Cost = 574,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldRockL", State = 100}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 30,
    StartConditions = {29},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "oldWallL", State = 100}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 31,
    StartConditions = {30},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "wallL", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 32,
    StartConditions = {31},
    Cost = 872,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "tableL", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 33,
    StartConditions = {32},
    Cost = 721,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "utensilL", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 34,
    StartConditions = {33},
    Cost = 905,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "boxDoor", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 35,
    StartConditions = {34},
    Cost = 966,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secFloor", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 36,
    StartConditions = {35},
    Cost = 966,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secWall", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 37,
    StartConditions = {36},
    Cost = 782,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secTableA", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 38,
    StartConditions = {37},
    Cost = 844,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secUtensilA",
        State = 9
      }
    }
  },
  {
    ChapterId = "Sushi",
    Id = 39,
    StartConditions = {38},
    Cost = 660,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secDecA", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 40,
    StartConditions = {39},
    Cost = 782,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secTableB", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 41,
    StartConditions = {40},
    Cost = 844,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secUtensilB",
        State = 9
      }
    }
  },
  {
    ChapterId = "Sushi",
    Id = 42,
    StartConditions = {41},
    Cost = 656,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secDecB", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 43,
    StartConditions = {42},
    Cost = 867,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secWallDec", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 44,
    StartConditions = {43},
    Cost = 867,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "boxDoorDec", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 45,
    StartConditions = {44},
    Cost = 1077,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "door", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 46,
    StartConditions = {45},
    Cost = 726,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "outFloor", State = 9},
      {
        Slot = "oldOutFloor",
        State = 100
      }
    }
  },
  {
    ChapterId = "Sushi",
    Id = 47,
    StartConditions = {46},
    Cost = 1007,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "wallDecL", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 48,
    StartConditions = {47},
    Cost = 1077,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rockL", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 49,
    StartConditions = {48},
    Cost = 726,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rockR", State = 9}
    }
  },
  {
    ChapterId = "Sushi",
    Id = 50,
    StartConditions = {49},
    Cost = 1007,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "doorDec", State = 9}
    }
  }
}
