local Step = {
  HighlightCompetitor = "1",
  HighlightRankReward = "2",
  HighlightRoundReward = "3"
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  for _, activityDefinition in pairs(PkRaceDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
end

function Executer:_OnOpenView(message)
  for activityType, activityDefinition in pairs(PkRaceDefinition) do
    if message.name == activityDefinition.MainWindowPrefabName then
      self.m_activityModel = GM.ActivityManager:GetModel(activityType)
      self.m_mainWindow = GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName)
      self.m_activityDefinition = activityDefinition
      self.m_activityType = activityType
      if self.m_activityModel:GetMyScore() > 0 then
        self:_Finish()
      else
        self:_ExecuteStep1()
      end
    end
  end
end

function Executer:_OnStateChanged(message)
  if self.m_activityModel == nil then
    return
  end
  self:_Finish()
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  self.m_strOngoingDatas = Step.HighlightCompetitor
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    if self.m_mainWindow.gameObject == nil or self.m_mainWindow.gameObject:IsNull() or self.m_activityModel:GetState() ~= ActivityState.Started then
      self:_Finish()
      return
    end
    local callback = function()
      self:_ExecuteStep2()
    end
    local trans = self.m_mainWindow:GetCompetitorTutorialRect()
    TutorialHelper.UpdateMask(trans.position, trans.rect.size, callback, false)
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText("pkrace_tutorial_1"), 50)
  end, 1.5)
end

function Executer:_ExecuteStep2()
  if self.m_mainWindow.gameObject == nil or self.m_mainWindow.gameObject:IsNull() then
    self:_Finish()
    return
  end
  self.m_strOngoingDatas = Step.HighlightRankReward
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local callback = function()
    self:_ExecuteStep3()
  end
  local trans = self.m_mainWindow:GetRankRewardTutorialRect()
  TutorialHelper.UpdateMask(trans.position, trans.rect.size, callback, false)
  local text = GM.GameTextModel:GetText("pkrace_tutorial_2")
  TutorialHelper.ShowDialog(text, 40)
end

function Executer:_ExecuteStep3()
  if self.m_mainWindow.gameObject == nil or self.m_mainWindow.gameObject:IsNull() then
    self:_Finish()
    return
  end
  self.m_strOngoingDatas = Step.HighlightRoundReward
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local callback = function()
    self:_Finish()
  end
  local trans = self.m_mainWindow:GetRoundRewardTutorialRect()
  TutorialHelper.UpdateMask(trans.position, trans.rect.size, callback, false)
  local text = GM.GameTextModel:GetText("pkrace_tutorial_3")
  TutorialHelper.ShowDialog(text, 50)
end

function Executer:_Finish()
  self:Finish(self.m_gesture)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
