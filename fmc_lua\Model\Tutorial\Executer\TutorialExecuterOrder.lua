local Step = {ClickGo = "1"}
local EStep2TextAnchorPercent = {
  [Step.ClickGo] = 40
}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:_InitArgs(args)
  self.m_orderId = args and args[1] or nil
end

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OrderFinished, self, self._OnOrderFinished)
end

function Executer:_OnOrderFinished()
  if self.m_orderCell then
    TutorialHelper.DehighlightOrder(self.m_orderCell)
    self.m_orderCell = nil
    self:Finish(self.m_gesture)
  end
end

function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  local targetOrder = self:_GetValidFixedOrder()
  if targetOrder == nil then
    return
  end
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickGo
  self:_SaveOngoingDatas()
  self:_HighlightOrderCell(targetOrder)
  return true
end

function Executer:_HighlightOrderCell(targetOrder)
  TutorialHelper.WholeMask()
  local textKey = "tutorial_serve_order_" .. self.m_orderId
  if GM.GameTextModel:HasText(textKey) then
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(textKey), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  end
  local orderCell = TutorialHelper.HighlightOrder(targetOrder)
  if not orderCell then
    self:Finish(self.m_gesture)
    return
  end
  self.m_orderCell = orderCell
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(orderCell:GetGoButton().gameObject.transform)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_GetValidFixedOrder()
  local orders = GM.MainBoardModel:GetOrders()
  for _, order in pairs(orders) do
    if order:GetType() == OrderType.Fixed and order:GetState() == OrderState.CanDeliver and order:GetId() == self.m_orderId then
      GM.UIManager:SetEventLockUntilNextPopup()
      local boardView = MainBoardView.GetInstance()
      if boardView ~= nil then
        local orderArea = boardView:GetOrderArea()
        local orderCell = orderArea:GetCell(order)
        if not orderArea:IsPlayingOrderAnimation() and not orderArea:IsScriptScroll() and orderCell ~= nil and orderCell:GetOrderViewState() == OrderState.CanDeliver then
          return order
        end
      end
    end
  end
  return nil
end

return function(tutorialId, strDatas, args)
  local copy = Table.DeepCopy(Executer)
  copy:_InitArgs(args)
  return TutorialExecuter.CreateExecuter(copy, tutorialId, strDatas)
end
