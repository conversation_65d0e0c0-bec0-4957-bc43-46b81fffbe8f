TestOrderGroupEditWindow = setmetatable({}, BaseWindow)
TestOrderGroupEditWindow.__index = TestOrderGroupEditWindow

function TestOrderGroupEditWindow:Init(orderGroupData)
  self.orderGroupData = orderGroupData
  self.m_nameInput.text = orderGroupData.name or ""
  self.m_cells = {}
  for i = 1, 7 do
    local cell = Object.Instantiate(self.m_prefab, self.m_content)
    local cellTb = cell:GetLuaTable()
    cellTb:Init(orderGroupData.arrOrderData[i])
    self.m_cells[#self.m_cells + 1] = cellTb
  end
end

function TestOrderGroupEditWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  self:Save()
end

function TestOrderGroupEditWindow:OnEndEditName()
  self.orderGroupData.name = self.m_nameInput.text
end

function TestOrderGroupEditWindow:Save()
  GM.TestModel:SaveOrderGroups()
  local orderGroupWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.TestOrderGroupWindow)
  if orderGroupWindow then
    orderGroupWindow:UpdateContent()
  end
end
