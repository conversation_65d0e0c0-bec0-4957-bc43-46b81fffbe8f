ShopContainerDiamonds = setmetatable({}, BaseShopListContainer)
ShopContainerDiamonds.__index = ShopContainerDiamonds

function ShopContainerDiamonds:Awake()
  BaseShopListContainer.Init(self, EShopType.Diamonds, false, 3)
end

function ShopContainerDiamonds:_GetCellData()
  return GM.ShopModel:GetDiamondsDatas() or {}
end

function ShopContainerDiamonds:_OnCellClicked(cell)
  local callback = function(success, rewards)
    if success then
      GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, rewards, "rewards_window_title_shop", false)
      GM.AudioModel:PlayEffect(AudioFileConfigName.SfxShopBuy)
    end
  end
  GM.ShopModel:BuyDiamonds(cell:GetData(), callback)
end
