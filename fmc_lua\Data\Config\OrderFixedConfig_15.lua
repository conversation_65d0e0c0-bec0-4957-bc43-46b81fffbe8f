return {
  {
    Id = "150010",
    GroupId = 1,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "150020",
    GroupId = 1,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150030",
    GroupId = 1,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e5dst_28",
      Count = 1
    }
  },
  {
    Id = "150040",
    GroupId = 1,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "150050",
    GroupId = 1,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150060",
    GroupId = 1,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "150070",
    GroupId = 1,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e3semi_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6rice_8",
      Count = 1
    }
  },
  {
    Id = "150080",
    GroupId = 2,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_10e6rice_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    }
  },
  {
    Id = "150090",
    GroupId = 2,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e6semi_19",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150100",
    GroupId = 2,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6porr_2",
      Count = 1
    }
  },
  {
    Id = "150110",
    GroupId = 2,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "150120",
    GroupId = 2,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e4nice_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150130",
    GroupId = 2,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e3icytre_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "150140",
    GroupId = 2,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150150",
    GroupId = 3,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5maca_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150160",
    GroupId = 3,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6soup_15",
      Count = 1
    }
  },
  {
    Id = "150170",
    GroupId = 3,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e6nice_9",
      Count = 1
    }
  },
  {
    Id = "150180",
    GroupId = 3,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "150190",
    GroupId = 3,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e1tato_13",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150200",
    GroupId = 3,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "150210",
    GroupId = 3,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150220",
    GroupId = 4,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "150230",
    GroupId = 4,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_fd_11", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_3",
      Count = 1
    }
  },
  {
    Id = "150240",
    GroupId = 4,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5maca_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150250",
    GroupId = 4,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "150260",
    GroupId = 4,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "150270",
    GroupId = 4,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e5mt_12",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150280",
    GroupId = 4,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e5fd_34",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150290",
    GroupId = 5,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "150300",
    GroupId = 5,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4assort_3",
      Count = 1
    }
  },
  {
    Id = "150310",
    GroupId = 5,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_15e3semi_20",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150320",
    GroupId = 5,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e1nutt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "150330",
    GroupId = 5,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e5nice_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150340",
    GroupId = 5,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150350",
    GroupId = 5,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "150360",
    GroupId = 6,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_15e1nice_1",
      Count = 1
    }
  },
  {
    Id = "150370",
    GroupId = 6,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    }
  },
  {
    Id = "150380",
    GroupId = 6,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150390",
    GroupId = 6,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "150400",
    GroupId = 6,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6stewmt_6",
      Count = 1
    }
  },
  {
    Id = "150410",
    GroupId = 6,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e4nice_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150420",
    GroupId = 6,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1dst_21",
      Count = 1
    }
  },
  {
    Id = "150430",
    GroupId = 7,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "150440",
    GroupId = 7,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e5mt_12",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150450",
    GroupId = 7,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_6e1icytre_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5maca_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150460",
    GroupId = 7,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e6nice_9",
      Count = 1
    }
  },
  {
    Id = "150470",
    GroupId = 7,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "150480",
    GroupId = 7,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e1dst_31",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150490",
    GroupId = 7,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "150500",
    GroupId = 8,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "150510",
    GroupId = 8,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_9e6nibble_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150520",
    GroupId = 8,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5nice_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150530",
    GroupId = 8,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "150540",
    GroupId = 8,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e4friedmt_19",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6porr_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150550",
    GroupId = 8,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_11e2mt_15",
      Count = 1
    }
  },
  {
    Id = "150560",
    GroupId = 8,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "150570",
    GroupId = 9,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e5nice_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150580",
    GroupId = 9,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_4",
      Count = 1
    }
  },
  {
    Id = "150590",
    GroupId = 9,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5maca_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150600",
    GroupId = 9,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_1",
      Count = 1
    }
  },
  {
    Id = "150610",
    GroupId = 9,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e4curry_24",
      Count = 1
    }
  },
  {
    Id = "150620",
    GroupId = 9,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_11e3scsau_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e4brun_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150630",
    GroupId = 9,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "150640",
    GroupId = 10,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "150650",
    GroupId = 10,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    }
  },
  {
    Id = "150660",
    GroupId = 10,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e1nice_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150670",
    GroupId = 10,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    }
  },
  {
    Id = "150680",
    GroupId = 10,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5fd_42",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150690",
    GroupId = 10,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "150700",
    GroupId = 10,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4sf_30",
      Count = 1
    }
  },
  {
    Id = "150710",
    GroupId = 11,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "150720",
    GroupId = 11,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5nice_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150730",
    GroupId = 11,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150740",
    GroupId = 11,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5fd_38",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150750",
    GroupId = 11,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "150760",
    GroupId = 11,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_8",
      Count = 1
    }
  },
  {
    Id = "150770",
    GroupId = 11,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "150780",
    GroupId = 12,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "150790",
    GroupId = 12,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e1dst_26",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150800",
    GroupId = 12,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4friedmt_17",
      Count = 1
    }
  },
  {
    Id = "150810",
    GroupId = 12,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "150820",
    GroupId = 12,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e1dst_30",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150830",
    GroupId = 12,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "150840",
    GroupId = 12,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150850",
    GroupId = 13,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150860",
    GroupId = 13,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5fd_35",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150870",
    GroupId = 13,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "150880",
    GroupId = 13,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e6rice_13",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_9",
      Count = 1
    }
  },
  {
    Id = "150890",
    GroupId = 13,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "150900",
    GroupId = 13,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e4nice_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150910",
    GroupId = 13,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6sf_22",
      Count = 1
    }
  },
  {
    Id = "150920",
    GroupId = 14,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e6nice_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6dst_16",
      Count = 1
    }
  },
  {
    Id = "150930",
    GroupId = 14,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5fd_36",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150940",
    GroupId = 14,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "150950",
    GroupId = 14,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    }
  },
  {
    Id = "150960",
    GroupId = 14,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "150970",
    GroupId = 14,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "150980",
    GroupId = 14,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e1nice_1",
      Count = 1
    }
  },
  {
    Id = "150990",
    GroupId = 15,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151000",
    GroupId = 15,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_friedve_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e6nice_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151010",
    GroupId = 15,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "151020",
    GroupId = 15,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "151030",
    GroupId = 15,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "151040",
    GroupId = 15,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_11e3icytre_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e4nice_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151050",
    GroupId = 15,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "151060",
    GroupId = 16,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e5fd_27",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopve_4",
      Count = 1
    }
  },
  {
    Id = "151070",
    GroupId = 16,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "151080",
    GroupId = 16,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5maca_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151090",
    GroupId = 16,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e4sf_30",
      Count = 1
    }
  },
  {
    Id = "151100",
    GroupId = 16,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "151110",
    GroupId = 16,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5fd_34",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151120",
    GroupId = 16,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_mixdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151130",
    GroupId = 17,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151140",
    GroupId = 17,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5nice_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151150",
    GroupId = 17,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "151160",
    GroupId = 17,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "151170",
    GroupId = 17,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e5fd_32",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e6semi_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151180",
    GroupId = 17,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e6rice_13",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_4",
      Count = 1
    }
  },
  {
    Id = "151190",
    GroupId = 17,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "151200",
    GroupId = 18,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "151210",
    GroupId = 18,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "151220",
    GroupId = 18,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e5fd_41",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151230",
    GroupId = 18,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e5semi_16",
      Count = 1
    }
  },
  {
    Id = "151240",
    GroupId = 18,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "151250",
    GroupId = 18,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e5fd_31",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151260",
    GroupId = 18,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "151270",
    GroupId = 19,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_3",
      Count = 1
    }
  },
  {
    Id = "151280",
    GroupId = 19,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e6nibble_12",
      Count = 1
    }
  },
  {
    Id = "151290",
    GroupId = 19,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151300",
    GroupId = 19,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    }
  },
  {
    Id = "151310",
    GroupId = 19,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e5dst_28",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151320",
    GroupId = 19,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151330",
    GroupId = 19,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6sf_22",
      Count = 1
    }
  },
  {
    Id = "151340",
    GroupId = 20,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_2",
      Count = 1
    }
  },
  {
    Id = "151350",
    GroupId = 20,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_10", Count = 1},
    Requirement_2 = {
      Type = "ds_15e1fd_30",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151360",
    GroupId = 20,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151370",
    GroupId = 20,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_7e5mt_6", Count = 1}
  },
  {
    Id = "151380",
    GroupId = 20,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151390",
    GroupId = 20,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5nice_4",
      Count = 1
    }
  },
  {
    Id = "151400",
    GroupId = 20,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "151410",
    GroupId = 21,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_12e6porr_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_2",
      Count = 1
    }
  },
  {
    Id = "151420",
    GroupId = 21,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e1dst_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151430",
    GroupId = 21,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    }
  },
  {
    Id = "151440",
    GroupId = 21,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_11e4tato_5",
      Count = 1
    }
  },
  {
    Id = "151450",
    GroupId = 21,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e5fd_28",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151460",
    GroupId = 21,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "151470",
    GroupId = 21,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151480",
    GroupId = 22,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151490",
    GroupId = 22,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5dst_29",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151500",
    GroupId = 22,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "151510",
    GroupId = 22,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    }
  },
  {
    Id = "151520",
    GroupId = 22,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151530",
    GroupId = 22,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "151540",
    GroupId = 22,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_13e4assort_12",
      Count = 1
    }
  },
  {
    Id = "151550",
    GroupId = 23,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "151560",
    GroupId = 23,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_15e1nice_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151570",
    GroupId = 23,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e6soup_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151580",
    GroupId = 23,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "151590",
    GroupId = 23,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5maca_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151600",
    GroupId = 23,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "151610",
    GroupId = 23,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "151620",
    GroupId = 24,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e1nice_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151630",
    GroupId = 24,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5fd_43",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151640",
    GroupId = 24,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "151650",
    GroupId = 24,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e6rice_14",
      Count = 1
    }
  },
  {
    Id = "151660",
    GroupId = 24,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5maca_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151670",
    GroupId = 24,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e6nice_6",
      Count = 1
    }
  },
  {
    Id = "151680",
    GroupId = 24,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    }
  },
  {
    Id = "151690",
    GroupId = 25,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "151700",
    GroupId = 25,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e5fd_29",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151710",
    GroupId = 25,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "151720",
    GroupId = 25,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    }
  },
  {
    Id = "151730",
    GroupId = 25,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_7e5mt_4", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151740",
    GroupId = 25,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    }
  },
  {
    Id = "151750",
    GroupId = 25,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e4friedmt_19",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151760",
    GroupId = 26,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "151770",
    GroupId = 26,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_15e1nice_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151780",
    GroupId = 26,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e6nice_6",
      Count = 1
    }
  },
  {
    Id = "151790",
    GroupId = 26,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5fd_37",
      Count = 1
    }
  },
  {
    Id = "151800",
    GroupId = 26,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151810",
    GroupId = 26,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "151820",
    GroupId = 26,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151830",
    GroupId = 27,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e5mt_12",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151840",
    GroupId = 27,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e1dst_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5fd_33",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151850",
    GroupId = 27,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "151860",
    GroupId = 27,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "151870",
    GroupId = 27,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e1fd_39",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151880",
    GroupId = 27,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_14e4curry_24",
      Count = 1
    }
  },
  {
    Id = "151890",
    GroupId = 27,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5nice_4",
      Count = 1
    }
  },
  {
    Id = "151900",
    GroupId = 28,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e5mt_12",
      Count = 1
    }
  },
  {
    Id = "151910",
    GroupId = 28,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e4nice_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151920",
    GroupId = 28,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e6soup_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    }
  },
  {
    Id = "151930",
    GroupId = 28,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_15e1nice_1",
      Count = 1
    }
  },
  {
    Id = "151940",
    GroupId = 28,
    ChapterId = 15,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e1fd_40",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151950",
    GroupId = 28,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_14e6nibble_13",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151960",
    GroupId = 28,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    }
  },
  {
    Id = "151970",
    GroupId = 29,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_7e6tapas_13",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "151980",
    GroupId = 29,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e4brun_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "151990",
    GroupId = 29,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "152000",
    GroupId = 29,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e1dv_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "152010",
    GroupId = 29,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5maca_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "152020",
    GroupId = 29,
    ChapterId = 15,
    Requirement_1 = {Type = "it_7_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    }
  },
  {
    Id = "152030",
    GroupId = 29,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e3semi_20",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e6nice_6",
      Count = 1
    }
  },
  {
    Id = "152040",
    GroupId = 30,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "152050",
    GroupId = 30,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_15e1cockt_29",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1nutt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "152060",
    GroupId = 30,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_15e5maca_4",
      Count = 1
    }
  },
  {
    Id = "152070",
    GroupId = 30,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_11e4tato_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "152080",
    GroupId = 30,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_15e5dst_27",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "152090",
    GroupId = 30,
    ChapterId = 15,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6dst_16",
      Count = 1
    }
  },
  {
    Id = "152100",
    GroupId = 30,
    ChapterId = 15,
    Requirement_1 = {
      Type = "ds_13e5mt_12",
      Count = 1
    }
  }
}
