ExtraBoardActivityNoticeWindow = setmetatable({}, ExtraBoardActivityBaseWindow)
ExtraBoardActivityNoticeWindow.__index = ExtraBoardActivityNoticeWindow

function ExtraBoardActivityNoticeWindow:Init(activityType, bUser<PERSON>lick)
  ExtraBoardActivityBaseWindow.Init(self, activityType, bUserClick)
  self.m_nextStateTime = self.m_model:GetNextStateTime()
  self:UpdatePerSecond()
  if self.m_activityDefinition.BaseItemTextKey then
    self.m_preDesc1Text.text = GM.GameTextModel:GetText("extraBoard_pre_desc1", self.m_activityDefinition.BaseItemTextKey, self.m_activityDefinition.BaseItemTextKey)
    self.m_preDesc2Text.text = GM.GameTextModel:GetText("extraBoard_pre_desc2", self.m_activityDefinition.BaseItemTextKey, self.m_activityDefinition.BaseItemTextKey)
  end
end

function ExtraBoardActivityNoticeWindow:UpdatePerSecond()
  ExtraBoardActivityBaseWindow.UpdatePerSecond(self)
  if self.m_nextStateTime ~= nil then
    local delta = math.max(0, self.m_nextStateTime - GM.GameModel:GetServerTime())
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end
