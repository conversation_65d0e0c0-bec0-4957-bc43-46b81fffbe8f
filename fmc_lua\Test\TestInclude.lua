require("Test.TestModel")
require("Test.TestAutoRunModel")
require("Test.TestInterface")
require("Test.TestMessage")
require("Test.TestMaskButton")
require("Test.TestWindow")
require("Test.ProfilingWindow")
require("Test.TestLocalBackupWindow")
require("Test.TestServerBackupWindow")
require("Test.TestItemListWindow")
require("Test.TestRemoveItemWindow")
require("Test.TestItemContentCell")
require("Test.TestFavoriteItemContent")
require("Test.TestAllItemContent")
require("Test.TestCodeEditWindow")
require("Test.TestLuaRemoteWindow")
require("Test.TestTutorialInfoWindow")
require("Test.TestPlayerprefsWindow")
require("Test.TestDebugAddressWindow")
require("Test.TestAutoTimelineView")
require("Test.TestLanguageSettingWindow")
require("Test.TestTwoButtonWindow")
require("Test.TestScreenDragView")
require("Test.TestCenterPointView")
require("Test.TestMatchListCell")
require("Test.TestMatchStrList")
require("Test.TestCommandMatchList")
require("Test.TestCommandDefinition")
require("Test.TestCommandView")
require("Test.TestMapBorderToolView")
require("Test.TestAccumulatedLogInfo")
require("Test.TestFileReplaceCell")
require("Test.TestFileReplaceWindow")
require("Test.TestTextWindow")
require("Test.TestServerConfigInfoWindow")
require("Test.TestIAPWindow")
require("Test.TestAutoRunSettingWindow")
require("Test.TestOrderGroupWindow")
require("Test.TestOrderEditCell")
require("Test.TestOrderGroupEditWindow")
require("Test.TestOrderGroupInfoCell")
require("Test.TestOrderInfoCell")
require("Test.TestRequirementEditCell")
require("Test.TestOrderGroupItemListCell")
require("Test.TestOrderEnergyDiffWindow")
require("Test.TestBoardPrompt")
require("Test.TestDigActivityToolWindow")
require("Test.TestAlbumGachaWindow")
require("Test.TestAlbumInfoWindow")
require("Test.TestAlbumSelectedGachaCell")
require("Test.TestAlbumSelectedGachaContent")
