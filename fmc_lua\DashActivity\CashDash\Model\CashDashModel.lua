CashDashModel = setmetatable({}, DashActivityModel)
CashDashModel.__index = CashDashModel

function CashDashModel:Init(virtualDBTable)
  DashActivityModel.Init(self, ActivityType.CashDash, virtualDBTable)
  EventDispatcher.AddListener(EEventType.OrderFinished, self, self._OnOrderFinished)
end

function CashDashModel:_OnOrderFinished(message)
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  local score = message.order:GetScore(true)
  if 0 < score then
    self:AddScore(score)
  end
end
