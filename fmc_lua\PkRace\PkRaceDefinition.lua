PkRaceDefinition = {
  [ActivityType.PkRace] = {
    EntryButtonKey = ESceneViewHudButtonKey.PkRace,
    ActivityDataTableName = VirtualDBTableName.PkRace,
    SignupBIType = EBIType.PkRaceSignup,
    RewardBIType = EBIType.PkRaceReward,
    RankBIType = EBIType.PkRaceRank,
    RoundRewardBIType = EBIType.PkRaceRoundReward,
    StateChangedEvent = EEventType.PkRaceStateChanged,
    RewardWindowPrefabName = UIPrefabConfigName.CoinRaceRewardWindow,
    CompleteWindowPrefabName = UIPrefabConfigName.PkRaceCompleteWindow,
    MainWindowPrefabName = UIPrefabConfigName.PkRaceMainWindow,
    EntryPrefabName = UIPrefabConfigName.PkRaceEntry,
    NoticeWindowPrefabName = UIPrefabConfigName.PkRaceNoticeWindow,
    BoardEntryPrefabName = UIPrefabConfigName.PkRaceBoardEntry,
    HelpWindowPrefabName = UIPrefabConfigName.PkRaceHelpWindow,
    TokenImageName = ImageFileConfigName.pkrace_token_icon,
    ActivityTokenPropertyType = EPropertyType.PkRaceToken,
    TutorialStartCondition = ETutorialStartCondition.PkRaceStart,
    TitleTextKey = "pkrace_title",
    MainTitleTextKey = "pkrace_title",
    ResourceLabels = {
      AddressableLabel.PkRace,
      AddressableLabel.PkRaceCommon
    }
  }
}
for activityType, activityDefinition in pairs(PkRaceDefinition) do
  EPropertySprite[activityDefinition.ActivityTokenPropertyType] = activityDefinition.TokenImageName
  EPropertySpriteBig[activityDefinition.ActivityTokenPropertyType] = activityDefinition.TokenImageName
end
