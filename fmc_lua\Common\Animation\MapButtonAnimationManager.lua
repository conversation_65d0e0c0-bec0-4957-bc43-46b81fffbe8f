MapButtonAnimationManager = {}
MapButtonAnimationManager.__index = MapButtonAnimationManager

function MapButtonAnimationManager:Awake()
  self.m_hardBtnList = {}
  self.m_commonBtnList = {}
  self.m_beatIdx = 1
  EventDispatcher.AddListener(EEventType.IconAnimationCreate, self, self._OnIconAnimationCreate)
  EventDispatcher.AddListener(EEventType.IconAnimationDestroy, self, self._OnIconAnimationDestroy)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnGameModeChanged)
end

function MapButtonAnimationManager:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  Scheduler.UnscheduleTarget(self)
  if self.m_hardBeatSeq ~= nil then
    self.m_hardBeatSeq:Kill()
    self.m_hardBeatSeq = nil
  end
  for _, v in pairs(self.m_commonBtnList) do
    v.seq:Kill()
  end
  self.m_hardBtnList = nil
  self.m_commonBtnList = nil
end

function MapButtonAnimationManager:_OnIconAnimationCreate(msg)
  local btn = msg and msg.btn or nil
  if btn then
    if btn:GetButtonAnimationType() == MapButtonAnimationType.Common then
      self:_AddButtonToCommonList(btn)
    elseif btn:GetButtonAnimationType() == MapButtonAnimationType.Hard then
      self:_AddButtonToHardList(btn)
    end
  end
end

function MapButtonAnimationManager:_OnIconAnimationDestroy(msg)
  self:_RemoveButtonFromList(msg and msg.btn)
end

function MapButtonAnimationManager:_OnGameModeChanged()
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    self:_TryStartHardBeat()
  end
end

function MapButtonAnimationManager:_AddButtonToCommonList(btn)
  for _, v in pairs(self.m_commonBtnList) do
    if v.btn == btn then
      return
    end
  end
  local dt = btn:PlayIconAnimation()
  local beatSeq = DOTween.Sequence()
  beatSeq:AppendInterval(dt + 4)
  beatSeq:AppendCallback(function()
    btn:PlayIconAnimation()
  end)
  beatSeq:SetLoops(-1)
  local button = {}
  button.btn = btn
  button.seq = beatSeq
  table.insert(self.m_commonBtnList, button)
end

function MapButtonAnimationManager:_AddButtonToHardList(btn)
  for _, v in pairs(self.m_hardBtnList) do
    if v.btn == btn then
      return
    end
  end
  local button = {}
  button.btn = btn
  button.priority = btn:GetBeatPriority()
  table.insert(self.m_hardBtnList, button)
  table.sort(self.m_hardBtnList, function(a, b)
    return a.priority < b.priority
  end)
  DelayExecuteFuncInView(function()
    self:_TryStartHardBeat()
  end, 1, self)
end

function MapButtonAnimationManager:_TryStartHardBeat()
  if self.m_hardBeatSeq ~= nil or Table.IsEmpty(self.m_hardBtnList) or GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    return
  end
  self.m_beatIdx = (self.m_beatIdx - 1) % #self.m_hardBtnList + 1
  if not self.m_hardBtnList[self.m_beatIdx] then
    return
  end
  local dt = self.m_hardBtnList[self.m_beatIdx].btn:PlayIconAnimation()
  local interval = #self.m_hardBtnList > 1 and 0.2 or 0
  local beatSeq = DOTween.Sequence()
  beatSeq:AppendInterval(dt + interval)
  beatSeq:AppendCallback(function()
    self.m_hardBeatSeq = nil
    self:_TryStartHardBeat()
  end)
  self.m_hardBeatSeq = beatSeq
  self.m_beatIdx = self.m_beatIdx + 1
end

function MapButtonAnimationManager:_RemoveButtonFromList(btn)
  if btn == nil then
    return
  end
  local i = 1
  while i <= #self.m_hardBtnList do
    if self.m_hardBtnList[i].btn == btn then
      table.remove(self.m_hardBtnList, i)
    else
      i = i + 1
    end
  end
  i = 1
  while i <= #self.m_commonBtnList do
    if self.m_commonBtnList[i].btn == btn then
      self.m_commonBtnList[i].seq:Kill()
      table.remove(self.m_commonBtnList, i)
    else
      i = i + 1
    end
  end
end
