TutorialBoardHelper = {}

function TutorialBoardHelper.Start()
  TutorialBoardHelper.s_boardModel = TutorialBoardModel.Create()
  TutorialBoardHelper.s_forcedData = {TimerDuration = 2, CostEnergy = false}
end

function TutorialBoardHelper.Show()
  GM.ModeViewController:LoadTutorialBoardView(TutorialBoardHelper.s_boardModel)
  while TutorialBoardView.GetInstance() == nil do
    coroutine.yield()
  end
end

function TutorialBoardHelper.End()
  GM.ModeViewController:UnloadTutorialBoardView()
  TutorialBoardHelper.s_boardModel = nil
  TutorialBoardHelper.s_forcedData = nil
end

function TutorialBoardHelper.UpdatePerSecond()
  TutorialBoardHelper.s_boardModel:UpdatePerSecond()
end

function TutorialBoardHelper.CreatePosition(x, y)
  return TutorialBoardModel.CreatePosition(x, y)
end

function TutorialBoardHelper.Wait(duration)
  Coroutine.Wait(duration)
end

function TutorialBoardHelper.AddItem(position, itemCode)
  TutorialBoardHelper.s_boardModel:GenerateItem(position, itemCode)
end

function TutorialBoardHelper.TapItem(position)
  local boardView = TutorialBoardView.GetInstance()
  local localPosition = boardView:ConvertBoardPositionToLocalPosition(position)
  boardView:ShowGestureTap(localPosition)
  Coroutine.Wait(1)
  boardView:HideGestureTap()
  boardView:OnPointerDown(position)
  boardView:OnPointerUp(position)
end

function TutorialBoardHelper.MoveItem(startPosition, endPosition)
  local boardView = TutorialBoardView.GetInstance()
  local startLocalPosition = boardView:ConvertBoardPositionToLocalPosition(startPosition)
  local endLocalPosition = boardView:ConvertBoardPositionToLocalPosition(endPosition)
  boardView:ShowGestureDrag(startLocalPosition)
  boardView:OnPointerDown(startPosition)
  TutorialBoardHelper.Wait(1)
  local onComplete = function()
    boardView:HideGestureDrag()
    boardView:OnPointerUp(endPosition)
  end
  local onUpdate = function(value)
    local localPosition = Vector3.Lerp(startLocalPosition, endLocalPosition, value)
    boardView:ShowGestureDrag(localPosition)
    boardView:OnDrag(localPosition)
  end
  local distance = Vector3.Distance(startLocalPosition, endLocalPosition)
  local tween = DOVirtual.Float(0, 1, distance / 300, onUpdate):OnComplete(onComplete)
  coroutine.yield(tween)
  Coroutine.Wait(1)
end

function TutorialBoardHelper.GetForcedTimerDuration()
  if TutorialBoardHelper.s_forcedData == nil then
    return nil
  end
  return TutorialBoardHelper.s_forcedData.CustomTimerDuration or TutorialBoardHelper.s_forcedData.TimerDuration
end

function TutorialBoardHelper.SetForcedTimerDuration(duration)
  TutorialBoardHelper.s_forcedData.CustomTimerDuration = duration
end

function TutorialBoardHelper.GetForcedCostEnergy()
  if TutorialBoardHelper.s_forcedData == nil then
    return nil
  end
  return TutorialBoardHelper.s_forcedData.CostEnergy
end
