GameTextModel = {}
GameTextModel.__index = GameTextModel

function GameTextModel:Init()
  self.m_mapCacheValue = {}
  self.m_arrSensitives = {}
  self.m_mapChapterText = {}
  self.m_mapSpecialStoryText = {}
  self:LoadCdnText()
end

function GameTextModel:LoadFileConfig()
  GM.ResourceLoader:LoadLatestFile(GM.DataResource.TextAssetConfig:GetConfig(TextAssetConfigName.sensitiveWord), function(data)
    self.m_arrSensitives = StringUtil.Split(data, "\r\n")
    if GameConfig.IsTestMode() then
      for i, _ in ipairs(self.m_arrSensitives) do
        if self.m_arrSensitives[i] ~= string.lower(self.m_arrSensitives[i]) then
          Log.Error("敏感词：" .. tostring(self.m_arrSensitives[i]) .. "包含大写字母，联系傲凌处理")
        end
      end
    end
  end)
end

function GameTextModel:OnCheckResourcesFinished()
  self:LoadChapterText(GM.ChapterManager.curActiveChapterName)
end

function GameTextModel:LoadCdnText()
  local cdnTextFileKey = CSGameTextModel:GetCdnTextFileKey()
  if not GM.CDNResourceManager:IsFileVersionValid(cdnTextFileKey) then
    return
  end
  CSGameTextModel:LoadCdnText()
  self.m_mapCacheValue = {}
end

function GameTextModel:ReloadLocalText()
  CSGameTextModel:ReloadLocalText(function(bSuccess)
    self.m_mapCacheValue = {}
  end)
end

function GameTextModel:GetText(key, ...)
  local params = table.pack(...)
  if params and params.n and params.n > 0 then
    return CSGameTextModel:GetText(key, ...)
  end
  if self.m_mapCacheValue[key] ~= nil then
    return self.m_mapCacheValue[key]
  end
  local value = CSGameTextModel:GetText(key)
  self.m_mapCacheValue[key] = value
  return value
end

function GameTextModel:HasText(key)
  return CSGameTextModel:HasText(key)
end

function GameTextModel:IsSensitiveWord(word)
  local strTrimmed = StringUtil.Trim(word)
  local uLength = #strTrimmed
  local bIsEnglishWord = true
  local char
  for i = 1, uLength do
    char = string.sub(strTrimmed, i, i)
    if char ~= " " and not StringUtil.IsAlpha(char) and not StringUtil.IsDigit(char) then
      bIsEnglishWord = false
      break
    end
  end
  if not bIsEnglishWord then
    strTrimmed = StringUtil.Replace(strTrimmed, " ", "")
  end
  if strTrimmed == "" then
    return false
  end
  strTrimmed = string.lower(strTrimmed)
  local strWord = ""
  for i = 1, #self.m_arrSensitives do
    strWord = self.m_arrSensitives[i]
    if strWord ~= "" and string.find(strTrimmed, strWord, 1, true) then
      return true
    end
  end
  return false
end

function GameTextModel:LoadChapterText(chapterName, onFinish)
  if self.m_mapChapterText[chapterName] then
    if onFinish then
      onFinish(true)
    end
    return
  end
  GM.UIManager:SetEventLock(true)
  local requirePath = "DynamicConfig.Mainline." .. tostring(chapterName) .. ".ChapterText_" .. tostring(chapterName)
  GM.ConfigModel:LoadDynamicConfigAsync(requirePath, function(tb)
    GM.UIManager:SetEventLock(false)
    if not tb then
      GM.BIManager:LogErrorInfo("ctx_ld_er", chapterName)
      CS.CSErrorMonitor.OpenForceRestartWindow("force_restart_desc")
      self.m_mapChapterText[chapterName] = {}
      if onFinish then
        onFinish(false)
      end
      return
    end
    local curTextShortName = LocalizationModel:GetTextShortName()
    self.m_mapChapterText[chapterName] = tb[curTextShortName] or {}
    for k, _ in pairs(tb) do
      tb[k] = nil
    end
    if onFinish then
      onFinish(true)
    end
  end)
end

function GameTextModel:LoadSpecialStoryText(suffix, onFinish)
  if self.m_mapSpecialStoryText[suffix] then
    if onFinish then
      onFinish(true)
    end
    return
  end
  GM.UIManager:SetEventLock(true)
  local requirePath = "DynamicConfig.Special.Text." .. tostring(suffix) .. ".StoryText_" .. tostring(suffix)
  GM.ConfigModel:LoadDynamicConfigAsync(requirePath, function(tb)
    GM.UIManager:SetEventLock(false)
    if not tb then
      GM.BIManager:LogErrorInfo("stx_ld_er", suffix)
      CS.CSErrorMonitor.OpenForceRestartWindow("force_restart_desc")
      self.m_mapChapterText[suffix] = {}
      if onFinish then
        onFinish(false)
      end
      return
    end
    local curTextShortName = LocalizationModel:GetTextShortName()
    self.m_mapSpecialStoryText[suffix] = tb[curTextShortName] or {}
    for k, _ in pairs(tb) do
      tb[k] = nil
    end
    if onFinish then
      onFinish(true)
    end
  end)
end

function GameTextModel:GetChapterText(chapterName, key)
  if GameConfig.IsTestMode() and PlayerPrefs.GetInt(EPlayerPrefKey.TestShowTextKey, 0) == 1 then
    return key
  end
  local mapText = self.m_mapChapterText[chapterName]
  if not mapText then
    mapText = {}
    Log.Error("没有加载章节文案！chapterName:" .. tostring(chapterName))
  end
  return mapText and mapText[key] or key
end

function GameTextModel:GetSpecialText(specialSuffix, key)
  if GameConfig.IsTestMode() and PlayerPrefs.GetInt(EPlayerPrefKey.TestShowTextKey, 0) == 1 then
    return key
  end
  local mapText = self.m_mapSpecialStoryText[specialSuffix]
  if not mapText then
    mapText = {}
    Log.Error("没有加载特殊文案！后缀:" .. tostring(specialSuffix))
  end
  return mapText and mapText[key] or key
end
