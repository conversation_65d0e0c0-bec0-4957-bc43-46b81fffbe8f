CoconutModel = setmetatable({}, DashActivityModel)
CoconutModel.__index = CoconutModel
CoconutModel.TokenType = "balloon"
CoconutModel.DisplayedLevelKey = "displayedLevel"
CoconutModel.DisplayedScoreKey = "displayedScore"
CoconutModel.FirstBalloonGeneratedKey = "firstBalloonGenerated"

function CoconutModel:Init(virtualDBTable)
  DashActivityModel.Init(self, ActivityType.Coconut, virtualDBTable)
end

function CoconutModel:LateInit()
  self.m_lateInited = true
  self:_TryTransformItems()
end

function CoconutModel:_LoadOtherServerConfig(config)
  self.m_config.levelConfigs = {}
  for _, lConfig in ipairs(config[self.m_activityDefinition.RewardConfigName]) do
    local levelConfigItem = {}
    levelConfigItem.score = lConfig.score
    levelConfigItem.skin = lConfig.skin
    levelConfigItem.rewards = ConfigUtil.GetCurrencyFromArrStr(lConfig.rewards)
    self.m_config.levelConfigs[lConfig.level] = levelConfigItem
  end
end

function CoconutModel:_DropData()
  self.m_needTransformItems = true
  self:_TryTransformItems()
  DashActivityModel._DropData(self)
end

function CoconutModel:_OnStateChanged()
  if self:GetState(false) ~= ActivityState.Started then
    self.m_needTransformItems = true
    self:_TryTransformItems()
  end
  DashActivityModel._OnStateChanged(self)
end

function CoconutModel:_AddScore(delta)
  DashActivityModel._AddScore(self, delta)
  GM.BIManager:LogAcquire(CoconutModel.TokenType, delta, EBIType.CoconutGetScore, true)
end

function CoconutModel:Upgrade()
  DashActivityModel.Upgrade(self)
  local level = self:GetLevel()
  local levelConfig = self:GetLevelConfigs()[level]
  if levelConfig == nil then
    self.m_needTransformItems = true
    self:_TryTransformItems()
  end
end

function CoconutModel:GetDisplayedLevelAndScore()
  local displayedLevel = self.m_dbTable:GetValue(CoconutModel.DisplayedLevelKey, "value") or 1
  local displayedScore = self.m_dbTable:GetValue(CoconutModel.DisplayedScoreKey, "value") or 0
  return displayedLevel, displayedScore
end

function CoconutModel:SetDisplayedLevelAndScore()
  self.m_dbTable:Set(CoconutModel.DisplayedLevelKey, "value", self:GetLevel())
  self.m_dbTable:Set(CoconutModel.DisplayedScoreKey, "value", self:GetScore())
end

function CoconutModel:HasFirstBalloonGenerated()
  return self.m_dbTable:GetValue(CoconutModel.FirstBalloonGeneratedKey, "value") == 1
end

function CoconutModel:SetFirstBalloonGenerated()
  self.m_dbTable:Set(CoconutModel.FirstBalloonGeneratedKey, "value", 1)
end

function CoconutModel:_TryTransformItems()
  if not self.m_lateInited or not self.m_needTransformItems then
    return
  end
  self.m_needTransformItems = nil
  local delta = GM.MainBoardModel:TransformItems(ItemChain.Balloon)
  if 0 < delta and self:GetState(false) == ActivityState.Ended then
    self:_AddScore(delta)
  end
end
