ItemDetailWindowMode = {Normal = 1, Order = 2}
EItemDetailWindowRefer = {
  Inventory = "in_itm",
  BoardInfoBar = "bd_if_b",
  ItemDetailWin = "itd_win",
  DishDetailWin = "itg_win",
  Order = "odr_ic",
  Bundle = "iap_rwd",
  Shop = "shop",
  RewardDisplay = "rwd",
  Discovery = "dcv",
  UIBoard = "ui_b",
  AddItemTutorial = "add8_tt"
}
ItemDetailWindow = setmetatable({
  canClickWindowMask = true,
  windowsArray = {}
}, BaseWindow)
ItemDetailWindow.__index = ItemDetailWindow
local CELL_COUNT_PER_ROW = 4

function ItemDetailWindow.Open(itemType, mode, refer)
  if GM.ItemDataModel:IsDishes(itemType) then
    return GM.UIManager:OpenView(UIPrefabConfigName.ItemDishDetailWindow, itemType, mode, refer)
  elseif ItemDetailWindow.ShowDescInfo(itemType) then
    return GM.UIManager:OpenView(UIPrefabConfigName.ItemDescDetailWindow, itemType, mode, refer)
  end
  return GM.UIManager:OpenView(UIPrefabConfigName.ItemDetailWindow, itemType, mode, refer)
end

function ItemDetailWindow.ShowDescInfo(itemType)
  local config = GM.ItemDataModel:GetModelConfig(itemType)
  if config.Category ~= nil and Table.ListContain(config.Category, EItemCategory.RandomBox) then
    return true
  end
  if config.BoosterType ~= nil then
    return true
  end
end

function ItemDetailWindow:UpdateView(itemType)
  self.m_mergeContentGroupTransform.gameObject:RemoveChildren()
  self.m_previousContentGroupTransform.gameObject:RemoveChildren()
  self.m_currentContentGroupTransform.gameObject:RemoveChildren()
  self:_UpdateView(itemType, ItemDetailWindowMode.Normal)
  EventDispatcher.DispatchEvent(EEventType.ItemDetailWindowUpdated)
end

function ItemDetailWindow.CloseAll()
  local arrWindows = Table.ShallowCopy(ItemDetailWindow.windowsArray)
  for _, window in ipairs(arrWindows) do
    window.eCloseAnimType = EViewCloseAnimType.None
    window:Close()
  end
end

function ItemDetailWindow:Init(itemType, mode, refer)
  Log.Assert(not StringUtil.IsNilOrEmpty(refer), "ItemDetailWindow 需要指定refer，见 EItemDetailWindowRefer")
  if #ItemDetailWindow.windowsArray >= 3 then
    ItemDetailWindow.windowsArray[1]:Close()
  end
  table.insert(ItemDetailWindow.windowsArray, self)
  self:LogWindowAction(EBIType.UIActionType.Open, refer, itemType)
  self:_UpdateView(itemType, mode)
end

function ItemDetailWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  Table.ListRemove(ItemDetailWindow.windowsArray, self)
end

function ItemDetailWindow:_UpdateView(itemType, mode)
  self.nextRefer = EItemDetailWindowRefer.ItemDetailWin
  local itemDataModel = GM.ItemDataModel
  local config = itemDataModel:GetModelConfig(itemType)
  self.m_tipGo:SetActive(itemDataModel:IsBox(itemType))
  self.m_arrCells = {}
  local chainId = itemDataModel:GetChainId(itemType)
  local chain = itemDataModel:GetChain(chainId)
  local level = itemDataModel:GetChainLevel(itemType)
  local generators = itemDataModel:GetItemGenerators(itemType)
  self.m_titleText.text = GM.GameTextModel:GetText(ItemNameDefinition.GetName(itemType))
  local minChainLevel = 1
  if level ~= 1 then
    local previousConfig = itemDataModel:GetModelConfig(chain[level - 1])
    if previousConfig.MergedType == nil then
      minChainLevel = level
    end
  end
  local maxChainLevel = #chain
  if level ~= #chain then
    local previousConfig = itemDataModel:GetModelConfig(chain[#chain - 1])
    if previousConfig.MergedType == nil then
      maxChainLevel = #chain - 1
    end
  end
  local maxConfig = itemDataModel:GetModelConfig(chain[maxChainLevel])
  local extraItemType
  if maxConfig ~= nil and maxConfig.MergedType ~= nil then
    extraItemType = ItemUtility.GetInnerCodeAndPrefixByCode(maxConfig.MergedType)
  end
  for index = minChainLevel, maxChainLevel do
    local hasArrow = (index ~= maxChainLevel or extraItemType ~= nil) and index % CELL_COUNT_PER_ROW ~= 0
    local cell = self:_AddCell(chain[index], false, hasArrow, index == level, false, self.m_mergeContentGroupTransform)
    if index == level then
      cell:ShowAsSelected(true)
    end
  end
  if extraItemType ~= nil then
    self:_AddCell(extraItemType, false, false, false, true, self.m_mergeContentGroupTransform)
  end
  if config.CollectRewards ~= nil then
    UIUtil.SetActive(self.m_mergeDescText.gameObject, true)
    self.m_mergeDescText.text = GM.GameTextModel:GetText("item_" .. itemType .. "_desc")
  else
    UIUtil.SetActive(self.m_mergeDescText.gameObject, false)
  end
  if generators ~= nil then
    self.m_previousContentGo:SetActive(true)
    local showItems = {}
    for _, gen in ipairs(generators) do
      local chainId = itemDataModel:GetChainId(gen)
      local chainLevel = itemDataModel:GetChainLevel(gen)
      if itemDataModel:IsKnown(gen) and (showItems[chainId] == nil or chainLevel > showItems[chainId]) then
        showItems[chainId] = chainLevel
      end
    end
    if next(showItems) ~= nil then
      for chainId, chainLevel in pairs(showItems) do
        local itemId = itemDataModel:IsDishes(chainId) and chainId or chainId .. "_" .. chainLevel
        self:_AddCell(itemId, true, false, true, true, self.m_previousContentGroupTransform)
      end
    else
      self:_AddCell(generators[1], true, false, true, true, self.m_previousContentGroupTransform)
    end
  else
    self.m_previousContentGo:SetActive(false)
  end
  if config.GeneratedItems ~= nil and not ExtraBoardActivityModel.IsExtraBoardActivityItem(itemType) then
    self.m_currentContentGo:SetActive(true)
    local currentSpreadData = config.GeneratedItems
    for _, item in ipairs(currentSpreadData) do
      if config.ChoicesOrder ~= nil then
        for i = 1, item.Weight do
          self:_AddCell(item.Code, true, false, true, true, self.m_currentContentGroupTransform)
        end
      else
        self:_AddCell(item.Code, true, false, true, true, self.m_currentContentGroupTransform)
      end
    end
    local transform = config.Transform
    if transform then
      for _, item in ipairs(transform) do
        self:_AddCell(item.Currency, true, false, true, true, self.m_currentContentGroupTransform)
      end
    end
  else
    self.m_currentContentGo:SetActive(false)
  end
  if config.Recipes ~= nil then
    self.m_cookContentGo:SetActive(true)
    self:UpdateCookContent(config.Recipes)
  else
    self.m_cookContentGo:SetActive(false)
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_contentGroup.transform)
  local preferredContentHeight = self.m_contentGroup.preferredHeight + 210
  UIUtil.SetSizeDelta(self.m_contentRect, nil, preferredContentHeight)
  self:_UpdateHotSaleDisplay()
  EventDispatcher.AddListener(EEventType.ShopBuyItemSuccess, self, self._UpdateHotSaleDisplay, true)
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self._UpdateHotSaleDisplay, true)
  if not GM.UIManager:CanShowTestUI() then
    self.m_testText.gameObject:SetActive(false)
  else
    self.m_testText.gameObject:SetActive(true)
    local strInfo = string.format([[
%s
score: %s
gold: %s
]], itemType, GM.ItemDataModel:GetItemScore(itemType), GM.ItemDataModel:GetItemGoldNum(itemType))
    self.m_testText.text = strInfo
  end
end

function ItemDetailWindow:UpdateCookContent(arrRecipe)
  if Table.IsEmpty(arrRecipe) then
    return
  end
  self.m_arrRecipe = arrRecipe
  local cellCount = #arrRecipe
  if not self.m_bInitCookContent then
    self.m_cookLoopGridView:InitGridView(cellCount, function(loopGridView, index, row, column)
      return self:GetCookGridItemByIndex(loopGridView, index)
    end)
  else
    self.m_cookLoopGridView:SetListItemCount(cellCount)
    self.m_cookLoopGridView:RefreshAllShownItem()
  end
  self.m_bInitCookContent = true
end

function ItemDetailWindow:GetCookGridItemByIndex(gridView, index)
  local recipe = self.m_arrRecipe[index + 1]
  if recipe == nil then
    return nil
  end
  local item = gridView:NewListViewItem("ItemDetailCellGridView")
  local tbCell = item.gameObject:GetLuaTable()
  tbCell:Init(recipe.Recipe, true, false, true, true, self)
  return item
end

function ItemDetailWindow:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function ItemDetailWindow:_AddCell(itemType, isProduceCell, hasArrow, forceShow, hasTipButton, parent)
  local cellObj = Object.Instantiate(self.m_itemOriginGo, parent)
  cellObj:SetActive(true)
  local cell = cellObj:GetLuaTable()
  cell:Init(itemType, isProduceCell, hasArrow, forceShow, hasTipButton, self)
  self.m_arrCells[#self.m_arrCells + 1] = cell
  return cell
end

function ItemDetailWindow:_UpdateHotSaleDisplay()
  for _, cell in ipairs(self.m_arrCells) do
    cell:UpdateHotSaleDisplay()
  end
end

function ItemDetailWindow:OnClickTip()
  local titleText = GM.GameTextModel:GetText("reward_info_title")
  local descriptionText = GM.GameTextModel:GetText("reward_info_desc")
  local buttonText = GM.GameTextModel:GetText("reward_info_button")
  GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, titleText, descriptionText, buttonText, function()
    CSPlatform:OpenURL(NetworkConfig.GetProbabilityDisclosureLink())
  end, nil, true)
end
