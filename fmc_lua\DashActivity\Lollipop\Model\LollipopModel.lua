LollipopModel = setmetatable({}, DashActivityModel)
LollipopModel.__index = LollipopModel
LollipopModel.HasOrderFinishedKey = "HasOrderFinished"
LollipopModel.TokenType = "lollipop"
LollipopModel.ActivityType = ActivityType.Lollipop
LollipopModel.TransformChain = ItemChain.Lollipop
LollipopModel.GetScoreType = EBIType.LollipopGetScore

function LollipopModel:Init(virtualDBTable)
  DashActivityModel.Init(self, self.ActivityType, virtualDBTable)
  EventDispatcher.AddListener(EEventType.OrderFinished, self, self._OnOrderFinished)
end

function LollipopModel:LateInit()
  self.m_lateInited = true
  self:_TryTransformItems()
end

function LollipopModel:_DropData()
  self.m_needTransformItems = true
  self:_TryTransformItems()
  DashActivityModel._DropData(self)
end

function LollipopModel:_OnStateChanged()
  if self:GetState(false) ~= ActivityState.Started then
    self.m_needTransformItems = true
    self:_TryTransformItems()
  end
  DashActivityModel._OnStateChanged(self)
end

function LollipopModel:_OnOrderFinished(message)
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return
  end
  if self:GetState() == ActivityState.Started then
    self.m_dbTable:Set(self.HasOrderFinishedKey, "value", 1)
  end
end

function LollipopModel:HasOrderFinished()
  return self.m_dbTable:GetValue(self.HasOrderFinishedKey, "value") == 1
end

function LollipopModel:_AddScore(delta)
  DashActivityModel._AddScore(self, delta)
  GM.BIManager:LogAcquire(self.TokenType, delta, self.GetScoreType, true)
end

function LollipopModel:Upgrade()
  DashActivityModel.Upgrade(self)
  local level = self:GetLevel()
  local levelConfig = self:GetLevelConfigs()[level]
  if levelConfig == nil then
    self.m_needTransformItems = true
    self:_TryTransformItems()
  end
end

function LollipopModel:_TryTransformItems()
  if not self.m_lateInited or not self.m_needTransformItems then
    return
  end
  self.m_needTransformItems = nil
  local delta = GM.MainBoardModel:TransformItems(self.TransformChain)
  if 0 < delta and self:GetState(false) == ActivityState.Ended then
    self:_AddScore(delta)
  end
end
