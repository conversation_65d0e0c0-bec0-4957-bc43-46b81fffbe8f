AlbumActivityGroupIcon = {}
AlbumActivityGroupIcon.__index = AlbumActivityGroupIcon

function AlbumActivityGroupIcon:Init(setIndex, cardSets, model, window)
  self.m_mainWindow = window
  self.m_setIndex = setIndex
  self.m_cardSets = cardSets
  self.m_config = self.m_cardSets[self.m_setIndex]
  self.m_model = model
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  self:InitPackInfo()
  self:UpdateRedPoint()
  AddHandlerAndRecordMap(self.m_model:GetEvent(), AlbumActivityModel.EventKey.GetNewCard, {
    obj = self,
    method = self._UpdateContent
  })
end

function AlbumActivityGroupIcon:InitPackInfo()
  self:_UpdateContent()
  local setId = self.m_config.setId
  self.m_name.text = GM.GameTextModel:GetText(setId .. "_name")
  SpriteUtil.SetImage(self.m_IconImageImg, ImageFileConfigName[setId], false)
end

function AlbumActivityGroupIcon:UpdateRedPoint()
  UIUtil.SetActive(self.m_newGo, not Table.IsEmpty(self.m_model:GetRedPointInfoBySetID(self.m_config.setId)))
end

function AlbumActivityGroupIcon:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
  GM.UIManager:RemoveAllEventLocks(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model:GetEvent(), self)
  end
end

function AlbumActivityGroupIcon:OnButtonClicked()
  GM.UIManager:OpenView(self.m_activityDefinition.PackWindowPrefabName, self.m_setIndex, self.m_cardSets, self.m_model:GetType())
end

function AlbumActivityGroupIcon:GetCanvAlpha()
  return self.m_cardIconCanv
end

function AlbumActivityGroupIcon:GetMiddleRect()
  return self.m_nodeRectTrans
end

function AlbumActivityGroupIcon:_UpdateContent()
  local curSetCard, maxSetCard = self.m_model:GetSetCollectProgress(self.m_config.setId)
  self.m_progressSlider.value = curSetCard / maxSetCard
  self.m_progressText.text = tostring(curSetCard) .. "/" .. tostring(maxSetCard)
  UIUtil.SetActive(self.m_checkGo, maxSetCard <= curSetCard)
  UIUtil.SetActive(self.m_progressGo, curSetCard < maxSetCard)
end

function AlbumActivityGroupIcon:PlayHitAnim()
  self.m_animator:SetTrigger("hit")
end

AlbumActivityMainWindow = setmetatable({}, AlbumActivityBaseWindow)
AlbumActivityMainWindow.__index = AlbumActivityMainWindow

function AlbumActivityMainWindow:Init(activityType, bUserClick, initCallback)
  AlbumActivityBaseWindow.Init(self, activityType, bUserClick)
  self:InitPack()
  self:InitRewards()
  self:_UpdateContent()
  self:UpdateCycleRefresh()
  self:UpdateJokerContent()
  self:PlayIconShowAct()
  AddHandlerAndRecordMap(self.m_model:GetEvent(), AlbumActivityModel.EventKey.CycleRefresh, {
    obj = self,
    method = self.UpdateCycleRefresh
  })
  AddHandlerAndRecordMap(self.m_model:GetEvent(), AlbumActivityModel.EventKey.GetNewCard, {
    obj = self,
    method = self._UpdateContent
  })
  AddHandlerAndRecordMap(self.m_model:GetEvent(), AlbumActivityModel.EventKey.RedRefresh, {
    obj = self,
    method = self.OnRedPointRefresh
  })
  AddHandlerAndRecordMap(self.m_model:GetEvent(), AlbumActivityModel.EventKey.JokerCountRefresh, {
    obj = self,
    method = self.UpdateJokerContent
  })
  if initCallback ~= nil then
    initCallback()
  end
  self:UpdatePerSecond()
  if self.m_model:GetState() == ActivityState.Started then
    self.m_model:SetWindowOpened()
  end
end

function AlbumActivityMainWindow:OnDestroy()
  AlbumActivityBaseWindow.OnDestroy(self)
  if self.m_flySeq ~= nil then
    self.m_flySeq:Kill()
    self.m_flySeq = nil
  end
  if self.m_enterSeq ~= nil then
    self.m_enterSeq:Kill()
    self.m_enterSeq = nil
  end
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model:GetEvent(), self)
  end
end

function AlbumActivityMainWindow:OnHelpBtnClick()
  GM.UIManager:OpenView(self.m_activityDefinition.HelpWindowPrefabName, self.m_model:GetType())
end

function AlbumActivityMainWindow:OnCloseBtnClick()
  self:Close()
end

function AlbumActivityMainWindow:InitPack()
  self.m_cardSets = self.m_model:GetCardSets()
  self.m_cells = {}
  self.m_cells[1] = self.m_cardIconLuaTable
  for index, eleInfo in ipairs(self.m_cardSets) do
    if self.m_cells[index] == nil then
      self.m_cells[index] = Object.Instantiate(self.m_cardIconLuaTable.gameObject, self.m_cardIconLuaTable.transform.parent):GetLuaTable()
    end
    self.m_cells[index]:Init(index, self.m_cardSets, self.m_model, self)
  end
end

function AlbumActivityMainWindow:GetSetCell(index)
  return self.m_cells and self.m_cells[index]
end

function AlbumActivityMainWindow:InitRewards()
  local rewards = self.m_model:GetAlbumConfig().reward
  self.m_rewardContent:Init(rewards)
end

function AlbumActivityMainWindow:_UpdateContent()
  self:UpdateProgress()
end

function AlbumActivityMainWindow:UpdateProgress()
  local curCard, maxCard = self.m_model:GetAlbumCollectProgress()
  self.m_progressSlider.value = curCard / maxCard
  self.m_progressSliderText.text = tostring(curCard) .. "/" .. tostring(maxCard)
end

function AlbumActivityMainWindow:OnRedPointRefresh()
  for _, cell in pairs(self.m_cells) do
    cell:UpdateRedPoint()
  end
end

function AlbumActivityMainWindow:UpdatePerSecond()
  if self.m_model ~= nil and self.m_model:GetState() == ActivityState.Started then
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime())
    if self.m_jokerEndTime ~= nil then
      local delta = math.max(0, self.m_jokerEndTime - GM.GameModel:GetServerTime())
      if delta == 0 then
        self.m_jokerCountdownText.text = GM.GameTextModel:GetText("joker_card_time_expired")
        self:TryPopRemindJokerWindow()
      else
        local str = TimeUtil.ParseTimeDescription(delta, 2, false, false)
        self.m_jokerCountdownText.text = str
      end
    end
  end
end

function AlbumActivityMainWindow:TryPopRemindJokerWindow()
  if self.m_bExpired then
    return
  end
  if GM.UIManager:GetOpenedTopView() == self and not GM.UIManager:IsEventLock() and not GM.UIManager:IsViewExisting(UIPrefabConfigName.AlbumJokerExchangeWindow) and not GM.UIManager:IsViewExisting(UIPrefabConfigName.AlbumRemindUseJokerWindow) and self.m_model:HasExpiredJokerCard() then
    GM.UIManager:OpenView(UIPrefabConfigName.AlbumRemindUseJokerWindow, self.m_activityType)
    self.m_bExpired = true
  end
end

function AlbumActivityMainWindow:PlayIconShowAct()
  for _, cell in pairs(self.m_cells) do
    cell:GetCanvAlpha().alpha = 0
    cell:GetMiddleRect().anchoredPosition = Vector2(0, -25)
  end
  local seq = DOTween.Sequence()
  seq:AppendInterval(0.5)
  for index = 1, #self.m_cells, 3 do
    local curInedex = index
    seq:AppendCallback(function()
      for numindex = 1, 3 do
        if curInedex <= #self.m_cells then
          local cell = self.m_cells[curInedex]
          local act = cell:GetCanvAlpha():DOFade(1, 0.2)
          cell:GetMiddleRect():DOAnchorPos(Vector2(0, 0), 0.2):SetEase(Ease.OutQuad)
          curInedex = curInedex + 1
        end
      end
    end)
    seq:AppendInterval(0.1)
  end
  seq:AppendCallback(function()
    self.m_enterSeq = nil
  end)
  self.m_enterSeq = seq
end

function AlbumActivityMainWindow:GetHelpBtnTransf()
  return self.m_helpBtnRect
end

function AlbumActivityMainWindow:OnBtnRecycleClicked()
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxButtonClick)
  GM.UIManager:OpenView(UIPrefabConfigName.AlbumRecyleWindow, self.m_model:GetType(), true)
end

function AlbumActivityMainWindow:UpdateCycleRefresh()
  if self.m_recycleBtnGo == nil then
    return
  end
  UIUtil.SetActive(self.m_recycleBtnGo, self.m_model:GetCycleCardConfig() ~= nil)
  UIUtil.SetActive(self.m_recycleTipGo, self.m_model:CanShowRecyleRedPoint())
end

function AlbumActivityMainWindow:OnJokerBtnClicked()
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxButtonClick)
  GM.UIManager:OpenView(UIPrefabConfigName.AlbumJokerExchangeWindow, self.m_model:GetType(), true)
end

function AlbumActivityMainWindow:GetRewardAreaRect()
  return self.m_rewardAreaRect
end

function AlbumActivityMainWindow:UpdateJokerContent()
  local jokerCount = self.m_model:GetJokerCount()
  UIUtil.SetActive(self.m_jokerBtnGo, 1 <= jokerCount)
  if jokerCount <= 0 then
    return
  end
  self.m_jokerNumText.text = jokerCount
  local jokerGetTime = self.m_model:GetMinJokerCardTime()
  local nextStateTime = self.m_model:GetNextStateTime()
  self.m_jokerEndTime = jokerGetTime + Sec2Day
  self.m_jokerEndTime = math.min(self.m_jokerEndTime, nextStateTime)
end

function AlbumActivityMainWindow:FlyCard2SetCell(cardGo, cardId, index)
  local cell = self:GetSetCell(index)
  if cell == nil then
    return
  end
  self.m_setScrollRect.verticalNormalizedPosition = self:_GetProgressMiddleVerticalNormalizedPosition(index)
  local go = GameObject.Instantiate(cardGo, self.transform)
  local seq = DOTween.Sequence()
  local rect = go.transform
  GM.UIManager:SetEventLock(true, self)
  seq:AppendInterval(0.2)
  seq:Append(rect:DOMove(cell.transform.position, 0.5):SetEase(Ease.InOutSine))
  seq:Join(rect:DOScale(Vector3(0, 0, 1), 0.5))
  seq:AppendCallback(function()
    cell:PlayHitAnim()
  end)
  seq:AppendInterval(0.4)
  seq:AppendCallback(function()
    self.m_flySeq = nil
    GM.UIManager:SetEventLock(false, self)
    GameObject.Destroy(go)
    local listGroup = self.m_model:GetCardSets()
    GM.UIManager:OpenView(self.m_activityDefinition.PackWindowPrefabName, index, listGroup, self.m_activityType, cardId)
  end)
  self.m_flySeq = seq
end

function AlbumActivityMainWindow:_GetProgressMiddleVerticalNormalizedPosition(index)
  local row = index // 3 + 1
  local cellHeight = self.m_setLayout.cellSize.y + self.m_setLayout.spacing.y
  local targetSliderHeight = (row - 1) * cellHeight + self.m_setLayout.padding.top
  local progressNodeHeight = self.m_packsRect.rect.height
  if targetSliderHeight > progressNodeHeight / 2 then
    local progressContentHeight = self.m_setContentRect.rect.height
    local normalized = (progressContentHeight - targetSliderHeight - progressNodeHeight / 2) / (progressContentHeight - progressNodeHeight)
    return math.max(normalized, 0)
  else
    return 1
  end
end
