local ArrBoxSpriteName = {
  ImageFileConfigName.album_blue_gift,
  ImageFileConfigName.album_purple_gift,
  ImageFileConfigName.album_red_gift
}
AlbumActivityRecyleSimpleButton = setmetatable({}, SimpleButton)
AlbumActivityRecyleSimpleButton.__index = AlbumActivityRecyleSimpleButton

function AlbumActivityRecyleSimpleButton:OnBtnClicked()
  if not self.m_bUIEnabled then
    GM.UIManager:ShowPromptWithKey("album_recycle_tip")
    return
  end
  if self.m_callback then
    self.m_callback()
  end
end

AlbumActivityRecyleBuyCell = {}
AlbumActivityRecyleBuyCell.__index = AlbumActivityRecyleBuyCell

function AlbumActivityRecyleBuyCell:Init(model, window, cfg, index)
  self.m_model = model
  self.m_window = window
  self.m_cfg = cfg
  self.m_index = index
  self.m_gemText.text = self.m_model:GetSkipCycleGem(index)
  self.m_starText.text = tostring(self.m_cfg.star)
  SpriteUtil.SetImage(self.m_BoxImg, ArrBoxSpriteName[index], true)
  SpriteUtil.SetImage(self.m_BoxMaskImg, ArrBoxSpriteName[index] .. "_mask", true)
  self:UpdateContent()
  self.m_model:SetStarShopRedTipsLevel(self.m_model:GetCanExchangeMaxRecycleIndex())
end

function AlbumActivityRecyleBuyCell:UpdateContent()
  local currentCycleInfo = self.m_model:GetCurrentCycleInfo()
  local currentEndTime = currentCycleInfo[tostring(self.m_index)]
  UIUtil.SetActive(self.m_BoxRectTrans.gameObject, true)
  UIUtil.SetActive(self.m_GemRectTrans.gameObject, false)
  UIUtil.SetActive(self.m_starRectTrans.gameObject, false)
  self.m_GreenSimpleButtonLuaTable:SetUIEnabled(true)
  local CurStar = self.m_model:GetSurplusCardStar()
  if currentEndTime == nil or currentEndTime < GM.GameModel:GetServerTime() then
    UIUtil.SetActive(self.m_starRectTrans.gameObject, true)
    UIUtil.SetActive(self.m_TimeText.gameObject, false)
    UIUtil.SetActive(self.m_time_bgRectTrans.gameObject, false)
    if CurStar < self.m_cfg.star then
      self.m_GreenSimpleButtonLuaTable:SetUIEnabled(false)
    end
    UIUtil.SetActive(self.m_BoxMaskImg.gameObject, false)
  else
    UIUtil.SetActive(self.m_time_bgRectTrans.gameObject, true)
    UIUtil.SetActive(self.m_GemRectTrans.gameObject, true)
    UIUtil.SetActive(self.m_TimeText.gameObject, true)
    UIUtil.SetActive(self.m_BoxMaskImg.gameObject, true)
  end
  self:UpdatePerSecond()
end

function AlbumActivityRecyleBuyCell:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  local currentCycleInfo = self.m_model:GetCurrentCycleInfo()
  local currentEndTime = currentCycleInfo[tostring(self.m_index)]
  if currentEndTime == nil or currentEndTime < GM.GameModel:GetServerTime() then
    if self.m_TimeText.gameObject.activeSelf then
      self:UpdateContent()
    end
    return
  end
  self.m_gemText.text = self.m_model:GetSkipCycleGem(self.m_index)
  self.m_TimeText.text = TimeUtil.ParseTimeDescription(currentEndTime - GM.GameModel:GetServerTime())
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_TimeText.transform.parent)
end

function AlbumActivityRecyleBuyCell:OnGiftBox()
  self.m_window:OnGiftBox(self, self.m_cfg, self.m_BoxImg.transform)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxButtonClick)
end

function AlbumActivityRecyleBuyCell:OnBtnClicked()
  local currentCycleInfo = self.m_model:GetCurrentCycleInfo()
  local currentEndTime = currentCycleInfo[tostring(self.m_index)]
  if currentEndTime ~= nil and currentEndTime >= GM.GameModel:GetServerTime() then
    self:BuyGemCdTime()
    return
  end
  local listCards, star = self.m_model:TryBuyCfg(self.m_index)
  if listCards == false then
    return
  end
  GM.UIManager:OpenView(UIPrefabConfigName.AlbumSelectCardWindow, self.m_model:GetType(), listCards, function()
    self:Buy(listCards, star)
  end, star)
end

function AlbumActivityRecyleBuyCell:BuyGemCdTime()
  if self.gameObject:IsNull() then
    return
  end
  local bNum = self.m_model:OnSkipCycleCard(self.m_index)
  if bNum ~= nil and bNum ~= true then
    GM.ShopModel:OnLackOfGem(bNum)
  else
    self.m_window:UpdateContent()
  end
end

function AlbumActivityRecyleBuyCell:Buy(listCards, star)
  if self.gameObject:IsNull() then
    return
  end
  local busccess = self.m_model:BuyRecyleCard(self.m_index, listCards, star)
  if busccess then
    self.m_window:BuySuccess(self.m_cfg, self.m_BoxRectTrans, ArrBoxSpriteName[self.m_index])
    local biMapCard = {}
    for _, card in ipairs(listCards) do
      if biMapCard[card] == nil then
        biMapCard[card] = 0
      end
      biMapCard[card] = biMapCard[card] + 1
    end
  end
end

function AlbumActivityRecyleBuyCell:GetShowRewardTrans()
  return self.m_BoxImg.transform
end

AlbumActivityRecyleWindow = setmetatable({bAutoBI = false}, AlbumActivityBaseWindow)
AlbumActivityRecyleWindow.__index = AlbumActivityRecyleWindow

function AlbumActivityRecyleWindow:Init(ActivityType)
  AlbumActivityBaseWindow.Init(self, ActivityType)
  self:InitList()
  self:UpdateContent()
  local currentCycleInfo = self.m_model:GetCurrentCycleInfo()
  local TimeList = {}
  local starNum = self.m_model:GetSurplusCardStar()
  for i, cfg in ipairs(self.m_configs) do
    table.insert(TimeList, {
      id = i,
      state = self.m_model:GetCycleCardState(i)
    })
  end
  self:LogWindowAction(EBIType.UIActionType.Open, EBIReferType.UserClick, {
    starNum = self.m_model:GetSurplusCardStar(),
    cfg = TimeList
  })
  self.m_rewardTip:Hide()
  UIUtil.SetLocalScale(self.m_titleTextRect, 0, 0)
  self.m_titleTextRect:DOScale(Vector3(1, 1, 1), 0.2):SetDelay(0.5)
end

function AlbumActivityRecyleWindow:OnDestroy()
  AlbumActivityBaseWindow.OnDestroy(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model:GetEvent(), self)
  end
end

function AlbumActivityRecyleWindow:InitList()
  self.m_cells = {}
  self.m_cells[1] = self.m_cell1LuaTable
  self.m_configs = self.m_model:GetCycleCardConfig()
  for i, cfg in ipairs(self.m_configs) do
    if self.m_cells[i] == nil then
      self.m_cells[i] = Object.Instantiate(self.m_cell1LuaTable.gameObject, self.m_cell1LuaTable.transform.parent):GetLuaTable()
    end
    self.m_cells[i]:Init(self.m_model, self, cfg, i)
  end
end

function AlbumActivityRecyleWindow:UpdateContent()
  for i, cfg in ipairs(self.m_configs) do
    self.m_cells[i]:UpdateContent()
  end
  self.m_descText.text = GM.GameTextModel:GetText("album_recycle_starnum", self.m_model:GetSurplusCardStar())
end

function AlbumActivityRecyleWindow:OnGiftBox(cell, cfg, trans)
  self.m_rewardTip:Show(cfg.reward, trans, 0, -50, true)
end

function AlbumActivityRecyleWindow:OnMaskClicked()
  self.m_rewardTip:Hide()
end

function AlbumActivityRecyleWindow:BuySuccess(cfg, boxTrans, imgKey)
  GM.UIManager:OpenView(UIPrefabConfigName.BoxRewardWindow, boxTrans.position, 1, imgKey, cfg.reward, nil, true, function()
    self:UpdateContent()
  end)
end

AlbumActivityCardTable = {}
AlbumActivityCardTable.__index = AlbumActivityCardTable

function AlbumActivityCardTable:Init(cardid1, cardid2, cardid3, model, mapCardsNum)
  local InitCard = function(cardid, luaTable)
    if cardid == nil then
      UIUtil.SetActive(luaTable.gameObject, false)
      return
    end
    UIUtil.SetActive(luaTable.gameObject, true)
    luaTable:Init(cardid, model, false, true, mapCardsNum[cardid] or 1)
  end
  InitCard(cardid1, self.m_Album_madaActivityCardLuaTable)
  InitCard(cardid2, self.m_Album_madaActivityCard_1LuaTable)
  InitCard(cardid3, self.m_Album_madaActivityCard_2LuaTable)
end

AlbumActivitySelectCardWindow = setmetatable({}, AlbumActivityBaseWindow)
AlbumActivitySelectCardWindow.__index = AlbumActivitySelectCardWindow

function AlbumActivitySelectCardWindow:Init(activityType, listCard, callBack, star)
  AlbumActivityBaseWindow.Init(self, activityType)
  self.m_mapCards = {}
  self.m_listCards = {}
  for i, card in ipairs(listCard) do
    if self.m_mapCards[card] == nil then
      self.m_mapCards[card] = 0
      table.insert(self.m_listCards, card)
    end
    self.m_mapCards[card] = self.m_mapCards[card] + 1
  end
  self.m_desc2Text.text = GM.GameTextModel:GetText("album_recycle_confirm_desc_2", #listCard, star)
  local initParam = ListViewParam.CopyDefaultInitParam()
  initParam.mItemDefaultWithPaddingSize = 390
  self.m_CardListLoop:InitListView(math.ceil(#self.m_listCards / 3), function(listView, cellIndex)
    return self:GetListItemByIndex(listView, cellIndex)
  end, initParam)
  self.m_callBack = callBack
  UIUtil.SetLocalScale(self.m_titleTextRect, 0, 0)
  self.m_titleTextRect:DOScale(Vector3(1, 1, 1), 0.2):SetDelay(0.5)
end

function AlbumActivitySelectCardWindow:GetListItemByIndex(listView, cellIndex)
  if cellIndex < 0 then
    return nil
  end
  local strCardID = self.m_listCards[cellIndex * 3 + 1]
  local strCardID1 = self.m_listCards[cellIndex * 3 + 2]
  local strCardID2 = self.m_listCards[cellIndex * 3 + 3]
  local strType = self.m_model:GetType():gsub("^%l", string.upper)
  local tableName = "AlbumActivityCard_table"
  local item = listView:NewListViewItem(tableName)
  local luaTable = item.gameObject:GetLuaTable()
  luaTable:Init(strCardID, strCardID1, strCardID2, self.m_model, self.m_mapCards)
  return item
end

function AlbumActivitySelectCardWindow:OnDestroy()
  AlbumActivityBaseWindow.OnDestroy(self)
end

function AlbumActivitySelectCardWindow:OnBtnOk()
  self:Close()
  if self.m_callBack then
    self.m_callBack()
  end
end
