EBIType = {
  Test = "test",
  LevelUp = "lvl_up",
  BuyInventorySlot = "new_inv",
  ItemCollect = "itm_c",
  SpreadItem = "spr_itm",
  MergeItem = "mg_itm",
  CostEnergy = "c_eng",
  ClickProducerItem = "c_p_i",
  ShopItem = "sp_itm",
  BuyBubble = "by_bud",
  ItemSell = "itm_s",
  ItemSellDone = "itm_sd",
  UndoSellItem = "ud_s_itm",
  SpeedUp = "spe_up",
  SpeedUpCook = "spe_up_ck",
  UndoCook = "ud_ck",
  BreakBubble = "bre_bub",
  BreakCobweb = "bre_cob",
  BreakRewardBubble = "bre_rb",
  FinishOrder = "fin_ord",
  ForceUpdate = "frc_upd",
  SceneDuration = "sce_dur",
  MoveItem = "m_itm",
  ChangeName = "cg_nm",
  ChangeIcon = "cg_icn",
  EnergyRestore = "eng_rto",
  CacheItem = "cac_itm",
  PopCacheItem = "p_cac_itm",
  StoreItem = "st_itm",
  RetrieveItem = "ret_itm",
  ItemUnlock = "itm_ul",
  ItemUnlockClaimReward = "itm_ulr",
  ItemSwapFailed = "itm_sf",
  AppTransfer = "app_trans",
  RemoveBubble = "rm_bub",
  BubbleDisappear = "bub_dap",
  CreateNewUser = "cr_n_u",
  DeleteItemConfirmOkClick = "dlt_itm_ok",
  OpenItem = "op_itm",
  ItemCD = "itm_cd",
  BoardFull = "bd_ful",
  ItemRestNumberOverflowError = "itm_r_n_o_err",
  StorageRestNumberOverflowError = "sto_r_n_o_err",
  OrderGroup = "ord_grp",
  CookAddMaterial = "in_eq",
  CookPutbackMaterial = "out_eq",
  TaskFinish = "tsk_fin",
  SpecialTaskBegin = "sp_tsk_b",
  SpecialTaskComplete = "sp_tsk_c",
  TaskProgressComplete = "tsk_pro",
  RoomComplete = "rm_done",
  TaskGroup = "tsk_grp",
  ChapterUnlock = "c_ul",
  GenerateCleanTask = "mk_clean",
  ShopBuy = "shop_buy",
  ShopRefresh = "shop_ref",
  BuyEnergy = "b_eng",
  FreeRefillEnergy = "f_rf_eng",
  TriggerEnergyDiscount = "t_eng_d",
  AddInfiniteEnergy = "a_i_eng",
  AddInfiniteEnergyError = "a_i_eng_e",
  StartIAPWhenEnergyDiscount = "sp_eng_d",
  FinishIAPWhenEnergyDiscount = "fp_eng_d",
  BundleBuy = "bdl_buy",
  BundleFree = "bdl_fr",
  ChainRestoreAfterFinish = "b_c_rto",
  GrowthFundAcqurie = "grf_a",
  GrowthFundClaimReward = "grf_c",
  BundleCollecGroupInfo = "bdl_c_gi",
  IAP = "iap",
  StartPurchase = "st_iap",
  CancelPurchase = "cl_iap",
  SuccessPurchase = "ok_iap",
  RestorePurchase = "rt_iap",
  QueryPurchase = "q_iap",
  QueryPurchaseS = "qs_iap",
  FP_QueryFaild = "fp_q",
  FP_NoPid = "fp_npid",
  FP_InvalidPid = "fp_xpid",
  FP_UnexpectedType = "fp_utyp",
  FP_CallbackFailed = "fp_cbf",
  FP_NoNetwork = "fp_net",
  FP_RewardAlready = "fp_rard",
  FP_VerifyPending = "fp_vp",
  FP_VerifyFailed = "fp_vf",
  NewGoogleIAPInfo = "g_iap_info",
  Restore = "rto",
  UserRestore = "u_rto",
  SceneLogin = "s_lgn",
  Survey = "svy",
  NetworkCheckAction = {
    StartSSO = "sso",
    SSOSuccess = "sso_ok",
    SSOFailed = "sso_f",
    StartLogin = "login",
    LoginSuccess = "lgn_ok",
    LoginFailed = "lgn_f",
    OperLogin = "ctrl",
    OperLoginSuccess = "ctrl_ok",
    OperLoginFailed = "ctrl_f",
    StartCDNDownload = "cdn",
    CDNDownloadSuccess = "cdn_ok",
    CDNDownloadFailed = "cdn_f",
    StartSyncDownloadData = "syn_dl",
    SyncDownloadDataSuccess = "syn_dl_s",
    SyncDownloadDataFailed = "syn_dl_f",
    StartSyncUploadData = "syn_ul",
    SyncUploadDataSuccess = "syn_ul_s",
    SyncUploadDataFailed = "syn_ul_f",
    StartRaceEntry = "cr_e",
    RaceEntrySuccess = "cr_e_s",
    RaceEntryFailed = "cr_e_f",
    StartBakeOut = "bk_o",
    BakeOutSuccess = "bk_o_s",
    BakeOutFailed = "bk_o_f",
    CheckResponse = "check_resp",
    PayFailedWithRcode60 = "pay_f_r60",
    PaySuccessAfterResendUnderRcode60 = "pay_s_r60",
    LoadingFinish = "ld_fin",
    CanInteract = "act",
    UpdateVersionFromWindow = "upd_f_w",
    ValidRestart = "vl_rs"
  },
  OperCallbackError = "op_cbe",
  SyncModel = "syc_m",
  SyncModelAction = {
    LoginForceSync = "l_f_s",
    LoginConflictExist = "l_c_e",
    LoginSyncTimeDiffer = "l_s_d",
    LoginDeviceIdDiffer = "l_d_d",
    LoginDeviceModelDiffer = "l_dm_d",
    DownloadFail = "d_f",
    DownloadSuccess = "d_s",
    DownloadSuccessTempSave = "d_s_s",
    UseServerData = "u_s_d",
    Clear = "c",
    TurnInconsistent = "t_i",
    TurnConsistent = "t_c"
  },
  SyncUploadError = "upd_err",
  SyncDownloadError = "dl_err",
  SkipStory = "sk_s",
  StartStory = "sk_st",
  StoryDuration = "r_st",
  UIActionType = {
    Open = "op",
    Close = "cl",
    Click = "cli"
  },
  RemoveBubbleConfirmClick = "r_b_c_c",
  SetMusic = "set_music",
  SetSound = "set_sound",
  SetVibration = "set_vibt",
  ClickOrderBubble = "c_o_b",
  AppInstalledInfo = "app_ins_info",
  SwitchRoom = "sw_room",
  SkipTimelineBuild = "sk_a",
  LoadPrefabError = "l_pfb_err",
  LoadSpriteError = "l_sp_err",
  CashDashGetRewards = "cd_g_r",
  LollipopGetScore = "l_g_s",
  LollipopGetRewards = "l_g_r",
  CoconutGetScore = "c_g_s",
  CoconutGetRewards = "c_g_r",
  PinataGetScore = "p_g_s",
  PinataGetRewards = "p_g_r",
  PinataGetExtraRewards = "p_g_e_r",
  RateConfirm = "r_cf",
  SceneBind = "bind",
  SceneBindAction = {
    BindSuccess = "b_s",
    BindFailed = "b_f",
    BindFailedVersion = "b_f_v"
  },
  SceneDataConflict = "dt_cfl",
  SceneDataConflictAction = {
    Show = "show",
    SameAccountUseLocal = "s_l",
    SameAccountUseServer = "s_s",
    DifferentAccountUseLocal = "d_l",
    DifferentAccountUseServer = "d_s",
    DifferentAccountCancel = "d_c"
  },
  BindReward = "bind_r",
  DataCracked = "d_ck",
  CrossPromotionInstalldTarget = "cp_ind_tar",
  CrossPromotionTaskFinish = "cp_task_f",
  CrossPromotionTaskReward = "cp_task_rwd",
  CrossPromotionPlayAction = {Down = "cp_down", PullUp = "cp_pull_up"},
  MoreGamePlayAction = {Down = "mg_down", PullUp = "mg_pull_up"},
  CrossPromotionBindSource = "cp_b_s",
  CPBindSourceAction = {
    URL = "url",
    TrackerName = "trk_nm",
    Both = "both"
  },
  TaskEntityError = "te_err",
  AdStart = "ad_st",
  AdFailed = "ad_f",
  AdSuccess = "ad_s",
  AdEarned = "ad_earn",
  AdCompensate = "ad_m",
  AdFailedCompensate = "ad_f_m",
  AdLoadedNetwork = "ad_ln",
  BakeOutExchangeCoin = "bo_e_c",
  BakeOutGetRewards = "bo_g_r",
  BakeOutGetRewardsError = "bo_g_r_e",
  BakeOutConfigError = "bo_c_e",
  BakeOutRandomDelayTime = "bo_r_d_t",
  BakeOutGetBakeOutRanks = "bo_g_rk",
  BattlePassVIPTaskProgress = "bp_vt_p",
  BattlePassExtraRankUp = "bp_er_up",
  BPVIPAcquireToken = "bp_vip_at",
  BP1TakeReward = "bp1_t_r",
  BP1TakeExtraReward = "bp1_t_er",
  BP1TakeActTaskReward = "bp1_a_r",
  BP2TakeReward = "nbp2_t_r",
  BP2TakeExtraReward = "nbp2_t_er",
  BP2TakeActTaskReward = "nbp2_a_r",
  BP3TakeReward = "nbp3_t_r",
  BP3TakeExtraReward = "nbp3_t_er",
  BP3TakeActTaskReward = "nbp3_a_r",
  BP4TakeReward = "nbp4_t_r",
  BP4TakeExtraReward = "nbp4_t_er",
  BP4TakeActTaskReward = "nbp4_a_r",
  BP5TakeReward = "nbp5_t_r",
  BP5TakeExtraReward = "nbp5_t_er",
  BP5TakeActTaskReward = "nbp5_a_r",
  BP6TakeReward = "nbp6_t_r",
  BP6TakeExtraReward = "nbp6_t_er",
  BP6TakeActTaskReward = "nbp6_a_r",
  BP7TakeReward = "nbp7_t_r",
  BP7TakeExtraReward = "nbp7_t_er",
  BP7TakeActTaskReward = "nbp7_a_r",
  BP8TakeReward = "nbp8_t_r",
  BP8TakeExtraReward = "nbp8_t_er",
  BP8TakeActTaskReward = "nbp8_a_r",
  TreasureDigGetScore = "td_g_s",
  TreasureDigGetTutorialScore = "td_g_ts",
  TreasureDigUseScore = "td_u_s",
  TreasureDigGetRewards = "td_g_r",
  TreasureDigGridAction = "td_g_a",
  TreasureDigRandomSelectLevel = "td_rs_l",
  ActivityStarted = "act_st",
  ActivityRankUp = "act_r_up",
  ActivityGetRewards = "act_getr",
  ActivityAddScore = "act_adds",
  ActivityScoreChanged = "act_s_c_t",
  CustomerServiceReward = "cs_rwd",
  NoticeReward = "not_rwd",
  ReadNotice = "rd_not",
  ClickURL = "ck_url",
  SlotChangeAniError = "slot_cg_e",
  SetRoleError = "st_rl_e",
  PlayStartVideoError = "p_s_v_e",
  CloudJsonDecodeError = "c_j_d_e",
  BPTicketRebuy = "bpt_rb_e",
  FinishOrderRemoveItemError = "fo_ri_e",
  SetNoticeAction = {Open = "sn_op", Close = "sn_cl"},
  NotificationState = "n_s",
  CoinRaceSignup = "cr_sup",
  CoinRaceReward = "cr_r",
  CoinRaceRank = "cr_rk",
  PkRaceSignup = "pkr_sup",
  PkRaceReward = "pkr_r",
  PkRaceRank = "pkr_rk",
  PkRaceRoundReward = "pkr_rr",
  ProgressActivity1GetRewards = "pg_g_r_1",
  ProgressActivity1GetScore = "pg_g_s_1",
  EventLevelReward = "el_r",
  EventLevelGetRewardLevel = "el_gr_l",
  EventLevelUp = "el_u",
  EventLevelAddScore = "el_as",
  SpreeLevelReward = "sl_r",
  SpreeLevelGetRewardLevel = "sl_gr_l",
  SpreeLevelUp = "sl_u",
  SpreeLevelAddScore = "sl_as",
  SpreeIAPSuccessAfterActivityEnded = "sl_s_ae",
  NewSkinTransf = "ns_tsf",
  BreakEggStart = "be_s",
  BreakEggStep = "be_st",
  BreakEggRevive = "be_r",
  BreakEggReward = "be_rwd",
  PiggyBankBuy = "pb_b",
  ItemNotFound = "itm_n_f",
  ErrorOrderRemove = "e_ord_rm",
  EnergyBoostSwitchUserOn = "e_b_s_o",
  TimeLimitedEnergyBoostTriggered = "e_b_tl",
  TimeLimitedEnergyBoostQuadTriggered = "e_b_tl_4",
  TimelineStep = "tl_stp",
  DailyTaskRewards = "d_t_r",
  DailyTaskFinalRewards = "d_t_f_r",
  ReturnUserGetReward = "ru_g_r",
  ExtraBoardTutorialCacheItem = "eb_tci",
  ExtraBoardRewardRecover = "eb_rr",
  ExtraBoardMaxLevelReward = "eb_mlr",
  ExtraBoardFinishCobwebRound = "eb_fcr",
  ShowBookFinalReward = "s_bfr",
  BookFinalReward = "fin_br",
  BookSeriesReward = "br_",
  BookItemReward = "it_br",
  TriggerSurpriseChest = "t_s_c",
  AcquireChestRewards = "a_c_r",
  BlindChest1GetFreeKey = "bc1_fk",
  BlindChest1ObtainKey = "bc1_ok",
  BlindChest1TakeReward = "bc1_r",
  BlindChest1TakeTopReward = "bc1_tr",
  BlindChest1TakeFinalReward = "bc1_fr",
  AlbumAcquirePackCard = "al_apc",
  AlbumAcquireJokerCard = "al_ajc",
  AlbumSetRewards = "al_sr",
  AlbumRewards = "al_r",
  AlbumStarRewards = "al_ar",
  AlbumOpenPack = "al_op",
  AlbumExchangeStar = "al_es",
  AlbumUseJoker = "al_uj",
  AlbumPurchaseCD = "al_pcd",
  CurrencyError = "cuy_err",
  ResourceLoaderError = "rsl_err",
  UpgradeSupplement = "ug_spmt",
  ItemRecycle = "itm_rcc",
  ItemDelete = "itm_dlt",
  GoldBalance = "gd_bl",
  DataBalanceInfo = "dt_bl_i",
  DataBalanceEffect = "dt_bl_e",
  StartFlambeMode = "in_flb",
  StartFlambeLink = "in_flb_l"
}
if GameConfig.IsTestMode() then
  setmetatable(EBIType, {
    __index = function(_, key)
      Log.Error("EBIType try to index a nil key: " .. tostring(key))
      return nil
    end
  })
end
EBISyncKey = {
  GameDuration = "GameDuration",
  ConsumedFreeGem = "ConsumedFreeGem",
  ConsumedPaidGem = "ConsumedPaidGem",
  ConsumedEnergy = "ConsumedEnergy"
}
EBITaskType = {Mainline = "main"}
EBIOrderAction = {OrderUnlock = "unlock", OrderFinish = "finish"}
EBIConsumerType = {
  BakeOut = "bakeout",
  BreakEgg = "breakegg",
  ShopRefresh = "shopref",
  Energy = "energy",
  SpreeShop = "spreeshop"
}
EBIProjectType = {
  SQLite = "sqlite",
  BundleBuyFailed = "bd_b_f",
  DBColumnOverflow = "db_c_of",
  ElementNoViewWhenRemove = "e_nv_r",
  ElementNoViewWhenUpdate = "e_nv_u",
  ElementOverlapLogic = "e_ol_l",
  ElementOverlapView = "e_ol_v",
  RestartGame = "rs_g",
  RestartGameAction = {
    Test = -1,
    FileReplaceChanged = 1,
    DownloadRequiredResource = 2,
    UpdateHint = 3,
    CDN = 4,
    SSO = 5,
    AccountSelect = 6,
    DeleteAccountAlready = 7,
    DeleteAccountSuccess = 8,
    UseServerData = 9,
    Language = 10,
    UnlockBorder = 11,
    ForceUnlockBorder = 12
  },
  DeviceCopy = "dv_cp",
  ItemChoose = "i_ch",
  ItemLeak = "it_lk",
  AcquireCleanGold = "acq_cl_gd",
  ConsumeCleanGold = "csm_cl_gd",
  BigDataErrorMonitor = "bd_e_m",
  BigDataErrorMonitorAction = {TooManyError = -1, VersionError = -2},
  TryQuickStartTask = "qk_st_t",
  TryDragStartTask = "dg_st_t",
  ClickTaskBubble = "clk_t_bb",
  ClickTaskNode = "clk_t_nd",
  SkipHotfixRestart = "skip_h_rt"
}
