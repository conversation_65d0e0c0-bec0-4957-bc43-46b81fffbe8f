ECommand = {
  OpenUI = {
    Desc = "打开界面",
    Cmd = "openui",
    AddSearchListFunc = function(self)
      local list = {}
      for _, UIName in pairs(UIPrefabConfigName) do
        if StringUtil.EndWith(UIName, "Window") or StringUtil.EndWith(UIName, "View") then
          list[#list + 1] = self.Cmd .. " " .. UIName
        end
      end
      return list
    end,
    ExecuteFunc = function(self, params)
      if not next(params) then
        GM.UIManager:ShowPrompt("请输入窗口名称")
        return false
      end
      GM.UIManager:OpenView(params[1], 1 < #params and table.unpack(params, 2, #params) or nil)
      return true
    end
  },
  AddProp = {
    Desc = "添加资产",
    Cmd = "addprop",
    AddSearchListFunc = function(self)
      local list = {}
      for _, propType in pairs(EPropertyType) do
        list[#list + 1] = self.Cmd .. " " .. propType
      end
      return list
    end,
    ExecuteFunc = function(self, params)
      if #params ~= 2 then
        GM.UIManager:ShowPrompt("请输入正确格式，addprop 类型 数量")
        return false
      end
      if tonumber(params[2]) == nil then
        GM.UIManager:ShowPrompt("请输入正确数量")
        return false
      end
      local reward = {
        [PROPERTY_TYPE] = params[1],
        [PROPERTY_COUNT] = tonumber(params[2])
      }
      GM.PropertyDataManager:AcquireWithCollectAnimation({reward}, EPropertySource.Give, nil, nil, EBIType.Test)
      return true
    end
  },
  AddGold = {
    Desc = "添加金币",
    Cmd = "addgold",
    ExecuteFunc = function(self, params)
      if tonumber(params[1]) == nil then
        GM.UIManager:ShowPrompt("请输入正确数量")
        return false
      end
      local reward = {
        [PROPERTY_TYPE] = EPropertyType.Gold,
        [PROPERTY_COUNT] = tonumber(params[2])
      }
      GM.PropertyDataManager:AcquireWithCollectAnimation({reward}, EPropertySource.Give, nil, nil, EBIType.Test)
      return true
    end
  },
  AddGem = {
    Desc = "添加钻石",
    Cmd = "addgem",
    ExecuteFunc = function(self, params)
      if tonumber(params[1]) == nil then
        GM.UIManager:ShowPrompt("请输入正确数量")
        return false
      end
      local reward = {
        [PROPERTY_TYPE] = EPropertyType.Gem,
        [PROPERTY_COUNT] = tonumber(params[2])
      }
      GM.PropertyDataManager:AcquireWithCollectAnimation({reward}, EPropertySource.Give, nil, nil, EBIType.Test)
      return true
    end
  },
  AddEventToken = {
    Desc = "添加副本代币",
    Cmd = "addacttoken",
    ExecuteFunc = function(self, params)
      if tonumber(params[1]) == nil then
        GM.UIManager:ShowPrompt("请输入正确数量")
        return false
      end
      return true
    end
  },
  FinishTask = {
    Desc = "设置当前任务",
    Cmd = "task",
    AddSearchListFunc = function(self)
      local list = {}
      local mainTaskConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.MainTask)
      for _, data in ipairs(mainTaskConfig) do
        list[#list + 1] = self.Cmd .. " " .. data.day .. "_" .. data.id
      end
      return list
    end,
    ExecuteFunc = function(self, params)
      if #params ~= 1 then
        GM.UIManager:ShowPrompt("请输入正确格式，task 任务id")
        return false
      end
      local taskData = TestWindow.GetMainTaskData(params[1])
      if taskData ~= nil then
        TestWindow.FinishToTask(taskData)
        GM:RestartGame(nil, EBIProjectType.RestartGameAction.Test)
      else
        GM.UIManager:ShowPrompt("任务不存在")
        return false
      end
      return true
    end
  },
  RestartGame = {
    Desc = "重启游戏",
    Cmd = "restart",
    ExecuteFunc = function(self, params)
      GM:RestartGame(nil, EBIProjectType.RestartGameAction.Test)
      return true
    end
  },
  TestCode = {
    Desc = "执行测试代码窗口",
    Cmd = "code",
    ExecuteFunc = function()
      GM.UIManager:OpenView(UIPrefabConfigName.TestCodeEditWindow)
      return true
    end
  },
  ClearItems = {
    Desc = "清空棋子",
    Cmd = "itemclear",
    ExecuteFunc = function()
      local currentBoardModel = BoardModelHelper.GetActiveModel()
      if currentBoardModel == nil then
        GM.UIManager:ShowPrompt("当前不在棋盘场景")
        return false
      end
      GM.TestModel:RemoveAll()
      return true
    end
  },
  ClearCache = {
    Desc = "清空棋子队列",
    Cmd = "itemcacheclear",
    ExecuteFunc = function()
      local currentBoardModel = BoardModelHelper.GetActiveModel()
      if currentBoardModel == nil then
        GM.UIManager:ShowPrompt("当前不在棋盘场景")
        return false
      end
      GM.TestModel:RemoveCachedQueue()
      return true
    end
  },
  AddOrderItem = {
    Desc = "添加订单棋子",
    Cmd = "itemadd",
    ExecuteFunc = function()
      local boardModel = BoardModelHelper.GetActiveModel()
      if boardModel == nil then
        GM.UIManager:ShowPrompt("当前不在棋盘场景")
        return false
      end
      if TestAllItemContent.GetInstance() ~= nil then
        GM.UIManager:ShowPrompt("已经打开")
        return
      end
      GM.TestModel:OpenAllItemContent()
      return true
    end
  },
  TutorialWindow = {
    Desc = "引导信息界面",
    Cmd = "tutorial",
    ExecuteFunc = function()
      GM.UIManager:OpenView(UIPrefabConfigName.TestTutorialInfoWindow)
      return true
    end
  },
  TimeScale = {
    Desc = "设置游戏速度",
    Cmd = "timescale",
    ExecuteFunc = function(self, params)
      if #params ~= 1 then
        GM.UIManager:ShowPrompt("请输入正确格式")
        return false
      end
      local scale = tonumber(params[1])
      CSTime.timeScale = scale
      return true
    end
  }
}
