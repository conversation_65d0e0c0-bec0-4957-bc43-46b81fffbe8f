DailyTaskMainWindow = setmetatable({disableEffectWhenCloseView = true}, BaseWindow)
DailyTaskMainWindow.__index = DailyTaskMainWindow

function DailyTaskMainWindow:BeforeOpenCheck()
  return GM.ActivityManager:GetModel(ActivityType.DailyTask):IsActivityOpen()
end

function DailyTaskMainWindow:Init()
  self.m_model = GM.ActivityManager:GetModel(ActivityType.DailyTask)
  self.m_rewardItem:Init(self.m_model:GetFinalRewards()[1])
  self.m_lastTime = self.m_model:GetCurRoundEndTime()
  local totalTask = self.m_model:GetTaskCount()
  local finishedTask = self.m_model:GetFinishedTaskCount()
  self.m_slider.value = finishedTask / totalTask
  self.m_sliderText.text = finishedTask .. "/" .. totalTask
  local seqIndex = {}
  for i = 1, totalTask do
    seqIndex[#seqIndex + 1] = i
  end
  table.sort(seqIndex, function(a, b)
    if self.m_model:HasFinishedTask(a) == self.m_model:HasFinishedTask(b) then
      return a < b
    else
      return self.m_model:HasFinishedTask(b)
    end
  end)
  local obj, cell
  for i = 1, totalTask do
    obj = Object.Instantiate(self.m_cellOrigin, self.m_contentRectTrans)
    cell = obj:GetLuaTable()
    cell:Init(self.m_model:GetTaskData(seqIndex[i]))
  end
  UIUtil.UpdateSortingOrder(self.m_effectGo, self:GetSortingOrder())
  self.m_rewardItemCanvas.sortingOrder = self:GetSortingOrder() + 1
  self:UpdatePerSecond()
  EventDispatcher.AddListener(EEventType.DailyTaskStateChanged, self, self.Close)
  self:LogWindowAction(EBIType.UIActionType.Open, EBIReferType.UserClick, self.m_model:GetId())
end

function DailyTaskMainWindow:UpdatePerSecond()
  if self.m_model ~= nil and self.m_lastTime ~= nil then
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(math.max(0, self.m_lastTime - GM.GameModel:GetServerTime()), 2, false, false)
  end
end
