SimpleStringDict = {}
SimpleStringDict.__index = SimpleStringDict
local CSUnencryptedPersistentDict = CS.UnencryptedPersistentDict

function SimpleStringDict.Create(fileName)
  local instance = setmetatable({}, SimpleStringDict)
  instance:_Init(fileName)
  return instance
end

function SimpleStringDict:_Init(fileName)
  self.fileName = fileName
  self.m_dataDict = CSUnencryptedPersistentDict.Create(fileName)
  self.m_mapCacheValue = {}
end

function SimpleStringDict:GetFileSize()
  if self.m_dataDict then
    return self.m_dataDict:GetFileSize()
  else
    return 0
  end
end

function SimpleStringDict:Serialize()
  if not self.m_LuaProfiler then
  end
  if self.m_bDirty then
    self.m_dataDict:Serialize()
    self.m_bDirty = false
  else
  end
end

function SimpleStringDict:HasKey(key)
  key = tostring(key)
  if self.m_mapCacheValue[key] ~= nil then
    return true
  end
  return self.m_dataDict:<PERSON><PERSON><PERSON>(key)
end

function SimpleStringDict:Get(key)
  key = tostring(key)
  if self.m_mapCacheValue[key] ~= nil then
    return self.m_mapCacheValue[key]
  end
  local value = self.m_dataDict:Get(key)
  self.m_mapCacheValue[key] = value
  return value
end

function SimpleStringDict:GetInNumber(key)
  local value = self:Get(key)
  return tonumber(value) or 0
end

function SimpleStringDict:GetInTable(key)
  local value = self:Get(key)
  if value then
    local table = json.decode(value)
    return Table.DeepCopy(table, true)
  else
    return nil
  end
end

function SimpleStringDict:GetAllInTable()
  local data = self.m_dataDict:GetDataDict()
  for k, v in pairs(data) do
    self.m_mapCacheValue[k] = v
  end
  return Table.DeepCopy(self.m_mapCacheValue, true)
end

function SimpleStringDict:GetContent()
  local data = self.m_dataDict:GetDataDict()
  for k, v in pairs(data) do
    self.m_mapCacheValue[k] = v
  end
  return self.m_mapCacheValue
end

function SimpleStringDict:Set(key, value)
  key = tostring(key)
  value = tostring(value)
  if self.m_mapCacheValue[key] == nil or self.m_mapCacheValue[key] ~= value then
    self.m_mapCacheValue[key] = value
    self.m_dataDict:Set(key, value)
    self:SetDirty()
  end
end

function SimpleStringDict:IsEmpty()
  return self.m_dataDict:IsEmpty()
end

function SimpleStringDict:IsCracked()
  return self.m_dataDict:IsCracked()
end

function SimpleStringDict:Remove(key)
  key = tostring(key)
  self.m_mapCacheValue[key] = nil
  self.m_dataDict:Remove(key)
  self:SetDirty()
end

function SimpleStringDict:Clear()
  self.m_mapCacheValue = {}
  self.m_dataDict:Clear()
  self:SetDirty()
end

function SimpleStringDict:ToJson()
  if not self.m_strJson then
    self.m_strJson = self.m_dataDict:ToJson()
  end
  return self.m_strJson
end

function SimpleStringDict:FromJson(strJson)
  self:Clear()
  strJson = strJson or ""
  self.m_dataDict:FromJson(strJson)
  self.m_strJson = strJson
end

function SimpleStringDict:SetDirty()
  self.m_bDirty = true
  self.m_strJson = nil
end

function SimpleStringDict:IsDirty()
  return self.m_bDirty
end
