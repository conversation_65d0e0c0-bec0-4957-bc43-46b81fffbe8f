MailDataWindow = setmetatable({}, BaseWindow)
MailDataWindow.__index = MailDataWindow

function MailDataWindow:Init(confirmText)
  self.m_confirmText = confirmText
  if self.m_confirmText ~= nil then
    self.m_descText.text = GM.GameTextModel:GetText("show_data_desc_2", string.gsub(self.m_confirmText, "(.)([%w_%-%.]+)(@.+)", "%1***%3"))
  else
    self.m_descText.text = GM.GameTextModel:GetText("show_data_desc_1")
  end
  self:_UpdateConfirmButton(false)
end

function MailDataWindow:OnInputEditing()
  self:_UpdateConfirmButton(self:IsInputLegal())
end

function MailDataWindow:IsInputLegal()
  if self.m_confirmText ~= nil then
    return self.m_inputField.text == self.m_confirmText
  end
  local inputText = self.m_inputField.text
  local legalCharPattern = "[%w-_%.]+"
  return inputText == string.match(inputText, legalCharPattern .. "@" .. legalCharPattern)
end

function MailDataWindow:_UpdateConfirmButton(enable)
  self.m_confirmButtonTb:SetEnabled(enable)
  self.m_confirmBtn.enabled = true
end

function MailDataWindow:OnRedClick()
  self:Close()
end

function MailDataWindow:OnGreenClick()
  if not self:IsInputLegal() then
    GM.UIManager:ShowPromptWithKey(self.m_confirmText ~= nil and "show_data_error_1" or "show_data_error_2")
    return
  end
  if self.m_confirmText ~= nil then
    GM.OperManager:SendUserDataEmail(self.m_confirmText)
  else
    GM.UIManager:OpenView(UIPrefabConfigName.MailDataWindow, StringUtil.Trim(self.m_inputField.text))
  end
  self:Close()
end
