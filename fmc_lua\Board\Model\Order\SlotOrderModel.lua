SlotOrderModel = setmetatable({}, BaseOrderModel)
SlotOrderModel.__index = SlotOrderModel

function SlotOrderModel:Init(boardModel, dbMetaTable, dbTable, slotCount)
  self.m_arrFillSlotSequence = {}
  for i = 1, slotCount do
    self.m_arrFillSlotSequence[#self.m_arrFillSlotSequence + 1] = i
  end
  BaseOrderModel.Init(self, dbMetaTable, dbTable, boardModel)
end

function SlotOrderModel:_TryFillOrders()
  local hasFilledOrder = false
  local hasFilledOrderOnce = false
  repeat
    hasFilledOrderOnce = false
    for _, slot in ipairs(self.m_arrFillSlotSequence) do
      local order = self.m_orders[slot]
      if order == nil or order:GetState() == OrderState.Finished then
        if order ~= nil then
          self:_DBRemoveOrder(slot)
          self.m_orders[slot] = nil
        end
        local newOrder = self:_TryCreateOrder(slot)
        if newOrder ~= nil then
          hasFilledOrder = true
          hasFilledOrderOnce = true
          self:_DBAddOrder(newOrder)
          self.m_orders[slot] = newOrder
          self:_LogOrderAction(newOrder, EBIOrderAction.OrderUnlock)
        end
      end
    end
  until not hasFilledOrderOnce
  if hasFilledOrder then
    self.m_boardModel:UpdateOrderState()
  end
end
