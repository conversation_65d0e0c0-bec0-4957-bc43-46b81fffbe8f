RateModel = {}
RateModel.__index = RateModel
local DBId = "1"
local DBColumnCanPopup = "canPopup"
local DBColumnRate = "rated"
local DBColumnTriggeredConfig = "triggeredConfig"
local DBColumnActiveConfigId = "activeConfigId"

function RateModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.Rate)
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self._OnMainTaskFinished)
  EventDispatcher.AddListener(EEventType.OnClaimedOrderGroupReward, self, self._OnClaimedOrderGroupReward)
end

function RateModel:LoadServerConfig()
  local configs = GM.ConfigModel:GetServerConfig(ServerConfigKey.RateUs)
  if not Table.IsEmpty(configs) then
    self.m_configs = configs
  else
    return
  end
  self:_CheckConfig()
  if self.m_dbTable:IsEmpty() then
    self:_ResetDBData()
  end
end

function RateModel:LateInit()
  self.m_lateInited = true
  self:_CheckConfig()
end

function RateModel:_CheckConfig()
  if not self.m_lateInited then
    return
  end
  if Table.IsEmpty(self.m_configs) then
    return
  end
  local map = {}
  for _, cfg in pairs(self.m_configs) do
    if not Table.IsEmpty(cfg.task) then
      for _, taskCfg in ipairs(cfg.task) do
        local chapterId = taskCfg.ChapterId
        local chapterName = GM.ChapterDataModel:GetChapterNameById(chapterId)
        local taskCount = taskCfg.TaskCount
        if not map[chapterId] then
          map[chapterId] = {}
        end
        if map[chapterId][taskCount] then
          Log.Error("引导评价配置重复 config id:" .. cfg.id .. ", 重复元素:" .. chapterId .. "_" .. taskCount)
        else
          map[chapterId][taskCount] = true
          if taskCount > GM.TaskDataModel:GetChapterTaskCount(chapterName) then
            Log.Error("引导评价配置超过章节任务数 config id:" .. cfg.id .. ", 问题配置:" .. chapterId .. "_" .. taskCount)
          end
        end
      end
    end
  end
end

function RateModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function RateModel:_OnMainTaskFinished(msg)
  if Table.IsEmpty(msg) or Table.IsEmpty(self.m_configs) then
    return
  end
  local ongoingChapterId = GM.TaskManager:GetOngoingChapterId()
  local ongoingFinishedCount = GM.TaskManager:GetChapterFinishedCount(ongoingChapterId)
  local triggeredConfig = self:_GetTriggeredConfig()
  for _, config in pairs(self.m_configs) do
    if not Table.IsEmpty(config.task) and not Table.ListContain(triggeredConfig, config.id) then
      for _, taskConfig in ipairs(config.task) do
        if ongoingChapterId == taskConfig.ChapterId and ongoingFinishedCount == taskConfig.TaskCount then
          self:TriggerPopup(config.id)
        end
      end
    end
  end
end

function RateModel:_OnClaimedOrderGroupReward()
  if Table.IsEmpty(self.m_configs) then
    return
  end
  local curDay = GM.MainBoardModel:GetCurOrderDay()
  local triggeredConfig = self:_GetTriggeredConfig()
  for _, config in pairs(self.m_configs) do
    if not Table.IsEmpty(config.day) and not Table.ListContain(triggeredConfig, config.id) then
      for _, day in ipairs(config.day) do
        if curDay == day then
          self:TriggerPopup(config.id)
        end
      end
    end
  end
end

function RateModel:CheckPopup()
  if not self:_CanPopupWindow() then
    return
  end
  return UIPrefabConfigName.RateWindow
end

function RateModel:GetRateLink()
  local config = self:_GetActiveConfig()
  return config and config.link or nil
end

function RateModel:FinishRate()
  self.m_dbTable:Set(DBId, DBColumnRate, 1)
end

function RateModel:OnWindowPopup()
  self.m_dbTable:Set(DBId, DBColumnCanPopup, 0)
end

function RateModel:TriggerPopup(cfgId)
  self.m_dbTable:Set(DBId, DBColumnCanPopup, 1)
  self.m_dbTable:Set(DBId, DBColumnActiveConfigId, cfgId)
end

function RateModel:_ResetDBData()
  if Table.IsEmpty(self.m_configs) then
    return
  end
  self.m_dbTable:Set(DBId, DBColumnCanPopup, 0)
  self.m_dbTable:Set(DBId, DBColumnRate, 0)
  self.m_dbTable:Set(DBId, DBColumnTriggeredConfig, "")
  self.m_dbTable:Set(DBId, DBColumnActiveConfigId, 0)
end

function RateModel:_CanPopupWindow()
  if Table.IsEmpty(self.m_configs) then
    return false
  end
  if self.m_dbTable:GetValue(DBId, DBColumnRate) == 1 then
    return false
  end
  if self.m_dbTable:GetValue(DBId, DBColumnCanPopup) ~= 1 then
    return false
  end
  if GM.TutorialModel:HasAnyStrongTutorialOngoing() then
    return false
  end
  return true
end

function RateModel:_GetActiveConfig()
  if Table.IsEmpty(self.m_configs) then
    Log.Error("RateModel:_GetActiveConfig 引导评价配置为空")
    return
  end
  local cfgId = self.m_dbTable:GetValue(DBId, DBColumnActiveConfigId)
  for _, cfg in pairs(self.m_configs) do
    if cfg.id == cfgId then
      return cfg
    end
  end
  return self.m_configs and self.m_configs[1] or nil
end

function RateModel:SetConfigTriggered()
  local cfgId = self.m_dbTable:GetValue(DBId, DBColumnActiveConfigId)
  local triggeredConfig = self:_GetTriggeredConfig()
  if Table.ListContain(triggeredConfig, cfgId) then
    return
  end
  table.insert(triggeredConfig, cfgId)
  local strConfig = StringUtil.Replace(json.encode(triggeredConfig), ",", "@")
  self.m_dbTable:Set(DBId, DBColumnTriggeredConfig, strConfig)
end

function RateModel:_GetTriggeredConfig()
  local strConfig = self.m_dbTable:GetValue(DBId, DBColumnTriggeredConfig)
  return json.decode(StringUtil.Replace(strConfig, "@", ",")) or {}
end

function RateModel:WhetherJumpToCustomerCenterWhenGiveBadReview()
  local config = self:_GetActiveConfig()
  return config and config.contact == 1
end
