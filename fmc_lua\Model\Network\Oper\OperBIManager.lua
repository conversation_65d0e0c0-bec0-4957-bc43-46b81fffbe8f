EOperBIEventType = {
  first_pay = "first_pay",
  collect_item = "collect_item",
  start_purchase = "start_purchase"
}
OperBIManager = {}
OperBIManager.__index = OperBIManager

function OperBIManager:TrackFirstInstall(serverTime)
  CSAppsFlyerManager:TrackEventWithUidAndTime("mf_install", tostring(GM.UserModel:GetUserId()), serverTime, true)
end

function OperBIManager:TrackClientPurchase(realMoney, currency, orderId)
  CSAppsFlyerManager:TrackIAP("client_purchase", tostring(GM.UserModel:GetUserId()), realMoney, currency, orderId)
  CSAppsFlyerManager:TrackEventWithUidAndTime("abc", tostring(GM.UserModel:GetUserId()), tostring(GM.GameModel:GetServerTime()), true)
  self:_LogFirebaseEventByEventName("abc")
end

function OperBIManager:OperLoginCallback(respData)
  if respData and IsTable(respData) then
    self.m_biData = {}
    for eventType, str in pairs(respData) do
      self.m_biData[eventType] = json.decode(str)
    end
    self:_SetGroupData("levelup", "level")
    self:_SetGroupData("dayup", "day")
  else
    GM.BIManager:LogErrorInfo(EBIType.OperCallbackError, "[OperCallback] Invalid adjust data. " .. Log.Encode(respData or ""))
  end
end

function OperBIManager:_SetGroupData(adjustKey, targetKey)
  local newTb = {}
  if self.m_biData and self.m_biData[adjustKey] then
    for _, data in pairs(self.m_biData[adjustKey]) do
      if data[targetKey] then
        newTb[data[targetKey]] = data
      end
    end
    self.m_biData[adjustKey] = newTb
  end
end

function OperBIManager:TrackEvent(operBIEventName)
  if self.m_biData and self.m_biData[operBIEventName] then
    self:_TrackEventByAppsflyer(self.m_biData[operBIEventName].appsflyer)
    self:_LogFirebaseEventByEventName(self.m_biData[operBIEventName].fben)
  end
end

function OperBIManager:TrackLevelUpEvent(level)
  if self.m_biData and self.m_biData.levelup and self.m_biData.levelup[level] then
    self:_TrackEventByAppsflyer(self.m_biData.levelup[level].appsflyer)
    self:_LogFirebaseEventByEventName(self.m_biData.levelup[level].fben)
  end
end

function OperBIManager:TrackDayUpEvent(day)
  if self.m_biData and self.m_biData.dayup and self.m_biData.dayup[day] then
    self:_TrackEventByAppsflyer(self.m_biData.dayup[day].appsflyer)
    self:_LogFirebaseEventByEventName(self.m_biData.dayup[day].fben)
  end
end

function OperBIManager:_TrackEventByAppsflyer(appsflyer)
  if appsflyer then
    CSAppsFlyerManager:TrackEventWithUidAndTime(appsflyer, tostring(GM.UserModel:GetUserId()), tostring(GM.GameModel:GetServerTime()))
  end
end

function OperBIManager:_LogFirebaseEventByEventName(firebaseEventName)
  if firebaseEventName then
    local func = function()
      CSFirebaseManager:LogEvent(firebaseEventName)
    end
    SafeCall(func)
  end
end
