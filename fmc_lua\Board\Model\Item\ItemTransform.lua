ItemTransformEventType = {StateChanged = 1}
ItemTransform = setmetatable({}, BaseItemComponent)
ItemTransform.__index = ItemTransform

function ItemTransform.Create(itemConfig)
  local itemTransform = setmetatable({}, ItemTransform)
  itemTransform:Init(itemConfig)
  return itemTransform
end

function ItemTransform:Init(itemConfig)
  self.event = PairEvent.Create(self)
  self.m_targetItemType = itemConfig.Transform[1].Currency
  self.m_duration = itemConfig.Transform_Duration
  self.m_speedUpCost = itemConfig.Transform_SpeedUpCost
  if self.m_duration ~= nil then
    self.m_startTimer = GM.GameModel:GetServerTime()
  else
    self.m_startTimer = -1
  end
end

function ItemTransform:FromSerialization(dbTable)
  self.m_startTimer = dbTable.transformStartTimer
end

function ItemTransform:ToSerialization(dbTable)
  dbTable.transformStartTimer = self.m_startTimer
end

function ItemTransform:UpdatePerSecond()
  if self.m_duration == nil then
    return
  end
  local itemAccelerateTime = self.m_itemModel:GetComponent(ItemAccelerateTime)
  if itemAccelerateTime ~= nil and self:CanAccelerate() and itemAccelerateTime:IsAccelerated() then
    local endTime = math.min(itemAccelerateTime:GetTime(), GM.GameModel:GetServerTime())
    self.m_startTimer = self.m_startTimer + itemAccelerateTime:GetLastUpdateTime() - endTime
    self.m_itemModel:GetBoardModel():SaveItemProperty(self.m_itemModel)
  end
  if self:GetTimerAmount() == 1 then
    self:Transform()
  end
  self.event:Call(ItemTransformEventType.StateChanged)
end

function ItemTransform:CanAccelerate()
  return self.m_duration ~= nil and self.m_startTimer ~= -1
end

function ItemTransform:ShowAcceleratedCountDownAnim()
  local itemAccelerateTime = self.m_itemModel:GetComponent(ItemAccelerateTime)
  return itemAccelerateTime ~= nil and self:CanAccelerate() and itemAccelerateTime:IsAccelerated()
end

function ItemTransform:OnSpeedUp(isFree)
  if self.m_duration == nil then
    return
  end
  self:_SpeedUp(isFree)
end

function ItemTransform:_SpeedUp(isFree)
  if not isFree then
    local cost = self:GetSpeedUpCost()
    local gemNumber = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
    if cost > gemNumber then
      local boardModel = self.m_itemModel:GetBoardModel()
      boardModel.event:Call(BoardEventType.LackGem, {
        LackNumber = cost - gemNumber
      })
      return
    end
    GM.PropertyDataManager:Consume(EPropertyType.Gem, cost, EBIType.SpeedUp, false, self.m_itemModel:GetCode())
  end
  GM.BIManager:LogAction(EBIType.SpeedUp, GM.BIManager:TableToString({
    t = self.m_itemModel:GetType(),
    d = self.m_duration - (self.m_startTimer and GM.GameModel:GetServerTime() - self.m_startTimer or 0),
    s = isFree and "ad" or "gem"
  }))
  self:Transform()
end

function ItemTransform:Transform(cost, justSpread)
  local transformAudioName = self.m_itemModel:GetTransformAudio()
  if transformAudioName ~= nil then
    GM.AudioModel:PlayEffect(transformAudioName)
  end
  local boardModel = self.m_itemModel:GetBoardModel()
  local newItem = boardModel:ReplaceItem(self.m_itemModel, self.m_targetItemType, cost and cost or ItemModelHelper.GetCost(self.m_itemModel))
  local eventInfo = {
    Source = self.m_itemModel,
    New = newItem,
    JustSpread = justSpread
  }
  boardModel.event:Call(BoardEventType.TransformItem, eventInfo)
end

function ItemTransform:GetDuration()
  return self.m_duration
end

function ItemTransform:GetStartTimer()
  return self.m_startTimer
end

function ItemTransform:GetSpeedUpCost()
  local timerAmount = self:GetTimerAmount()
  local speedUpCost = (1 - timerAmount) * self.m_speedUpCost
  speedUpCost = math.ceil(speedUpCost)
  return math.max(speedUpCost, 1)
end

function ItemTransform:GetTimerAmount()
  local elapsedTime = GM.GameModel:GetServerTime() - self.m_startTimer
  return self:_GetTimerAmount(elapsedTime)
end

function ItemTransform:GetNextTimerAmount()
  local nextElapsedTime = GM.GameModel:GetServerTime() - self.m_startTimer + 1
  return self:_GetTimerAmount(nextElapsedTime)
end

function ItemTransform:_GetTimerAmount(elapsedTime)
  return math.min(elapsedTime / self.m_duration, 1)
end
