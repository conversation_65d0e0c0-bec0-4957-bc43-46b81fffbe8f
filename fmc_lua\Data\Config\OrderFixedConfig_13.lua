return {
  {
    Id = "130010",
    GroupId = 1,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_13e5assort_15",
      Count = 1
    }
  },
  {
    Id = "130020",
    GroupId = 1,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130030",
    GroupId = 1,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "130040",
    GroupId = 1,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "130050",
    GroupId = 1,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130060",
    GroupId = 1,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e4sf_26",
      Count = 1
    }
  },
  {
    Id = "130070",
    GroupId = 1,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    }
  },
  {
    Id = "130080",
    GroupId = 2,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "130090",
    GroupId = 2,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130100",
    GroupId = 2,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6assort_13",
      Count = 1
    }
  },
  {
    Id = "130110",
    GroupId = 2,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "130120",
    GroupId = 2,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_11e5fd_26",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130130",
    GroupId = 2,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "130140",
    GroupId = 2,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "130150",
    GroupId = 3,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_10e6rice_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6sf_29",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130160",
    GroupId = 3,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_7e6assort_4",
      Count = 1
    }
  },
  {
    Id = "130170",
    GroupId = 3,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_e4sf_12", Count = 1}
  },
  {
    Id = "130180",
    GroupId = 3,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1cockt_25",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4friedmt_20",
      Count = 1
    }
  },
  {
    Id = "130190",
    GroupId = 3,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6sf_22",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130200",
    GroupId = 3,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "130210",
    GroupId = 3,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_3",
      Count = 1
    }
  },
  {
    Id = "130220",
    GroupId = 4,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6semi_13",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5assort_15",
      Count = 1
    }
  },
  {
    Id = "130230",
    GroupId = 4,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6porr_1",
      Count = 1
    }
  },
  {
    Id = "130240",
    GroupId = 4,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130250",
    GroupId = 4,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e5fd_24",
      Count = 1
    }
  },
  {
    Id = "130260",
    GroupId = 4,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "130270",
    GroupId = 4,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_10e6dst_11",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130280",
    GroupId = 4,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4friedmt_21",
      Count = 1
    }
  },
  {
    Id = "130290",
    GroupId = 5,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6rice_12",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "130300",
    GroupId = 5,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1saus_27",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6rice_8",
      Count = 1
    }
  },
  {
    Id = "130310",
    GroupId = 5,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_13e5mt_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130320",
    GroupId = 5,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_6e1icytre_4",
      Count = 1
    },
    Requirement_2 = {Type = "ds_sal_1", Count = 1}
  },
  {
    Id = "130330",
    GroupId = 5,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_12e1nutt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e3scsau_1",
      Count = 1
    }
  },
  {
    Id = "130340",
    GroupId = 5,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130350",
    GroupId = 5,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1cockt_24",
      Count = 1
    }
  },
  {
    Id = "130360",
    GroupId = 6,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "130370",
    GroupId = 6,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6bec_3",
      Count = 1
    }
  },
  {
    Id = "130380",
    GroupId = 6,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130390",
    GroupId = 6,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_12",
      Count = 1
    }
  },
  {
    Id = "130400",
    GroupId = 6,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "130410",
    GroupId = 6,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130420",
    GroupId = 6,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1dst_21",
      Count = 1
    }
  },
  {
    Id = "130430",
    GroupId = 7,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4friedmt_22",
      Count = 1
    }
  },
  {
    Id = "130440",
    GroupId = 7,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "130450",
    GroupId = 7,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_13e5mt_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130460",
    GroupId = 7,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_10",
      Count = 1
    }
  },
  {
    Id = "130470",
    GroupId = 7,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "130480",
    GroupId = 7,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130490",
    GroupId = 7,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_13e4brun_3",
      Count = 1
    }
  },
  {
    Id = "130500",
    GroupId = 8,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "130510",
    GroupId = 8,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "130520",
    GroupId = 8,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_12e6porr_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4friedmt_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130530",
    GroupId = 8,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "130540",
    GroupId = 8,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130550",
    GroupId = 8,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "130560",
    GroupId = 8,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1cockt_26",
      Count = 1
    }
  },
  {
    Id = "130570",
    GroupId = 9,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4brun_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "130580",
    GroupId = 9,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1cockt_25",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "130590",
    GroupId = 9,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130600",
    GroupId = 9,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_10e6rice_10",
      Count = 1
    }
  },
  {
    Id = "130610",
    GroupId = 9,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillmt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    }
  },
  {
    Id = "130620",
    GroupId = 9,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130630",
    GroupId = 9,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    }
  },
  {
    Id = "130640",
    GroupId = 10,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "130650",
    GroupId = 10,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6dst_11",
      Count = 1
    }
  },
  {
    Id = "130660",
    GroupId = 10,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130670",
    GroupId = 10,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_11e4tato_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e3soup_13",
      Count = 1
    }
  },
  {
    Id = "130680",
    GroupId = 10,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130690",
    GroupId = 10,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1cockt_24",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_1",
      Count = 1
    }
  },
  {
    Id = "130700",
    GroupId = 10,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "130710",
    GroupId = 11,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    }
  },
  {
    Id = "130720",
    GroupId = 11,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4assort_11",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130730",
    GroupId = 11,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "ds_dst_1", Count = 1}
  },
  {
    Id = "130740",
    GroupId = 11,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130750",
    GroupId = 11,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "130760",
    GroupId = 11,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfru_1",
      Count = 1
    }
  },
  {
    Id = "130770",
    GroupId = 11,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "130780",
    GroupId = 12,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_2",
      Count = 1
    }
  },
  {
    Id = "130790",
    GroupId = 12,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_13e5fd_27",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130800",
    GroupId = 12,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e2sf_27",
      Count = 1
    }
  },
  {
    Id = "130810",
    GroupId = 12,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4friedmt_20",
      Count = 1
    }
  },
  {
    Id = "130820",
    GroupId = 12,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_friedsf_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130830",
    GroupId = 12,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4brun_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "130840",
    GroupId = 12,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "130850",
    GroupId = 13,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "130860",
    GroupId = 13,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1cockt_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130870",
    GroupId = 13,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_13e5dst_23",
      Count = 1
    }
  },
  {
    Id = "130880",
    GroupId = 13,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6porr_1",
      Count = 1
    }
  },
  {
    Id = "130890",
    GroupId = 13,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_9e6assort_5",
      Count = 1
    }
  },
  {
    Id = "130900",
    GroupId = 13,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e2mt_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130910",
    GroupId = 13,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_12e6porr_2",
      Count = 1
    }
  },
  {
    Id = "130920",
    GroupId = 14,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "130930",
    GroupId = 14,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130940",
    GroupId = 14,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6bec_5",
      Count = 1
    }
  },
  {
    Id = "130950",
    GroupId = 14,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_1",
      Count = 1
    }
  },
  {
    Id = "130960",
    GroupId = 14,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5assort_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "130970",
    GroupId = 14,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_13e2sf_27",
      Count = 1
    }
  },
  {
    Id = "130980",
    GroupId = 14,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "130990",
    GroupId = 15,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2flb_5",
      Count = 1
    }
  },
  {
    Id = "131000",
    GroupId = 15,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131010",
    GroupId = 15,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e6porr_1",
      Count = 1
    }
  },
  {
    Id = "131020",
    GroupId = 15,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1appe_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_9",
      Count = 1
    }
  },
  {
    Id = "131030",
    GroupId = 15,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131040",
    GroupId = 15,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131050",
    GroupId = 15,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_13e6bec_1",
      Count = 1
    }
  },
  {
    Id = "131060",
    GroupId = 16,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "131070",
    GroupId = 16,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_2",
      Count = 1
    }
  },
  {
    Id = "131080",
    GroupId = 16,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131090",
    GroupId = 16,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_13e6bec_7",
      Count = 1
    }
  },
  {
    Id = "131100",
    GroupId = 16,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_12e1dst_21",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131110",
    GroupId = 16,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_13e1appe_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131120",
    GroupId = 16,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "131130",
    GroupId = 17,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1}
  },
  {
    Id = "131140",
    GroupId = 17,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4assort_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131150",
    GroupId = 17,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_12e1nutt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "131160",
    GroupId = 17,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "131170",
    GroupId = 17,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131180",
    GroupId = 17,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131190",
    GroupId = 17,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6bec_10",
      Count = 1
    },
    Requirement_2 = {Type = "ds_7e5mt_3", Count = 1}
  },
  {
    Id = "131200",
    GroupId = 18,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131210",
    GroupId = 18,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4appe_3",
      Count = 1
    }
  },
  {
    Id = "131220",
    GroupId = 18,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_10e4sf_26",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131230",
    GroupId = 18,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "131240",
    GroupId = 18,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_3",
      Count = 1
    }
  },
  {
    Id = "131250",
    GroupId = 18,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131260",
    GroupId = 18,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_8e6soup_8",
      Count = 1
    }
  },
  {
    Id = "131270",
    GroupId = 19,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_11e1tato_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "131280",
    GroupId = 19,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1appe_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131290",
    GroupId = 19,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e1icytre_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131300",
    GroupId = 19,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "131310",
    GroupId = 19,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_mixdrk_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131320",
    GroupId = 19,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "131330",
    GroupId = 19,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_8",
      Count = 1
    }
  },
  {
    Id = "131340",
    GroupId = 20,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "131350",
    GroupId = 20,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131360",
    GroupId = 20,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131370",
    GroupId = 20,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1appe_2",
      Count = 1
    }
  },
  {
    Id = "131380",
    GroupId = 20,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_11e3icytre_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e2mt_15",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131390",
    GroupId = 20,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1cockt_25",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "131400",
    GroupId = 20,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4assort_14",
      Count = 1
    }
  },
  {
    Id = "131410",
    GroupId = 21,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_11e6porr_1",
      Count = 1
    }
  },
  {
    Id = "131420",
    GroupId = 21,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4brun_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131430",
    GroupId = 21,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1cockt_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "131440",
    GroupId = 21,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_11e4tato_5",
      Count = 1
    }
  },
  {
    Id = "131450",
    GroupId = 21,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131460",
    GroupId = 21,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1appe_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4friedmt_18",
      Count = 1
    }
  },
  {
    Id = "131470",
    GroupId = 21,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e6porr_2",
      Count = 1
    }
  },
  {
    Id = "131480",
    GroupId = 22,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1cockt_24",
      Count = 1
    }
  },
  {
    Id = "131490",
    GroupId = 22,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131500",
    GroupId = 22,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_13e5dst_23",
      Count = 1
    }
  },
  {
    Id = "131510",
    GroupId = 22,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1mdrk_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6rice_12",
      Count = 1
    }
  },
  {
    Id = "131520",
    GroupId = 22,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131530",
    GroupId = 22,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_friedve_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5mt_12",
      Count = 1
    }
  },
  {
    Id = "131540",
    GroupId = 22,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131550",
    GroupId = 23,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "131560",
    GroupId = 23,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131570",
    GroupId = 23,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6bec_11",
      Count = 1
    }
  },
  {
    Id = "131580",
    GroupId = 23,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_11e4tato_24",
      Count = 1
    }
  },
  {
    Id = "131590",
    GroupId = 23,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_13e1appe_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131600",
    GroupId = 23,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_15",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6bec_4",
      Count = 1
    }
  },
  {
    Id = "131610",
    GroupId = 23,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_5",
      Count = 1
    }
  },
  {
    Id = "131620",
    GroupId = 24,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    }
  },
  {
    Id = "131630",
    GroupId = 24,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_13e1appe_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131640",
    GroupId = 24,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_12e1nutt_2",
      Count = 1
    }
  },
  {
    Id = "131650",
    GroupId = 24,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_10e1icytre_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_4",
      Count = 1
    }
  },
  {
    Id = "131660",
    GroupId = 24,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131670",
    GroupId = 24,
    ChapterId = 13,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_13e4friedmt_20",
      Count = 1
    }
  },
  {
    Id = "131680",
    GroupId = 24,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_11e6stewmt_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131690",
    GroupId = 25,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131700",
    GroupId = 25,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6bec_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131710",
    GroupId = 25,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_8",
      Count = 1
    }
  },
  {
    Id = "131720",
    GroupId = 25,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_21", Count = 1}
  },
  {
    Id = "131730",
    GroupId = 25,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131740",
    GroupId = 25,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_12e1nutt_1",
      Count = 1
    }
  },
  {
    Id = "131750",
    GroupId = 25,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_9e6assort_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5sf_28",
      Count = 1
    }
  },
  {
    Id = "131760",
    GroupId = 26,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_13e5dst_22",
      Count = 1
    }
  },
  {
    Id = "131770",
    GroupId = 26,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6semi_13",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_10e6nibble_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131780",
    GroupId = 26,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "131790",
    GroupId = 26,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6bec_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e5mt_12",
      Count = 1
    }
  },
  {
    Id = "131800",
    GroupId = 26,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1appe_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131810",
    GroupId = 26,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "131820",
    GroupId = 26,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_12e1nutt_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131830",
    GroupId = 27,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "131840",
    GroupId = 27,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131850",
    GroupId = 27,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1cockt_26",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_4",
      Count = 1
    }
  },
  {
    Id = "131860",
    GroupId = 27,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_6e2flb_5",
      Count = 1
    }
  },
  {
    Id = "131870",
    GroupId = 27,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131880",
    GroupId = 27,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_11e2veg_11",
      Count = 1
    }
  },
  {
    Id = "131890",
    GroupId = 27,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6bec_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e4friedmt_21",
      Count = 1
    }
  },
  {
    Id = "131900",
    GroupId = 28,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_12e1nutt_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfru_1",
      Count = 1
    }
  },
  {
    Id = "131910",
    GroupId = 28,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6appe_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131920",
    GroupId = 28,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "131930",
    GroupId = 28,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1icytre_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e1nutt_6",
      Count = 1
    }
  },
  {
    Id = "131940",
    GroupId = 28,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131950",
    GroupId = 28,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_10e1mdrk_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_13e6brun_4",
      Count = 1
    }
  },
  {
    Id = "131960",
    GroupId = 28,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_11e6nibble_10",
      Count = 1
    }
  },
  {
    Id = "131970",
    GroupId = 29,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6stewmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_4",
      Count = 1
    }
  },
  {
    Id = "131980",
    GroupId = 29,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4friedmt_19",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "131990",
    GroupId = 29,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_12e1nutt_1",
      Count = 1
    }
  },
  {
    Id = "132000",
    GroupId = 29,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e1sala_8",
      Count = 1
    }
  },
  {
    Id = "132010",
    GroupId = 29,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4brun_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "132020",
    GroupId = 29,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "132030",
    GroupId = 29,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e4friedmt_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_11e4tato_19",
      Count = 1
    }
  },
  {
    Id = "132040",
    GroupId = 30,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "132050",
    GroupId = 30,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "132060",
    GroupId = 30,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6bec_6",
      Count = 1
    }
  },
  {
    Id = "132070",
    GroupId = 30,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_grillmt_12",
      Count = 1
    }
  },
  {
    Id = "132080",
    GroupId = 30,
    ChapterId = 13,
    Requirement_1 = {
      Type = "ds_13e6bec_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "132090",
    GroupId = 30,
    ChapterId = 13,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "132100",
    GroupId = 30,
    ChapterId = 13,
    Requirement_1 = {Type = "it_7_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  }
}
