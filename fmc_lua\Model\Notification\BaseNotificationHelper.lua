BaseNotificationHelper = {}
BaseNotificationHelper.__index = BaseNotificationHelper

function BaseNotificationHelper.IsSceneExist(strScene)
  Log.Assert(false, "通知需要继承IsSceneExist判断自己是否支持配置的strScene")
  return false
end

DashActivityNotificationHelper = setmetatable({}, BaseNotificationHelper)
DashActivityNotificationHelper.__index = DashActivityNotificationHelper

function DashActivityNotificationHelper._GenerateDashStartNotification(results, pDashModel, eNotiType, strTextTitle, strTextDesc)
  table.insert(results, {
    Type = eNotiType,
    Title = GM.GameTextModel:GetText(strTextTitle),
    Message = GM.GameTextModel:GetText(strTextDesc),
    Delay = pDashModel:GetNextStateTime() - GM.GameModel:GetServerTime()
  })
end

function DashActivityNotificationHelper._GenerateDashEndNotification(results, pDashModel, eNotiType, strTextTitle, strTextDesc, nEndtimeDelay)
  if nEndtimeDelay == nil then
    nEndtimeDelay = 18000
  end
  local level = pDashModel:GetLevel()
  local levelConfigs = pDashModel:GetLevelConfigs()
  if levelConfigs[level] == nil then
    return
  end
  if level == 1 and pDashModel:GetScore() == 0 then
    return
  end
  local delay = pDashModel:GetNextStateTime() - GM.GameModel:GetServerTime() - nEndtimeDelay
  if delay <= 0 then
    return
  end
  table.insert(results, {
    Type = eNotiType,
    Title = GM.GameTextModel:GetText(strTextTitle),
    Message = GM.GameTextModel:GetText(strTextDesc),
    Delay = delay
  })
end
