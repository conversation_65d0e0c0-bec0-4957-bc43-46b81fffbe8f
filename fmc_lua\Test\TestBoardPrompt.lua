BoardPromptType.TestCollectAll = "testCollectAll"
BoardPromptType.TestRecoverSpread = "testRecoverSpread"
BoardPromptType.TestStartCook = "testStartCook"
BoardPromptType.TestTakeOutDish = "testTakeOutDish"
BoardPromptType.TestTakeOutMaterial = "testTakeOutMaterial"
BoardPromptType.TestStoreItem = "testStoreItem"
BoardPromptType.TestRetrieveItem = "testRetrieveItem"
BoardPromptType.TestOpenChest = "testOpenChest"
BoardPromptType.TestSpreadItem = "testSpreadItem"
BoardPromptOpenChest = setmetatable({}, BoardPrompt)
BoardPromptOpenChest.__index = BoardPromptOpenChest

function BoardPromptOpenChest.Create()
  local prompt = setmetatable({}, BoardPromptOpenChest)
  prompt:Init(BoardPromptType.TestOpenChest)
  return prompt
end

function BoardPromptOpenChest:CanStart(boardView)
  if boardView:GetModel():HasOpeningItem() then
    return false
  end
  local filter = function(itemModel)
    local itemSpread = itemModel:GetComponent(ItemSpread)
    return itemSpread ~= nil and itemSpread:GetState() == ItemSpreadState.Closed
  end
  self.m_itemModels = boardView:GetModel():FilterItems(filter)
  return #self.m_itemModels ~= 0
end

function BoardPromptOpenChest:_Start(boardView)
  self.m_itemModel = Table.ListRandomSelectOne(self.m_itemModels)
  if boardView:GetSelectedItemModel() == self.m_itemModel then
    self:StartStep2(boardView)
  else
    local itemView = boardView:GetItemView(self.m_itemModel)
    boardView:ShowHandTapEffect(itemView.transform.position)
  end
end

function BoardPromptOpenChest:AutoDo(boardView)
  self:_Stop(boardView)
  boardView:GetModel():OpenItem(self.m_itemModel)
end

function BoardPromptOpenChest:StartStep2(boardView)
  if boardView:GetSelectedItemModel() ~= self.m_itemModel then
    return false
  end
  boardView:HideHandTapEffect()
  boardView:GetInfoBar():GetInfoContent():SetHandEffectActive(true)
  return true
end

function BoardPromptOpenChest:_Stop(boardView)
  boardView:HideHandTapEffect()
  boardView:GetInfoBar():GetInfoContent():SetHandEffectActive(false)
end

BoardPromptTestSpreadItem = setmetatable({}, BoardPrompt)
BoardPromptTestSpreadItem.__index = BoardPromptTestSpreadItem

function BoardPromptTestSpreadItem.Create()
  local prompt = setmetatable({}, BoardPromptTestSpreadItem)
  prompt:Init(BoardPromptType.TestSpreadItem)
  return prompt
end

function BoardPromptTestSpreadItem:_CanStart(boardView)
  BoardPromptTestSpreadItem.CurrentOrder = nil
  local filter1 = function(itemModel)
    if GM.ItemDataModel:IsBox(itemModel:GetCode()) then
      return true
    end
  end
  self.m_itemModels = boardView:GetModel():FilterItems(filter1)
  if self.m_itemModels[1] then
    return true
  end
  local boardModel = boardView:GetModel()
  local itemDataModel = GM.ItemDataModel
  if GM.TestAutoRunModel.leastEnergyDiffFirst then
    local orders = boardModel:GetOrders()
    local order = GM.FlambeTimeModel:_GetNextTargetOrder(orders)
    BoardPromptTestSpreadItem.CurrentOrder = order
    if self:_ShouldSpreadForOrder(boardModel, itemDataModel, order) then
      return true
    end
    return false
  else
    local sortedOrders = boardView:GetOrderArea():_GetSortedOrders()
    for _, order in ipairs(sortedOrders) do
      if self:_ShouldSpreadForOrder(boardModel, itemDataModel, order) then
        BoardPromptTestSpreadItem.CurrentOrder = order
        return true
      end
    end
  end
  if self:_ShouldSpreadForOrder(boardModel, itemDataModel, nil) then
    return true
  end
  return false
end

function BoardPromptTestSpreadItem:CanStart(boardView)
  local boardModel = boardView:GetModel()
  if boardModel:IsBoardFull() then
    return false
  end
  return self:_CanStart(boardView)
end

function BoardPromptTestSpreadItem:_ShouldSpreadForOrder(boardModel, itemDataModel, order)
  local filter2 = function(itemModel)
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and not StringUtil.StartWith(itemModel:GetCode(), "pd_") then
      local itemConfig = itemDataModel:GetModelConfig(itemModel:GetCode())
      local arrGenerations = {}
      for _, gen in pairs(itemConfig.GeneratedItems) do
        arrGenerations[#arrGenerations + 1] = gen.Code
      end
      if itemConfig.Transform then
        arrGenerations[#arrGenerations + 1] = itemConfig.Transform[1][PROPERTY_TYPE]
      end
      for _, itemCode in ipairs(arrGenerations) do
        if boardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(itemCode, true, not GM.TestAutoRunModel.useInventory, order) then
          return true
        end
      end
    end
  end
  self.m_itemModels = boardModel:FilterItems(filter2, 1)
  if self.m_itemModels[1] then
    return true
  end
  self.m_itemModels = BoardPromptTestSpreadItem.AutoRunFindSuitablePd(boardModel, itemDataModel, order)
  if self.m_itemModels[1] then
    return true
  end
  return false
end

function BoardPromptTestSpreadItem.AutoRunFindSuitablePd(boardModel, itemDataModel, order, ignoreStorage, ignoreAddItem)
  local mapRequireSplits = {}
  local filter3 = function(itemModel)
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread and not itemSpread:IsDisposable() and (ignoreStorage or itemSpread:GetStorageRestNumber(not ignoreAddItem) > 0) then
      local generations = itemDataModel:GetModelConfig(itemModel:GetCode()).GeneratedItems
      for _, gen in pairs(generations) do
        if mapRequireSplits[gen.Code] then
          return true
        end
      end
    end
  end
  local directResult, indirectResult = boardModel:GetUnfilledOrderRequirementsSeparately(not GM.TestAutoRunModel.useInventory, order)
  local unfilled = {}
  for itemType, count in pairs(directResult) do
    if not itemDataModel:IsDishes(itemType) then
      unfilled[itemType] = true
    end
  end
  for itemType, count in pairs(indirectResult) do
    if not itemDataModel:IsDishes(itemType) then
      unfilled[itemType] = true
    end
  end
  for itemType, _ in pairs(unfilled) do
    local splits = itemDataModel:GetItemSplits(itemType)
    for type, _ in pairs(splits) do
      mapRequireSplits[type] = true
    end
  end
  if not next(mapRequireSplits) then
    return {}
  end
  return boardModel:FilterItems(filter3)
end

function BoardPromptTestSpreadItem:_Start(boardView)
  Table.ListRandomReorder(self.m_itemModels)
  table.sort(self.m_itemModels, self.sortFunc)
  local itemView = boardView:GetItemView(self.m_itemModels[1])
  boardView:ShowHandTapEffect(itemView.transform.position)
end

function BoardPromptTestSpreadItem:AutoDo(boardView)
  self:_Stop(boardView)
  local itemChoose = self.m_itemModels[1]:GetComponent(ItemChoose)
  if itemChoose then
    itemChoose:GetChoices()
    itemChoose:OnChoose(1)
    return
  end
  local itemSpread = self.m_itemModels[1]:GetComponent(ItemSpread)
  if itemSpread and itemSpread:CanAccelerate() and not StringUtil.StartWith(self.m_itemModels[1]:GetCode(), "pd_") then
    itemSpread:OnSpeedUp(true)
  end
  boardView:GetModel():TapItem(self.m_itemModels[1])
end

function BoardPromptTestSpreadItem:_Stop(boardView)
  boardView:HideHandTapEffect()
end

BoardPromptRecoverSpread = setmetatable({}, BoardPrompt)
BoardPromptRecoverSpread.__index = BoardPromptRecoverSpread

function BoardPromptRecoverSpread.Create()
  local prompt = setmetatable({}, BoardPromptRecoverSpread)
  prompt:Init(BoardPromptType.TestRecoverSpread)
  return prompt
end

function BoardPromptRecoverSpread:CanStart(boardView)
  local boardModel = boardView:GetModel()
  local itemDataModel = GM.ItemDataModel
  local order
  if GM.TestAutoRunModel.leastEnergyDiffFirst then
    order = BoardPromptTestSpreadItem.CurrentOrder
  end
  local pdsIgnoreStorage = BoardPromptTestSpreadItem.AutoRunFindSuitablePd(boardModel, itemDataModel, order, true, true)
  if pdsIgnoreStorage[1] then
    local pdsWithStoage = BoardPromptTestSpreadItem.AutoRunFindSuitablePd(boardModel, itemDataModel, order, false, true)
    self.m_shouldRecoverStorage = pdsWithStoage[1] == nil
  end
  self.m_boosterItem = nil
  self.m_pdItem = nil
  local addItem = GM.MainBoardModel:GetOneBoardItemByType("additem_1")
  if addItem then
    local firstOrder = boardView:GetOrderArea():_GetSortedOrders()[1]
    local pds = BoardPromptTestSpreadItem.AutoRunFindSuitablePd(boardModel, itemDataModel, firstOrder, true)
    if pds[1] then
      self.m_boosterItem = addItem
      self.m_pdItem = pds[1]
    end
  end
  return self.m_shouldRecoverStorage or self.m_boosterItem
end

function BoardPromptRecoverSpread:AutoDo(boardView)
  self:_Stop(boardView)
  if self.m_shouldRecoverStorage then
    local boardModel = boardView:GetModel()
    local itemDataModel = GM.ItemDataModel
    local allPds = BoardPromptTestSpreadItem.AutoRunFindSuitablePd(boardModel, itemDataModel, nil, true)
    local itemSpread
    for _, pd in ipairs(allPds) do
      itemSpread = pd:GetComponent(ItemSpread)
      if itemSpread:CanAccelerate() and itemSpread:GetStorageRestNumber() == 0 then
        itemSpread:OnSpeedUp(true)
      end
    end
  end
  if self.m_boosterItem then
    GM.MainBoardModel:DragItem(self.m_boosterItem, self.m_pdItem:GetPosition())
  end
end

BoardPromptCollectAll = setmetatable({}, BoardPrompt)
BoardPromptCollectAll.__index = BoardPromptCollectAll

function BoardPromptCollectAll.Create()
  local prompt = setmetatable({}, BoardPromptCollectAll)
  prompt:Init(BoardPromptType.TestCollectAll)
  return prompt
end

function BoardPromptCollectAll:CanStart(boardView)
  local filter = function(itemModel)
    return itemModel:GetComponent(ItemCollectable) ~= nil
  end
  self.m_itemModels = boardView:GetModel():FilterItems(filter)
  return #self.m_itemModels ~= 0
end

function BoardPromptCollectAll:AutoDo(boardView)
  self:_Stop(boardView)
  table.sort(self.m_itemModels, function(a, b)
    return tonumber(a:GetId()) < tonumber(b:GetId())
  end)
  boardView:GetModel():TapItem(self.m_itemModels[1])
end

BoardPromptStartCook = setmetatable({}, BoardPrompt)
BoardPromptStartCook.__index = BoardPromptStartCook

function BoardPromptStartCook.Create()
  local prompt = setmetatable({}, BoardPromptStartCook)
  prompt:Init(BoardPromptType.TestStartCook)
  return prompt
end

function BoardPromptStartCook:CanStart(boardView)
  local boardModel = boardView:GetModel()
  local directResult, indirectResult = boardModel:GetUnfilledOrderRequirementsSeparately(not GM.TestAutoRunModel.useInventory)
  local cookCmps = boardModel:GetAllItemCookCmp(true, false)
  for _, cookCmp in ipairs(cookCmps) do
    if cookCmp:GetState() == EItemCookState.CanCook and directResult[cookCmp:GetRecipe()] ~= nil then
      self.m_cookComp = cookCmp
      return true
    end
  end
  return false
end

function BoardPromptStartCook:AutoDo(boardView)
  self:_Stop(boardView)
  self.m_cookComp:StartCook()
  self.m_cookComp:OnSpeedUp(true)
end

BoardPromptTakeOutDish = setmetatable({}, BoardPrompt)
BoardPromptTakeOutDish.__index = BoardPromptTakeOutDish

function BoardPromptTakeOutDish.Create()
  local prompt = setmetatable({}, BoardPromptTakeOutDish)
  prompt:Init(BoardPromptType.TestTakeOutDish)
  return prompt
end

function BoardPromptTakeOutDish:CanStart(boardView)
  local cookCmps = GM.MainBoardModel:GetAllItemCookCmp(true, false)
  for _, cookCmp in ipairs(cookCmps) do
    if cookCmp:GetState() == EItemCookState.Cooked then
      self.m_cookCmp = cookCmp
      return true
    end
  end
  return false
end

function BoardPromptTakeOutDish:AutoDo(boardView)
  self:_Stop(boardView)
  self.m_cookCmp:OnTap()
end

BoardPromptStoreItem = setmetatable({}, BoardPrompt)
BoardPromptStoreItem.__index = BoardPromptStoreItem

function BoardPromptStoreItem.Create()
  local prompt = setmetatable({}, BoardPromptStoreItem)
  prompt:Init(BoardPromptType.TestStoreItem)
  return prompt
end

function BoardPromptStoreItem:CanStart(boardView)
  if not GM.TestAutoRunModel.useInventory then
    return false
  end
  local order = BoardPromptTestSpreadItem.CurrentOrder or boardView:GetOrderArea():_GetSortedOrders()[1]
  if not order then
    return false
  end
  local boardModel = boardView:GetModel()
  local emptyCount = boardModel:GetEmptyPositionCount()
  local itemCode
  local filter = function(itemModel)
    if not boardModel:CanItemStore(itemModel) then
      return false
    end
    if emptyCount == 0 and GM.ItemDataModel:IsBox(itemCode) then
      return true
    end
    if itemModel:GetComponent(ItemCook) then
      return false
    end
    itemCode = itemModel:GetCode()
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and StringUtil.StartWith(itemCode, "pd_") then
      return not boardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(itemCode, false, false, order)
    end
    return not boardModel:IsOrderRelaventItem(order, itemCode)
  end
  local itemModels = boardView:GetModel():FilterItems(filter, 1)
  self.m_itemModel = itemModels[1]
  return self.m_itemModel ~= nil
end

function BoardPromptStoreItem:AutoDo(boardView)
  local boardModel = boardView:GetModel()
  boardModel:StoreItem(self.m_itemModel)
end

BoardPromptRetrieveItem = setmetatable({}, BoardPrompt)
BoardPromptRetrieveItem.__index = BoardPromptRetrieveItem

function BoardPromptRetrieveItem.Create()
  local prompt = setmetatable({}, BoardPromptRetrieveItem)
  prompt:Init(BoardPromptType.TestRetrieveItem)
  return prompt
end

function BoardPromptRetrieveItem:CanStart(boardView)
  if not GM.TestAutoRunModel.useInventory then
    return false
  end
  local firstOrder = BoardPromptTestSpreadItem.CurrentOrder or boardView:GetOrderArea():_GetSortedOrders()[1]
  if not firstOrder then
    return false
  end
  local boardModel = boardView:GetModel()
  local emptyCount = boardModel:GetEmptyPositionCount()
  if emptyCount == 0 then
    return false
  end
  if 2 <= emptyCount then
    local duplicateItems = boardModel:GetStoredDuplicateItems(false)
    for itemCode, items in pairs(duplicateItems) do
      local item = next(items)
      if boardModel:CanItemMerge(item) then
        local nextItem = next(items, item)
        self.m_itemModels = {item, nextItem}
        return true
      end
    end
  end
  local itemModel, itemCode
  for i = 1, boardModel:GetStoredItemCount() do
    itemModel = boardModel:GetStoredItem(i)
    itemCode = itemModel:GetCode()
    if boardModel:IsOrderRelaventItem(firstOrder, itemCode) then
      self.m_itemModels = {itemModel}
      return true
    elseif itemModel:GetComponent(ItemSpread) and StringUtil.StartWith(itemCode, "pd_") and boardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(itemCode, false, false, firstOrder) then
      self.m_itemModels = {itemModel}
      return true
    elseif 2 <= emptyCount and GM.ItemDataModel:IsBox(itemCode) then
      self.m_itemModels = {itemModel}
      return true
    end
  end
  return false
end

function BoardPromptRetrieveItem:AutoDo(boardView)
  for _, itemModel in ipairs(self.m_itemModels) do
    local boardModel = boardView:GetModel()
    boardModel:RetrieveStoredItemByItemModel(itemModel)
  end
end

BoardPromptTakeOutMaterial = setmetatable({}, BoardPrompt)
BoardPromptTakeOutMaterial.__index = BoardPromptTakeOutMaterial

function BoardPromptTakeOutMaterial.Create()
  local prompt = setmetatable({}, BoardPromptTakeOutMaterial)
  prompt:Init(BoardPromptType.TestTakeOutMaterial)
  return prompt
end

function BoardPromptTakeOutMaterial:CanStart(boardView)
  local boardModel = boardView:GetModel()
  local emptyCellCount = boardModel:GetEmptyPositionCount()
  if emptyCellCount == 0 then
    return false
  end
  local codeCountMap = boardModel:GetCodeCountMap(true, false, false)
  local cookCodeCountMap = {}
  local cookState
  local cookCmps = boardModel:GetAllItemCookCmp(true, false)
  for _, cookCmp in ipairs(cookCmps) do
    cookState = cookCmp:GetState()
    if cookState == EItemCookState.Prepare or cookState == EItemCookState.CanCook then
      local arrItemCodes = cookCmp:GetCurMaterialsArray()
      for _, code in ipairs(arrItemCodes) do
        cookCodeCountMap[code] = (cookCodeCountMap[code] or 0) + 1
      end
    end
  end
  for _, order in ipairs(boardView:GetOrderArea():_GetSortedOrders()) do
    if self:_CanStartWithOrder(boardModel, codeCountMap, cookCmps, cookCodeCountMap, order, emptyCellCount) then
      return true
    end
  end
  return false
end

function BoardPromptTakeOutMaterial:_CanStartWithOrder(boardModel, codeCountMap, cookCmps, cookCodeCountMap, order, emptyCellCount)
  local requirements = order:GetRequirements()
  local requirementFillStates = order:GetRequirementFillStates()
  local itemDataModel = GM.ItemDataModel
  local unfilledNormalItems = {}
  local lackDish = false
  for i, requirement in ipairs(requirements) do
    if requirementFillStates[i] ~= ERequirementFillState.Init then
    else
      if itemDataModel:IsDishes(requirement) then
        lackDish = true
      end
      unfilledNormalItems[requirement] = (unfilledNormalItems[requirement] or 0) + 1
    end
  end
  self.m_cookCmp = nil
  self.m_dishType = nil
  self.m_arrUnwantedMaterials = nil
  self.m_arrWantedMaterials = nil
  self.m_arrWantedOthersMaterials = nil
  local arrWantedOthersMaterials = {}
  local satisfied = true
  for normalCode, needCount in pairs(unfilledNormalItems) do
    if not cookCodeCountMap[normalCode] or needCount > cookCodeCountMap[normalCode] then
      satisfied = false
      break
    end
    for i = 1, needCount do
      arrWantedOthersMaterials[#arrWantedOthersMaterials + 1] = normalCode
    end
  end
  if satisfied and emptyCellCount >= #arrWantedOthersMaterials and 0 < #arrWantedOthersMaterials then
    self.m_arrWantedOthersMaterials = arrWantedOthersMaterials
    return true
  end
  local directResult, indirectResult = boardModel:GetUnfilledOrderRequirementsSeparately(not GM.TestAutoRunModel.useInventory)
  local unfilledDishes = {}
  local itemDataModel = GM.ItemDataModel
  for itemType, count in pairs(directResult) do
    if itemDataModel:IsDishes(itemType) then
      unfilledDishes[itemType] = true
      local dishMaterials = boardModel:GetUnfilledDishMaterials(itemType, true)
      for dishMaterial, _ in pairs(dishMaterials) do
        unfilledDishes[dishMaterial] = true
      end
    end
  end
  for dishType, _ in pairs(unfilledDishes) do
    for _, cookCmp in ipairs(cookCmps) do
      local canCook, lackedMaterials, arrUnwantedMaterials = cookCmp:CanCookAllowTakeOut(dishType)
      if canCook then
        local satisfied = true
        local arrWantedMaterials = {}
        local arrWantedOthersMaterials = {}
        for _, material in ipairs(lackedMaterials) do
          if codeCountMap[material] then
            codeCountMap[material] = codeCountMap[material] - 1
            if codeCountMap[material] == 0 then
              codeCountMap[material] = nil
            end
            arrWantedMaterials[#arrWantedMaterials + 1] = material
          elseif cookCodeCountMap[material] then
            cookCodeCountMap[material] = cookCodeCountMap[material] - 1
            if cookCodeCountMap[material] == 0 then
              cookCodeCountMap[material] = nil
            end
            arrWantedOthersMaterials[#arrWantedOthersMaterials + 1] = material
          else
            satisfied = false
            break
          end
        end
        if emptyCellCount < #arrUnwantedMaterials then
          satisfied = false
        elseif emptyCellCount - #arrUnwantedMaterials + #arrWantedMaterials == 0 and 0 < #arrWantedOthersMaterials then
          satisfied = false
        end
        if satisfied then
          self.m_cookCmp = cookCmp
          self.m_dishType = dishType
          self.m_arrUnwantedMaterials = arrUnwantedMaterials
          self.m_arrWantedMaterials = arrWantedMaterials
          self.m_arrWantedOthersMaterials = arrWantedOthersMaterials
          return arrWantedMaterials[1] or arrWantedOthersMaterials[1] or arrUnwantedMaterials[1]
        else
          for _, material in ipairs(arrWantedMaterials) do
            codeCountMap[material] = (codeCountMap[material] or 0) + 1
          end
          for _, material in ipairs(arrWantedOthersMaterials) do
            cookCodeCountMap[material] = (cookCodeCountMap[material] or 0) + 1
          end
        end
      end
    end
  end
  return false
end

function BoardPromptTakeOutMaterial:AutoDo(boardView)
  self:_Stop(boardView)
  for _, order in pairs(GM.MainBoardModel:GetOrderModel():GetOrders()) do
    order:TryUnlockAllCookCmp()
  end
  local boardModel = boardView:GetModel()
  local cookCmps = boardModel:GetAllItemCookCmp(true, false)
  local cookState
  if not self.m_cookCmp and not Table.IsEmpty(self.m_arrWantedOthersMaterials) then
    while self.m_arrWantedOthersMaterials[1] do
      for _, cookCmp in ipairs(cookCmps) do
        cookState = cookCmp:GetState()
        if cookState == EItemCookState.Prepare or cookState == EItemCookState.CanCook then
          local arrItemCodes = cookCmp:GetCurMaterialsArray()
          for _, code in ipairs(arrItemCodes) do
            if code == self.m_arrWantedOthersMaterials[1] then
              local success, item = cookCmp:PutBackMaterial(code, V3Zero)
              table.remove(self.m_arrWantedOthersMaterials, 1)
              break
            end
          end
        end
      end
    end
    return
  end
  for _, material in ipairs(self.m_arrUnwantedMaterials) do
    self.m_cookCmp:PutBackMaterial(material, V3Zero)
  end
  local filterItemCode
  local filter = function(itemModel)
    return itemModel:GetCode() == filterItemCode
  end
  for _, material in ipairs(self.m_arrWantedMaterials) do
    filterItemCode = material
    local item = boardModel:FilterItems(filter, 1)[1]
    boardModel:AddMaterialToCook(item, self.m_cookCmp)
    self.m_cookCmp:AddMaterial(item)
  end
  while self.m_arrWantedOthersMaterials[1] do
    for _, cookCmp in ipairs(cookCmps) do
      if cookCmp ~= self.m_cookCmp then
        cookState = cookCmp:GetState()
        if cookState == EItemCookState.Prepare or cookState == EItemCookState.CanCook then
          local arrItemCodes = cookCmp:GetCurMaterialsArray()
          for _, code in ipairs(arrItemCodes) do
            if code == self.m_arrWantedOthersMaterials[1] then
              local success, item = cookCmp:PutBackMaterial(code, V3Zero)
              boardModel:AddMaterialToCook(item, self.m_cookCmp)
              table.remove(self.m_arrWantedOthersMaterials, 1)
              break
            end
          end
        end
      end
    end
  end
  self.m_cookCmp:StartCook()
  self.m_cookCmp:OnSpeedUp(true)
end
