PassActivityPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
PassActivityPopupHelper.__index = PassActivityPopupHelper

function PassActivityPopupHelper.Create()
  local helper = setmetatable({}, PassActivityPopupHelper)
  helper:Init()
  return helper
end

function PassActivityPopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, activityDefinition in pairs(PassActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.ShouldRefreshTimelimitTasksEvent, self, self._OnStateChanged)
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._OnStateChanged)
  end
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnStateChanged)
end

function PassActivityPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function PassActivityPopupHelper:CheckPopup()
  local arrPassType = {}
  for activityType, _ in pairs(PassActivityDefinition) do
    arrPassType[#arrPassType + 1] = activityType
  end
  table.sort(arrPassType, function(a, b)
    if a and b then
      local modelA = GM.ActivityManager:GetModel(a)
      local modelB = GM.ActivityManager:GetModel(b)
      return modelA:GetState() > modelB:GetState()
    else
      return a ~= nil
    end
  end)
  for _, activityType in ipairs(arrPassType) do
    local activityDefinition = PassActivityDefinition[activityType]
    local args = {activityType}
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Started then
      if not model:HasWindowOpenedOnce(ActivityState.Started) then
        return activityDefinition.ReadyWindowPrefabName, args
      end
      if model:ShouldRefreshTimelimitTasks() then
        PassActivityViewHelper.SetViewChain(false)
        local canFinishTasks = model:GetCanFinishTasks(true)
        if #canFinishTasks ~= 0 then
          return activityDefinition.FinishTaskWindowPrefabName, {activityType, canFinishTasks}
        end
        return activityDefinition.NewTimelimitTaskWindowPrefabName, args
      end
    elseif state == ActivityState.Ended and model:HasWindowOpenedOnce(ActivityState.Started) then
      if not model:HasWindowOpenedOnce(ActivityState.Ended) then
        return activityDefinition.EndWindowPrefabName, args
      end
      if model:CanOpenBuyTicketPopupWindow() then
        return activityDefinition.BuyTicketPopupWindowPrefabName, args
      end
      local canTakeRewards = model:GetCanTakeRewards()
      if #canTakeRewards ~= 0 then
        return activityDefinition.RewardRecoverWindowPrefabName, {activityType, canTakeRewards}
      end
    end
  end
end
