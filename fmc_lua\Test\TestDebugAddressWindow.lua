TestDebugAddressWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestDebugAddressWindow.__index = TestDebugAddressWindow

function TestDebugAddressWindow:Init()
  local currentAddress = PlayerPrefs.GetString(EPlayerPrefKey.TestDebugAddress, "localhost")
  self.m_addressInput.text = currentAddress
end

function TestDebugAddressWindow:OnDefaultButtonClicked()
  self.m_addressInput.text = "localhost"
end

function TestDebugAddressWindow:OnConfirmButtonClicked()
  local currentAddress = PlayerPrefs.GetString(EPlayerPrefKey.TestDebugAddress, "localhost")
  local inputAddress = self.m_addressInput.text
  if currentAddress ~= inputAddress then
    PlayerPrefs.SetString(EPlayerPrefKey.TestDebugAddress, inputAddress)
    PlatformInterface.ExitGame()
  end
  self:Close()
end
