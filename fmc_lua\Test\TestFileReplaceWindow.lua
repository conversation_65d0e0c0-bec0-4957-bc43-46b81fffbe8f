TestFileReplaceWindow = setmetatable({}, BaseWindow)
TestFileReplaceWindow.__index = TestFileReplaceWindow

function TestFileReplaceWindow:Init()
  local mapFileReplaces = {}
  for configName, _ in pairs(TextAssetConfigName) do
    local lastUnderlineIndex = StringUtil.rFind(configName, "_")
    if lastUnderlineIndex then
      local realName = string.sub(configName, 1, lastUnderlineIndex - 1)
      if realName ~= "OrderFixedConfig" and realName ~= "OrderGroupConfig" and realName ~= "TaskConfig" and realName ~= "ChapterConfig" then
        local fileReplace = string.sub(configName, lastUnderlineIndex + 1)
        if not mapFileReplaces[realName] then
          mapFileReplaces[realName] = {
            TEST_NO_SUFFIX
          }
        end
        local arrOptions = mapFileReplaces[realName]
        arrOptions[#arrOptions + 1] = fileReplace
      end
    end
  end
  self.m_cells = {}
  local mapTestFileReplaces = GM.TestModel:GetTestFileReplaces()
  local configNames = Table.GetKeys(mapFileReplaces)
  table.sort(configNames, function(a, b)
    return string.reverse(a) < string.reverse(b)
  end)
  for _, configName in ipairs(configNames) do
    local arrOptions = mapFileReplaces[configName]
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.TestFileReplaceCell), self.m_contentRect, Vector3.zero, function(go)
      local luaTable = go:GetLuaTable()
      luaTable:Init(self, configName, arrOptions, mapTestFileReplaces[configName])
      self.m_cells[#self.m_cells + 1] = luaTable
      LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_contentRect)
    end)
  end
  self.m_clearDataSwitch:Init(function(isOn)
    self.m_shouldClearData = isOn
  end)
  self.m_clearDataSwitch:SetOn(true, true)
  self.m_sameTogetherSwitch:Init(function(isOn)
    self.bSameTogether = isOn
  end)
  self.m_sameTogetherSwitch:SetOn(true, true)
end

function TestFileReplaceWindow:OnCellSelectOne(changedCell, option)
  if not self.bSameTogether then
    return
  end
  for _, cell in ipairs(self.m_cells) do
    if cell ~= changedCell then
      cell:OnSelectOption(option)
    end
  end
end

function TestFileReplaceWindow:OnTouchConfirm()
  local mapTestFileReplaces = {}
  for _, tb in ipairs(self.m_cells) do
    mapTestFileReplaces[tb.configName] = tb:GetSelectedOption()
  end
  if not self.m_shouldClearData then
    GM.TestModel:SaveTestFileReplaces(mapTestFileReplaces)
    GM:RestartGame(nil, "testFileReplace")
    return
  end
  GM.TestModel:ClearServerData(false, function(success)
    if success then
      GM.TestModel:SaveTestFileReplaces(mapTestFileReplaces)
      GM.TestModel:ClearData(false)
      GM:RestartGame(nil, "testFileReplace")
    end
  end)
end

function TestFileReplaceWindow:OnTouchReset()
  for _, cell in ipairs(self.m_cells) do
    cell:Reset()
  end
end
