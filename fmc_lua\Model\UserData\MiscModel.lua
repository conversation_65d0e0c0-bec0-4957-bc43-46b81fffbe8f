MiscModel = {}
MiscModel.__index = MiscModel

function MiscModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.Misc)
  ModelHelper.DefineProperties(MiscModel, EMiscKey)
end

function MiscModel:GetData()
  return self.m_dbTable
end

function MiscModel:FromSyncData(dataArr)
  self.m_dbTable:FromArr(dataArr)
end

function MiscModel:Get(key)
  return self.m_dbTable:GetValue(key, "value")
end

function MiscModel:Set(key, value)
  self.m_dbTable:Set(key, "value", value)
end

function MiscModel:Remove(key)
  self.m_dbTable:Remove(key)
end

function MiscModel:GetInNumber(key)
  return tonumber(self:Get(key)) or 0
end

function MiscModel:ChangeNumber(key, delta)
  if delta == 0 then
    return
  end
  local curNum = self:GetInNumber(key)
  local newNum = curNum + delta
  self:Set(key, tostring(newNum))
end
