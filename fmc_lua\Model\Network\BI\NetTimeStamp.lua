NetTimeStamp = {}
NetTimeStamp.__index = NetTimeStamp

function NetTimeStamp.Create(name)
  local ts = setmetatable({}, NetTimeStamp)
  ts:Init(name)
  return ts
end

function NetTimeStamp:Init(name)
  self.m_cpuStartTime = DeviceInfo.GetCpuTime()
  self.m_profilerStartTime = MicrofunProfiler.Instance:CreateTimeStamp("net_ts_" .. name)
end

function NetTimeStamp:EndAndGetDur()
  self.m_profilerStartTime:End()
  local gtDur = self.m_profilerStartTime.gameTime
  local gtInMS = math.floor(gtDur * 1000)
  local totalDur = self.m_profilerStartTime.time
  local totalInMS = math.floor(totalDur * 1000)
  local cpuInS = DeviceInfo.GetCpuTime() - self.m_cpuStartTime
  return {
    cpuInS = cpuInS,
    gtInMS = gtInMS,
    totalInMS = totalInMS
  }
end
