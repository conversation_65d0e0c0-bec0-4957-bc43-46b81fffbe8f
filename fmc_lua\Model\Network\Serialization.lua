return function()
  local Serialization = {}
  local ENDIAN_MODE = "<"
  local BOOLEAN_PACK_OPTION = ENDIAN_MODE .. "B"
  local BOOLEAN_SIZE = 1
  local SBYTE_PACK_OPTION = ENDIAN_MODE .. "b"
  local SBYTE_SIZE = 1
  local INT16_PACK_OPTION = ENDIAN_MODE .. "i2"
  local INT16_SIZE = 2
  local INT32_PACK_OPTION = ENDIAN_MODE .. "i4"
  local INT32_SIZE = 4
  local INT64_PACK_OPTION = ENDIAN_MODE .. "i8"
  local INT64_SIZE = 8
  local BYTE_PACK_OPTION = ENDIAN_MODE .. "B"
  local BYTE_SIZE = 1
  local UINT8_PACK_OPTION = ENDIAN_MODE .. "I1"
  local UINT8_SIZE = 1
  local UINT16_PACK_OPTION = ENDIAN_MODE .. "I2"
  local UINT16_SIZE = 2
  local UINT32_PACK_OPTION = ENDIAN_MODE .. "I4"
  local UINT32_SIZE = 4
  local UINT64_PACK_OPTION = ENDIAN_MODE .. "I8"
  local UINT64_SIZE = 8
  local SINGLE_PACK_OPTION = ENDIAN_MODE .. "f"
  local SINGLE_SIZE = 4
  local DOUBLE_PACK_OPTION = ENDIAN_MODE .. "d"
  local DOUBLE_SIZE = 8
  local BYTE2STRING_PACK_OPTION = ENDIAN_MODE .. "s2"
  Serialization.MessageHeader = {}
  
  function Serialization.MessageHeader.Serialize(writer, message_header)
    assert(message_header.ProtocolMd5)
    assert(message_header.MessageId)
    assert(message_header.Operation)
    assert(#message_header.ProtocolMd5 == 16)
    local bRet
    for i = 1, 16 do
      bRet = Serialization.Byte.Serialize(writer, message_header.ProtocolMd5[i])
      if not bRet then
        return false
      end
    end
    bRet = Serialization.VarUInt64.Serialize(writer, message_header.MessageId)
    if not bRet then
      return false
    end
    bRet = Serialization.String.Serialize(writer, message_header.Operation)
    if not bRet then
      return false
    end
    return true
  end
  
  function Serialization.MessageHeader.Deserialize(reader)
    local message_header = {}
    message_header.ProtocolMd5 = {}
    local bRet
    for i = 1, 16 do
      bRet, message_header.ProtocolMd5[i] = Serialization.Byte.Deserialize(reader)
      if not bRet then
        return false
      end
    end
    bRet, message_header.MessageId = Serialization.VarUInt64.Deserialize(reader)
    if not bRet then
      return false
    end
    bRet, message_header.Operation = Serialization.String.Deserialize(reader)
    if not bRet then
      return false
    end
    return true, message_header
  end
  
  Serialization.Byte = {}
  
  function Serialization.Byte.Serialize(writer, byte_value)
    byte_value = byte_value == nil and 0 or byte_value
    return writer:write(string.pack(BYTE_PACK_OPTION, byte_value))
  end
  
  function Serialization.Byte.Deserialize(reader)
    local val = reader:read(BYTE_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(BYTE_PACK_OPTION, val)
  end
  
  Serialization.UInt8 = {}
  
  function Serialization.UInt8.Serialize(writer, uint8_value)
    uint8_value = uint8_value == nil and 0 or uint8_value
    return writer:write(string.pack(UINT8_PACK_OPTION, uint8_value))
  end
  
  function Serialization.UInt8.Deserialize(reader)
    local val = reader:read(UINT8_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(UINT8_PACK_OPTION, val)
  end
  
  Serialization.UInt16 = {}
  
  function Serialization.UInt16.Serialize(writer, uint16_value)
    uint16_value = uint16_value == nil and 0 or uint16_value
    return writer:write(string.pack(UINT16_PACK_OPTION, uint16_value))
  end
  
  function Serialization.UInt16.Deserialize(reader)
    local val = reader:read(UINT16_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(UINT16_PACK_OPTION, val)
  end
  
  Serialization.UInt32 = {}
  
  function Serialization.UInt32.Serialize(writer, uint32_value)
    uint32_value = uint32_value == nil and 0 or uint32_value
    return writer:write(string.pack(UINT32_PACK_OPTION, uint32_value))
  end
  
  function Serialization.UInt32.Deserialize(reader)
    local val = reader:read(UINT32_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(UINT32_PACK_OPTION, val)
  end
  
  Serialization.UInt64 = {}
  
  function Serialization.UInt64.Serialize(writer, uint64_value)
    uint64_value = uint64_value == nil and 0 or uint64_value
    return writer:write(string.pack(UINT64_PACK_OPTION, uint64_value))
  end
  
  function Serialization.UInt64.Deserialize(reader)
    local val = reader:read(UINT64_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(UINT64_PACK_OPTION, val)
  end
  
  Serialization.SByte = {}
  
  function Serialization.SByte.Serialize(writer, sbyte_value)
    sbyte_value = sbyte_value == nil and 0 or sbyte_value
    return writer:write(string.pack(SBYTE_PACK_OPTION, sbyte_value))
  end
  
  function Serialization.SByte.Deserialize(reader)
    local val = reader:read(SBYTE_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(SBYTE_PACK_OPTION, val)
  end
  
  Serialization.Int16 = {}
  
  function Serialization.Int16.Serialize(writer, int16_value)
    int16_value = int16_value == nil and 0 or int16_value
    return writer:write(string.pack(INT16_PACK_OPTION, int16_value))
  end
  
  function Serialization.Int16.Deserialize(reader)
    local val = reader:read(INT16_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(INT16_PACK_OPTION, val)
  end
  
  Serialization.Int32 = {}
  
  function Serialization.Int32.Serialize(writer, int32_value)
    int32_value = int32_value == nil and 0 or int32_value
    return writer:write(string.pack(INT32_PACK_OPTION, int32_value))
  end
  
  function Serialization.Int32.Deserialize(reader)
    local val = reader:read(INT32_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(INT32_PACK_OPTION, val)
  end
  
  Serialization.Int64 = {}
  
  function Serialization.Int64.Serialize(writer, int64_value)
    int64_value = int64_value == nil and 0 or int64_value
    return writer:write(string.pack(INT64_PACK_OPTION, int64_value))
  end
  
  function Serialization.Int64.Deserialize(reader)
    local val = reader:read(INT64_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(INT64_PACK_OPTION, val)
  end
  
  Serialization.Single = {}
  
  function Serialization.Single.Serialize(writer, single_value)
    single_value = single_value == nil and 0 or single_value
    return writer:write(string.pack(SINGLE_PACK_OPTION, single_value))
  end
  
  function Serialization.Single.Deserialize(reader)
    local val = reader:read(SINGLE_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(SINGLE_PACK_OPTION, val)
  end
  
  Serialization.Double = {}
  
  function Serialization.Double.Serialize(writer, double_value)
    double_value = double_value == nil and 0 or double_value
    return writer:write(string.pack(DOUBLE_PACK_OPTION, double_value))
  end
  
  function Serialization.Double.Deserialize(reader)
    local val = reader:read(DOUBLE_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(DOUBLE_PACK_OPTION, val)
  end
  
  Serialization.Boolean = {}
  
  function Serialization.Boolean.Serialize(writer, boolean_value)
    local value
    if boolean_value then
      value = 1
    else
      value = 0
    end
    return writer:write(string.pack(BOOLEAN_PACK_OPTION, value))
  end
  
  function Serialization.Boolean.Deserialize(reader)
    local val = reader:read(BOOLEAN_SIZE)
    if val == nil then
      return false
    end
    return true, string.unpack(BOOLEAN_PACK_OPTION, val) ~= 0
  end
  
  Serialization.DateTime = {}
  
  function Serialization.DateTime.Serialize(writer, datetime_value)
    datetime_value = datetime_value == nil and 0 or datetime_value
    return Serialization.VarInt64.Serialize(writer, datetime_value)
  end
  
  function Serialization.DateTime.Deserialize(reader)
    return Serialization.VarInt64.Deserialize(reader)
  end
  
  Serialization.String = {}
  
  function Serialization.String.Serialize(writer, string_value)
    string_value = string_value == nil and "" or string_value
    local string_length = string.len(string_value)
    local bRet
    bRet = Serialization.VarUInt64.Serialize(writer, string_length)
    if not bRet then
      return false
    end
    bRet = writer:write(string_value)
    if not bRet then
      return false
    end
    return true
  end
  
  function Serialization.String.Deserialize(reader)
    local bRet, string_length
    bRet, string_length = Serialization.VarUInt64.Deserialize(reader)
    if not bRet then
      return false
    end
    local val = reader:read(string_length)
    if val == nil then
      return false
    end
    return true, val
  end
  
  Serialization.B2String = {}
  
  function Serialization.B2String.Serialize(writer, string_value)
    string_value = string_value == nil and "" or string_value
    return writer:write(string.pack(BYTE2STRING_PACK_OPTION, string_value))
  end
  
  function Serialization.B2String.Deserialize(reader)
    local bRet, string_length
    bRet, string_length = Serialization.UInt16.Deserialize(reader)
    if not bRet then
      return false
    end
    local val = reader:read(string_length)
    if val == nil then
      return false
    end
    return true, val
  end
  
  Serialization.VarUInt64 = {}
  
  function Serialization.VarUInt64.Serialize(writer, uint64_value)
    uint64_value = uint64_value == nil and 0 or uint64_value
    local bRet
    for nLen = 1, 8 do
      local ucCode = uint64_value & 127
      uint64_value = uint64_value >> 7
      if uint64_value ~= 0 then
        ucCode = ucCode | 128
      end
      bRet = writer:write(string.pack(BYTE_PACK_OPTION, ucCode))
      if not bRet then
        return false
      end
      if uint64_value == 0 then
        return true
      end
    end
    return writer:write(string.pack(BYTE_PACK_OPTION, uint64_value))
  end
  
  function Serialization.VarUInt64.Deserialize(reader)
    local ullVal = 0
    local val
    for i = 1, 8 do
      val = reader:read(BYTE_SIZE)
      if val == nil then
        return false
      end
      local ucCode = string.unpack(BYTE_PACK_OPTION, val)
      ullVal = ullVal | (ucCode & 127) << (i - 1) * 7
      if ucCode & 128 == 0 then
        return true, ullVal
      end
    end
    val = reader:read(BYTE_SIZE)
    if val == nil then
      return false
    end
    local ucCode = string.unpack(BYTE_PACK_OPTION, val)
    ullVal = ullVal | (ucCode & 127) << 56
    return true, ullVal
  end
  
  Serialization.VarInt64 = {}
  
  function Serialization.VarInt64.Serialize(writer, int64_value)
    int64_value = int64_value == nil and 0 or int64_value
    local ullVal = (int64_value < 0 and {
      -int64_value
    } or {int64_value})[1] << 1 | (int64_value < 0 and 1 or 0)
    return Serialization.VarUInt64.Serialize(writer, ullVal)
  end
  
  function Serialization.VarInt64.Deserialize(reader)
    local bRet, ullVal
    bRet, ullVal = Serialization.VarUInt64.Deserialize(reader)
    if not bRet then
      return false
    end
    local isSigned = ullVal & 1 ~= 0
    ullVal = ullVal >> 1
    return true, (isSigned and {
      -ullVal
    } or {ullVal})[1]
  end
  
  function Serialization.Array(object_serializer)
    local array_serializer = {}
    
    function array_serializer.Serialize(writer, array_value)
      local bRet
      local array_length = #array_value
      bRet = Serialization.VarUInt64.Serialize(writer, array_length)
      if not bRet then
        return false
      end
      for k, v in ipairs(array_value) do
        bRet = object_serializer.Serialize(writer, v)
        if not bRet then
          return false
        end
      end
      return true
    end
    
    function array_serializer.Deserialize(reader)
      local array_value = {}
      local bRet, array_length
      bRet, array_length = Serialization.VarUInt64.Deserialize(reader)
      if not bRet then
        return false
      end
      for i = 1, array_length do
        bRet, array_value[i] = object_serializer.Deserialize(reader)
        if not bRet then
          return false
        end
      end
      return true, array_value
    end
    
    return array_serializer
  end
  
  return Serialization
end
