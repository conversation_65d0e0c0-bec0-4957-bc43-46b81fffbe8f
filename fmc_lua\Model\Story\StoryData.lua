StoryData = {}
StoryData.__index = StoryData

function StoryData.Create(tbData, specialPrefix)
  local data = setmetatable(tbData, StoryData)
  data.specialPrefix = specialPrefix
  data:Init()
  return data
end

function StoryData:Init()
  self.animationName = self.animation
  if self.animation and StringUtil.EndWith(self.animation, "-loop") then
    self.animationName = string.sub(self.animation, 0, #self.animation - 5)
    self.bLoopAnimation = true
  end
end

function StoryData:IsAvatarFlip()
  return self.res == 1
end

function StoryData:GetRoleName()
  if self.speaker then
    local splits = StringUtil.Split(self.speaker, "_")
    Log.Assert(2 <= #splits, "剧情配置中 speaker 格式错误！")
    return GM.StoryDataModel:GetRoleName(splits[1] or "")
  end
  local curActiveChapterName = GM.ChapterManager.curActiveChapterName
  return GM.StoryDataModel:GetRoleName(curActiveChapterName)
end

function StoryData:GetContentText()
  local text
  if self.specialPrefix then
    text = GM.GameTextModel:GetSpecialText(self.specialPrefix, self.content)
  else
    local activeChapterId = GM.MakeoverManager.curActiveChapterId
    text = GM.GameTextModel:GetChapterText(activeChapterId, self.content)
  end
  local result, ret = SafeCall(function()
    return StringUtil.Replace(text, "PLAYER_NAME", GM.UserProfileModel:GetName())
  end)
  if result then
    return ret
  else
    return text
  end
end

function StoryData:GetCompType()
  return self:IsAvatarFlip() and EStoryComponentType.Right or EStoryComponentType.Left
end
