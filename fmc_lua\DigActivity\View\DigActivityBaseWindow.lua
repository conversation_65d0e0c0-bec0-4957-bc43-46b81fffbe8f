DigActivityBaseWindow = setmetatable({}, BaseWindow)
DigActivityBaseWindow.__index = DigActivityBaseWindow

function DigActivityBaseWindow:BeforeOpenCheck()
  local model
  for activityType, _ in pairs(DigActivityDefinition) do
    model = GM.ActivityManager:GetModel(activityType)
    if model and model:GetState() ~= ActivityState.Released then
      return true
    end
  end
  return false
end

function DigActivityBaseWindow:Init(activityType, bUserClick, bMainWindow)
  self.m_activityType = activityType
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_activityDefinition = DigActivityDefinition[self.m_activityType]
  self.m_model:SetWindowOpened()
  if not bMainWindow then
    AddHandlerAndRecordMap(self.m_model.event, DigActivityEventType.StateChanged, {
      obj = self,
      method = self.Close
    })
  end
  self:LogWindowAction(EBIType.UIActionType.Open, {
    bUserClick and EBIReferType.UserClick or EBIReferType.AutoPopup
  })
end

function DigActivityBaseWindow:OnCloseView(bWithoutAnimation)
  BaseWindow.OnCloseView(self, bWithoutAnimation)
  self:LogWindowAction(EBIType.UIActionType.Close)
end

function DigActivityBaseWindow:OnDestroy()
  BaseWindow.OnDestroy(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
end

DigActivityHelpWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, DigActivityBaseWindow)
DigActivityHelpWindow.__index = DigActivityHelpWindow
