EEventType = {
  EnterMainScene = "EnterMainScene",
  ChangeGameMode = "ChangeGameMode",
  ChangeGameModeFinished = "ChangeGameModeFinished",
  ChapterWillChange = "ChapterWillChange",
  ChapterChangeWillFinish = "ChapterChangeWillFinish",
  ChapterChangeFinished = "ChapterChangeFinished",
  LogicChangeChapter = "LogicChangeChapter",
  CacheItems = "CacheItems",
  PopCachedItem = "PopCachedItem",
  PopCacheFailed = "PopCacheFailed",
  ChangeCachedItems = "ChangeCachedItems",
  ViewCacheItems = "ViewCacheItems",
  CacheActivityItems = "CacheActivityItems",
  PlayCollectAnimation = "PlayCollectAnimation",
  PlayPropertyIncreaseAnimation = "PlayPropertyIncreaseAnimation",
  PlayPropertyDecreaseAnimation = "PlayPropertyDecreaseAnimation",
  PlayConsumeAnimation = "PlayConsumeAnimation",
  PropertyAcquired = "PropertyAcquired",
  PropertyConsumed = "PropertyConsumed",
  LevelUp = "LevelUp",
  EnergyRestored = "EnergyRestored",
  EnergyChanged = "EnergyChanged",
  OnAddEnergy = "OnAddEnergy",
  FunctionOpen = "FunctionOpen",
  LoginFinished = "LoginFinished",
  BoardPointerUp = "BoardPointerUp",
  ItemViewAdded = "ItemViewAdded",
  ItemMerged = "ItemMerged",
  ItemAffected = "ItemAffected",
  ItemSpread = "ItemSpread",
  ItemSpreadFailed = "ItemSpreadFailed",
  ItemReplaced = "ItemReplaced",
  ItemOpening = "ItemOpening",
  ItemOpened = "ItemOpened",
  ItemUnlocked = "ItemUnlocked",
  ItemRetrieved = "ItemRetrieved",
  ItemActivated = "ItemActivated",
  ItemStored = "ItemStored",
  ItemCookStarted = "ItemCookStarted",
  ItemCookEnded = "ItemCookEnded",
  ItemCookAddMaterial = "ItemCookAddMaterial",
  ItemCookMaterialClicked = "ItemCookMaterialClicked",
  TakeOutDishItem = "TakeOutDishItem",
  ItemTutorial = "ItemTutorial",
  BubbleDisposed = "BubbleDisposed",
  ItemSpeederClicked = "ItemSpeederClicked",
  OnSpeedUp = "OnSpeedUp",
  UpdateStorage = "UpdateStorage",
  PaperboxDisappear = "PaperboxDisappear",
  BoardCollect = "BoardCollect",
  ItemFlyTarget = "ItemFlyTarget",
  NewTasksUnlocked = "NewTasksUnlocked",
  TaskEnableFunction = "TaskEnableFunction",
  TaskNodeOnSelected = "TaskNodeOnSelected",
  NewContentReleased = "NewContentReleased",
  NewChapterUnlocked = "NewChapterUnlocked",
  NewReleasedOrderGroupUnlocked = "NewReleasedOrderGroupUnlocked",
  OrderGroupRefreshed = "OrderGroupRefreshed",
  UpdateOrderGroupButton = "UpdateOrderGroupButton",
  OnInventoryItemSold = "OnInventoryItemSold",
  OnInventoryItemUndoSell = "OnInventoryItemUndoSell",
  OnInventoryCapacityUpdate = "OnInventoryCapacityUpdate",
  OnClaimedOrderGroupReward = "OnClaimedOrderGroupReward",
  CollectGold = "CollectGold",
  CosumeEnergy = "CosumeEnergy",
  BuyBubble = "BuyBubble",
  BuyCobweb = "BuyCobweb",
  ProducerItemClick = "ProducerItemClick",
  RefreshSettingStrongTip = "RefreshSettingStrongTip",
  OpenView = "OpenView",
  CloseView = "CloseView",
  OnViewWillClose = "OnViewWillClose",
  InventoryNewSlot = "InventoryNewSlot",
  InventoryItemUpdate = "InventoryItemUpdate",
  InventoryChangeTab = "InventoryChangeTab",
  UpdateSceneViewHud = "UpdateSceneViewHud",
  HighlightHud = "HighlightHud",
  DiscoveriesUpdate = "DiscoveriesUpdate",
  HideTapCacheWeakTutorial = "HideTapCacheWeakTutorial",
  ToggleSkipPropHud = "ToggleSkipPropHud",
  ShowEditSlider = "ShowEditSlider",
  HideEditSlider = "HideEditSlider",
  FlyingElementClear = "FlyingElementClear",
  UpdateProfile = "UpdateProfile",
  Exclamation = "Exclamation",
  UpdateNotice = "UpdateNotice",
  ShowPrompt = "ShowPrompt",
  TaskGoClicked = "TaskGoClicked",
  RoomTouchesUp = "RoomTouchesUp",
  EventLockClear = "EventLockClear",
  ShowNewOrders = "ShowNewOrders",
  OrderDayChanged = "OrderDayChanged",
  OnEventLockMaskClicked = "OnEventLockMaskClicked",
  ItemDetailWindowUpdated = "ItemDetailWindowUpdated",
  TaskBubbleStateChanged = "TaskBubbleStateChanged",
  CacheBubbleStateChanged = "CacheBubbleStateChanged",
  FlambeTimeChanged = "FlambeTimeChanged",
  FlambeTimePopup = "FlambeTimePopup",
  BeforeFlambeEnded = "BeforeFlambeEnded",
  ItemDeleteButtonUpdate = "ItemDeleteButtonUpdate",
  AddBundleWindowToPopupChain = "AddBundleWindowToPopupChain",
  RemoveBundleWindowFromPopupChain = "RemoveBundleWindowFromPopupChain",
  MainTaskFinished = "MainTaskFinished",
  TaskProgressRewardClaimed = "TaskProgressRewardClaimed",
  TimelineStart = "TimelineStart",
  TimelineComplete = "TimelineComplete",
  InitVideoFinished = "InitVideoFinished",
  ShopRefreshed = "ShopRefreshed",
  ShopBuyItemSuccess = "ShopBuyItemSuccess",
  BuyEnergySuccess = "BuyEnergySuccess",
  BundleDataRefreshed = "BundleDataRefreshed",
  BundleIAPRestoreSuccess = "BundleIAPRestoreSuccess",
  OrderStateChanged = "OrderStateChanged",
  OrderFinished = "OrderFinished",
  OrderStatusChanged = "OrderStatusChanged",
  TimelimitComponentRemoved = "TimelimitComponentRemoved",
  OrderAnimationFinished = "OrderAnimationFinished",
  ItemFlyChanged = "ItemFlyChanged",
  RefreshPrompt = "RefreshPrompt",
  OrderRefreshedByChapterChange = "OrderRefreshedByChapterChange",
  UpdateOrderEmpty = "UpdateOrderEmpty",
  OrderItemCookPopTip = "OrderItemCookPopTip",
  CacheRootNodeStateChanged = "CacheRootNodeStateChanged",
  TutorialFinished = "TutorialFinished",
  StrongTutorialStart = "StrongTutorialStart",
  ApplicationWillEnterForeground = "ApplicationWillEnterForeground",
  ApplicationDidEnterBackground = "ApplicationDidEnterBackground",
  ShowItemTestInfoChanged = "ShowItemTestInfoChanged",
  FavoriteItemChanged = "FavoriteItemChanged",
  CashDashStateChanged = "CashDashStateChanged",
  CashDashUpgraded = "CashDashUpgraded",
  LollipopStateChanged = "LollipopStateChanged",
  LollipopUpgraded = "LollipopUpgraded",
  CoconutStateChanged = "CoconutStateChanged",
  CoconutUpgraded = "CoconutUpgraded",
  PinataStateChanged = "PinataStateChanged",
  PinataUpgraded = "PinataUpgraded",
  PinataUpdateOrderCell = "PinataUpdateOrderCell",
  BakeOutStateChanged = "BakeOutStateChanged",
  BakeOutCoinExchanged = "BakeOutCoinExchanged",
  BakeOutModeChanged = "BakeOutModeChanged",
  CoinRaceSignUpClicked = "CoinRaceSignUpClicked",
  CoinRaceStateChanged = "CoinRaceStateChanged",
  CoinRaceScoreChanged = "CoinRaceScoreChanged",
  BreakEggStateChanged = "BreakEggStateChanged",
  PiggyBankStateChanged = "PiggyBankStateChanged",
  PiggyBankAccumulateGem = "PiggyBankAccumulateGem",
  DailyTaskStateChanged = "DailyTaskStateChanged",
  DailyTaskFinishTask = "DailyTaskFinishTask",
  ActivityRaceCompleted = "ActivityRaceCompleted",
  PkRaceStateChanged = "PkRaceStateChanged",
  SurpriseChestStateChanged = "SurpriseChestStateChanged",
  SurpriseChestDataStateChanged = "SurpriseChestDataStateChanged",
  SurpriseChestRefreshBoardEntry = "SurpriseChestRefreshBoardEntry",
  BlindChestClickSlot = "BlindChestClickSlot",
  BlindChest1StateChanged = "BlindChest1StateChanged",
  BlindChest1TurnChanged = "BlindChest1TurnChanged",
  BlindChest1KeyChanged = "BlindChest1KeyChanged",
  BlindChest1ScoreChanged = "BlindChest1ScoreChanged",
  PassActivityBonusTaskCellCreated = "PassActivityBonusTaskCellCreated",
  TryScrollToProgressActivityBoardCell = "TryScrollToProgressActivityBoardCell",
  ProgressActivity1StateChanged = "ProgressActivity1StateChanged",
  ProgressActivity1ScoreChanged = "ProgressActivity1ScoreChanged",
  ProgressActivity1LevelChanged = "ProgressActivity1LevelChanged",
  FlowerStateChanged = "FlowerStateChanged",
  FlowerShouldRefreshTimelimitTasks = "FlowerShouldRefreshTimelimitTasks",
  FlowerTaskProgressChanged = "FlowerTaskProgressChanged",
  FlowerCanFinishTaskTimesChanged = "FlowerCanFinishTaskTimesChanged",
  FlowerCanTakeRewardNumberChanged = "FlowerCanTakeRewardNumberChanged",
  FlowerBuyTicketSuccess = "FlowerBuyTicketSuccess",
  PirateStateChanged = "PirateStateChanged",
  PirateShouldRefreshTimelimitTasks = "PirateShouldRefreshTimelimitTasks",
  PirateTaskProgressChanged = "PirateTaskProgressChanged",
  PirateCanFinishTaskTimesChanged = "PirateCanFinishTaskTimesChanged",
  PirateCanTakeRewardNumberChanged = "PirateCanTakeRewardNumberChanged",
  PirateBuyTicketSuccess = "PirateBuyTicketSuccess",
  PetStateChanged = "PetStateChanged",
  PetShouldRefreshTimelimitTasks = "PetShouldRefreshTimelimitTasks",
  PetTaskProgressChanged = "PetTaskProgressChanged",
  PetCanFinishTaskTimesChanged = "PetCanFinishTaskTimesChanged",
  PetCanTakeRewardNumberChanged = "PetCanTakeRewardNumberChanged",
  PetBuyTicketSuccess = "PetBuyTicketSuccess",
  EventTokenNumChange = "EventTokenNumChange",
  SkinAcquired = "SkinAcquired",
  EventLevelExpChange = "EventLevelExpChange",
  SpreeSpringStateChanged = "SpreeSpringStateChanged",
  SpreeWizardStateChanged = "SpreeWizardStateChanged",
  SpreeEasterStateChanged = "SpreeEasterStateChanged",
  UIItemBookUpdate = "UIItemBookUpdate",
  ExtraBoardViewCreated = "ExtraBoardViewCreated",
  ExtraBoardActivityItemUnlocked = "ExtraBoardActivityItemUnlocked",
  ExtraBoardEnterNextCobwebRound = "ExtraBoardEnterNextCobwebRound",
  ExtraBoardCobwebUnlock = "ExtraBoardCobwebUnlock",
  ExtraBoard1StateChanged = "ExtraBoard1StateChanged",
  ExtraBoard2StateChanged = "ExtraBoard2StateChanged",
  ExtraBoard3StateChanged = "ExtraBoard3StateChanged",
  ExtraBoard4StateChanged = "ExtraBoard4StateChanged",
  ExtraBoard5StateChanged = "ExtraBoard5StateChanged",
  ExtraBoard6StateChanged = "ExtraBoard6StateChanged",
  ExtraBoard7StateChanged = "ExtraBoard7StateChanged",
  AlbumPopStateChanged = "AlbumPopStateChanged",
  JokerUpdateExchangeSelect = "JokerUpdateExchangeSelect",
  AlbumStartButtonClick = "AlbumStartButtonClick",
  Album1StateChanged = "Album1StateChanged",
  SpreeLevelExpChange = "SpreeLevelExpChange",
  AccountBindSuccess = "AccountBindSuccess",
  AccountLogout = "AccountLogout",
  SyncStateChanged = "SyncStateChanged",
  UpcomingEventStateChanged = "UpcomingEventStateChanged",
  EnergyBoostModeChanged = "EnergyBoostModeChanged",
  IconAnimationCreate = "IconAnimationCreate",
  IconAnimationDestroy = "IconAnimationDestroy",
  TreasureDigStateChanged = "TreasureDigStateChanged",
  TreasureDigScoreChanged = "TreasureDigScoreChanged",
  TreasureDigItemStateChanged = "TreasureDigItemStateChanged",
  TreasureDigGridChanged = "TreasureDigGridChanged",
  TestOrderGroupSelectItem = "TestOrderGroupSelectItem"
}
