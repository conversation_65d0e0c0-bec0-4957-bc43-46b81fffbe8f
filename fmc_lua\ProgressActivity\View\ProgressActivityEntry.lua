ProgressActivityEntry = setmetatable({}, HudGeneralButton)
ProgressActivityEntry.__index = ProgressActivityEntry

function ProgressActivityEntry:Init(model)
  self.m_model = model
  self.m_activityType = self.m_model:GetType()
  self.m_activityDefinition = ProgressActivityDefinition[self.m_activityType]
  self:UpdatePerSecond()
end

function ProgressActivityEntry:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  if self.m_model:GetState() == ActivityState.Ended or self.m_model:GetState() == ActivityState.Released then
    self.m_countDownText.text = GM.GameTextModel:GetText("countdown_finished")
  end
  local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
  self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
end

function ProgressActivityEntry:OnBtnClicked()
  if self.m_model:GetState() == ActivityState.Preparing then
    GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, false)
  elseif self.m_model:GetState() == ActivityState.Started then
    GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_activityType, false)
  end
end
