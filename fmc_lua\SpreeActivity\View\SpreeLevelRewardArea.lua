SpreeLevelRewardArea = setmetatable({RewardCellHeight = 300}, LevelRewardArea)
SpreeLevelRewardArea.__index = SpreeLevelRewardArea

function SpreeLevelRewardArea:Init(activityModel)
  self.m_activityModel = activityModel
  local arr = self.m_activityModel:GetLevelConfig()
  LevelRewardArea.Init(self, #arr)
end

function SpreeLevelRewardArea:GetProgressCell(level)
  local cellObject = Object.Instantiate(self.m_progressCellPrefab, self.m_sliderNode)
  local cell = cellObject:GetLuaTable()
  cell:Init(self.m_activityModel, level)
  return cell
end

function SpreeLevelRewardArea:_UpdatePositionContent()
  local level = self.m_activityModel:GetLevel()
  UIUtil.SetSizeDelta(self.m_sliderFillNode, nil, self:_GetSliderHeight(level))
  local scrollRect = self.transform:GetComponent(typeof(ScrollRect))
  scrollRect.verticalNormalizedPosition = self:_GetMiddleVerticalNormalizedPosition(level)
end

function SpreeLevelRewardArea:GetRewardCell(level)
  local cellObject = Object.Instantiate(self.m_rewardCellPrefab, self.m_rewardsNode)
  local cell = cellObject:GetLuaTable()
  cell:Init(self.m_activityModel, level)
  return cell
end

function SpreeLevelRewardArea:GetCurrentLevel()
  return self.m_activityModel:GetLevel()
end

function SpreeLevelRewardArea:UpdateContent()
  self:_UpdateLevelContent()
end

SpreeLevelRewardCell = setmetatable({}, LevelRewardCell)
SpreeLevelRewardCell.__index = SpreeLevelRewardCell

function SpreeLevelRewardCell:Init(activityModel, level)
  LevelRewardCell.Init(self, level)
  self.m_activityModel = activityModel
  self.m_rewards = self.m_activityModel:GetLevelRewards(level) or Table.Empty
  if #self.m_rewards == 1 then
    self:_SetRewardImage(1, Vector2(150, 150), Vector2.zero)
  elseif #self.m_rewards == 2 then
    self:_SetRewardImage(1, Vector2(140, 140), Vector2(-80, 0))
    self:_SetRewardImage(2, Vector2(140, 140), Vector2(80, 0))
  else
    self:_SetRewardImage(1, Vector2(130, 130), Vector2(-110, 0))
    self:_SetRewardImage(2, Vector2(130, 130), Vector2.zero)
    self:_SetRewardImage(3, Vector2(130, 130), Vector2(110, 0))
  end
end

function SpreeLevelRewardCell:OnDestroy()
  if self.m_receiveTween ~= nil then
    self.m_receiveTween:Kill()
  end
end

function SpreeLevelRewardCell:UpdateLevelContent(currentLevel)
  local imgRes
  if currentLevel < self.m_level then
    imgRes = ImageFileConfigName.spree_level_cell_bg1
  else
    imgRes = ImageFileConfigName.spree_level_cell_bg2
  end
  local isRewared = currentLevel >= self.m_level
  self.m_checkGo:SetActive(isRewared)
  self.m_maskGo:SetActive(isRewared)
  SpriteUtil.SetImage(self.m_backgroundImage, imgRes)
end

function SpreeLevelRewardCell:_SetRewardImage(index, size, anchoredPosition)
  local rewardImage = self["m_item" .. index .. "Img"]
  rewardImage.transform.anchoredPosition = anchoredPosition
  rewardImage.gameObject:SetActive(true)
  local reward = self.m_rewards[index]
  local rewardType = reward[PROPERTY_TYPE]
  local isPropertyType = GM.PropertyDataManager:IsPropertyType(rewardType)
  local spriteName = isPropertyType and EPropertySpriteBig[rewardType] or GM.ItemDataModel:GetSpriteName(rewardType)
  SpriteUtil.SetImage(rewardImage, spriteName, false, function()
    rewardImage.transform.sizeDelta = size
  end)
  local ali = GM.PropertyDataManager:IsPropertyType(reward[PROPERTY_TYPE]) and TextAnchor.LowerCenter or TextAnchor.LowerRight
  local num = GM.PropertyDataManager:IsPropertyType(reward[PROPERTY_TYPE]) and reward[PROPERTY_COUNT] or "X" .. reward[PROPERTY_COUNT]
  self["m_number" .. index].text = num
  if reward[PROPERTY_COUNT] == 1 then
    self["m_number" .. index].text = ""
  end
  self["m_number" .. index].alignment = ali
end

SpreeLevelProgressCell = setmetatable({}, LevelProgressCell)
SpreeLevelProgressCell.__index = SpreeLevelProgressCell

function SpreeLevelProgressCell:Init(activityModel, level)
  LevelProgressCell.Init(self, level)
  self.m_levelText.text = level
end

function SpreeLevelProgressCell:UpdateLevelContent(currentLevel)
  local image = self.transform:GetComponent(typeof(Image))
  local imgRes, textOutline
  if currentLevel < self.m_level then
    imgRes = ImageFileConfigName.spree_level_grade_bg1
    textOutline = "58597E"
  else
    imgRes = ImageFileConfigName.spree_level_grade_bg2
    textOutline = "008435"
  end
  SpriteUtil.SetImage(image, imgRes)
  local uiOutline = self.m_levelText.gameObject:GetComponent(typeof(CS.UIOutline))
  uiOutline:SetColor(UIUtil.ConvertHexColor2CSColor(textOutline))
end
