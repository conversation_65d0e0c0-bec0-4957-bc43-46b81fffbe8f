DashActivityIconArea = setmetatable({}, HudBaseButton)
DashActivityIconArea.__index = DashActivityIconArea

function DashActivityIconArea:Init(orderCell)
  self.m_orderCell = orderCell
end

function DashActivityIconArea:IconScaleAnimation(needEffect)
  HudBaseButton.IconScaleAnimation(self, needEffect)
  if self.m_orderCell:IsScoreUpdatedByFlyElementHit() then
    self.m_orderCell:UpdateScore()
  end
end
