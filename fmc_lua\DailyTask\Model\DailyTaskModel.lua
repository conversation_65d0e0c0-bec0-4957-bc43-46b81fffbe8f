DailyTaskModel = setmetatable({}, BaseActivityModel)
DailyTaskModel.__index = DailyTaskModel
local DBKey = {
  ResetTime = "rt",
  AllTaskFinished = "atf",
  FinalRewardAcquired = "fra"
}
local DBKeyScoreType = {
  [DailyTaskType.UseEnergy] = "sue",
  [DailyTaskType.CollectGold] = "scg",
  [DailyTaskType.ServeCustomer] = "ssc",
  [DailyTaskType.SpendGem] = "ssg"
}
local DBKeyFinishIndexPrefix = "ti"
local DBColumnValue = "value"
local Sec2Day = Sec2Day
local ConfMiscKey = {
  UICode = "daily_task_uiCode",
  FinalReward = "daily_task_final_reward"
}

function DailyTaskModel:Init(virtualDBTable)
  BaseActivityModel.Init(self, ActivityType.DailyTask, virtualDBTable)
  EventDispatcher.AddListener(EEventType.PropertyConsumed, self, self._OnPropertyConsumed)
  EventDispatcher.AddListener(EEventType.PropertyAcquired, self, self._OnProeprtyAcquired)
  EventDispatcher.AddListener(EEventType.OrderFinished, self, self._OnOrderFinished)
end

function DailyTaskModel:_OnStateChanged()
  EventDispatcher.DispatchEvent(EEventType.DailyTaskStateChanged)
end

function DailyTaskModel:_LoadOtherServerConfig(config)
  self.m_arrTaskDatas = {}
  for _, cfg in ipairs(config.daily_task_list) do
    self.m_arrTaskDatas[#self.m_arrTaskDatas + 1] = DailyTaskData.Create(cfg)
  end
  table.sort(self.m_arrTaskDatas, function(a, b)
    return a:GetIndex() < b:GetIndex()
  end)
  self.m_miscCfg = {}
  local miscConf = config.misc_conf
  for index, value in ipairs(miscConf) do
    self.m_miscCfg[value.confType] = value.param
    if value.confType == ConfMiscKey.FinalReward then
      self.m_miscCfg[value.confType] = {
        ConfigUtil.GetCurrencyFromStr(value.param)
      }
    end
  end
  self:UpdatePerSecond()
  EventDispatcher.DispatchEvent(EEventType.DailyTaskStateChanged)
end

function DailyTaskModel:GetResourceLabels()
  return {
    AddressableLabel.DailyTask
  }
end

function DailyTaskModel:UpdatePerSecond()
  BaseActivityModel.UpdatePerSecond(self)
  if self:GetState() == ActivityState.Started and self:_GetResetTime() ~= GM.GameModel:GetServerTime() // Sec2Day * Sec2Day then
    self:_ResetTaskData()
  end
end

function DailyTaskModel:_ResetTaskData()
  local id = self:GetId()
  self.m_dbTable:Drop()
  self.m_dbTable:Set("id", "value", id)
  self:_SetResetTime()
  self:LogActivity(EBIType.ActivityStarted)
  EventDispatcher.DispatchEvent(EEventType.DailyTaskStateChanged)
end

function DailyTaskModel:_GetTaskDBFinishKey(index)
  return DBKeyFinishIndexPrefix .. index
end

function DailyTaskModel:_SetTaskFinished(index)
  self.m_dbTable:Set(self:_GetTaskDBFinishKey(index), DBColumnValue, 1)
  if not self:HasAllTaskFinished() then
    for i = 1, self:GetTaskCount() do
      if not self:HasFinishedTask(i) then
        return
      end
    end
    self.m_dbTable:Set(DBKey.AllTaskFinished, DBColumnValue, 1)
  end
end

function DailyTaskModel:_GetResetTime()
  return self.m_dbTable:GetValue(DBKey.ResetTime, DBColumnValue)
end

function DailyTaskModel:_SetResetTime()
  local curTime = GM.GameModel:GetServerTime()
  self.m_dbTable:Set(DBKey.ResetTime, DBColumnValue, curTime // Sec2Day * Sec2Day)
end

function DailyTaskModel:GetScore(type)
  return self.m_dbTable:GetValue(DBKeyScoreType[type], DBColumnValue) or 0
end

function DailyTaskModel:_AddScore(type, delta)
  self.m_dbTable:Set(DBKeyScoreType[type], DBColumnValue, self:GetScore(type) + delta)
end

function DailyTaskModel:_HasFinalRewardsAcquired()
  return self.m_dbTable:GetValue(DBKey.FinalRewardAcquired, DBColumnValue) == 1
end

function DailyTaskModel:IsActivityOpen()
  return self:GetState() == ActivityState.Started and self:_GetResetTime() ~= nil and (not self:HasAllTaskFinished() or not self:_HasFinalRewardsAcquired())
end

function DailyTaskModel:HasAllTaskFinished()
  return self.m_dbTable:GetValue(DBKey.AllTaskFinished, DBColumnValue) == 1
end

function DailyTaskModel:HasFinishedTask(index)
  return self.m_dbTable:GetValue(self:_GetTaskDBFinishKey(index), DBColumnValue) == 1
end

function DailyTaskModel:GetTaskCount()
  return #self.m_arrTaskDatas
end

function DailyTaskModel:GetFinishedTaskCount()
  local finishedTask = 0
  local totalTask = self:GetTaskCount()
  for i = 1, totalTask do
    finishedTask = finishedTask + (self:HasFinishedTask(i) and 1 or 0)
  end
  return finishedTask
end

function DailyTaskModel:GetTaskData(index)
  return self.m_arrTaskDatas[index]
end

function DailyTaskModel:GetTaskType(index)
  return self.m_arrTaskDatas[index] and self.m_arrTaskDatas[index]:GetType()
end

function DailyTaskModel:GetUICode()
  return self.m_miscCfg and self.m_miscCfg[ConfMiscKey.UICode] or nil
end

function DailyTaskModel:GetMainWindowPrefabName()
  local uiCode = self:GetUICode() or "default"
  return DailyTaskMainWindowPrefabName[uiCode] or DailyTaskMainWindowPrefabName.default
end

function DailyTaskModel:GetCurRoundEndTime()
  return math.min(self.m_config.eTime or 0, (self:_GetResetTime() // Sec2Day + 1) * Sec2Day)
end

function DailyTaskModel:CanAcquireFinalRewards()
  return self:IsActivityOpen() and not self:_HasFinalRewardsAcquired() and self:HasAllTaskFinished()
end

function DailyTaskModel:GetFinalRewards()
  return self.m_miscCfg[ConfMiscKey.FinalReward]
end

function DailyTaskModel:AcquireFinalRewards()
  if self:_HasFinalRewardsAcquired() then
    return false
  end
  RewardApi.AcquireRewardsLogic(self:GetFinalRewards(), EPropertySource.Give, EBIType.DailyTaskFinalRewards, EGameMode.Board, CacheItemType.Stack)
  self:LogActivity(EBIType.ActivityGetRewards, 0)
  self.m_dbTable:Set(DBKey.FinalRewardAcquired, DBColumnValue, 1)
  EventDispatcher.DispatchEvent(EEventType.DailyTaskStateChanged)
  return true
end

function DailyTaskModel:_OnPropertyConsumed(msg)
  local type = msg.property[PROPERTY_TYPE]
  local score = msg.property[PROPERTY_COUNT]
  if msg.ext ~= nil and msg.ext.origin ~= nil then
    score = msg.ext.origin
  end
  if type == EPropertyType.Gem then
    self:_UpdateScore(DailyTaskType.SpendGem, score)
  elseif type == EPropertyType.Energy then
    self:_UpdateScore(DailyTaskType.UseEnergy, score)
  end
end

function DailyTaskModel:_OnProeprtyAcquired(msg)
  if msg.scene == EBIType.ItemSell then
    return
  end
  for _, property in ipairs(msg.arrProperties) do
    if property[PROPERTY_TYPE] == EPropertyType.Gold or property[PROPERTY_TYPE] == EPropertyType.BakeOutToken then
      self:_UpdateScore(DailyTaskType.CollectGold, property[PROPERTY_COUNT])
    end
  end
end

function DailyTaskModel:_OnOrderFinished(msg)
  self:_UpdateScore(DailyTaskType.ServeCustomer, 1)
end

function DailyTaskModel:_UpdateScore(type, num)
  if not self:IsActivityOpen() then
    return
  end
  self:_AddScore(type, num)
  self:_Try2FinishTasks()
end

function DailyTaskModel:_Try2FinishTasks()
  if self:HasAllTaskFinished() then
    return
  end
  for i = 1, self:GetTaskCount() do
    if not self:HasFinishedTask(i) then
      local data = self:GetTaskData(i)
      if self:GetScore(data:GetType()) >= data:GetScore() then
        self:_FinishTask(i)
      end
    end
  end
end

function DailyTaskModel:_FinishTask(index)
  if self:HasFinishedTask(index) or self:GetTaskData(index) == nil then
    return
  end
  RewardApi.AcquireRewardsLogic(self:GetTaskData(index):GetRewards(), EPropertySource.Give, EBIType.DailyTaskRewards, EGameMode.Board, CacheItemType.Stack)
  self:LogActivity(EBIType.ActivityGetRewards, index)
  self:_SetTaskFinished(index)
  self:LogActivity(EBIType.ActivityRankUp, index)
  EventDispatcher.DispatchEvent(EEventType.DailyTaskFinishTask, {
    data = self:GetTaskData(index)
  })
end

function DailyTaskModel:GetBoardEntryShowConfig()
  return {
    statusChangeName = EEventType.DailyTaskStateChanged,
    bubbleName = "m_dailyTaskBoardBubble",
    bubbleNodeName = "m_dailyTaskBoardBubbleNode",
    entryPrefabName = UIPrefabConfigName.DailyTaskBoardBubble,
    getBubbleFunName = "GetDailyTaskBoardBubble",
    scrollToFunName = "ScrollToDailyTaskBoardBubble",
    destroyFun = function(bubble)
      AddressableLoader.Destroy(bubble.gameObject)
    end,
    checkFun = function()
      return self:IsActivityOpen()
    end
  }
end

DailyTaskData = {}
DailyTaskData.__index = DailyTaskData

function DailyTaskData.Create(config)
  local data = setmetatable({}, DailyTaskData)
  data:Init(config)
  return data
end

function DailyTaskData:Init(config)
  self.m_index = config.index
  self.m_rewards = {
    ConfigUtil.GetCurrencyFromStr(config.reward)
  }
  self.m_type = config.type
  self.m_num = config.num
end

function DailyTaskData:GetIndex()
  return self.m_index
end

function DailyTaskData:GetRewards()
  return self.m_rewards
end

function DailyTaskData:GetType()
  return self.m_type
end

function DailyTaskData:GetScore()
  return self.m_num
end
