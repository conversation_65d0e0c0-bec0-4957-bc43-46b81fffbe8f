PropertyButton = setmetatable({}, SimpleButton)
PropertyButton.__index = PropertyButton

function PropertyButton:Init(propertyType, count, callback)
  SimpleButton.Init(self, count, callback)
  if self.m_propertyType ~= propertyType then
    self.m_iconImg.gameObject:SetActive(false)
    self.m_propertyType = propertyType
    SpriteUtil.SetImage(self.m_iconImg, EPropertySpriteBig[propertyType], true, function()
      self.m_iconImg.gameObject:SetActive(true)
      self:_MiddleIconAndText()
    end)
  else
    self:_MiddleIconAndText()
  end
end

function PropertyButton:_MiddleIconAndText()
  self.m_text.transform.sizeDelta = Vector2(math.min(self.transform.sizeDelta.x - 150, self.m_text.preferredWidth), self.m_text.transform.sizeDelta.y)
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_wrapTrans)
end
