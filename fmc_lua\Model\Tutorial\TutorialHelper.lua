TutorialHelper = {}

function TutorialHelper.GetSceneView()
  return GM.UIManager:GetOpenedTopViewByType(EViewType.SceneView)
end

function TutorialHelper.GetTutorialLayer()
  return TutorialHelper.GetSceneView():GetTutorialLayer()
end

function TutorialHelper.GetSceneViewAndTutorialLayer()
  local sceneView = TutorialHelper.GetSceneView()
  return sceneView, sceneView:GetTutorialLayer()
end

function TutorialHelper.HideTutorialLayer(gesture, arrow)
  TutorialHelper.GetTutorialLayer():Hide(gesture, arrow)
end

function TutorialHelper.FinishTutorial(gesture, arrow)
  TutorialHelper.GetTutorialLayer():Finish(gesture, arrow)
end

function TutorialHelper.GetActivityDefinition(activityType)
  return SpreeActivityDefinition[activityType]
end

function TutorialHelper.HideMask()
  TutorialHelper.GetTutorialLayer():HideMask()
end

function TutorialHelper.UpdateMask(center, size, callback, clipRectClickable)
  TutorialHelper.GetTutorialLayer():UpdateMask(center or V3Zero, size or V2Zero, callback, clipRectClickable)
end

function TutorialHelper.WholeMask(callback)
  TutorialHelper.GetTutorialLayer():UpdateMask(V3Zero, V2Zero, callback)
end

function TutorialHelper.UpdateMaskOnBoard(center, size, callback, clipRectClickable)
  TutorialHelper.GetTutorialLayer():UpdateMask(center + TutorialLayer.CameraOffset, size, callback, clipRectClickable)
end

function TutorialHelper.MaskOnItemBoard(from, to, callback, clipRectClickable)
  TutorialHelper.GetTutorialLayer():UpdateMaskOnBoard(from, to, callback, clipRectClickable)
end

function TutorialHelper.SetMaskAlphaOnce(alpha)
  TutorialHelper.GetTutorialLayer():SetMaskAlphaOnce(alpha)
end

function TutorialHelper.MaskOnGivenBoard(boardView, from, to, callback, clipRectClickable)
  TutorialHelper.GetTutorialLayer():UpdateMaskOnGivenBoard(boardView, from, to, callback, clipRectClickable)
end

function TutorialHelper.HighlightForUI(transform, disableInteraction)
  TutorialHelper.GetTutorialLayer():HighlightForUI(transform, disableInteraction)
end

function TutorialHelper.DehighlightForUI(transform)
  TutorialHelper.GetTutorialLayer():DehighlightForUI(transform)
end

function TutorialHelper.HighlightHudButton(hudButtonKey, disableInteraction, arrHudAnchorTypes)
  local sceneView, tutorialLayer = TutorialHelper.GetSceneViewAndTutorialLayer()
  sceneView:HudForceImmediatelyShow(arrHudAnchorTypes)
  local hudButtonTransform = sceneView:GetHudButton(hudButtonKey).transform
  tutorialLayer:HighlightForUI(hudButtonTransform, disableInteraction)
  return hudButtonTransform
end

function TutorialHelper.DehighlightHudButton(hudButtonKey, arrHudAnchorTypes)
  local sceneView, tutorialLayer = TutorialHelper.GetSceneViewAndTutorialLayer()
  sceneView:HudForceImmediatelyHide(arrHudAnchorTypes)
  local hudButton = sceneView:GetHudButton(hudButtonKey)
  if hudButton ~= nil then
    tutorialLayer:DehighlightForUI(hudButton.transform)
  end
end

function TutorialHelper.HighlightForBoard(transform)
  TutorialHelper.GetTutorialLayer():HighlightForBoard(transform)
end

function TutorialHelper.DehighlightForBoard(transform)
  TutorialHelper.GetTutorialLayer():DehighlightForBoard(transform)
end

function TutorialHelper.HighlightOrder(order)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    local orderCell = orderArea:GetCell(order)
    if orderCell ~= nil then
      orderCell.highlightForTutorial = true
      TutorialHelper.ScrollToNormalOrder(orderCell, false)
      TutorialHelper.GetTutorialLayer():HighlightForBoard(orderCell.transform)
    end
    return orderCell
  end
end

function TutorialHelper.HighlightDashActivityOrder()
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    local orderCell = orderArea:GetDashActivityOrder()
    if orderCell ~= nil then
      TutorialHelper.GetTutorialLayer():HighlightForBoard(orderCell.transform)
    end
    return orderCell
  end
end

function TutorialHelper.HighlightBakeOutBubble()
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    local bubble = orderArea:GetOrderTbByActivityType(ActivityType.BakeOut)
    if bubble ~= nil and bubble.gameObject.activeSelf then
      TutorialHelper.GetTutorialLayer():HighlightForBoard(bubble.transform)
      return bubble
    end
  end
end

function TutorialHelper.HighlightDailyTaskBoardBubble()
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    local cell = orderArea:GetDailyTaskBoardBubble()
    if cell ~= nil and cell.gameObject.activeSelf then
      TutorialHelper.GetTutorialLayer():HighlightForBoard(cell.transform)
      return cell
    end
  end
end

function TutorialHelper.DehighlightOrder(orderCell)
  orderCell.highlightForTutorial = nil
  TutorialHelper.GetTutorialLayer():DehighlightForBoard(orderCell.transform)
end

function TutorialHelper.CanHighlightBoardTask()
  local taskCell = TutorialHelper._GetBoardTaskBubble()
  return not taskCell:IsPlayingAnimation()
end

function TutorialHelper.HighlightBoardTask()
  local taskCell = TutorialHelper._GetBoardTaskBubble()
  if taskCell ~= nil then
    if not GM.ConfigModel:UseNewCacheLayout() then
      TutorialHelper.ScrollToFront(false)
    else
      TutorialHelper.ScrollToBoardTaskBubble(false)
    end
    TutorialHelper.GetTutorialLayer():HighlightForBoard(taskCell.transform)
  end
  return taskCell
end

function TutorialHelper.DehighlightBoardTask()
  local taskCell = TutorialHelper._GetBoardTaskBubble()
  if taskCell ~= nil then
    TutorialHelper.GetTutorialLayer():DehighlightForBoard(taskCell.transform)
  end
end

function TutorialHelper.HighlightCacheRoot()
  local cacheRoot = TutorialHelper._GetBoardCacheRoot()
  if cacheRoot ~= nil then
    if not GM.ConfigModel:UseNewCacheLayout() then
      TutorialHelper.ScrollToFront(false)
    else
      TutorialHelper.ScrollToBoardCacheRoot(false)
    end
    local targetScale = BoardCacheRoot.GetCacheElementScale()
    TutorialHelper.GetTutorialLayer():HighlightForBoard(cacheRoot.transform, false, Vector3(targetScale, targetScale, 1))
  end
  return cacheRoot
end

function TutorialHelper.DehighlightCacheRoot()
  local cacheRoot = TutorialHelper._GetBoardCacheRoot()
  if cacheRoot ~= nil then
    TutorialHelper.GetTutorialLayer():DehighlightForBoard(cacheRoot.transform)
  end
end

function TutorialHelper.HighlightOrderGroup()
  local orderGroup = TutorialHelper._GetBoardOrderGroup()
  if orderGroup ~= nil then
    TutorialHelper.ScrollToFront(false)
    TutorialHelper.GetTutorialLayer():HighlightForBoard(orderGroup.transform)
  end
  return orderGroup
end

function TutorialHelper.DehighlightOrderGroup()
  local orderGroup = TutorialHelper._GetBoardOrderGroup()
  if orderGroup ~= nil then
    TutorialHelper.GetTutorialLayer():DehighlightForBoard(orderGroup.transform)
  end
end

function TutorialHelper.UpdateBoardHighlightPos()
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    TutorialHelper.GetTutorialLayer():UpdateBoardHighlightPos(boardView)
  end
end

function TutorialHelper.HighlightBoardInfoBar(forInventory)
  local infoBar = TutorialHelper._GetBoardInfoBar()
  if infoBar ~= nil then
    TutorialHelper.GetTutorialLayer():HighlightForBoard(infoBar.transform, forInventory)
  end
  return infoBar
end

function TutorialHelper.DehighlightBoardInfoBar()
  local infoBar = TutorialHelper._GetBoardInfoBar()
  if infoBar ~= nil then
    TutorialHelper.GetTutorialLayer():DehighlightForBoard(infoBar.transform)
  end
end

function TutorialHelper.GetBoardInfoTipButton()
  local infoBar = TutorialHelper._GetBoardInfoBar()
  local go = infoBar and infoBar:GetInfoBtnGo()
  return go and go:GetLuaTable()
end

function TutorialHelper.ToggleBoardHighlightRaycast(enable)
  TutorialHelper.GetTutorialLayer():ToggleBoardHighlightRaycast(enable)
end

function TutorialHelper.HighlightBoardInfoSkipBtnGo()
  local infoBar = TutorialHelper._GetBoardInfoBar():GetSkipBtnGo()
  if infoBar ~= nil then
    TutorialHelper.GetTutorialLayer():HighlightForBoard(infoBar.transform)
  end
  return infoBar
end

function TutorialHelper.DehighlightBoardInfoSkipBtnGo()
  local infoBar = TutorialHelper._GetBoardInfoBar():GetSkipBtnGo()
  if infoBar ~= nil then
    TutorialHelper.GetTutorialLayer():DehighlightForBoard(infoBar.transform)
    TutorialHelper._GetBoardInfoBar():AdjustDescription()
  end
end

function TutorialHelper.HighlightBoardInfoCookBtnGo()
  local infoBar = TutorialHelper._GetBoardInfoBar():GetCookBtnGo()
  if infoBar ~= nil then
    TutorialHelper.GetTutorialLayer():HighlightForBoard(infoBar.transform)
  end
  return infoBar
end

function TutorialHelper.DehighlightBoardInfoCookBtnGo()
  local infoBar = TutorialHelper._GetBoardInfoBar():GetCookBtnGo()
  if infoBar ~= nil then
    TutorialHelper.GetTutorialLayer():DehighlightForBoard(infoBar.transform)
  end
end

function TutorialHelper.HighlightBoardCookBubbleBtnGo()
  local cookBtnGo = MainBoardView:GetInstance():GetCookBubble().gameObject
  TutorialHelper.GetTutorialLayer():HighlightForBoard(cookBtnGo.transform)
  return cookBtnGo
end

function TutorialHelper.DehighlightBoardCookBubbleBtnGo()
  local cookBtnGo = MainBoardView:GetInstance():GetCookBubble().gameObject
  TutorialHelper.GetTutorialLayer():DehighlightForBoard(cookBtnGo.transform)
end

function TutorialHelper.HighlightBoardInfoCookMaterials()
  local infoBar = TutorialHelper._GetBoardInfoBar():GetCookMaterialsGo()
  if infoBar ~= nil then
    TutorialHelper.GetTutorialLayer():HighlightForBoard(infoBar.transform)
  end
  return infoBar
end

function TutorialHelper.DehighlightBoardInfoCookMaterials()
  local infoBar = TutorialHelper._GetBoardInfoBar():GetCookMaterialsGo()
  if infoBar ~= nil then
    TutorialHelper.GetTutorialLayer():DehighlightForBoard(infoBar.transform)
  end
end

function TutorialHelper.TapOnBoardInfoCookFirstMaterial()
  local trans = TutorialHelper._GetBoardInfoBar():GetCookFirstMaterialTrans()
  local position = trans.position
  position.z = 0
  return TutorialHelper.GetTutorialLayer():PlayTapAnimation(position, 0.9)
end

function TutorialHelper.HighlightBoardInfoUnlockBtnGo()
  local infoBar = TutorialHelper._GetBoardInfoBar():GetUnlockBtnGo()
  if infoBar ~= nil then
    TutorialHelper.GetTutorialLayer():HighlightForBoard(infoBar.transform)
  end
  return infoBar
end

function TutorialHelper.DehighlightBoardInfoUnlockBtnGo()
  local infoBar = TutorialHelper._GetBoardInfoBar():GetUnlockBtnGo()
  if infoBar ~= nil then
    TutorialHelper.GetTutorialLayer():DehighlightForBoard(infoBar.transform)
    TutorialHelper._GetBoardInfoBar():AdjustDescription()
  end
end

function TutorialHelper.HighlightActivityBoardEntry(activityType)
  local entryTb = TutorialHelper.GetActivityBoardEntry(activityType)
  if entryTb ~= nil and entryTb.gameObject ~= nil and not entryTb.gameObject:IsNull() then
    TutorialHelper.HighlightForBoard(entryTb.transform)
    return entryTb.transform
  end
end

function TutorialHelper.DehighlightActivityBoardEntry(activityType)
  local entryTb = TutorialHelper.GetActivityBoardEntry(activityType)
  if entryTb ~= nil and entryTb.gameObject ~= nil and not entryTb.gameObject:IsNull() then
    TutorialHelper.DehighlightForBoard(entryTb.transform)
    return entryTb.transform
  end
end

function TutorialHelper.HighlightItems(itemDatasArr)
  TutorialHelper.GetTutorialLayer():HighlightItems(itemDatasArr)
end

function TutorialHelper.UnHighlightItems()
  TutorialHelper.GetTutorialLayer():UnHighlightItems()
end

function TutorialHelper.HideGesture(gesture)
  TutorialHelper.GetTutorialLayer():HideGesture(gesture)
end

function TutorialHelper.TapCustomPos(worldPos)
  return TutorialHelper.GetTutorialLayer():PlayTapAnimation(worldPos)
end

function TutorialHelper.TapOnHudButton(hudButtonKey, offset, scale)
  local sceneView, tutorialLayer = TutorialHelper.GetSceneViewAndTutorialLayer()
  local position = sceneView:GetHudButton(hudButtonKey).transform.position
  if offset then
    position = position + offset
  end
  return tutorialLayer:PlayTapAnimation(position, scale)
end

function TutorialHelper.TapOnCustomRectTrans(rectTrans)
  return TutorialHelper.TapCustomPos(rectTrans.position)
end

function TutorialHelper.TapOnBoard(boardPos, select)
  local activeBoardModel = BoardModelHelper.GetActiveModel()
  if not activeBoardModel then
    return nil
  end
  local item = activeBoardModel:GetItem(boardPos)
  if not item or not activeBoardModel:CanItemMove(item) then
    Log.Error("引导点击不合法 pos:" .. BoardPosition.__tostring(boardPos))
    return nil
  end
  select = select == nil and true or select
  local boardView = MainBoardView.GetInstance()
  if select and boardView ~= nil then
    boardView:UpdateSelectedItem(item)
  end
  return TutorialHelper.GetTutorialLayer():PlayTapOnBoardAnimation(boardPos)
end

function TutorialHelper.TapOnGivenBoard(boardView, boardPos)
  return TutorialHelper.GetTutorialLayer():PlayTapOnGivenBoardAnimation(boardView, boardPos)
end

function TutorialHelper.TapOnItem(itemModel)
  return TutorialHelper.TapOnBoard(itemModel:GetPosition())
end

function TutorialHelper.TapOnBoardInfoBarButton(btnGo)
  local position = btnGo.transform.position - Vector3(0, 30, 0)
  position.z = 0
  return TutorialHelper.GetTutorialLayer():PlayTapAnimation(position, 0.9)
end

function TutorialHelper.TapOnBoardInfoBarCookStartButtonForPrompt()
  local btnGo = TutorialHelper._GetBoardInfoBar():GetCookBtnGo()
  local position = btnGo.transform.position + Vector3(-8, 8, 0) + TutorialLayer.CameraOffset
  position.z = 0
  return TutorialHelper.GetTutorialLayer():PlayTapAnimation(position, 0.9)
end

function TutorialHelper.DragOnItems(from, to, boardView)
  local activeBoardModel = BoardModelHelper.GetActiveModel()
  if not activeBoardModel then
    return nil
  end
  if not activeBoardModel:CanItemAtPositionTutorial(from, to) then
    Log.Error("引导拖动不合法 from:" .. BoardPosition.__tostring(from) .. " to:" .. BoardPosition.__tostring(to))
    return nil
  end
  local item = BoardModelHelper.GetActiveModel():GetItem(from)
  local spriteImg = GM.ItemDataModel:GetSpriteName(item:GetType())
  return TutorialHelper.GetTutorialLayer():PlayDragOnBoardAnimation(from, to, spriteImg, boardView)
end

function TutorialHelper.PlayDragAnimation(fromPosition, toPosition, spriteImg)
  return TutorialHelper.GetTutorialLayer():PlayDragAnimation(fromPosition, toPosition, spriteImg)
end

function TutorialHelper.MaskDragToInventory(pos, text, callback, clipRectClickable)
  local hudButton = TutorialHelper.GetHudButton(ESceneViewHudButtonKey.Inventory)
  local worldPos = hudButton.transform.position
  local boardview = BoardViewHelper.GetActiveView()
  local boardModel = boardview:GetModel()
  GM.TutorialModel:SetForceSourceBoardPosition(pos)
  GM.TutorialModel:SetForceTargetBoardPosition(boardModel:CreatePosition(0, 0))
  local screenPosition = boardview:ConvertBoardPositionToScreenPosition(pos)
  local from = PositionUtil.UICameraScreen2World(screenPosition)
  local boardUnitSize = TutorialHelper.GetTutorialLayer():GetBoardUnitSize()
  boardUnitSize = boardUnitSize * 0.625
  local maskFrom = from + Vector3(boardUnitSize, boardUnitSize, 0)
  local maskTo = worldPos - Vector3(75, 75, 0)
  local center = (maskFrom + maskTo) / 2
  local dis = maskTo - maskFrom
  local size = Vector2(math.abs(dis.x), math.abs(dis.y))
  TutorialHelper.UpdateMask(center, size, callback, clipRectClickable)
  local anchorPercent = (1 - screenPosition.y / Screen.height) * 100
  TutorialHelper.ShowDialog(text, anchorPercent - 25)
  local item = BoardModelHelper.GetActiveModel():GetItem(pos)
  local spriteImg = GM.ItemDataModel:GetSpriteName(item:GetType())
  return TutorialHelper.GetTutorialLayer():PlayDragAnimation(from, worldPos, spriteImg)
end

function TutorialHelper.HideDialog()
  TutorialHelper.GetTutorialLayer():HideDialog()
end

function TutorialHelper.ShowDialog(text, anchorPercent, speakerName, flip)
  TutorialHelper.GetTutorialLayer():ShowText(text, anchorPercent, speakerName, flip)
end

function TutorialHelper.UpdateTextAnchorPercent(anchorPercent)
  TutorialHelper.GetTutorialLayer():UpdateTextAnchorPercent(anchorPercent)
end

function TutorialHelper.ShowDialogWithBoardMaskArea(text, boardPos1, boardPos2, speakerName, flip)
  TutorialHelper.GetTutorialLayer():ShowTextWithBoardMaskArea(text, boardPos1, boardPos2, speakerName, flip)
end

function TutorialHelper.ShowDialogWithGivenBoardMaskArea(boardView, text, boardPos1, boardPos2, speakerName, flip, interval)
  TutorialHelper.GetTutorialLayer():ShowTextWithGivenBoardMaskArea(boardView, text, boardPos1, boardPos2, speakerName, flip, interval)
end

function TutorialHelper.ShowDialogContinueText()
  TutorialHelper.GetTutorialLayer():ShowDialogContinueText()
end

function TutorialHelper.ShowDialogWithTrans(text, tragetTrans, deltaPercent, guideAvatarKey, flip)
  local screenHeight = Screen.height
  local screenPosition = PositionUtil.UICameraWorld2Screen(tragetTrans.position).y
  local anchorPercent = (1 - screenPosition / screenHeight) * 100
  deltaPercent = deltaPercent or 0
  TutorialHelper.ShowDialog(text, anchorPercent + deltaPercent, guideAvatarKey, flip)
end

function TutorialHelper.ShowDialogWithTransAuto(text, tragetTrans, guideAvatarKey, flip)
  local screenHeight = Screen.height
  local screenPosition = PositionUtil.UICameraWorld2Screen(tragetTrans.position).y
  local anchorPercent = (1 - screenPosition / screenHeight) * 100
  local deltaPercent = 20
  if 90 <= anchorPercent + deltaPercent then
    deltaPercent = -deltaPercent
  end
  TutorialHelper.ShowDialog(text, anchorPercent + deltaPercent, guideAvatarKey, flip)
end

function TutorialHelper.UpdateDialogSortingOrder(sortingOrder)
  TutorialHelper.GetTutorialLayer():UpdateDialogSortingOrder(sortingOrder)
end

function TutorialHelper.HideArrow(arrow)
  TutorialHelper.GetTutorialLayer():HideArrow(arrow)
end

function TutorialHelper.AddArrow2HudButton(hudButtonKey, rotateZ)
  local sceneView, tutorialLayer = TutorialHelper.GetSceneViewAndTutorialLayer()
  local hudButtonTransform = sceneView:GetHudButton(hudButtonKey).gameObject.transform
  local arrow = tutorialLayer:AddArrow(hudButtonTransform.position, rotateZ, hudButtonTransform.sizeDelta.y / 2)
  return arrow
end

function TutorialHelper.AddArrow2CustomRectTrans(rectTransform, rotateZ)
  local sceneView, tutorialLayer = TutorialHelper.GetSceneViewAndTutorialLayer()
  local arrow = tutorialLayer:AddArrow(rectTransform.position, rotateZ, rectTransform.sizeDelta.y / 2)
  return arrow
end

function TutorialHelper.GetItems(itemCode)
  return GM.MainBoardModel:FilterItems(function(itemModel)
    return itemModel:GetCode() == itemCode and itemModel:GetComponent(ItemLocker) == nil
  end)
end

function TutorialHelper.SpeedUpSpreadItem(itemType)
  local items = GM.MainBoardModel:GetItemsOfType(itemType)
  if 0 < #items then
    local spreader
    for _, item in ipairs(items) do
      spreader = item:GetComponent(ItemSpread)
      spreader:SpeedUp()
    end
  end
end

function TutorialHelper.GetSelectedItem()
  local boardView = BoardViewHelper.GetActiveView()
  return boardView and boardView:GetSelectedItemModel()
end

function TutorialHelper.GetMainFirstOrderCell()
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    local orderRoot = orderArea:GetOrderRoot()
    if orderRoot.childCount == 0 then
      return nil
    end
    local orderCell = orderRoot:GetChild(0)
    if orderCell ~= nil then
      return orderCell.gameObject:GetLuaTable()
    end
  end
  return nil
end

function TutorialHelper.GetMainOrderCell(order)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    return orderArea:GetCell(order)
  end
  return nil
end

function TutorialHelper.ScrollToNormalOrder(orderCell, needAnim)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    orderArea:ForceRebuildLayout()
    orderArea:ScrollToRectTransformVisible(orderCell.transform, needAnim)
  end
end

function TutorialHelper.ScrollToFront(needAnim)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    if orderArea ~= nil then
      orderArea:ScrollToFront(needAnim)
    end
  end
end

function TutorialHelper.ScrollToBoardCacheRoot(needAnim)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    if orderArea ~= nil then
      orderArea:ScrollToBoardCacheRoot(needAnim)
    end
  end
end

function TutorialHelper.ScrollToBoardTaskBubble(needAnim)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    if orderArea ~= nil then
      orderArea:ScrollToBoardTaskBubble(needAnim)
    end
  end
end

function TutorialHelper.ScrollToMainOrderRoot(needAnim)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    if orderArea ~= nil then
      orderArea:ScrollToOrderRoot(needAnim)
    end
  end
end

function TutorialHelper.ScrollToDashActivity(needAnim)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    if orderArea ~= nil then
      orderArea:ScrollToDashActivityOrder(needAnim)
    end
  end
end

function TutorialHelper.ScrollToMainBakeOut(needAnim)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    if orderArea ~= nil then
      orderArea:ScrollToActivityEntry(ActivityType.BakeOut, needAnim)
    end
  end
end

function TutorialHelper.ScrollToDailyTaskBoardBubble(needAnim)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    if orderArea ~= nil then
      orderArea:ScrollToDailyTaskBoardBubble(needAnim)
    end
  end
end

function TutorialHelper.ScrollToActivityEntry(activityType, needAnim)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    if orderArea ~= nil then
      orderArea:ScrollToActivityEntry(activityType, needAnim)
    end
  end
end

function TutorialHelper.ShowTutorialBoard(tutorialBoardName)
  GM.UIManager:OpenView(UIPrefabConfigName.TutorialBoardWindow, tutorialBoardName)
end

function TutorialHelper.GetHudButton(hudButonKey)
  return TutorialHelper.GetSceneView():GetHudButton(hudButonKey)
end

function TutorialHelper.GetActivityBoardEntry(activityType)
  local boardView = MainBoardView.GetInstance()
  if boardView ~= nil then
    local orderArea = boardView:GetOrderArea()
    local boardEntry = orderArea:GetOrderTbByActivityType(activityType)
    if boardEntry ~= nil and boardEntry.gameObject.activeSelf then
      return boardEntry
    end
  end
end

function TutorialHelper._GetBoardInfoBar()
  local boardView = BoardViewHelper.GetActiveView()
  return boardView and boardView:GetInfoBar()
end

function TutorialHelper._GetBoardTaskBubble()
  local boardView = MainBoardView.GetInstance()
  return boardView and boardView:GetOrderArea():GetTaskBubble()
end

function TutorialHelper._GetBoardCacheRoot()
  local boardView = BoardViewHelper.GetActiveView()
  return boardView and boardView:GetOrderArea():GetBoardCacheRoot()
end

function TutorialHelper._GetBoardOrderGroup()
  local boardView = BoardViewHelper.GetActiveView()
  return boardView and boardView:GetOrderArea():GetOrderGroupButton()
end
