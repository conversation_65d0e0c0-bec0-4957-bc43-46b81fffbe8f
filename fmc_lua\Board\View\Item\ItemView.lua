local EAnimationType = {
  SpreadReady = 1,
  Tap = 2,
  Prompt = 3,
  PromptBack = 4,
  BubbleIdle = 5,
  TimeSkip = 6,
  Drop = 7,
  MergeLightDisappear = 8,
  Retrive = 9
}
ItemView = {}
ItemView.__index = ItemView
ItemView.MoveDuration = 0.3

function ItemView:Init(itemModel, inBoard, onSpineLoaded)
  if inBoard == nil then
    inBoard = true
  end
  self.m_model = itemModel
  self.m_components = {}
  self.m_tweens = {}
  self.inBoard = inBoard
  if inBoard then
    self.transform.localPosition = self:GetLocalPosition(self.m_model:GetPosition())
    if GameConfig.IsTestMode() then
      self.gameObject.name = self.m_model:GetPosition():GetX() .. "_" .. self.m_model:GetPosition():GetY()
    end
  end
  self:RestoreSpriteMaterial()
  local itemCobweb = itemModel:GetComponent(ItemCobweb)
  local itemRewardBubble = itemModel:GetComponent(ItemRewardBubble)
  if itemCobweb ~= nil then
    local spriteName = GM.ItemDataModel:GetSpriteName(itemCobweb:GetInnerItemCode())
    SpriteUtil.SetSpriteRenderer(self.m_spriteRenderer, spriteName)
    self:ChangeSpriteMaterial2Gray()
  elseif itemRewardBubble ~= nil then
  else
    local spriteName = GM.ItemDataModel:GetSpriteName(itemModel:GetType())
    SpriteUtil.SetSpriteRenderer(self.m_spriteRenderer, spriteName)
  end
  local itemBubble = itemModel:GetComponent(ItemBubble)
  if itemBubble ~= nil then
    local innerSpriteName = GM.ItemDataModel:GetSpriteName(itemBubble:GetInnerItemCode())
    SpriteUtil.SetSpriteRenderer(self.m_innerSpriteRenderer, innerSpriteName)
    self:_PlayBubbleIdleAnimation()
  end
  local spinePrefab = itemModel:GetType() .. "_spine"
  if GM.DataResource.ScenePrefabConfig:HasConfig(spinePrefab) then
    GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(spinePrefab), self.m_rootTrans, Vector3.zero, function(go)
      self.m_spine = go:GetLuaTable()
      self.m_spine:Init()
      self.m_spriteRenderer.gameObject:SetActive(false)
      self:OnSpineLoaded()
      if onSpineLoaded then
        onSpineLoaded(self)
      end
    end)
  elseif onSpineLoaded then
    onSpineLoaded(self)
  end
  AddHandlerAndRecordMap(self.m_model.event, ItemEventType.SetPosition, {
    obj = self,
    method = self._MoveToBoardPosition
  })
  self.m_checkGo.transform.localScale = V3Zero
  self.m_checkGoVisible = false
  self.m_starGo.transform.localScale = V3Zero
  self.m_starGoVisible = false
  self:_UpdateStar()
  if self.m_model:IsShowCollectEffect() then
    self:ShowMaxCollectEffect()
  end
  local itemSpread = itemModel:GetComponent(ItemSpread)
  if itemSpread ~= nil then
    if itemSpread:GetState() == ItemSpreadState.Closed then
      self:UpdateExclamation(not itemModel:GetBoardModel():HasOpeningItem())
    else
      self:UpdateExclamation(false)
    end
  else
    self:UpdateExclamation(false)
  end
  EventDispatcher.AddListener(EEventType.OrderItemCookPopTip, self, self._OnOrderItemCookPopTip)
end

function ItemView:AddComponent(component)
  component:SetItemView(self)
  self.m_components[getmetatable(component)] = component
end

function ItemView:RemoveComponent(component)
  self.m_components[getmetatable(component)] = nil
end

function ItemView:OnSpineLoaded()
  for _, component in pairs(self.m_components) do
    component:OnSpineLoaded(self.m_spine)
  end
end

function ItemView:_GetComponent(type)
  return self.m_components[type]
end

function ItemView:GetModel()
  return self.m_model
end

function ItemView:GetSpine()
  return self.m_spine
end

function ItemView:GetSpriteRenderer()
  return self.m_spriteRenderer
end

function ItemView:_ChangeSpriteMaterial(material)
  if not self.m_originalMaterial then
    self.m_originalMaterial = self.m_spriteRenderer.material
  end
  self.m_spriteRenderer.material = material
end

function ItemView:ChangeSpriteMaterial2Gray()
  if self.m_bGray then
    return
  end
  self.m_bGray = true
  self:_ChangeSpriteMaterial(self.m_cobwebMaterial)
end

function ItemView:RestoreSpriteMaterial()
  if not self.m_bGray then
    return
  end
  self.m_bGray = false
  if self.m_originalMaterial then
    self:_ChangeSpriteMaterial(self.m_originalMaterial)
  end
end

function ItemView:GetInnerSpriteRenderer()
  return self.m_innerSpriteRenderer
end

function ItemView:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  RemoveAllHandlers(self.m_model.event, self)
  for _, tween in pairs(self.m_tweens) do
    if tween:IsActive() then
      tween:Kill()
    end
  end
end

function ItemView:UpdateChecked(visible)
  if self.m_checkGoVisible == visible then
    return
  end
  self.m_checkGoVisible = visible
  self.m_checkGo:SetActive(true)
  local checkTrans = self.m_checkGo.transform
  checkTrans:DOKill()
  if visible then
    checkTrans:DOScale(1, 0.2)
  else
    checkTrans:DOScale(0, 0.2):OnComplete(function()
      self.m_checkGo:SetActive(false)
    end)
  end
end

function ItemView:UpdateExclamation(show)
  if show ~= self.m_exclamationGo.activeSelf then
    self.m_exclamationGo:SetActive(show)
  end
end

function ItemView:_UpdateStar()
  if self.m_model:CanShowMaxLevelIcon() then
    self:_UpdateStarVisible(true)
  end
end

function ItemView:_UpdateStarVisible(visible)
  visible = visible and self.inBoard
  if self.m_starGoVisible == visible then
    return
  end
  self.m_starGoVisible = visible
  self.m_starGo:SetActive(true)
  local starTrans = self.m_starGo.transform
  starTrans:DOKill()
  if visible then
    starTrans:DOScale(1, 0.2)
  else
    starTrans:DOScale(0, 0.2):OnComplete(function()
      self.m_starGo:SetActive(false)
    end)
  end
end

function ItemView:SetFlying(flying)
  self.m_bFlying = flying
  UIUtil.SetActive(self.m_hintGo, not flying)
  for _, component in pairs(self.m_components) do
    component:SetFlying(flying)
  end
  EventDispatcher.DispatchEvent(EEventType.ItemFlyChanged)
end

function ItemView:IsFlying()
  return self.m_bFlying
end

function ItemView:_MoveToBoardPosition(boardPosition)
  if not self.inBoard then
    return
  end
  self:TryStopJumpTween()
  if self.m_moveTween then
    self.m_moveTween:Kill(true)
  end
  local localPosition = self:GetLocalPosition(boardPosition)
  local localPositionXY = Vector3(localPosition.x, localPosition.y, self.transform.localPosition.z)
  self.m_moveTween = self.transform:DOLocalMove(localPositionXY, ItemView.MoveDuration):SetEase(Ease.OutBack):OnComplete(function()
    self.transform.localPosition = localPosition
    self.m_moveTween = nil
  end)
end

function ItemView:GetLocalPosition(boardPosition)
  local localPosition = boardPosition:ToLocalPosition()
  local boardModel = self.m_model:GetBoardModel()
  local zIndex = boardModel:GetHorizontalTiles() * boardModel:GetVerticalTiles() - boardModel:GetHorizontalTiles() * (boardPosition:GetY() - 1) - boardPosition:GetX() + 2
  return Vector3(localPosition.x + BaseBoardModel.TileSize / 2, localPosition.y + BaseBoardModel.TileSize / 2, zIndex * 10)
end

function ItemView:MergeLightAppear()
  self.m_mergeLightObject = Object.Instantiate(self.m_mergeLightContinuePrefab, self.transform)
  self.m_mergeLightObject.transform.localPosition = Vector3(0, 0, -self.transform.localPosition.z)
  self.m_mergeLightObject.transform.localScale = V3Zero
  self.m_mergeLightTween = self.m_mergeLightObject.transform:DOScale(1, 0.2)
end

function ItemView:MergeLightDisappear()
  if self.m_mergeLightTween ~= nil then
    self.m_mergeLightTween:Kill()
    self.m_mergeLightTween = nil
  end
  if self.m_mergeLightObject ~= nil then
    local go = self.m_mergeLightObject
    local tween = go.transform:DOScale(0, 0.2):OnComplete(function()
      Object.Destroy(go)
      self.m_tweens[EAnimationType.MergeLightDisappear] = nil
    end)
    self.m_tweens[EAnimationType.MergeLightDisappear] = tween
    self.m_mergeLightObject = nil
  end
end

function ItemView:OnDisable()
  self.m_effectRoot.gameObject:RemoveChildren()
end

function ItemView:ShowSpreadLight(boostLevelSpan)
  local prefabType = self.m_spreadLightPrefab
  if boostLevelSpan and 0 < boostLevelSpan then
    prefabType = boostLevelSpan == 2 and self.m_spreadLightBoostQuadPrefab or self.m_spreadLightBoostPrefab
  end
  Object.Instantiate(prefabType, self.m_effectRoot)
end

function ItemView:OnRetrived()
  self.m_rootTrans:SetLocalScale(1.2)
end

function ItemView:ShowRetrieveLight()
  local transform = self.m_rootTrans
  local sequence = DOTween.Sequence()
  sequence:Append(transform:DOScale(1, 0.2))
  sequence:Append(transform:DOScale(1.1, 0.2))
  sequence:Append(transform:DOScale(1, 0.2))
  sequence:OnComplete(function()
    self.m_tweens[EAnimationType.Retrive] = nil
  end)
  self.m_tweens[EAnimationType.Retrive] = sequence
  Object.Instantiate(self.m_retrieveLightPrefab, self.m_effectRoot)
end

function ItemView:ShowMaxCollectEffect()
  Object.Instantiate(self.m_maxEffectPrefab, self.transform)
end

function ItemView:UpdateItemAffectedEffect(dragItemModel)
  local isBoosterType = dragItemModel ~= nil and dragItemModel:IsBoosterType() or false
  if isBoosterType then
    local setAlpha = false
    local boardModel = self.m_model:GetBoardModel()
    if not boardModel:CanItemAffect(dragItemModel, self.m_model) and not boardModel:CanTwoItemsMerge(dragItemModel, self.m_model) then
      setAlpha = self.m_model ~= dragItemModel and self.m_model:GetComponent(ItemPaperBox) == nil and self.m_model:GetComponent(ItemLocker) == nil
    end
    if setAlpha then
      self.m_mapAlphaChangeSpriteRenderer = {}
      local spriteRenderers = self.gameObject:GetComponentsInChildren(typeof(SpriteRenderer), true)
      for i = 0, spriteRenderers.Length - 1 do
        self.m_mapAlphaChangeSpriteRenderer[spriteRenderers[i]] = spriteRenderers[i].color.a
        UIUtil.SetAlpha(spriteRenderers[i], math.min(spriteRenderers[i].color.a, 0.5))
      end
      self.m_mapAlphaChangeImage = {}
      local images = self.gameObject:GetComponentsInChildren(typeof(Image), true)
      for i = 0, images.Length - 1 do
        self.m_mapAlphaChangeImage[images[i]] = images[i].color.a
        UIUtil.SetAlpha(images[i], math.min(images[i].color.a, 0.5))
      end
      self.m_mapColorChangeParticle = {}
      local particles = self.gameObject:GetComponentsInChildren(typeof(ParticleSystem), true)
      for i = 0, particles.Length - 1 do
        local particleMain = particles[i].main
        if particleMain and particleMain.startColor.mode == ParticleSystemGradientMode.Color then
          local curColor = particleMain.startColor.color
          self.m_mapColorChangeParticle[particles[i]] = curColor
          particleMain.startColor = ParticleSystemMinMaxGradient(CSColor(curColor.r, curColor.g, curColor.b, math.min(curColor.a, 0.35)))
          particles[i]:Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear)
          particles[i]:Play()
        end
      end
      self.m_arraySpineAnimation = self.gameObject:GetAllLuaTable("SpineAnimation")
      for i = 0, self.m_arraySpineAnimation.Length - 1 do
        self.m_arraySpineAnimation[i]:SetOpacity(0.5)
      end
    end
  elseif self.m_mapAlphaChangeSpriteRenderer ~= nil then
    for key, value in pairs(self.m_mapAlphaChangeSpriteRenderer) do
      if not key:IsNull() then
        UIUtil.SetAlpha(key, value)
      end
    end
    self.m_mapAlphaChangeSpriteRenderer = nil
    for key, value in pairs(self.m_mapAlphaChangeImage) do
      if not key:IsNull() then
        UIUtil.SetAlpha(key, value)
      end
    end
    self.m_mapAlphaChangeImage = nil
    for key, value in pairs(self.m_mapColorChangeParticle) do
      if not key:IsNull() then
        key.main.startColor = ParticleSystemMinMaxGradient(value)
        key:Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear)
        key:Play()
      end
    end
    self.m_mapColorChangeParticle = nil
    for i = 0, self.m_arraySpineAnimation.Length - 1 do
      self.m_arraySpineAnimation[i]:SetOpacity(1)
    end
    self.m_arraySpineAnimation = nil
  end
end

function ItemView:PlayTapAnimation(range)
  local transform = self.m_rootTrans
  local sequence = DOTween.Sequence()
  sequence:Append(transform:DOScale(1 - range, 0.1))
  sequence:Append(transform:DOScale(1 + range, 0.2))
  sequence:Append(transform:DOScale(1, 0.2))
  sequence:Append(transform:DOScale(1 + range / 2, 0.2))
  sequence:Append(transform:DOScale(1, 0.2))
  sequence:OnComplete(function()
    self.m_tweens[EAnimationType.Tap] = nil
  end)
  self.m_tweens[EAnimationType.Tap] = sequence
end

local TimePerFrame = 0.016666666666666666

function ItemView:PlayDropAnimation()
  local transform = self.gameObject.transform
  local originPos = transform.localPosition
  self:SetFlying(true)
  transform.localScale = V3Zero
  transform.localPosition = Vector3(originPos.x, originPos.y, 0)
  local sequence = DOTween.Sequence()
  sequence:Append(transform:DOScale(1.25, 15 * TimePerFrame))
  sequence:Join(transform:DOLocalMoveY(originPos.y + 54, 15 * TimePerFrame))
  sequence:Append(transform:DOScale(1.23, 8 * TimePerFrame))
  sequence:Join(transform:DOLocalMoveY(originPos.y + 54, 8 * TimePerFrame))
  sequence:Append(transform:DOScale(0.87, 12 * TimePerFrame))
  sequence:Join(transform:DOLocalMoveY(originPos.y, 12 * TimePerFrame))
  sequence:AppendCallback(function()
    self:ShowSpreadLight()
  end)
  sequence:Append(transform:DOScale(1, 7 * TimePerFrame))
  sequence:Join(transform:DOLocalMoveY(originPos.y, 7 * TimePerFrame))
  sequence:OnComplete(function()
    self.m_tweens[EAnimationType.Drop] = nil
    transform.localPosition = self:GetLocalPosition(self.m_model:GetPosition())
    self:SetFlying(false)
  end)
  self.m_tweens[EAnimationType.Drop] = sequence
end

function ItemView:PlayPromptAnimation(targetPosition, targetItem)
  self:StopPromptAnimation(false)
  local sequence = DOTween.Sequence()
  local transform = self.m_rootTrans.transform
  local boardPos = self.m_model:GetPosition()
  local targetBoardPos = targetItem:GetPosition()
  local tempZ = -self.transform.localPosition.z + 8
  if boardPos:GetY() > targetBoardPos:GetY() or boardPos:GetY() == targetBoardPos:GetY() and boardPos:GetX() > targetBoardPos:GetX() then
    tempZ = tempZ - 1
  end
  transform:SetLocalPosZ(tempZ)
  local movePos
  if targetPosition == nil or self.m_model:GetComponent(ItemCook) ~= nil and targetItem:GetComponent(ItemCook) == nil or self.m_model:GetComponent(ItemCobweb) ~= nil then
    movePos = V3Zero
  else
    local direction = Vector3.Normalize(targetPosition - transform.position)
    direction.z = 0
    movePos = 20 * direction
  end
  for i = 1, 2 do
    sequence:Append(transform:DOScale(1.15, 0.25):SetEase(Ease.OutCubic))
    sequence:Join(transform:DOLocalMoveX(movePos.x, 0.45):SetEase(Ease.OutCubic))
    sequence:Join(transform:DOLocalMoveY(movePos.y, 0.45):SetEase(Ease.OutCubic))
    sequence:AppendInterval(0.05)
    sequence:Append(transform:DOScale(1, 0.25))
    sequence:Join(transform:DOLocalMoveX(0, 0.5))
    sequence:Join(transform:DOLocalMoveY(0, 0.5))
  end
  sequence:AppendInterval(1)
  sequence:SetLoops(-1)
  self.m_tweens[EAnimationType.Prompt] = sequence
end

function ItemView:StopPromptAnimation(ignoreAnim)
  if self.m_tweens[EAnimationType.Prompt] ~= nil then
    self:_StopAnimation(EAnimationType.Prompt)
    local transform = self.m_rootTrans.transform
    transform:SetLocalPosZ(0)
    self:_StopAnimation(EAnimationType.PromptBack)
    if ignoreAnim ~= false then
      local duration = (transform.localScale.x - 1) / 0.19999999999999996 * 0.4
      local sequence = DOTween.Sequence()
      sequence:Insert(0, transform:DOScale(1, duration))
      sequence:Insert(0, transform:DOLocalMove(V3Zero, duration))
      sequence:OnComplete(function()
        self.m_tweens[EAnimationType.PromptBack] = nil
      end)
      self.m_tweens[EAnimationType.PromptBack] = sequence
    else
      transform.localScale = V3One
      transform.localPosition = V3Zero
    end
  end
end

function ItemView.PlayBubbleIdleAnimation(transform)
  local sequence = DOTween.Sequence()
  sequence:Append(transform:DOScale(0.98, 0.5))
  sequence:Insert(0, transform:DOLocalMove(Vector3(0, 3, 0), 0.5))
  sequence:Append(transform:DOScale(1.02, 1))
  sequence:Insert(0.5, transform:DOLocalMove(Vector3(0, -3, 0), 1))
  sequence:Append(transform:DOScale(V3One, 0.5))
  sequence:Insert(1.5, transform:DOLocalMove(V3Zero, 0.5))
  sequence:SetLoops(-1)
  return sequence
end

function ItemView:PlayHitAnimation()
  local transform = self.m_rootTrans.transform
  local seq = DOTween.Sequence()
  seq:Append(transform:DOScale(0.5, 0.08))
  seq:Append(transform:DOScale(1.25, 0.16))
  seq:Append(transform:DOScale(1, 0.08))
end

function ItemView:_PlayBubbleIdleAnimation()
  local transform = self.m_spriteRenderer.transform
  local sequence = ItemView.PlayBubbleIdleAnimation(transform)
  self.m_tweens[EAnimationType.BubbleIdle] = sequence
end

function ItemView:PlayTimeSkipAnimation(delay)
  delay = delay or 0
  local sequence = DOTween.Sequence()
  local transform = self.m_rootTrans.transform
  sequence:Insert(delay, transform:DOLocalMoveY(30, 0.25):SetEase(Ease.OutCubic))
  sequence:Insert(delay + 0.25, transform:DOLocalMoveY(0, 0.25):SetEase(Ease.InCubic))
  sequence:InsertCallback(delay, function()
    Object.Instantiate(self.m_timeSkipEffectPrefab, self.transform)
  end)
end

function ItemView:SetJumpTween(tween, finishCallback)
  tween:SetAutoKill(true)
  tween:OnComplete(function()
    self.m_jumpTween = nil
  end)
  if finishCallback then
    tween:OnKill(finishCallback)
  end
  self.m_jumpTween = tween
end

function ItemView:TryStopJumpTween()
  if self.m_jumpTween ~= nil then
    self.m_jumpTween:Kill()
    self.m_jumpTween = nil
    self.transform.localScale = Vector3.one
    return true
  end
  return false
end

function ItemView:_StopAnimation(type)
  if self.m_tweens[type] ~= nil then
    self.m_tweens[type]:Kill()
    self.m_tweens[type] = nil
  end
end

function ItemView:OnDragBegin()
  self.m_bDragging = true
  self:_UpdateStarVisible(false)
  self:UpdateChecked(false)
  for _, component in pairs(self.m_components) do
    component:OnDragBegin()
  end
  self:TryStopJumpTween()
end

function ItemView:OnDragEnd()
  self.m_bDragging = false
  self:_UpdateStar()
  for _, component in pairs(self.m_components) do
    component:OnDragEnd()
  end
end

function ItemView:IsDragging()
  return self.m_bDragging
end

function ItemView:GetRootTrans()
  return self.m_rootTrans
end

function ItemView:PlayChooseAudio()
  local cookEquipmentAudioName = self.m_model:GetCookEquipmentAudio()
  if cookEquipmentAudioName then
    GM.AudioModel:StopCookEffect()
    GM.AudioModel:PlayCookEffect(cookEquipmentAudioName)
    return
  end
  local booster = self.m_model:GetComponent(ItemBooster)
  if booster == nil then
    GM.AudioModel:PlayEffect(AudioFileConfigName.sfxChooseItem)
  else
    GM.AudioModel:PlayEffect(AudioFileConfigName["SfxChoose" .. booster.boosterType])
  end
end

function ItemView:_OnOrderItemCookPopTip(message)
  if not GM.ConfigModel:IsServerControlOpen(EGeneralConfType.IngredientRecipeHint) then
    return
  end
  if message.tip and message.popItems and message.popItems[self.m_model] then
    local go = Object.Instantiate(self.m_itemPopTipPrefab, self.transform)
    local popTip = go:GetLuaTable()
    popTip:PopTip(message.itemCode, message.tipType)
  end
end
