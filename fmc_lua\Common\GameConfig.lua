local csClientVersion = ProjectConfig.CLIENT_CODE_VERSION
local gameToken = ProjectConfig.GAME_TOKEN
local isTestMode = ProjectConfig.IS_TEST_MODE
GameConfig = {
  CURRENT_PLATFORM = CSPlatform:GetChannel(),
  PROJECT_ID = ProjectConfig.PROJECT_ID
}
GameConfig.__index = GameConfig

function GameConfig.IsTestMode()
  return isTestMode
end

function GameConfig.GetCurrentVersion()
  return csClientVersion
end

function GameConfig.GetGameToken()
  return gameToken
end

if GameConfig.GetCurrentVersion() ~= csClientVersion then
  ProjectConfig.CLIENT_CODE_VERSION = GameConfig.GetCurrentVersion()
end
