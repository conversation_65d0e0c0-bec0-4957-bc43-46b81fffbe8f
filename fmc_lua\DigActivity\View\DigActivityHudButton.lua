DigActivityHudButton = setmetatable({}, HudPropertyButton)
DigActivityHudButton.__index = DigActivityHudButton

function DigActivityHudButton:Awake()
  HudPropertyButton.Awake(self)
  if self.m_activityDefinition ~= nil then
    self:SyncToModelValue()
    self:_AddListener()
  end
end

function DigActivityHudButton:Init(activityType)
  self.m_model = GM.ActivityManager:GetModel(activityType)
  if self.m_model == nil then
    return
  end
  self.m_activityDefinition = DigActivityDefinition[activityType]
  HudPropertyButton.Init(self, self.m_activityDefinition.ActivityTokenPropertyType)
  if self.gameObject.activeInHierarchy then
    self:_AddListener()
  end
end

function DigActivityHudButton:_AddListener()
  EventDispatcher.AddListener(self.m_activityDefinition.ScoreChangedEvent, self, self.OnScoreChanged)
  EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self.OnStateChanged)
end

function DigActivityHudButton:OnScoreChanged(msg)
  if msg ~= nil and msg.UpdateScore then
    self:SyncToModelValue()
  end
end

function DigActivityHudButton:OnStateChanged()
  if not self.m_model or self.m_model:GetState() == ActivityState.Released then
    return
  end
  self:SyncToModelValue()
end

function DigActivityHudButton:GetPropertyNum()
  if self.m_model == nil then
    return 0
  end
  return self.m_model:GetScore() or 0
end

function DigActivityHudButton:SyncToModelValue()
  HudPropertyButton.SyncToModelValue(self)
  local bShowExclamation = self:GetPropertyNum() > 0
  UIUtil.SetActive(self.m_exclamationGo, bShowExclamation)
  UIUtil.SetActive(self.m_lightGo, bShowExclamation)
end

function DigActivityHudButton:UpdateTextAnimation(...)
  local bShowExclamation = self:GetPropertyNum() > 0
  UIUtil.SetActive(self.m_exclamationGo, bShowExclamation)
  UIUtil.SetActive(self.m_lightGo, bShowExclamation)
  HudPropertyButton.UpdateTextAnimation(self, ...)
end
