DiscoveriesWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
DiscoveriesWindow.__index = DiscoveriesWindow
local ETabKey = {
  Producer = "1",
  Equipment = "2",
  Property = "3",
  Dish = "4"
}
local MOVE_CONTAINER_TWEEN_DURATION = 0.6

function DiscoveriesWindow:Init()
  self.m_arrDiscoveriesChainMap = {}
  local maxListSize = 0
  local chain
  local initTab = 1
  for tab = 4, 1, -1 do
    self.m_arrDiscoveriesChainMap[tab] = GM.ItemDataModel:GetDiscoveriesMap(tab) or {}
    maxListSize = math.max(maxListSize, #self.m_arrDiscoveriesChainMap[tab])
    chain = GM.ItemDataModel:GetCanRewardDiscoveriesOrder(tab)
    if chain ~= nil then
      initTab = tab
    end
  end
  self.m_listView:InitListView(maxListSize, function(listView, cellIndex)
    return self:_OnListItemByIndex(listView, cellIndex)
  end)
  self.m_dataList = {}
  self.m_mapTweens = {}
  self.m_tabView:Init(function(tabKey)
    if not tabKey then
      return
    end
    tabKey = tonumber(tabKey)
    if tabKey ~= self.m_nTab then
      self.m_nTab = tabKey
      self:UpdateTable(self.m_nTab)
      self:_TryMove()
      return true
    end
    self:_TryMove()
  end, tostring(initTab), function(tabKey)
    local chain = GM.ItemDataModel:GetCanRewardDiscoveriesOrder(tonumber(tabKey))
    return chain ~= nil
  end)
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = true,
    hudKey = ESceneViewHudButtonKey.Gem
  })
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = true,
    hudKey = ESceneViewHudButtonKey.Energy
  })
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = true,
    hudKey = ESceneViewHudButtonKey.Coin
  })
end

function DiscoveriesWindow:AddEventListener()
  EventDispatcher.AddListener(EEventType.DiscoveriesUpdate, self, self._TryMove)
end

function DiscoveriesWindow:RemoveEventListener()
  EventDispatcher.RemoveTarget(self)
end

function DiscoveriesWindow:_TryMove(msg)
  self.m_content:DOKill(false)
  self.m_scrollRect:StopMovement()
  local chain, rewardItemType
  local lastChainId = msg and msg.chainId
  local shouldMove = not lastChainId
  if not shouldMove then
    local nextCodeInSameChain, nextCodeIndex = GM.ItemDataModel:HasUnlockRewardInDiscoveriesOrder(lastChainId)
    if not nextCodeInSameChain then
      shouldMove = true
    else
      local lastChainIndex = msg and msg.chainIndex or 0
      local chainItem = self.m_listView:GetShownItemByItemIndex(lastChainIndex)
      if chainItem and not chainItem:IsNull() then
        local lastChainTb = chainItem.gameObject:GetLuaTable()
        local cellGo = lastChainTb:GetCellTb(nextCodeIndex).gameObject
        local relativePos = self.m_scrollRect.viewport:InverseTransformPoint(cellGo.transform.position)
        if relativePos.y <= -1150 or relativePos.y >= -10 then
          shouldMove = true
          chain = lastChainId
          rewardItemType = nextCodeInSameChain
        end
      end
    end
  end
  if shouldMove and not chain then
    chain, rewardItemType = GM.ItemDataModel:GetCanRewardDiscoveriesOrder(self.m_nTab)
  end
  if chain and shouldMove then
    if not self:MoveTo(chain, rewardItemType) then
      self.m_tabView:UpdateExclaimation(tostring(self.m_nTab))
    end
  else
    self.m_tabView:UpdateExclaimation(tostring(self.m_nTab))
  end
end

function DiscoveriesWindow:OnCloseView(bWithoutAnimation)
  for seq, _ in pairs(self.m_mapTweens) do
    seq:Kill()
  end
  self.m_mapTweens = nil
  BaseWindow.OnCloseView(self, bWithoutAnimation)
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = false,
    hudKey = ESceneViewHudButtonKey.Gem
  })
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = false,
    hudKey = ESceneViewHudButtonKey.Energy
  })
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = false,
    hudKey = ESceneViewHudButtonKey.Coin
  })
end

function DiscoveriesWindow:_AddTween(tween)
  if not self.m_mapTweens then
    tween:Kill()
  else
    self.m_mapTweens[tween] = true
  end
end

function DiscoveriesWindow:_OnListItemByIndex(listView, index)
  local data = self.m_dataList[index + 1]
  if not data then
    return nil
  end
  local item = listView:NewListViewItem("DiscoveriesChain")
  local chainTb = item.gameObject:GetLuaTable()
  chainTb:Init(data.order, data.title, index)
  return item
end

function DiscoveriesWindow:MoveTo(chain, rewardItemType)
  for chainIndex, v in ipairs(self.m_dataList) do
    if v.order == chain then
      self.m_curChainIndex = chainIndex
      local itemIndex
      if rewardItemType then
        itemIndex = GM.ItemDataModel:GetItemIndexInDiscoveriesOrder(chain, rewardItemType)
      end
      self:_MoveTo(chainIndex, itemIndex)
      return true
    end
  end
end

function DiscoveriesWindow:GetCanRewardCell()
  if not self.m_curChainIndex then
    return nil
  end
  local curChainObj = self.m_listView:GetShownItemByIndex(self.m_curChainIndex - 1)
  if curChainObj then
    local curChainTb = curChainObj.gameObject:GetLuaTable()
    if curChainTb and curChainTb.m_list then
      for _, item in ipairs(curChainTb.m_list) do
        if GM.ItemDataModel:GetUnlockState(item.m_type) == EItemUnlockState.Unlocked then
          return item.gameObject.transform
        end
      end
    end
  end
  return nil
end

function DiscoveriesWindow:UpdateTable(tab)
  self.m_dataList = self.m_arrDiscoveriesChainMap[tab] or {}
  self.m_listView:SetListItemCount(#self.m_dataList, true)
  self.m_listView:RefreshAllShownItem()
  self:_UpdateListViewHeight()
end

function DiscoveriesWindow:_UpdateListViewHeight()
  local height, items, chain
  local itemDataModel = GM.ItemDataModel
  for i = 1, #self.m_dataList do
    chain = self.m_dataList[i].order
    items = itemDataModel:GetDiscoveriesItemsByOrder(chain)
    height = 75 + 194 * ((#items + 3) // 4)
    self.m_listView:SetItemSize(i - 1, height)
  end
  self.m_listView:UpdateContentSize()
end

function DiscoveriesWindow:_MoveTo(chainIndex, itemIndex)
  local finishPosY = 0
  local scrollViewHeight = self.m_content.rect.height
  local endPos = scrollViewHeight - self.m_content.parent.rect.height
  if 0 < endPos then
    finishPosY = self.m_listView:GetItemPos(chainIndex - 1)
    if itemIndex then
      finishPosY = finishPosY + (itemIndex - 1) // 4 * 189
    end
    finishPosY = math.min(finishPosY, endPos)
  end
  self.m_scrollRect.velocity = V2Zero
  local tween = self.m_content:DOLocalMoveY(finishPosY, MOVE_CONTAINER_TWEEN_DURATION):SetEase(Ease.InCubic)
  self:_AddTween(tween)
end
