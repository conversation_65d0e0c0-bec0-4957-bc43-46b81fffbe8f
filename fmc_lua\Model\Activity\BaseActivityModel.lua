BaseActivityModel = {
  eModelType = ActivityModelType.Other
}
BaseActivityModel.__index = BaseActivityModel

function BaseActivityModel:Init(type, virtualDBTable)
  self.event = PairEvent.Create(self)
  self.m_type = type
  self.m_dbTable = virtualDBTable
  self.m_lockHours = 0
  self.m_bForceStateReleaseWhenResourceNotReady = false
  self:LoadServerConfig()
end

function BaseActivityModel:LateInit()
end

function BaseActivityModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function BaseActivityModel:LoadServerConfig()
  self:_LoadServerConfig()
  self:_CheckId()
  self:_UpdateState()
end

function BaseActivityModel:_CheckId()
  local dataId = self:GetId()
  if dataId == nil then
    if self.m_config ~= nil then
      self.m_dbTable:Set("id", "value", self.m_config.id)
    end
  elseif self.m_config == nil or self.m_config.id ~= dataId then
    self:_DropData()
    self.m_state = nil
    self.m_nextStateTime = nil
    if self.m_config ~= nil then
      self.m_dbTable:Set("id", "value", self.m_config.id)
    end
  end
end

function BaseActivityModel:Update()
end

function BaseActivityModel:UpdatePerSecond()
  if self.m_state ~= ActivityState.Released then
    self:_UpdateState()
  end
end

function BaseActivityModel:OnCheckResourcesFinished()
end

function BaseActivityModel:_LoadServerConfig()
  local md5 = GM.ConfigModel:GetServerConfigMD5(self.m_type)
  if self.m_md5 == md5 then
    return
  end
  self.m_md5 = md5
  local config = GM.ConfigModel:GetServerConfig(self.m_type)
  if config == nil then
    self.m_config = nil
    return
  end
  self.m_config = {}
  self.m_config.id = config.id
  self.m_config.sTime = config.sTime
  self.m_config.eTime = config.eTime
  self.m_config.rTime = config.rTime
  self:_LoadGeneralConfig(config)
  self:_LoadOtherServerConfig(config)
end

function BaseActivityModel:_LoadGeneralConfig(config)
  self.m_config.generalActivityConf = {}
  local arrGeneralActivityConf = config.generalActivityConf
  if arrGeneralActivityConf then
    for _, conf in ipairs(arrGeneralActivityConf) do
      Log.Assert(self.m_config.generalActivityConf[conf.confType] == nil, "Same confType overwrite!" .. tostring(conf.confType))
      self.m_config.generalActivityConf[conf.confType] = conf
    end
  end
  self.m_lockHours = self:GetGeneralConfig("lockHours", EConfigParamType.Int) or 0
end

function BaseActivityModel:GetGeneralConfig(configType, eGeneralConfigParam)
  Log.Assert(eGeneralConfigParam ~= nil, "BaseActivityModel:GetGeneralConfig() eGeneralConfigParam is nil")
  return self.m_config and self.m_config.generalActivityConf and self.m_config.generalActivityConf[configType] and self.m_config.generalActivityConf[configType][eGeneralConfigParam]
end

function BaseActivityModel:_LoadOtherServerConfig(config)
  Log.Assert(false, "_LoadOtherServerConfig()是抽象接口")
end

function BaseActivityModel:_DropData()
  self.m_dbTable:Drop()
end

function BaseActivityModel:_UpdateState()
  local state, nextStateTime = self:_CalculateState()
  local formerState = self.m_state
  self.m_state = state
  self.m_nextStateTime = nextStateTime
  local bForceStateReleaseWhenResourceNotReady = false
  if self:NeedCheckResource() then
    for _, label in ipairs(self:GetResourceLabels()) do
      if not GM.DownloadManager:IsLabelDownloaded(label) then
        bForceStateReleaseWhenResourceNotReady = true
        break
      end
    end
    if bForceStateReleaseWhenResourceNotReady and not self.m_needRestart and GM.SceneManager:GetGameMode() ~= EGameMode.Loading and not IsAutoRun() then
      for _, label in ipairs(self:GetResourceLabels()) do
        Log.Info("[ResourceDownloadWindow] activity:" .. self.m_type .. "; label:" .. label .. "; bDownload:" .. (GM.DownloadManager:IsLabelDownloaded(label) and "true" or "false"))
      end
      Log.Info("[ResourceDownloadWindow] GM.CheckResourcesStageFinished: " .. (GM.CheckResourcesStageFinished and "true" or "false"))
      GM.UIManager:OpenView(UIPrefabConfigName.ResourceDownloadWindow)
      self.m_needRestart = true
    end
  end
  local stateChanged = bForceStateReleaseWhenResourceNotReady ~= self.m_bForceStateReleaseWhenResourceNotReady or formerState ~= state
  self.m_bForceStateReleaseWhenResourceNotReady = bForceStateReleaseWhenResourceNotReady
  if stateChanged then
    self:_OnStateChanged()
  end
end

function BaseActivityModel:_CalculateState()
  if self.m_config == nil then
    return ActivityState.Released, nil
  end
  local serverTime = GM.GameModel:GetServerTime()
  if serverTime < self.m_config.sTime then
    return ActivityState.Preparing, self.m_config.sTime
  elseif serverTime < self.m_config.eTime then
    return ActivityState.Started, self.m_config.eTime
  elseif self.m_config.rTime and serverTime < self.m_config.rTime then
    return ActivityState.Ended, self.m_config.rTime
  else
    return ActivityState.Released, nil
  end
end

function BaseActivityModel:_OnStateChanged()
end

function BaseActivityModel:GetType()
  return self.m_type
end

function BaseActivityModel:GetState(checkResource)
  checkResource = checkResource ~= false
  if self.m_bForceStateReleaseWhenResourceNotReady and checkResource then
    return ActivityState.Released
  end
  return self.m_state
end

function BaseActivityModel:IsStateReliable()
  return self.m_state == ActivityState.Released or not self.m_bForceStateReleaseWhenResourceNotReady
end

function BaseActivityModel:IsActivityOpen()
  Log.Error("当前model缺少活动开启且未提前完成的实现: type = " .. self.m_type)
end

function BaseActivityModel:GetAllStateChangedEvent()
  Log.Error("当前model缺少获取活动状态改变事件的实现: type = " .. self.m_type)
end

function BaseActivityModel:GetNextStateTime(checkResource)
  checkResource = checkResource ~= false
  if self.m_bForceStateReleaseWhenResourceNotReady and checkResource then
    return nil
  end
  return self.m_nextStateTime
end

function BaseActivityModel:GetEndTime()
  return self.m_config and self.m_config.eTime or nil
end

function BaseActivityModel:GetStartTime()
  return self.m_config and self.m_config.sTime or 0
end

function BaseActivityModel:GetLockDurationInSeconds()
  return self.m_lockHours * 3600
end

function BaseActivityModel:IsInLockTime()
  local serverTime = GM.GameModel:GetServerTime()
  local deadline = (self:GetEndTime() or 0) - self:GetLockDurationInSeconds()
  return serverTime >= deadline
end

function BaseActivityModel:GetId()
  return self.m_dbTable:GetValue("id", "value")
end

function BaseActivityModel:HasWindowOpenedOnce(state)
  local key = "windowOpened" .. state
  return self.m_dbTable:GetValue(key, "value") ~= nil
end

function BaseActivityModel:SetWindowOpened(state)
  state = state or self.m_state
  if state == ActivityState.Started and not self:HasWindowOpenedOnce(state) then
    self:LogActivity(EBIType.ActivityStarted, "")
  end
  local key = "windowOpened" .. state
  self.m_dbTable:Set(key, "value", 1)
end

function BaseActivityModel:SetWindowReopen()
  local key = "windowOpened" .. self.m_state
  self.m_dbTable:Remove(key, "value")
end

function BaseActivityModel:LogActivity(scene, action)
  GM.BIManager:LogActivity(self.m_type, self:GetId(), scene, action)
end

function BaseActivityModel:IsSendingRankRequest()
  return false
end

function BaseActivityModel:CanHeartBeat()
  return false
end

function BaseActivityModel:FromHeartBeat(data)
end

function BaseActivityModel:ToHeartBeat(data)
end

function BaseActivityModel:NeedCheckResource()
  return self.m_state ~= ActivityState.Released
end

function BaseActivityModel:GetResourceLabels()
  return {}
end

function BaseActivityModel:GetActivityDefinition()
  return self.m_activityDefinition
end

function BaseActivityModel:RestoreIapRewards(iapType)
  return false
end

function BaseActivityModel:AcquireActivityToken(score)
  Log.Error("virtual method, override me! " .. tostring(self.m_type))
end

function BaseActivityModel:GetActivityTokenNumber()
  Log.Error("virtual method, override me! " .. tostring(self.m_type))
  return 0
end

function BaseActivityModel:CanAddScore()
  return false
end

function BaseActivityModel:CanAddOrderReward(orderType)
  return self:CanAddScore()
end

function BaseActivityModel:GetOrderExtraReward(orderScore, coinNum, orderId)
  Log.Error("virtual method, override me! " .. tostring(self.m_type))
  return {}
end

function BaseActivityModel.GetButtonTarget(definition, propertyType)
  for activityType, activityDefinition in pairs(definition) do
    if propertyType == activityDefinition.ActivityTokenPropertyType then
      if GM.SceneManager:GetGameMode() == EGameMode.Board then
        local boardView = MainBoardView.GetInstance()
        if boardView ~= nil then
          local orderArea = boardView:GetOrderArea()
          if orderArea ~= nil then
            return orderArea:GetIconAreaByActivityType(activityType)
          end
        end
      elseif GM.SceneManager:GetGameMode() == EGameMode.Main then
        return TutorialHelper.GetHudButton(activityDefinition.EntryButtonKey)
      end
    end
  end
  return nil
end
