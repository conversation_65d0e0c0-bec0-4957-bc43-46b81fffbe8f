JokerExchangeCardSetsCell = {}
JokerExchangeCardSetsCell.__index = JokerExchangeCardSetsCell

function JokerExchangeCardSetsCell:Init(wnd, setConfig, model, bShowOnlyNone, selectCallback)
  self.m_model = model
  self.m_setConfig = setConfig
  self.m_bShowNoneCard = bShowOnlyNone
  self.m_exchangeWindow = wnd
  local albumCfg = self.m_model:GetAlbumConfig()
  self.m_titleText.text = GM.GameTextModel:GetText(self.m_setConfig.setId .. "_name")
  SpriteUtil.SetImage(self.m_setIcon, ImageFileConfigName[self.m_setConfig.setId])
  local rewards = self.m_setConfig.reward
  self.m_rewardContent:Init(rewards)
  local cur, max = self.m_model:GetSetCollectProgress(self.m_setConfig.setId)
  local bOnlyOne = max - cur == 1
  local cards = Table.ShallowCopy(self.m_setConfig.cardGroup)
  table.sort(cards, function(a, b)
    local bNewA = self.m_model:GetCardCount(a) == 0
    local bNewB = self.m_model:GetCardCount(b) == 0
    if bNewA ~= bNewB then
      return bNewA
    end
    return self.m_model:GetCardInfo(a).star > self.m_model:GetCardInfo(b).star
  end)
  self.m_cards = self.m_cards or {}
  for i, v in ipairs(cards) do
    if not self.m_cards[i] then
      self.m_cards[i] = Object.Instantiate(self.m_jokerExchangeCardCellPrefab, self.m_cardTransform):GetLuaTable()
    end
    self.m_cards[i].gameObject:SetActive(true)
    self.m_cards[i]:Init(v, self.m_model, self.m_bShowNoneCard, bOnlyOne, self.m_exchangeWindow, selectCallback)
  end
  for i = #cards + 1, #self.m_cards do
    self.m_cards[i].gameObject:SetActive(false)
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_cardTransform)
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_JokerExchangeCardSetsCellRectTrans)
end

function JokerExchangeCardSetsCell:UpdateSelectState(cardId)
  if not self.gameObject.activeSelf then
    return
  end
  for i, v in ipairs(self.m_cards) do
    v:UpdateSelectState(cardId)
  end
end
