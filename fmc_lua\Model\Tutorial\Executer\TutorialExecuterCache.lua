local Step = {ClickCache = "1"}
local EStep2TextKey = {
  [Step.ClickCache] = "tutorial_cache_item_1"
}
local EStep2TextAnchorPercent = {
  [Step.ClickCache] = 40
}
local Executer = setmetatable({}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self.OnPopCachedItem)
end

function Executer:TryStartTutorial()
  if self:_CanExecuteStep1() then
    self:_ExecuteStep1()
    return true
  end
end

function Executer:OnPopCachedItem(msg)
  if self.m_strOngoingDatas == Step.ClickCache and self.m_gesture and msg.GameMode == EGameMode.Board then
    TutorialHelper.DehighlightCacheRoot()
    self:Finish(self.m_gesture)
  end
end

function Executer:_CanExecuteStep1()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return false
  end
  local topCachedItemType = GM.MainBoardModel:GetCachedItem(1)
  if topCachedItemType == nil then
    return false
  end
  local boardView = BoardViewHelper.GetActiveView()
  if boardView:GetOrderArea():GetBoardCacheRoot():GetDisplayItem() ~= topCachedItemType or boardView:GetOrderArea():GetBoardCacheRoot():IsPlayingAnimation() or not boardView:GetOrderArea():GetBoardCacheRoot():IsShowing() then
    return false
  end
  local isBoardFullofItems = GM.MainBoardModel:FindEmptyPositionInValidOrder() == nil
  if isBoardFullofItems then
    return false
  end
  return true
end

function Executer:_ExecuteStep1()
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.ClickCache
  self:_SaveOngoingDatas()
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  local cacheRoot = TutorialHelper.HighlightCacheRoot()
  self.m_gesture = TutorialHelper.TapCustomPos(cacheRoot:GetFlyTargetPosition())
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
