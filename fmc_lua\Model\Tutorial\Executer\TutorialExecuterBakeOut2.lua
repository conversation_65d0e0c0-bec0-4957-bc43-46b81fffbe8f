local Step = {
  HighlightOrder = "1",
  ClickBoardBubble = "2",
  ClickExchangeButton = "3",
  ExchangeCoin = "4"
}
local EStep2TextKey = {
  [Step.HighlightOrder] = "tutorial_bakeout_3",
  [Step.ClickBoardBubble] = "tutorial_bakeout_startrade1",
  [Step.ClickExchangeButton] = "tutorial_bakeout_startrade2",
  [Step.ExchangeCoin] = "tutorial_bakeout_startrade3"
}
local EStep2TextAnchorPercent = {
  [Step.HighlightOrder] = 38,
  [Step.ClickBoardBubble] = 45,
  [Step.ClickExchangeButton] = 39,
  [Step.ExchangeCoin] = 40
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self.OnChangeGameModeFinished)
  EventDispatcher.AddListener(EEventType.OpenView, self, self.OnViewOpened)
  EventDispatcher.AddListener(EEventType.OnViewWillClose, self, self.OnViewWillClose)
  EventDispatcher.AddListener(EEventType.BakeOutStateChanged, self, self.OnBakeOutStateChanged)
  EventDispatcher.AddListener(EEventType.BakeOutCoinExchanged, self, self.OnBakeOutCoinExchange)
  self:OnBakeOutStateChanged()
end

function Executer:OnChangeGameModeFinished()
  if not self.m_bExecuteStep1 and GM.SceneManager:GetGameMode() == EGameMode.Board then
    self:_ExecuteStep1()
  end
end

function Executer:OnBakeOutStateChanged()
  if StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) then
    return
  end
  local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  if bakeOutModel ~= nil and bakeOutModel:GetState() ~= ActivityState.Started then
    if self.m_highlightTransform ~= nil then
      TutorialHelper.DehighlightForUI(self.m_highlightTransform)
      self.m_highlightTransform = nil
    end
    if self.m_highlightOrderCell ~= nil then
      TutorialHelper.DehighlightOrder(self.m_highlightOrderCell)
      if self.m_highlightOrderCell.SetOrderIconClickEnabled then
        self.m_highlightOrderCell:SetOrderIconClickEnabled(true)
      end
      if self.m_highlightOrderCell.SetGoButtonEnabled then
        self.m_highlightOrderCell:SetGoButtonEnabled(true)
      end
      self.m_highlightOrderCell = nil
    end
    self:Finish(self.m_gesture)
  end
end

function Executer:OnBakeOutCoinExchange()
  if self.m_strOngoingDatas == Step.ExchangeCoin then
    if self.m_highlightTransform ~= nil then
      TutorialHelper.DehighlightForUI(self.m_highlightTransform)
      self.m_highlightTransform = nil
    end
    self:Finish(self.m_gesture)
  end
end

function Executer:OnViewOpened(msg)
  if msg.name == UIPrefabConfigName.BakeOutMainWindow and self.m_strOngoingDatas == Step.ClickBoardBubble then
    if self.m_highlightTransform ~= nil then
      TutorialHelper.DehighlightForUI(self.m_highlightTransform)
      self.m_highlightTransform = nil
    end
    self:_ExecuteStep3()
  elseif msg.name == UIPrefabConfigName.BakeOutCoinExchangeWindow and self.m_strOngoingDatas == Step.ClickExchangeButton then
    self:_ExecuteStep4()
  end
end

function Executer:OnViewWillClose(msg)
  if self.m_strOngoingDatas == Step.ClickExchangeButton and msg.name == UIPrefabConfigName.BakeOutMainWindow and self.m_highlightTransform ~= nil then
    TutorialHelper.DehighlightForUI(self.m_highlightTransform)
    self.m_highlightTransform = nil
  end
end

function Executer:_ExecuteStep1()
  local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  if not bakeOutModel:IsBakeOutModeOn() then
    return false
  end
  if self:CheckOtherTutorialInStrong() then
    return
  end
  if not GM.TutorialModel:IsTutorialFinished(ETutorialId.BakeOut1) then
    GM.TutorialModel:FinishTutorial(ETutorialId.BakeOut1)
  end
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  self.m_bExecuteStep1 = true
  self:SetStrongTutorial(true)
  self.m_strOngoingDatas = Step.HighlightOrder
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.ScrollToMainOrderRoot(true)
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local orderCell = TutorialHelper.GetMainFirstOrderCell()
    local callback = function()
      self:Finish(self.m_gesture)
    end
    if orderCell ~= nil then
      TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
      TutorialHelper.ScrollToNormalOrder(orderCell)
      TutorialHelper.UpdateMaskOnBoard(orderCell.transform.position, orderCell.transform.sizeDelta, callback, false)
    else
      self:Finish(self.m_gesture)
    end
  end, 1.2)
end

function Executer:_ExecuteStep2()
  self.m_strOngoingDatas = Step.ClickBoardBubble
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.ScrollToMainBakeOut(true)
  TutorialHelper.HideTutorialLayer()
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local bakeOutBoardBubble = TutorialHelper.HighlightBakeOutBubble()
    if bakeOutBoardBubble == nil then
      self:Finish(self.m_gesture)
      return
    end
    self.m_highlightOrderCell = bakeOutBoardBubble
    TutorialHelper.WholeMask()
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
    self.m_gesture = TutorialHelper.TapOnCustomRectTrans(self.m_highlightOrderCell:GetTouchContentTransform())
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  end, 0.5)
end

function Executer:_ExecuteStep3()
  self.m_strOngoingDatas = Step.ClickExchangeButton
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  if self.m_highlightOrderCell ~= nil then
    TutorialHelper.DehighlightOrder(self.m_highlightOrderCell)
    self.m_highlightOrderCell = nil
  end
  TutorialHelper.HideTutorialLayer(self.m_gesture)
  self.m_gesture = nil
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    TutorialHelper.WholeMask()
    local mainWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BakeOutMainWindow)
    if mainWindow ~= nil and mainWindow:GetExchangeBtnGo() ~= nil then
      self.m_highlightTransform = mainWindow:GetExchangeBtnGo().transform
      TutorialHelper.HighlightForUI(self.m_highlightTransform)
      if mainWindow:GetExchangeBtnTapTransform() ~= nil then
        self.m_gesture = TutorialHelper.TapOnCustomRectTrans(mainWindow:GetExchangeBtnTapTransform())
        self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
      end
    else
      self:Finish(self.m_gesture)
      return
    end
    TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
  end, 1.5)
end

function Executer:_ExecuteStep4()
  self.m_strOngoingDatas = Step.ExchangeCoin
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  if self.m_gesture ~= nil then
    TutorialHelper.HideGesture(self.m_gesture)
    self.m_gesture = nil
  end
  local exchangeWindow = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BakeOutCoinExchangeWindow)
  if exchangeWindow ~= nil and exchangeWindow:GetExchangeBtnGo() ~= nil then
    self.m_highlightTransform = exchangeWindow:GetExchangeBtnGo().transform
    TutorialHelper.HighlightForUI(self.m_highlightTransform)
    self.m_gesture = TutorialHelper.TapOnCustomRectTrans(self.m_highlightTransform)
    self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
    GM.ActivityManager:GetModel(ActivityType.BakeOut):SetExchangeCoinFree()
    exchangeWindow:UpdateContent()
  else
    self:Finish(self.m_gesture)
    return
  end
  TutorialHelper.WholeMask()
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
