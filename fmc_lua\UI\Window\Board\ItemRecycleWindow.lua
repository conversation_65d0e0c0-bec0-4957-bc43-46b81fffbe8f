ItemRecycleWindow = setmetatable({}, BaseWindow)
ItemRecycleWindow.__index = ItemRecycleWindow

function ItemRecycleWindow:BeforeOpenCheck()
  return GM.SceneManager:GetGameMode() == EGameMode.Board and GM.ItemRecycleModel:GetCurMaxSeries() ~= nil
end

function ItemRecycleWindow:Init()
  self.m_arrDishes, self.m_arrInstrus, self.m_arrOthers, self.m_mapInInstrumentItems = GM.ItemRecycleModel:GetRecycleData()
  self.m_bCanRecycle = false
  for _, tb in ipairs({
    self.m_arrDishes,
    self.m_arrInstrus,
    self.m_arrOthers,
    self.m_mapInInstrumentItems
  }) do
    if next(tb) ~= nil then
      self.m_bCanRecycle = true
      break
    end
  end
  local nSeries, producerType = GM.ItemRecycleModel:GetCurMaxSeries()
  local shouldStoreMaxPd = self:ShouldPlayPdInventoryAnimation(producerType)
  if shouldStoreMaxPd then
    SpriteUtil.SetImage(self.m_pdImg, producerType, true)
    SpriteUtil.SetImage(self.m_pdImg2, producerType, true)
  else
    self.m_btmGo:SetActive(false)
    UIUtil.AddSizeDelta(self.m_contentRect, 0, -320)
  end
  if self.m_bCanRecycle then
    self.m_energyCount = GM.ItemRecycleModel:CalculateEnergyCount(self.m_arrDishes, self.m_arrInstrus, self.m_arrOthers, self.m_mapInInstrumentItems)
    self.m_energyText.text = "x" .. self.m_energyCount
    self:InitCells()
  else
    self.m_topGo:SetActive(false)
    UIUtil.AddSizeDelta(self.m_contentRect, 0, -760)
  end
  if not shouldStoreMaxPd and not self.m_bCanRecycle then
    Log.Error("棋子回收弹窗逻辑错误")
    self:Close()
  end
  self:LogWindowAction(EBIType.UIActionType.Open, EBIReferType.AutoPopup)
end

function ItemRecycleWindow:InitCells()
  local arrDishes = Table.ShallowCopy(self.m_arrDishes)
  local arrInstrus = Table.ShallowCopy(self.m_arrInstrus)
  local arrOthers = Table.ShallowCopy(self.m_arrOthers)
  local itemDataModel = GM.ItemDataModel
  local itemType
  for materialItem, cookItem in pairs(self.m_mapInInstrumentItems) do
    itemType = materialItem:GetType()
    if itemDataModel:IsDishes(itemType) then
      arrDishes[#arrDishes + 1] = materialItem
    else
      arrOthers[#arrOthers + 1] = materialItem
    end
  end
  self:SortArray(arrDishes, false)
  self:SortArray(arrInstrus, true)
  self:SortArray(arrOthers, true)
  self:InitTypeCells(arrDishes)
  self:InitTypeCells(arrInstrus)
  self:InitTypeCells(arrOthers)
end

function ItemRecycleWindow:InitTypeCells(sortedArray)
  local firstItem = sortedArray[1]
  if not sortedArray[1] then
    return
  end
  local type = firstItem:GetType()
  local num = 1
  local eTag = firstItem.eRecycleTag
  local cookType = self:_GetRelaventCookItemType(firstItem)
  if not sortedArray[2] then
    self:AddCell(type, num, eTag, cookType)
    return
  end
  local tp, et, ct, itemModel
  for i = 2, #sortedArray - 1 do
    itemModel = sortedArray[i]
    tp = itemModel:GetType()
    et = itemModel.eRecycleTag
    ct = self:_GetRelaventCookItemType(itemModel)
    if tp ~= type or et ~= eTag or ct ~= cookType then
      self:AddCell(type, num, eTag, cookType)
      type = tp
      eTag = et
      cookType = ct
      num = 1
    else
      num = num + 1
    end
  end
  itemModel = sortedArray[#sortedArray]
  tp = itemModel:GetType()
  et = itemModel.eRecycleTag
  ct = self:_GetRelaventCookItemType(itemModel)
  if tp ~= type or et ~= eTag or ct ~= cookType then
    self:AddCell(type, num, eTag, cookType)
    self:AddCell(tp, 1, et, ct)
  else
    self:AddCell(type, num + 1, eTag, cookType)
  end
end

function ItemRecycleWindow:_GetRelaventCookItemType(materialItem)
  local cookCmp = self.m_mapInInstrumentItems[materialItem]
  if not cookCmp then
    return nil
  end
  return cookCmp:GetItemModel():GetType()
end

function ItemRecycleWindow:SortArray(array, checkLevel)
  local itemDataModel = GM.ItemDataModel
  local nSeries1, nSeries2, type1, type2, chain1, chain2, level1, level2
  table.sort(array, function(item1, item2)
    type1 = item1:GetType()
    type2 = item2:GetType()
    nSeries1 = itemDataModel:GetItemSeries(type1)
    nSeries2 = itemDataModel:GetItemSeries(type2)
    if nSeries1 ~= nSeries2 then
      return nSeries1 > nSeries2
    end
    chain1 = itemDataModel:GetChainId(type1)
    chain2 = itemDataModel:GetChainId(type2)
    if chain1 ~= chain2 then
      return chain1 > chain2
    end
    if checkLevel then
      level1 = itemDataModel:GetChainLevel(type1)
      level2 = itemDataModel:GetChainLevel(type2)
      if level1 ~= level2 then
        return level1 > level2
      end
    end
    if item1.eRecycleTag ~= item2.eRecycleTag then
      return item1.eRecycleTag < item2.eRecycleTag
    elseif item1.eRecycleTag == EItemRecycleTag.InInstrument then
      local cook1 = self.m_mapInInstrumentItems[item1]
      local cook2 = self.m_mapInInstrumentItems[item2]
      if not cook1 or not cook2 then
        return true
      end
      local cookType1 = cook1:GetItemModel():GetType()
      local cookType2 = cook2:GetItemModel():GetType()
      return cookType1 < cookType2
    end
    return item1:GetId() < item2:GetId()
  end)
end

function ItemRecycleWindow:AddCell(type, num, eTag, cookType)
  local newCellGo = GameObject.Instantiate(self.m_cellTemplateGo, self.m_cellRoot)
  newCellGo:SetActive(true)
  local newCell = newCellGo:GetLuaTable()
  newCell:UpdateContent(type, num, eTag, cookType)
end

function ItemRecycleWindow:OnGreenClick()
  MainBoardView:GetInstance():UpdateSelectedItem(nil)
  if self.m_bCanRecycle and GM.ItemRecycleModel:ConfirmRecycle(self.m_arrDishes, self.m_arrInstrus, self.m_arrOthers, self.m_mapInInstrumentItems) then
    GM.UIManager:OpenViewWhenIdle(UIPrefabConfigName.ItemRecycleAnimationWindow, self.m_arrDishes, self.m_arrInstrus, self.m_arrOthers, self.m_mapInInstrumentItems, self.m_energyCount)
  end
  local nSeries, producerType = GM.ItemRecycleModel:GetCurMaxSeries()
  local producerItem = self:ShouldPlayPdInventoryAnimation(producerType)
  if producerItem then
    local producerView = MainBoardView:GetInstance():GetItemView(producerItem)
    local boardWorldPosition = producerView.transform.position
    GM.MainBoardModel:StoreItem(producerItem)
    GM.UIManager:SetEventLock(true)
    PropertyAnimationManager.AddFlyingCount()
    DelayExecuteFunc(function()
      GM.UIManager:SetEventLock(false)
      PropertyAnimationManager.RemoveFlyingCount()
      ItemRecycleWindow.PlayPdInventoryAnimation(producerType, boardWorldPosition)
    end, 0.5)
  end
  self:LogWindowAction(EBIType.UIActionType.Click, nil, "confirm")
  self:Close()
end

function ItemRecycleWindow:OnRedClick()
  self:LogWindowAction(EBIType.UIActionType.Click, nil, "cancel")
  self:Close()
end

function ItemRecycleWindow:ShouldPlayPdInventoryAnimation(producerType)
  return GM.MainBoardModel:GetOneBoardItemByType(producerType)
end

function ItemRecycleWindow.PlayPdInventoryAnimation(producerType, boardWorldPosition)
  local screenPosition = MainBoardView:GetInstance():ConvertWorldPositionToScreenPosition(boardWorldPosition)
  local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
  uiWorldPosition.z = 0
  local moveDuration = 0.7
  local targetPosition = TutorialHelper.GetHudButton(ESceneViewHudButtonKey.Inventory).transform.position
  local fromScale = 1
  local targetScale = 1
  PropertyAnimationManager.PlayFlyElementAnimation(producerType, moveDuration, uiWorldPosition, targetPosition, fromScale, targetScale, true, true, 10, nil, function()
    local inventoryButton = TutorialHelper.GetHudButton(ESceneViewHudButtonKey.Inventory)
    if inventoryButton then
      inventoryButton:IconScaleAnimation(true, false)
    end
  end)
end

ItemRecycleCell = {}
ItemRecycleCell.__index = ItemRecycleCell

function ItemRecycleCell:UpdateContent(type, num, eTag, cookType)
  SpriteUtil.SetImage(self.m_image, type)
  self.m_countText.text = "x" .. num
  if eTag == EItemRecycleTag.InBoard then
    self.m_tagGo:SetActive(false)
  else
    self.m_tagGo:SetActive(true)
    if eTag == EItemRecycleTag.InInventory then
      SpriteUtil.SetImage(self.m_tagImg, ImageFileConfigName.game_icon_cabinet)
    elseif eTag == EItemRecycleTag.InInstrument then
      SpriteUtil.SetImage(self.m_tagImg, cookType)
    elseif eTag == EItemRecycleTag.InCache then
      SpriteUtil.SetImage(self.m_tagImg, ImageFileConfigName.icon_board_cache)
    end
  end
end
