EnergyBoostHelpWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  disableEffectWhenCloseView = true
}, BaseWindow)
EnergyBoostHelpWindow.__index = EnergyBoostHelpWindow

function EnergyBoostHelpWindow:Init()
  for i = 1, 3 do
    self:_LoadPdImage(i)
  end
  self.m_descText1.text = GM.GameTextModel:GetText("doubleEnergy_hint1", 2)
  self.m_descText3.text = GM.GameTextModel:GetText("doubleEnergy_hint3", 1)
end

function EnergyBoostHelpWindow:_LoadPdImage(index, fadeInDelay)
  local imageCmp = self["m_pdImg" .. index]
  if not imageCmp then
    return
  end
  local itemDataModel = GM.ItemDataModel
  local maxUnlockedLevel = 4
  local chain = "pd_" .. index
  local chainMaxLevel = itemDataModel:GetChainMaxLevel(chain)
  for level = maxUnlockedLevel + 1, chainMaxLevel do
    if itemDataModel:IsUnlocked(chain .. "_" .. level) then
      maxUnlockedLevel = level
    else
      break
    end
  end
  local imageKey = chain .. "_" .. maxUnlockedLevel
  SpriteUtil.SetImage(imageCmp, ImageFileConfigName[imageKey], true)
end
