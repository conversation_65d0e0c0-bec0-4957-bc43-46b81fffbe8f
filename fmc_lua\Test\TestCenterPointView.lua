TestCenterPointView = setmetatable({
  eCloseType = EViewCloseType.Hide
}, BaseView)
TestCenterPointView.__index = TestCenterPointView

function TestCenterPointView:SetActive(bShow)
  if bShow then
    if GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.TestCenterPointView) then
      GM.UIManager:CloseView(UIPrefabConfigName.TestCenterPointView)
    else
      GM.UIManager:OpenView(UIPrefabConfigName.TestCenterPointView)
    end
  else
    GM.UIManager:CloseView(UIPrefabConfigName.TestCenterPointView)
  end
end
