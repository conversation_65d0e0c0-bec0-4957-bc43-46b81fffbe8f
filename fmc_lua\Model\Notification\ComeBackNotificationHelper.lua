ComeBackNotificationHelper = setmetatable({}, BaseNotificationHelper)
ComeBackNotificationHelper.__index = ComeBackNotificationHelper

function ComeBackNotificationHelper.IsSceneExist(strScene)
  if string.find(strScene, NotificationScene.ComeBack) ~= nil then
    return true
  end
  return false
end

function ComeBackNotificationHelper.GetHours(strScene)
  local strSplit = StringUtil.Split(strScene, "_")
  if strSplit[2] ~= nil then
    return tonumber(strSplit[2]) * 3600
  end
  Log.Assert(false, "ComeBack 配置错误")
  return Sec2Day
end

function ComeBackNotificationHelper.Generate(strScene)
  local results = {}
  local llTimeSec = ComeBackNotificationHelper.GetHours(strScene)
  local strTileKey, strDescKey = GM.NotificationModel:GetTextTileAndDesc(strScene)
  strTileKey = strTileKey ~= "" and strTileKey or "push_come_back_title"
  strDescKey = strDescKey ~= "" and strDescKey or "push_come_back_desc"
  table.insert(results, {
    Type = NotificationType.ComeBack,
    Title = GM.GameTextModel:GetText(strTileKey),
    Message = GM.GameTextModel:GetText(strDescKey),
    Delay = llTimeSec
  })
  return results
end
