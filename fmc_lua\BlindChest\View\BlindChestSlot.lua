BlindChestSlot = {}
BlindChestSlot.__index = BlindChestSlot

function BlindChestSlot:Init(mainWindow, index, model)
  self.m_model = model
  self.m_mainWindow = mainWindow
  self.m_index = index
  self:UpdateState()
  self.m_spineAnim:Init()
  self.m_spineAnim:SetTimeScale(1 + index * 0.02)
  self.m_spineAnim:PlayAnimation("idle", nil, true)
  UIUtil.UpdateSortingOrder(self.gameObject, mainWindow:GetSortingOrder() + 1)
end

function BlindChestSlot:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  if self.m_openSeq ~= nil then
    self.m_openSeq:Kill()
    self.m_openSeq = nil
  end
end

function BlindChestSlot:GetIndex()
  return self.m_index
end

function BlindChestSlot:UpdateState()
  local roundSlotNumber = self.m_model:GetTurnConfig().SlotNumber
  if roundSlotNumber < self.m_index then
    self.gameObject:SetActive(false)
    return
  end
  self.gameObject:SetActive(true)
  self.m_buttonGo.transform.localScale = self.m_model:IsSlotOpened(self.m_index) and V3Zero or V3One
end

function BlindChestSlot:PlayHideAnim()
  self.transform:DOScale(Vector3(0, 0, 0), 0.3):SetEase(Ease.OutCubic)
end

function BlindChestSlot:PlayEnterAnim()
  local originScale = self.transform.localScale
  self.transform.localScale = Vector3(0, 0, 0)
  DelayExecuteFuncInView(function()
    DelayExecuteFuncInView(function()
      self.m_openEffect:Play()
    end, 0.3, self)
    self.transform:DOScale(originScale + Vector3(0.1, 0.1, 0), 0.4):SetEase(Ease.OutCubic)
    self.transform:DOScale(originScale, 0.1):SetDelay(0.4)
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxBlindChestTreasure1)
  end, (self.m_index - 1) * 0.1, self)
end

function BlindChestSlot:PlayOpenAnimation(result)
  self.m_bClicked = true
  self.m_spineAnim:SetTimeScale(1)
  self.m_spineAnim:PlayAnimation("open", nil, false)
  local seq = DOTween.Sequence()
  seq:Append(self.m_spineAnim.transform:DOScale(Vector3(1.1, 1.1, 1), 0.1))
  seq:Append(self.m_spineAnim.transform:DOScale(Vector3(1, 1, 1), 0.1))
  seq:AppendInterval(0.8)
  seq:AppendCallback(function()
    if result == BlindChestModel.EmptyRewardIndex then
      self.m_emptyEffect:Play()
      GM.AudioModel:PlayEffect(AudioFileConfigName.SfxBlindChestRod2)
    elseif result == BlindChestModel.TopRewardIndex then
      self.m_topRewardEffect:Play()
      GM.AudioModel:PlayEffect(AudioFileConfigName.SfxBlindChestTreasure2)
    else
      self.m_otherRewardEffect:Play()
      GM.AudioModel:PlayEffect(AudioFileConfigName.sfxBlindChestOpen)
    end
    self.m_openSeq = nil
  end)
  self.m_openSeq = seq
end

function BlindChestSlot:OnClicked()
  if self.m_bClicked then
    return
  end
  EventDispatcher.DispatchEvent(EEventType.BlindChestClickSlot)
  self.m_mainWindow:OnSlotClicked(self)
end

function BlindChestSlot:GetTargetTrans()
  return self.m_targetTrans
end
