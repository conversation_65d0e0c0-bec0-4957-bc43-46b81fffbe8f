HudPropertyButton = setmetatable({
  HitAudio = AudioFileConfigName.SfxRewardCrush
}, HudBaseButton)
HudPropertyButton.__index = HudPropertyButton

function HudPropertyButton:Awake()
  HudBaseButton.Awake(self)
  self:_AddListeners()
  if self.ePropertyType then
    self:SyncToModelValue()
  end
end

function HudPropertyButton:Init(ePropertyType)
  self.ePropertyType = ePropertyType
  self:SyncToModelValue()
  self.m_valueTween = nil
  if self.gameObject.activeInHierarchy then
    self:_AddListeners()
  end
end

function HudPropertyButton:_AddListeners()
  if self.m_hasAddListener then
    return false
  end
  EventDispatcher.AddListener(EEventType.FlyingElementClear, self, self.OnFlyingElementClear)
  EventDispatcher.AddListener(EEventType.PropertyAcquired, self, self.OnPropertyAcquired)
  EventDispatcher.AddListener(EEventType.PropertyConsumed, self, self.OnPropertyConsumed)
  EventDispatcher.AddListener(EEventType.CloseView, self, self._OnCloseView)
  EventDispatcher.AddListener(EEventType.EventLockClear, self, self._DelaySyncModelValue)
  self.m_hasAddListener = true
  return true
end

function HudPropertyButton:_OnCloseView()
  self:_DelaySyncModelValue()
end

function HudPropertyButton:_DelaySyncModelValue()
  DelayExecuteFuncInView(function()
    if self:_CanSyncToModelValue() then
      self:SyncToModelValue()
    end
  end, 1, self)
end

function HudPropertyButton:_CanSyncToModelValue()
  if GM.UIManager.allWindowClosed and PropertyAnimationManager.uiLockFlyingCount <= 0 and not GM.UIManager:IsEventLock() then
    return true
  end
  return false
end

function HudPropertyButton:SyncToModelValue()
  if self.m_valueTween then
    self.m_valueTween:Kill()
    self.m_valueTween = nil
  end
  self.m_value = self:GetPropertyNum()
  self.m_iTextAnimationTo = self.m_value
  self:UpdateValueText()
end

function HudPropertyButton:UpdateValueText()
  if self.m_valueText then
    self.m_valueText.text = math.floor(self.m_value + 0.5)
  end
end

function HudPropertyButton:IsValueSyncedToModelValue()
  if PropertyAnimationManager.uiLockFlyingCount == 0 then
    return true
  end
  return self.m_value == self:GetPropertyNum()
end

function HudPropertyButton:OnClicked()
end

function HudPropertyButton:GetPropertyNum()
  if self.ePropertyType == nil then
    local strViewName = "nil"
    if self.gameObject ~= nil and not self.gameObject:IsNull() then
      strViewName = self.gameObject.name
    end
    Log.Error("[HudPropertyButton] propertyType is nil, name:" .. strViewName)
    return 0
  end
  return GM.PropertyDataManager:GetPropertyNum(self.ePropertyType)
end

local UPDATE_TEXT_ANIMATION_DURATION = {
  0.1,
  0.3,
  0.5
}

function HudPropertyButton:GetTextAnimationDuration(diff)
  diff = math.abs(diff)
  if diff <= 1 then
    return UPDATE_TEXT_ANIMATION_DURATION[1]
  elseif diff <= 10 then
    return UPDATE_TEXT_ANIMATION_DURATION[2]
  else
    return UPDATE_TEXT_ANIMATION_DURATION[3]
  end
end

function HudPropertyButton:IconScaleAnimation(needEffect, checkType)
  if self:_CheckPropertyType(checkType) then
    HudBaseButton.IconScaleAnimation(self, needEffect)
    if needEffect then
      self:PlayEffect()
    end
  end
end

function HudPropertyButton:PlayEffect()
  if not StringUtil.IsNilOrEmpty(self.HitAudio) then
    GM.AudioModel:PlayEffect(self.HitAudio)
  end
  PlatformInterface.Vibrate(EVibrationType.Light)
end

function HudPropertyButton:_CheckPropertyType(checkType)
  return self.ePropertyType == checkType
end

function HudPropertyButton:UpdateTextAnimation(checkType, valueChange, isMiddleStep)
  if not self:_CheckPropertyType(checkType) then
    return
  end
  local valueFrom = self.m_value
  local modelValue = self:GetPropertyNum()
  local valueTo = modelValue
  if not self.m_value or not self.m_iTextAnimationTo then
    self:TweenCompleteCallback()
    local strTextAnimationInited = self.m_iTextAnimationTo and "Inited" or "nil"
    local errInfo = "[HudPropertyButton] " .. self.gameObject.name .. " m_value is nil, self.m_iTextAnimationTo is " .. strTextAnimationInited
    Log.Error(errInfo)
    return
  end
  if valueChange then
    valueTo = self.m_iTextAnimationTo + valueChange
    valueTo = math.min(valueTo, modelValue)
    valueTo = math.max(valueTo, 0)
  end
  if not self.m_valueTween or valueTo ~= self.m_iTextAnimationTo then
    local duration = isMiddleStep and FLY_ELE_ANIMATION_INTERVAL or self:GetTextAnimationDuration(valueTo - valueFrom)
    self.m_iTextAnimationTo = valueTo
    if self.m_valueTween then
      self.m_valueTween:Kill()
    end
    self.m_valueTween = DOVirtual.Float(valueFrom, valueTo, duration, function(x)
      self.m_value = x
      self:UpdateValueText()
    end)
    
    function self.m_valueTween.onComplete()
      if isMiddleStep then
        self.m_value = valueTo
        self:UpdateValueText()
      elseif valueChange == nil or 0 <= valueChange then
        self:TweenCompleteCallback()
      end
    end
  end
end

function HudPropertyButton:TweenCompleteCallback()
  if self.m_bListenToFlyingElementClear then
    self.m_bListenToFlyingElementClear = nil
  end
  DelayExecuteFuncInView(function()
    if PropertyAnimationManager.uiLockFlyingCount == 0 then
      if self:_CanSyncToModelValue() then
        self:SyncToModelValue()
      else
        self:_DelaySyncModelValue()
      end
    else
      self.m_bListenToFlyingElementClear = true
    end
  end, 1, self)
end

function HudPropertyButton:OnFlyingElementClear()
  if self.m_bListenToFlyingElementClear then
    self.m_bListenToFlyingElementClear = nil
    self:SyncToModelValue()
  end
end

function HudPropertyButton:OnPropertyAcquired(msg)
  for _, item in ipairs(msg.arrProperties) do
    if item[PROPERTY_TYPE] == self.ePropertyType then
      self.m_bListenToFlyingElementClear = nil
      Scheduler.UnscheduleTarget(self)
      break
    end
  end
end

function HudPropertyButton:OnPropertyConsumed(msg)
  local type = msg.property[PROPERTY_TYPE]
  if type == self.ePropertyType then
    self.m_bListenToFlyingElementClear = nil
    Scheduler.UnscheduleTarget(self)
  end
end

function HudPropertyButton:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  Scheduler.UnscheduleTarget(self)
  if self.m_valueTween then
    self.m_valueTween:Kill()
    self.m_valueTween = nil
  end
end
