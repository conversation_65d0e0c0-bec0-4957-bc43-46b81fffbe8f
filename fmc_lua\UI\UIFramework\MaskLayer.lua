MaskLayer = {}
MaskLayer.__index = MaskLayer

function MaskLayer:Awake()
  self.m_iconTween = self.m_imageTrans:DOLocalRotate(Vector3(0, 0, -360), 1, RotateType.FastBeyond360):SetEase(Ease.Linear):SetLoops(-1, LoopType.Restart):Pause()
end

function MaskLayer:Show()
  self.transform.localScale = V3One
  self.m_containerGo:SetActive(false)
  self.startAnimation = true
  self.time = 0
  self.m_iIndex = 0
  self.m_fDt = 0
end

function MaskLayer:Hide()
  self.transform.localScale = Vector3.zero
  if self.m_iconTween then
    self.m_iconTween:Pause()
  end
  self.didStart = false
  self.startAnimation = false
  self.m_fDt = nil
end

function MaskLayer:Update(dt)
  if self.startAnimation then
    if self.time >= 0.3 then
      self.time = 0
      if not self.didStart then
        self:StartAnimation()
      end
      self:ShowEllipsisAnimation()
    end
    self.time = self.time + dt
  end
end

function MaskLayer:OnD<PERSON>roy()
  self:Hide()
  if self.m_iconTween then
    self.m_iconTween:Kill()
    self.m_iconTween = nil
  end
end

function MaskLayer:StartAnimation()
  self.didStart = true
  self.m_containerGo:SetActive(true)
  self.m_iIndex = 0
  self.m_iconTween:Restart()
end

function MaskLayer:ShowEllipsisAnimation()
  self.m_iIndex = self.m_iIndex % 4
  self.m_ellipsisText.text = string.rep(".", self.m_iIndex)
  self.m_iIndex = self.m_iIndex + 1
end

function MaskLayer:UpdatePerSecond()
  if self.m_fDt == nil then
    return
  end
  self.m_fDt = self.m_fDt + 1
  if self.m_fDt > 180 then
    GM.UIManager:HideMask()
  end
end
