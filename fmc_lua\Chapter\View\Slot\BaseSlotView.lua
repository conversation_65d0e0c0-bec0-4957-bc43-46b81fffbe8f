BaseSlotView = {}
BaseSlotView.__index = BaseSlotView

function BaseSlotView:Init(roomModel, slotId)
  self.roomModel = roomModel
  self.slotId = slotId
end

function BaseSlotView:GetNamePrefix()
  return self.roomModel.chapterName .. "_" .. self.slotId .. "_"
end

function BaseSlotView:PlayTransAnimation(animationConfig, callback)
  local animationSplits = StringUtil.Split(animationConfig, "-")
  local prefabName = animationSplits[1]
  local animName = animationSplits[2]
  local prefab = ScenePrefabConfigName[prefabName]
  if prefab == nil then
    Log.Error("prefab " .. prefabName .. " 不存在！请策划检查配置！")
    if callback then
      callback()
    end
    return
  end
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(prefab), self.m_animRoot, Vector3.zero, function(go)
    local spine = go:GetLuaTable()
    if spine == nil then
      Log.Error("prefab " .. prefabName .. " 没挂脚本！请资源策划检查！")
      if callback then
        callback()
      end
      return
    end
    spine:Init(self.roomModel.chapterName)
    local func = function()
      go:RemoveSelf()
      if callback then
        callback()
      end
    end
    if animName then
      if spine:HasSetInitAnim() then
        Log.Error("Prefab " .. prefabName .. " 中已设置初始动画！配置中指定动画名时 prefab 中最好不要设置，否则会出现动画硬切！")
      end
      spine:PlayAnimation(animName, func)
    else
      local valid = spine:HasSetInitAnim()
      if not valid then
        func()
        Log.Error("Prefab " .. prefabName .. " 中未设置初始动画且配置中未配动画名，动画无法播放！请策划检查配置！")
      else
        spine:SetCompleteCallback(func)
      end
    end
  end)
end

function BaseSlotView:PlayEffect(prefabConfig, callback)
  local prefabName = prefabConfig
  local position = V3Zero
  local scale = V3One
  local middlePos = string.find(prefabConfig, ",")
  if middlePos ~= nil then
    prefabName = string.sub(prefabConfig, 1, middlePos - 1)
    local posConfig = string.sub(prefabConfig, middlePos + 1)
    local pos = TimelineStep.GetPositionList(posConfig)[1]
    scale = V3One * pos[3]
    position = Vector3(pos[1], pos[2], 0)
  end
  local prefab = ScenePrefabConfigName[prefabName]
  if prefab == nil then
    Log.Error("prefab " .. prefabName .. " 不存在！请策划检查配置！")
    if callback then
      callback()
    end
    return
  end
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(prefab), self.m_loadRoot, position, function(go)
    go.transform:SetParent(self.m_animRoot)
    go.transform.localPosition = position
    go.transform.localScale = scale
    if callback then
      local luaComp = go:GetLuaTable()
      if luaComp == nil then
        Log.Error("prefab " .. prefabName .. " 没挂脚本！请资源策划检查！")
        callback()
        return
      end
      luaComp:SetCompleteCallback(function()
        go:RemoveSelf()
        callback()
      end)
    end
  end)
end

function BaseSlotView:OnDisable()
  self.m_animRoot.gameObject:RemoveChildren()
end
