SurveyChoice = {}
SurveyChoice.__index = SurveyChoice

function SurveyChoice:Init(choiceData)
  self.choiceData = choiceData
  self.m_text.text = choiceData.title
end

function SurveyChoice:IsSelected()
  return self.m_toggle.isOn
end

function SurveyChoice:GetInput()
  return self.m_inputField and self.m_inputField.text or ""
end

function SurveyChoice:_OnChoiceChange()
  if self.m_selectBg then
    local img = self.m_toggle.isOn and ImageFileConfigName.choice_selected_bg or ImageFileConfigName.choice_bg
    SpriteUtil.SetImage(self.m_selectBg, img)
  end
  if self.m_checkGo then
    self.m_checkGo:SetActive(self.m_toggle.isOn)
  end
  if self.choiceData == nil or not self.choiceData.is_open then
    return
  end
  if self.m_inputFieldGo then
    self.m_inputFieldGo:SetActive(self.m_toggle.isOn)
  end
  if self.m_parent and self.m_parent.UpdateView then
    self.m_parent:UpdateView()
  end
end

function SurveyChoice:SetParent(parent)
  self.m_parent = parent
end
