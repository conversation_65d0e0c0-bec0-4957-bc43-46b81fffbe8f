ScreenFitter = {}
ScreenFitter.__index = ScreenFitter

function ScreenFitter.GetStandardRatio()
  return CSScreenFitter.STANDARD_RATIO
end

function ScreenFitter.GetStandardHeight()
  return CSScreenFitter.STANDARD_HEIGHT
end

function ScreenFitter.GetStandardWidth()
  return CSScreenFitter.STANDARD_WIDTH
end

function ScreenFitter.GetScreenAdjustSize()
  return CSScreenFitter.GetAdjustSize(Screen.width / Screen.height)
end

function ScreenFitter.IsWideScreen()
  return Screen.width / Screen.height > ScreenFitter.GetStandardRatio()
end

function ScreenFitter.GetFitScale(normalScale, minScale, maxRatio, minRatio)
  Log.Assert(minScale <= normalScale, "ScreenFitter.GetFitScale")
  minScale = math.min(minScale, normalScale)
  maxRatio = maxRatio or 1.7777777777777777
  minRatio = minRatio or 1.0
  local scale = normalScale
  local ratio = Screen.height / Screen.width
  if maxRatio <= ratio then
    scale = normalScale
  elseif minRatio >= ratio then
    scale = minScale
  else
    local ratioPercent = (ratio - minRatio) / (maxRatio - minRatio)
    scale = ratioPercent * (normalScale - minScale) + minScale
  end
  return scale
end

function ScreenFitter.NeedNotch()
  return PlatformInterface.HasNotch() or Screen.width / Screen.height < ScreenFitter.GetStandardRatio()
end

function ScreenFitter._GetNotchAdaptiveHeight(factor)
  return math.max(0, (Screen.height * ScreenFitter.GetStandardWidth() / (Screen.width * ScreenFitter.GetStandardHeight())) ^ 2 - 1) * factor
end

function ScreenFitter.GetNotchHeight()
  local baseHeight = PlatformInterface.HasNotch() and 40 or 0
  return baseHeight + ScreenFitter._GetNotchAdaptiveHeight(230)
end

function ScreenFitter.GetBoardOffsetY()
  if not ScreenFitter.NeedNotch() then
    return 0
  end
  return ScreenFitter._GetNotchAdaptiveHeight(200) - ScreenFitter._GetNotchAdaptiveHeight(230)
end

function ScreenFitter.GetSceneViewSizeOffset()
  if not ScreenFitter.NeedNotch() then
    return Vector2(20, 20)
  else
    local notchHeight = ScreenFitter.GetNotchHeight()
    return Vector2(notchHeight, math.min(notchHeight, 40))
  end
end

function ScreenFitter.GetBoardScale()
  return ScreenFitter.GetFitScale(1.0, 0.92, 2.111111111111111, 1.7777777777777777)
end
