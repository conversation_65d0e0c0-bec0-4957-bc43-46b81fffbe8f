TestScreenDragView = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow
}, BaseView)
TestScreenDragView.__index = TestScreenDragView

function TestScreenDragView:Init()
  local _, mapInput = GM.ModeViewController:GetChapterCam()
  self.m_mapInput = mapInput
  self.m_baseScreenLuaTable:Init("SPEED_HEIGHT_BASE", 0, 4000, self.m_mapInput)
  self.m_dragFactorLuaTable:Init("SCREEN_OFFSET_X_FACTOR", 0, 2, self.m_mapInput)
  self.m_inertiaFactorLuaTable:Init("SCREEN_OFFSET_Y_FACTOR", 0, 2, self.m_mapInput)
  self.m_scrollFactorLuaTable:Init("INERTIA_DECELERATION_RATE", 0, 2, self.m_mapInput)
end

function TestScreenDragView:Reset()
  SPEED_HEIGHT_BASE = 2040
  TOUCH_MOVE_SPEED_FACTOR = 3000
  SCROLL_SPEED = 5
  INERTIA_DECELERATION_RATE = 0.85
  SCREEN_OFFSET_X_FACTOR = 1
  SCREEN_OFFSET_Y_FACTOR = 1
  self.m_mapInput:_FitScreen()
end

TestScreenDragCell = {}
TestScreenDragCell.__index = TestScreenDragCell

function TestScreenDragCell:Init(globalKey, from, to, mapInput)
  self.m_key = globalKey
  self.from = from
  self.to = to
  self.m_rangeText.text = "(" .. from .. "~" .. to .. ")"
  self.m_speedSlider.minValue = from
  self.m_speedSlider.maxValue = to
  self.m_speedSlider.value = _G[self.m_key]
  self.m_oldValue = _G[self.m_key]
  self.m_mapInput = mapInput
end

function TestScreenDragCell:OnValueChange()
  self.m_InputFieldInpu.text = self.m_speedSlider.value
  _G[self.m_key] = self.m_speedSlider.value
  if self.m_mapInput then
    self.m_mapInput:_FitScreen()
  end
end

function TestScreenDragCell:OnEndEdit()
  self.m_speedSlider.value = tonumber(self.m_InputFieldInpu.text)
  if self.m_mapInput then
    self.m_mapInput:_FitScreen()
  end
end
