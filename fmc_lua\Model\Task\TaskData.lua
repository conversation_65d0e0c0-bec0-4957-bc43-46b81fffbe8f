TaskData = {}
TaskData.__index = TaskData

function TaskData.Create(tbData, chapterId, chapterName)
  local data = setmetatable(tbData, TaskData)
  data:Init(chapterId, chapterName)
  return data
end

function TaskData:Init(chapterId, chapterName)
  self.chapterId = chapterId
  self.chapterName = chapterName
  table.insert(self.Rewards, 1, {
    [PROPERTY_TYPE] = EPropertyType.TaskProgress,
    [PROPERTY_COUNT] = 1
  })
  RewardApi.CryptRewards(self.Rewards)
end

function TaskData:_GetFirstSlotId()
  local slotId
  if self.SlotState and self.SlotState[1] then
    slotId = self.SlotState[1].Slot
  end
  Log.Assert(slotId ~= nil, tostring(self.Id) .. " 任务缺少 title icon 配置!")
  return slotId or ""
end

function TaskData:_GetSlotIdsStr()
  local str = ""
  if not self.SlotState then
    return str
  end
  local slotCount = #self.SlotState
  for index, changeData in ipairs(self.SlotState) do
    str = str .. changeData.Slot
    if index < slotCount then
      str = str .. ","
    end
  end
  return str
end
