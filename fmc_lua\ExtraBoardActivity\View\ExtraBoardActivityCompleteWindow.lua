ExtraBoardActivityCompleteWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, ExtraBoardActivityBaseWindow)
ExtraBoardActivityCompleteWindow.__index = ExtraBoardActivityCompleteWindow

function ExtraBoardActivityCompleteWindow:Init(activityType, bUser<PERSON>lick)
  ExtraBoardActivityBaseWindow.Init(self, activityType, bUserClick)
  local itemCode = self.m_model:GetItemCodeByLevel(self.m_model:GetMaxLevel())
  SpriteUtil.SetImage(self.m_itemImg, GM.ItemDataModel:GetSpriteName(itemCode), true)
  local maxReward = self.m_model:GetMaxLevelReward()
  if not Table.IsEmpty(maxReward) then
    self.m_rewardContent:Init(maxReward)
    self.m_bPlayRewardAnim = true
  end
end

function ExtraBoardActivityCompleteWindow:OnCloseBtnClick()
  self:Close()
end

function ExtraBoardActivityCompleteWindow:OnCloseView(...)
  ExtraBoardActivityBaseWindow.OnCloseView(self, ...)
  if self.m_rewardContent ~= nil and self.m_bPlayRewardAnim then
    self.m_rewardContent:PlayRewardAnimation()
  end
  local bookModel = self.m_model:GetBoardModel():GetItemIllustratedBook()
  if bookModel ~= nil and self.m_activityDefinition.BookWindowPrefabName ~= nil then
    GM.UIManager:OpenView(self.m_activityDefinition.BookWindowPrefabName, self.m_activityType, bookModel)
  end
end
