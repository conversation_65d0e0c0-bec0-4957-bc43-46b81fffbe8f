ItemRewardBubbleView = setmetatable({}, BaseItemViewComponent)
ItemRewardBubbleView.__index = ItemRewardBubbleView

function ItemRewardBubbleView:Init(itemRewardBubbleModel)
  self.m_model = itemRewardBubbleModel
  local innerSpriteName = GM.ItemDataModel:GetSpriteName(self.m_model:GetInnerItemCode())
  SpriteUtil.SetSpriteRenderer(self.m_innerSpriteRenderer, innerSpriteName)
  AddHandlerAndRecordMap(self.m_model.event, ItemRewardBubbleEventType.Disposed, {
    obj = self,
    method = self._OnDisposed
  })
end

function ItemRewardBubbleView:_OnDisposed()
  UIUtil.SetActive(self.m_arrowGo, false)
  self.m_animator.enabled = false
  local sequence = DOTween.Sequence()
  sequence:Append(self.m_innerSpriteRenderer.transform:DOScale(0, 0.2))
  sequence:Append(self.m_bubbleSpriteRenderer.transform:DOScale(2, 0.8))
  sequence:Insert(0.2, self.m_bubbleSpriteRenderer:DOFade(0, 0.8))
  sequence:AppendCallback(function()
    self.m_seq = nil
  end)
  self.m_seq = sequence
end

function ItemRewardBubbleView:OnDestroy()
  if self.m_seq ~= nil then
    self.m_seq:Kill()
    self.m_seq = nil
  end
end

function ItemRewardBubbleView:SetItemView(...)
  BaseItemViewComponent.SetItemView(self, ...)
end
