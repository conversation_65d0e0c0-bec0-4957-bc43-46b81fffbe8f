ProgressActivityDefinition = {
  [ActivityType.ProgressActivity1] = {
    EntryButtonKey = ESceneViewHudButtonKey.ProgressActivity1,
    ActivityDataTableName = VirtualDBTableName.ProgressActivity1,
    ActivityTokenPropertyType = EPropertyType.ProgressActivity1,
    StateChangedEvent = EEventType.ProgressActivity1StateChanged,
    ScoreChangedEvent = EEventType.ProgressActivity1ScoreChanged,
    LevelChangedEvent = EEventType.ProgressActivity1LevelChanged,
    GetRewardsBIType = EBIType.ProgressActivity1GetRewards,
    GetScoreBIType = EBIType.ProgressActivity1GetScore,
    EntryPrefabName = UIPrefabConfigName.ProgressActivity1Entry,
    BoardEntryPrefabName = UIPrefabConfigName.ProgressActivity1BoardEntry,
    NoticeWindowPrefabName = UIPrefabConfigName.ProgressActivity1NoticeWindow,
    FinishFailWindowPrefabName = UIPrefabConfigName.ProgressActivity1FinishFailWindow,
    FinishSuccessWindowPrefabName = UIPrefabConfigName.ProgressActivity1FinishSuccessWindow,
    MainWindowPrefabName = UIPrefabConfigName.ProgressActivity1MainWindow,
    RewardProgressWindowPrefabName = UIPrefabConfigName.ProgressActivity1RewardProgressWindow,
    ActivityTokenIconName = ImageFileConfigName.progress_activity_1_token_icon,
    ResourceLabels = {
      AddressableLabel.ProgressActivityCommon,
      AddressableLabel.ProgressActivity1
    },
    TutorialStartCondition = ETutorialStartCondition.ProgressStart,
    WindowDescKey = "progress_ball_desc"
  }
}
for activityType, activityDefinition in pairs(ProgressActivityDefinition) do
  EPropertySprite[activityDefinition.ActivityTokenPropertyType] = activityDefinition.ActivityTokenIconName
  EPropertySpriteBig[activityDefinition.ActivityTokenPropertyType] = activityDefinition.ActivityTokenIconName
end
