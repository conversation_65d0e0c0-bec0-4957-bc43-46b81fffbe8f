LoopSpineAnimation = setmetatable({}, LoopAnimation)
LoopSpineAnimation.__index = LoopSpineAnimation

function LoopSpineAnimation:Start()
  LoopAnimation.Start(self)
  if self.notUseManager == nil then
    self._controlledByManager = GM.LoopAnimationManager:AddToQueue(self)
  end
  self.m_spine:Update(0)
  self.m_spine:LateUpdate()
end

function LoopSpineAnimation:_Init()
  self.m_animator = self.m_spine.Skeleton
  self.m_animatorState = self.m_spine.state or self.m_spine.AnimationState
  self._transform = self.gameObject.transform
  LoopAnimation._Init(self)
end

function LoopSpineAnimation:ReinitFromPool()
  LoopAnimation.ReinitFromPool(self)
  if self.notUseManager == nil then
    self._controlledByManager = GM.LoopAnimationManager:AddToQueue(self)
  end
end

function LoopSpineAnimation:_OnAnimationComplete()
  if self.m_curEffectGo then
    self.m_curEffectGo:SetActive(false)
    self.m_curEffectGo = nil
  end
  if self._controlledByManager then
    self.m_spine.enabled = false
    self._playing = false
    GM.LoopAnimationManager:OnAnimationCompleted(self)
  else
    LoopAnimation._OnAnimationComplete(self)
  end
  if self.m_aniCompleteCallback then
    self:m_aniCompleteCallback()
  end
end

function LoopSpineAnimation:_OnPlayAnimation()
  local effectGo = "m_" .. self.m_strCurrentAnimationName .. "EffectGo"
  if self[effectGo] then
    self[effectGo]:SetActive(true)
    self.m_curEffectGo = self[effectGo]
  end
end

local CSSceneCamera = CS.SceneCamera

function LoopSpineAnimation:IsCulled()
  if self.m_spine == nil or self.m_spine:IsNull() then
    return true
  end
  if self.gameObject.layer == LayerMask.NameToLayer("UI") then
    return false
  end
  return not CSSceneCamera.IsInViewport(self.m_spine.gameObject.transform)
end

function LoopSpineAnimation:OnDestroy()
  if self._controlledByManager and GM and GM.LoopAnimationManager then
    GM.LoopAnimationManager:RemoveFromQueue(self)
    self._controlledByManager = nil
  end
  LoopAnimation.OnDestroy(self)
end

function LoopSpineAnimation:PlayAnimation(animatName)
  self.m_spine.enabled = true
  self._playing = true
  LoopAnimation._PlayAnimation(self, animatName)
end

function LoopSpineAnimation:IsPlaying()
  return self._playing
end

function LoopSpineAnimation:SetIdleAnimation(idleName, idleCount)
  self.m_strIdleName = idleName or self.m_strIdleName
  self.m_strIdleCount = idleCount or self.m_strIdleCount
end

function LoopSpineAnimation:SetRandomAnimation(randomData)
  for animName, weight in pairs(randomData) do
    self[animName] = weight
  end
end

function LoopSpineAnimation:RefreshData()
  self:_InitData()
  self:_PlayAnimation()
end
