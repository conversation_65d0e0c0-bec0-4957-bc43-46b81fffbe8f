ItemDataModel = {}
ItemDataModel.__index = ItemDataModel
EItemUnlockState = {
  Unknown = 0,
  Locked = 1,
  Unlocked = 2,
  Rewarded = 3
}
local DBStateCloumn = "state"

function ItemDataModel:Init()
  self.m_unlockDB = GM.DBTableManager:GetTable(EDBTableConfigs.ItemUnlock)
  self.m_itemProduceEnergy = {}
end

function ItemDataModel:LoadFileConfig()
  self.m_mapModelConfigs = {}
  self.m_mapViewConigs = {}
  self.m_mapPropertyConfigs = {}
  self.m_arrItemType = {}
  self.m_mapNonDishMaterials = {}
  self.m_mapDishMaterials = {}
  self.m_mapItemSplits = {}
  self.m_mapRelaventItems = {}
  self.m_discoveriesMap = {}
  self.m_discoveriesItemsMap = {}
  local unorderedDiscoveriesMap = {}
  local isTestMode = GameConfig.IsTestMode()
  local configs = GM.ConfigModel:GetLocalConfig(LocalConfigKey.ItemProperty)
  for _, config in ipairs(configs) do
    self.m_mapPropertyConfigs[config.Type] = config
  end
  configs = GM.ConfigModel:GetLocalConfig(LocalConfigKey.ItemModel)
  local mathFloor = math.floor
  for _, config in ipairs(configs) do
    RewardApi.CryptRewards(config.CollectRewards)
    RewardApi.CryptOneReward(config.SellCurrency)
    local type = config.Type
    if config.Category and Table.ListContain(config.Category, EItemCategory.Dish) then
      config.ChainId = type
      config.ChainLevel = 1
    else
      local originIndex = StringUtil.rFindChar(type, "_")
      local index = originIndex or 0
      config.ChainId = string.sub(type, 1, index - 1)
      config.ChainLevel = tonumber(string.sub(type, index + 1))
      if isTestMode then
        Log.Assert(originIndex ~= nil, "item config error (解析index为空): " .. type)
        Log.Assert(config.ChainId ~= nil and config.ChainLevel ~= nil, "item config error (ChainId 或 ChainLevel为空): " .. type)
      end
    end
    if type ~= ItemType.PaperBox and type ~= ItemType.Cobweb and type ~= ItemType.Bubble and type ~= ItemType.RewardBubble then
      self.m_arrItemType[#self.m_arrItemType + 1] = type
    end
    self.m_mapModelConfigs[type] = config
    if isTestMode and config.BubbleChance ~= nil then
      Log.Assert(self.m_mapPropertyConfigs[type] ~= nil and self.m_mapPropertyConfigs[type].BubblePrice ~= nil, "item config error (Property配置中BubblePrice缺失): " .. type)
    end
    if not config.BookReward or not config.BookOrder then
    else
      local isDish = self:IsDishes(config.Type)
      local bookOrderId = config.BookOrder
      if not unorderedDiscoveriesMap[bookOrderId] then
        unorderedDiscoveriesMap[bookOrderId] = {}
        local tab = mathFloor(bookOrderId / 1000)
        if not self.m_discoveriesMap[tab] then
          self.m_discoveriesMap[tab] = {}
        end
        self.m_discoveriesMap[tab][#self.m_discoveriesMap[tab] + 1] = {
          title = isDish and bookOrderId or config.ChainId,
          order = bookOrderId
        }
      end
      config.BookSubOrder = config.BookSubOrder or 9999
      unorderedDiscoveriesMap[bookOrderId][#unorderedDiscoveriesMap[bookOrderId] + 1] = {
        type = config.Type,
        order = config.BookSubOrder
      }
    end
  end
  configs = GM.ConfigModel:GetLocalConfig(LocalConfigKey.ItemView)
  for _, config in ipairs(configs) do
    self.m_mapViewConigs[config.Type] = config
  end
  self:_SortDiscoveriesData(unorderedDiscoveriesMap)
  self:_InitModelData()
end

function ItemDataModel:GetAllItemTypes()
  return self.m_arrItemType
end

function ItemDataModel:_InitModelData()
  self.m_chainIdMaxLevelMap = {}
  self.m_chainIdProtectLevelMap = {}
  self.m_chainIdGeneratorMap = {}
  self.m_itemGeneratorMap = {}
  local customChainIdGeneratorMap = {}
  for type, item in pairs(self.m_mapModelConfigs) do
    local mergedType = item.MergedType
    if mergedType then
      local mergedItem = self.m_mapModelConfigs[mergedType]
      if mergedItem then
        if mergedItem.MergedFromType then
          Log.Error(tostring(mergedItem.Type) .. " has more than one MergedFromType.")
        end
        mergedItem.MergedFromType = type
      else
      end
    end
    local chainId = self:GetChainId(item.Type)
    local level = self:GetChainLevel(item.Type)
    local maxLevel = self.m_chainIdMaxLevelMap[chainId]
    if maxLevel == nil then
      self.m_chainIdMaxLevelMap[chainId] = level
    else
      self.m_chainIdMaxLevelMap[chainId] = math.max(level, maxLevel)
    end
    if level == 1 then
      self.m_chainIdProtectLevelMap[chainId] = item.ProtectLevel
    end
    if item.Materials ~= nil then
      local materialsArray = {}
      for _, v in ipairs(item.Materials) do
        table.insert(materialsArray, v.Material)
      end
      item.Materials = materialsArray
      if item.Instrument[1] and not StringUtil.StartWith(item.Instrument[1].Instru, "eq_") then
        local materialsWithDisposableInstru = Table.ShallowCopy(materialsArray)
        materialsWithDisposableInstru[#materialsWithDisposableInstru + 1] = item.Instrument[1].Instru
        item.MaterialsWithDisposableInstru = materialsWithDisposableInstru
      end
    end
    if item.Generators ~= nil then
      self.m_itemGeneratorMap[item.Type] = item.Generators
    end
    if item.OrderGenerators ~= nil then
      customChainIdGeneratorMap[chainId] = item.OrderGenerators
    end
    if item.TapeItems ~= nil or item.GeneratedItems ~= nil then
      Log.Assert(item.GeneratedItems ~= nil, item.Type .. " 未配置 GeneratedItems，影响棋子详情页显示！")
      local spreadWeight = item.TapeItems or item.GeneratedItems
      if item.Category and not Table.ListContain(item.Category, EItemCategory.RandomBox) then
        for _, pair in ipairs(spreadWeight) do
          if ItemModelFactory.GetCodePrefixTargetType(pair.Code) == nil then
            local generatedChainId = self:GetChainId(pair.Code)
            local generators = self.m_chainIdGeneratorMap[generatedChainId]
            if generators == nil then
              generators = {}
              self.m_chainIdGeneratorMap[generatedChainId] = generators
            end
            if not Table.ListContain(generators, item.Type) then
              table.insert(generators, item.Type)
            end
          end
        end
      end
    end
    if self:IsTransformItem(type) then
      local transformTo = item.GeneratedItems[1].Code
      local transformToConfig = self.m_mapModelConfigs[transformTo]
      if transformToConfig then
        transformToConfig.TransformFrom = type
      else
        Log.Error(tostring(type) .. "'s Transform Type:" .. tostring(transformTo) .. " config does not exist.")
      end
      if item.Transform then
        transformTo = item.Transform[1].Currency
        local transformToConfig = self.m_mapModelConfigs[transformTo]
        if transformToConfig then
          transformToConfig.TransformFrom = type
        else
          Log.Error(tostring(type) .. "'s Transform Type:" .. tostring(transformTo) .. " config does not exist.")
        end
      end
    end
  end
  for chainId, generators in pairs(customChainIdGeneratorMap) do
    self.m_chainIdGeneratorMap[chainId] = generators
  end
  self:_InitDiscoveriesRewardCount()
  if self:GetModelConfig("it_1_1_1").Score == nil then
    Log.Error("未配置棋子 Score！")
  end
end

function ItemDataModel:GetModelConfig(type, canBeNil)
  local config = self.m_mapModelConfigs[type]
  if not canBeNil and config == nil then
    Log.Error("No item model config for type: " .. tostring(type))
  end
  return config
end

function ItemDataModel:IsItemExist(type)
  return self.m_mapModelConfigs[type] ~= nil
end

function ItemDataModel:GetViewConfig(type)
  return self.m_mapViewConigs[type]
end

function ItemDataModel:GetPropertyConfig(type)
  return self.m_mapPropertyConfigs[type]
end

function ItemDataModel:GetSpriteName(type)
  local imgName = type
  local config = self:GetViewConfig(type)
  if config ~= nil and config.Image ~= nil then
    imgName = Table.ListRandomSelectOne(config.Image)
  end
  if ExtraBoardActivityModel.IsExtraBoardCommonItem(type) then
    imgName = ExtraBoardActivityModel.GetRealExtraBoardItem(type)
  end
  return imgName
end

function ItemDataModel:GetChainLevel(type)
  return self:GetModelConfig(type).ChainLevel
end

function ItemDataModel:GetChainId(type)
  return self:GetModelConfig(type).ChainId
end

function ItemDataModel:GetTransformFrom(type)
  return self.m_mapModelConfigs[type].TransformFrom
end

function ItemDataModel:GetItemScore(type)
  local itemConfig = self:GetModelConfig(type, true)
  if not itemConfig then
    return 0
  end
  return itemConfig.Score or 0
end

function ItemDataModel:GetItemGoldNum(type)
  local itemConfig = self:GetModelConfig(type, true)
  if not itemConfig then
    return 0
  end
  return itemConfig.Reward or 0
end

function ItemDataModel:GetChainMaxLevel(chainId)
  return self.m_chainIdMaxLevelMap[chainId]
end

function ItemDataModel:GetChainProtectLevel(chainId)
  return self.m_chainIdProtectLevelMap[chainId]
end

function ItemDataModel:GetChainGenerators(chainId)
  return self.m_chainIdGeneratorMap[chainId]
end

function ItemDataModel:GetItemGenerators(itemType)
  return self.m_itemGeneratorMap[itemType]
end

function ItemDataModel:GetChain(chainId)
  local chain = {}
  local maxLevel = self:GetChainMaxLevel(chainId)
  for level = 1, maxLevel do
    table.insert(chain, chainId .. "_" .. level)
  end
  return chain
end

function ItemDataModel:_SortDiscoveriesData(unorderedDiscoveriesMap)
  local firstChain, chainA, chainB, levelA, levelB
  local sortByOrder = function(a, b)
    if a.order ~= b.order then
      return a.order < b.order
    end
    if a.type and b.type then
      chainA = self:GetChainId(a.type)
      levelA = self:GetChainLevel(a.type)
      chainB = self:GetChainId(b.type)
      levelB = self:GetChainLevel(b.type)
      if chainA ~= chainB then
        if chainA == firstChain then
          return true
        elseif chainB == firstChain then
          return false
        else
          return chainA < chainB
        end
      elseif chainA == chainB and levelA ~= levelB then
        return levelA < levelB
      end
    end
  end
  for _, list in pairs(self.m_discoveriesMap) do
    table.sort(list, sortByOrder)
  end
  for bookOrderId, list in pairs(unorderedDiscoveriesMap) do
    firstChain = self:GetChainId(list[1].type)
    table.sort(list, sortByOrder)
    self.m_discoveriesItemsMap[bookOrderId] = {}
    for index, value in ipairs(list) do
      self.m_discoveriesItemsMap[bookOrderId][index] = value.type
    end
  end
end

function ItemDataModel:_InitDiscoveriesRewardCount()
  local count = 0
  for bookOrderId, list in pairs(self.m_discoveriesItemsMap) do
    for _, itemCode in ipairs(list) do
      if self:GetUnlockState(itemCode) == EItemUnlockState.Unlocked then
        count = count + 1
      end
    end
  end
  self.m_discoveriesRewardCount = count
end

function ItemDataModel:GetDiscoveriesMap(tab)
  return self.m_discoveriesMap[tab]
end

function ItemDataModel:GetDiscoveriesItemsByOrder(bookOrderId)
  return self.m_discoveriesItemsMap[bookOrderId]
end

function ItemDataModel:GetCanRewardDiscoveriesOrder(Tab)
  for tab, list in pairs(self.m_discoveriesMap) do
    if not Tab or tab == Tab then
      for _, data in ipairs(list) do
        local rewardItemType = self:HasUnlockRewardInDiscoveriesOrder(data.order)
        if rewardItemType then
          return data.order, rewardItemType
        end
      end
    end
  end
end

function ItemDataModel:GetDiscoveriesCanRewardCount()
  return self.m_discoveriesRewardCount
end

function ItemDataModel:HasUnlockRewardInDiscoveriesOrder(bookOrderId)
  local items = self:GetDiscoveriesItemsByOrder(bookOrderId)
  for index, itemCode in ipairs(items or {}) do
    local rewardItemType = self:GetUnlockState(itemCode) == EItemUnlockState.Unlocked
    if rewardItemType then
      return itemCode, index
    end
  end
  return nil
end

function ItemDataModel:GetItemIndexInDiscoveriesOrder(bookOrderId, itemType)
  local items = self:GetDiscoveriesItemsByOrder(bookOrderId)
  for index, type in ipairs(items or {}) do
    if type == itemType then
      return index
    end
  end
  return nil
end

function ItemDataModel:IsUnlocked(type)
  return self:GetUnlockState(type) > EItemUnlockState.Locked
end

function ItemDataModel:IsKnown(type)
  return self:GetUnlockState(type) > EItemUnlockState.Unknown
end

function ItemDataModel:SetUnlocked(type)
  if self:IsUnlocked(type) then
    return
  end
  if self:GetModelConfig(type).BookOrder ~= nil then
    self.m_discoveriesRewardCount = self.m_discoveriesRewardCount + 1
  end
  self.m_unlockDB:Set(type, DBStateCloumn, EItemUnlockState.Unlocked)
  GM.BIManager:LogAction(EBIType.ItemUnlock, type)
  EventDispatcher.DispatchEvent(EEventType.ItemUnlocked, type)
  EventDispatcher.DispatchEvent(EEventType.DiscoveriesUpdate)
  return true
end

function ItemDataModel:SetLocked(type)
  if self:IsUnlocked(type) then
    return
  end
  self.m_unlockDB:Set(type, DBStateCloumn, EItemUnlockState.Locked)
end

function ItemDataModel:GetUnlockState(type)
  return self.m_unlockDB:GetValue(type, DBStateCloumn) or EItemUnlockState.Unknown
end

function ItemDataModel:ClaimUnlockedRewards(type)
  self.m_discoveriesRewardCount = self.m_discoveriesRewardCount - 1
  self.m_unlockDB:Set(type, DBStateCloumn, EItemUnlockState.Rewarded)
  local config = self:GetModelConfig(type)
  local arrRewards = config.BookReward
  RewardApi.CryptRewards(arrRewards)
  RewardApi.AcquireRewardsLogic(arrRewards, EPropertySource.Give, EBIType.ItemUnlock, EGameMode.Board, CacheItemType.Stack)
  GM.BIManager:LogAction(EBIType.ItemUnlockClaimReward, type)
  return arrRewards
end

function ItemDataModel:GetData()
  return self.m_unlockDB
end

function ItemDataModel:FromSyncData(dataArr)
  self.m_unlockDB:FromArr(dataArr)
  self:_InitDiscoveriesRewardCount()
end

function ItemDataModel:GetDetailDescParameter(itemType)
  local config = self:GetModelConfig(itemType)
  if config and config.BoosterType and config.Effect then
    return config.Effect
  end
end

function ItemDataModel:IsDishes(itemType)
  local config = self:GetModelConfig(itemType, true)
  if config and config.Category and Table.ListContain(config.Category, EItemCategory.Dish) then
    return true, config.InRandom == 1
  end
  return false
end

function ItemDataModel:IsInstrument(itemType)
  local config = self:GetModelConfig(itemType, true)
  if config and config.Category and Table.ListContain(config.Category, EItemCategory.Instrument) then
    return true
  end
  return false
end

function ItemDataModel:IsBox(itemType)
  local config = self:GetModelConfig(itemType, true)
  if config and config.Category and Table.ListContain(config.Category, EItemCategory.Box) then
    return true
  end
  return false
end

function ItemDataModel:IsTransformItem(itemType)
  local config = self:GetModelConfig(itemType, true)
  if config and config.DropOnSpot == 1 and config.DropsTotal == 1 then
    return true
  end
  return false
end

function ItemDataModel:IsDisposableInstrument(itemType)
  if not self:IsInstrument(itemType) then
    return false
  end
  return not StringUtil.StartWith(itemType, "eq_"), true
end

function ItemDataModel:GetDishUnlockedInstru(dish)
  local config = self:GetModelConfig(dish)
  local instrument = config.Instrument
  local unlockedInstrument, dur, instru
  for i = #instrument, 1, -1 do
    instru = instrument[i]
    if self:IsUnlocked(instru.Instru) then
      unlockedInstrument = instru.Instru
      dur = instru.Dur
      break
    end
  end
  if unlockedInstrument == nil then
    unlockedInstrument = instrument[1].Instru
    dur = instrument[1].Dur
  end
  return unlockedInstrument, dur
end

function ItemDataModel:GetMaterials(itemCode, canBeNil, includeDisposableInstru)
  local config = self:GetModelConfig(itemCode, canBeNil)
  if not config or not config.Materials then
    return
  end
  return includeDisposableInstru and config.MaterialsWithDisposableInstru or config.Materials
end

function ItemDataModel:GetAllMaterials(itemCode, canBeNil)
  if self.m_mapNonDishMaterials[itemCode] then
    return self.m_mapNonDishMaterials[itemCode], self.m_mapDishMaterials[itemCode]
  end
  local arrMaterials = self:GetMaterials(itemCode, canBeNil, true)
  if not arrMaterials then
    return
  end
  local arrNonDishMaterials = {}
  local arrDishMaterials = {}
  for _, mat in ipairs(arrMaterials) do
    local nonDishResult, dishResult = self:GetAllMaterials(mat)
    if nonDishResult ~= nil then
      arrDishMaterials[#arrDishMaterials + 1] = mat
      Table.ListAppend(arrNonDishMaterials, nonDishResult)
      Table.ListAppend(arrDishMaterials, dishResult)
    else
      arrNonDishMaterials[#arrNonDishMaterials + 1] = mat
    end
  end
  self.m_mapNonDishMaterials[itemCode] = arrNonDishMaterials
  self.m_mapDishMaterials[itemCode] = arrDishMaterials
  return arrNonDishMaterials, arrDishMaterials
end

function ItemDataModel:IsMainProduct(chainId)
  local splited = StringUtil.Split(chainId, "_")
  return #splited == 3 and splited[1] == "it" and splited[3] == "1"
end

function ItemDataModel:IsPd(type)
  return StringUtil.StartWith(type, "pd_")
end

function ItemDataModel:GetItemSeries(type)
  if self:IsDishes(type) then
    local arrNonDishMaterials, arrDishMaterials = self:GetAllMaterials(type)
    local nSeries
    for _, materialType in ipairs(arrNonDishMaterials) do
      nSeries = self:GetItemSeries(materialType)
      if nSeries then
        return nSeries
      end
    end
  else
    if not self:IsItemExist(type) then
      return nil
    end
    local chainId = self:GetChainId(type) or ""
    local index1 = string.find(chainId, "_a")
    if index1 then
      local index2 = string.find(chainId, "_", index1 + 2)
      index2 = index2 and index2 - 1 or nil
      local nSeries = tonumber(string.sub(chainId, index1 + 2, index2))
      return nSeries
    end
  end
  return nil
end

function ItemDataModel:GetCookAnimationName(recipe, suffix)
  local suffix = StringUtil.IsNilOrEmpty(suffix) and "" or "_" .. suffix
  local originName = "cook"
  local configName = self:GetModelConfig(recipe).SpecialAnim
  local animName = configName or originName
  animName = animName .. suffix
  return originName .. suffix, animName
end

function ItemDataModel:SetRewardsLocked(arrRewards)
  local type
  local propertyDataManager = GM.PropertyDataManager
  for _, reward in ipairs(arrRewards) do
    type = reward[PROPERTY_TYPE]
    if not propertyDataManager:IsPropertyType(type) then
      self:SetLocked(type)
    end
  end
end

function ItemDataModel:GetItemSplits(itemType)
  if self.m_mapItemSplits[itemType] and self.m_mapRelaventItems[itemType] then
    return self.m_mapItemSplits[itemType], self.m_mapRelaventItems[itemType]
  end
  if not self.m_mapModelConfigs[itemType] then
    return {}, {}
  end
  local mapSplits = {}
  local mapRelativeItems = {}
  self:_GetItemSplits(mapSplits, mapRelativeItems, itemType, 1)
  self.m_mapItemSplits[itemType] = mapSplits
  self.m_mapRelaventItems[itemType] = mapRelativeItems
  return mapSplits, mapRelativeItems
end

function ItemDataModel:_GetItemSplits(mapSplits, mapRelativeItems, itemType, count)
  mapRelativeItems[itemType] = true
  local arrGenerators = self.m_itemGeneratorMap[itemType]
  if self.m_mapModelConfigs[itemType].MergedFromType then
    self:_GetItemSplits(mapSplits, mapRelativeItems, self.m_mapModelConfigs[itemType].MergedFromType, 2 * count)
  elseif self.m_mapModelConfigs[itemType].TransformFrom then
    self:_GetItemSplits(mapSplits, mapRelativeItems, self.m_mapModelConfigs[itemType].TransformFrom, count)
  elseif self:IsDishes(itemType) then
    for _, materialType in ipairs(self:GetMaterials(itemType, false, true)) do
      self:_GetItemSplits(mapSplits, mapRelativeItems, materialType, count)
    end
  else
    mapSplits[itemType] = (mapSplits[itemType] or 0) + count
  end
end

function ItemDataModel:GetItemCurProduceEnergy(itemCode)
  local chain = self:GetChainId(itemCode)
  local level = self:GetChainLevel(itemCode)
  local toLevel1Count = ItemUtility.GetToLevel1Count(level)
  local generators = self:GetItemGenerators(itemCode)
  if generators == nil or #generators <= 0 then
    Log.Error("计算喷发体力消耗：" .. itemCode .. " 没有 Generators，不是喷发生成，没有体力消耗")
    return 0
  end
  local generatorChain = self:GetChainId(generators[1])
  if not self:IsPd(generatorChain) then
    Log.Error("计算喷发体力消耗：" .. itemCode .. " 的母棋子不是 pd，调用层需要向上追溯到最基本合成线棋子")
    return 0
  end
  local pdItem = self:GetMaxUnlockedItem(generators)
  if self.m_itemProduceEnergy[pdItem] ~= nil and self.m_itemProduceEnergy[pdItem][chain] ~= nil then
    Log.Info("计算喷发体力消耗：" .. itemCode .. " 的母棋子是 " .. pdItem .. "，喷发一级的消耗是 " .. self.m_itemProduceEnergy[pdItem][chain])
    return self.m_itemProduceEnergy[pdItem][chain] * toLevel1Count
  end
  local generatedItems = self:GetModelConfig(pdItem).GeneratedItems
  if generatedItems == nil then
    Log.Error("计算喷发体力消耗：" .. itemCode .. " Generators 的 GeneratedItems 为空")
    return 0
  end
  local totalGenItemCount = 0
  local genChainMap = {}
  local genChainCount = 0
  local genCode, genWeight, genChain, genLevel
  for _, config in pairs(generatedItems) do
    genCode = config.Code
    genWeight = config.Weight
    totalGenItemCount = totalGenItemCount + config.Weight
    genChain = self:GetChainId(genCode)
    genLevel = self:GetChainLevel(genCode)
    if genChainMap[genChain] == nil then
      genChainCount = genChainCount + 1
      genChainMap[genChain] = 0
    end
    genChainMap[genChain] = genChainMap[genChain] + ItemUtility.GetToLevel1Count(genLevel) * genWeight
  end
  if self.m_itemProduceEnergy[pdItem] == nil then
    self.m_itemProduceEnergy[pdItem] = {}
  end
  local splitEnergy = 1 / genChainCount
  local rate
  for genChain, genCount in pairs(genChainMap) do
    rate = genCount / totalGenItemCount
    self.m_itemProduceEnergy[pdItem][genChain] = splitEnergy / rate
  end
  if self.m_itemProduceEnergy[pdItem][chain] ~= nil then
    Log.Info("计算喷发体力消耗：" .. itemCode .. " 的母棋子是 " .. pdItem .. "，喷发一级的消耗是 " .. self.m_itemProduceEnergy[pdItem][chain])
    return self.m_itemProduceEnergy[pdItem][chain] * toLevel1Count
  end
  Log.Error("计算喷发体力消耗：" .. itemCode .. " unexpected")
  return 0
end

function ItemDataModel:GetMaxUnlockedItem(itemList)
  local level, maxLevel, minLevel, maxLevelItem, minLevelItem
  for _, item in ipairs(itemList) do
    level = self:GetChainLevel(item)
    if self:IsUnlocked(item) then
      if maxLevel == nil or maxLevel < level then
        maxLevelItem = item
        maxLevel = level
      end
    elseif minLevel == nil or minLevel > level then
      minLevelItem = item
      minLevel = level
    end
  end
  return maxLevelItem or minLevelItem
end
