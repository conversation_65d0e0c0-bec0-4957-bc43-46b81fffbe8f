TaskManager = {}
TaskManager.__index = TaskManager
local EMetaDBKey = {
  OngoingTaskIds = "OngoingTaskIds",
  OngoingChapterName = "OngoingChapterName",
  OngoingChapterId = "OngoingChapterId",
  TaskProgress = "TaskProgress",
  TaskCleanGold = "TaskCleanGold",
  CleanGoldCost = "CleanGoldCost",
  ChapterFinishWin = "ChapterFinishWin"
}
TASK_ID_SEPARATOR = ";"
local CLEAN_COST_SEPARATOR = "#"
TASK_CHAPTER_ID = "ChapterId"
TASK_COUNT = "TaskCount"

function TaskManager:Init()
  self.m_arrOngoingTaskIds = {}
  self.m_mapChapterFinishedTaskIds = {}
  self.m_metaDBTable = GM.DBTableManager:GetTable(EDBTableConfigs.TaskMetaData)
  self.m_finishedTasksDBTable = GM.DBTableManager:GetTable(EDBTableConfigs.FinishedTasks)
  self:_LoadOngoingData()
end

function TaskManager:GetMetaData()
  return self.m_metaDBTable
end

function TaskManager:GetFinishedTaskData()
  return self.m_finishedTasksDBTable
end

function TaskManager:OnLoadFileConfigFinished()
  self:_LoadData()
end

function TaskManager:_LoadOngoingData()
  self.m_nOngoingChapterId = self.m_metaDBTable:GetValueInNumber(EMetaDBKey.OngoingChapterId, DB_VALUE_KEY)
  self.m_nOngoingChapterId = math.max(1, self.m_nOngoingChapterId)
  local strOngoingData = self.m_metaDBTable:GetValue(EMetaDBKey.OngoingTaskIds, DB_VALUE_KEY)
  self.m_arrOngoingTaskIds = StringUtil.SplitToNum(strOngoingData, TASK_ID_SEPARATOR, 0)
  self.m_nCleanGoldCount = self.m_metaDBTable:GetValueInNumber(EMetaDBKey.TaskCleanGold, DB_VALUE_KEY)
  local strCleanGoldCost = self.m_metaDBTable:GetValue(EMetaDBKey.CleanGoldCost, DB_VALUE_KEY)
  local arrCleanGoldCost = StringUtil.Split(strCleanGoldCost, TASK_ID_SEPARATOR)
  local mapCleanGoldCost = {}
  local arr
  for _, strCost in ipairs(arrCleanGoldCost) do
    arr = StringUtil.SplitToNum(strCost, CLEAN_COST_SEPARATOR)
    if not arr[2] or mapCleanGoldCost[arr[1]] then
      Log.Error("load task clean cost error!")
    end
    mapCleanGoldCost[arr[1]] = arr[2]
  end
  self.m_mapCleanGoldCost = mapCleanGoldCost
end

function TaskManager:_LoadData()
  self:_LoadOngoingData()
  GM.ChapterDataModel:ConsiderTaskChapter(self.m_nOngoingChapterId)
  local chapterCount = GM.ChapterDataModel:GetChapterCount()
  if 0 < chapterCount and chapterCount < self.m_nOngoingChapterId then
    self.m_nOngoingChapterId = chapterCount
    self.m_arrOngoingTaskIds = {}
  end
  self.m_strOngoingChapterName = GM.ChapterDataModel:GetChapterNameById(self.m_nOngoingChapterId)
  self.m_mapChapterFinishedTaskIds = self.m_finishedTasksDBTable:GetValues()
  self.m_ongoingChapterTotalCount = GM.TaskDataModel:GetChapterTaskCount(self.m_strOngoingChapterName)
  local mapOngoingChapterFinishedTaskIds = self.m_mapChapterFinishedTaskIds[self.m_nOngoingChapterId] or {}
  self.m_ongoingChapterFinishCount = #Table.GetKeys(mapOngoingChapterFinishedTaskIds)
  GM.ChapterManager.curActiveChapterName = self.m_strOngoingChapterName
  self:_CalculateTaskProgress()
  local noProgressRecord = self:GetTaskProgress() == 0
  self.m_dataLoaded = true
  if noProgressRecord then
    self:_SaveTaskProgress()
  end
end

function TaskManager:_TryFixFinishRecord()
  local mapRecordFinishedTaskIds = self.m_mapChapterFinishedTaskIds[self.m_nOngoingChapterId] or {}
  if next(mapRecordFinishedTaskIds) == nil then
    return
  end
  local mapPreTaskIds = GM.TaskDataModel:GetTaskPreIdsByChapter(self.m_strOngoingChapterName)
  local fixedTaskIds = {}
  local mapNewFinishedTaskIds = {}
  local funcGetAllPreTaskIds
  
  function funcGetAllPreTaskIds(taskId)
    for _, preTaskId in ipairs(mapPreTaskIds[taskId] or {}) do
      if not mapNewFinishedTaskIds[preTaskId] then
        funcGetAllPreTaskIds(preTaskId)
      end
    end
    mapNewFinishedTaskIds[taskId] = true
    if not mapRecordFinishedTaskIds[taskId] then
      fixedTaskIds[taskId] = true
    end
  end
  
  for taskId, _ in pairs(mapRecordFinishedTaskIds) do
    funcGetAllPreTaskIds(taskId)
  end
  if next(fixedTaskIds) == nil then
    return
  end
  for fixedTaskId, _ in pairs(fixedTaskIds) do
    local cost = self:GetTaskCost(fixedTaskId, self.m_strOngoingChapterName)
    mapRecordFinishedTaskIds[fixedTaskId] = cost
    self.m_ongoingChapterFinishCount = self.m_ongoingChapterFinishCount + 1
    self.m_finishedTasksDBTable:Set(self.m_nOngoingChapterId, fixedTaskId, DB_VALUE_KEY, cost)
    Table.ListRemove(self.m_arrOngoingTaskIds, fixedTaskId)
    Log.Error("任务进度丢失，已找回：" .. fixedTaskId)
  end
  self:TryStartNewTasks()
  self:_SaveOngoingData()
end

function TaskManager:FromSyncData(metaDataArr, dataArr)
  self.m_metaDBTable:FromArr(metaDataArr)
  self.m_finishedTasksDBTable:FromArr(dataArr)
  self.m_bFromSyncData = true
end

function TaskManager:OnSyncDataFinished()
  if not self.m_bFromSyncData then
    return
  end
  self:_LoadData()
  self.m_bFromSyncData = nil
end

function TaskManager:LateInit()
  self:TryStartNewTasks()
  self:_TryFixFinishRecord()
  self:TryBalanceTaskCost()
end

function TaskManager:GetOngoingChapterName()
  return self.m_strOngoingChapterName
end

function TaskManager:GetOngoingChapterId()
  return self.m_nOngoingChapterId
end

function TaskManager:GetOngoingTasks()
  return self.m_arrOngoingTaskIds
end

function TaskManager:GetOngoingTask()
  return self.m_arrOngoingTaskIds[1]
end

function TaskManager:GetOngoingTasksString()
  local str = ""
  local first = true
  for _, taskId in ipairs(self.m_arrOngoingTaskIds) do
    if first then
      first = false
    else
      str = str .. TASK_ID_SEPARATOR
    end
    str = str .. taskId
  end
  return str
end

function TaskManager:IsTaskFinished(chapterId, taskId)
  if self:IsChapterFinished(chapterId) then
    return true
  end
  local mapChapterFinishedTaskIds = self.m_mapChapterFinishedTaskIds[chapterId]
  return mapChapterFinishedTaskIds and mapChapterFinishedTaskIds[taskId]
end

function TaskManager:IsOngoingTasksAllFinished()
  return self.m_arrOngoingTaskIds[1] == nil
end

function TaskManager:IsOngoingChapterProgressFinished()
  return self.m_ongoingChapterFinishCount == self.m_ongoingChapterTotalCount
end

function TaskManager:IsAllFinished()
  local chapterCount = GM.ChapterDataModel:GetChapterCount()
  if chapterCount < self.m_nOngoingChapterId then
    return true
  elseif chapterCount > self.m_nOngoingChapterId then
    return false
  end
  return self:IsOngoingTasksAllFinished() and self:IsOngoingChapterProgressFinished()
end

function TaskManager:IsChapterFinished(chapterId)
  if chapterId == self.m_nOngoingChapterId then
    return self:IsOngoingTasksAllFinished() and self:IsOngoingChapterProgressFinished()
  elseif chapterId == 0 then
    return true
  else
    return self.m_mapChapterFinishedTaskIds[chapterId] ~= nil
  end
end

function TaskManager:GetChapterFinishedCount(chapterId)
  local mapChapterFinishedTaskIds = self.m_mapChapterFinishedTaskIds[chapterId]
  return mapChapterFinishedTaskIds and #Table.GetKeys(mapChapterFinishedTaskIds) or 0
end

function TaskManager:IsChapterLocked(chapterId)
  local valid = chapterId and chapterId <= GM.ChapterDataModel:GetChapterCount()
  local isChapterFinished = self:IsChapterFinished(chapterId)
  return not valid or chapterId ~= self.m_nOngoingChapterId and not isChapterFinished
end

function TaskManager:CanFinishOngoingTask()
  local myGold = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gold)
  local satisfiedCount = 0
  local firstSatisfiedTaskId
  for _, taskId in ipairs(self.m_arrOngoingTaskIds) do
    if myGold >= self:GetTaskCost(taskId) then
      satisfiedCount = satisfiedCount + 1
      firstSatisfiedTaskId = firstSatisfiedTaskId or taskId
    end
  end
  if 0 < satisfiedCount then
    return true, firstSatisfiedTaskId, satisfiedCount
  end
  return false
end

function TaskManager:GetTaskCost(taskId, chapterName)
  local cost = GM.TaskDataModel:GetTaskData(chapterName or self.m_strOngoingChapterName, taskId).Cost or 0
  local cleanCost = self:GetTaskCleanGoldCost(taskId)
  return cost + cleanCost
end

function TaskManager:GetTaskRewards(taskId, excludeExp)
  local rewards = GM.TaskDataModel:GetTaskData(self.m_strOngoingChapterName, taskId).Rewards or {}
  if excludeExp then
    local newRewards = {}
    for _, value in ipairs(rewards) do
      if value[PROPERTY_TYPE] ~= EPropertyType.Experience then
        newRewards[#newRewards + 1] = value
      end
    end
    return newRewards
  end
  return rewards
end

function TaskManager:GetExpRewardCount(taskId)
  local rewards = GM.TaskDataModel:GetTaskData(self.m_strOngoingChapterName, taskId).Rewards or {}
  for _, value in ipairs(rewards) do
    if value[PROPERTY_TYPE] == EPropertyType.Experience then
      return value[PROPERTY_COUNT]
    end
  end
  return 0
end

function TaskManager:GetOngoingChapterProgressCount()
  return self.m_ongoingChapterFinishCount, self.m_ongoingChapterTotalCount
end

function TaskManager:GetTaskProgress()
  if not self.m_dataLoaded then
    return self.m_metaDBTable:GetValueInNumber(EMetaDBKey.TaskProgress, DB_VALUE_KEY)
  end
  return self.m_nOngoingChapterId * 1000 + self:GetChapterFinishedCount(self.m_nOngoingChapterId)
end

function TaskManager:GoFinishTask(taskId)
  local cost = self:GetTaskCost(taskId)
  local myGold = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gold)
  if cost > myGold then
    Log.Error("不应该出现这种情况。")
    return false
  end
  local ongoingChapterName = GM.TaskManager:GetOngoingChapterName()
  GM.TimelineManager:PlayTimeline(taskId, ongoingChapterName, function()
    self:OnTaskFinished(taskId)
  end)
  return true
end

function TaskManager:OnTaskFinished(taskId)
  local cost = self:GetTaskCost(taskId)
  GM.PropertyDataManager:Consume(EPropertyType.Gold, cost, EBIType.TaskFinish, true, taskId)
  local cleanCost = self:GetTaskCleanGoldCost(taskId)
  self:ConsumeTaskCleanGold(cleanCost)
  local rewards = self:GetTaskRewards(taskId)
  if rewards[1] then
    local userLevel = GM.LevelModel:GetCurrentLevel()
    RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.TaskFinish, EGameMode.Board, CacheItemType.Stack)
    RewardApi.AcquireRewardsInView(self:GetTaskRewards(taskId, true))
    if userLevel < GM.LevelModel:GetCurrentLevel() then
      GM.UIManager:SetEventLockUntilNextPopup()
    end
  end
  self:SaveSlotsChanges(taskId)
  if not self.m_mapChapterFinishedTaskIds[self.m_nOngoingChapterId] then
    self.m_mapChapterFinishedTaskIds[self.m_nOngoingChapterId] = {}
  end
  local mapOngoingChapterFinishedTaskIds = self.m_mapChapterFinishedTaskIds[self.m_nOngoingChapterId]
  Log.Assert(mapOngoingChapterFinishedTaskIds[taskId] == nil, "Task can only be finished once:" .. tostring(self.m_strOngoingChapterName) .. ", " .. tostring(taskId))
  mapOngoingChapterFinishedTaskIds[taskId] = cost
  self.m_ongoingChapterFinishCount = self.m_ongoingChapterFinishCount + 1
  self.m_finishedTasksDBTable:Set(self.m_nOngoingChapterId, taskId, DB_VALUE_KEY, cost)
  self.m_progressTaskFinishCount = self.m_progressTaskFinishCount + 1
  Table.ListRemove(self.m_arrOngoingTaskIds, taskId)
  self:TryStartNewTasks()
  self:_SaveOngoingData()
  EventDispatcher.DispatchEvent(EEventType.MainTaskFinished, {taskId = taskId})
  local biCost = {
    [EPropertyType.Gold] = cost
  }
  GM.BIManager:LogTask(self:GetOngoingChapterName(), GM.ChapterDataModel:GetChapterIdByName(self:GetOngoingChapterName()), taskId, biCost, rewards, EBITaskType.Mainline, "")
end

function TaskManager:SaveSlotsChanges(taskId)
  local slotChanges = self:GetTaskSlotsChanges(taskId)
  for _, changeData in ipairs(slotChanges) do
    local slotId = changeData.Slot
    local state = changeData.State
    GM.ChapterManager.roomModel:SetState(slotId, state)
  end
end

function TaskManager:GetTaskSlotsChanges(taskId, chapterName)
  return GM.TaskDataModel:GetTaskData(chapterName or self.m_strOngoingChapterName, taskId).SlotState or {}
end

function TaskManager:_CalculateTaskProgress()
  local dataModel = GM.TaskDataModel
  local chapterDataModel = GM.ChapterDataModel
  local accTaskCount = self.m_ongoingChapterFinishCount
  local chapterName
  for chapterId = 1, self.m_nOngoingChapterId - 1 do
    chapterName = chapterDataModel:GetChapterNameById(chapterId)
    accTaskCount = accTaskCount + dataModel:GetChapterTaskCount(chapterName)
  end
  local groupTaskNum
  local groupId = 1
  local groupCount = dataModel:GetTaskGroupCount()
  while groupId <= groupCount do
    groupTaskNum = dataModel:GetTaskGroupNeedNum(groupId)
    if accTaskCount >= groupTaskNum then
      groupId = groupId + 1
      accTaskCount = accTaskCount - groupTaskNum
    else
      break
    end
  end
  self.m_ongoingProgressId = groupId
  self.m_progressTaskFinishCount = accTaskCount
end

function TaskManager:_SaveTaskProgress()
  self.m_metaDBTable:Set(EMetaDBKey.TaskProgress, DB_VALUE_KEY, self:GetTaskProgress())
end

function TaskManager:IsVersionRevert()
  local nDBOngoingChapterId = self.m_metaDBTable:GetValueInNumber(EMetaDBKey.OngoingChapterId, DB_VALUE_KEY)
  if nDBOngoingChapterId and nDBOngoingChapterId > GM.ChapterDataModel:GetChapterCount() then
    return true
  end
  return false
end

function TaskManager:_SaveOngoingData()
  if self:IsVersionRevert() then
    return
  end
  local strOngoingIds = self:_BuildArrayDataString(self.m_arrOngoingTaskIds)
  local ongoingCount = #self.m_arrOngoingTaskIds
  Log.Assert(ongoingCount <= 1, "当前同时进行的任务数量为：" .. ongoingCount)
  self.m_metaDBTable:Set(EMetaDBKey.OngoingTaskIds, DB_VALUE_KEY, strOngoingIds)
  self.m_metaDBTable:Set(EMetaDBKey.TaskCleanGold, DB_VALUE_KEY, self.m_nCleanGoldCount)
  local arrCleanGoldCost = {}
  for taskId, cleanCost in pairs(self.m_mapCleanGoldCost) do
    arrCleanGoldCost[#arrCleanGoldCost + 1] = taskId .. "#" .. cleanCost
  end
  local strCleanGoldCost = self:_BuildArrayDataString(arrCleanGoldCost)
  self.m_metaDBTable:Set(EMetaDBKey.CleanGoldCost, DB_VALUE_KEY, strCleanGoldCost)
  self.m_metaDBTable:Set(EMetaDBKey.OngoingChapterId, DB_VALUE_KEY, self.m_nOngoingChapterId)
  self.m_metaDBTable:Set(EMetaDBKey.OngoingChapterName, DB_VALUE_KEY, self.m_strOngoingChapterName)
  self:_SaveTaskProgress()
end

function TaskManager:_BuildArrayDataString(arrArray)
  local str = ""
  local count = #arrArray
  for i = 1, count do
    str = str .. tostring(arrArray[i])
    if i < count then
      str = str .. TASK_ID_SEPARATOR
    end
  end
  return str
end

function TaskManager:GetCanBeTriggeredNewTasks(chapterName)
  local arrTaskStarters = GM.TaskDataModel:GetTaskStartersByChapter(chapterName)
  local mapOngoingTaskIds = {}
  for _, ongoingTaskId in ipairs(self.m_arrOngoingTaskIds) do
    mapOngoingTaskIds[ongoingTaskId] = true
  end
  local arrTasksTriggered = {}
  local taskId
  local chapterId = GM.ChapterDataModel:GetChapterIdByName(chapterName)
  local mapFinishedTaskIds = self.m_mapChapterFinishedTaskIds[chapterId] or {}
  for _, starter in pairs(arrTaskStarters) do
    taskId = starter:GetTaskId()
    if not mapFinishedTaskIds[taskId] and not mapOngoingTaskIds[taskId] and starter:IsAllConditionSatisfied() then
      arrTasksTriggered[#arrTasksTriggered + 1] = taskId
    end
  end
  return arrTasksTriggered
end

function TaskManager:_TriggerNewTasks(chapterName)
  local arrTasksTriggered = self:GetCanBeTriggeredNewTasks(chapterName)
  if next(arrTasksTriggered) then
    Table.ListAppend(self.m_arrOngoingTaskIds, arrTasksTriggered)
    self:_SaveOngoingData()
    EventDispatcher.DispatchEvent(EEventType.NewTasksUnlocked, {newTaskIds = arrTasksTriggered})
  end
end

function TaskManager:TryStartNewTasks()
  self:_TriggerNewTasks(self.m_strOngoingChapterName)
end

function TaskManager:TryStartNewChapter()
  if not self:IsOngoingChapterProgressFinished() then
    return false
  end
  local nextChapterName = GM.ChapterDataModel:GetNextChapterNameById(self.m_nOngoingChapterId)
  if not nextChapterName then
    return false
  end
  self.m_strOngoingChapterName = nextChapterName
  self.m_nOngoingChapterId = self.m_nOngoingChapterId + 1
  self.m_ongoingChapterFinishCount = 0
  self.m_ongoingChapterTotalCount = GM.TaskDataModel:GetChapterTaskCount(self.m_strOngoingChapterName)
  self:_TriggerNewTasks(nextChapterName)
  self:_GenerateCleanGoldCost()
  local arrChapterUnlockRewards = GM.ChapterDataModel:GetChapterUnlockRewards(self.m_nOngoingChapterId)
  if arrChapterUnlockRewards then
    RewardApi.AcquireRewardsLogic(arrChapterUnlockRewards, EPropertySource.Give, EBIType.ChapterUnlock, EGameMode.Board, CacheItemType.Stack)
  end
  EventDispatcher.DispatchEvent(EEventType.NewChapterUnlocked)
  return true, arrChapterUnlockRewards
end

function TaskManager:IsLastChapter()
  local index = self.m_nOngoingChapterId
  return index == GM.ChapterDataModel:GetChapterCount()
end

function TaskManager:SetAutoChangingChapter(auto)
  self.m_bAutoChangingChapter = auto
end

function TaskManager:IsAutoChangingChapter()
  return self.m_bAutoChangingChapter == true
end

function TaskManager:GetOngoingGroupId()
  return self.m_ongoingProgressId
end

function TaskManager:CanClaimProgressReward()
  local finished, total = self:GetProgressRewardProgress()
  return 0 < total and total <= finished
end

function TaskManager:ClaimProgressReward()
  if not self:CanClaimProgressReward() then
    return
  end
  local rewards = self:GetProgressReward()
  RewardApi.CryptRewards(rewards)
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.TaskGroup, EGameMode.Board, CacheItemType.Stack)
  self.m_ongoingProgressId = self.m_ongoingProgressId + 1
  self.m_progressTaskFinishCount = 0
  self:_SaveTaskProgress()
end

function TaskManager:GetProgressRewardProgress()
  return self.m_progressTaskFinishCount, GM.TaskDataModel:GetTaskGroupNeedNum(self.m_ongoingProgressId)
end

function TaskManager:GetProgressReward()
  return GM.TaskDataModel:GetTaskGroupRewards(self.m_ongoingProgressId)
end

function TaskManager:_GenerateCleanGoldCost()
  local totalCleanGold = self.m_nCleanGoldCount
  local leftCleanGold = totalCleanGold
  local totalConfigCost, arrUnfinishedTasks = self:GetCurChapterRemainTaskCosts()
  if totalConfigCost <= 0 then
    Log.Error("计算清场消耗错误1")
    return
  end
  table.sort(arrUnfinishedTasks, function(taskData1, taskData2)
    return taskData1.Id < taskData2.Id
  end)
  local mapCleanGoldCost = {}
  for _, taskData in ipairs(arrUnfinishedTasks) do
    local cleanCost = taskData.Cost / totalConfigCost * totalCleanGold
    cleanCost = math.ceil(cleanCost)
    if leftCleanGold > cleanCost then
      mapCleanGoldCost[taskData.Id] = cleanCost
      leftCleanGold = leftCleanGold - cleanCost
    else
      mapCleanGoldCost[taskData.Id] = leftCleanGold
      leftCleanGold = 0
      break
    end
  end
  self.m_mapCleanGoldCost = mapCleanGoldCost
  self:_SaveOngoingData()
  if GameConfig.IsTestMode() then
    TestAccumulatedLogInfo.Start()
    TestAccumulatedLogInfo.TestPrintCleanTaskCost(mapCleanGoldCost, totalCleanGold)
    TestAccumulatedLogInfo.LogNow()
  end
end

function TaskManager:GetTaskCleanGoldCost(taskId)
  return self.m_mapCleanGoldCost[taskId] or 0
end

function TaskManager:ConsumeTaskCleanGold(count)
  if count < 0 then
    Log.Error("消耗清场金币错误")
    return
  end
  if count > self.m_nCleanGoldCount then
    Log.Error("清场金币不够")
    count = self.m_nCleanGoldCount
  end
  self.m_nCleanGoldCount = self.m_nCleanGoldCount - count
  self:_SaveOngoingData()
  GM.BIManager:LogProject(EBIProjectType.ConsumeCleanGold, count)
end

function TaskManager:AddTaskCleanGold(count)
  self.m_nCleanGoldCount = self.m_nCleanGoldCount + count
  self:_SaveOngoingData()
  GM.BIManager:LogProject(EBIProjectType.AcquireCleanGold, count)
end

function TaskManager:GetCleanGoldCount()
  return self.m_nCleanGoldCount
end

function TaskManager:TryBalanceTaskCost()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  if not (GM.ConfigModel:IsServerControlOpen(EGeneralConfType.BalanceGold) and not orderModel:IsGuideGroup() and orderModel:IsSameChapterWithTask()) or self:IsAllFinished() then
    return
  end
  local canGet = orderModel:GetChapterCanGetGolds()
  local allowRemain = GM.ChapterDataModel:GetChapterRemainGold(self.m_nOngoingChapterId)
  local remainCost, arrUnfinishedTasks = self:GetCurChapterRemainTaskCosts()
  if remainCost == 0 then
    return
  end
  local delta = canGet - remainCost - allowRemain
  Log.Info("金币平衡：开始 canGet " .. canGet .. ", remainCost " .. remainCost .. ", delta " .. delta)
  if delta ~= 0 then
    GM.BIManager:LogAction(EBIType.GoldBalance, delta)
  end
  if 0 < delta then
    local totalAdjustCount = math.min(remainCost * 2, delta)
    local oneAdjustCount = totalAdjustCount / remainCost
    Log.Info("金币平衡：金币过多，增加消耗，增加 " .. totalAdjustCount .. ", 平均每个增加 " .. oneAdjustCount)
    for _, task in ipairs(arrUnfinishedTasks) do
      local oriCost = task.Cost
      local adjustCount = math.ceil(oriCost * oneAdjustCount)
      adjustCount = math.min(totalAdjustCount, adjustCount)
      task.Cost = oriCost + adjustCount
      totalAdjustCount = totalAdjustCount - adjustCount
      Log.Info("金币平衡：金币过多，增加消耗，id " .. task.Id .. ", 之前 " .. oriCost .. "，现在 " .. task.Cost)
    end
  elseif delta < 0 then
    local totalAdjustCount = math.min(remainCost, -delta)
    local oneAdjustCount = totalAdjustCount / remainCost
    Log.Info("金币平衡：金币过少，减少消耗，减少 " .. totalAdjustCount .. ", 平均每个减少 " .. oneAdjustCount)
    for _, task in ipairs(arrUnfinishedTasks) do
      local oriCost = task.Cost
      local adjustCount = math.ceil(oriCost * oneAdjustCount)
      adjustCount = math.min(totalAdjustCount, adjustCount)
      adjustCount = math.min(oriCost, adjustCount)
      task.Cost = oriCost - adjustCount
      totalAdjustCount = totalAdjustCount - adjustCount
      Log.Info("金币平衡：金币过少，减少消耗，id " .. task.Id .. ", 之前 " .. oriCost .. "，现在 " .. task.Cost)
    end
  else
    return
  end
end

function TaskManager:GetCurChapterRemainTaskCosts()
  local arrUnfinishedTasks = {}
  local mapFinishedTaskIds = self.m_mapChapterFinishedTaskIds[self.m_nOngoingChapterId] or {}
  local mapTaskData = GM.TaskDataModel:GetTaskDatasByChapter(self.m_strOngoingChapterName)
  local cost = 0
  for taskId, task in pairs(mapTaskData) do
    if not mapFinishedTaskIds[taskId] then
      arrUnfinishedTasks[#arrUnfinishedTasks + 1] = task
      cost = cost + task.Cost
    end
  end
  return cost, arrUnfinishedTasks
end

function TaskManager:HasPopedChapterFinishWin()
  local chapterId = self.m_metaDBTable:GetValueInNumber(EMetaDBKey.ChapterFinishWin, DB_VALUE_KEY)
  return chapterId >= self.m_nOngoingChapterId
end

function TaskManager:SetPopedChapterFinishWin()
  self.m_metaDBTable:Set(EMetaDBKey.ChapterFinishWin, DB_VALUE_KEY, self.m_nOngoingChapterId)
end

function TaskManager:GetCurChapterFinishedTaskRewards()
  local totalRewards = {}
  local mapFinishedTaskIds = self.m_mapChapterFinishedTaskIds[self.m_nOngoingChapterId] or {}
  local rewards
  for taskId, _ in pairs(mapFinishedTaskIds) do
    rewards = self:GetTaskRewards(taskId)
    Table.ListAppend(totalRewards, rewards)
  end
  return totalRewards
end
