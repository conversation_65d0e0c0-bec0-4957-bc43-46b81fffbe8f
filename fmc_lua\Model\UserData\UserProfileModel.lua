UserProfileModel = {}
UserProfileModel.__index = UserProfileModel
AVATAR_PREFIX = "head"
AVATAR_COUNT = 8
local PreAvatarReward = "ar_"
local DEFAULT_NAME_PREFIX = "Player"

function UserProfileModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.UserProfile)
  self.name = GM.UserModel:Get(EUserLocalDataKey.Name)
  self.icon = GM.UserModel:Get(EUserLocalDataKey.Icon)
  if self.icon == "avatar_christina" then
    self.icon = AVATAR_PREFIX .. 1
  end
end

function UserProfileModel:GetData()
  return self.m_dbTable
end

function UserProfileModel:FromSyncData(dataArr)
  self.m_dbTable:FromArr(dataArr)
end

function UserProfileModel:Get(key)
  return self.m_dbTable:GetValue(key, "value")
end

function UserProfileModel:AcquireAvatar(key)
  self.m_dbTable:Set(key, "value", 1)
end

function UserProfileModel:GetAvatarRewardsAll()
  return self.m_dbTable:GetValues()
end

function UserProfileModel:GetAvatarImagesAll()
  local avatars = {}
  for i = 1, AVATAR_COUNT do
    avatars[#avatars + 1] = AVATAR_PREFIX .. i
  end
  local tbMap = self.m_dbTable:GetValues()
  for avatar, _ in pairs(tbMap) do
    table.insert(avatars, self:GetAvatarImage(avatar))
  end
  return avatars
end

function UserProfileModel:GenerateDefaultNameIcon()
  if not StringUtil.IsNilOrEmpty(self.name) and not StringUtil.IsNilOrEmpty(self.icon) then
    return
  end
  local accountName = GM.AccountManager:GetAccountName()
  local accountIcon = GM.AccountManager:GetAccountPictureUrl()
  if not self:_IsValidName(accountName) then
    accountName = self:GetDefaultName()
  end
  if StringUtil.IsNilOrEmpty(accountIcon) then
    accountIcon = AVATAR_PREFIX .. "1"
  end
  self:_SetName(accountName)
  self:_SetIcon(accountIcon)
end

function UserProfileModel:GetDefaultName()
  return DEFAULT_NAME_PREFIX .. GM.UserModel:GetDisplayUserId()
end

function UserProfileModel:IsNowDefaultName()
  return self:GetName(true) == self:GetDefaultName()
end

function UserProfileModel:FromAvatarAndName(name, icon)
  if StringUtil.IsNilOrEmpty(name) or StringUtil.IsNilOrEmpty(icon) then
    return
  end
  self:_SetName(name)
  self:_SetIcon(icon)
end

function UserProfileModel:IsAvatarReward(avatar)
  if avatar == nil then
    return false
  end
  return StringUtil.StartWith(avatar, PreAvatarReward)
end

function UserProfileModel:GetAvatarImage(avatar)
  if self:IsAvatarReward(avatar) then
    return string.sub(avatar, #PreAvatarReward + 1, #avatar)
  end
  return avatar
end

function UserProfileModel:IsAvatarRewardIcon(icon)
  local map = self:GetAvatarRewardsAll()
  for name, _ in pairs(map) do
    if self:GetAvatarImage(name) == icon then
      return true
    end
  end
  return false
end

function UserProfileModel:GetName(ignoreCheck)
  if not ignoreCheck then
    Log.Assert(not StringUtil.IsNilOrEmpty(self.name), "user name is empty or nil")
  end
  return self.name or ""
end

function UserProfileModel:GetIcon()
  Log.Assert(not StringUtil.IsNilOrEmpty(self.icon), "user icon is empty or nil")
  return self.icon or ""
end

function UserProfileModel:ChangeName(name)
  if not self:_IsValidName(name) then
    return false
  end
  name = StringUtil.Trim(name)
  self:_SetName(name)
  GM.BIManager:LogAction(EBIType.ChangeName, name)
  EventDispatcher.DispatchEvent(EEventType.UpdateProfile)
  return true
end

function UserProfileModel:_SetName(name)
  self.name = name
  GM.UserModel:Set(EUserLocalDataKey.Name, self.name)
end

function UserProfileModel:ChangeIcon(icon)
  if StringUtil.IsNilOrEmpty(icon) then
    return
  end
  icon = StringUtil.Trim(icon)
  self:_SetIcon(icon)
  GM.BIManager:LogAction(EBIType.ChangeIcon, icon)
  EventDispatcher.DispatchEvent(EEventType.UpdateProfile)
end

function UserProfileModel:_SetIcon(icon)
  self.icon = icon
  GM.UserModel:Set(EUserLocalDataKey.Icon, self.icon)
end

function UserProfileModel:_IsValidName(name)
  if StringUtil.IsNilOrEmpty(name) then
    return false
  end
  if StringUtil.rFindChar(name, "%") then
    return false
  end
  if GM.GameTextModel:IsSensitiveWord(name) then
    return false
  end
  return true
end

function UserProfileModel:IsBuildInAvatar(icon)
  local tb = self:GetAvatarImagesAll()
  return Table.ListContain(tb, icon)
end
