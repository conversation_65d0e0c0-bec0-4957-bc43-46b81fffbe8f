BoardEntryShowArea = {}
BoardEntryShowArea.__index = BoardEntryShowArea
BoardEntryShowArea.Set = {}
BoardEntryShowArea.PosArea = {}
BoardEntryShowArea.halfspace = 90

function BoardEntryShowArea.Find()
  local i = 1
  while BoardEntryShowArea.PosArea[i] ~= nil do
    i = i + 1
  end
  BoardEntryShowArea.PosArea[i] = true
  return i
end

function BoardEntryShowArea.Create(prefabName)
  BoardEntryShowArea.Set[prefabName] = {}
  BoardEntryShowArea.Set[prefabName].id = BoardEntryShowArea.Find()
  BoardEntryShowArea.Set[prefabName].cnt = 0
  local prefab = BoardEntryShowArea._GetBoardEntryPrefab()
  local go = GameObject.Instantiate(prefab, GM.UIManager:GetCanvasRoot())
  UIUtil.SetActive(go, true)
  BoardEntryShowArea.Loaded(prefabName, go)
end

function BoardEntryShowArea.Loaded(prefabName, go)
  go.transform.localScale = Vector3.one * 0.8
  local luaTable = go:GetLuaTable()
  luaTable:UpdateScale()
  local hs = BoardEntryShowArea.halfspace
  local i = BoardEntryShowArea.Set[prefabName].id
  local anchorPosition = BoardEntryShowArea._GetAnchorPosition()
  local originPos = Vector3(anchorPosition.x + hs, anchorPosition.y + hs * (i * 2 - 1), go.transform.position.z)
  go.transform.position = originPos
  UIUtil.SetAnchoredPosition(go.transform, anchorPosition.x - hs)
  luaTable:SetFlyTargetPosition()
  go.transform.position = originPos
  go:SetActive(false)
  local parentRect = luaTable:GetContentRect()
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(prefabName), parentRect, V3Zero, function()
    if not go:IsNull() then
      go:SetActive(true)
      go.transform:DOLocalMoveX(anchorPosition.x - hs, 0.2):SetEase(Ease.OutCubic)
    end
  end)
  BoardEntryShowArea.Set[prefabName].button = go
  BoardEntryShowArea.Set[prefabName].pos = Vector3(anchorPosition.x - hs, anchorPosition.y + hs * (i * 2 - 1), go.transform.position.z)
end

function BoardEntryShowArea.Add(prefabName, flyCount)
  if BoardEntryShowArea.Set[prefabName] == nil then
    BoardEntryShowArea.Create(prefabName)
  end
  local ele = BoardEntryShowArea.Set[prefabName]
  ele.cnt = ele.cnt + (flyCount or 1)
  if not ele.button:IsNull() and ele.deleting then
    ele.deleting = nil
    ele.button.transform:DOKill(false)
    local anchorPosition = BoardEntryShowArea._GetAnchorPosition()
    ele.button.transform:DOLocalMoveX(anchorPosition.x - BoardEntryShowArea.halfspace, 0.2):SetEase(Ease.OutCubic)
  end
  if ele.button ~= nil and not ele.button:IsNull() then
    return ele.button
  end
end

function BoardEntryShowArea.Finish(prefabName)
  BoardEntryShowArea.Set[prefabName].cnt = BoardEntryShowArea.Set[prefabName].cnt - 1
  if BoardEntryShowArea.Set[prefabName].cnt == 0 then
    BoardEntryShowArea.Delete(prefabName)
  end
end

function BoardEntryShowArea.Delete(prefabName)
  if BoardEntryShowArea.Set[prefabName] == nil then
    return
  end
  BoardEntryShowArea.Set[prefabName].deleting = true
  local anchorPosition = BoardEntryShowArea._GetAnchorPosition()
  local obj = BoardEntryShowArea.Set[prefabName].button
  if obj:IsNull() then
    local i = BoardEntryShowArea.Set[prefabName].id
    BoardEntryShowArea.PosArea[i] = nil
    BoardEntryShowArea.Set[prefabName] = nil
    return
  end
  obj.transform:DOLocalMoveX(anchorPosition.x + BoardEntryShowArea.halfspace, 0.5):SetEase(Ease.OutCubic):OnComplete(function()
    if not obj:IsNull() then
      Object.Destroy(obj)
    end
    local i = BoardEntryShowArea.Set[prefabName].id
    BoardEntryShowArea.PosArea[i] = nil
    BoardEntryShowArea.Set[prefabName] = nil
  end)
end

function BoardEntryShowArea._GetAnchorPosition()
  local baseSceneView = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BaseSceneView)
  return baseSceneView:GetBoardEntryAnchor().position
end

function BoardEntryShowArea._GetBoardEntryPrefab()
  local baseSceneView = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BaseSceneView)
  return baseSceneView:GetBoardEntryPrefab()
end
