UIBoardItemDeleteButton = {}
UIBoardItemDeleteButton.__index = UIBoardItemDeleteButton

function UIBoardItemDeleteButton:Init(boardModel)
  self.m_boardModel = boardModel
  self:_UpdateVisibility()
  self:HideTapEffect()
  EventDispatcher.AddListener(EEventType.PopCachedItem, self, self._TryRefreshVisibility)
end

function UIBoardItemDeleteButton:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function UIBoardItemDeleteButton:_TryRefreshVisibility(msg)
  if msg ~= nil and msg.GameMode == self.m_boardModel:GetGameMode() then
    self:_UpdateVisibility(self.m_itemModel)
  end
end

function UIBoardItemDeleteButton:_UpdateVisibility(itemModel)
  if not self.m_boardModel:CanShowDeleteButton() then
    self.m_itemModel = itemModel
    self:_SetVisibility(false)
    return
  end
  self.m_itemModel = itemModel
  local bShow = itemModel ~= nil and self.m_boardModel:CanItemSell(itemModel)
  self:_SetVisibility(bShow)
  EventDispatcher.DispatchEvent(EEventType.ItemDeleteButtonUpdate)
  if not bShow then
    self:HideTapEffect()
  end
end

function UIBoardItemDeleteButton:_SetVisibility(bVisible)
  if self.gameObject and not self.gameObject:IsNull() then
    UIUtil.SetActive(self.gameObject, bVisible)
  end
end

function UIBoardItemDeleteButton:OnSellButtonClicked()
  self:HideTapEffect()
  GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "extraboard_delete_title", "extraboard_delete_desc", "common_button_cancel", "common_button_ok", function(window)
    window:Close()
  end, function(window)
    if self.m_boardModel:CheckItemStillInPosition(self.m_itemModel) then
      self.m_boardModel:SellItem(self.m_itemModel)
      self:_UpdateVisibility()
    end
    window:Close()
  end, false, nil, nil)
end

function UIBoardItemDeleteButton:UpdateItem(itemModel)
  self:_UpdateVisibility(itemModel)
end

function UIBoardItemDeleteButton:ShowTapEffect()
  UIUtil.SetActive(self.m_handEffectGo, true)
end

function UIBoardItemDeleteButton:HideTapEffect()
  UIUtil.SetActive(self.m_handEffectGo, false)
end
