TutorialPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  },
  allowPopWhenHasFlying = true,
  canIgnorePopup = false
}, BasePopupHelper)
TutorialPopupHelper.__index = TutorialPopupHelper

function TutorialPopupHelper.Create()
  local helper = setmetatable({}, TutorialPopupHelper)
  helper:Init()
  return helper
end

function TutorialPopupHelper:NeedCheckPopup()
  return GM.TutorialModel:ShouldAutoPopup()
end

function TutorialPopupHelper:CheckPopup()
  if GM.TutorialModel:ShouldAutoPopup() then
    return GM.TutorialModel:TryAutoPopup()
  end
end
