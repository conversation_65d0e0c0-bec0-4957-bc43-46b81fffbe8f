PayMessage = {DEFAULT_RETRY = 2, APP_ID = 6}
PayMessage.__index = PayMessage
local Serialization = require("Model.Network.Pay.PayInterface")
local floor = math.floor

function PayMessage.PaidProof(info, callback)
  local tbReq = {
    appId = PayMessage.APP_ID,
    userId = GM.UserModel:GetUserId(),
    adid = "::" .. CSAppsFlyerManager:GetAppsFlyerId(),
    idfa = DeviceInfo.GetAdvertisingIdentifier(),
    idfv = DeviceInfo.GetDeviceID(),
    mac = "",
    android_id = DeviceInfo.GetDeviceID(),
    gps_adid = DeviceInfo.GetAdvertisingIdentifier(),
    channelId = GameConfig.CURRENT_PLATFORM,
    payMethodId = info.payChannelId,
    purchaseDate = 0,
    orderId = info.orderId,
    productId = info.productId,
    amount = floor(info.realMoney * 100),
    level = GM.LevelModel:GetCurrentLevel(),
    currency = info.currency,
    country = DeviceInfo.GetCountry(),
    payResult = info.payResult,
    receiptData = info.receiptData,
    receiptSignature = info.receiptSignature,
    verifyResult = 0,
    verifyJSON = "",
    strScene = info.strScene or "",
    ctime = GM.GameModel:GetServerTime()
  }
  PayMessage.SendByHttp("ACProofAndGetStatus", tbReq, false, callback)
end

function PayMessage.IAP(info)
  local tbReq = {
    appId = PayMessage.APP_ID,
    userId = GM.UserModel:GetUserId(),
    channelId = GameConfig.CURRENT_PLATFORM,
    amount = 0,
    realMoney = floor((info.realMoney or 0) * 100),
    currency = info.currency or "",
    productID = info.productId or "",
    productOrder = info.orderId or "",
    payChannel = info.payChannelId or 0,
    iccid = "",
    level = GM.LevelModel:GetCurrentLevel(),
    strScene = info.strScene or "",
    ctime = GM.GameModel:GetServerTime()
  }
  PayMessage.SendByHttp("ACIAP", tbReq, true)
end

function PayMessage.Query(info, callback)
  local tbReq = {
    userId = GM.UserModel:GetUserId(),
    channelId = GameConfig.CURRENT_PLATFORM,
    productId = info.productId or ""
  }
  PayMessage.SendByHttp("ACReissueOrder", tbReq, false, callback)
end

function PayMessage.Serialize(reqCtx, strOpName, tbMsgReq)
  local writer = CSNetLibManager:CreateBufferBlockWriter(reqCtx.ReqBody)
  Serialization.MessageHeader.Serialize(writer, {
    ProtocolMd5 = Serialization.ProtocolMd5,
    MessageId = reqCtx.MessageId,
    Operation = strOpName
  })
  local serializer = Serialization.reqNameMap[strOpName]
  local result = serializer.Serialize(writer, tbMsgReq)
  CSNetLibManager:ReleaseBufferBlockWriter(writer)
  return result
end

function PayMessage.Deserialize(reqCtx, strOpName)
  local reader = CSNetLibManager:CreateBufferBlockReader(reqCtx.RespBody)
  local deserializer = Serialization.respNameMap[strOpName]
  local bRet, body
  bRet, body = deserializer.Deserialize(reader)
  CSNetLibManager:ReleaseBufferBlockReader(reader)
  if not bRet then
    return nil
  end
  return body
end

function PayMessage.SendByHttp(strOpName, tbMsgReq, bCache, callback, uTimeout, uRetryCount)
  local strUrl = NetworkConfig.GetHttpServerUrl(strOpName)
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(strUrl, "POST", uTimeout or 8000, uRetryCount or PayMessage.DEFAULT_RETRY)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetLogFail()
  if not PayMessage.Serialize(reqCtx, strOpName, tbMsgReq) then
    Log.Warning("PayMessage Serialize failed for " .. strOpName)
  end
  local response
  reqCtx:SetCheckResponse(function()
    if GM == nil then
      return nil
    end
    if math.floor(reqCtx.Status / 100) == 2 then
      response = PayMessage.Deserialize(reqCtx, strOpName)
      if response == nil then
        return "Failed to deserialize response"
      end
      if response.rcode == 60 or response.resultCode == 60 then
        return "Error response"
      end
    end
    return nil
  end)
  reqCtx:SetCallback(function()
    if GM == nil then
      return
    end
    if callback ~= nil then
      local result = false
      if reqCtx.Rcode == ResultCode.Succeeded then
        result = true
      elseif reqCtx.Rcode == ResultCode.Not2xx then
        if reqCtx.Status == 403 and reqCtx.MaxRetry > 0 then
          reqCtx.MaxRetry = reqCtx.MaxRetry - 1
          reqCtx:Retain()
          reqCtx:Send()
          return
        end
      else
        if PayMessage.TryFallbackRequest(reqCtx) then
          return
        end
        response = reqCtx.ErrorMsg
      end
      callback(result, response, reqCtx)
    end
    if reqCtx.Rcode ~= ResultCode.Succeeded then
      Log.Warning("Pay Message failed, error message is " .. reqCtx.ErrorMsg, LogTag.Purchase)
    end
    if bCache then
      GM.HttpManager:OnFinishSavedHttpRequest(reqCtx.MessageId, reqCtx.Rcode)
    end
  end)
  if bCache then
    GM.HttpManager:SaveHttpRequest(reqCtx, strOpName)
  end
  reqCtx:Send()
end

function PayMessage.TryFallbackRequest(reqCtx)
  return GM.HttpManager:TryFallbackRequest(reqCtx, "207.90.252.33", "https://payapi-ga.mergecola.com")
end
