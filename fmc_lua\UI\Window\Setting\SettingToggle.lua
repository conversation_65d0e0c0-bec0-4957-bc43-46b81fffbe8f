SettingToggle = {}
SettingToggle.__index = SettingToggle

function SettingToggle:Init(clickCallback, toggleChecker)
  self.m_clickCallback = clickCallback
  self.m_toggleChecker = toggleChecker
  self:UpdateContent()
end

function SettingToggle:IsOn()
  return self.m_on
end

function SettingToggle:OnClicked()
  if self.m_clickCallback ~= nil then
    self.m_clickCallback(self)
    self:UpdateContent()
  end
end

function SettingToggle:UpdateContent()
  local isOn = self.m_toggleChecker and self.m_toggleChecker() or false
  if self.m_on ~= isOn then
    self.m_on = isOn
    self.m_closeGo:SetActive(not self.m_on)
    self.m_iconGo:SetActive(self.m_on)
    self.m_disabledIconGo:SetActive(not self.m_on)
  end
end
