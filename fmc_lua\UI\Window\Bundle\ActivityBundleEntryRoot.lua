ActivityBundleEntryRoot = {}
ActivityBundleEntryRoot.__index = ActivityBundleEntryRoot

function ActivityBundleEntryRoot:Awake()
  self:_AddListeners()
end

function ActivityBundleEntryRoot:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function ActivityBundleEntryRoot:Init(activityType)
  self.m_mapHudButton = {}
  self.m_activityType = activityType
  local model
  for _, bundleType in ipairs(BundleTypeOrderList) do
    model = GM.BundleManager:GetModel(bundleType)
    if model ~= nil then
      for _, dataGroup in ipairs(model:GetAllGroupDatas() or {}) do
        self:_OnBundleDataRefreshed(dataGroup, bundleType)
      end
    end
  end
  if self.gameObject.activeInHierarchy then
    self:_AddListeners()
  end
end

function ActivityBundleEntryRoot:_AddListeners()
  EventDispatcher.AddListener(EEventType.BundleDataRefreshed, self, self.OnBundleDataRefreshed, true)
end

function ActivityBundleEntryRoot:OnBundleDataRefreshed(msg)
  self:_OnBundleDataRefreshed(msg.dataGroup, msg.bundleType)
end

function ActivityBundleEntryRoot:_OnBundleDataRefreshed(dataGroup, bundleType)
  local model = GM.BundleManager:GetModel(bundleType)
  if model == nil or model.GetActivityEntryShowConfig == nil then
    return
  end
  local entryConfig = model:GetActivityEntryShowConfig()
  local hudKey = GetBundleHudKey(bundleType, dataGroup:GetGroupId())
  local uiDefinition = BundleUIType[dataGroup:GetBundleUIType()]
  local prefabName = uiDefinition and uiDefinition.entryPrefabName or entryConfig.entryPrefabName
  local checkFunc = function()
    return entryConfig.checkFun(dataGroup, self.m_activityType)
  end
  local updateFunction = self:_EntryUpdateFunctionCreator(hudKey, prefabName, checkFunc, {bundleType, dataGroup}, true)
  updateFunction()
end

function ActivityBundleEntryRoot:_EntryUpdateFunctionCreator(hudType, entryPrefabConfigName, checkFunc, params, bRefreshLayout)
  local entryNodeName = GetEntryRootName(EEntryRootKey.Bundle)
  if params == nil then
    params = {}
  end
  local refreshLayoutFunc = function()
    if self[entryNodeName] ~= nil then
      LayoutRebuilder.ForceRebuildLayoutImmediate(self[entryNodeName])
      LayoutRebuilder.ForceRebuildLayoutImmediate(self[entryNodeName].parent)
    end
  end
  return function()
    if checkFunc() then
      UIUtil.SetActive(self[entryNodeName].gameObject, true)
      if self.m_mapHudButton[hudType] == nil then
        local config = GM.DataResource.UIPrefabConfig:GetConfig(entryPrefabConfigName)
        local callback = function(gameObject)
          if self.m_mapHudButton[hudType] ~= nil or not checkFunc() then
            AddressableLoader.Destroy(gameObject)
            return
          end
          local entry = gameObject:GetLuaTable()
          if entry.Init ~= nil then
            entry:Init(table.unpack(params))
          end
          entry:SetFlyTargetPosition()
          self.m_mapHudButton[hudType] = entry
          if bRefreshLayout then
            refreshLayoutFunc()
          end
        end
        GM.ResourceLoader:LoadPrefab(config, self[entryNodeName], V3Zero, callback)
      end
    elseif self.m_mapHudButton[hudType] ~= nil then
      local btn = self.m_mapHudButton[hudType]
      self.m_mapHudButton[hudType] = nil
      if bRefreshLayout then
        UIUtil.SetActive(btn.gameObject, false)
        refreshLayoutFunc()
      end
      AddressableLoader.Destroy(btn.gameObject)
    end
  end
end
