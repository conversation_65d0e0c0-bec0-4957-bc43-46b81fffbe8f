TestTutorialInfoWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestTutorialInfoWindow.__index = TestTutorialInfoWindow

function TestTutorialInfoWindow:Init()
  local arrTutorialConfigs = require("Data.Tutorial.TutorialConfig")
  local tutorialModel = GM.TutorialModel
  self.m_arrCells = {}
  local cellObj
  for i, config in ipairs(arrTutorialConfigs) do
    cellObj = GameObject.Instantiate(self.m_cellOrigin, self.m_contentRectTrans)
    cellObj:SetActive(true)
    self.m_arrCells[i] = cellObj:GetLuaTable()
    self.m_arrCells[i]:Init(tutorialModel, config, i, self)
  end
end

function TestTutorialInfoWindow:UpdateContent()
  for _, cell in ipairs(self.m_arrCells) do
    cell:UpdateContent()
  end
end

function TestTutorialInfoWindow:OnSelectAllBtnClicked()
  for _, cell in ipairs(self.m_arrCells) do
    cell:SetOn()
  end
end

function TestTutorialInfoWindow:OnFinishBtnClicked()
  local tutorialModel = GM.TutorialModel
  local tutorialId
  for _, cell in ipairs(self.m_arrCells) do
    tutorialId = cell:GetTutorialConfig().id
    if cell:IsOn() and (tutorialModel.m_mapOngoingTutorials[tutorialId] or not tutorialModel:IsTutorialFinished(tutorialId)) then
      tutorialModel:TestFinishTutorial(tutorialId)
    end
  end
  if TutorialHelper.GetSceneView() then
    TutorialHelper.GetTutorialLayer():HideAll()
  end
  self:UpdateContent()
  GM.UIManager:ShowPrompt("完成")
end

function TestTutorialInfoWindow:OnFinishOneBtnClicked()
  local tutorialModel = GM.TutorialModel
  local tutorialId
  for _, cell in ipairs(self.m_arrCells) do
    tutorialId = cell:GetTutorialConfig().id
    if not cell:IsOn() and not tutorialModel:IsTutorialFinished(tutorialId) then
      tutorialModel:TestFinishTutorial(tutorialId)
      cell:JumpToMe()
      break
    end
  end
  if TutorialHelper.GetSceneView() then
    TutorialHelper.GetTutorialLayer():HideAll()
  end
  self:UpdateContent()
  GM.UIManager:ShowPrompt("完成")
end

function TestTutorialInfoWindow:OnFinishMutiBtnClicked()
  local tutorialModel = GM.TutorialModel
  local tutorialId
  local idx = 0
  local jump = false
  for _, cell in ipairs(self.m_arrCells) do
    tutorialId = cell:GetTutorialConfig().id
    if not cell:IsOn() and not tutorialModel:IsTutorialFinished(tutorialId) then
      tutorialModel:TestFinishTutorial(tutorialId)
      idx = idx + 1
      if not jump then
        cell:JumpToMe()
        jump = true
      end
    end
    if idx == 5 then
      break
    end
  end
  if TutorialHelper.GetSceneView() then
    TutorialHelper.GetTutorialLayer():HideAll()
  end
  self:UpdateContent()
  GM.UIManager:ShowPrompt("完成")
end

function TestTutorialInfoWindow:OnUnFinishMutiBtnClicked()
  local tutorialModel = GM.TutorialModel
  local tutorialId
  local idx = 0
  local jump = false
  for _, cell in ipairs(self.m_arrCells) do
    tutorialId = cell:GetTutorialConfig().id
    if not cell:IsOn() and tutorialModel:IsTutorialFinished(tutorialId) then
      tutorialModel:TestUnFinishTutorial(tutorialId)
      idx = idx + 1
      if not jump then
        cell:JumpToMe()
        jump = true
      end
    end
    if idx == 5 then
      break
    end
  end
  self:UpdateContent()
  GM.UIManager:ShowPrompt("完成")
end

TestTutorialInfoCell = {}
TestTutorialInfoCell.__index = TestTutorialInfoCell

function TestTutorialInfoCell:Init(tutorialModel, tutorialConfig, index, parent)
  self.m_config = tutorialConfig
  self.m_tutorialModel = tutorialModel
  self.m_index = index
  self.m_parent = parent
  self:UpdateContent()
end

function TestTutorialInfoCell:UpdateContent()
  local str = self.m_config.id .. "[" .. self.m_config.desc .. "]"
  local finished = self.m_tutorialModel:IsTutorialFinished(self.m_config.id)
  if finished then
    str = str .. " 完成"
  else
    local ongoing = self.m_tutorialModel:IsTutorialOnGoing(self.m_config.id)
    if ongoing then
      str = str .. " 进行中:" .. tostring(self.m_tutorialModel:GetOngoindDatas(self.m_config.id))
    else
      str = str .. " 未开始"
    end
  end
  self.m_dataText.text = str
  self.m_toggle.isOn = finished
end

function TestTutorialInfoCell:IsOn()
  return self.m_toggle.isOn
end

function TestTutorialInfoCell:SetOn()
  self.m_toggle.isOn = true
end

function TestTutorialInfoCell:GetTutorialConfig()
  return self.m_config
end

function TestTutorialInfoCell:JumpToMe()
  local cellHeight = 59.29
  local viewHeight = 810.15
  if self.m_index < 7 then
    return
  end
  local contentHeight = self.m_parent.m_contentRectTrans.sizeDelta.y
  local moveY = (self.m_index - 1 - 7) * cellHeight
  if moveY > contentHeight - viewHeight - cellHeight then
    moveY = contentHeight - viewHeight - cellHeight
  end
  self.m_parent.m_contentRectTrans:SetLocalPosY(moveY)
end
