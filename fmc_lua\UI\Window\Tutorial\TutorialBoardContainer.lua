TutorialBoardContainer = {}
TutorialBoardContainer.__index = TutorialBoardContainer

function TutorialBoardContainer:Init(tutorialBoardName, onAnimationCompleted)
  self.m_tutorialBoardData = require("Model.TutorialBoard." .. tutorialBoardName)
  self.m_onAnimationCompleted = onAnimationCompleted
  self.m_descriptionText.text = GM.GameTextModel:GetText(self.m_tutorialBoardData.Description)
  local func = function()
    while true do
      self:_PlayTutorialBoardAnimation()
    end
  end
  self.m_coroutine = coroutine.create(func)
end

function TutorialBoardContainer:OnDestroy()
  if self.m_tween ~= nil then
    self.m_tween:Kill()
  end
  TutorialBoardHelper.End()
end

function TutorialBoardContainer:Update()
  if self.m_coroutine == nil then
    return
  end
  if self.m_tween ~= nil and not self.m_tween:IsActive() then
    self.m_tween = nil
  end
  if self.m_tween == nil then
    local success, argument = coroutine.resume(self.m_coroutine)
    if success then
      self.m_tween = argument
    else
      Log.Assert(false, "引导棋盘执行错误：\n" .. argument)
    end
  end
end

function TutorialBoardContainer:UpdatePerSecond()
  if self.m_coroutine == nil then
    return
  end
  TutorialBoardHelper.UpdatePerSecond()
end

function TutorialBoardContainer:_PlayTutorialBoardAnimation()
  TutorialBoardHelper.Start()
  self.m_tutorialBoardData.Init()
  TutorialBoardHelper.Show()
  coroutine.yield(self.m_boardImage:DOFade(1, 1))
  self.m_tutorialBoardData.Pass()
  coroutine.yield(self.m_boardImage:DOFade(0, 1))
  TutorialBoardHelper.End()
  if self.m_onAnimationCompleted ~= nil then
    self.m_onAnimationCompleted()
  end
end
