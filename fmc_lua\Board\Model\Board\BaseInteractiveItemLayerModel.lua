BaseInteractiveItemLayerModel = setmetatable({}, BaseItemLayerModel)
BaseInteractiveItemLayerModel.__index = BaseInteractiveItemLayerModel

function BaseInteractiveItemLayerModel:Init(boardModel, dbTable, itemManager)
  BaseItemLayerModel.Init(self, boardModel)
  self.m_dbTable = dbTable
  self.m_itemManager = itemManager
  self:ResetData()
end

function BaseInteractiveItemLayerModel:ResetData()
  self.m_itemTypeCountMap = {}
  self.m_mapItems = {}
  self.m_mapTypeItems = {}
  self.m_emptyCount = self.m_boardModel:GetHorizontalTiles() * self.m_boardModel:GetVerticalTiles()
end

function BaseInteractiveItemLayerModel:_SyncDBData()
  local posArr = {}
  for key, data in pairs(self.m_dbTable:GetValues()) do
    local item = self.m_itemManager:GetItem(data.itemId)
    if item then
      local count = 1
      for p in string.gmatch(key, "([^_]+)") do
        posArr[count] = tonumber(p)
        count = count + 1
      end
      local pos = self.m_boardModel:CreatePosition(posArr[1], posArr[2])
      self.m_items:SetValueOnPosition(pos, data.itemId)
      item:SetPosition(pos)
      self:_OnItemChange(item, true)
    else
      GM.BIManager:LogProject(EBIType.ItemNotFound, "SILM itemId:" .. tostring(data.itemId))
      self.m_dbTable:Remove(key)
    end
  end
end

function BaseInteractiveItemLayerModel:_OnItemChange(itemModel, bAddItem)
  local itemType = itemModel and itemModel:GetType() or nil
  self.m_emptyCount = self.m_emptyCount + (bAddItem and -1 or 1)
  self.m_itemTypeCountMap[itemType] = (self.m_itemTypeCountMap[itemType] or 0) + (bAddItem and 1 or -1)
  if not self.m_mapTypeItems[itemType] then
    self.m_mapTypeItems[itemType] = {}
  end
  if bAddItem then
    self.m_mapItems[itemModel] = true
    self.m_mapTypeItems[itemType][itemModel] = true
  else
    self.m_mapItems[itemModel] = nil
    self.m_mapTypeItems[itemType][itemModel] = nil
  end
end

function BaseInteractiveItemLayerModel:GetItem(position)
  if position == nil or not position:IsValid() then
    return nil
  end
  local itemId = self.m_items:GetValueOnPosition(position)
  return self.m_itemManager:GetItem(itemId)
end

function BaseInteractiveItemLayerModel:FilterItems(filter, limitCount)
  local arrResults = {}
  local count = 0
  for itemModel, _ in pairs(self.m_mapItems) do
    if limitCount and limitCount <= count then
      break
    end
    if filter(itemModel) then
      table.insert(arrResults, itemModel)
      count = count + 1
    end
  end
  return arrResults
end

function BaseInteractiveItemLayerModel:FilterItemsByType(itemType, filter, limitCount)
  local arrResults = {}
  local count = 0
  for itemModel, _ in pairs(self.m_mapTypeItems[itemType] or {}) do
    if limitCount and limitCount <= count then
      break
    end
    if not filter or filter(itemModel) then
      table.insert(arrResults, itemModel)
      count = count + 1
    end
  end
  return arrResults
end

function BaseInteractiveItemLayerModel:SetItem(position, item)
  if position == nil or not position:IsValid() then
    return
  end
  local originalItem = self:GetItem(position)
  if originalItem then
    self:_OnItemChange(originalItem, false)
  end
  local itemId = item and item:GetId()
  if itemId == nil then
    self.m_dbTable:Remove(position:GetX() .. "_" .. position:GetY())
  else
    self.m_dbTable:Set(position:GetX() .. "_" .. position:GetY(), "itemId", itemId)
  end
  self.m_items:SetValueOnPosition(position, itemId)
  if item then
    self:_OnItemChange(item, true)
  end
end

function BaseInteractiveItemLayerModel:GetItemCountByType(itemType)
  return self.m_itemTypeCountMap[itemType] or 0
end

function BaseInteractiveItemLayerModel:GetItemsByType(itemType)
  local map = self.m_mapTypeItems[itemType]
  return map or {}
end

function BaseInteractiveItemLayerModel:GetOneBoardItemByType(itemType)
  local map = self.m_mapTypeItems[itemType]
  return map and next(map)
end

function BaseInteractiveItemLayerModel:GetEmptyPositionCount()
  return self.m_emptyCount
end

function BaseInteractiveItemLayerModel:HasItem(itemModel)
  return self.m_mapItems[itemModel] ~= nil
end

function BaseInteractiveItemLayerModel:GetAllItems()
  return self.m_mapItems
end
