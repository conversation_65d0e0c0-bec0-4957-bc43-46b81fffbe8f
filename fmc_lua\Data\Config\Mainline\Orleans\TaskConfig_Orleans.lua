return {
  {
    ChapterId = "Orleans",
    Id = 1,
    Cost = 663,
    <PERSON><PERSON>s = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "trashIndoorA",
        State = 100
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 2,
    StartConditions = {1},
    Cost = 663,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "trashIndoorB",
        State = 100
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 3,
    StartConditions = {2},
    Cost = 733,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "trashIndoorC",
        State = 100
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 4,
    StartConditions = {3},
    Cost = 875,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "firstFloor", State = 9},
      {
        Slot = "firstFloorOld",
        State = 100
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 5,
    StartConditions = {4},
    Cost = 663,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "trashIndoorD",
        State = 100
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 6,
    StartConditions = {5},
    Cost = 592,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "trashIndoorE",
        State = 100
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 7,
    StartConditions = {6},
    Cost = 946,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secFloor", State = 9}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 8,
    StartConditions = {7},
    Cost = 946,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secFence", State = 9}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 9,
    StartConditions = {8},
    Cost = 735,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secRoof", State = 9}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 10,
    StartConditions = {9},
    Cost = 833,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secWallLeft",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 11,
    StartConditions = {10},
    Cost = 833,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secWallRight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 12,
    StartConditions = {11},
    Cost = 833,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secWinLeft", State = 9}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 13,
    StartConditions = {12},
    Cost = 833,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secWinRight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 14,
    StartConditions = {13},
    Cost = 784,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "secRag", State = 9}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 15,
    StartConditions = {14},
    Cost = 735,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secTableLeft",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 16,
    StartConditions = {15},
    Cost = 735,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secUtensilLeft",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 17,
    StartConditions = {16},
    Cost = 799,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secTableRight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 18,
    StartConditions = {17},
    Cost = 799,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "secUtensilRight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 19,
    StartConditions = {18},
    Cost = 744,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "curtainLeft",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 20,
    StartConditions = {19},
    Cost = 744,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "curtainRight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 21,
    StartConditions = {20},
    Cost = 855,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "chandelier​​Left",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 22,
    StartConditions = {21},
    Cost = 855,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "chandelier​​Right",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 23,
    StartConditions = {22},
    Cost = 910,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "statue", State = 1}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 24,
    StartConditions = {23},
    Cost = 855,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "statue", State = 9}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 25,
    StartConditions = {24},
    Cost = 953,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stage", State = 9}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 26,
    StartConditions = {25},
    Cost = 894,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stagePiano", State = 1}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 27,
    StartConditions = {26},
    Cost = 1011,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "stagePiano", State = 9}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 28,
    StartConditions = {27},
    Cost = 777,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "circleTableLeft",
        State = 1
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 29,
    StartConditions = {28},
    Cost = 835,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "circleTableLeft",
        State = 2
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 30,
    StartConditions = {29},
    Cost = 777,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "circleTableLeft",
        State = 3
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 31,
    StartConditions = {30},
    Cost = 777,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "circleTableLeft",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 32,
    StartConditions = {31},
    Cost = 777,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "circleTableRight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 33,
    StartConditions = {32},
    Cost = 896,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "circleChairRight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 34,
    StartConditions = {33},
    Cost = 831,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "circleUtensilRight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 35,
    StartConditions = {34},
    Cost = 831,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "circleCandleRight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 36,
    StartConditions = {35},
    Cost = 831,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "trashLeft", State = 100}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 37,
    StartConditions = {36},
    Cost = 896,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "groundFloor",
        State = 9
      },
      {
        Slot = "groundFloorOld",
        State = 100
      },
      {
        Slot = "rightFenceTrash",
        State = 100
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 38,
    StartConditions = {37},
    Cost = 961,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftOutWall",
        State = 1
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 39,
    StartConditions = {38},
    Cost = 1092,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftOutWall",
        State = 2
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 40,
    StartConditions = {39},
    Cost = 701,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftOutWall",
        State = 3
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 41,
    StartConditions = {40},
    Cost = 878,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftOutWall",
        State = 4
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 42,
    StartConditions = {41},
    Cost = 878,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftOutWall",
        State = 5
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 43,
    StartConditions = {42},
    Cost = 962,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftOutWall",
        State = 6
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 44,
    StartConditions = {43},
    Cost = 962,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftOutWall",
        State = 7
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 45,
    StartConditions = {44},
    Cost = 1045,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "leftFence", State = 9}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 46,
    StartConditions = {45},
    Cost = 795,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "leftFenceLight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 47,
    StartConditions = {46},
    Cost = 1045,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightFence", State = 9}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 48,
    StartConditions = {47},
    Cost = 795,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightFenceLight",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 49,
    StartConditions = {48},
    Cost = 938,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "trashRightShelf",
        State = 1
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 50,
    StartConditions = {49},
    Cost = 938,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "trashRightShelf",
        State = 100
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 51,
    StartConditions = {50},
    Cost = 1069,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightOutWall",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 52,
    StartConditions = {51},
    Cost = 1135,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightShelfPlant",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 53,
    StartConditions = {52},
    Cost = 873,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {
        Slot = "rightBackPlant",
        State = 9
      }
    }
  },
  {
    ChapterId = "Orleans",
    Id = 54,
    StartConditions = {53},
    Cost = 807,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightTable", State = 1}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 55,
    StartConditions = {54},
    Cost = 873,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightTable", State = 2}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 56,
    StartConditions = {55},
    Cost = 938,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightTable", State = 3}
    }
  },
  {
    ChapterId = "Orleans",
    Id = 57,
    StartConditions = {56},
    Cost = 1069,
    Rewards = {
      {Currency = "exp", Amount = 185},
      {Currency = "energy", Amount = 15}
    },
    SlotState = {
      {Slot = "rightTable", State = 9}
    }
  }
}
