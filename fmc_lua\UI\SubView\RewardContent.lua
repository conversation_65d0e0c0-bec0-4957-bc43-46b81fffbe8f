RewardContent = {}
RewardContent.__index = RewardContent

function RewardContent:Awake()
  if self.m_bCanAddListener and self.m_bCheckRewardValid then
    RewardApi.AddFilterRewardEventListener(self, self._UpdateRewards)
  end
  self.m_bAwaked = true
end

function RewardContent:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function RewardContent:Init(rewards, scrollNum, detailWindowRefer)
  self.m_initRewards = rewards
  self.m_scrollNum = scrollNum
  self.m_detailWindowRefer = detailWindowRefer
  self:_UpdateRewards()
  if self.m_bAwaked and self.m_bCheckRewardValid then
    RewardApi.AddFilterRewardEventListener(self, self._UpdateRewards)
  else
    self.m_bCanAddListener = true
  end
end

function RewardContent:_UpdateRewards()
  self.m_arrRewards = RewardApi.FilterRewards(self.m_initRewards)
  local rewards = self.m_arrRewards
  if self.m_rewardItems == nil then
    self.m_rewardItems = {}
  end
  self.m_avatarGo:SetActive(false)
  self.m_rewardGo:SetActive(false)
  local rewardGo, rewardType
  for i = 1, #rewards do
    rewardType = rewards[i][PROPERTY_TYPE]
    if GM.UserProfileModel:IsAvatarReward(rewardType) then
      if self.m_rewardItems[i] ~= nil then
        self.m_rewardItems[i].gameObject:RemoveSelf()
        self.m_rewardItems[i] = nil
      end
      rewardGo = self.m_avatarGo
    elseif StringUtil.StartWith(rewardType, ProducerInventoryRewardPrefix) and self.m_inventoryProducerGo ~= nil then
      if self.m_rewardItems[i] ~= nil then
        self.m_rewardItems[i].gameObject:RemoveSelf()
        self.m_rewardItems[i] = nil
      end
      rewardGo = self.m_inventoryProducerGo
    else
      rewardGo = self.m_rewardGo
    end
    if self.m_rewardItems[i] == nil then
      self.m_rewardItems[i] = Object.Instantiate(rewardGo, self.m_rewardsRoot):GetLuaTable()
    end
    UIUtil.SetActive(self.m_rewardItems[i].gameObject, true)
    self.m_rewardItems[i]:Init(rewards[i], self.m_detailWindowRefer)
  end
  for i = #rewards + 1, #self.m_rewardItems do
    self.m_rewardItems[i].gameObject:SetActive(false)
  end
  if self.m_viewportImg ~= nil and not self.m_viewportImg:IsNull() then
    local num = self.m_scrollNum or 6
    if num >= #rewards then
      self.m_viewportImg.raycastTarget = false
    else
      self.m_viewportImg.raycastTarget = true
    end
  end
  if self.m_scrollViewScro ~= nil and not self.m_scrollViewScro:IsNull() then
    self.m_scrollViewScro.verticalNormalizedPosition = 1
  end
end

function RewardContent:ClearRewards()
  if Table.IsEmpty(self.m_rewardItems) then
    return
  end
  for i = 1, #self.m_rewardItems do
    UIUtil.SetActive(self.m_rewardItems[i].gameObject, false)
    Object.Destroy(self.m_rewardItems[i].gameObject)
  end
  self.m_rewardItems = {}
end

function RewardContent:GetRewardItem(index)
  return self.m_rewardItems and self.m_rewardItems[index] or nil
end

function RewardContent:PlayRewardAnimation(bLockEvent)
  if bLockEvent == nil then
    bLockEvent = true
  end
  GM.AudioModel:PlayEffect(AudioFileConfigName.sfxRewardCollect)
  local arrWorldPos = {}
  for i, rewardData in ipairs(self.m_arrRewards) do
    arrWorldPos[i] = self:GetRewardItem(i):GetIconRectTrans().position
  end
  RewardApi.AcquireRewardsInView(self.m_arrRewards, {arrWorldPos = arrWorldPos, eventLock = bLockEvent})
end

RewardItem = {}
RewardItem.__index = RewardItem
local InnerRewardType = {
  Property = 1,
  Skin = 2,
  Item = 3,
  Avatar = 4,
  Album = 5,
  Joker = 6
}

function RewardItem:Init(data)
  self.m_infoButton.gameObject:SetActive(false)
  local image, setNativeSize = RewardApi.GetRewardIconNameAndIsSetNativeSize(data[PROPERTY_TYPE])
  local scale = 1
  if GM.PropertyDataManager:IsPropertyType(data[PROPERTY_TYPE]) then
    if AlbumActivityModel.IsAlbumPackReward(data[PROPERTY_TYPE]) then
      self.m_eInnerRewardType = InnerRewardType.Album
    elseif AlbumActivityModel.IsAlbumJokerCard(data[PROPERTY_TYPE]) then
      self.m_eInnerRewardType = InnerRewardType.Joker
    else
      self.m_eInnerRewardType = InnerRewardType.Property
    end
    if not self.m_usePreserveAspect then
      scale = 0.8
    else
      scale = RewardItem.GetSpecialRewardScale(data[PROPERTY_TYPE])
    end
  elseif GM.UserProfileModel:IsAvatarReward(data[PROPERTY_TYPE]) then
    self.m_eInnerRewardType = InnerRewardType.Avatar
  else
    self.m_eInnerRewardType = InnerRewardType.Item
  end
  self.m_icon.transform:SetLocalScaleXY(scale)
  if self.m_usePreserveAspect then
    setNativeSize = false
  end
  self.m_icon.enabled = false
  SpriteUtil.SetImage(self.m_icon, image, setNativeSize, function()
    if not self.m_icon:IsNull() then
      self.m_icon.enabled = true
      self:OnSpriteLoadFinish()
    end
  end)
  if not data[PROPERTY_COUNT] then
    self:SetAmountText("")
  else
    self:SetAmountText(tostring(data[PROPERTY_COUNT]))
  end
end

function RewardItem.GetSpecialRewardScale(rewardType)
  if rewardType == EPropertyType.Gem or rewardType == EPropertyType.Energy then
    return 0.9
  end
  return 1
end

function RewardItem:OnSpriteLoadFinish()
end

function RewardItem:SetTipButtonVisible(visible)
  self.m_infoButton.gameObject:SetActive(visible)
end

function RewardItem:SetAmountText(str)
  if self.m_amount == nil then
    return
  end
  if self.m_hideSingleAmount and self.m_eInnerRewardType ~= InnerRewardType.Property and tonumber(count) == 1 then
    self.m_amount.text = ""
    return
  end
  if self.m_noXForProperty and self.m_eInnerRewardType == InnerRewardType.Property then
    self.m_amount.text = str
    return
  end
  self.m_amount.text = self.m_needX and "x" .. str or str
end

function RewardItem:GetIconRectTrans()
  return self.m_icon.transform
end

function RewardItem:GetIcon()
  return self.m_icon
end

function RewardItem:GetText()
  return self.m_amount
end

RewardItemWithItemTipButton = setmetatable({infoIapMode = false}, RewardItem)
RewardItemWithItemTipButton.__index = RewardItemWithItemTipButton

function RewardItemWithItemTipButton:Init(data, detailWindowRefer)
  RewardItem.Init(self, data)
  if self.m_eInnerRewardType == InnerRewardType.Item then
    self.m_itemType = data[PROPERTY_TYPE]
    self.m_infoButton:Init(function()
      ItemDetailWindow.Open(self.m_itemType, ItemDetailWindowMode.Normal, detailWindowRefer or EItemDetailWindowRefer.RewardDisplay)
    end)
    self.m_infoButton.gameObject:SetActive(true)
  elseif self.m_eInnerRewardType == InnerRewardType.Album then
    self.m_infoButton:Init(function()
      GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.AlbumPackRewardBubble), self.transform, V3Zero, function(go)
        if go ~= nil and not go:IsNull() then
          local tip = go:GetLuaTable()
          tip:Init(self:GetIconRectTrans(), data[PROPERTY_TYPE])
        end
      end)
    end)
    self.m_infoButton.gameObject:SetActive(true)
  elseif self.m_eInnerRewardType == InnerRewardType.Joker then
    self.m_infoButton:Init(function()
      GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.AlbumJokerRewardBubble), self.transform, V3Zero, function(go)
        if go ~= nil and not go:IsNull() then
          local tip = go:GetLuaTable()
          tip:Init(self:GetIconRectTrans())
        end
      end)
    end)
    self.m_infoButton.gameObject:SetActive(true)
  else
    self.m_infoButton.gameObject:SetActive(false)
  end
end

IAPRewardItem = setmetatable({infoIapMode = true}, RewardItemWithItemTipButton)
IAPRewardItem.__index = IAPRewardItem
