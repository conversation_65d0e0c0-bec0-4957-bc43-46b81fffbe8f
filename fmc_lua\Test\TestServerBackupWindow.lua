TestServerBackupWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow + 1
}, BaseWindow)
TestServerBackupWindow.__index = TestServerBackupWindow

function TestServerBackupWindow:Init()
  self.m_cells = {}
  self.m_schemaInput.text = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema)
  self:OnClickRefresh()
end

function TestServerBackupWindow:IsLoadingData()
  return self.m_bIsLoadingData
end

function TestServerBackupWindow:OnClickRefresh()
  if self:IsLoadingData() then
    return
  end
  self.m_contentRectTrans.gameObject:SetActive(false)
  self.m_initializeText.gameObject:SetActive(true)
  self.m_initializeText.text = "加载中..."
  self.m_bIsLoadingData = true
  local cellObj
  local schema = self.m_schemaInput.text
  TestMessage.GetServerBackupData(schema, function(success, arrList)
    self.m_contentRectTrans.gameObject:SetActive(true)
    self.m_bIsLoadingData = false
    if success then
      self.m_initializeText.gameObject:SetActive(#arrList == 0)
      if #arrList == 0 then
        self.m_initializeText.text = "云端数据为空"
      end
      table.sort(arrList)
      for i, name in ipairs(arrList) do
        if self.m_cells[i] ~= nil then
          self.m_cells[i]:UpdateContent(schema, name)
        else
          self.m_cells[i] = self:_CreateNewCell(schema, name)
        end
      end
      for i = #arrList + 1, #self.m_cells do
        self.m_cells[i].gameObject:RemoveSelf()
        self.m_cells[i] = nil
      end
      GM.UIManager:ShowPrompt("获取云端数据成功")
    else
      self.m_initializeText.gameObject:SetActive(false)
      GM.UIManager:ShowPrompt("获取云端数据失败")
    end
  end)
end

function TestServerBackupWindow:OnClickSave()
  if self.m_bIsLoadingData then
    return
  end
  if self.m_nameInput.text == "" then
    GM.UIManager:ShowPrompt("保存名称不能为空")
    return
  end
  local desc = self:_GetWholeDescText(self.m_nameInput.text)
  local schema = self.m_schemaInput.text
  GM.TestModel:SaveBackupDataToServer(schema, desc, function(success)
    if success then
      GM.UIManager:ShowPrompt("上传成功")
      self.m_cells[#self.m_cells] = self:_CreateNewCell(schema, desc)
    else
      GM.UIManager:ShowPrompt("上传失败")
    end
  end)
end

function TestServerBackupWindow:_CreateNewCell(schema, desc)
  local cellObj = GameObject.Instantiate(self.m_cellOrigin, self.m_contentRectTrans)
  cellObj:SetActive(true)
  local cell = cellObj:GetLuaTable()
  cell:Init(self, schema, desc)
  return cell
end

function TestServerBackupWindow:_GetWholeDescText(desc)
  return "【" .. desc .. "】\n" .. GameConfig.GetCurrentVersion() .. " - " .. os.date("%Y/%m/%d-%H:%M:%S", GM.GameModel:GetServerTime())
end

TestServerBackupCell = {}
TestServerBackupCell.__index = TestServerBackupCell

function TestServerBackupCell:Init(window, schema, desc)
  self.m_window = window
  self.m_schema = schema
  self.m_desc = desc
  self.m_dataText.text = desc
end

function TestServerBackupCell:UpdateContent(schema, desc)
  self.m_schema = schema
  self.m_desc = desc
  self.m_dataText.text = desc
end

function TestServerBackupCell:OnClickApply()
  if self.m_window:IsLoadingData() then
    return
  end
  GM.TestModel:UseServerBackupData(self.m_schema, self.m_desc, function(success)
    if success then
      GM.TestModel:ClearData(false)
      GM:RestartGame(ERestartType.Normal, EBIProjectType.RestartGameAction.Test)
    else
      GM.UIManager:ShowPrompt("应用失败")
    end
  end)
end

function TestServerBackupCell:OnClickClean()
  if self.m_window:IsLoadingData() then
    return
  end
  GM.TestModel:RemoveServerBackupData(self.m_schema, self.m_desc, function(success)
    if success then
      GM.UIManager:ShowPrompt("移除成功")
      self.gameObject:RemoveSelf()
    else
      GM.UIManager:ShowPrompt("移除失败")
    end
  end)
end
