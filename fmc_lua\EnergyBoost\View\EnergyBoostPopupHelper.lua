EnergyBoostPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
EnergyBoostPopupHelper.__index = EnergyBoostPopupHelper

function EnergyBoostPopupHelper.Create()
  local helper = setmetatable({}, EnergyBoostPopupHelper)
  helper:Init()
  return helper
end

function EnergyBoostPopupHelper:Init()
  BasePopupHelper.Init(self)
  EventDispatcher.AddListener(EEventType.EnergyBoostModeChanged, self, self._OnEnergyBoostModeChanged)
end

function EnergyBoostPopupHelper:_OnEnergyBoostModeChanged()
  self:SetNeedCheckPopup(true)
end

function EnergyBoostPopupHelper:CheckPopup()
  local openState = GM.MiscModel:GetEnergyBoostWindowOpenStateInNumber()
  if GM.EnergyBoostModel:IsEnergyBoostConfigOn() then
    local eTriggerType = GM.EnergyBoostModel:GetTriggerBoostType()
    if openState ~= eTriggerType then
      GM.MiscModel:SetEnergyBoostWindowOpenState(eTriggerType)
      return UIPrefabConfigName.EnergyBoostSettingWindow, {true}
    end
  elseif openState > EEnergyBoostType.None then
    GM.MiscModel:SetEnergyBoostWindowOpenState(EEnergyBoostType.None)
  end
  return nil
end
