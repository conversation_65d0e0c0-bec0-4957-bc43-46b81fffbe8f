TestCommandMatchList = setmetatable({}, TestMatchStrList)
TestCommandMatchList.__index = TestCommandMatchList

function TestMatchStrList:Update()
  if Input.GetKeyDown(KeyCode.UpArrow) then
    if self.selectIdx == 1 then
      return
    end
    self.selectIdx = self.selectIdx - 1
    self.m_matchListView:MovePanelToItemIndex(self.selectIdx - 1)
  end
  if Input.GetKeyDown(KeyCode.DownArrow) then
    if self.selectIdx == #self.m_matchList then
      return
    end
    self.selectIdx = self.selectIdx + 1
    self.m_matchListView:MovePanelToItemIndex(self.selectIdx - 1)
  end
  if Input.GetKeyDown(KeyCode.Tab) then
    self:OnCellClick(self.m_matchList[self.selectIdx])
  end
end

TestCommandMatchListCell = setmetatable({}, TestMatchListCell)
TestCommandMatchListCell.__index = TestCommandMatchListCell

function TestCommandMatchListCell:Init(showStr, parent, idx)
  TestMatchListCell.Init(self, showStr, parent, idx)
  local split = StringUtil.Split(showStr, "##")
  self.m_btnText.text = split[1]
  self.m_descText.text = split[2]
end
