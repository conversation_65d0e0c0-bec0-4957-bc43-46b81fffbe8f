MultiTierBundleWindow = setmetatable({}, BundleBaseWindow)
MultiTierBundleWindow.__index = MultiTierBundleWindow

function MultiTierBundleWindow:Init(bundleType, dataGroup, bUserClick, eTriggerType)
  BundleBaseWindow.Init(self, bundleType, dataGroup, bUserClick, eTriggerType)
  self.m_cellContainer:Init(self.m_model, self.m_model:GetCurBundleDatas(dataGroup), self)
  local sortingOrder = self:GetSortingOrder()
  self.m_spriteMask.frontSortingOrder = sortingOrder + 3
  self.m_spriteMask.backSortingOrder = sortingOrder
  self:PlayEnterAnim()
  self.m_bNeedShowEnergyRestoreTime = not GM.EnergyModel:IsEnergyFull(EnergyType.Main)
  UIUtil.SetActive(self.m_bottomDescText.gameObject, self.m_bNeedShowEnergyRestoreTime)
  self:UpdatePerSecond()
end

function MultiTierBundleWindow:OnD<PERSON>roy()
  BundleBaseWindow.OnDestroy(self)
  if not Table.IsEmpty(self.m_arrSeq) then
    for _, seq in ipairs(self.m_arrSeq) do
      seq:Kill()
    end
    self.m_arrSeq = nil
  end
end

function MultiTierBundleWindow:PlayEnterAnim()
  self.m_titleSpine:Initialize(false)
  self.m_bgSpine:Initialize(false)
  self.m_lightSpineAnim:Initialize(false)
  self.m_rootCanvasGroup.alpha = 0
  self.m_closeCanvasGroup.alpha = 0
  self.m_dealCanvasGroup.alpha = 0
  self.m_titleTextCanvasGroup.alpha = 0
  self.m_countdownCanvasGroup.alpha = 0
  self.m_bottomCanvasGroup.alpha = 0
  self.m_cellContainer:HideAllCell()
  UIUtil.SetActive(self.m_titleEffect.gameObject, false)
  UIUtil.SetActive(self.m_bestDealLoopEffectGo, false)
  self.m_arrSeq = {}
  local windowOpenTime = 0.15
  DelayExecuteFuncInView(function()
    self.m_rootCanvasGroup.alpha = 1
    self.m_titleSpine.AnimationState:SetAnimation(0, "appear", false)
    self.m_titleSpine.AnimationState:AddAnimation(0, "idle", true, 0)
    self.m_bgSpine.AnimationState:SetAnimation(0, "appear", false)
    self.m_bgSpine.AnimationState:AddAnimation(0, "idle", true, 0)
    self.m_lightSpineAnim.AnimationState:SetAnimation(0, "appear", false)
    self.m_lightSpineAnim.AnimationState:AddAnimation(0, "idle", true, 0)
    self.m_energyLightEffect:Play()
    table.insert(self.m_arrSeq, self.m_titleTextCanvasGroup:DOFade(1, 0.2):SetDelay(0.2))
    table.insert(self.m_arrSeq, self.m_dealCanvasGroup:DOFade(1, 0.2):SetDelay(1))
    table.insert(self.m_arrSeq, self.m_closeCanvasGroup:DOFade(1, 0.2))
    table.insert(self.m_arrSeq, self.m_countdownCanvasGroup:DOFade(1, 0.2):SetDelay(0.2))
    table.insert(self.m_arrSeq, self.m_bottomCanvasGroup:DOFade(1, 0.2):SetDelay(0.2))
    self.m_cellContainer:PlayShowCellAnim(0.2, 0.1)
    self.m_boomEffect:Play()
    UIUtil.SetActive(self.m_titleEffect.gameObject, true)
    self.m_titleEffect:Play()
    self.m_titleBoomEffect:Play()
    self.m_bestDealEffect:Play()
  end, windowOpenTime, self, true)
  DelayExecuteFuncInView(function()
    UIUtil.SetActive(self.m_bestDealLoopEffectGo, true)
  end, windowOpenTime + 0.93, self)
end

function MultiTierBundleWindow:GetPurchaseIds()
  local arrBundleDatas = self.m_model:GetCurBundleDatas(self.m_dataGroup)
  local bundleIds = {}
  for _, data in ipairs(arrBundleDatas or {}) do
    table.insert(bundleIds, data:GetBundleId())
  end
  return bundleIds
end

function MultiTierBundleWindow:UpdatePerSecond()
  if self.m_model ~= nil and self.m_dataGroup ~= nil then
    local restDuration = self.m_model:GetRestDuration(self.m_dataGroup)
    if 0 < restDuration then
      self.m_countdownText.text = TimeUtil.ParseTimeDescription(restDuration, 2, false, false)
    else
      UIUtil.SetActive(self.m_countdownGo, false)
    end
  end
  local energyDuration = GM.EnergyModel:GetEnergyFullDuration(EnergyType.Main) or 0
  local param1 = TimeUtil.ToMSOrHMS(math.max(0, energyDuration))
  local param2 = string.format("<color=#ffc80a>%d</color><sprite=\"%s\" name=\"%s\">", EnergyModel.MaxEnergy, ImageFileConfigName.icon_energy, ImageFileConfigName.icon_energy)
  self.m_bottomDescText.text = GM.GameTextModel:GetText("energy_multitier_desc3", param1, param2)
  if self.m_bNeedShowEnergyRestoreTime and energyDuration <= 0 then
    self:Close()
    return
  end
end

MultiTierBundleCellContainer = {}
MultiTierBundleCellContainer.__index = MultiTierBundleCellContainer

function MultiTierBundleCellContainer:Init(model, arrBundleDatas, parent)
  Log.Assert(#arrBundleDatas == 3, "升降挡礼包必须至少有三个生效的购买项")
  self.m_model = model
  self.m_arrBundleDatas = arrBundleDatas
  self.m_parent = parent
  local tbCell
  for i = 1, 3 do
    tbCell = self:_GetCell(i)
    if arrBundleDatas[i] ~= nil and tbCell ~= nil then
      tbCell:Init(model, arrBundleDatas[i], parent)
    end
  end
end

function MultiTierBundleCellContainer:OnDestroy()
end

function MultiTierBundleCellContainer:_GetCell(i)
  return self["m_bundleCell" .. i]
end

function MultiTierBundleCellContainer:HideAllCell()
  local tbCell
  for i = 1, 3 do
    tbCell = self:_GetCell(i)
    if tbCell ~= nil then
      tbCell:Hide()
    end
  end
end

function MultiTierBundleCellContainer:PlayShowCellAnim(delay, interval)
  local tbCell
  for i = 1, 3 do
    tbCell = self:_GetCell(i)
    if tbCell ~= nil then
      tbCell:Show(delay + (i - 1) * interval)
    end
  end
end

MultiTierBundleCell = {}
MultiTierBundleCell.__index = MultiTierBundleCell

function MultiTierBundleCell:Init(model, bundleData, window)
  self.m_model = model
  self.m_data = bundleData
  self.m_window = window
  local rewards = bundleData:GetGoods()
  self.m_rewardContent:Init(rewards)
  self.m_discountTag.text = GM.GameTextModel:GetText("energy_multitier_desc1", bundleData:GetDiscountTag())
  self.m_originPriceText.text = GM.GameTextModel:GetText("energy_multitier_desc2", self:GetOriginPriceText())
  local clickCallback = function()
    self.m_model:BuyBundle(self.m_data, function(rewards)
      GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, rewards, "rewards_window_title_shop", true)
      if self.m_window ~= nil and self.m_window.gameObject ~= nil and not self.m_window.gameObject:IsNull() and self.m_window.Close ~= nil then
        self.m_window:Close()
      end
    end)
  end
  self.m_iapButton:Init(GM.InAppPurchaseModel:GetLocalizedPrice(self.m_data:GetPurchaseId()), clickCallback)
end

function MultiTierBundleCell:OnDestroy()
  if self.m_showSeq ~= nil then
    self.m_showSeq:Kill()
    self.m_showSeq = nil
  end
end

function MultiTierBundleCell:GetOriginPriceText()
  return GM.InAppPurchaseModel:GetLocalizedOriginPrice(self.m_data:GetPurchaseId(), self.m_data.originPrice)
end

function MultiTierBundleCell:Hide()
  self.m_canvasGroup.alpha = 0
end

function MultiTierBundleCell:Show(delay)
  self.m_showSeq = self.m_canvasGroup:DOFade(1, 0.2):SetDelay(delay)
end
