TutorialExecuter = {AlwaysStrongTutorial = false, AddToAutoPopup = true}
TutorialExecuter.__index = TutorialExecuter
TutorialExecuter.StepStart = "0"
TutorialExecuter.StepEnd = "99"

function TutorialExecuter.CreateExecuter(template, tutorialId, ongoingDatas)
  local executer = setmetatable({}, template)
  executer:_Init(tutorialId, ongoingDatas)
  return executer
end

function TutorialExecuter:_Init(tutorialId, ongoingDatas)
  self.m_model = GM.TutorialModel
  self.m_tutorialId = tutorialId
  self:_ParseDatasStr(ongoingDatas)
  if self.AlwaysStrongTutorial then
    EventDispatcher.DispatchEvent(EEventType.StrongTutorialStart)
  end
  if self.AddToAutoPopup then
    GM.TutorialModel:AddAutoPopup(self)
  end
end

function TutorialExecuter:_ParseDatasStr(ongoingDatas)
  self.m_strOngoingDatas = ongoingDatas
end

function TutorialExecuter:_ToDatasStr()
  return self.m_strOngoingDatas
end

function TutorialExecuter:OnStart()
end

function TutorialExecuter:OnRemove()
  EventDispatcher.RemoveTarget(self)
  GM.TutorialModel:RemoveAutoPopup(self)
end

function TutorialExecuter:_SaveOngoingDatas()
  self.m_model:UpdateOngoingDatas(self.m_tutorialId, self:_ToDatasStr())
end

function TutorialExecuter:GetTutorialId()
  return self.m_tutorialId
end

function TutorialExecuter:Finish(gesture, arrow)
  if TutorialHelper.GetSceneView() then
    TutorialHelper.FinishTutorial(gesture, arrow)
  end
  self.m_model:FinishTutorial(self:GetTutorialId())
end

function TutorialExecuter:LogTutorialStepFinish(strInfo)
  GM.BIManager:LogTutorialStepFinish(self.m_tutorialId, strInfo)
end

function TutorialExecuter:IsInStrongTutorial()
  return self.AlwaysStrongTutorial or self.m_bInStrongTutorial
end

function TutorialExecuter:SetStrongTutorial(enable)
  self.m_bInStrongTutorial = enable
  if enable then
    EventDispatcher.DispatchEvent(EEventType.StrongTutorialStart)
  end
end

function TutorialExecuter:CheckOtherTutorialInStrong()
  if not self:IsInStrongTutorial() and GM.TutorialModel:HasAnyStrongTutorialOngoing() then
    self.m_model:FinishTutorial(self:GetTutorialId())
    if GameConfig.IsTestMode() then
      GM.UIManager:ShowTestPrompt("引导：" .. self:GetTutorialId() .. "因其他强引导存在而终止")
    end
    return true
  end
  return false
end

function TutorialExecuter:TryStartTutorial()
  if self.AddToAutoPopup then
    Log.Assert(false, "override in subClass")
  end
end
