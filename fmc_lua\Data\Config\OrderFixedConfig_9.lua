return {
  {
    Id = "90010",
    GroupId = 1,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "90020",
    GroupId = 1,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90030",
    GroupId = 1,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90040",
    GroupId = 1,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_9saus_4", Count = 1}
  },
  {
    Id = "90050",
    GroupId = 1,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90060",
    GroupId = 1,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    }
  },
  {
    Id = "90070",
    GroupId = 1,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e4friedmt_12",
      Count = 1
    }
  },
  {
    Id = "90080",
    GroupId = 2,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90090",
    GroupId = 2,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_9e5fd_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90100",
    GroupId = 2,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_6",
      Count = 1
    }
  },
  {
    Id = "90110",
    GroupId = 2,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    }
  },
  {
    Id = "90120",
    GroupId = 2,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90130",
    GroupId = 2,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e1icytre_4",
      Count = 1
    }
  },
  {
    Id = "90140",
    GroupId = 2,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "90150",
    GroupId = 3,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e6cockt_7",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9saus_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90160",
    GroupId = 3,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "90170",
    GroupId = 3,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90180",
    GroupId = 3,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_16",
      Count = 1
    }
  },
  {
    Id = "90190",
    GroupId = 3,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90200",
    GroupId = 3,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e2sf_24",
      Count = 1
    }
  },
  {
    Id = "90210",
    GroupId = 3,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfr_1",
      Count = 1
    }
  },
  {
    Id = "90220",
    GroupId = 4,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillve_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_3",
      Count = 1
    }
  },
  {
    Id = "90230",
    GroupId = 4,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_9e2saus_14",
      Count = 1
    }
  },
  {
    Id = "90240",
    GroupId = 4,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_9saus_8", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90250",
    GroupId = 4,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_7", Count = 1}
  },
  {
    Id = "90260",
    GroupId = 4,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90270",
    GroupId = 4,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90280",
    GroupId = 4,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_7",
      Count = 1
    }
  },
  {
    Id = "90290",
    GroupId = 5,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_fd_14", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90300",
    GroupId = 5,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    }
  },
  {
    Id = "90310",
    GroupId = 5,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1icytre_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90320",
    GroupId = 5,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {Type = "ds_9saus_2", Count = 1}
  },
  {
    Id = "90330",
    GroupId = 5,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "90340",
    GroupId = 5,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90350",
    GroupId = 5,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "90360",
    GroupId = 6,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "90370",
    GroupId = 6,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "90380",
    GroupId = 6,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6nibble_6",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90390",
    GroupId = 6,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1saus_13",
      Count = 1
    }
  },
  {
    Id = "90400",
    GroupId = 6,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "90410",
    GroupId = 6,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90420",
    GroupId = 6,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "90430",
    GroupId = 7,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90440",
    GroupId = 7,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1},
    Requirement_2 = {
      Type = "ds_e1icytre_2",
      Count = 1
    }
  },
  {
    Id = "90450",
    GroupId = 7,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_12",
      Count = 1
    },
    Requirement_2 = {Type = "it_7_2_5", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90460",
    GroupId = 7,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {Type = "ds_fd_16", Count = 1}
  },
  {
    Id = "90470",
    GroupId = 7,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_friedmt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4saus_19",
      Count = 1
    }
  },
  {
    Id = "90480",
    GroupId = 7,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90490",
    GroupId = 7,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "90500",
    GroupId = 8,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90510",
    GroupId = 8,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "90520",
    GroupId = 8,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_6", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90530",
    GroupId = 8,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_9e2saus_20",
      Count = 1
    }
  },
  {
    Id = "90540",
    GroupId = 8,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_7e6tapas_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90550",
    GroupId = 8,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "90560",
    GroupId = 8,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillmt_2",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "90570",
    GroupId = 9,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_6",
      Count = 1
    }
  },
  {
    Id = "90580",
    GroupId = 9,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_9saus_3", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90590",
    GroupId = 9,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90600",
    GroupId = 9,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6saus_22",
      Count = 1
    }
  },
  {
    Id = "90610",
    GroupId = 9,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_18",
      Count = 1
    }
  },
  {
    Id = "90620",
    GroupId = 9,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9saus_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90630",
    GroupId = 9,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_12",
      Count = 1
    }
  },
  {
    Id = "90640",
    GroupId = 10,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_9e6assort_5",
      Count = 1
    }
  },
  {
    Id = "90650",
    GroupId = 10,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_2",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9e5mt_9", Count = 1}
  },
  {
    Id = "90660",
    GroupId = 10,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90670",
    GroupId = 10,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_9e5mt_10",
      Count = 1
    }
  },
  {
    Id = "90680",
    GroupId = 10,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90690",
    GroupId = 10,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_16",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillmt_11",
      Count = 1
    }
  },
  {
    Id = "90700",
    GroupId = 10,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_chopfs_1",
      Count = 1
    }
  },
  {
    Id = "90710",
    GroupId = 11,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "90720",
    GroupId = 11,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e2sf_24",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90730",
    GroupId = 11,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_9saus_5", Count = 1}
  },
  {
    Id = "90740",
    GroupId = 11,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90750",
    GroupId = 11,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_15",
      Count = 1
    }
  },
  {
    Id = "90760",
    GroupId = 11,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "it_4_2_6", Count = 1}
  },
  {
    Id = "90770",
    GroupId = 11,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillsf_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfru_1",
      Count = 1
    }
  },
  {
    Id = "90780",
    GroupId = 12,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillsf_4",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90790",
    GroupId = 12,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e5fd_22",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_7", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90800",
    GroupId = 12,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "90810",
    GroupId = 12,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "90820",
    GroupId = 12,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_fd_8", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1saus_18",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90830",
    GroupId = 12,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "90840",
    GroupId = 12,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_9e2saus_12",
      Count = 1
    }
  },
  {
    Id = "90850",
    GroupId = 13,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90860",
    GroupId = 13,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6soup_11",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90870",
    GroupId = 13,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6nibble_1",
      Count = 1
    }
  },
  {
    Id = "90880",
    GroupId = 13,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_19",
      Count = 1
    }
  },
  {
    Id = "90890",
    GroupId = 13,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1saus_13",
      Count = 1
    }
  },
  {
    Id = "90900",
    GroupId = 13,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_7e5mt_6", Count = 1},
    Requirement_2 = {
      Type = "ds_9e5saus_25",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90910",
    GroupId = 13,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "90920",
    GroupId = 14,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_7e6assort_4",
      Count = 1
    }
  },
  {
    Id = "90930",
    GroupId = 14,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90940",
    GroupId = 14,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_5", Count = 1},
    Requirement_2 = {Type = "ds_e4sf_12", Count = 1}
  },
  {
    Id = "90950",
    GroupId = 14,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e2saus_10",
      Count = 1
    },
    Requirement_2 = {Type = "it_2_1_6", Count = 1}
  },
  {
    Id = "90960",
    GroupId = 14,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "90970",
    GroupId = 14,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "90980",
    GroupId = 14,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "90990",
    GroupId = 15,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6dst_7",
      Count = 1
    }
  },
  {
    Id = "91000",
    GroupId = 15,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91010",
    GroupId = 15,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "91020",
    GroupId = 15,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6nibble_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "91030",
    GroupId = 15,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_16",
      Count = 1
    }
  },
  {
    Id = "91040",
    GroupId = 15,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedve_1",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91050",
    GroupId = 15,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1sala_6",
      Count = 1
    }
  },
  {
    Id = "91060",
    GroupId = 16,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_2", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_4",
      Count = 1
    }
  },
  {
    Id = "91070",
    GroupId = 16,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9saus_7", Count = 1}
  },
  {
    Id = "91080",
    GroupId = 16,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_mixdrk_9",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91090",
    GroupId = 16,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4saus_23",
      Count = 1
    }
  },
  {
    Id = "91100",
    GroupId = 16,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6soup_5",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "91110",
    GroupId = 16,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_fd_18", Count = 1},
    Requirement_2 = {Type = "ds_juice_9", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91120",
    GroupId = 16,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e2sf_24",
      Count = 1
    }
  },
  {
    Id = "91130",
    GroupId = 17,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "91140",
    GroupId = 17,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91150",
    GroupId = 17,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillmt_9",
      Count = 1
    }
  },
  {
    Id = "91160",
    GroupId = 17,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "91170",
    GroupId = 17,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_9e2sf_24",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91180",
    GroupId = 17,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6assort_5",
      Count = 1
    }
  },
  {
    Id = "91190",
    GroupId = 17,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_9e6soup_12",
      Count = 1
    }
  },
  {
    Id = "91200",
    GroupId = 18,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillve_2",
      Count = 1
    }
  },
  {
    Id = "91210",
    GroupId = 18,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfru_1",
      Count = 1
    }
  },
  {
    Id = "91220",
    GroupId = 18,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e2mt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91230",
    GroupId = 18,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    }
  },
  {
    Id = "91240",
    GroupId = 18,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "91250",
    GroupId = 18,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e4saus_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91260",
    GroupId = 18,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "91270",
    GroupId = 19,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_fd_16", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "91280",
    GroupId = 19,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_7e4semi_7",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e2saus_17",
      Count = 1
    }
  },
  {
    Id = "91290",
    GroupId = 19,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91300",
    GroupId = 19,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_chopfr_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "91310",
    GroupId = 19,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1saus_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91320",
    GroupId = 19,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "91330",
    GroupId = 19,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_9e5mt_8", Count = 1},
    Requirement_2 = {
      Type = "ds_9e6soup_9",
      Count = 1
    }
  },
  {
    Id = "91340",
    GroupId = 20,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "91350",
    GroupId = 20,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_mixdrk_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91360",
    GroupId = 20,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "91370",
    GroupId = 20,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_9", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    }
  },
  {
    Id = "91380",
    GroupId = 20,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_5", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_8",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91390",
    GroupId = 20,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1saus_24",
      Count = 1
    }
  },
  {
    Id = "91400",
    GroupId = 20,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_7e5mt_6", Count = 1}
  },
  {
    Id = "91410",
    GroupId = 21,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    }
  },
  {
    Id = "91420",
    GroupId = 21,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e4saus_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91430",
    GroupId = 21,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6saus_22",
      Count = 1
    }
  },
  {
    Id = "91440",
    GroupId = 21,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_fd_11", Count = 1}
  },
  {
    Id = "91450",
    GroupId = 21,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_6e4stewmt_2",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91460",
    GroupId = 21,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1saus_18",
      Count = 1
    }
  },
  {
    Id = "91470",
    GroupId = 21,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_7e2sf_21",
      Count = 1
    }
  },
  {
    Id = "91480",
    GroupId = 22,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e4friedmt_12",
      Count = 1
    }
  },
  {
    Id = "91490",
    GroupId = 22,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e4friedmt_6",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91500",
    GroupId = 22,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {Type = "ds_9saus_5", Count = 1}
  },
  {
    Id = "91510",
    GroupId = 22,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "91520",
    GroupId = 22,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_dst_1", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91530",
    GroupId = 22,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    }
  },
  {
    Id = "91540",
    GroupId = 22,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "91550",
    GroupId = 23,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_4",
      Count = 1
    }
  },
  {
    Id = "91560",
    GroupId = 23,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_10",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_14", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91570",
    GroupId = 23,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e2saus_12",
      Count = 1
    }
  },
  {
    Id = "91580",
    GroupId = 23,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_6", Count = 1},
    Requirement_2 = {
      Type = "ds_9e5fd_23",
      Count = 1
    }
  },
  {
    Id = "91590",
    GroupId = 23,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e6soup_10",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91600",
    GroupId = 23,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_4", Count = 1}
  },
  {
    Id = "91610",
    GroupId = 23,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedsf_4",
      Count = 1
    }
  },
  {
    Id = "91620",
    GroupId = 24,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "91630",
    GroupId = 24,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9saus_9", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91640",
    GroupId = 24,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_e1cockt_4",
      Count = 1
    }
  },
  {
    Id = "91650",
    GroupId = 24,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1saus_18",
      Count = 1
    }
  },
  {
    Id = "91660",
    GroupId = 24,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6soup_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91670",
    GroupId = 24,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "91680",
    GroupId = 24,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_14",
      Count = 1
    }
  },
  {
    Id = "91690",
    GroupId = 25,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_6", Count = 1}
  },
  {
    Id = "91700",
    GroupId = 25,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e6soup_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91710",
    GroupId = 25,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1assort_7",
      Count = 1
    }
  },
  {
    Id = "91720",
    GroupId = 25,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "91730",
    GroupId = 25,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_5", Count = 1},
    Requirement_2 = {
      Type = "ds_6e2flb_5",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91740",
    GroupId = 25,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {Type = "ds_e4sf_13", Count = 1}
  },
  {
    Id = "91750",
    GroupId = 25,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "91760",
    GroupId = 26,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_7", Count = 1},
    Requirement_2 = {Type = "ds_fd_18", Count = 1}
  },
  {
    Id = "91770",
    GroupId = 26,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_6", Count = 1},
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91780",
    GroupId = 26,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillmt_6",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    }
  },
  {
    Id = "91790",
    GroupId = 26,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e6assort_5",
      Count = 1
    }
  },
  {
    Id = "91800",
    GroupId = 26,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_mixdrk_8",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91810",
    GroupId = 26,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_6e5flb_4",
      Count = 1
    }
  },
  {
    Id = "91820",
    GroupId = 26,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_4", Count = 1},
    Requirement_2 = {
      Type = "ds_7e1tapas_19",
      Count = 1
    }
  },
  {
    Id = "91830",
    GroupId = 27,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_14",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_chopfru_1",
      Count = 1
    }
  },
  {
    Id = "91840",
    GroupId = 27,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_12",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91850",
    GroupId = 27,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_mixdrk_4",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4friedmt_13",
      Count = 1
    }
  },
  {
    Id = "91860",
    GroupId = 27,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e6soup_11",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9e5mt_8", Count = 1}
  },
  {
    Id = "91870",
    GroupId = 27,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_8", Count = 1},
    Requirement_2 = {Type = "ds_fd_11", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91880",
    GroupId = 27,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e6sala_5",
      Count = 1
    }
  },
  {
    Id = "91890",
    GroupId = 27,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "91900",
    GroupId = 28,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_7", Count = 1},
    Requirement_2 = {
      Type = "ds_friedsf_3",
      Count = 1
    }
  },
  {
    Id = "91910",
    GroupId = 28,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_1_8", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4saus_16",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91920",
    GroupId = 28,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6soup_6",
      Count = 1
    }
  },
  {
    Id = "91930",
    GroupId = 28,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_1_9", Count = 1},
    Requirement_2 = {
      Type = "ds_9e4friedmt_16",
      Count = 1
    }
  },
  {
    Id = "91940",
    GroupId = 28,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_fd_19", Count = 1},
    Requirement_2 = {Type = "clean", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91950",
    GroupId = 28,
    ChapterId = 9,
    Requirement_1 = {Type = "it_2_2_6", Count = 1},
    Requirement_2 = {Type = "it_4_2_4", Count = 1}
  },
  {
    Id = "91960",
    GroupId = 28,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_13",
      Count = 1
    }
  },
  {
    Id = "91970",
    GroupId = 29,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_8e6nibble_2",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "91980",
    GroupId = 29,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_6", Count = 1},
    Requirement_2 = {
      Type = "ds_9e2saus_14",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "91990",
    GroupId = 29,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_5",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e6tapas_17",
      Count = 1
    }
  },
  {
    Id = "92000",
    GroupId = 29,
    ChapterId = 9,
    Requirement_1 = {Type = "it_7_2_3", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1saus_15",
      Count = 1
    }
  },
  {
    Id = "92010",
    GroupId = 29,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillmt_9",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_7e2sf_21",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "92020",
    GroupId = 29,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_7",
      Count = 1
    }
  },
  {
    Id = "92030",
    GroupId = 29,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_friedsf_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e4saus_23",
      Count = 1
    }
  },
  {
    Id = "92040",
    GroupId = 30,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1hotdrk_1",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_friedmt_5",
      Count = 1
    }
  },
  {
    Id = "92050",
    GroupId = 30,
    ChapterId = 9,
    Requirement_1 = {Type = "ds_juice_8", Count = 1},
    Requirement_2 = {
      Type = "ds_9e1sala_7",
      Count = 1
    },
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "92060",
    GroupId = 30,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_11",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_grillsf_5",
      Count = 1
    }
  },
  {
    Id = "92070",
    GroupId = 30,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_e1cockt_8",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1assort_6",
      Count = 1
    }
  },
  {
    Id = "92080",
    GroupId = 30,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_chopfs_1",
      Count = 1
    },
    Requirement_2 = {Type = "ds_9saus_9", Count = 1},
    Rewards = {
      {Currency = "additem_1", Amount = 1}
    }
  },
  {
    Id = "92090",
    GroupId = 30,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_grillmt_7",
      Count = 1
    },
    Requirement_2 = {Type = "clean", Count = 1}
  },
  {
    Id = "92100",
    GroupId = 30,
    ChapterId = 9,
    Requirement_1 = {
      Type = "ds_9e1hotdrk_3",
      Count = 1
    },
    Requirement_2 = {
      Type = "ds_9e1saus_18",
      Count = 1
    }
  }
}
