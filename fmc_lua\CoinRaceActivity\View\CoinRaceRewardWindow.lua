CoinRaceRewardWindow = setmetatable({
  canCloseByAndroidBack = false,
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  hideHudAnchorType = {
    EHudAnchorType.All
  }
}, BaseWindow)
CoinRaceRewardWindow.__index = CoinRaceRewardWindow

function CoinRaceRewardWindow:Init(activityType, rank, rewards)
  Log.Assert(0 < rank and rank <= 3, "param rank is error")
  Log.Assert(not Table.IsEmpty(rewards), "param rewards is error")
  self.m_activityType = activityType
  self.m_rank = rank
  self.m_rewards = rewards
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  SpriteUtil.SetImage(self.m_boxImg, self.m_activityDefinition.CoinRaceBoxRankImgPrefix .. rank, true)
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.AutoPopup
  })
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.StateChanged, {
    obj = self,
    method = self.Close
  })
  local seq = DOTween.Sequence()
  seq:AppendCallback(function()
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxFirework)
  end)
  seq:AppendInterval(3)
  seq:SetLoops(-1)
  self.m_tween = seq
  self.m_userAvatar:SetAvatar(EAvatarFrame.Highlight, GM.UserProfileModel:GetIcon())
end

function CoinRaceRewardWindow:OnDestroy()
  BaseWindow.OnDestroy(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
  if self.m_tween then
    self.m_tween:Kill()
    self.m_tween = nil
  end
end

function CoinRaceRewardWindow:OnMaskClicked()
  self:Close()
  GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, self.m_rewards, "rewards_window_title_normal", true, nil, function()
    GM.UIManager:SetEventLock(true)
    DelayExecuteFunc(function()
      GM.UIManager:SetEventLock(false)
      local model = GM.ActivityManager:GetModel(self.m_activityType)
      if model:GetState() == ActivityState.Started and not model:HasFinishedAllRounds() then
        GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, true, true)
      end
    end, 1.5)
  end)
end
