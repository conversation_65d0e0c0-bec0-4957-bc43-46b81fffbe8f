ExtraBoardActivityHelpWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, ExtraBoardActivityBaseWindow)
ExtraBoardActivityHelpWindow.__index = ExtraBoardActivityHelpWindow

function ExtraBoardActivityHelpWindow:Init(activityType, bUserClick)
  ExtraBoardActivityBaseWindow.Init(self, activityType, bUserClick)
  local originIndex = 3
  for i = 1, 3 do
    SpriteUtil.SetImage(self["m_itemImg" .. i], GM.ItemDataModel:GetSpriteName(self.m_model:GetItemTypeByIndex(originIndex + i)), true)
  end
  for i = 4, 5 do
    SpriteUtil.SetImage(self["m_itemImg" .. i], GM.ItemDataModel:GetSpriteName(self.m_model:GetItemTypeByIndex(3)), true)
  end
  local rareChainId = self.m_model:GetRareItemChainId()
  local maxRareLevel = self.m_model:GetRareMaxLevel()
  local rareMaxLevelItemCode = rareChainId .. "_" .. maxRareLevel
  SpriteUtil.SetImage(self.m_itemImg6, GM.ItemDataModel:GetSpriteName(rareMaxLevelItemCode), true)
  local bShowEnergyGo = self.m_model:IsAcquireTypeValid(EActTokenAcquireType.CostEnergy) or self.m_model:IsAcquireTypeValid(EActTokenAcquireType.ProducerClick)
  UIUtil.SetActive(self.m_energyGo, bShowEnergyGo)
  local bShowOrderGo = self.m_model:IsAcquireTypeValid(EActTokenAcquireType.FinishOrder)
  UIUtil.SetActive(self.m_orderGo, bShowOrderGo)
end
