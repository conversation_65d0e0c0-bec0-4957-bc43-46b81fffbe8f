TestCommandView = setmetatable({
  eCloseType = EViewCloseType.Hide
}, BaseWindow)
TestCommandView.__index = TestCommandView

function TestCommandView:Init()
  self.m_InputFieldInpu.text = ""
  if TestCommandView.cacheStr then
    self.m_InputFieldInpu.text = TestCommandView.cacheStr
  end
  self.m_searchList = {}
  self:AddCommandList2Search()
  self.m_InputFieldInpu:ActivateInputField()
  self.m_InputFieldInpu.caretPosition = string.len(self.m_InputFieldInpu.text)
end

function TestCommandView:Update()
  if Input.GetKeyDown(KeyCode.Return) then
    self:OnExecuteBntClick()
  end
end

function TestCommandView:OnExecuteBntClick()
  if self:ExecuteCmd(self.m_InputFieldInpu.text) then
    TestCommandView.cacheStr = self.m_InputFieldInpu.text
    GM.UIManager:ShowPrompt("执行成功")
  end
end

function TestCommandView:OnInputChange()
  local bCmd = false
  for eCmd, cmdDef in pairs(ECommand) do
    if self.m_InputFieldInpu.text and string.find(self.m_InputFieldInpu.text, cmdDef.Cmd) then
      self:AddCommandContentList2Search(eCmd)
      bCmd = true
      break
    end
  end
  if not bCmd then
    self.m_searchList = {}
    self:AddCommandList2Search()
    self.m_curCmd = nil
  end
  self.m_MatchListLuaTable:UpdateListView(self.m_InputFieldInpu.text)
end

function TestCommandView:UpdateSearch()
  self.m_MatchListLuaTable:Init(self.m_searchList, function(str)
    local preList = StringUtil.Split(str, "##")
    str = preList[1]
    self.m_InputFieldInpu.text = str .. " "
    if not StringUtil.IsNilOrEmpty(str) then
      self.m_InputFieldInpu.caretPosition = string.len(self.m_InputFieldInpu.text)
    end
  end)
  self.m_MatchListLuaTable:UpdateListView(self.m_InputFieldInpu.text)
end

function TestCommandView:AddCommandList2Search()
  for _, cmdDef in pairs(ECommand) do
    self.m_searchList[#self.m_searchList + 1] = cmdDef.Cmd .. "##" .. cmdDef.Desc
  end
  self:UpdateSearch()
end

function TestCommandView:AddCommandContentList2Search(eCmd)
  if self.m_curCmd == eCmd then
    return
  end
  self.m_curCmd = eCmd
  self.m_searchList = {}
  if ECommand[eCmd].AddSearchListFunc then
    Table.ListAppend(self.m_searchList, ECommand[eCmd]:AddSearchListFunc())
  end
  self:UpdateSearch()
end

function TestCommandView:ExecuteCmd(cmdStr)
  if StringUtil.IsNilOrEmpty(cmdStr) then
    GM.UIManager:ShowPrompt("命令行为空，请输入命令")
    return
  end
  cmdStr = StringUtil.Trim(cmdStr)
  local list = StringUtil.Split(cmdStr, " ")
  local cmd = list[1]
  table.remove(list, 1)
  for _, cmdDef in pairs(ECommand) do
    if cmd == cmdDef.Cmd then
      return cmdDef:ExecuteFunc(list)
    end
  end
end
