ProgressActivityPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
ProgressActivityPopupHelper.__index = ProgressActivityPopupHelper

function ProgressActivityPopupHelper.Create()
  local helper = setmetatable({}, ProgressActivityPopupHelper)
  helper:Init()
  return helper
end

function ProgressActivityPopupHelper:Init()
  BasePopupHelper.Init(self)
  for _, definition in pairs(ProgressActivityDefinition) do
    EventDispatcher.AddListener(definition.StateChangedEvent, self, self._OnStateChanged)
    EventDispatcher.AddListener(definition.ScoreChangedEvent, self, self._OnScoreChanged)
  end
end

function ProgressActivityPopupHelper:_OnStateChanged()
  self:SetNeedCheckPopup(true)
end

function ProgressActivityPopupHelper:_OnScoreChanged()
  self:SetNeedCheckPopup(true)
end

function ProgressActivityPopupHelper:CheckPopup()
  for activityType, definition in pairs(ProgressActivityDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if state == ActivityState.Preparing then
      local curGameMode = GM.SceneManager:GetGameMode()
      if not model:HasWindowOpenedOnce(ActivityState.Preparing) and curGameMode == EGameMode.Main then
        return definition.NoticeWindowPrefabName, {activityType, true}
      end
    elseif state == ActivityState.Started then
      if not model:HasWindowOpenedOnce(ActivityState.Started) then
        return definition.MainWindowPrefabName, {activityType, true}
      end
    elseif state == ActivityState.Ended and model:HasWindowOpenedOnce(ActivityState.Started) and not model:HasWindowOpenedOnce(ActivityState.Ended) then
      local level = model:GetLevel()
      local levelConfig = model:GetLevelConfigs()[level]
      if levelConfig ~= nil then
        return definition.FinishFailWindowPrefabName, {activityType, true}, true
      end
    end
  end
end

ProgressActivityRewardPopupHelper = setmetatable({}, ProgressActivityPopupHelper)
ProgressActivityRewardPopupHelper.__index = ProgressActivityRewardPopupHelper

function ProgressActivityRewardPopupHelper.Create()
  local helper = setmetatable({}, ProgressActivityRewardPopupHelper)
  helper:Init()
  return helper
end

function ProgressActivityRewardPopupHelper:CheckPopup()
  for activityType, definition in pairs(ProgressActivityDefinition) do
    local model = GM.ActivityManager:GetModel(activityType)
    local state = model:GetState()
    if (state == ActivityState.Started or state == ActivityState.Ended) and model:CanAcquireReward() then
      return definition.MainWindowPrefabName, {activityType, true}
    end
  end
end
