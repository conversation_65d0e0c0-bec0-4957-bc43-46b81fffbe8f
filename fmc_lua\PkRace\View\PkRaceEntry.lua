PkRaceEntry = setmetatable({}, HudGeneralButton)
PkRaceEntry.__index = PkRaceEntry

function PkRaceEntry:Init(model)
  self.m_model = model
  if self.m_model == nil then
    return
  end
  self.m_activityType = self.m_model:GetType()
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  self.m_lastUpdateTime = 0
  self:_UpdateTokenText()
  self:UpdatePerSecond()
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.OpenMainWindow, {
    obj = self,
    method = self._UpdateTokenText
  })
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.ScoreChanged, {
    obj = self,
    method = self._UpdateTokenText
  })
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.StateChanged, {
    obj = self,
    method = self._UpdateTokenText
  })
end

function PkRaceEntry:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
  HudGeneralButton.OnDestroy(self)
end

function PkRaceEntry:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  elseif self.gameObject.activeSelf then
    self.gameObject:SetActive(false)
  end
  local serTime = GM.GameModel:GetServerTime()
  if serTime - self.m_lastUpdateTime > 120 then
    self.m_lastUpdateTime = serTime
    self:_UpdateRankText()
  end
end

function PkRaceEntry:OnBtnClicked()
  local state = self.m_model:GetState()
  if self.m_model:IsInRace() then
    self.m_model:TryOpenMainWindow()
  else
    GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, false, true)
  end
end

function PkRaceEntry:_UpdateTokenText()
  if self.m_model == nil or not self.m_model:HasNetwork() then
    return
  end
  self.m_exclamationGo:SetActive(self.m_model:CanShowExclaimation())
  if self.m_model:IsInRace() then
    local tokenNum = self.m_model:GetMyScore()
    local targetNum = self.m_model:GetTargetScore()
    self.m_tokenText.text = tokenNum .. "/" .. targetNum
  else
    self.m_tokenText.text = "0"
  end
  self:_UpdateRankText()
end

function PkRaceEntry:_UpdateRankText()
  if self.m_model == nil or not self.m_model:HasNetwork() then
    return
  end
  self.m_exclamationGo:SetActive(self.m_model:CanShowExclaimation())
  local rank = not self.m_model:IsInRace() and -1 or self.m_model:GetMyRank(true)
  UIUtil.SetActive(self.m_firstRankGo, rank == 1)
  UIUtil.SetActive(self.m_secondRankGo, rank == 2)
end
