SurpriseChestRewardBubble = {}
SurpriseChestRewardBubble.__index = SurpriseChestRewardBubble

function SurpriseChestRewardBubble:Show(activityType, activityDefinition, rewards, viewData)
  self.m_activityType = activityType
  self.m_activityDefinition = activityDefinition
  if self.m_rewardBubbleTween ~= nil then
    self.m_rewardBubbleTween:Kill()
    self.m_rewardBubbleTween = nil
  end
  self.m_currentSelected = CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject
  local spacingX = 50
  local rewardsCount = #rewards
  if rewardsCount == 4 then
    spacingX = 80
  elseif rewardsCount == 3 then
    spacingX = 110
  elseif rewardsCount == 2 then
    spacingX = 180
  end
  self.m_layout.spacing = Vector2(spacingX, 0)
  self.m_rewardContent:Init(rewards)
  viewData = viewData or {}
  if viewData.pos ~= nil then
    self.transform.position = viewData.pos
  end
  local bLeft = viewData.bLeft or false
  UIUtil.SetActive(self.m_leftArrowGo, bLeft)
  UIUtil.SetActive(self.m_rightArrowGo, not bLeft)
  local pivotX = bLeft and 0.25 or 0.75
  local originAnchorX = bLeft and 107 or 411
  self.m_bgRect.pivot = Vector2(pivotX, 1)
  UIUtil.SetAnchoredPosition(self.m_bgRect, originAnchorX)
  UIUtil.SetActive(self.m_bgRect.gameObject, true)
  self.m_bgRect:SetLocalScaleXY(0)
  local seq = DOTween.Sequence()
  seq:Append(self.m_bgRect:DOScale(1, 0.2):SetEase(Ease.OutBack))
  seq:AppendCallback(function()
    self.m_rewardBubbleTween = nil
  end)
  self.m_rewardBubbleTween = seq
  self.m_bShow = true
end

function SurpriseChestRewardBubble:Hide()
  if not self.m_bShow then
    return
  end
  if self.m_rewardBubbleTween ~= nil then
    self.m_rewardBubbleTween:Kill()
    self.m_rewardBubbleTween = nil
  end
  local seq = DOTween.Sequence()
  seq:Append(self.m_bgRect:DOScale(0, 0.1):SetEase(Ease.InBack))
  seq:AppendCallback(function()
    self.m_rewardBubbleTween = nil
    UIUtil.SetActive(self.m_bgRect.gameObject, false)
  end)
  self.m_rewardBubbleTween = seq
  self.m_bShow = false
end

function SurpriseChestRewardBubble:Update()
  if not self.m_bShow then
    return
  end
  if self.m_currentSelected == nil then
    return
  end
  local selectGo = CS.UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject
  if selectGo == self.m_tipBtnGo then
    return
  end
  if self.m_currentSelected == selectGo then
    return
  end
  if selectGo == nil or selectGo:IsNull() then
    self.m_currentSelected = nil
    self:Hide()
    return
  end
  local btnTb = selectGo:GetLuaTable()
  if btnTb ~= nil and btnTb.bRewardHelpBtn then
    return
  end
  self.m_currentSelected = nil
  self:Hide()
end

function SurpriseChestRewardBubble:OnHelpClick()
  GM.UIManager:OpenView(self.m_activityDefinition.HelpWindowPrefabName, self.m_activityType, true)
end
