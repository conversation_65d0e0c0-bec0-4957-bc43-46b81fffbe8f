EnergyHudButton = setmetatable({
  HitAudio = AudioFileConfigName.SfxEnergyCrush
}, HudPropertyButton)
EnergyHudButton.__index = EnergyHudButton

function EnergyHudButton:Awake()
  HudPropertyButton.Awake(self)
  HudPropertyButton.Init(self, EPropertyType.Energy)
  EventDispatcher.AddListener(EEventType.EnergyRestored, self, self.OnEnergyRestored)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnChangeGameMode)
  self:_CheckInfiniteState()
  self:UpdatePerSecond()
end

function EnergyHudButton:OnEnergyRestored(msg)
  if msg.type ~= self.ePropertyType then
    return
  end
  self:UpdateTextAnimation(self.ePropertyType, msg.count)
  self:UpdatePerSecond()
end

function EnergyHudButton:IconScaleAnimation(needEffect, checkType)
  HudPropertyButton.IconScaleAnimation(self, needEffect, checkType)
  self:_CheckInfiniteState()
  self:UpdatePerSecond()
end

function EnergyHudButton:_CheckInfiniteState()
  local energyType = EnergyModel.PropertyType2EnergyType(self.ePropertyType)
  if GM.EnergyModel:IsEnergyInfinite(energyType) then
    if not self.m_isInfinite then
      self.m_isInfinite = true
      self.m_valueText.gameObject:SetActive(false)
      self.m_inifiteImgGo:SetActive(true)
      self.m_countDownGo:SetActive(true)
      if not self._isShowCountDown then
        self._isShowCountDown = true
        self.m_countDownGo:SetActive(true)
      end
    end
    self.m_infiniteTime = GM.EnergyModel:GetEnergyInfiniteTime(energyType)
  end
end

function EnergyHudButton:UpdatePerSecond()
  local energyType = EnergyModel.PropertyType2EnergyType(self.ePropertyType)
  if GM.EnergyModel:IsEnergyInfinite(energyType) then
    if self.m_isInfinite and self.m_infiniteTime then
      self.m_countDownText.text = TimeUtil.ToMSOrHMS(math.max(0, (self.m_infiniteTime or 0) - GM.GameModel:GetServerTime()))
    end
  else
    if self.m_isInfinite then
      self.m_isInfinite = false
      self.m_valueText.gameObject:SetActive(true)
      self.m_inifiteImgGo:SetActive(false)
      self.m_infiniteTime = nil
    end
    if GM.EnergyModel:IsEnergyFull(energyType) then
      if self._isShowCountDown then
        self._isShowCountDown = false
        self.m_countDownGo:SetActive(false)
      end
    else
      if not self._isShowCountDown then
        self._isShowCountDown = true
        self.m_countDownGo:SetActive(true)
      end
      self.m_countDownText.text = TimeUtil.ToMSOrHMS(GM.EnergyModel:GetRestoreOneRestDuration(energyType))
    end
  end
end

function EnergyHudButton:OnClicked()
  local inShopWindow = GM.UIManager:IsViewExisting(UIPrefabConfigName.ShopWindow)
  GM.UIManager:CloseView(UIPrefabConfigName.ShopWindow)
  GM.ShopModel:OpenEnergyWindow(EnergyModel.PropertyType2EnergyType(self.ePropertyType), nil, inShopWindow)
end

function EnergyHudButton:_CheckPropertyType(checkType)
  return HudPropertyButton._CheckPropertyType(self, checkType) or EnergyModel.PropertyType2InfiniteEnergyType(checkType) ~= nil
end

function EnergyHudButton:_UpdatePropertyType()
  local formerPropertyType = self.ePropertyType
  self.ePropertyType = EPropertyType.Energy
  local curView = BoardViewHelper.GetActiveView()
  if curView ~= nil then
    self.ePropertyType = EPropertyType.Energy
  end
  if self.ePropertyType ~= formerPropertyType then
    self.m_propertyTypeChanged = true
    self:_UpdateContent()
    self:_CheckInfiniteState()
    self:UpdatePerSecond()
  end
end

function EnergyHudButton:OnChangeGameMode()
  self:_UpdatePropertyType()
  if self.m_propertyTypeChanged then
    self.m_propertyChangeEffect:Play()
  end
  self.m_propertyTypeChanged = false
end

function EnergyHudButton:_UpdateContent()
  SpriteUtil.SetImage(self.m_iconImg, self.ePropertyType == EPropertyType.EventEnergy and ImageFileConfigName.icon_energy_event or ImageFileConfigName.icon_energy)
  SpriteUtil.SetImage(self.m_boardImg, self.ePropertyType == EPropertyType.EventEnergy and ImageFileConfigName.hud_property_bg_event or ImageFileConfigName.hud_property_bg)
  self:SyncToModelValue()
end
