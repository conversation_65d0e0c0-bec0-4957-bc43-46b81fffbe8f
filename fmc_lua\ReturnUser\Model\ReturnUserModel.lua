ReturnUserModel = {}
ReturnUserModel.__index = ReturnUserModel
local DBColumnValue = DB_VALUE_KEY
local DBKey = {
  ExpiredTime = "expiredTime",
  ReturnReward = "returnReward",
  RewardExpiredTime = "rewardexpiredTime",
  RewardLeaveDay = "rewardLeaveDay",
  ForceRefreshOrder = "ForceRefreshOrder"
}

function ReturnUserModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.ReturnUser)
  EventDispatcher.AddListener(EEventType.EnterMainScene, self, self.OnEnterMainScene)
end

function ReturnUserModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function ReturnUserModel:OnEnterMainScene()
  self:TryForceRefreshOrder()
  self.m_bEnterMainScene = true
end

function ReturnUserModel:LoadServerConfig(tbLoginResp)
  self:_LoadReturnUserReward(tbLoginResp)
  local expiredTime = tbLoginResp.return_expired_time
  self.m_llExpiredTime = expiredTime
  if not self:IsOpen() or not expiredTime then
    self.m_dbTable:Remove(DBKey.ExpiredTime)
    self.m_dbTable:Remove(DBKey.ForceRefreshOrder)
    return
  end
  local savedExpiredTime = tonumber(self:GetExpiredTime())
  if savedExpiredTime ~= expiredTime then
    self.m_dbTable:Remove(DBKey.ForceRefreshOrder)
  end
  self:SaveExpiredTime(expiredTime)
  if self.m_bEnterMainScene then
    self:TryForceRefreshOrder()
  end
end

function ReturnUserModel:_LoadReturnUserReward(tbLoginResp)
  local expiredTime = tbLoginResp.return_expired_time
  local nLeaveTimeDay = tbLoginResp.lost_days
  if expiredTime == nil or nLeaveTimeDay == nil then
    return
  end
  local endTime = self:GetRewardExpiredTime()
  if endTime == expiredTime then
    return
  end
  self:SaveRewardExpiredTime(expiredTime, nLeaveTimeDay)
  self:SaveReturnReward(expiredTime)
end

function ReturnUserModel:IsOpen()
  return GM.ConfigModel:IsReturnUserRewardOpen()
end

function ReturnUserModel:IsInReturnRewardTime()
  if not self:IsOpen() then
    return false
  end
  local endTime = self:GetExpiredTime()
  if not endTime then
    return false
  end
  if endTime <= GM.GameModel:GetServerTime() then
    return false
  end
  return true
end

function ReturnUserModel:GetData()
  return self.m_dbTable
end

function ReturnUserModel:FromSyncData(dataArray)
  local bFind = false
  for _, str in ipairs(dataArray) do
    local datas = StringUtil.Split(str, ",")
    if datas ~= nil and datas[1] ~= nil and datas[2] ~= nil and datas[1] == DBKey.RewardExpiredTime then
      bFind = true
      local llDownLoadExpiredTime = tonumber(datas[2])
      if llDownLoadExpiredTime ~= nil and llDownLoadExpiredTime < self:GetRewardExpiredTime() then
        return
      end
    end
  end
  if bFind then
    self.m_dbTable:FromArr(dataArray)
  end
end

function ReturnUserModel:GetExpiredTime()
  return tonumber(self.m_dbTable:GetValue(DBKey.ExpiredTime, DBColumnValue))
end

function ReturnUserModel:SaveExpiredTime(time)
  return self.m_dbTable:Set(DBKey.ExpiredTime, DBColumnValue, tostring(time))
end

function ReturnUserModel:HasForceRefreshOrderFlag()
  return not StringUtil.IsNilOrEmpty(self.m_dbTable:GetValue(DBKey.ForceRefreshOrder, DBColumnValue))
end

function ReturnUserModel:SaveForceRefreshOrderFlag()
  return self.m_dbTable:Set(DBKey.ForceRefreshOrder, DBColumnValue, "1")
end

function ReturnUserModel:GetRewardExpiredTime()
  return tonumber(self.m_dbTable:GetValue(DBKey.RewardExpiredTime, DBColumnValue))
end

function ReturnUserModel:GetRewardLeaveDay()
  return tonumber(self.m_dbTable:GetValue(DBKey.RewardLeaveDay, DBColumnValue))
end

function ReturnUserModel:SaveRewardExpiredTime(time, nLeaveTimeDay)
  self.m_dbTable:Set(DBKey.RewardExpiredTime, DBColumnValue, tostring(time))
  self.m_dbTable:Set(DBKey.RewardLeaveDay, DBColumnValue, tostring(nLeaveTimeDay))
end

function ReturnUserModel:TryGetReturnReward()
  local endTime = self:GetRewardExpiredTime()
  if not endTime then
    return
  end
  if endTime < GM.GameModel:GetServerTime() then
    self:SetReturnRewardGot()
    return
  end
  local config = GM.ConfigModel:GetServerConfig(ServerConfigKey.ReturnUserRewards)
  if config == nil then
    return
  end
  local rewards = config[1].rewards
  if rewards == nil then
    return
  end
  if Table.IsEmpty(rewards) then
    Log.Error("回归奖励为空")
    return
  end
  local strTime = self.m_dbTable:GetValue(DBKey.ReturnReward, DBColumnValue)
  local llTime = 0
  if strTime ~= nil then
    llTime = tonumber(strTime)
  end
  if llTime ~= self:GetRewardExpiredTime() then
    return
  end
  self:SetReturnRewardGot()
  GM.BIManager:LogAction(EBIType.ReturnUserGetReward, self:GetRewardLeaveDay())
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.ReturnUserGetReward, EGameMode.Board, CacheItemType.Stack)
  return rewards
end

function ReturnUserModel:GetReturnRewardFlag()
  return self.m_dbTable:GetValue(DBKey.ReturnReward, DBColumnValue)
end

function ReturnUserModel:SaveReturnReward(erpiredTime)
  return self.m_dbTable:Set(DBKey.ReturnReward, DBColumnValue, tostring(erpiredTime))
end

function ReturnUserModel:SetReturnRewardGot()
  return self.m_dbTable:Set(DBKey.ReturnReward, DBColumnValue, "0")
end

function ReturnUserModel:TryForceRefreshOrder()
  if self:IsOpen() and self:IsInReturnRewardTime() and not self:HasForceRefreshOrderFlag() then
    self:SaveForceRefreshOrderFlag()
    local orderModel = GM.MainBoardModel:GetOrderModel()
  end
end
