SpreeLevelBubble = {}
SpreeLevelBubble.__index = SpreeLevelBubble

function SpreeLevelBubble:Init(activityType)
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self:UpdateContent()
  EventDispatcher.AddListener(EEventType.SpreeLevelExpChange, self, self.OnSpreeLevelExpChanged)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnChangeGameMode)
end

function SpreeLevelBubble:OnSpreeLevelExpChanged()
  if self.m_recordLevel ~= self.m_model:GetLevel() and not self.m_bLockBeforeLevelUp and GM.SceneManager:GetGameMode() == SpreeActivityDefinition[self.m_model:GetType()].GameMode then
    self.m_bLockBeforeLevelUp = true
    GM.UIManager:SetEventLock(true)
  end
  self.m_recordLevel = self.m_model:GetLevel()
end

function SpreeLevelBubble:OnChangeGameMode()
  if self.m_bLockBeforeLevelUp then
    self.m_bLockBeforeLevelUp = nil
    GM.UIManager:SetEventLock(false)
    local rewards = self.m_model:GetLevelRewards(self.m_model:GetLevel())
    local arrTargetGameMode = Table.ListRep(self.m_model:GetLevelRewardsTargetGameMode(self.m_model:GetLevel()), #rewards, false)
    RewardApi.AcquireRewardsInView(rewards, {
      eventLock = true,
      noDelayTime = true,
      targetGameMode = arrTargetGameMode
    })
  end
end

function SpreeLevelBubble:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  if self.m_bIsUping then
    GM.UIManager:SetEventLock(false)
    self.m_bIsUping = false
  end
end

function SpreeLevelBubble:UpdateContent()
  local level = self.m_model:GetLevel()
  self.m_recordLevel = level
  self.m_levelText.text = level
  local curExp = self.m_model:GetLevelExp()
  local config = self.m_model:GetLevelConfig()
  local targetExp = self.m_model:GetLevelUpCost()
  if level == #config then
    self.m_expText.text = GM.GameTextModel:GetText("branch_event_max_level_2")
    self.m_expSlider.value = 0
  else
    self.m_expText.text = curExp .. "/" .. targetExp
    self.m_expSlider.value = curExp / targetExp
  end
end

function SpreeLevelBubble:OnClicked()
  GM.UIManager:OpenView(SpreeActivityDefinition[self.m_model:GetType()].LevelWindowPrefabName, self.m_model:GetType(), true)
end

function SpreeLevelBubble:GetStarRectTransform()
  return self.m_iconRectTrans
end

function SpreeLevelBubble:PlayAcquireAnimation()
  if self.m_bLockBeforeLevelUp then
    self.m_bLockBeforeLevelUp = false
    self.m_recordLevelConfig = nil
    GM.UIManager:SetEventLock(false)
  end
  if self.m_bTweening then
    return
  end
  local level = self.m_model:GetLevel()
  local curExp = self.m_model:GetLevelExp()
  local config = self.m_model:GetLevelConfig()
  local targetExp = self.m_model:GetLevelUpCost()
  local oldLevel = tonumber(self.m_levelText.text) or 0
  local bUp = level > oldLevel
  self.m_bTweening = true
  local sliderValueTween = DOTween.Sequence()
  if bUp then
    local type = self.m_model:GetType()
    self.m_bIsUping = true
    GM.UIManager:SetEventLock(true)
    sliderValueTween:Append(self.m_expSlider:DOValue(1, 0.5))
    sliderValueTween:AppendCallback(function()
      GM.UIManager:OpenView(SpreeActivityDefinition[type].LevelUpWindowPrefabName, type)
    end)
    sliderValueTween:InsertCallback(0.7, function()
      if self.m_bIsUping then
        GM.UIManager:SetEventLock(false)
        self.m_bIsUping = false
        self:UpdateContent()
      end
      self.m_bTweening = false
    end)
  else
    local sliderValue = level == #config and 0 or curExp / targetExp
    sliderValueTween:Append(self.m_expSlider:DOValue(sliderValue, 0 < sliderValue and sliderValue or 0.1))
    sliderValueTween:AppendCallback(function()
      self:UpdateContent()
      self.m_bTweening = false
    end)
  end
  if level == #config then
    self.m_expText.text = GM.GameTextModel:GetText("branch_event_max_level_2")
  elseif not bUp then
    self.m_expText.text = curExp .. "/" .. targetExp
  end
end
