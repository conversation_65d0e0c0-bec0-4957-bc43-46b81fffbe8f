BreakEggRewardCell = {}
BreakEggRewardCell.__index = BreakEggRewardCell

function BreakEggRewardCell:Init(data)
  self.m_propertyType = data[PROPERTY_TYPE]
  self.m_propertyCount = data[PROPERTY_COUNT]
  local spriteName = ConfigUtil.GetCurrencyImageName(data)
  SpriteUtil.SetImage(self.m_iconImage, spriteName, true)
  self.m_countText.text = "X" .. self.m_propertyCount
end

function BreakEggRewardCell:MergeData(data)
  if self.m_propertyType ~= data[PROPERTY_TYPE] then
    return false
  end
  self.m_propertyCount = self.m_propertyCount + data[PROPERTY_COUNT]
  self.m_countText.text = "X" .. self.m_propertyCount
  return true
end

BreakEggRewardArea = {}
BreakEggRewardArea.__index = BreakEggRewardArea

function BreakEggRewardArea:Init()
  self.m_activityModel = GM.ActivityManager:GetModel(ActivityType.BreakEgg)
end

function BreakEggRewardArea:UpdateContent()
  for i = 1, self.m_rewardContentNode.childCount do
    Object.Destroy(self.m_rewardContentNode:GetChild(i - 1).gameObject)
  end
  self.m_rewardCells = {}
  local rewards = self.m_activityModel:GetCollectRewards()
  for _, reward in ipairs(rewards) do
    local cellObject = Object.Instantiate(self.m_cellPrefab, self.m_rewardContentNode)
    local cell = cellObject:GetLuaTable()
    cell:Init(reward)
    table.insert(self.m_rewardCells, cell)
  end
  self:_RefreshGemEqualVal()
end

function BreakEggRewardArea:AddRewardData(data)
  for _, cell in ipairs(self.m_rewardCells) do
    if cell:MergeData(data) then
      self:_RefreshGemEqualVal()
      return
    end
  end
  local cellObject = Object.Instantiate(self.m_cellPrefab, self.m_rewardContentNode)
  local cell = cellObject:GetLuaTable()
  table.insert(self.m_rewardCells, cell)
  self:UpdateRewardInit()
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_rewardContentNode)
  self:_RefreshGemEqualVal()
end

function BreakEggRewardArea:UpdateRewardInit()
  local rewards = self.m_activityModel:GetCollectRewards()
  for i = 1, #rewards do
    if self.m_rewardCells[i] then
      self.m_rewardCells[i]:Init(rewards[i])
    end
  end
end

function BreakEggRewardArea:_RefreshGemEqualVal()
  self.m_gemEqualVal.text = self.m_activityModel:GetEqualGem()
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_gemEqualRectTrans)
end
