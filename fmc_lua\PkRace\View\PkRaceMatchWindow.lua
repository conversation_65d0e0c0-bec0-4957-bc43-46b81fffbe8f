PkRaceMatchWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark,
  disableEffectWhenCloseView = true,
  canCloseByAndroidBack = false
}, PkRaceBaseWindow)
PkRaceMatchWindow.__index = PkRaceMatchWindow

function PkRaceMatchWindow:Init(activityType)
  PkRaceBaseWindow.Init(self, activityType, false)
  self.m_ownAvatar:SetAvatar(EAvatarFrame.PkRace, GM.UserProfileModel:GetIcon())
  self.m_playerAvatar:SetAvatar(EAvatarFrame.PkRace)
  self.m_ownNameText.text = GM.UserProfileModel:GetName()
  self.m_playerNameText.text = "???"
  self.m_arrowRect:DOLocalRotate(Vector3(0, 0, -360), 1, RotateType.FastBeyond360):SetEase(Ease.Linear):SetLoops(-1, LoopType.Restart)
  self:PlayEntryAnim()
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPkRaceActivityVS1)
end

function PkRaceMatchWindow:OnDestroy()
  if self.m_enterSeq ~= nil then
    self.m_enterSeq:Kill()
    self.m_enterSeq = nil
  end
  self.m_arrowRect:DOKill()
  Scheduler.UnscheduleTarget(self)
end

function PkRaceMatchWindow:PlayEntryAnim()
  local ownOriginPosX = self.m_ownRect.anchoredPosition.x
  local playerOriginPosX = self.m_playerRect.anchoredPosition.x
  UIUtil.SetAnchoredPosition(self.m_ownRect, ownOriginPosX - 1000)
  UIUtil.SetAnchoredPosition(self.m_playerRect, playerOriginPosX + 1000)
  UIUtil.SetLocalScale(self.m_vsRect, 0, 0)
  local rate = math.random(900, 1260)
  local time = rate / 720
  local seq = DOTween.Sequence()
  seq:AppendInterval(0.2)
  seq:Append(self.m_ownRect:DOAnchorPosX(ownOriginPosX - 50, 0.3):SetEase(Ease.OutCubic))
  seq:Join(self.m_playerRect:DOAnchorPosX(playerOriginPosX + 50, 0.3):SetEase(Ease.OutCubic))
  seq:Insert(0.4, self.m_vsRect:DOScale(Vector3(1, 1, 1), 0.2):SetEase(Ease.OutCubic))
  seq:InsertCallback(0.5, function()
    UIUtil.SetActive(self.m_vsEffectGo, true)
    UIUtil.SetLocalScale(self.m_vsIdleEffectGo.transform, 0, 0)
    UIUtil.SetActive(self.m_vsIdleEffectGo, true)
    self.m_vsIdleEffectGo.transform:DOScale(Vector3(55, 55, 1), 0.2):SetDelay(0.3)
  end)
  seq:Join(self.m_ownRect:DOAnchorPosX(ownOriginPosX, 2):SetEase(Ease.OutCubic))
  seq:Join(self.m_playerRect:DOAnchorPosX(playerOriginPosX, 2):SetEase(Ease.OutCubic))
  seq:InsertCallback(0.7, function()
    self.m_enterSeq = nil
    self:_OnEntry()
  end)
  self.m_enterSeq = seq
end

function PkRaceMatchWindow:_OnEntry()
  local state = self.m_model:GetState()
  if state ~= ActivityState.Started then
    self:Close()
    return
  end
  local minUpdateTime = 1
  local matchSuccessFunc = function()
    if self.m_bSuccess then
      return
    end
    self.m_bSuccess = true
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPkRaceActivityVS2)
    self.m_arrowRect:DOKill()
    UIUtil.SetActive(self.m_arrowRect.gameObject, false)
    local competitorData = self.m_model:GetOneCompetitorData()
    if competitorData ~= nil then
      self.m_playerAvatar:SetAvatar(EAvatarFrame.PkRace, competitorData:GetIcon())
      self.m_playerNameText.text = competitorData:GetUserName()
    end
    UIUtil.SetActive(self.m_matchSuccessEffectGo, true)
    local ownOriginPosX = self.m_ownRect.anchoredPosition.x
    local playerOriginPosX = self.m_playerRect.anchoredPosition.x
    DOTween.Sequence():AppendInterval(1):AppendCallback(function()
      self.m_vsIdleEffectGo.transform:DOScale(Vector3(0, 0, 1), 0.2)
    end):Append(self.m_ownRect:DOAnchorPosX(ownOriginPosX - 1000, 0.2):SetEase(Ease.InOutSine)):Join(self.m_playerRect:DOAnchorPosX(playerOriginPosX + 1000, 0.2):SetEase(Ease.InOutSine)):Join(self.m_vsRect:DOScale(Vector3(0, 0, 1), 0.2):SetEase(Ease.InOutSine)):AppendCallback(function()
      UIUtil.SetActive(self.m_ownRect.gameObject, false)
      UIUtil.SetActive(self.m_playerRect.gameObject, false)
      self:Close()
      self.m_model:TryOpenMainWindow(true)
    end)
  end
  local bCanUpdate = false
  DelayExecuteFuncInView(function()
    if bCanUpdate then
      matchSuccessFunc()
    else
      bCanUpdate = true
    end
  end, minUpdateTime, self)
  local successCallback = function()
    if bCanUpdate then
      matchSuccessFunc()
    else
      bCanUpdate = true
    end
  end
  local failCallback = function()
    self:Close()
  end
  self.m_model:TryCompetitionEntry(successCallback, failCallback)
end
