PassActivityTaskCell = {}
PassActivityTaskCell.__index = PassActivityTaskCell
local TASK_DESC_SHORT_KEY = {
  [EPassActivityTargetType.Customer] = "battlepass_taskTitle_cus_short",
  [EPassActivityTargetType.Merge] = "battlepass_taskTitle_cyMerge_short",
  [EPassActivityTargetType.Gold] = "battlepass_taskTitle_gold_short",
  [EPassActivityTargetType.UseGem] = "battlepass_taskTitle_usegem_short",
  [EPassActivityTargetType.Energy] = "battlepass_taskTitle_energy_short",
  [EPassActivityTargetType.Day] = "battlepass_taskTitle_day_short",
  [EPassActivityTargetType.Dish] = "battlepass_taskTitle_dish_short"
}
local TASK_DESC_KEY = {
  [EPassActivityTargetType.Customer] = "battlepass_taskTitle_cus",
  [EPassActivityTargetType.Merge] = "battlepass_taskTitle_cyMerge",
  [EPassActivityTargetType.Gold] = "battlepass_taskTitle_gold",
  [EPassActivityTargetType.UseGem] = "battlepass_taskTitle_usegem",
  [EPassActivityTargetType.Energy] = "battlepass_taskTitle_energy",
  [EPassActivityTargetType.Day] = "battlepass_taskTitle_day",
  [EPassActivityTargetType.Dish] = "battlepass_taskTitle_dish"
}

function PassActivityTaskCell.GetTaskDesc(task, bShort)
  local target = task:GetFinalTarget()
  local count = task:GetFinalCount()
  local key = bShort and TASK_DESC_SHORT_KEY[target] or TASK_DESC_KEY[target]
  if key then
    local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
    if target == EPassActivityTargetType.Gold and bakeOutModel:CanAcquireToken() then
      key = bShort and "battlepass_taskTitle_cookOff_short" or "battlepass_taskTitle_cookOff"
    end
    return GM.GameTextModel:GetText(key, count)
  else
    local name = GM.GameTextModel:GetText(ItemNameDefinition[target])
    return GM.GameTextModel:GetText("battlepass_taskTitle_mergeItem", name)
  end
end

function PassActivityTaskCell:Init(activityType, task, finishedMode, window)
  self.m_task = task
  self.m_activityType = activityType
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_finishedMode = finishedMode
  self.m_descriptionText.text = self.GetTaskDesc(task, not self.m_model:IsTaskGroupOpen())
  self.m_window = window
  self:UpdateTaskImg()
  self:UpdateProgress()
  self.m_rewardText.text = task.Reward
  if self.m_checkGo ~= nil then
    self.m_checkGo:SetActive(finishedMode)
  end
  if self.m_completedRootGo ~= nil then
    self.m_completedRootGo:SetActive(not finishedMode)
  end
  if self.m_countDownRootGo ~= nil then
    self.m_countDownRootGo:SetActive(not finishedMode)
  end
  if GM.UIManager:CanShowTestUI() and self.m_testBtnGo then
    self.m_testBtnGo:SetActive(not finishedMode)
  end
  self:UpdatePerSecond()
  if self.m_window ~= nil then
    local baseSortingOrder = self.m_window:GetSortingOrder()
    if self.m_enterEffect ~= nil then
      UIUtil.UpdateSortingOrder(self.m_enterEffect.gameObject, baseSortingOrder + 1)
    end
  end
end

function PassActivityTaskCell:UpdateProgress()
  local count = self.m_task:GetFinalCount()
  local finishedCount = self.m_finishedMode and count or self.m_model:GetFinishedCount(self.m_task, true)
  local delta = finishedCount ~= 0 and finishedCount ~= count and 0.05 or 0
  self.m_progressSlider.value = finishedCount / count + delta
  self.m_progressText.text = finishedCount .. "/" .. count
end

function PassActivityTaskCell:UpdateTaskImg()
  if self.m_iconImage ~= nil and not self.m_iconImage:IsNull() and self.m_iconImage.gameObject and not self.m_iconImage.gameObject:IsNull() then
    local bFinish = self.m_model:GetFinishedCount(self.m_task) >= self.m_task:GetFinalCount() or self.m_finishedMode
    PassActivityViewHelper.SetTaskTargetImage(self.m_iconImage, self.m_task, bFinish)
  end
end

function PassActivityTaskCell:UpdatePerSecond()
  if self.m_countdownText ~= nil and self.m_model ~= nil then
    local delta = math.max(self.m_model:GetTaskRefreshTime() - GM.GameModel:GetServerTime(), 0)
    self.m_countdownText.text = TimeUtil.ToMSOrHMS(delta)
  end
end

function PassActivityTaskCell:TestFinish()
  if not GameConfig.IsTestMode() then
    return
  end
  local target = self.m_task:GetFinalTarget()
  local delta = self.m_task:GetFinalCount() - self.m_model:GetFinishedCount(self.m_task)
  self.m_model:_OnTaskTargetReached(target, delta)
  GM.UIManager:ShowTestPrompt("操作成功，请重新打开活动窗口")
end

function PassActivityTaskCell:OnClicked()
end

function PassActivityTaskCell:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function PassActivityTaskCell:GetTask()
  return self.m_task
end

function PassActivityTaskCell:PlayGroupChangedAnim(newTask)
  local seq = DOTween.Sequence()
  seq:Append(self.transform:DOScale(Vector3(0, 0, 1), 0.3):SetEase(Ease.OutCubic))
  seq:AppendCallback(function()
    self:Init(self.m_activityType, newTask, false)
  end)
  seq:Append(self.transform:DOScale(Vector3(1.2, 1.2, 1), 0.4):SetEase(Ease.InCubic))
  seq:Append(self.transform:DOScale(Vector3(1, 1, 1), 0.1):SetEase(Ease.OutCubic))
  seq:AppendCallback(function()
    self.m_enterEffect:Simulate(0)
    self.m_enterEffect:Play()
  end)
  return seq
end

PassActivityBonusTaskCell = setmetatable({}, PassActivityTaskCell)
PassActivityBonusTaskCell.__index = PassActivityBonusTaskCell

function PassActivityBonusTaskCell:Init(...)
  PassActivityTaskCell.Init(self, ...)
  local unlocked = self.m_model:IsCurrentTimelimitTasksFinished()
  self.m_unlockContentGo:SetActive(unlocked)
  self.m_lockContentGo:SetActive(not unlocked)
  if unlocked then
    EventDispatcher.DispatchEvent(EEventType.PassActivityBonusTaskCellCreated, self)
  end
end

PassActivityVIPTaskCell = setmetatable({}, PassActivityTaskCell)
PassActivityVIPTaskCell.__index = PassActivityVIPTaskCell

function PassActivityVIPTaskCell:Init(activityType, task, finishedMode, bHideVIP)
  PassActivityTaskCell.Init(self, activityType, task, finishedMode)
  local passActivityDefinition = PassActivityDefinition[self.m_model:GetType()]
  local mainWindow = GM.UIManager:GetOpenedViewByName(passActivityDefinition.MainWindowPrefabName)
  if mainWindow then
    UIUtil.UpdateSortingOrder(self.m_outerLightEffectGo, mainWindow:GetSortingOrder() + 2)
  end
  if bHideVIP or self.m_model:HasTicket() then
    UIUtil.SetActive(self.m_vipButtonGo, false)
    self.m_vipButton.enabled = false
  else
    UIUtil.SetActive(self.m_vipButtonGo, true)
    self.m_vipButton.enabled = true
    if self.m_model:GetFinishedCount(task) >= task:GetFinalCount() then
      UIUtil.SetActive(self.m_outerLightEffectGo, true)
      self.m_vipButtonScaleTween = DOTween.Sequence():Append(self.m_vipButtonGo.transform:DOScale(1.08, 0.3)):Append(self.m_vipButtonGo.transform:DOScale(1, 0.3)):Append(self.m_vipButtonGo.transform:DOScale(1.08, 0.3)):Append(self.m_vipButtonGo.transform:DOScale(1, 0.3)):AppendInterval(0.8):SetLoops(-1)
    end
    EventDispatcher.AddListener(passActivityDefinition.BuyTicketSuccessEvent, self, self._OnBuyTicketSuccess)
    EventDispatcher.AddListener(passActivityDefinition.BuyMaxTicketSuccessEvent, self, self._OnBuyTicketSuccess)
  end
end

function PassActivityVIPTaskCell:_OnBuyTicketSuccess()
  UIUtil.SetActive(self.m_vipButtonGo, false)
  self.m_vipButton.enabled = false
end

function PassActivityVIPTaskCell:OnVipButtonClicked()
  PassActivityViewHelper.OpenBuyTicketWindow(self.m_model:GetType())
end

function PassActivityVIPTaskCell:OnDestroy()
  PassActivityTaskCell.OnDestroy(self)
  if self.m_vipButtonScaleTween then
    self.m_vipButtonScaleTween:Kill()
    self.m_vipButtonScaleTween = nil
  end
end
