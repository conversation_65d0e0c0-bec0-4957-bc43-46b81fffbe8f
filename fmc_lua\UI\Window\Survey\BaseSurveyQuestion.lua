BaseSurveyQuestion = {}
BaseSurveyQuestion.__index = BaseSurveyQuestion

function BaseSurveyQuestion:_InitPicture()
  if self.m_displayImg == nil or StringUtil.IsNilOrEmpty(self.m_questionData.picture) then
    if self.m_displayImg then
      self.m_displayImg.gameObject:SetActive(false)
    end
    return
  end
  local fx = 796
  self.m_displayImg.sprite = AddressableLoader.LoadSpriteByBase64(self.m_questionData.picture)
  if self.m_displayImg.sprite then
    local size = self.m_displayImg.sprite.rect.size
    if fx < size.x then
      size.y = size.y * fx / size.x
      size.x = fx
    end
    self.m_displayImg.rectTransform.sizeDelta = size
  end
  self.m_displayImg.color = CSColor.white
end
